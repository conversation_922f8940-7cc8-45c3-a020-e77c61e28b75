import moment from 'moment';
import {
  CHART_COLORS,
  CHART_DATA,
  CHART_FIELD_COMPARE_DICT,
  CHART_FIELD_TYPE,
  CHART_PERIOD,
  CHART_TYPE,
  CHART_X_AXIS_TYPE,
  MONTH_TO_CHART_LABEL,
} from '../constant/chart';
import { APP_TYPE, DATE_COMPARE_TYPE, ORDER_TYPE, REPORT_DISPLAY_TYPE } from '../constant/common';
import { compareOrderDateByDisplayType, countWeekdayOccurrencesInMonth, weekOfMonth } from './common';

export const getDataForChartDashboardTodaySales = (
  allOutlets,
  allOutletsUserOrdersDone,
  chartPeriod,
  yAxisKey,
  startDate = new Date(),
  endDate = new Date(),

  reportDisplayType = REPORT_DISPLAY_TYPE.DAY,
  outletShifts = [],
) => {
  try {
    // endDate = moment(endDate).add(1, 'day');

    // console.log('data before chart');

    var parsedData = [];

    var yMinValue = 0;
    var yMaxValue = 0;

    // const allOutletsUserOrdersDoneToday = allOutletsUserOrdersDone.filter(order => {
    //     return moment().isSame(order.createdAt, 'day');
    // });

    for (var i = 0; i < allOutlets.length; i++) {
      var record = {
        label: allOutlets[i].name,
        value: 0,
      };

      if (chartPeriod === CHART_PERIOD.TODAY) {
        const allOutletsUserOrdersDoneToday = allOutletsUserOrdersDone.filter(
          (order) => {
            return moment().isSame(moment(order.createdAt), 'day');
          },
        );

        for (var x = 0; x < 24; x++) {
          const currDay = moment().startOf('day').add(x, 'hour');

          for (var j = 0; j < allOutletsUserOrdersDoneToday.length; j++) {
            if (
              allOutletsUserOrdersDoneToday[j].outletId ===
              allOutlets[i].uniqueId &&
              moment(currDay).isSame(
                moment(allOutletsUserOrdersDoneToday[j].createdAt),
                'hour',
              )
            ) {
              record.value += allOutletsUserOrdersDoneToday[j][yAxisKey];
              // console.log('Got Loop Here1');
            }
          }
        }

        if (record.value > yMaxValue) {
          yMaxValue = record.value;
        }

        record.value = record.value.toFixed(0);

        parsedData.push(record);
      } else if (chartPeriod === CHART_PERIOD.THIS_WEEK) {
        const startDate = moment().subtract(7, 'day').startOf('day');
        const endDate = moment().endOf('day');

        const allOutletsUserOrdersDoneToday = allOutletsUserOrdersDone.filter(
          (order) => {
            return (
              moment(startDate).isSameOrBefore(order.createdAt) &&
              moment(endDate).isAfter(order.createdAt)
            );
          },
        );

        for (var x = 0; x < moment(endDate).diff(startDate, 'day'); x++) {
          const currDay = moment(startDate).add(x, 'day');

          for (var j = 0; j < allOutletsUserOrdersDoneToday.length; j++) {
            if (
              allOutletsUserOrdersDoneToday[j].outletId ===
              allOutlets[i].uniqueId &&
              moment(currDay).isSame(
                moment(allOutletsUserOrdersDoneToday[j].createdAt),
                'day',
              )
            ) {
              record.value += allOutletsUserOrdersDoneToday[j][yAxisKey];
              // console.log('Got Loop Here1');
            }
          }
        }

        // yMaxValue += record.value;
        if (record.value > yMaxValue) {
          yMaxValue = record.value;
        }

        record.value = record.value.toFixed(0);

        parsedData.push(record);
      } else if (chartPeriod === CHART_PERIOD.THIS_MONTH) {
        // const startDate = moment().subtract(moment(startDate).daysInMonth(), 'day').startOf('day');
        // const endDate = moment().endOf('day');

        const allOutletsUserOrdersDoneToday = allOutletsUserOrdersDone.filter(
          (order) => {
            return (
              moment(startDate).isSameOrBefore(order.createdAt) &&
              moment(endDate).isAfter(order.createdAt)
            );
          },
        );

        for (var x = 0; x < moment(startDate).daysInMonth(); x++) {
          const currDay = moment(startDate).add(x, 'day');

          for (var j = 0; j < allOutletsUserOrdersDoneToday.length; j++) {
            if (
              allOutletsUserOrdersDoneToday[j].outletId ===
              allOutlets[i].uniqueId &&
              moment(currDay).isSame(
                moment(allOutletsUserOrdersDoneToday[j].createdAt),
                'day',
              )
            ) {
              record.value += allOutletsUserOrdersDoneToday[j][yAxisKey];
              // console.log('Got Loop Here1');
            }
          }
        }

        // yMaxValue += record.value;
        if (record.value > yMaxValue) {
          yMaxValue = record.value;
        }

        record.value = record.value.toFixed(0);

        parsedData.push(record);
      } else if (chartPeriod === CHART_PERIOD.THREE_MONTHS) {
        const startDate = moment().subtract(2, 'month').startOf('month');
        const endDate = moment().endOf('month');

        const allOutletsUserOrdersDoneToday = allOutletsUserOrdersDone.filter(
          (order) => {
            return (
              moment(startDate).isSameOrBefore(order.createdAt) &&
              moment(endDate).isAfter(order.createdAt)
            );
          },
        );

        var currWeekCount = 0;
        var prevMonth = moment(startDate);

        for (var x = 0; x < moment(endDate).diff(startDate, 'week'); x++) {
          const currWeek = moment(startDate).add(x, 'week');

          if (moment(currWeek).isSame(prevMonth, 'month')) {
            currWeekCount++;
          } else {
            prevMonth = moment(currWeek);

            currWeekCount = 0;
            currWeekCount++;
          }

          for (var j = 0; j < allOutletsUserOrdersDoneToday.length; j++) {
            if (
              allOutletsUserOrdersDoneToday[j].outletId ===
              allOutlets[i].uniqueId &&
              moment(currWeek).isSame(
                moment(allOutletsUserOrdersDoneToday[j].createdAt),
                'week',
              )
            ) {
              record.value += allOutletsUserOrdersDoneToday[j][yAxisKey];
              // console.log('Got Loop Here1');
            }
          }
        }

        if (record.value > yMaxValue) {
          yMaxValue = record.value;
        }

        record.value = record.value.toFixed(0);

        parsedData.push(record);
      } else if (chartPeriod === CHART_PERIOD.SIX_MONTHS) {
        const startDate = moment().subtract(5, 'month').startOf('month');
        const endDate = moment().endOf('month');

        const allOutletsUserOrdersDoneToday = allOutletsUserOrdersDone.filter(
          (order) => {
            return (
              moment(startDate).isSameOrBefore(order.createdAt) &&
              moment(endDate).isAfter(order.createdAt)
            );
          },
        );

        var currWeekCount = 0;
        var prevMonth = moment(startDate);

        for (var x = 0; x < moment(endDate).diff(startDate, 'week'); x++) {
          const currWeek = moment(startDate).add(x, 'week');

          if (moment(currWeek).isSame(prevMonth, 'month')) {
            currWeekCount++;
          } else {
            prevMonth = moment(currWeek);

            currWeekCount = 0;
            currWeekCount++;
          }

          for (var j = 0; j < allOutletsUserOrdersDoneToday.length; j++) {
            if (
              allOutletsUserOrdersDoneToday[j].outletId ===
              allOutlets[i].uniqueId &&
              moment(currWeek).isSame(
                moment(allOutletsUserOrdersDoneToday[j].createdAt),
                'week',
              )
            ) {
              record.value += allOutletsUserOrdersDoneToday[j][yAxisKey];
              // console.log('Got Loop Here1');
            }
          }
        }

        if (record.value > yMaxValue) {
          yMaxValue = record.value;
        }

        record.value = record.value.toFixed(0);

        parsedData.push(record);
      } else if (chartPeriod === CHART_PERIOD.THIS_YEAR) {
        const allOutletsUserOrdersDoneToday = allOutletsUserOrdersDone.filter(
          (order) => {
            return moment().isSame(moment(order.createdAt), 'year');
          },
        );

        for (var x = 0; x < 12; x++) {
          const currMonth = moment().startOf('year').add(x, 'month');

          for (var j = 0; j < allOutletsUserOrdersDoneToday.length; j++) {
            if (
              allOutletsUserOrdersDoneToday[j].outletId ===
              allOutlets[i].uniqueId &&
              moment(currMonth).isSame(
                moment(allOutletsUserOrdersDoneToday[j].createdAt),
                'month',
              )
            ) {
              record.value += allOutletsUserOrdersDoneToday[j][yAxisKey];
              // console.log('Got Loop Here1');
            }
          }
        }

        if (record.value > yMaxValue) {
          yMaxValue = record.value;
        }

        record.value = record.value.toFixed(0);

        parsedData.push(record);
      } else if (chartPeriod === CHART_PERIOD.YTD) {
        const startDate = moment().subtract(11, 'month').startOf('month');
        const endDate = moment().endOf('month');

        const allOutletsUserOrdersDoneToday = allOutletsUserOrdersDone.filter(
          (order) => {
            return (
              moment(startDate).isSameOrBefore(order.createdAt) &&
              moment(endDate).isAfter(order.createdAt)
            );
          },
        );

        for (var x = 0; x < moment(endDate).diff(startDate, 'month'); x++) {
          const currMonth = moment(startDate).add(x, 'month');

          for (var j = 0; j < allOutletsUserOrdersDoneToday.length; j++) {
            if (
              allOutletsUserOrdersDoneToday[j].outletId ===
              allOutlets[i].uniqueId &&
              moment(currMonth).isSame(
                moment(allOutletsUserOrdersDoneToday[j].createdAt),
                'month',
              )
            ) {
              record.value += allOutletsUserOrdersDoneToday[j][yAxisKey];
              // console.log('Got Loop Here1');
            }
          }
        }

        if (record.value > yMaxValue) {
          yMaxValue = record.value;
        }

        record.value = record.value.toFixed(0);

        parsedData.push(record);
      } else if (chartPeriod === CHART_PERIOD.NONE) {
        const allOutletsUserOrdersDoneTodayRange =
          allOutletsUserOrdersDone.filter((order) => {
            return (
              moment(startDate).isSameOrBefore(order.createdAt) &&
              moment(endDate).isAfter(order.createdAt)
            );
          });

        if (
          moment(endDate).diff(startDate, 'day') <=
          moment(startDate).daysInMonth()
        ) {
          endDate = moment(endDate).add(1, 'day');

          const diffDays = moment(endDate).diff(startDate, 'day');

          var prevDate = moment(startDate).add(-1, 'month');

          for (var k = 0; k < diffDays; k++) {
            const currDate = moment(startDate).add(k, 'day');

            // category.push({
            //     //'label': moment(currDate).format('DD MMM'),
            //     'label': !moment(currDate).isSame(prevDate, 'month') ? moment(currDate).format('DD MMM') : moment(currDate).format('DD')
            //     //'label': (i > 1 ? moment(currDate).format('DD') : moment(currDate).format('DD MMM'))

            // });

            prevDate = currDate;

            for (
              var j = 0;
              j < allOutletsUserOrdersDoneTodayRange.length;
              j++
            ) {
              if (
                allOutletsUserOrdersDoneTodayRange[j].outletId ===
                allOutlets[i].uniqueId &&
                moment(currDate).isSame(
                  moment(allOutletsUserOrdersDoneTodayRange[j].createdAt),
                  'day',
                )
              ) {
                record.value += allOutletsUserOrdersDoneTodayRange[j][yAxisKey];
              }
            }
          }

          if (record.value > yMaxValue) {
            yMaxValue = record.value;
          }

          record.value = record.value.toFixed(0);

          parsedData.push(record);
        } else if (moment(endDate).diff(startDate, 'month') <= 6) {
          var prevMonth = moment(startDate);

          const diffWeeks = moment(endDate).diff(startDate, 'week');

          for (var k = 0; k < diffWeeks; k++) {
            const currDate = moment(startDate).add(k, 'week');

            if (moment(currDate).isSame(prevMonth, 'month')) {
              currWeekCount++;
            } else {
              prevMonth = moment(currDate);

              currWeekCount = 0;
              currWeekCount++;
            }

            // category.push({
            //     // label: moment(currDate).format('MMM') + ' W' + currWeekCount,
            //     label: moment(currDate).format('MMM') + ' W' + countWeekdayOccurrencesInMonth(currDate),
            // });

            for (
              var j = 0;
              j < allOutletsUserOrdersDoneTodayRange.length;
              j++
            ) {
              if (
                allOutletsUserOrdersDoneTodayRange[j].outletId ===
                allOutlets[i].uniqueId &&
                moment(currDate).isSame(
                  moment(allOutletsUserOrdersDoneTodayRange[j].createdAt),
                  'week',
                )
              ) {
                record.value += allOutletsUserOrdersDoneTodayRange[j][yAxisKey];
              }
            }
          }

          if (record.value > yMaxValue) {
            yMaxValue = record.value;
          }

          record.value = record.value.toFixed(0);

          parsedData.push(record);
        }
        // if (moment(endDate).diff(startDate, 'month') <= 6) // for now no need conditions first, for more than 6 months interval
        else {
          const diffYears = moment(endDate).diff(startDate, 'month');

          for (var k = 0; k < diffYears; k++) {
            const currDate = moment(startDate).add(k, 'month');

            // category.push({
            //     label: moment(currDate).format('YY\' MMM'),
            // });

            for (
              var j = 0;
              j < allOutletsUserOrdersDoneTodayRange.length;
              j++
            ) {
              if (
                allOutletsUserOrdersDoneTodayRange[j].outletId ===
                allOutlets[i].uniqueId &&
                moment(currDate).isSame(
                  moment(allOutletsUserOrdersDoneTodayRange[j].createdAt),
                  'month',
                )
              ) {
                record.value += allOutletsUserOrdersDoneTodayRange[j][yAxisKey];
              }
            }
          }

          if (record.value > yMaxValue) {
            yMaxValue = record.value;
          }

          record.value = record.value.toFixed(0);

          parsedData.push(record);
        }
      }

      // else if (chartPeriod === CHART_PERIOD.DEFAULT ) {
      //     for (var j = 0; j < allOutletsUserOrdersDoneToday.length; j++) {
      //         if (allOutletsUserOrdersDoneToday[j].outletId === allOutlets[i].uniqueId) {
      //             record.value += allOutletsUserOrdersDoneToday[j][yAxisKey];

      //         }
      //     }

      //     if (record.value > yMaxValue) {
      //         yMaxValue = record.value;
      //     }

      //     record.value = record.value.toFixed(0);

      //     parsedData.push(record);
      // }

      // else {
      //     for (var j = 0; j < allOutletsUserOrdersDoneToday.length; j++) {
      //         if (allOutletsUserOrdersDoneToday[j].outletId === allOutlets[i].uniqueId) {
      //             record.value += allOutletsUserOrdersDoneToday[j][yAxisKey];
      //         }
      //     }

      //     if (record.value > yMaxValue) {
      //         yMaxValue = record.value;
      //     }

      //     record.value = record.value.toFixed(0);

      //     parsedData.push(record);
      // }
    }

    var output = {};

    output.type = CHART_DATA[CHART_TYPE.DASHBOARD_TODAY_SALES].type;
    output.dataFormat = CHART_DATA[CHART_TYPE.DASHBOARD_TODAY_SALES].dataFormat;
    output.dataSource = {
      chart: CHART_DATA[CHART_TYPE.DASHBOARD_TODAY_SALES].chart,

      data: parsedData,
    };

    // console.log('parsedData');
    // console.log(parsedData);
    // console.log(yMaxValue);

    if (output.type !== 'HLinearGauge') {
      // const yAxisStep = Math.pow(10, Math.min(1, (yMaxValue.toFixed(0).length - 1)));

      // const yAxisStep = Math.ceil((yMaxValue / 10) / 10) * 10;

      // const yMaxValueParsed = Math.ceil(yMaxValue / yAxisStep);
      // const yAxisMaxValue = yMaxValueParsed === 1 ? 2 : yMaxValueParsed;
      // const numDivLines = yMaxValueParsed === 1 ? 1 : yMaxValueParsed - 1;

      const baseNumberLength = yMaxValue.toFixed(0).length - 1;
      const baseNumber = parseInt(
        `1${Array(baseNumberLength)
          .fill()
          .map((num) => 0)
          .join('')}`,
      );
      var maxNumber = baseNumber;

      if (yMaxValue > baseNumber * 5) {
        maxNumber = baseNumber * 10;
      } else if (yMaxValue > baseNumber * 3) {
        maxNumber = baseNumber * 5;
      } else if (yMaxValue > baseNumber * 2) {
        maxNumber = baseNumber * 3;
      } else {
        maxNumber = baseNumber * 2;
      }

      maxNumber = Math.max(maxNumber, 100);

      output.dataSource.chart = {
        ...output.dataSource.chart,
        labelDisplay: 'WRAP',
        // labelStep: 2,
        bgColor: '#ffffff',
        canvasBgColor: '#ffffff',
        bgAlpha: 100,

        // showYAxisValues: 0,

        // yaxismaxvalue: yAxisMaxValue * yAxisStep,
        yaxismaxvalue: maxNumber,
        yaxisminvalue: yMinValue,
        adjustDiv: 0,
        // numDivLines: numDivLines,
        numDivLines: 9,

        // yaxisminvalue: 0,
        formatNumberScale: 0,
        decimals: 0,
      };

      // // console.log('/////////////////////////////////////////////////')

      // // console.log({
      //     yMaxValue: yMaxValue,
      //     yMaxValueParsed: yMaxValueParsed,
      //     yaxismaxvalue: yAxisMaxValue * yAxisStep,
      //     yaxisminvalue: yMinValue,
      //     adjustDiv: 0,
      //     numDivLines: numDivLines,
      // });

      // // console.log('/////////////////////////////////////////////////')
    }

    // if (output.type === 'column2D') {
    //     if (type === CHART_TYPE.S_MILK) {
    //         // for (let i = 0; i < output.dataSource.data.length; i++) {
    //         //     if (output.dataSource.data[i].value.length > 0) {
    //         //         yMaxValue = parseInt(output.dataSource.data[i].value) > yMaxValue ? parseInt(output.dataSource.data[i].value) : yMaxValue;

    //         //     }
    //         // }

    //         // const yMaxValueParsed = Math.ceil(yMaxValue / 5);
    //         // const yAxisMaxValue = yMaxValueParsed === 1 ? 2 : yMaxValueParsed;
    //         // const numDivLines = yMaxValueParsed === 1 ? 1 : yMaxValueParsed - 1;

    //         // output.dataSource.chart = {
    //         //     ...output.dataSource.chart,
    //         //     adjustDiv: 0,
    //         //     numDivLines: numDivLines,
    //         //     yaxismaxvalue: yAxisMaxValue * 5,
    //         //     yaxisminvalue: yMinValue,
    //         //     formatNumberScale: 0,
    //         // };
    //     }
    //     else if (type === CHART_TYPE.S_SHIT_PEE) {
    //         for (let i = 0; i < output.dataSource.data.length; i++) {
    //             if (output.dataSource.data[i].value.length > 0) {
    //                 yMaxValue = parseInt(output.dataSource.data[i].value) > yMaxValue ? parseInt(output.dataSource.data[i].value) : yMaxValue;
    //             }
    //         }

    //         output.dataSource.chart = {
    //             ...output.dataSource.chart,
    //             adjustDiv: 0,
    //             numDivLines: yMaxValue,
    //             yaxismaxvalue: yMaxValue + 1,
    //             yaxisminvalue: yMinValue,
    //             formatNumberScale: 0,
    //         };
    //     }
    // }

    // console.log('chart output');
    // console.log(output);

    return output;
  } catch (ex) {
    console.log('chart error');
    console.log(ex);
    return false;
  }
};

///////////data for crmChart///////////////////
// export const getDataForCrmBarChart = (allOutlets, allOutletsUserOrdersDone, chartPeriod, yAxisKey) => {

//     var parsedData = [];

//     var yMinValue = 0;
//     var yMaxValue = 0;

//     if (chartPeriod === CHART_PERIOD.TODAY) {
//         const allOutletsUserOrdersDoneToday = allOutletsUserOrdersDone.filter(order => {
//             return moment().isSame(moment(order.createdAt), 'day');
//         });

//         for (var i = 0; i < 24; i++) {
//             const currHour = moment().startOf('day').add(i, 'hour');

//             var record = {
//                 label: i.toString().padStart(2, '0'),
//                 value: 0,
//             };

//             for (var j = 0; j < allOutletsUserOrdersDoneToday.length; j++) {
//                 if (moment(currHour).isSame(moment(allOutletsUserOrdersDoneToday[j].createdAt), 'hour')) {
//                     record.value += allOutletsUserOrdersDoneToday[j][yAxisKey];
//                 }
//             }

//             if (record.value > yMaxValue) {
//                 yMaxValue = record.value;
//             }

//             record.value = record.value.toFixed(0);

//             parsedData.push(record);
//         }

// }
// }

export const getDataForSalesLineChart = (
  allOutlets,
  allOutletsUserOrdersDone,
  chartPeriod,
  yAxisKey,
  xAxisKey,
  startDate = new Date(),
  endDate = new Date(),

  reportDisplayType = REPORT_DISPLAY_TYPE.DAY,
  outletShifts = [],
) => {
  try {
    // endDate = moment(endDate).add(1, 'day');
    // endDate = moment(endDate).endOf('day');

    // console.log('data before chart');

    var parsedData = [];

    var yMinValue = 0;
    var yMaxValue = 0;

    var category = [];
    var datasetDict = {};

    var outletNameDict = {};
    for (var i = 0; i < allOutlets.length; i++) {
      outletNameDict[allOutlets[i].uniqueId] = allOutlets[i].name;
    }

    // var annotations = {
    //     "groups": [{
    //         "items": [{
    //             //Text annotation 1
    //             "type": "text",
    //             "text": 'Hello',
    //             //Define the attributes needed to create a text annotation
    //         }, {
    //             //Text annotation n
    //             "type": "text",
    //             "text": 'Hi',
    //             //Define the attributes needed to create a text annotation
    //         }]
    //     }]
    // };

    var annotations = {
      groups: [
        {
          items: [],
        },
      ],
    };
    var annotationsItems = [];

    if (chartPeriod === CHART_PERIOD.TODAY) {
      const allOutletsUserOrdersDoneToday = allOutletsUserOrdersDone.filter(
        (order) => {
          // return moment().isSame(moment(order.createdAt), 'day');
          return compareOrderDateByDisplayType(Date.now(), order.createdAt, DATE_COMPARE_TYPE.IS_SAME, 'day', reportDisplayType, outletShifts);
        },
      );

      for (var i = 0; i < allOutlets.length; i++) {
        datasetDict[allOutlets[i].uniqueId] = {
          seriesname: allOutlets[i].name,
          data: Array.from(Array(24)).map(() => {
            return { value: 0 };
          }),
        };
      }

      for (var i = 0; i < 24; i++) {
        const currHour = moment().startOf('day').add(i, 'hour');

        // var record = {
        //     label: i.toString().padStart(2, '0'),
        //     value: 0,
        // };

        category.push({
          label: i.toString().padStart(2, '0'),
        });

        for (var j = 0; j < allOutletsUserOrdersDoneToday.length; j++) {
          if (
            // moment(currHour).isSame(
            //   moment(allOutletsUserOrdersDoneToday[j].createdAt),
            //   'hour',
            // )
            compareOrderDateByDisplayType(currHour, allOutletsUserOrdersDoneToday[j].createdAt, DATE_COMPARE_TYPE.IS_SAME, 'hour', reportDisplayType, outletShifts)
          ) {
            datasetDict[allOutletsUserOrdersDoneToday[j].outletId].data[
              i
            ].value += allOutletsUserOrdersDoneToday[j][yAxisKey];
            // record.value += allOutletsUserOrdersDoneToday[j][yAxisKey];
          }
        }

        // if (record.value > yMaxValue) {
        //     yMaxValue = record.value;
        // }

        // record.value = record.value.toFixed(0);

        // parsedData.push(record);
      }
    } else if (chartPeriod === CHART_PERIOD.THIS_WEEK) {
      // const allOutletsUserOrdersDoneToday = allOutletsUserOrdersDone.filter(order => {
      //     return moment().isSame(moment(order.createdAt), 'week');
      // });

      const startDate = moment(Date.now()).subtract(7, 'days').startOf('day');
      const endDate = moment().endOf('day');

      const allOutletsUserOrdersDoneToday = allOutletsUserOrdersDone.filter(
        (order) => {
          return (
            moment(startDate).isSameOrBefore(order.createdAt) &&
            moment(endDate).isAfter(order.createdAt)
            // compareOrderDateByDisplayType(startDate, order.createdAt, DATE_COMPARE_TYPE.IS_SAME_OR_BEFORE, '', reportDisplayType, outletShifts) &&
            // compareOrderDateByDisplayType(endDate, order.createdAt, DATE_COMPARE_TYPE.IS_AFTER, '', reportDisplayType, outletShifts)
          );
        },
      );

      // console.log('allOutletsUserOrdersDoneToday');
      // console.log(allOutletsUserOrdersDoneToday);

      for (var i = 0; i < allOutlets.length; i++) {
        datasetDict[allOutlets[i].uniqueId] = {
          seriesname: allOutlets[i].name,
          data: Array.from(Array(moment(endDate).diff(startDate, 'day'))).map(
            () => {
              return { value: 0 };
            },
          ),
        };
      }

      for (var i = 0; i < moment(endDate).diff(startDate, 'day'); i++) {
        // const currDay = moment().startOf('week').add(i, 'day');
        const currDay = moment(startDate).add(i, 'day');

        // var record = {
        //     label: moment().day(i).format('ddd'),
        //     value: 0,
        // };

        category.push({
          label: moment(startDate).add(i, 'day').format('ddd'),
        });

        for (var j = 0; j < allOutletsUserOrdersDoneToday.length; j++) {
          if (
            // moment(currDay).isSame(
            //   moment(allOutletsUserOrdersDoneToday[j].createdAt),
            //   'day',
            // )
            compareOrderDateByDisplayType(currDay, allOutletsUserOrdersDoneToday[j].createdAt, DATE_COMPARE_TYPE.IS_SAME, 'day', reportDisplayType, outletShifts)
          ) {
            datasetDict[allOutletsUserOrdersDoneToday[j].outletId].data[
              i
            ].value += allOutletsUserOrdersDoneToday[j][yAxisKey];
            // record.value += allOutletsUserOrdersDoneToday[j][yAxisKey];
          }
        }

        // if (record.value > yMaxValue) {
        //     yMaxValue = record.value;
        // }

        // record.value = record.value.toFixed(0);

        // parsedData.push(record);
      }
    } else if (chartPeriod === CHART_PERIOD.THIS_MONTH) {
      // const allOutletsUserOrdersDoneToday = allOutletsUserOrdersDone.filter(order => {
      //     return moment().isSame(moment(order.createdAt), 'month');
      // });

      // const startDate = moment().subtract(moment(startDate).daysInMonth(), 'day').startOf('day');
      // const endDate = moment().endOf('day');

      const allOutletsUserOrdersDoneToday = allOutletsUserOrdersDone.filter(
        (order) => {
          return (
            moment(startDate).isSameOrBefore(order.createdAt) &&
            moment(endDate).isAfter(order.createdAt)
            // compareOrderDateByDisplayType(startDate, order.createdAt, DATE_COMPARE_TYPE.IS_SAME_OR_BEFORE, '', reportDisplayType, outletShifts) &&
            // compareOrderDateByDisplayType(endDate, order.createdAt, DATE_COMPARE_TYPE.IS_AFTER, '', reportDisplayType, outletShifts)
          );
        },
      );

      if (xAxisKey === CHART_X_AXIS_TYPE.DAY) {
        for (var i = 0; i < allOutlets.length; i++) {
          datasetDict[allOutlets[i].uniqueId] = {
            seriesname: allOutlets[i].name,
            data: Array.from(Array(moment(startDate).daysInMonth())).map(() => {
              return { value: 0 };
            }),
          };
        }

        for (var i = 0; i < moment(startDate).daysInMonth(); i++) {
          // for (var i = 0; i < 31; i++) {
          // const currDay = moment().startOf('month').add(i, 'day');
          const currDay = moment(startDate).add(i, 'day');

          // var record = {
          //     label: i.toString().padStart(2, '0'),
          //     value: 0,
          // };

          category.push({
            label: (i + 1).toString().padStart(2, '0'),
          });

          for (var j = 0; j < allOutletsUserOrdersDoneToday.length; j++) {
            if (
              // moment(currDay).isSame(
              //   moment(allOutletsUserOrdersDoneToday[j].createdAt),
              //   'day',
              // )
              compareOrderDateByDisplayType(
                currDay,
                allOutletsUserOrdersDoneToday[j].createdAt,
                DATE_COMPARE_TYPE.IS_SAME,
                'day',
                reportDisplayType,
                outletShifts
              )
            ) {
              // need do for outlet based
              datasetDict[allOutletsUserOrdersDoneToday[j].outletId].data[
                i
              ].value += allOutletsUserOrdersDoneToday[j][yAxisKey];
              // record.value += allOutletsUserOrdersDoneToday[j][yAxisKey];
            }
          }

          // if (record.value > yMaxValue) {
          //     yMaxValue = record.value;
          // }

          // record.value = record.value.toFixed(0);

          // parsedData.push(record);
        }
      }
      else if (xAxisKey === CHART_X_AXIS_TYPE.WEEK || xAxisKey === CHART_X_AXIS_TYPE.MONTH) {
        var currWeekCount = 0;
        var prevMonth = moment(startDate);

        for (var i = 0; i < allOutlets.length; i++) {
          datasetDict[allOutlets[i].uniqueId] = {
            seriesname: allOutlets[i].name,
            data: Array.from(Array(moment(endDate).diff(startDate, 'week'))).map(
              () => {
                return { value: 0 };
              },
            ),
          };
        }

        for (var i = 0; i < moment(endDate).diff(startDate, 'week'); i++) {
          const currWeek = moment(startDate).add(i, 'week');

          if (moment(currWeek).isSame(prevMonth, 'month')) {
            currWeekCount++;
          } else {
            prevMonth = moment(currWeek);

            currWeekCount = 0;
            currWeekCount++;
          }

          // var record = {
          //     label: (currWeekCount > 1 ? '' : moment(currWeek).format('MMM')) + ' W' + currWeekCount,
          //     value: 0,
          // };

          category.push({
            label:
              `${currWeekCount > 1 ? '' : moment(currWeek).format('MMM')
              } W${currWeekCount}`,
          });

          for (var j = 0; j < allOutletsUserOrdersDoneToday.length; j++) {
            if (
              // moment(currWeek).isSame(
              //   moment(allOutletsUserOrdersDoneToday[j].createdAt),
              //   'week',
              // )
              compareOrderDateByDisplayType(
                currWeek,
                allOutletsUserOrdersDoneToday[j].createdAt,
                DATE_COMPARE_TYPE.IS_SAME,
                'week',
                reportDisplayType,
                outletShifts
              )
            ) {
              datasetDict[allOutletsUserOrdersDoneToday[j].outletId].data[
                i
              ].value += allOutletsUserOrdersDoneToday[j][yAxisKey];
              // record.value += allOutletsUserOrdersDoneToday[j][yAxisKey];
            }
          }

          // if (record.value > yMaxValue) {
          //     yMaxValue = record.value;
          // }

          // record.value = record.value.toFixed(0);

          // parsedData.push(record);
        }
      }
    } else if (chartPeriod === CHART_PERIOD.THREE_MONTHS) {
      const startDate = moment().subtract(2, 'month').startOf('month');
      const endDate = moment().endOf('month');

      const allOutletsUserOrdersDoneToday = allOutletsUserOrdersDone.filter(
        (order) => {
          return (
            moment(startDate).isSameOrBefore(order.createdAt) &&
            moment(endDate).isAfter(order.createdAt)
            // compareOrderDateByDisplayType(
            //   startDate,
            //   order.createdAt,
            //   DATE_COMPARE_TYPE.IS_SAME_OR_AFTER,
            //   '',
            //   reportDisplayType,
            //   outletShifts
            // )
            // &&
            // compareOrderDateByDisplayType(
            //   endDate,
            //   order.createdAt,
            //   DATE_COMPARE_TYPE.IS_AFTER,
            //   '',
            //   reportDisplayType,
            //   outletShifts
            // )
          );
        },
      );

      if (xAxisKey === CHART_X_AXIS_TYPE.DAY) {
        for (var i = 0; i < allOutlets.length; i++) {
          datasetDict[allOutlets[i].uniqueId] = {
            seriesname: allOutlets[i].name,
            data: Array.from(Array(moment(endDate).diff(startDate, 'day'))).map(() => {
              return { value: 0 };
            }),
          };
        }

        for (var i = 0; i < moment(endDate).diff(startDate, 'day'); i++) {
          // for (var i = 0; i < 31; i++) {
          // const currDay = moment().startOf('month').add(i, 'day');
          const currDay = moment(startDate).add(i, 'day');

          // var record = {
          //     label: i.toString().padStart(2, '0'),
          //     value: 0,
          // };

          category.push({
            label: (i + 1).toString().padStart(2, '0'),
          });

          for (var j = 0; j < allOutletsUserOrdersDoneToday.length; j++) {
            if (
              // moment(currDay).isSame(
              //   moment(allOutletsUserOrdersDoneToday[j].createdAt),
              //   'day',
              // )
              compareOrderDateByDisplayType(
                currDay,
                allOutletsUserOrdersDoneToday[j].createdAt,
                DATE_COMPARE_TYPE.IS_SAME,
                'day',
                reportDisplayType,
                outletShifts
              )
            ) {
              // need do for outlet based
              datasetDict[allOutletsUserOrdersDoneToday[j].outletId].data[
                i
              ].value += allOutletsUserOrdersDoneToday[j][yAxisKey];
              // record.value += allOutletsUserOrdersDoneToday[j][yAxisKey];
            }
          }

          // if (record.value > yMaxValue) {
          //     yMaxValue = record.value;
          // }

          // record.value = record.value.toFixed(0);

          // parsedData.push(record);
        }
      }
      else if (xAxisKey === CHART_X_AXIS_TYPE.WEEK) {
        var currWeekCount = 0;
        var prevMonth = moment(startDate);

        for (var i = 0; i < allOutlets.length; i++) {
          datasetDict[allOutlets[i].uniqueId] = {
            seriesname: allOutlets[i].name,
            data: Array.from(Array(moment(endDate).diff(startDate, 'week'))).map(
              () => {
                return { value: 0 };
              },
            ),
          };
        }

        for (var i = 0; i < moment(endDate).diff(startDate, 'week'); i++) {
          const currWeek = moment(startDate).add(i, 'week');

          if (moment(currWeek).isSame(prevMonth, 'month')) {
            currWeekCount++;
          } else {
            prevMonth = moment(currWeek);

            currWeekCount = 0;
            currWeekCount++;
          }

          // var record = {
          //     label: (currWeekCount > 1 ? '' : moment(currWeek).format('MMM')) + ' W' + currWeekCount,
          //     value: 0,
          // };

          category.push({
            label:
              `${currWeekCount > 1 ? '' : moment(currWeek).format('MMM')
              } W${currWeekCount}`,
          });

          for (var j = 0; j < allOutletsUserOrdersDoneToday.length; j++) {
            if (
              // moment(currWeek).isSame(
              //   moment(allOutletsUserOrdersDoneToday[j].createdAt),
              //   'week',
              // )
              compareOrderDateByDisplayType(
                currWeek,
                allOutletsUserOrdersDoneToday[j].createdAt,
                DATE_COMPARE_TYPE.IS_SAME,
                'week',
                reportDisplayType,
                outletShifts
              )
            ) {
              datasetDict[allOutletsUserOrdersDoneToday[j].outletId].data[
                i
              ].value += allOutletsUserOrdersDoneToday[j][yAxisKey];
              // record.value += allOutletsUserOrdersDoneToday[j][yAxisKey];
            }
          }

          // if (record.value > yMaxValue) {
          //     yMaxValue = record.value;
          // }

          // record.value = record.value.toFixed(0);

          // parsedData.push(record);
        }
      }
      else if (xAxisKey === CHART_X_AXIS_TYPE.MONTH) {
        for (var i = 0; i < allOutlets.length; i++) {
          datasetDict[allOutlets[i].uniqueId] = {
            seriesname: allOutlets[i].name,
            data: Array.from(Array(3)).map(() => {
              return { value: 0 };
            }),
          };
        }

        var currMonth = moment(startDate).startOf('month');

        for (var i = 0; i < 3; i++) {
          // const currMonth = moment().startOf('year').add(i, 'month');
          var currMonth = moment(currMonth).add(i, 'month');

          // var record = {
          //     label: moment(currMonth).format('MMM'),
          //     value: 0,
          // };

          category.push({
            label: moment(currMonth).format('MMM'),
          });

          for (var j = 0; j < allOutletsUserOrdersDoneToday.length; j++) {
            if (
              // moment(currMonth).isSame(
              //   moment(allOutletsUserOrdersDoneToday[j].createdAt),
              //   'month',
              // )
              compareOrderDateByDisplayType(
                currMonth,
                allOutletsUserOrdersDoneToday[i].createdAt,
                DATE_COMPARE_TYPE.IS_SAME,
                'month',
                reportDisplayType,
                outletShifts
              )
            ) {
              datasetDict[allOutletsUserOrdersDoneToday[j].outletId].data[
                i
              ].value += allOutletsUserOrdersDoneToday[j][yAxisKey];
              // record.value += allOutletsUserOrdersDoneToday[j][yAxisKey];
            }
          }

          // if (record.value > yMaxValue) {
          //     yMaxValue = record.value;
          // }

          // record.value = record.value.toFixed(0);

          // parsedData.push(record);
        }
      }
    } else if (chartPeriod === CHART_PERIOD.SIX_MONTHS) {
      const startDate = moment().subtract(5, 'month').startOf('month');
      const endDate = moment().endOf('month');

      const allOutletsUserOrdersDoneToday = allOutletsUserOrdersDone.filter(
        (order) => {
          return (
            moment(startDate).isSameOrBefore(order.createdAt) &&
            moment(endDate).isAfter(order.createdAt)
            // compareOrderDateByDisplayType(
            //   startDate,
            //   order.createdAt,
            //   DATE_COMPARE_TYPE.IS_SAME_OR_BEFORE,
            //   '',
            //   reportDisplayType,
            //   outletShifts
            // )
            // &&
            // compareOrderDateByDisplayType(
            //   endDate,
            //   order.createdAt,
            //   DATE_COMPARE_TYPE.IS_AFTER,
            //   '',
            //   reportDisplayType,
            //   outletShifts
            // )
          );
        },
      );

      if (xAxisKey === CHART_X_AXIS_TYPE.DAY || xAxisKey === CHART_X_AXIS_TYPE.WEEK) {
        for (var i = 0; i < allOutlets.length; i++) {
          datasetDict[allOutlets[i].uniqueId] = {
            seriesname: allOutlets[i].name,
            data: Array.from(Array(moment(endDate).diff(startDate, 'week'))).map(
              () => {
                return { value: 0 };
              },
            ),
          };
        }

        for (var i = 0; i < moment(endDate).diff(startDate, 'week'); i++) {
          const currWeek = moment(startDate).add(i, 'week');

          if (moment(currWeek).isSame(prevMonth, 'month')) {
            currWeekCount++;
          } else {
            prevMonth = moment(currWeek);

            currWeekCount = 0;
            currWeekCount++;
          }

          // var record = {
          //     label: 'W' + currWeekCount + ' ' + (currWeekCount > 1 ? '' : moment(currWeek).format('MMM')),
          //     value: 0,
          // };

          category.push({
            label:
              `W${currWeekCount
              } ${currWeekCount > 1 ? '' : moment(currWeek).format('MMM')}`,
          });

          for (var j = 0; j < allOutletsUserOrdersDoneToday.length; j++) {
            if (
              // moment(currWeek).isSame(
              //   moment(allOutletsUserOrdersDoneToday[j].createdAt),
              //   'week',
              // )
              compareOrderDateByDisplayType(
                currWeek,
                allOutletsUserOrdersDoneToday[j].createdAt,
                DATE_COMPARE_TYPE.IS_SAME,
                'week',
                reportDisplayType,
                outletShifts
              )
            ) {
              datasetDict[allOutletsUserOrdersDoneToday[j].outletId].data[
                i
              ].value += allOutletsUserOrdersDoneToday[j][yAxisKey];
              // record.value += allOutletsUserOrdersDoneToday[j][yAxisKey];
            }
          }

          // if (record.value > yMaxValue) {
          //     yMaxValue = record.value;
          // }

          // record.value = record.value.toFixed(0);

          // parsedData.push(record);
        }
      }
      else if (xAxisKey === CHART_X_AXIS_TYPE.MONTH) {
        for (var i = 0; i < allOutlets.length; i++) {
          datasetDict[allOutlets[i].uniqueId] = {
            seriesname: allOutlets[i].name,
            data: Array.from(Array(6)).map(() => {
              return { value: 0 };
            }),
          };
        }

        var currMonth = moment(startDate).startOf('month');

        for (var i = 0; i < 6; i++) {
          // const currMonth = moment().startOf('year').add(i, 'month');
          var currMonth = moment(currMonth).add(i, 'month');

          // var record = {
          //     label: moment(currMonth).format('MMM'),
          //     value: 0,
          // };

          category.push({
            label: moment(currMonth).format('MMM'),
          });

          for (var j = 0; j < allOutletsUserOrdersDoneToday.length; j++) {
            if (
              // moment(currMonth).isSame(
              //   moment(allOutletsUserOrdersDoneToday[j].createdAt),
              //   'month',
              // )
              compareOrderDateByDisplayType(
                currMonth,
                allOutletsUserOrdersDoneToday[j].createdAt,
                DATE_COMPARE_TYPE.IS_SAME,
                'month',
                reportDisplayType,
                outletShifts
              )
            ) {
              datasetDict[allOutletsUserOrdersDoneToday[j].outletId].data[
                i
              ].value += allOutletsUserOrdersDoneToday[j][yAxisKey];
              // record.value += allOutletsUserOrdersDoneToday[j][yAxisKey];
            }
          }

          // if (record.value > yMaxValue) {
          //     yMaxValue = record.value;
          // }

          // record.value = record.value.toFixed(0);

          // parsedData.push(record);
        }
      }
    } else if (chartPeriod === CHART_PERIOD.THIS_YEAR) {
      const allOutletsUserOrdersDoneToday = allOutletsUserOrdersDone.filter(
        (order) => {
          return moment().isSame(moment(order.createdAt), 'year');

          // return compareOrderDateByDisplayType(
          //   Date.now(),
          //   order.createdAt,
          //   DATE_COMPARE_TYPE.IS_SAME,
          //   'year',
          //   reportDisplayType,
          //   outletShifts
          // )
        },
      );

      if (xAxisKey === CHART_X_AXIS_TYPE.DAY || xAxisKey === CHART_X_AXIS_TYPE.WEEK) {
        for (var i = 0; i < allOutlets.length; i++) {
          datasetDict[allOutlets[i].uniqueId] = {
            seriesname: allOutlets[i].name,
            data: Array.from(Array(moment(endDate).diff(startDate, 'week'))).map(
              () => {
                return { value: 0 };
              },
            ),
          };
        }

        for (var i = 0; i < moment(endDate).diff(startDate, 'week'); i++) {
          const currWeek = moment(startDate).add(i, 'week');

          if (moment(currWeek).isSame(prevMonth, 'month')) {
            currWeekCount++;
          } else {
            prevMonth = moment(currWeek);

            currWeekCount = 0;
            currWeekCount++;
          }

          // var record = {
          //     label: 'W' + currWeekCount + ' ' + (currWeekCount > 1 ? '' : moment(currWeek).format('MMM')),
          //     value: 0,
          // };

          category.push({
            label:
              `W${currWeekCount
              } ${currWeekCount > 2 ? '' : moment(currWeek).format('MMM')}`,
          });

          for (var j = 0; j < allOutletsUserOrdersDoneToday.length; j++) {
            if (
              // moment(currWeek).isSame(
              //   moment(allOutletsUserOrdersDoneToday[j].createdAt),
              //   'week',
              // )
              compareOrderDateByDisplayType(
                currWeek,
                allOutletsUserOrdersDoneToday[j].createdAt,
                DATE_COMPARE_TYPE.IS_SAME,
                'week',
                reportDisplayType,
                outletShifts
              )
            ) {
              datasetDict[allOutletsUserOrdersDoneToday[j].outletId].data[
                i
              ].value += allOutletsUserOrdersDoneToday[j][yAxisKey];
              // record.value += allOutletsUserOrdersDoneToday[j][yAxisKey];
            }
          }

          // if (record.value > yMaxValue) {
          //     yMaxValue = record.value;
          // }

          // record.value = record.value.toFixed(0);

          // parsedData.push(record);
        }
      }
      else if (xAxisKey === CHART_X_AXIS_TYPE.MONTH) {
        for (var i = 0; i < allOutlets.length; i++) {
          datasetDict[allOutlets[i].uniqueId] = {
            seriesname: allOutlets[i].name,
            data: Array.from(Array(12)).map(() => {
              return { value: 0 };
            }),
          };
        }

        for (var i = 0; i < 12; i++) {
          const currMonth = moment().startOf('year').add(i, 'month');

          // var record = {
          //     label: moment(currMonth).format('MMM'),
          //     value: 0,
          // };

          category.push({
            label: moment(currMonth).format('MMM'),
          });

          for (var j = 0; j < allOutletsUserOrdersDoneToday.length; j++) {
            if (
              // moment(currMonth).isSame(
              //   moment(allOutletsUserOrdersDoneToday[j].createdAt),
              //   'month',
              // )
              compareOrderDateByDisplayType(
                currMonth,
                allOutletsUserOrdersDoneToday[j].createdAt,
                DATE_COMPARE_TYPE.IS_SAME,
                'month',
                reportDisplayType,
                outletShifts
              )
            ) {
              datasetDict[allOutletsUserOrdersDoneToday[j].outletId].data[
                i
              ].value += allOutletsUserOrdersDoneToday[j][yAxisKey];
              // record.value += allOutletsUserOrdersDoneToday[j][yAxisKey];
            }
          }

          // if (record.value > yMaxValue) {
          //     yMaxValue = record.value;
          // }

          // record.value = record.value.toFixed(0);

          // parsedData.push(record);
        }
      }
    } else if (chartPeriod === CHART_PERIOD.YTD) {
      const startDate = moment().subtract(11, 'month').startOf('month');
      const endDate = moment().endOf('month');

      const allOutletsUserOrdersDoneToday = allOutletsUserOrdersDone.filter(
        (order) => {
          return (
            moment(startDate).isSameOrBefore(order.createdAt) &&
            moment(endDate).isAfter(order.createdAt)
            // compareOrderDateByDisplayType(
            //   startDate,
            //   order.createdAt,
            //   DATE_COMPARE_TYPE.IS_SAME_OR_BEFORE,
            //   '',
            //   reportDisplayType,
            //   outletShifts
            // )
            // &&
            // compareOrderDateByDisplayType(
            //   endDate,
            //   order.createdAt,
            //   DATE_COMPARE_TYPE.IS_AFTER,
            //   '',
            //   reportDisplayType,
            //   outletShifts
            // )
          );
        },
      );

      if (xAxisKey === CHART_X_AXIS_TYPE.DAY || xAxisKey === CHART_X_AXIS_TYPE.WEEK) {
        for (var i = 0; i < allOutlets.length; i++) {
          datasetDict[allOutlets[i].uniqueId] = {
            seriesname: allOutlets[i].name,
            data: Array.from(Array(moment(endDate).diff(startDate, 'week'))).map(
              () => {
                return { value: 0 };
              },
            ),
          };
        }

        for (var i = 0; i < moment(endDate).diff(startDate, 'week'); i++) {
          const currWeek = moment(startDate).add(i, 'week');

          if (moment(currWeek).isSame(prevMonth, 'month')) {
            currWeekCount++;
          } else {
            prevMonth = moment(currWeek);

            currWeekCount = 0;
            currWeekCount++;
          }

          // var record = {
          //     label: 'W' + currWeekCount + ' ' + (currWeekCount > 1 ? '' : moment(currWeek).format('MMM')),
          //     value: 0,
          // };

          category.push({
            label:
              `W${currWeekCount
              } ${currWeekCount > 2 ? '' : moment(currWeek).format('MMM')}`,
          });

          for (var j = 0; j < allOutletsUserOrdersDoneToday.length; j++) {
            if (
              // moment(currWeek).isSame(
              //   moment(allOutletsUserOrdersDoneToday[j].createdAt),
              //   'week',
              // )
              compareOrderDateByDisplayType(
                currWeek,
                allOutletsUserOrdersDoneToday[j].createdAt,
                DATE_COMPARE_TYPE.IS_SAME,
                'week',
                reportDisplayType,
                outletShifts
              )
            ) {
              datasetDict[allOutletsUserOrdersDoneToday[j].outletId].data[
                i
              ].value += allOutletsUserOrdersDoneToday[j][yAxisKey];
              // record.value += allOutletsUserOrdersDoneToday[j][yAxisKey];
            }
          }

          // if (record.value > yMaxValue) {
          //     yMaxValue = record.value;
          // }

          // record.value = record.value.toFixed(0);

          // parsedData.push(record);
        }
      }
      else if (xAxisKey === CHART_X_AXIS_TYPE.MONTH) {
        for (var i = 0; i < allOutlets.length; i++) {
          datasetDict[allOutlets[i].uniqueId] = {
            seriesname: allOutlets[i].name,
            data: Array.from(Array(12)).map(() => {
              return { value: 0 };
            }),
          };
        }

        for (var i = 0; i < 12; i++) {
          const currMonth = moment(startDate).add(i, 'month');

          // var record = {
          //     label: moment(currMonth).format('YY\' MMM'),
          //     value: 0,
          // };

          category.push({
            label: moment(currMonth).format("YY' MMM"),
          });

          for (var j = 0; j < allOutletsUserOrdersDoneToday.length; j++) {
            if (
              // moment(currMonth).isSame(
              //   moment(allOutletsUserOrdersDoneToday[j].createdAt),
              //   'month',
              // )
              compareOrderDateByDisplayType(
                currMonth,
                allOutletsUserOrdersDoneToday[j].createdAt,
                DATE_COMPARE_TYPE.IS_SAME,
                'month',
                reportDisplayType,
                outletShifts
              )
            ) {
              datasetDict[allOutletsUserOrdersDoneToday[j].outletId].data[
                i
              ].value += allOutletsUserOrdersDoneToday[j][yAxisKey];
              // record.value += allOutletsUserOrdersDoneToday[j][yAxisKey];
            }
          }

          // if (record.value > yMaxValue) {
          //     yMaxValue = record.value;
          // }

          // record.value = record.value.toFixed(0);

          // parsedData.push(record);
        }
      }
    } else if (chartPeriod === CHART_PERIOD.NONE) {
      const allOutletsUserOrdersDoneTodayRange =
        allOutletsUserOrdersDone.filter((order) => {
          return (
            moment(startDate).isSameOrBefore(order.createdAt) &&
            moment(endDate).isAfter(order.createdAt)
            // compareOrderDateByDisplayType(
            //   startDate,
            //   order.createdAt,
            //   DATE_COMPARE_TYPE.IS_SAME_OR_BEFORE,
            //   '',
            //   reportDisplayType,
            //   outletShifts
            // )
            // &&
            // compareOrderDateByDisplayType(
            //   endDate,
            //   order.createdAt,
            //   DATE_COMPARE_TYPE.IS_AFTER,
            //   '',
            //   reportDisplayType,
            //   outletShifts
            // )
          );
        });

      if (
        moment(endDate).diff(startDate, 'day') <=
        moment(startDate).daysInMonth()
      ) {
        endDate = moment(endDate).add(1, 'day');

        const diffDays = moment(endDate).diff(startDate, 'day');

        var prevDate = moment(startDate).add(-1, 'month');

        for (var i = 0; i < allOutlets.length; i++) {
          datasetDict[allOutlets[i].uniqueId] = {
            seriesname: allOutlets[i].name,
            data: Array.from(Array(diffDays)).map(() => {
              return { value: 0 };
            }),
          };
        }

        for (var i = 0; i < diffDays; i++) {
          const currDate = moment(startDate).add(i, 'day');

          category.push({
            //'label': moment(currDate).format('DD MMM'),
            label: !moment(currDate).isSame(prevDate, 'month')
              ? moment(currDate).format('DD MMM')
              : moment(currDate).format('DD'),
            //'label': (i > 1 ? moment(currDate).format('DD') : moment(currDate).format('DD MMM'))
          });

          prevDate = currDate;

          for (var j = 0; j < allOutletsUserOrdersDoneTodayRange.length; j++) {
            if (
              // allOutletsUserOrdersDoneTodayRange[j].outletId === allOutlets[i].uniqueId &&
              // moment(currDate).isSame(
              //   moment(allOutletsUserOrdersDoneTodayRange[j].createdAt),
              //   'day',
              // )
              compareOrderDateByDisplayType(
                currDate,
                allOutletsUserOrdersDoneTodayRange[j].createdAt,
                DATE_COMPARE_TYPE.IS_SAME,
                'day',
                reportDisplayType,
                outletShifts
              )
            ) {
              datasetDict[allOutletsUserOrdersDoneTodayRange[j].outletId].data[
                i
              ].value += allOutletsUserOrdersDoneTodayRange[j][yAxisKey];
              // record.value += allOutletsUserOrdersDoneTodayRange[j][yAxisKey];
            }
          }

          // if (record.value > yMaxValue) {
          //     yMaxValue = record.value;
          // }

          // record.value = record.value.toFixed(0);

          // parsedData.push(record);
        }
      } else if (moment(endDate).diff(startDate, 'month') <= 6) {
        var prevMonth = moment(startDate);

        const diffWeeks = moment(endDate).diff(startDate, 'week');

        for (var i = 0; i < allOutlets.length; i++) {
          datasetDict[allOutlets[i].uniqueId] = {
            seriesname: allOutlets[i].name,
            data: Array.from(Array(diffWeeks)).map(() => {
              return { value: 0 };
            }),
          };
        }

        for (var i = 0; i < diffWeeks; i++) {
          const currDate = moment(startDate).add(i, 'week');

          if (moment(currDate).isSame(prevMonth, 'month')) {
            currWeekCount++;
          } else {
            prevMonth = moment(currDate);

            currWeekCount = 0;
            currWeekCount++;
          }

          category.push({
            // label: moment(currDate).format('MMM') + ' W' + currWeekCount,
            label:
              `${moment(currDate).format('MMM')
              } W${countWeekdayOccurrencesInMonth(currDate)}`,
          });

          for (var j = 0; j < allOutletsUserOrdersDoneTodayRange.length; j++) {
            if (
              // allOutletsUserOrdersDoneTodayRange[j].outletId === allOutlets[i].uniqueId &&
              // moment(currDate).isSame(
              //   moment(allOutletsUserOrdersDoneTodayRange[j].createdAt),
              //   'week',
              // )
              compareOrderDateByDisplayType(
                currDate,
                allOutletsUserOrdersDoneTodayRange[j].createdAt,
                DATE_COMPARE_TYPE.IS_SAME,
                'week',
                reportDisplayType,
                outletShifts
              )
            ) {
              datasetDict[allOutletsUserOrdersDoneTodayRange[j].outletId].data[
                i
              ].value += allOutletsUserOrdersDoneTodayRange[j][yAxisKey];
              // record.value += allOutletsUserOrdersDoneTodayRange[j][yAxisKey];
            }
          }

          // if (record.value > yMaxValue) {
          //     yMaxValue = record.value;
          // }

          // record.value = record.value.toFixed(0);

          // parsedData.push(record);
        }
      }
      // if (moment(endDate).diff(startDate, 'month') <= 6) // for now no need conditions first, for more than 6 months interval
      else {
        const diffYears = moment(endDate).diff(startDate, 'month');

        for (var i = 0; i < allOutlets.length; i++) {
          datasetDict[allOutlets[i].uniqueId] = {
            seriesname: allOutlets[i].name,
            data: Array.from(Array(diffYears)).map(() => {
              return { value: 0 };
            }),
          };
        }

        for (var i = 0; i < diffYears; i++) {
          const currDate = moment(startDate).add(i, 'month');

          category.push({
            label: moment(currDate).format("YY' MMM"),
          });

          for (var j = 0; j < allOutletsUserOrdersDoneTodayRange.length; j++) {
            if (
              // allOutletsUserOrdersDoneTodayRange[j].outletId === allOutlets[i].uniqueId &&
              // moment(currDate).isSame(
              //   moment(allOutletsUserOrdersDoneTodayRange[j].createdAt),
              //   'month',
              // )
              compareOrderDateByDisplayType(
                currDate,
                allOutletsUserOrdersDoneTodayRange[j].createdAt,
                DATE_COMPARE_TYPE.IS_SAME,
                'month',
                reportDisplayType,
                outletShifts
              )
            ) {
              datasetDict[allOutletsUserOrdersDoneTodayRange[j].outletId].data[
                i
              ].value += allOutletsUserOrdersDoneTodayRange[j][yAxisKey];
              // record.value += allOutletsUserOrdersDoneTodayRange[j][yAxisKey];
            }
          }

          // if (record.value > yMaxValue) {
          //     yMaxValue = record.value;
          // }

          // record.value = record.value.toFixed(0);

          // parsedData.push(record);
        }
      }
    }

    // const allOutletsUserOrdersDoneToday = allOutletsUserOrdersDone.filter(order => {
    //     return moment().isSame(moment(order.createdAt), 'year');
    // });

    // for (var i = 0; i < 12; i++) {
    //     var record = {
    //         label: MONTH_TO_CHART_LABEL[i],
    //         value: 0,
    //     };

    //     const currMonth = moment().startOf('year').add(i, 'month');

    //     for (var j = 0; j < allOutletsUserOrdersDoneToday.length; j++) {
    //         // if (allOutletsUserOrdersDoneToday[j].outletId === allOutlets[i].uniqueId) {
    //         //     record.value += allOutletsUserOrdersDoneToday[j].finalPrice;

    //         //     // if (allOutletsUserOrdersDoneToday[j].finalPrice > yMaxValue) {
    //         //     //     yMaxValue += allOutletsUserOrdersDoneToday[j].finalPrice;
    //         //     // }
    //         // }

    //         if (moment(currMonth).isSame(moment(allOutletsUserOrdersDoneToday[j].createdAt), 'month')) {
    //             record.value += allOutletsUserOrdersDoneToday[j].finalPrice;
    //         }
    //     }

    //     // yMaxValue += record.value;
    //     if (record.value > yMaxValue) {
    //         yMaxValue = record.value;
    //     }

    //     record.value = record.value.toFixed(0);

    //     parsedData.push(record);
    // }

    for (var j = 0; j < allOutlets.length; j++) {
      var outletId = allOutlets[j].uniqueId;

      var currTotal = 0;

      for (var k = 0; k < datasetDict[outletId].data.length; k++) {
        // currTotal += datasetDict[outletId].data[k].value;

        currTotal = datasetDict[outletId].data[k].value;

        if (currTotal > yMaxValue) {
          yMaxValue = currTotal;
        }
      }

      // if (currTotal > yMaxValue) {
      //     yMaxValue = currTotal;
      // }
    }

    var chartLegend = [];

    const dataset = Object.entries(datasetDict).map(([key, value], index) => {
      const mappedIndex =
        index % CHART_COLORS[CHART_TYPE.DASHBOARD_LINE_CHART_SALES].length; // might got more than 10 preset colors, set to infinite list instead

      chartLegend.push({
        itemName: value.seriesname,
        itemSku: value.seriesname,
        chartColor:
          CHART_COLORS[CHART_TYPE.DASHBOARD_LINE_CHART_SALES][mappedIndex],
      });

      return {
        ...value,
        color: CHART_COLORS[CHART_TYPE.DASHBOARD_LINE_CHART_SALES][mappedIndex],
      };
    });

    var output = {};

    output.type = CHART_DATA[CHART_TYPE.DASHBOARD_LINE_CHART_SALES].type;
    output.dataFormat =
      CHART_DATA[CHART_TYPE.DASHBOARD_LINE_CHART_SALES].dataFormat;
    output.dataSource = {
      chart: CHART_DATA[CHART_TYPE.DASHBOARD_LINE_CHART_SALES].chart,

      categories: [
        {
          category,
        },
      ],
      dataset,

      annotations,

      // data: parsedData,
    };

    // // console.log('parsedData');
    // // console.log(parsedData);
    // console.log(yMaxValue);

    if (output.type !== 'HLinearGauge') {
      // const yAxisStep = Math.ceil((yMaxValue / 10) / 10) * 10;

      // const yMaxValueParsed = Math.ceil(yMaxValue / yAxisStep);
      // const yAxisMaxValue = yMaxValueParsed === 1 ? 2 : yMaxValueParsed;
      // const numDivLines = yMaxValueParsed === 1 ? 1 : yMaxValueParsed - 1;

      const baseNumberLength = yMaxValue.toFixed(0).length - 1;
      const baseNumber = parseInt(
        `1${Array(baseNumberLength)
          .fill()
          .map((num) => 0)
          .join('')}`,
      );
      var maxNumber = baseNumber;

      if (yMaxValue > baseNumber * 5) {
        maxNumber = baseNumber * 10;
      } else if (yMaxValue > baseNumber * 3) {
        maxNumber = baseNumber * 5;
      } else if (yMaxValue > baseNumber * 2) {
        maxNumber = baseNumber * 3;
      } else {
        maxNumber = baseNumber * 2;
      }

      maxNumber = Math.max(maxNumber, 100);

      output.dataSource.chart = {
        ...output.dataSource.chart,
        labelDisplay: 'WRAP',
        // labelDisplay: 'STAGGER',
        // labelStep: 2,
        bgColor: '#ffffff',
        canvasBgColor: '#ffffff',
        bgAlpha: 100,

        useEllipsesWhenOverflow: 1,

        // showYAxisValues: 0,
        // yaxismaxvalue: yAxisMaxValue * yAxisStep,
        yaxismaxvalue: maxNumber,
        yaxisminvalue: yMinValue,
        adjustDiv: 0,
        // numDivLines: numDivLines,
        numDivLines: 9,

        // yaxisminvalue: 0,
        formatNumberScale: 0,
        decimals: 0,
      };

      // // console.log('/////////////////////////////////////////////////')

      // // console.log({
      //     yMaxValue: yMaxValue,
      //     yMaxValueParsed: yMaxValueParsed,
      //     yaxismaxvalue: yAxisMaxValue * yAxisStep,
      //     yaxisminvalue: yMinValue,
      //     adjustDiv: 0,
      //     numDivLines: numDivLines,
      // });

      // // console.log('/////////////////////////////////////////////////')

      return {
        chartData: output,
        chartLegend,
      };
    }

    // if (output.type === 'column2D') {
    //     if (type === CHART_TYPE.S_MILK) {
    //         // for (let i = 0; i < output.dataSource.data.length; i++) {
    //         //     if (output.dataSource.data[i].value.length > 0) {
    //         //         yMaxValue = parseInt(output.dataSource.data[i].value) > yMaxValue ? parseInt(output.dataSource.data[i].value) : yMaxValue;

    //         //     }
    //         // }

    //         // const yMaxValueParsed = Math.ceil(yMaxValue / 5);
    //         // const yAxisMaxValue = yMaxValueParsed === 1 ? 2 : yMaxValueParsed;
    //         // const numDivLines = yMaxValueParsed === 1 ? 1 : yMaxValueParsed - 1;

    //         // output.dataSource.chart = {
    //         //     ...output.dataSource.chart,
    //         //     adjustDiv: 0,
    //         //     numDivLines: numDivLines,
    //         //     yaxismaxvalue: yAxisMaxValue * 5,
    //         //     yaxisminvalue: yMinValue,
    //         //     formatNumberScale: 0,
    //         // };
    //     }
    //     else if (type === CHART_TYPE.S_SHIT_PEE) {
    //         for (let i = 0; i < output.dataSource.data.length; i++) {
    //             if (output.dataSource.data[i].value.length > 0) {
    //                 yMaxValue = parseInt(output.dataSource.data[i].value) > yMaxValue ? parseInt(output.dataSource.data[i].value) : yMaxValue;
    //             }
    //         }

    //         output.dataSource.chart = {
    //             ...output.dataSource.chart,
    //             adjustDiv: 0,
    //             numDivLines: yMaxValue,
    //             yaxismaxvalue: yMaxValue + 1,
    //             yaxisminvalue: yMinValue,
    //             formatNumberScale: 0,
    //         };
    //     }
    // }

    // console.log('sales line chart output');
    // console.log(output);

    return output;
  } catch (ex) {
    console.log('chart error');
    console.log(ex);
    return false;
  }
};

export const getDataForSalesLineChartHourly = (
  allOutlets,
  allOutletsUserOrdersDone,
  chartPeriod,
  yAxisKey,
  xAxisKey,
  startDate = new Date(),
  endDate = new Date(),
  reportDisplayType = REPORT_DISPLAY_TYPE.DAY,
  outletShifts = [],
) => {
  try {
    var datasetDict = {};
    var category = [];
    
    var yMinValue = 0;
    var yMaxValue = 0;
    
    var annotations = {
      groups: [
        {
          items: [],
        },
      ],
    };
    
    // Initialize dataset for each outlet
    for (var i = 0; i < allOutlets.length; i++) {
      datasetDict[allOutlets[i].uniqueId] = {
        seriesname: allOutlets[i].name,
        data: Array.from(Array(24)).map(() => {
          return { value: 0 };
        }),
      };
    }
    
    // Generate 24-hour X-axis labels
    for (var hour = 0; hour < 24; hour++) {
      const ampm = hour < 12 ? 'am' : 'pm';
      const hour12 = hour === 0 ? 12 : hour > 12 ? hour - 12 : hour;
      category.push({
        label: `${hour12}${ampm}`,
      });
    }
    
    // Filter orders based on chart period
    let filteredOrders = allOutletsUserOrdersDone;
    
    // filteredOrders = allOutletsUserOrdersDone.filter((order) => {
    //   return (
    //     moment(startDate).isSameOrBefore(order.createdAt) &&
    //     moment(endDate).isAfter(order.createdAt)
    //   );
    // });
    
    // if (chartPeriod === CHART_PERIOD.TODAY) {
    //   filteredOrders = allOutletsUserOrdersDone.filter((order) => {
    //     return moment().isSame(moment(order.createdAt), 'day');
    //   });
    // } else if (chartPeriod === CHART_PERIOD.THIS_WEEK) {
    //   const weekStart = moment().startOf('week');
    //   const weekEnd = moment().endOf('week');
    //   filteredOrders = allOutletsUserOrdersDone.filter((order) => {
    //     return (
    //       moment(weekStart).isSameOrBefore(order.createdAt) &&
    //       moment(weekEnd).isAfter(order.createdAt)
    //     );
    //   });
    // } else if (chartPeriod === CHART_PERIOD.THIS_MONTH) {
    //   const monthStart = moment().startOf('month');
    //   const monthEnd = moment().endOf('month');
    //   filteredOrders = allOutletsUserOrdersDone.filter((order) => {
    //     return (
    //       moment(monthStart).isSameOrBefore(order.createdAt) &&
    //       moment(monthEnd).isAfter(order.createdAt)
    //     );
    //   });
    // } else if (chartPeriod === CHART_PERIOD.THREE_MONTHS) {
    //   const startDate = moment().subtract(2, 'month').startOf('month');
    //   const endDate = moment().endOf('month');
    //   filteredOrders = allOutletsUserOrdersDone.filter((order) => {
    //     return (
    //       moment(startDate).isSameOrBefore(order.createdAt) &&
    //       moment(endDate).isAfter(order.createdAt)
    //     );
    //   });
    // } else if (chartPeriod === CHART_PERIOD.SIX_MONTHS) {
    //   const startDate = moment().subtract(5, 'month').startOf('month');
    //   const endDate = moment().endOf('month');
    //   filteredOrders = allOutletsUserOrdersDone.filter((order) => {
    //     return (
    //       moment(startDate).isSameOrBefore(order.createdAt) &&
    //       moment(endDate).isAfter(order.createdAt)
    //     );
    //   });
    // } else if (chartPeriod === CHART_PERIOD.THIS_YEAR) {
    //   filteredOrders = allOutletsUserOrdersDone.filter((order) => {
    //     return moment().isSame(moment(order.createdAt), 'year');
    //   });
    // } else if (chartPeriod === CHART_PERIOD.YTD) {
    //   const startDate = moment().startOf('year');
    //   const endDate = moment();
    //   filteredOrders = allOutletsUserOrdersDone.filter((order) => {
    //     return (
    //       moment(startDate).isSameOrBefore(order.createdAt) &&
    //       moment(endDate).isAfter(order.createdAt)
    //     );
    //   });
    // } else if (chartPeriod === CHART_PERIOD.NONE) {
    //   filteredOrders = allOutletsUserOrdersDone.filter((order) => {
    //     return (
    //       moment(startDate).isSameOrBefore(order.createdAt) &&
    //       moment(endDate).isAfter(order.createdAt)
    //     );
    //   });
    // } else {
    //   // Default case - use the provided date
    //   filteredOrders = allOutletsUserOrdersDone.filter((order) => {
    //     return moment(startDate).isSame(moment(order.createdAt), 'day');
    //   });
    // }
    
    // Aggregate sales by hour for each outlet
    for (var j = 0; j < filteredOrders.length; j++) {
      const order = filteredOrders[j];
      const orderHour = moment(order.createdAt).hour();
      
      if (datasetDict[order.outletId]) {
        datasetDict[order.outletId].data[orderHour].value += parseFloat(order[yAxisKey] || 0);
      }
    }
    
    // Calculate max value for Y-axis scaling
    for (var j = 0; j < allOutlets.length; j++) {
      var outletId = allOutlets[j].uniqueId;
      
      for (var k = 0; k < datasetDict[outletId].data.length; k++) {
        var currTotal = datasetDict[outletId].data[k].value;
        
        if (currTotal > yMaxValue) {
          yMaxValue = currTotal;
        }
      }
    }
    
    // Generate chart legend and apply colors
    var chartLegend = [];
    
    const dataset = Object.entries(datasetDict).map(([key, value], index) => {
      const mappedIndex = index % CHART_COLORS[CHART_TYPE.DASHBOARD_LINE_CHART_SALES].length;
      
      const dataWithDecimals = value.data.map(item => ({
        value: parseFloat(item.value).toFixed(2)
      }));
      
      chartLegend.push({
        itemName: value.seriesname,
        itemSku: value.seriesname,
        chartColor: 
          CHART_COLORS[CHART_TYPE.DASHBOARD_LINE_CHART_SALES][mappedIndex],
      });
      
      return {
        ...value,
        data: dataWithDecimals,
        color: CHART_COLORS[CHART_TYPE.DASHBOARD_LINE_CHART_SALES][mappedIndex],
      };
    });
    
    // Set up chart configuration
    var output = {};
    
    output.type = CHART_DATA[CHART_TYPE.DASHBOARD_LINE_CHART_SALES].type;
    output.dataFormat = 
      CHART_DATA[CHART_TYPE.DASHBOARD_LINE_CHART_SALES].dataFormat;
    output.dataSource = {
      chart: {
        ...CHART_DATA[CHART_TYPE.DASHBOARD_LINE_CHART_SALES].chart,
        showLabels: "1",
        showLabels: "1",
        labelDisplay: "auto",
        showXAxisLine: "1",
        decimals: "2",
        forceDecimals: "1",
        showSum: "0",
        showValues: "0",
        placeValuesInside: "0",
        usePlotGradientColor: "0",
        showPlotBorder: "0",
        showToolTip: "1",
      },
      categories: [
        {
          category,
        },
      ],
      dataset,
      data: category.map((cat, index) => {
        let totalValue = 0;
        Object.values(datasetDict).forEach(outlet => {
          if (outlet.data && outlet.data[index]) {
            totalValue += parseFloat(outlet.data[index].value || 0);
          }
        });
        
        return {
          label: cat.label,
          value: totalValue.toFixed(2)
        };
      }),
      annotations,
    };
    
    // Set Y-axis range
    if (output.type !== 'HLinearGauge') {
      const baseNumberLength = yMaxValue.toFixed(0).length - 1;
      const baseNumber = parseInt(
        `1${Array(baseNumberLength)
          .fill()
          .map((num) => 0)
          .join('')}`,
      );
      var maxNumber = baseNumber;
      
      if (yMaxValue > baseNumber * 5) {
        maxNumber = baseNumber * 10;
      } else if (yMaxValue > baseNumber * 3) {
        maxNumber = baseNumber * 5;
      } else if (yMaxValue > baseNumber * 2) {
        maxNumber = baseNumber * 3;
      } else {
        maxNumber = baseNumber * 2;
      }
      
      maxNumber = Math.max(maxNumber, 100);

      output.dataSource.chart = {
        ...output.dataSource.chart,
        labelDisplay: 'WRAP',
        bgColor: '#ffffff',
        canvasBgColor: '#ffffff',
        bgAlpha: 100,
        
        useEllipsesWhenOverflow: 1,
        
        yaxismaxvalue: maxNumber,
        yaxisminvalue: yMinValue,
        adjustDiv: 0,
        numDivLines: 9,
        
        formatNumberScale: 0,
        decimals: 0,
      };
    }
    
    return {
      chartData: output,
      chartLegend,
    };
  } catch (ex) {
    console.log('chart error');
    console.log(ex);
    return false;
  }
};

export const getDataForChartReportRevisitSales = (
  allOutletsParam,
  allOutletsUserOrdersDone,
  chartPeriod,
  yAxisKey,
  xAxisKey,
  startDate = new Date(),
  endDate = new Date(),

  reportDisplayType = REPORT_DISPLAY_TYPE.DAY,
  outletShifts = [],
) => {
  try {
    // endDate = moment(endDate).add(1, 'day');
    // endDate = moment(endDate).endOf('day');

    // console.log('data before chart');

    var parsedData = [];

    var yMinValue = 0;
    var yMaxValue = 0;

    var category = [];
    var datasetDict = {};

    var outletNameDict = {};
    // for (var i = 0; i < allOutlets.length; i++) {
    //   outletNameDict[allOutlets[i].uniqueId] = allOutlets[i].name;
    // }

    outletNameDict.newCust = 'Walk-In Customer';
    outletNameDict.repeatCust = 'Regular Customer';

    var allOutlets = [
      {
        label: 'Walk-In Customer',
        value: 'newCust',
      },
      {
        label: 'Regular Customer',
        value: 'repeatCust',
      },
    ];

    // var annotations = {
    //     "groups": [{
    //         "items": [{
    //             //Text annotation 1
    //             "type": "text",
    //             "text": 'Hello',
    //             //Define the attributes needed to create a text annotation
    //         }, {
    //             //Text annotation n
    //             "type": "text",
    //             "text": 'Hi',
    //             //Define the attributes needed to create a text annotation
    //         }]
    //     }]
    // };

    var annotations = {
      groups: [
        {
          items: [],
        },
      ],
    };
    var annotationsItems = [];

    if (chartPeriod === CHART_PERIOD.TODAY) {
      const allOutletsUserOrdersDoneToday = allOutletsUserOrdersDone.filter(
        (order) => {
          // return moment().isSame(moment(order.createdAt), 'day');
          return compareOrderDateByDisplayType(
            moment().valueOf(),
            order.createdAt,
            DATE_COMPARE_TYPE.IS_SAME,
            'day',
            reportDisplayType,
            outletShifts
          )
        },
      );

      for (var i = 0; i < allOutlets.length; i++) {
        datasetDict[allOutlets[i].value] = {
          seriesname: allOutlets[i].label,
          data: Array.from(Array(24)).map(() => {
            return {
              value: 0,
              toolText: `RM 0.00`,
            };
          }),
        };
      }

      for (var i = 0; i < 24; i++) {
        const currHour = moment().startOf('day').add(i, 'hour');

        // var record = {
        //     label: i.toString().padStart(2, '0'),
        //     value: 0,
        // };

        category.push({
          label: i.toString().padStart(2, '0'),
        });

        for (var j = 0; j < allOutletsUserOrdersDoneToday.length; j++) {
          if (
            // moment(currHour).isSame(
            //   moment(allOutletsUserOrdersDoneToday[j].createdAt),
            //   'hour',
            // )
            compareOrderDateByDisplayType(
              currHour,
              allOutletsUserOrdersDoneToday[j].createdAt,
              DATE_COMPARE_TYPE.IS_SAME,
              'hour',
              reportDisplayType,
              outletShifts
            )
          ) {
            if (
              (allOutletsUserOrdersDoneToday[j].settlementDetails &&
                allOutletsUserOrdersDoneToday[j].settlementDetails.repeatCust)
              ||
              allOutletsUserOrdersDoneToday[j].repeatCust
            ) {
              datasetDict.repeatCust.data[
                i
              ].value += allOutletsUserOrdersDoneToday[j][yAxisKey];
              // record.value += allOutletsUserOrdersDoneToday[j][yAxisKey];

              datasetDict.repeatCust.data[
                i
              ].toolText = `RM ${datasetDict.repeatCust.data[
                i
              ].value.toFixed(2)}`;
            }
            else {
              datasetDict.newCust.data[
                i
              ].value += allOutletsUserOrdersDoneToday[j][yAxisKey];

              datasetDict.newCust.data[
                i
              ].toolText = `RM ${datasetDict.newCust.data[
                i
              ].value.toFixed(2)}`;
            }
          }
        }

        // if (record.value > yMaxValue) {
        //     yMaxValue = record.value;
        // }

        // record.value = record.value.toFixed(0);

        // parsedData.push(record);
      }
    } else if (chartPeriod === CHART_PERIOD.THIS_WEEK) {
      // const allOutletsUserOrdersDoneToday = allOutletsUserOrdersDone.filter(order => {
      //     return moment().isSame(moment(order.createdAt), 'week');
      // });

      const startDate = moment(Date.now()).subtract(7, 'days').startOf('day');
      const endDate = moment().endOf('day');

      const allOutletsUserOrdersDoneToday = allOutletsUserOrdersDone.filter(
        (order) => {
          return (
            moment(startDate).isSameOrBefore(order.createdAt) &&
            moment(endDate).isAfter(order.createdAt)
          );
        },
      );

      // console.log('allOutletsUserOrdersDoneToday');
      // console.log(allOutletsUserOrdersDoneToday);

      for (var i = 0; i < allOutlets.length; i++) {
        datasetDict[allOutlets[i].value] = {
          seriesname: allOutlets[i].label,
          data: Array.from(Array(moment(endDate).diff(startDate, 'day'))).map(
            () => {
              return {
                value: 0,
                toolText: `RM 0.00`,
              };
            },
          ),
        };
      }

      for (var i = 0; i < moment(endDate).diff(startDate, 'day'); i++) {
        // const currDay = moment().startOf('week').add(i, 'day');
        const currDay = moment(startDate).add(i, 'day');

        // var record = {
        //     label: moment().day(i).format('ddd'),
        //     value: 0,
        // };

        category.push({
          label: moment(startDate).add(i, 'day').format('ddd'),
        });

        for (var j = 0; j < allOutletsUserOrdersDoneToday.length; j++) {
          if (
            // moment(currDay).isSame(
            //   moment(allOutletsUserOrdersDoneToday[j].createdAt),
            //   'day',
            // )
            compareOrderDateByDisplayType(
              currDay,
              allOutletsUserOrdersDoneToday[j].createdAt,
              DATE_COMPARE_TYPE.IS_SAME,
              'day',
              reportDisplayType,
              outletShifts
            )
          ) {
            if (
              (allOutletsUserOrdersDoneToday[j].settlementDetails &&
                allOutletsUserOrdersDoneToday[j].settlementDetails.repeatCust)
              ||
              allOutletsUserOrdersDoneToday[j].repeatCust
            ) {
              datasetDict.repeatCust.data[
                i
              ].value += allOutletsUserOrdersDoneToday[j][yAxisKey];
              // record.value += allOutletsUserOrdersDoneToday[j][yAxisKey];

              datasetDict.repeatCust.data[
                i
              ].toolText = `RM ${datasetDict.repeatCust.data[
                i
              ].value.toFixed(2)}`;
            }
            else {
              datasetDict.newCust.data[
                i
              ].value += allOutletsUserOrdersDoneToday[j][yAxisKey];

              datasetDict.newCust.data[
                i
              ].toolText = `RM ${datasetDict.newCust.data[
                i
              ].value.toFixed(2)}`;
            }
          }
        }

        // if (record.value > yMaxValue) {
        //     yMaxValue = record.value;
        // }

        // record.value = record.value.toFixed(0);

        // parsedData.push(record);
      }
    } else if (chartPeriod === CHART_PERIOD.THIS_MONTH) {
      // const allOutletsUserOrdersDoneToday = allOutletsUserOrdersDone.filter(order => {
      //     return moment().isSame(moment(order.createdAt), 'month');
      // });

      // const startDate = moment().subtract(moment(startDate).daysInMonth(), 'day').startOf('day');
      // const endDate = moment().endOf('day');

      const allOutletsUserOrdersDoneToday = allOutletsUserOrdersDone.filter(
        (order) => {
          return (
            moment(startDate).isSameOrBefore(order.createdAt) &&
            moment(endDate).isAfter(order.createdAt)
          );
        },
      );

      if (xAxisKey === CHART_X_AXIS_TYPE.DAY) {
        for (var i = 0; i < allOutlets.length; i++) {
          datasetDict[allOutlets[i].value] = {
            seriesname: allOutlets[i].label,
            data: Array.from(Array(moment(startDate).daysInMonth())).map(() => {
              return {
                value: 0,
                toolText: `RM 0.00`,
              };
            }),
          };
        }

        for (var i = 0; i < moment(startDate).daysInMonth(); i++) {
          // for (var i = 0; i < 31; i++) {
          // const currDay = moment().startOf('month').add(i, 'day');
          const currDay = moment(startDate).add(i, 'day');

          // var record = {
          //     label: i.toString().padStart(2, '0'),
          //     value: 0,
          // };

          category.push({
            label: (i + 1).toString().padStart(2, '0'),
          });

          for (var j = 0; j < allOutletsUserOrdersDoneToday.length; j++) {
            if (
              // moment(currDay).isSame(
              //   moment(allOutletsUserOrdersDoneToday[j].createdAt),
              //   'day',
              // )
              compareOrderDateByDisplayType(
                currDay,
                allOutletsUserOrdersDoneToday[j].createdAt,
                DATE_COMPARE_TYPE.IS_SAME,
                'day',
                reportDisplayType,
                outletShifts
              )
            ) {
              if (
                (allOutletsUserOrdersDoneToday[j].settlementDetails &&
                  allOutletsUserOrdersDoneToday[j].settlementDetails.repeatCust)
                ||
                allOutletsUserOrdersDoneToday[j].repeatCust
              ) {
                datasetDict.repeatCust.data[
                  i
                ].value += allOutletsUserOrdersDoneToday[j][yAxisKey];
                // record.value += allOutletsUserOrdersDoneToday[j][yAxisKey];

                datasetDict.repeatCust.data[
                  i
                ].toolText = `RM ${datasetDict.repeatCust.data[
                  i
                ].value.toFixed(2)}`;
              }
              else {
                datasetDict.newCust.data[
                  i
                ].value += allOutletsUserOrdersDoneToday[j][yAxisKey];

                datasetDict.newCust.data[
                  i
                ].toolText = `RM ${datasetDict.newCust.data[
                  i
                ].value.toFixed(2)}`;
              }
            }
          }

          // if (record.value > yMaxValue) {
          //     yMaxValue = record.value;
          // }

          // record.value = record.value.toFixed(0);

          // parsedData.push(record);
        }
      }
      else if (xAxisKey === CHART_X_AXIS_TYPE.WEEK || xAxisKey === CHART_X_AXIS_TYPE.MONTH) {
        var currWeekCount = 0;
        var prevMonth = moment(startDate);

        for (var i = 0; i < allOutlets.length; i++) {
          datasetDict[allOutlets[i].value] = {
            seriesname: allOutlets[i].label,
            data: Array.from(Array(moment(endDate).diff(startDate, 'week'))).map(
              () => {
                return {
                  value: 0,
                  toolText: `RM 0.00`,
                };
              },
            ),
          };
        }

        for (var i = 0; i < moment(endDate).diff(startDate, 'week'); i++) {
          const currWeek = moment(startDate).add(i, 'week');

          if (moment(currWeek).isSame(prevMonth, 'month')) {
            currWeekCount++;
          } else {
            prevMonth = moment(currWeek);

            currWeekCount = 0;
            currWeekCount++;
          }

          // var record = {
          //     label: (currWeekCount > 1 ? '' : moment(currWeek).format('MMM')) + ' W' + currWeekCount,
          //     value: 0,
          // };

          category.push({
            label:
              `${currWeekCount > 1 ? '' : moment(currWeek).format('MMM')
              } W${currWeekCount}`,
          });

          for (var j = 0; j < allOutletsUserOrdersDoneToday.length; j++) {
            if (
              // moment(currWeek).isSame(
              //   moment(allOutletsUserOrdersDoneToday[j].createdAt),
              //   'week',
              // )
              compareOrderDateByDisplayType(
                currWeek,
                allOutletsUserOrdersDoneToday[j].createdAt,
                DATE_COMPARE_TYPE.IS_SAME,
                'week',
                reportDisplayType,
                outletShifts
              )
            ) {
              if (
                (allOutletsUserOrdersDoneToday[j].settlementDetails &&
                  allOutletsUserOrdersDoneToday[j].settlementDetails.repeatCust)
                ||
                allOutletsUserOrdersDoneToday[j].repeatCust
              ) {
                datasetDict.repeatCust.data[
                  i
                ].value += allOutletsUserOrdersDoneToday[j][yAxisKey];
                // record.value += allOutletsUserOrdersDoneToday[j][yAxisKey];

                datasetDict.repeatCust.data[
                  i
                ].toolText = `RM ${datasetDict.repeatCust.data[
                  i
                ].value.toFixed(2)}`;
              }
              else {
                datasetDict.newCust.data[
                  i
                ].value += allOutletsUserOrdersDoneToday[j][yAxisKey];

                datasetDict.newCust.data[
                  i
                ].toolText = `RM ${datasetDict.newCust.data[
                  i
                ].value.toFixed(2)}`;
              }
            }
          }

          // if (record.value > yMaxValue) {
          //     yMaxValue = record.value;
          // }

          // record.value = record.value.toFixed(0);

          // parsedData.push(record);
        }
      }
    } else if (chartPeriod === CHART_PERIOD.THREE_MONTHS) {
      const startDate = moment().subtract(2, 'month').startOf('month');
      const endDate = moment().endOf('month');

      const allOutletsUserOrdersDoneToday = allOutletsUserOrdersDone.filter(
        (order) => {
          return (
            moment(startDate).isSameOrBefore(order.createdAt) &&
            moment(endDate).isAfter(order.createdAt)
          );
        },
      );

      if (xAxisKey === CHART_X_AXIS_TYPE.DAY) {
        for (var i = 0; i < allOutlets.length; i++) {
          datasetDict[allOutlets[i].value] = {
            seriesname: allOutlets[i].label,
            data: Array.from(Array(moment(endDate).diff(startDate, 'day'))).map(() => {
              return {
                value: 0,
                toolText: `RM 0.00`,
              };
            }),
          };
        }

        for (var i = 0; i < moment(endDate).diff(startDate, 'day'); i++) {
          // for (var i = 0; i < 31; i++) {
          // const currDay = moment().startOf('month').add(i, 'day');
          const currDay = moment(startDate).add(i, 'day');

          // var record = {
          //     label: i.toString().padStart(2, '0'),
          //     value: 0,
          // };

          category.push({
            label: (i + 1).toString().padStart(2, '0'),
          });

          for (var j = 0; j < allOutletsUserOrdersDoneToday.length; j++) {
            if (
              // moment(currDay).isSame(
              //   moment(allOutletsUserOrdersDoneToday[j].createdAt),
              //   'day',
              // )
              compareOrderDateByDisplayType(
                currDay,
                allOutletsUserOrdersDoneToday[j].createdAt,
                DATE_COMPARE_TYPE.IS_SAME,
                'day',
                reportDisplayType,
                outletShifts
              )
            ) {
              if (
                (allOutletsUserOrdersDoneToday[j].settlementDetails &&
                  allOutletsUserOrdersDoneToday[j].settlementDetails.repeatCust)
                ||
                allOutletsUserOrdersDoneToday[j].repeatCust
              ) {
                datasetDict.repeatCust.data[
                  i
                ].value += allOutletsUserOrdersDoneToday[j][yAxisKey];
                // record.value += allOutletsUserOrdersDoneToday[j][yAxisKey];

                datasetDict.repeatCust.data[
                  i
                ].toolText = `RM ${datasetDict.repeatCust.data[
                  i
                ].value.toFixed(2)}`;
              }
              else {
                datasetDict.newCust.data[
                  i
                ].value += allOutletsUserOrdersDoneToday[j][yAxisKey];

                datasetDict.newCust.data[
                  i
                ].toolText = `RM ${datasetDict.newCust.data[
                  i
                ].value.toFixed(2)}`;
              }
            }
          }

          // if (record.value > yMaxValue) {
          //     yMaxValue = record.value;
          // }

          // record.value = record.value.toFixed(0);

          // parsedData.push(record);
        }
      }
      else if (xAxisKey === CHART_X_AXIS_TYPE.WEEK) {
        var currWeekCount = 0;
        var prevMonth = moment(startDate);

        for (var i = 0; i < allOutlets.length; i++) {
          datasetDict[allOutlets[i].value] = {
            seriesname: allOutlets[i].label,
            data: Array.from(Array(moment(endDate).diff(startDate, 'week'))).map(
              () => {
                return {
                  value: 0,
                  toolText: `RM 0.00`,
                };
              },
            ),
          };
        }

        for (var i = 0; i < moment(endDate).diff(startDate, 'week'); i++) {
          const currWeek = moment(startDate).add(i, 'week');

          if (moment(currWeek).isSame(prevMonth, 'month')) {
            currWeekCount++;
          } else {
            prevMonth = moment(currWeek);

            currWeekCount = 0;
            currWeekCount++;
          }

          // var record = {
          //     label: (currWeekCount > 1 ? '' : moment(currWeek).format('MMM')) + ' W' + currWeekCount,
          //     value: 0,
          // };

          category.push({
            label:
              `${currWeekCount > 1 ? '' : moment(currWeek).format('MMM')
              } W${currWeekCount}`,
          });

          for (var j = 0; j < allOutletsUserOrdersDoneToday.length; j++) {
            if (
              // moment(currWeek).isSame(
              //   moment(allOutletsUserOrdersDoneToday[j].createdAt),
              //   'week',
              // )
              compareOrderDateByDisplayType(
                currWeek,
                allOutletsUserOrdersDoneToday[j].createdAt,
                DATE_COMPARE_TYPE.IS_SAME,
                'week',
                reportDisplayType,
                outletShifts
              )
            ) {
              if (
                (allOutletsUserOrdersDoneToday[j].settlementDetails &&
                  allOutletsUserOrdersDoneToday[j].settlementDetails.repeatCust)
                ||
                allOutletsUserOrdersDoneToday[j].repeatCust
              ) {
                datasetDict.repeatCust.data[
                  i
                ].value += allOutletsUserOrdersDoneToday[j][yAxisKey];
                // record.value += allOutletsUserOrdersDoneToday[j][yAxisKey];

                datasetDict.repeatCust.data[
                  i
                ].toolText = `RM ${datasetDict.repeatCust.data[
                  i
                ].value.toFixed(2)}`;
              }
              else {
                datasetDict.newCust.data[
                  i
                ].value += allOutletsUserOrdersDoneToday[j][yAxisKey];

                datasetDict.newCust.data[
                  i
                ].toolText = `RM ${datasetDict.newCust.data[
                  i
                ].value.toFixed(2)}`;
              }
            }
          }

          // if (record.value > yMaxValue) {
          //     yMaxValue = record.value;
          // }

          // record.value = record.value.toFixed(0);

          // parsedData.push(record);
        }
      }
      else if (xAxisKey === CHART_X_AXIS_TYPE.MONTH) {
        for (var i = 0; i < allOutlets.length; i++) {
          datasetDict[allOutlets[i].value] = {
            seriesname: allOutlets[i].label,
            data: Array.from(Array(3)).map(() => {
              return {
                value: 0,
                toolText: `RM 0.00`,
              };
            }),
          };
        }

        var currMonth = moment(startDate).startOf('month');

        for (var i = 0; i < 3; i++) {
          // const currMonth = moment().startOf('year').add(i, 'month');
          var currMonth = moment(currMonth).add(i, 'month');

          // var record = {
          //     label: moment(currMonth).format('MMM'),
          //     value: 0,
          // };

          category.push({
            label: moment(currMonth).format('MMM'),
          });

          for (var j = 0; j < allOutletsUserOrdersDoneToday.length; j++) {
            if (
              // moment(currMonth).isSame(
              //   moment(allOutletsUserOrdersDoneToday[j].createdAt),
              //   'month',
              // )
              compareOrderDateByDisplayType(
                currMonth,
                allOutletsUserOrdersDoneToday[j].createdAt,
                DATE_COMPARE_TYPE.IS_SAME,
                'month',
                reportDisplayType,
                outletShifts
              )
            ) {
              if (
                (allOutletsUserOrdersDoneToday[j].settlementDetails &&
                  allOutletsUserOrdersDoneToday[j].settlementDetails.repeatCust)
                ||
                allOutletsUserOrdersDoneToday[j].repeatCust
              ) {
                datasetDict.repeatCust.data[
                  i
                ].value += allOutletsUserOrdersDoneToday[j][yAxisKey];
                // record.value += allOutletsUserOrdersDoneToday[j][yAxisKey];

                datasetDict.repeatCust.data[
                  i
                ].toolText = `RM ${datasetDict.repeatCust.data[
                  i
                ].value.toFixed(2)}`;
              }
              else {
                datasetDict.newCust.data[
                  i
                ].value += allOutletsUserOrdersDoneToday[j][yAxisKey];

                datasetDict.newCust.data[
                  i
                ].toolText = `RM ${datasetDict.newCust.data[
                  i
                ].value.toFixed(2)}`;
              }
            }
          }

          // if (record.value > yMaxValue) {
          //     yMaxValue = record.value;
          // }

          // record.value = record.value.toFixed(0);

          // parsedData.push(record);
        }
      }
    } else if (chartPeriod === CHART_PERIOD.SIX_MONTHS) {
      const startDate = moment().subtract(5, 'month').startOf('month');
      const endDate = moment().endOf('month');

      const allOutletsUserOrdersDoneToday = allOutletsUserOrdersDone.filter(
        (order) => {
          return (
            moment(startDate).isSameOrBefore(order.createdAt) &&
            moment(endDate).isAfter(order.createdAt)
          );
        },
      );

      if (xAxisKey === CHART_X_AXIS_TYPE.DAY || xAxisKey === CHART_X_AXIS_TYPE.WEEK) {
        for (var i = 0; i < allOutlets.length; i++) {
          datasetDict[allOutlets[i].value] = {
            seriesname: allOutlets[i].label,
            data: Array.from(Array(moment(endDate).diff(startDate, 'week'))).map(
              () => {
                return {
                  value: 0,
                  toolText: `RM 0.00`,
                };
              },
            ),
          };
        }

        for (var i = 0; i < moment(endDate).diff(startDate, 'week'); i++) {
          const currWeek = moment(startDate).add(i, 'week');

          if (moment(currWeek).isSame(prevMonth, 'month')) {
            currWeekCount++;
          } else {
            prevMonth = moment(currWeek);

            currWeekCount = 0;
            currWeekCount++;
          }

          // var record = {
          //     label: 'W' + currWeekCount + ' ' + (currWeekCount > 1 ? '' : moment(currWeek).format('MMM')),
          //     value: 0,
          // };

          category.push({
            label:
              `W${currWeekCount
              } ${currWeekCount > 1 ? '' : moment(currWeek).format('MMM')}`,
          });

          for (var j = 0; j < allOutletsUserOrdersDoneToday.length; j++) {
            if (
              // moment(currWeek).isSame(
              //   moment(allOutletsUserOrdersDoneToday[j].createdAt),
              //   'week',
              // )
              compareOrderDateByDisplayType(
                currWeek,
                allOutletsUserOrdersDoneToday[j].createdAt,
                DATE_COMPARE_TYPE.IS_SAME,
                'week',
                reportDisplayType,
                outletShifts
              )
            ) {
              if (
                (allOutletsUserOrdersDoneToday[j].settlementDetails &&
                  allOutletsUserOrdersDoneToday[j].settlementDetails.repeatCust)
                ||
                allOutletsUserOrdersDoneToday[j].repeatCust
              ) {
                datasetDict.repeatCust.data[
                  i
                ].value += allOutletsUserOrdersDoneToday[j][yAxisKey];
                // record.value += allOutletsUserOrdersDoneToday[j][yAxisKey];

                datasetDict.repeatCust.data[
                  i
                ].toolText = `RM ${datasetDict.repeatCust.data[
                  i
                ].value.toFixed(2)}`;
              }
              else {
                datasetDict.newCust.data[
                  i
                ].value += allOutletsUserOrdersDoneToday[j][yAxisKey];

                datasetDict.newCust.data[
                  i
                ].toolText = `RM ${datasetDict.newCust.data[
                  i
                ].value.toFixed(2)}`;
              }
            }
          }

          // if (record.value > yMaxValue) {
          //     yMaxValue = record.value;
          // }

          // record.value = record.value.toFixed(0);

          // parsedData.push(record);
        }
      }
      else if (xAxisKey === CHART_X_AXIS_TYPE.MONTH) {
        for (var i = 0; i < allOutlets.length; i++) {
          datasetDict[allOutlets[i].value] = {
            seriesname: allOutlets[i].label,
            data: Array.from(Array(6)).map(() => {
              return {
                value: 0,
                toolText: `RM 0.00`,
              };
            }),
          };
        }

        var currMonth = moment(startDate).startOf('month');

        for (var i = 0; i < 6; i++) {
          // const currMonth = moment().startOf('year').add(i, 'month');
          var currMonth = moment(currMonth).add(i, 'month');

          // var record = {
          //     label: moment(currMonth).format('MMM'),
          //     value: 0,
          // };

          category.push({
            label: moment(currMonth).format('MMM'),
          });

          for (var j = 0; j < allOutletsUserOrdersDoneToday.length; j++) {
            if (
              // moment(currMonth).isSame(
              //   moment(allOutletsUserOrdersDoneToday[j].createdAt),
              //   'month',
              // )
              compareOrderDateByDisplayType(
                currMonth,
                allOutletsUserOrdersDoneToday[j].createdAt,
                DATE_COMPARE_TYPE.IS_SAME,
                'month',
                reportDisplayType,
                outletShifts
              )
            ) {
              if (
                (allOutletsUserOrdersDoneToday[j].settlementDetails &&
                  allOutletsUserOrdersDoneToday[j].settlementDetails.repeatCust)
                ||
                allOutletsUserOrdersDoneToday[j].repeatCust
              ) {
                datasetDict.repeatCust.data[
                  i
                ].value += allOutletsUserOrdersDoneToday[j][yAxisKey];
                // record.value += allOutletsUserOrdersDoneToday[j][yAxisKey];

                datasetDict.repeatCust.data[
                  i
                ].toolText = `RM ${datasetDict.repeatCust.data[
                  i
                ].value.toFixed(2)}`;
              }
              else {
                datasetDict.newCust.data[
                  i
                ].value += allOutletsUserOrdersDoneToday[j][yAxisKey];

                datasetDict.newCust.data[
                  i
                ].toolText = `RM ${datasetDict.newCust.data[
                  i
                ].value.toFixed(2)}`;
              }
            }
          }

          // if (record.value > yMaxValue) {
          //     yMaxValue = record.value;
          // }

          // record.value = record.value.toFixed(0);

          // parsedData.push(record);
        }
      }
    } else if (chartPeriod === CHART_PERIOD.THIS_YEAR) {
      const allOutletsUserOrdersDoneToday = allOutletsUserOrdersDone.filter(
        (order) => {
          return moment().isSame(moment(order.createdAt), 'year');
        },
      );

      if (xAxisKey === CHART_X_AXIS_TYPE.DAY || xAxisKey === CHART_X_AXIS_TYPE.WEEK) {
        for (var i = 0; i < allOutlets.length; i++) {
          datasetDict[allOutlets[i].value] = {
            seriesname: allOutlets[i].label,
            data: Array.from(Array(moment(endDate).diff(startDate, 'week'))).map(
              () => {
                return {
                  value: 0,
                  toolText: `RM 0.00`,
                };
              },
            ),
          };
        }

        for (var i = 0; i < moment(endDate).diff(startDate, 'week'); i++) {
          const currWeek = moment(startDate).add(i, 'week');

          if (moment(currWeek).isSame(prevMonth, 'month')) {
            currWeekCount++;
          } else {
            prevMonth = moment(currWeek);

            currWeekCount = 0;
            currWeekCount++;
          }

          // var record = {
          //     label: 'W' + currWeekCount + ' ' + (currWeekCount > 1 ? '' : moment(currWeek).format('MMM')),
          //     value: 0,
          // };

          category.push({
            label:
              `W${currWeekCount
              } ${currWeekCount > 2 ? '' : moment(currWeek).format('MMM')}`,
          });

          for (var j = 0; j < allOutletsUserOrdersDoneToday.length; j++) {
            if (
              // moment(currWeek).isSame(
              //   moment(allOutletsUserOrdersDoneToday[j].createdAt),
              //   'week',
              // )
              compareOrderDateByDisplayType(
                currWeek,
                allOutletsUserOrdersDoneToday[j].createdAt,
                DATE_COMPARE_TYPE.IS_SAME,
                'week',
                reportDisplayType,
                outletShifts
              )
            ) {
              if (
                (allOutletsUserOrdersDoneToday[j].settlementDetails &&
                  allOutletsUserOrdersDoneToday[j].settlementDetails.repeatCust)
                ||
                allOutletsUserOrdersDoneToday[j].repeatCust
              ) {
                datasetDict.repeatCust.data[
                  i
                ].value += allOutletsUserOrdersDoneToday[j][yAxisKey];
                // record.value += allOutletsUserOrdersDoneToday[j][yAxisKey];

                datasetDict.repeatCust.data[
                  i
                ].toolText = `RM ${datasetDict.repeatCust.data[
                  i
                ].value.toFixed(2)}`;
              }
              else {
                datasetDict.newCust.data[
                  i
                ].value += allOutletsUserOrdersDoneToday[j][yAxisKey];

                datasetDict.newCust.data[
                  i
                ].toolText = `RM ${datasetDict.newCust.data[
                  i
                ].value.toFixed(2)}`;
              }
            }
          }

          // if (record.value > yMaxValue) {
          //     yMaxValue = record.value;
          // }

          // record.value = record.value.toFixed(0);

          // parsedData.push(record);
        }
      }
      else if (xAxisKey === CHART_X_AXIS_TYPE.MONTH) {
        for (var i = 0; i < allOutlets.length; i++) {
          datasetDict[allOutlets[i].value] = {
            seriesname: allOutlets[i].label,
            data: Array.from(Array(12)).map(() => {
              return {
                value: 0,
                toolText: `RM 0.00`,
              };
            }),
          };
        }

        for (var i = 0; i < 12; i++) {
          const currMonth = moment().startOf('year').add(i, 'month');

          // var record = {
          //     label: moment(currMonth).format('MMM'),
          //     value: 0,
          // };

          category.push({
            label: moment(currMonth).format('MMM'),
          });

          for (var j = 0; j < allOutletsUserOrdersDoneToday.length; j++) {
            if (
              // moment(currMonth).isSame(
              //   moment(allOutletsUserOrdersDoneToday[j].createdAt),
              //   'month',
              // )
              compareOrderDateByDisplayType(
                currMonth,
                allOutletsUserOrdersDoneToday[j].createdAt,
                DATE_COMPARE_TYPE.IS_SAME,
                'month',
                reportDisplayType,
                outletShifts
              )
            ) {
              if (
                (allOutletsUserOrdersDoneToday[j].settlementDetails &&
                  allOutletsUserOrdersDoneToday[j].settlementDetails.repeatCust)
                ||
                allOutletsUserOrdersDoneToday[j].repeatCust
              ) {
                datasetDict.repeatCust.data[
                  i
                ].value += allOutletsUserOrdersDoneToday[j][yAxisKey];
                // record.value += allOutletsUserOrdersDoneToday[j][yAxisKey];

                datasetDict.repeatCust.data[
                  i
                ].toolText = `RM ${datasetDict.repeatCust.data[
                  i
                ].value.toFixed(2)}`;
              }
              else {
                datasetDict.newCust.data[
                  i
                ].value += allOutletsUserOrdersDoneToday[j][yAxisKey];

                datasetDict.newCust.data[
                  i
                ].toolText = `RM ${datasetDict.newCust.data[
                  i
                ].value.toFixed(2)}`;
              }
            }
          }

          // if (record.value > yMaxValue) {
          //     yMaxValue = record.value;
          // }

          // record.value = record.value.toFixed(0);

          // parsedData.push(record);
        }
      }
    } else if (chartPeriod === CHART_PERIOD.YTD) {
      const startDate = moment().subtract(11, 'month').startOf('month');
      const endDate = moment().endOf('month');

      const allOutletsUserOrdersDoneToday = allOutletsUserOrdersDone.filter(
        (order) => {
          return (
            moment(startDate).isSameOrBefore(order.createdAt) &&
            moment(endDate).isAfter(order.createdAt)
          );
        },
      );

      if (xAxisKey === CHART_X_AXIS_TYPE.DAY || xAxisKey === CHART_X_AXIS_TYPE.WEEK) {
        for (var i = 0; i < allOutlets.length; i++) {
          datasetDict[allOutlets[i].value] = {
            seriesname: allOutlets[i].label,
            data: Array.from(Array(moment(endDate).diff(startDate, 'week'))).map(
              () => {
                return {
                  value: 0,
                  toolText: `RM 0.00`,
                };
              },
            ),
          };
        }

        for (var i = 0; i < moment(endDate).diff(startDate, 'week'); i++) {
          const currWeek = moment(startDate).add(i, 'week');

          if (moment(currWeek).isSame(prevMonth, 'month')) {
            currWeekCount++;
          } else {
            prevMonth = moment(currWeek);

            currWeekCount = 0;
            currWeekCount++;
          }

          // var record = {
          //     label: 'W' + currWeekCount + ' ' + (currWeekCount > 1 ? '' : moment(currWeek).format('MMM')),
          //     value: 0,
          // };

          category.push({
            label:
              `W${currWeekCount
              } ${currWeekCount > 2 ? '' : moment(currWeek).format('MMM')}`,
          });

          for (var j = 0; j < allOutletsUserOrdersDoneToday.length; j++) {
            if (
              // moment(currWeek).isSame(
              //   moment(allOutletsUserOrdersDoneToday[j].createdAt),
              //   'week',
              // )
              compareOrderDateByDisplayType(
                currWeek,
                allOutletsUserOrdersDoneToday[j].createdAt,
                DATE_COMPARE_TYPE.IS_SAME,
                'week',
                reportDisplayType,
                outletShifts
              )
            ) {
              if (
                (allOutletsUserOrdersDoneToday[j].settlementDetails &&
                  allOutletsUserOrdersDoneToday[j].settlementDetails.repeatCust)
                ||
                allOutletsUserOrdersDoneToday[j].repeatCust
              ) {
                datasetDict.repeatCust.data[
                  i
                ].value += allOutletsUserOrdersDoneToday[j][yAxisKey];
                // record.value += allOutletsUserOrdersDoneToday[j][yAxisKey];

                datasetDict.repeatCust.data[
                  i
                ].toolText = `RM ${datasetDict.repeatCust.data[
                  i
                ].value.toFixed(2)}`;
              }
              else {
                datasetDict.newCust.data[
                  i
                ].value += allOutletsUserOrdersDoneToday[j][yAxisKey];

                datasetDict.newCust.data[
                  i
                ].value += allOutletsUserOrdersDoneToday[j][yAxisKey];

                datasetDict.newCust.data[
                  i
                ].toolText = `RM ${datasetDict.newCust.data[
                  i
                ].value.toFixed(2)}`;
              }
            }
          }

          // if (record.value > yMaxValue) {
          //     yMaxValue = record.value;
          // }

          // record.value = record.value.toFixed(0);

          // parsedData.push(record);
        }
      }
      else if (xAxisKey === CHART_X_AXIS_TYPE.MONTH) {
        for (var i = 0; i < allOutlets.length; i++) {
          datasetDict[allOutlets[i].value] = {
            seriesname: allOutlets[i].label,
            data: Array.from(Array(12)).map(() => {
              return {
                value: 0,
                toolText: `RM 0.00`,
              };
            }),
          };
        }

        for (var i = 0; i < 12; i++) {
          const currMonth = moment(startDate).add(i, 'month');

          // var record = {
          //     label: moment(currMonth).format('YY\' MMM'),
          //     value: 0,
          // };

          category.push({
            label: moment(currMonth).format("YY' MMM"),
          });

          for (var j = 0; j < allOutletsUserOrdersDoneToday.length; j++) {
            if (
              // moment(currMonth).isSame(
              //   moment(allOutletsUserOrdersDoneToday[j].createdAt),
              //   'month',
              // )
              compareOrderDateByDisplayType(
                currMonth,
                allOutletsUserOrdersDoneToday[j].createdAt,
                DATE_COMPARE_TYPE.IS_SAME,
                'month',
                reportDisplayType,
                outletShifts
              )
            ) {
              if (
                (allOutletsUserOrdersDoneToday[j].settlementDetails &&
                  allOutletsUserOrdersDoneToday[j].settlementDetails.repeatCust)
                ||
                allOutletsUserOrdersDoneToday[j].repeatCust
              ) {
                datasetDict.repeatCust.data[
                  i
                ].value += allOutletsUserOrdersDoneToday[j][yAxisKey];
                // record.value += allOutletsUserOrdersDoneToday[j][yAxisKey];

                datasetDict.repeatCust.data[
                  i
                ].toolText = `RM ${datasetDict.repeatCust.data[
                  i
                ].value.toFixed(2)}`;
              }
              else {
                datasetDict.newCust.data[
                  i
                ].value += allOutletsUserOrdersDoneToday[j][yAxisKey];

                datasetDict.newCust.data[
                  i
                ].toolText = `RM ${datasetDict.newCust.data[
                  i
                ].value.toFixed(2)}`;
              }
            }
          }

          // if (record.value > yMaxValue) {
          //     yMaxValue = record.value;
          // }

          // record.value = record.value.toFixed(0);

          // parsedData.push(record);
        }
      }
    } else if (chartPeriod === CHART_PERIOD.NONE) {
      const allOutletsUserOrdersDoneTodayRange =
        allOutletsUserOrdersDone.filter((order) => {
          return (
            moment(startDate).isSameOrBefore(order.createdAt) &&
            moment(endDate).isAfter(order.createdAt)
          );
        });

      if (
        moment(endDate).diff(startDate, 'day') <=
        moment(startDate).daysInMonth()
      ) {
        endDate = moment(endDate).add(1, 'day');

        const diffDays = moment(endDate).diff(startDate, 'day');

        var prevDate = moment(startDate).add(-1, 'month');

        for (var i = 0; i < allOutlets.length; i++) {
          datasetDict[allOutlets[i].value] = {
            seriesname: allOutlets[i].label,
            data: Array.from(Array(diffDays)).map(() => {
              return {
                value: 0,
                toolText: `RM 0.00`,
              };
            }),
          };
        }

        for (var i = 0; i < diffDays; i++) {
          const currDate = moment(startDate).add(i, 'day');

          category.push({
            //'label': moment(currDate).format('DD MMM'),
            label: !moment(currDate).isSame(prevDate, 'month')
              ? moment(currDate).format('DD MMM')
              : moment(currDate).format('DD'),
            //'label': (i > 1 ? moment(currDate).format('DD') : moment(currDate).format('DD MMM'))
          });

          prevDate = currDate;

          for (var j = 0; j < allOutletsUserOrdersDoneTodayRange.length; j++) {
            if (
              // allOutletsUserOrdersDoneTodayRange[j].outletId === allOutlets[i].uniqueId &&
              // moment(currDate).isSame(
              //   moment(allOutletsUserOrdersDoneTodayRange[j].createdAt),
              //   'day',
              // )
              compareOrderDateByDisplayType(
                currDate,
                allOutletsUserOrdersDoneTodayRange[j].createdAt,
                DATE_COMPARE_TYPE.IS_SAME,
                'day',
                reportDisplayType,
                outletShifts
              )
            ) {
              if (
                (allOutletsUserOrdersDoneTodayRange[j].settlementDetails &&
                  allOutletsUserOrdersDoneTodayRange[j].settlementDetails.repeatCust)
                ||
                allOutletsUserOrdersDoneTodayRange[j].repeatCust
              ) {
                datasetDict.repeatCust.data[
                  i
                ].value += allOutletsUserOrdersDoneTodayRange[j][yAxisKey];
                // record.value += allOutletsUserOrdersDoneToday[j][yAxisKey];

                datasetDict.repeatCust.data[
                  i
                ].toolText = `RM ${datasetDict.repeatCust.data[
                  i
                ].value.toFixed(2)}`;
              }
              else {
                datasetDict.newCust.data[
                  i
                ].value += allOutletsUserOrdersDoneTodayRange[j][yAxisKey];

                datasetDict.newCust.data[
                  i
                ].toolText = `RM ${datasetDict.newCust.data[
                  i
                ].value.toFixed(2)}`;
              }
            }
          }

          // if (record.value > yMaxValue) {
          //     yMaxValue = record.value;
          // }

          // record.value = record.value.toFixed(0);

          // parsedData.push(record);
        }
      } else if (moment(endDate).diff(startDate, 'month') <= 6) {
        var prevMonth = moment(startDate);

        const diffWeeks = moment(endDate).diff(startDate, 'week');

        for (var i = 0; i < allOutlets.length; i++) {
          datasetDict[allOutlets[i].value] = {
            seriesname: allOutlets[i].label,
            data: Array.from(Array(diffWeeks)).map(() => {
              return {
                value: 0,
                toolText: `RM 0.00`,
              };
            }),
          };
        }

        for (var i = 0; i < diffWeeks; i++) {
          const currDate = moment(startDate).add(i, 'week');

          if (moment(currDate).isSame(prevMonth, 'month')) {
            currWeekCount++;
          } else {
            prevMonth = moment(currDate);

            currWeekCount = 0;
            currWeekCount++;
          }

          category.push({
            // label: moment(currDate).format('MMM') + ' W' + currWeekCount,
            label:
              `${moment(currDate).format('MMM')
              } W${countWeekdayOccurrencesInMonth(currDate)}`,
          });

          for (var j = 0; j < allOutletsUserOrdersDoneTodayRange.length; j++) {
            if (
              // allOutletsUserOrdersDoneTodayRange[j].outletId === allOutlets[i].uniqueId &&
              // moment(currDate).isSame(
              //   moment(allOutletsUserOrdersDoneTodayRange[j].createdAt),
              //   'week',
              // )
              compareOrderDateByDisplayType(
                currDate,
                allOutletsUserOrdersDoneTodayRange[j].createdAt,
                DATE_COMPARE_TYPE.IS_SAME,
                'week',
                reportDisplayType,
                outletShifts
              )
            ) {
              if (
                (allOutletsUserOrdersDoneTodayRange[j].settlementDetails &&
                  allOutletsUserOrdersDoneTodayRange[j].settlementDetails.repeatCust)
                ||
                allOutletsUserOrdersDoneTodayRange[j].repeatCust
              ) {
                datasetDict.repeatCust.data[
                  i
                ].value += allOutletsUserOrdersDoneTodayRange[j][yAxisKey];
                // record.value += allOutletsUserOrdersDoneToday[j][yAxisKey];

                datasetDict.repeatCust.data[
                  i
                ].toolText = `RM ${datasetDict.repeatCust.data[
                  i
                ].value.toFixed(2)}`;
              }
              else {
                datasetDict.newCust.data[
                  i
                ].value += allOutletsUserOrdersDoneTodayRange[j][yAxisKey];

                datasetDict.newCust.data[
                  i
                ].toolText = `RM ${datasetDict.newCust.data[
                  i
                ].value.toFixed(2)}`;
              }
            }
          }

          // if (record.value > yMaxValue) {
          //     yMaxValue = record.value;
          // }

          // record.value = record.value.toFixed(0);

          // parsedData.push(record);
        }
      }
      // if (moment(endDate).diff(startDate, 'month') <= 6) // for now no need conditions first, for more than 6 months interval
      else {
        const diffYears = moment(endDate).diff(startDate, 'month');

        for (var i = 0; i < allOutlets.length; i++) {
          datasetDict[allOutlets[i].value] = {
            seriesname: allOutlets[i].label,
            data: Array.from(Array(diffYears)).map(() => {
              return {
                value: 0,
                toolText: `RM 0.00`,
              };
            }),
          };
        }

        for (var i = 0; i < diffYears; i++) {
          const currDate = moment(startDate).add(i, 'month');

          category.push({
            label: moment(currDate).format("YY' MMM"),
          });

          for (var j = 0; j < allOutletsUserOrdersDoneTodayRange.length; j++) {
            if (
              // allOutletsUserOrdersDoneTodayRange[j].outletId === allOutlets[i].uniqueId &&
              // moment(currDate).isSame(
              //   moment(allOutletsUserOrdersDoneTodayRange[j].createdAt),
              //   'month',
              // )
              compareOrderDateByDisplayType(
                currDate,
                allOutletsUserOrdersDoneTodayRange[j].createdAt,
                DATE_COMPARE_TYPE.IS_SAME,
                'month',
                reportDisplayType,
                outletShifts
              )
            ) {
              if (
                (allOutletsUserOrdersDoneTodayRange[j].settlementDetails &&
                  allOutletsUserOrdersDoneTodayRange[j].settlementDetails.repeatCust)
                ||
                allOutletsUserOrdersDoneTodayRange[j].repeatCust
              ) {
                datasetDict.repeatCust.data[
                  i
                ].value += allOutletsUserOrdersDoneTodayRange[j][yAxisKey];
                // record.value += allOutletsUserOrdersDoneToday[j][yAxisKey];

                datasetDict.repeatCust.data[
                  i
                ].toolText = `RM ${datasetDict.repeatCust.data[
                  i
                ].value.toFixed(2)}`;
              }
              else {
                datasetDict.newCust.data[
                  i
                ].value += allOutletsUserOrdersDoneTodayRange[j][yAxisKey];

                datasetDict.newCust.data[
                  i
                ].toolText = `RM ${datasetDict.newCust.data[
                  i
                ].value.toFixed(2)}`;
              }
            }
          }

          // if (record.value > yMaxValue) {
          //     yMaxValue = record.value;
          // }

          // record.value = record.value.toFixed(0);

          // parsedData.push(record);
        }
      }
    }

    // const allOutletsUserOrdersDoneToday = allOutletsUserOrdersDone.filter(order => {
    //     return moment().isSame(moment(order.createdAt), 'year');
    // });

    // for (var i = 0; i < 12; i++) {
    //     var record = {
    //         label: MONTH_TO_CHART_LABEL[i],
    //         value: 0,
    //     };

    //     const currMonth = moment().startOf('year').add(i, 'month');

    //     for (var j = 0; j < allOutletsUserOrdersDoneToday.length; j++) {
    //         // if (allOutletsUserOrdersDoneToday[j].outletId === allOutlets[i].uniqueId) {
    //         //     record.value += allOutletsUserOrdersDoneToday[j].finalPrice;

    //         //     // if (allOutletsUserOrdersDoneToday[j].finalPrice > yMaxValue) {
    //         //     //     yMaxValue += allOutletsUserOrdersDoneToday[j].finalPrice;
    //         //     // }
    //         // }

    //         if (moment(currMonth).isSame(moment(allOutletsUserOrdersDoneToday[j].createdAt), 'month')) {
    //             record.value += allOutletsUserOrdersDoneToday[j].finalPrice;
    //         }
    //     }

    //     // yMaxValue += record.value;
    //     if (record.value > yMaxValue) {
    //         yMaxValue = record.value;
    //     }

    //     record.value = record.value.toFixed(0);

    //     parsedData.push(record);
    // }

    for (var j = 0; j < allOutlets.length; j++) {
      var outletId = allOutlets[j].value;

      var currTotal = 0;

      for (var k = 0; k < datasetDict[outletId].data.length; k++) {
        // currTotal += datasetDict[outletId].data[k].value;

        currTotal = datasetDict[outletId].data[k].value;

        if (currTotal > yMaxValue) {
          yMaxValue = currTotal;
        }
      }

      // if (currTotal > yMaxValue) {
      //     yMaxValue = currTotal;
      // }
    }

    var chartLegend = [];

    const dataset = Object.entries(datasetDict).map(([key, value], index) => {
      const mappedIndex =
        index % CHART_COLORS[CHART_TYPE.DASHBOARD_LINE_CHART_SALES].length; // might got more than 10 preset colors, set to infinite list instead

      chartLegend.push({
        itemName: value.seriesname,
        itemSku: value.seriesname,
        chartColor:
          CHART_COLORS[CHART_TYPE.DASHBOARD_LINE_CHART_SALES][mappedIndex],
      });

      return {
        ...value,
        color: CHART_COLORS[CHART_TYPE.DASHBOARD_LINE_CHART_SALES][mappedIndex],
      };
    });

    var output = {};

    output.type = CHART_DATA[CHART_TYPE.DASHBOARD_LINE_CHART_SALES].type;
    output.dataFormat =
      CHART_DATA[CHART_TYPE.DASHBOARD_LINE_CHART_SALES].dataFormat;
    output.dataSource = {
      chart: CHART_DATA[CHART_TYPE.DASHBOARD_LINE_CHART_SALES].chart,

      categories: [
        {
          category,
        },
      ],
      dataset,

      annotations,

      // data: parsedData,
    };

    // // console.log('parsedData');
    // // console.log(parsedData);
    // console.log(yMaxValue);

    if (output.type !== 'HLinearGauge') {
      // const yAxisStep = Math.ceil((yMaxValue / 10) / 10) * 10;

      // const yMaxValueParsed = Math.ceil(yMaxValue / yAxisStep);
      // const yAxisMaxValue = yMaxValueParsed === 1 ? 2 : yMaxValueParsed;
      // const numDivLines = yMaxValueParsed === 1 ? 1 : yMaxValueParsed - 1;

      const baseNumberLength = yMaxValue.toFixed(0).length - 1;
      const baseNumber = parseInt(
        `1${Array(baseNumberLength)
          .fill()
          .map((num) => 0)
          .join('')}`,
      );
      var maxNumber = baseNumber;

      if (yMaxValue > baseNumber * 5) {
        maxNumber = baseNumber * 10;
      } else if (yMaxValue > baseNumber * 3) {
        maxNumber = baseNumber * 5;
      } else if (yMaxValue > baseNumber * 2) {
        maxNumber = baseNumber * 3;
      } else {
        maxNumber = baseNumber * 2;
      }

      maxNumber = getSuitableYAxisMaxValue(maxNumber);

      maxNumber = Math.max(maxNumber, 100);

      output.dataSource.chart = {
        ...output.dataSource.chart,
        labelDisplay: 'WRAP',
        // labelDisplay: 'STAGGER',
        // labelStep: 2,
        bgColor: '#ffffff',
        canvasBgColor: '#ffffff',
        bgAlpha: 100,

        useEllipsesWhenOverflow: 1,

        // showYAxisValues: 0,
        // yaxismaxvalue: yAxisMaxValue * yAxisStep,
        yaxismaxvalue: maxNumber,
        yaxisminvalue: yMinValue,
        adjustDiv: 0,
        // numDivLines: numDivLines,
        numDivLines: 9,

        // yaxisminvalue: 0,
        formatNumberScale: 0,
        decimals: 0,
      };

      // // console.log('/////////////////////////////////////////////////')

      // // console.log({
      //     yMaxValue: yMaxValue,
      //     yMaxValueParsed: yMaxValueParsed,
      //     yaxismaxvalue: yAxisMaxValue * yAxisStep,
      //     yaxisminvalue: yMinValue,
      //     adjustDiv: 0,
      //     numDivLines: numDivLines,
      // });

      // // console.log('/////////////////////////////////////////////////')

      return {
        chartData: output,
        chartLegend,
      };
    }

    // if (output.type === 'column2D') {
    //     if (type === CHART_TYPE.S_MILK) {
    //         // for (let i = 0; i < output.dataSource.data.length; i++) {
    //         //     if (output.dataSource.data[i].value.length > 0) {
    //         //         yMaxValue = parseInt(output.dataSource.data[i].value) > yMaxValue ? parseInt(output.dataSource.data[i].value) : yMaxValue;

    //         //     }
    //         // }

    //         // const yMaxValueParsed = Math.ceil(yMaxValue / 5);
    //         // const yAxisMaxValue = yMaxValueParsed === 1 ? 2 : yMaxValueParsed;
    //         // const numDivLines = yMaxValueParsed === 1 ? 1 : yMaxValueParsed - 1;

    //         // output.dataSource.chart = {
    //         //     ...output.dataSource.chart,
    //         //     adjustDiv: 0,
    //         //     numDivLines: numDivLines,
    //         //     yaxismaxvalue: yAxisMaxValue * 5,
    //         //     yaxisminvalue: yMinValue,
    //         //     formatNumberScale: 0,
    //         // };
    //     }
    //     else if (type === CHART_TYPE.S_SHIT_PEE) {
    //         for (let i = 0; i < output.dataSource.data.length; i++) {
    //             if (output.dataSource.data[i].value.length > 0) {
    //                 yMaxValue = parseInt(output.dataSource.data[i].value) > yMaxValue ? parseInt(output.dataSource.data[i].value) : yMaxValue;
    //             }
    //         }

    //         output.dataSource.chart = {
    //             ...output.dataSource.chart,
    //             adjustDiv: 0,
    //             numDivLines: yMaxValue,
    //             yaxismaxvalue: yMaxValue + 1,
    //             yaxisminvalue: yMinValue,
    //             formatNumberScale: 0,
    //         };
    //     }
    // }

    // console.log('sales line chart output');
    // console.log(output);

    return output;
  } catch (ex) {
    console.log('chart error');
    console.log(ex);

    return false;
  }
};

export const getDataForChartReportRevisitNum = (
  allOutletsParam,
  allOutletsUserOrdersDone,
  chartPeriod,
  yAxisKey,
  xAxisKey,
  startDate = new Date(),
  endDate = new Date(),

  reportDisplayType = REPORT_DISPLAY_TYPE.DAY,
  outletShifts = [],
) => {
  try {
    // endDate = moment(endDate).add(1, 'day');
    // endDate = moment(endDate).endOf('day');

    // console.log('data before chart');

    var parsedData = [];

    var yMinValue = 0;
    var yMaxValue = 0;

    var category = [];
    var datasetDict = {};

    var outletNameDict = {};
    // for (var i = 0; i < allOutlets.length; i++) {
    //   outletNameDict[allOutlets[i].uniqueId] = allOutlets[i].name;
    // }

    outletNameDict.newCust = 'Walk-In Customer';
    outletNameDict.repeatCust = 'Regular Customer';

    var allOutlets = [
      {
        label: 'Walk-In Customer',
        value: 'newCust',
      },
      {
        label: 'Regular Customer',
        value: 'repeatCust',
      },
    ];

    // var annotations = {
    //     "groups": [{
    //         "items": [{
    //             //Text annotation 1
    //             "type": "text",
    //             "text": 'Hello',
    //             //Define the attributes needed to create a text annotation
    //         }, {
    //             //Text annotation n
    //             "type": "text",
    //             "text": 'Hi',
    //             //Define the attributes needed to create a text annotation
    //         }]
    //     }]
    // };

    var annotations = {
      groups: [
        {
          items: [],
        },
      ],
    };
    var annotationsItems = [];

    if (chartPeriod === CHART_PERIOD.TODAY) {
      const allOutletsUserOrdersDoneToday = allOutletsUserOrdersDone.filter(
        (order) => {
          // return moment().isSame(moment(order.createdAt), 'day');
          return compareOrderDateByDisplayType(
            moment().valueOf(),
            order.createdAt,
            DATE_COMPARE_TYPE.IS_SAME,
            'day',
            reportDisplayType,
            outletShifts
          )
        },
      );

      for (var i = 0; i < allOutlets.length; i++) {
        datasetDict[allOutlets[i].value] = {
          seriesname: allOutlets[i].label,
          data: Array.from(Array(24)).map(() => {
            return {
              value: 0,
              toolText: `0`,
            };
          }),
        };
      }

      for (var i = 0; i < 24; i++) {
        const currHour = moment().startOf('day').add(i, 'hour');

        // var record = {
        //     label: i.toString().padStart(2, '0'),
        //     value: 0,
        // };

        category.push({
          label: i.toString().padStart(2, '0'),
        });

        for (var j = 0; j < allOutletsUserOrdersDoneToday.length; j++) {
          if (
            compareOrderDateByDisplayType(
              currHour,
              allOutletsUserOrdersDoneToday[j].createdAt,
              DATE_COMPARE_TYPE.IS_SAME,
              'hour',
              reportDisplayType,
              outletShifts
            )
          ) {
            if (
              (allOutletsUserOrdersDoneToday[j].settlementDetails &&
                allOutletsUserOrdersDoneToday[j].settlementDetails.repeatCust)
              ||
              allOutletsUserOrdersDoneToday[j].repeatCust
            ) {
              datasetDict.repeatCust.data[
                i
              ].value += 1;
              // record.value += allOutletsUserOrdersDoneToday[j][yAxisKey];

              datasetDict.repeatCust.data[
                i
              ].toolText = `${datasetDict.repeatCust.data[
                i
              ].value.toFixed(0)}`;
            }
            else {
              datasetDict.newCust.data[
                i
              ].value += 1;

              datasetDict.newCust.data[
                i
              ].toolText = `${datasetDict.newCust.data[
                i
              ].value.toFixed(0)}`;
            }
          }
        }

        // if (record.value > yMaxValue) {
        //     yMaxValue = record.value;
        // }

        // record.value = record.value.toFixed(0);

        // parsedData.push(record);
      }
    } else if (chartPeriod === CHART_PERIOD.THIS_WEEK) {
      // const allOutletsUserOrdersDoneToday = allOutletsUserOrdersDone.filter(order => {
      //     return moment().isSame(moment(order.createdAt), 'week');
      // });

      const startDate = moment(Date.now()).subtract(7, 'days').startOf('day');
      const endDate = moment().endOf('day');

      const allOutletsUserOrdersDoneToday = allOutletsUserOrdersDone.filter(
        (order) => {
          return (
            moment(startDate).isSameOrBefore(order.createdAt) &&
            moment(endDate).isAfter(order.createdAt)
          );
        },
      );

      // console.log('allOutletsUserOrdersDoneToday');
      // console.log(allOutletsUserOrdersDoneToday);

      for (var i = 0; i < allOutlets.length; i++) {
        datasetDict[allOutlets[i].value] = {
          seriesname: allOutlets[i].label,
          data: Array.from(Array(moment(endDate).diff(startDate, 'day'))).map(
            () => {
              return {
                value: 0,
                toolText: `0`,
              };
            },
          ),
        };
      }

      for (var i = 0; i < moment(endDate).diff(startDate, 'day'); i++) {
        // const currDay = moment().startOf('week').add(i, 'day');
        const currDay = moment(startDate).add(i, 'day');

        // var record = {
        //     label: moment().day(i).format('ddd'),
        //     value: 0,
        // };

        category.push({
          label: moment(startDate).add(i, 'day').format('ddd'),
        });

        for (var j = 0; j < allOutletsUserOrdersDoneToday.length; j++) {
          if (
            // moment(currDay).isSame(
            //   moment(allOutletsUserOrdersDoneToday[j].createdAt),
            //   'day',
            // )
            compareOrderDateByDisplayType(
              currDay,
              allOutletsUserOrdersDoneToday[j].createdAt,
              DATE_COMPARE_TYPE.IS_SAME,
              'day',
              reportDisplayType,
              outletShifts
            )
          ) {
            if (
              (allOutletsUserOrdersDoneToday[j].settlementDetails &&
                allOutletsUserOrdersDoneToday[j].settlementDetails.repeatCust)
              ||
              allOutletsUserOrdersDoneToday[j].repeatCust
            ) {
              datasetDict.repeatCust.data[
                i
              ].value += 1;
              // record.value += allOutletsUserOrdersDoneToday[j][yAxisKey];

              datasetDict.repeatCust.data[
                i
              ].toolText = `${datasetDict.repeatCust.data[
                i
              ].value.toFixed(0)}`;
            }
            else {
              datasetDict.newCust.data[
                i
              ].value += 1;

              datasetDict.newCust.data[
                i
              ].toolText = `${datasetDict.newCust.data[
                i
              ].value.toFixed(0)}`;
            }
          }
        }

        // if (record.value > yMaxValue) {
        //     yMaxValue = record.value;
        // }

        // record.value = record.value.toFixed(0);

        // parsedData.push(record);
      }
    } else if (chartPeriod === CHART_PERIOD.THIS_MONTH) {
      // const allOutletsUserOrdersDoneToday = allOutletsUserOrdersDone.filter(order => {
      //     return moment().isSame(moment(order.createdAt), 'month');
      // });

      // const startDate = moment().subtract(moment(startDate).daysInMonth(), 'day').startOf('day');
      // const endDate = moment().endOf('day');

      const allOutletsUserOrdersDoneToday = allOutletsUserOrdersDone.filter(
        (order) => {
          return (
            moment(startDate).isSameOrBefore(order.createdAt) &&
            moment(endDate).isAfter(order.createdAt)
          );
        },
      );

      if (xAxisKey === CHART_X_AXIS_TYPE.DAY) {
        for (var i = 0; i < allOutlets.length; i++) {
          datasetDict[allOutlets[i].value] = {
            seriesname: allOutlets[i].label,
            data: Array.from(Array(moment(startDate).daysInMonth())).map(() => {
              return {
                value: 0,
                toolText: `0`,
              };
            }),
          };
        }

        for (var i = 0; i < moment(startDate).daysInMonth(); i++) {
          // for (var i = 0; i < 31; i++) {
          // const currDay = moment().startOf('month').add(i, 'day');
          const currDay = moment(startDate).add(i, 'day');

          // var record = {
          //     label: i.toString().padStart(2, '0'),
          //     value: 0,
          // };

          category.push({
            label: (i + 1).toString().padStart(2, '0'),
          });

          for (var j = 0; j < allOutletsUserOrdersDoneToday.length; j++) {
            if (
              // moment(currDay).isSame(
              //   moment(allOutletsUserOrdersDoneToday[j].createdAt),
              //   'day',
              // )
              compareOrderDateByDisplayType(
                currDay,
                allOutletsUserOrdersDoneToday[j].createdAt,
                DATE_COMPARE_TYPE.IS_SAME,
                'day',
                reportDisplayType,
                outletShifts
              )
            ) {
              if (
                (allOutletsUserOrdersDoneToday[j].settlementDetails &&
                  allOutletsUserOrdersDoneToday[j].settlementDetails.repeatCust)
                ||
                allOutletsUserOrdersDoneToday[j].repeatCust
              ) {
                datasetDict.repeatCust.data[
                  i
                ].value += 1;
                // record.value += allOutletsUserOrdersDoneToday[j][yAxisKey];

                datasetDict.repeatCust.data[
                  i
                ].toolText = `${datasetDict.repeatCust.data[
                  i
                ].value.toFixed(0)}`;
              }
              else {
                datasetDict.newCust.data[
                  i
                ].value += 1;

                datasetDict.newCust.data[
                  i
                ].toolText = `${datasetDict.newCust.data[
                  i
                ].value.toFixed(0)}`;
              }
            }
          }

          // if (record.value > yMaxValue) {
          //     yMaxValue = record.value;
          // }

          // record.value = record.value.toFixed(0);

          // parsedData.push(record);
        }
      }
      else if (xAxisKey === CHART_X_AXIS_TYPE.WEEK || xAxisKey === CHART_X_AXIS_TYPE.MONTH) {
        var currWeekCount = 0;
        var prevMonth = moment(startDate);

        for (var i = 0; i < allOutlets.length; i++) {
          datasetDict[allOutlets[i].value] = {
            seriesname: allOutlets[i].label,
            data: Array.from(Array(moment(endDate).diff(startDate, 'week'))).map(
              () => {
                return {
                  value: 0,
                  toolText: `0`,
                };
              },
            ),
          };
        }

        for (var i = 0; i < moment(endDate).diff(startDate, 'week'); i++) {
          const currWeek = moment(startDate).add(i, 'week');

          if (moment(currWeek).isSame(prevMonth, 'month')) {
            currWeekCount++;
          } else {
            prevMonth = moment(currWeek);

            currWeekCount = 0;
            currWeekCount++;
          }

          // var record = {
          //     label: (currWeekCount > 1 ? '' : moment(currWeek).format('MMM')) + ' W' + currWeekCount,
          //     value: 0,
          // };

          category.push({
            label:
              `${currWeekCount > 1 ? '' : moment(currWeek).format('MMM')
              } W${currWeekCount}`,
          });

          for (var j = 0; j < allOutletsUserOrdersDoneToday.length; j++) {
            if (
              // moment(currWeek).isSame(
              //   moment(allOutletsUserOrdersDoneToday[j].createdAt),
              //   'week',
              // )
              compareOrderDateByDisplayType(
                currWeek,
                allOutletsUserOrdersDoneToday[j].createdAt,
                DATE_COMPARE_TYPE.IS_SAME,
                'week',
                reportDisplayType,
                outletShifts
              )
            ) {
              if (
                (allOutletsUserOrdersDoneToday[j].settlementDetails &&
                  allOutletsUserOrdersDoneToday[j].settlementDetails.repeatCust)
                ||
                allOutletsUserOrdersDoneToday[j].repeatCust
              ) {
                datasetDict.repeatCust.data[
                  i
                ].value += 1;
                // record.value += allOutletsUserOrdersDoneToday[j][yAxisKey];

                datasetDict.repeatCust.data[
                  i
                ].toolText = `${datasetDict.repeatCust.data[
                  i
                ].value.toFixed(0)}`;
              }
              else {
                datasetDict.newCust.data[
                  i
                ].value += 1;

                datasetDict.newCust.data[
                  i
                ].toolText = `${datasetDict.newCust.data[
                  i
                ].value.toFixed(0)}`;
              }
            }
          }

          // if (record.value > yMaxValue) {
          //     yMaxValue = record.value;
          // }

          // record.value = record.value.toFixed(0);

          // parsedData.push(record);
        }
      }
    } else if (chartPeriod === CHART_PERIOD.THREE_MONTHS) {
      const startDate = moment().subtract(2, 'month').startOf('month');
      const endDate = moment().endOf('month');

      const allOutletsUserOrdersDoneToday = allOutletsUserOrdersDone.filter(
        (order) => {
          return (
            moment(startDate).isSameOrBefore(order.createdAt) &&
            moment(endDate).isAfter(order.createdAt)
          );
        },
      );

      if (xAxisKey === CHART_X_AXIS_TYPE.DAY) {
        for (var i = 0; i < allOutlets.length; i++) {
          datasetDict[allOutlets[i].value] = {
            seriesname: allOutlets[i].label,
            data: Array.from(Array(moment(endDate).diff(startDate, 'day'))).map(() => {
              return {
                value: 0,
                toolText: `0`,
              };
            }),
          };
        }

        for (var i = 0; i < moment(endDate).diff(startDate, 'day'); i++) {
          // for (var i = 0; i < 31; i++) {
          // const currDay = moment().startOf('month').add(i, 'day');
          const currDay = moment(startDate).add(i, 'day');

          // var record = {
          //     label: i.toString().padStart(2, '0'),
          //     value: 0,
          // };

          category.push({
            label: (i + 1).toString().padStart(2, '0'),
          });

          for (var j = 0; j < allOutletsUserOrdersDoneToday.length; j++) {
            if (
              // moment(currDay).isSame(
              //   moment(allOutletsUserOrdersDoneToday[j].createdAt),
              //   'day',
              // )
              compareOrderDateByDisplayType(
                currDay,
                allOutletsUserOrdersDoneToday[j].createdAt,
                DATE_COMPARE_TYPE.IS_SAME,
                'day',
                reportDisplayType,
                outletShifts
              )
            ) {
              if (
                (allOutletsUserOrdersDoneToday[j].settlementDetails &&
                  allOutletsUserOrdersDoneToday[j].settlementDetails.repeatCust)
                ||
                allOutletsUserOrdersDoneToday[j].repeatCust
              ) {
                datasetDict.repeatCust.data[
                  i
                ].value += 1;
                // record.value += allOutletsUserOrdersDoneToday[j][yAxisKey];

                datasetDict.repeatCust.data[
                  i
                ].toolText = `${datasetDict.repeatCust.data[
                  i
                ].value.toFixed(0)}`;
              }
              else {
                datasetDict.newCust.data[
                  i
                ].value += 1;

                datasetDict.newCust.data[
                  i
                ].toolText = `${datasetDict.newCust.data[
                  i
                ].value.toFixed(0)}`;
              }
            }
          }

          // if (record.value > yMaxValue) {
          //     yMaxValue = record.value;
          // }

          // record.value = record.value.toFixed(0);

          // parsedData.push(record);
        }
      }
      else if (xAxisKey === CHART_X_AXIS_TYPE.WEEK) {
        var currWeekCount = 0;
        var prevMonth = moment(startDate);

        for (var i = 0; i < allOutlets.length; i++) {
          datasetDict[allOutlets[i].value] = {
            seriesname: allOutlets[i].label,
            data: Array.from(Array(moment(endDate).diff(startDate, 'week'))).map(
              () => {
                return {
                  value: 0,
                  toolText: `0`,
                };
              },
            ),
          };
        }

        for (var i = 0; i < moment(endDate).diff(startDate, 'week'); i++) {
          const currWeek = moment(startDate).add(i, 'week');

          if (moment(currWeek).isSame(prevMonth, 'month')) {
            currWeekCount++;
          } else {
            prevMonth = moment(currWeek);

            currWeekCount = 0;
            currWeekCount++;
          }

          // var record = {
          //     label: (currWeekCount > 1 ? '' : moment(currWeek).format('MMM')) + ' W' + currWeekCount,
          //     value: 0,
          // };

          category.push({
            label:
              `${currWeekCount > 1 ? '' : moment(currWeek).format('MMM')
              } W${currWeekCount}`,
          });

          for (var j = 0; j < allOutletsUserOrdersDoneToday.length; j++) {
            if (
              // moment(currWeek).isSame(
              //   moment(allOutletsUserOrdersDoneToday[j].createdAt),
              //   'week',
              // )
              compareOrderDateByDisplayType(
                currWeek,
                allOutletsUserOrdersDoneToday[j].createdAt,
                DATE_COMPARE_TYPE.IS_SAME,
                'week',
                reportDisplayType,
                outletShifts
              )
            ) {
              if (
                (allOutletsUserOrdersDoneToday[j].settlementDetails &&
                  allOutletsUserOrdersDoneToday[j].settlementDetails.repeatCust)
                ||
                allOutletsUserOrdersDoneToday[j].repeatCust
              ) {
                datasetDict.repeatCust.data[
                  i
                ].value += 1;
                // record.value += allOutletsUserOrdersDoneToday[j][yAxisKey];

                datasetDict.repeatCust.data[
                  i
                ].toolText = `${datasetDict.repeatCust.data[
                  i
                ].value.toFixed(0)}`;
              }
              else {
                datasetDict.newCust.data[
                  i
                ].value += 1;

                datasetDict.newCust.data[
                  i
                ].toolText = `${datasetDict.newCust.data[
                  i
                ].value.toFixed(0)}`;
              }
            }
          }

          // if (record.value > yMaxValue) {
          //     yMaxValue = record.value;
          // }

          // record.value = record.value.toFixed(0);

          // parsedData.push(record);
        }
      }
      else if (xAxisKey === CHART_X_AXIS_TYPE.MONTH) {
        for (var i = 0; i < allOutlets.length; i++) {
          datasetDict[allOutlets[i].value] = {
            seriesname: allOutlets[i].label,
            data: Array.from(Array(3)).map(() => {
              return {
                value: 0,
                toolText: `0`,
              };
            }),
          };
        }

        var currMonth = moment(startDate).startOf('month');

        for (var i = 0; i < 3; i++) {
          // const currMonth = moment().startOf('year').add(i, 'month');
          var currMonth = moment(currMonth).add(i, 'month');

          // var record = {
          //     label: moment(currMonth).format('MMM'),
          //     value: 0,
          // };

          category.push({
            label: moment(currMonth).format('MMM'),
          });

          for (var j = 0; j < allOutletsUserOrdersDoneToday.length; j++) {
            if (
              // moment(currMonth).isSame(
              //   moment(allOutletsUserOrdersDoneToday[j].createdAt),
              //   'month',
              // )
              compareOrderDateByDisplayType(
                currMonth,
                allOutletsUserOrdersDoneToday[j].createdAt,
                DATE_COMPARE_TYPE.IS_SAME,
                'month',
                reportDisplayType,
                outletShifts
              )
            ) {
              if (
                (allOutletsUserOrdersDoneToday[j].settlementDetails &&
                  allOutletsUserOrdersDoneToday[j].settlementDetails.repeatCust)
                ||
                allOutletsUserOrdersDoneToday[j].repeatCust
              ) {
                datasetDict.repeatCust.data[
                  i
                ].value += 1;
                // record.value += allOutletsUserOrdersDoneToday[j][yAxisKey];

                datasetDict.repeatCust.data[
                  i
                ].toolText = `${datasetDict.repeatCust.data[
                  i
                ].value.toFixed(0)}`;
              }
              else {
                datasetDict.newCust.data[
                  i
                ].value += 1;

                datasetDict.newCust.data[
                  i
                ].toolText = `${datasetDict.newCust.data[
                  i
                ].value.toFixed(0)}`;
              }
            }
          }

          // if (record.value > yMaxValue) {
          //     yMaxValue = record.value;
          // }

          // record.value = record.value.toFixed(0);

          // parsedData.push(record);
        }
      }
    } else if (chartPeriod === CHART_PERIOD.SIX_MONTHS) {
      const startDate = moment().subtract(5, 'month').startOf('month');
      const endDate = moment().endOf('month');

      const allOutletsUserOrdersDoneToday = allOutletsUserOrdersDone.filter(
        (order) => {
          return (
            moment(startDate).isSameOrBefore(order.createdAt) &&
            moment(endDate).isAfter(order.createdAt)
          );
        },
      );

      if (xAxisKey === CHART_X_AXIS_TYPE.DAY || xAxisKey === CHART_X_AXIS_TYPE.WEEK) {
        for (var i = 0; i < allOutlets.length; i++) {
          datasetDict[allOutlets[i].value] = {
            seriesname: allOutlets[i].label,
            data: Array.from(Array(moment(endDate).diff(startDate, 'week'))).map(
              () => {
                return {
                  value: 0,
                  toolText: `0`,
                };
              },
            ),
          };
        }

        for (var i = 0; i < moment(endDate).diff(startDate, 'week'); i++) {
          const currWeek = moment(startDate).add(i, 'week');

          if (moment(currWeek).isSame(prevMonth, 'month')) {
            currWeekCount++;
          } else {
            prevMonth = moment(currWeek);

            currWeekCount = 0;
            currWeekCount++;
          }

          // var record = {
          //     label: 'W' + currWeekCount + ' ' + (currWeekCount > 1 ? '' : moment(currWeek).format('MMM')),
          //     value: 0,
          // };

          category.push({
            label:
              `W${currWeekCount
              } ${currWeekCount > 1 ? '' : moment(currWeek).format('MMM')}`,
          });

          for (var j = 0; j < allOutletsUserOrdersDoneToday.length; j++) {
            if (
              // moment(currWeek).isSame(
              //   moment(allOutletsUserOrdersDoneToday[j].createdAt),
              //   'week',
              // )
              compareOrderDateByDisplayType(
                currWeek,
                allOutletsUserOrdersDoneToday[j].createdAt,
                DATE_COMPARE_TYPE.IS_SAME,
                'week',
                reportDisplayType,
                outletShifts
              )
            ) {
              if (
                (allOutletsUserOrdersDoneToday[j].settlementDetails &&
                  allOutletsUserOrdersDoneToday[j].settlementDetails.repeatCust)
                ||
                allOutletsUserOrdersDoneToday[j].repeatCust
              ) {
                datasetDict.repeatCust.data[
                  i
                ].value += 1;
                // record.value += allOutletsUserOrdersDoneToday[j][yAxisKey];

                datasetDict.repeatCust.data[
                  i
                ].toolText = `${datasetDict.repeatCust.data[
                  i
                ].value.toFixed(0)}`;
              }
              else {
                datasetDict.newCust.data[
                  i
                ].value += 1;

                datasetDict.newCust.data[
                  i
                ].toolText = `${datasetDict.newCust.data[
                  i
                ].value.toFixed(0)}`;
              }
            }
          }

          // if (record.value > yMaxValue) {
          //     yMaxValue = record.value;
          // }

          // record.value = record.value.toFixed(0);

          // parsedData.push(record);
        }
      }
      else if (xAxisKey === CHART_X_AXIS_TYPE.MONTH) {
        for (var i = 0; i < allOutlets.length; i++) {
          datasetDict[allOutlets[i].value] = {
            seriesname: allOutlets[i].label,
            data: Array.from(Array(6)).map(() => {
              return {
                value: 0,
                toolText: `0`,
              };
            }),
          };
        }

        var currMonth = moment(startDate).startOf('month');

        for (var i = 0; i < 6; i++) {
          // const currMonth = moment().startOf('year').add(i, 'month');
          var currMonth = moment(currMonth).add(i, 'month');

          // var record = {
          //     label: moment(currMonth).format('MMM'),
          //     value: 0,
          // };

          category.push({
            label: moment(currMonth).format('MMM'),
          });

          for (var j = 0; j < allOutletsUserOrdersDoneToday.length; j++) {
            if (
              // moment(currMonth).isSame(
              //   moment(allOutletsUserOrdersDoneToday[j].createdAt),
              //   'month',
              // )
              compareOrderDateByDisplayType(
                currMonth,
                allOutletsUserOrdersDoneToday[j].createdAt,
                DATE_COMPARE_TYPE.IS_SAME,
                'month',
                reportDisplayType,
                outletShifts
              )
            ) {
              if (
                (allOutletsUserOrdersDoneToday[j].settlementDetails &&
                  allOutletsUserOrdersDoneToday[j].settlementDetails.repeatCust)
                ||
                allOutletsUserOrdersDoneToday[j].repeatCust
              ) {
                datasetDict.repeatCust.data[
                  i
                ].value += 1;
                // record.value += allOutletsUserOrdersDoneToday[j][yAxisKey];

                datasetDict.repeatCust.data[
                  i
                ].toolText = `${datasetDict.repeatCust.data[
                  i
                ].value.toFixed(0)}`;
              }
              else {
                datasetDict.newCust.data[
                  i
                ].value += 1;

                datasetDict.newCust.data[
                  i
                ].toolText = `${datasetDict.newCust.data[
                  i
                ].value.toFixed(0)}`;
              }
            }
          }

          // if (record.value > yMaxValue) {
          //     yMaxValue = record.value;
          // }

          // record.value = record.value.toFixed(0);

          // parsedData.push(record);
        }
      }
    } else if (chartPeriod === CHART_PERIOD.THIS_YEAR) {
      const allOutletsUserOrdersDoneToday = allOutletsUserOrdersDone.filter(
        (order) => {
          return moment().isSame(moment(order.createdAt), 'year');
        },
      );

      if (xAxisKey === CHART_X_AXIS_TYPE.DAY || xAxisKey === CHART_X_AXIS_TYPE.WEEK) {
        for (var i = 0; i < allOutlets.length; i++) {
          datasetDict[allOutlets[i].value] = {
            seriesname: allOutlets[i].label,
            data: Array.from(Array(moment(endDate).diff(startDate, 'week'))).map(
              () => {
                return {
                  value: 0,
                  toolText: `0`,
                };
              },
            ),
          };
        }

        for (var i = 0; i < moment(endDate).diff(startDate, 'week'); i++) {
          const currWeek = moment(startDate).add(i, 'week');

          if (moment(currWeek).isSame(prevMonth, 'month')) {
            currWeekCount++;
          } else {
            prevMonth = moment(currWeek);

            currWeekCount = 0;
            currWeekCount++;
          }

          // var record = {
          //     label: 'W' + currWeekCount + ' ' + (currWeekCount > 1 ? '' : moment(currWeek).format('MMM')),
          //     value: 0,
          // };

          category.push({
            label:
              `W${currWeekCount
              } ${currWeekCount > 2 ? '' : moment(currWeek).format('MMM')}`,
          });

          for (var j = 0; j < allOutletsUserOrdersDoneToday.length; j++) {
            if (
              // moment(currWeek).isSame(
              //   moment(allOutletsUserOrdersDoneToday[j].createdAt),
              //   'week',
              // )
              compareOrderDateByDisplayType(
                currWeek,
                allOutletsUserOrdersDoneToday[j].createdAt,
                DATE_COMPARE_TYPE.IS_SAME,
                'week',
                reportDisplayType,
                outletShifts
              )
            ) {
              if (
                (allOutletsUserOrdersDoneToday[j].settlementDetails &&
                  allOutletsUserOrdersDoneToday[j].settlementDetails.repeatCust)
                ||
                allOutletsUserOrdersDoneToday[j].repeatCust
              ) {
                datasetDict.repeatCust.data[
                  i
                ].value += 1;
                // record.value += allOutletsUserOrdersDoneToday[j][yAxisKey];

                datasetDict.repeatCust.data[
                  i
                ].toolText = `${datasetDict.repeatCust.data[
                  i
                ].value.toFixed(0)}`;
              }
              else {
                datasetDict.newCust.data[
                  i
                ].value += 1;

                datasetDict.newCust.data[
                  i
                ].toolText = `${datasetDict.newCust.data[
                  i
                ].value.toFixed(0)}`;
              }
            }
          }

          // if (record.value > yMaxValue) {
          //     yMaxValue = record.value;
          // }

          // record.value = record.value.toFixed(0);

          // parsedData.push(record);
        }
      }
      else if (xAxisKey === CHART_X_AXIS_TYPE.MONTH) {
        for (var i = 0; i < allOutlets.length; i++) {
          datasetDict[allOutlets[i].value] = {
            seriesname: allOutlets[i].label,
            data: Array.from(Array(12)).map(() => {
              return {
                value: 0,
                toolText: `0`,
              };
            }),
          };
        }

        for (var i = 0; i < 12; i++) {
          const currMonth = moment().startOf('year').add(i, 'month');

          // var record = {
          //     label: moment(currMonth).format('MMM'),
          //     value: 0,
          // };

          category.push({
            label: moment(currMonth).format('MMM'),
          });

          for (var j = 0; j < allOutletsUserOrdersDoneToday.length; j++) {
            if (
              // moment(currMonth).isSame(
              //   moment(allOutletsUserOrdersDoneToday[j].createdAt),
              //   'month',
              // )
              compareOrderDateByDisplayType(
                currMonth,
                allOutletsUserOrdersDoneToday[j].createdAt,
                DATE_COMPARE_TYPE.IS_SAME,
                'month',
                reportDisplayType,
                outletShifts
              )
            ) {
              if (
                (allOutletsUserOrdersDoneToday[j].settlementDetails &&
                  allOutletsUserOrdersDoneToday[j].settlementDetails.repeatCust)
                ||
                allOutletsUserOrdersDoneToday[j].repeatCust
              ) {
                datasetDict.repeatCust.data[
                  i
                ].value += 1;
                // record.value += allOutletsUserOrdersDoneToday[j][yAxisKey];

                datasetDict.repeatCust.data[
                  i
                ].toolText = `${datasetDict.repeatCust.data[
                  i
                ].value.toFixed(0)}`;
              }
              else {
                datasetDict.newCust.data[
                  i
                ].value += 1;

                datasetDict.newCust.data[
                  i
                ].toolText = `${datasetDict.newCust.data[
                  i
                ].value.toFixed(0)}`;
              }
            }
          }

          // if (record.value > yMaxValue) {
          //     yMaxValue = record.value;
          // }

          // record.value = record.value.toFixed(0);

          // parsedData.push(record);
        }
      }
    } else if (chartPeriod === CHART_PERIOD.YTD) {
      const startDate = moment().subtract(11, 'month').startOf('month');
      const endDate = moment().endOf('month');

      const allOutletsUserOrdersDoneToday = allOutletsUserOrdersDone.filter(
        (order) => {
          return (
            moment(startDate).isSameOrBefore(order.createdAt) &&
            moment(endDate).isAfter(order.createdAt)
          );
        },
      );

      if (xAxisKey === CHART_X_AXIS_TYPE.DAY || xAxisKey === CHART_X_AXIS_TYPE.WEEK) {
        for (var i = 0; i < allOutlets.length; i++) {
          datasetDict[allOutlets[i].value] = {
            seriesname: allOutlets[i].label,
            data: Array.from(Array(moment(endDate).diff(startDate, 'week'))).map(
              () => {
                return {
                  value: 0,
                  toolText: `0`,
                };
              },
            ),
          };
        }

        for (var i = 0; i < moment(endDate).diff(startDate, 'week'); i++) {
          const currWeek = moment(startDate).add(i, 'week');

          if (moment(currWeek).isSame(prevMonth, 'month')) {
            currWeekCount++;
          } else {
            prevMonth = moment(currWeek);

            currWeekCount = 0;
            currWeekCount++;
          }

          // var record = {
          //     label: 'W' + currWeekCount + ' ' + (currWeekCount > 1 ? '' : moment(currWeek).format('MMM')),
          //     value: 0,
          // };

          category.push({
            label:
              `W${currWeekCount
              } ${currWeekCount > 2 ? '' : moment(currWeek).format('MMM')}`,
          });

          for (var j = 0; j < allOutletsUserOrdersDoneToday.length; j++) {
            if (
              // moment(currWeek).isSame(
              //   moment(allOutletsUserOrdersDoneToday[j].createdAt),
              //   'week',
              // )
              compareOrderDateByDisplayType(
                currWeek,
                allOutletsUserOrdersDoneToday[j].createdAt,
                DATE_COMPARE_TYPE.IS_SAME,
                'week',
                reportDisplayType,
                outletShifts
              )
            ) {
              if (
                (allOutletsUserOrdersDoneToday[j].settlementDetails &&
                  allOutletsUserOrdersDoneToday[j].settlementDetails.repeatCust)
                ||
                allOutletsUserOrdersDoneToday[j].repeatCust
              ) {
                datasetDict.repeatCust.data[
                  i
                ].value += 1;
                // record.value += allOutletsUserOrdersDoneToday[j][yAxisKey];

                datasetDict.repeatCust.data[
                  i
                ].toolText = `${datasetDict.repeatCust.data[
                  i
                ].value.toFixed(0)}`;
              }
              else {
                datasetDict.newCust.data[
                  i
                ].value += 1;

                datasetDict.newCust.data[
                  i
                ].toolText = `${datasetDict.newCust.data[
                  i
                ].value.toFixed(0)}`;
              }
            }
          }

          // if (record.value > yMaxValue) {
          //     yMaxValue = record.value;
          // }

          // record.value = record.value.toFixed(0);

          // parsedData.push(record);
        }
      }
      else if (xAxisKey === CHART_X_AXIS_TYPE.MONTH) {
        for (var i = 0; i < allOutlets.length; i++) {
          datasetDict[allOutlets[i].value] = {
            seriesname: allOutlets[i].label,
            data: Array.from(Array(12)).map(() => {
              return {
                value: 0,
                toolText: `0`,
              };
            }),
          };
        }

        for (var i = 0; i < 12; i++) {
          const currMonth = moment(startDate).add(i, 'month');

          // var record = {
          //     label: moment(currMonth).format('YY\' MMM'),
          //     value: 0,
          // };

          category.push({
            label: moment(currMonth).format("YY' MMM"),
          });

          for (var j = 0; j < allOutletsUserOrdersDoneToday.length; j++) {
            if (
              // moment(currMonth).isSame(
              //   moment(allOutletsUserOrdersDoneToday[j].createdAt),
              //   'month',
              // )
              compareOrderDateByDisplayType(
                currMonth,
                allOutletsUserOrdersDoneToday[j].createdAt,
                DATE_COMPARE_TYPE.IS_SAME,
                'month',
                reportDisplayType,
                outletShifts
              )
            ) {
              if (
                (allOutletsUserOrdersDoneToday[j].settlementDetails &&
                  allOutletsUserOrdersDoneToday[j].settlementDetails.repeatCust)
                ||
                allOutletsUserOrdersDoneToday[j].repeatCust
              ) {
                datasetDict.repeatCust.data[
                  i
                ].value += 1;
                // record.value += allOutletsUserOrdersDoneToday[j][yAxisKey];

                datasetDict.repeatCust.data[
                  i
                ].toolText = `${datasetDict.repeatCust.data[
                  i
                ].value.toFixed(0)}`;
              }
              else {
                datasetDict.newCust.data[
                  i
                ].value += 1;

                datasetDict.newCust.data[
                  i
                ].toolText = `${datasetDict.newCust.data[
                  i
                ].value.toFixed(0)}`;
              }
            }
          }

          // if (record.value > yMaxValue) {
          //     yMaxValue = record.value;
          // }

          // record.value = record.value.toFixed(0);

          // parsedData.push(record);
        }
      }
    } else if (chartPeriod === CHART_PERIOD.NONE) {
      const allOutletsUserOrdersDoneTodayRange =
        allOutletsUserOrdersDone.filter((order) => {
          return (
            moment(startDate).isSameOrBefore(order.createdAt) &&
            moment(endDate).isAfter(order.createdAt)
          );
        });

      if (
        moment(endDate).diff(startDate, 'day') <=
        moment(startDate).daysInMonth()
      ) {
        endDate = moment(endDate).add(1, 'day');

        const diffDays = moment(endDate).diff(startDate, 'day');

        var prevDate = moment(startDate).add(-1, 'month');

        for (var i = 0; i < allOutlets.length; i++) {
          datasetDict[allOutlets[i].value] = {
            seriesname: allOutlets[i].label,
            data: Array.from(Array(diffDays)).map(() => {
              return {
                value: 0,
                toolText: `0`,
              };
            }),
          };
        }

        for (var i = 0; i < diffDays; i++) {
          const currDate = moment(startDate).add(i, 'day');

          category.push({
            //'label': moment(currDate).format('DD MMM'),
            label: !moment(currDate).isSame(prevDate, 'month')
              ? moment(currDate).format('DD MMM')
              : moment(currDate).format('DD'),
            //'label': (i > 1 ? moment(currDate).format('DD') : moment(currDate).format('DD MMM'))
          });

          prevDate = currDate;

          for (var j = 0; j < allOutletsUserOrdersDoneTodayRange.length; j++) {
            if (
              // allOutletsUserOrdersDoneTodayRange[j].outletId === allOutlets[i].uniqueId &&
              // moment(currDate).isSame(
              //   moment(allOutletsUserOrdersDoneTodayRange[j].createdAt),
              //   'day',
              // )
              compareOrderDateByDisplayType(
                currDate,
                allOutletsUserOrdersDoneTodayRange[j].createdAt,
                DATE_COMPARE_TYPE.IS_SAME,
                'day',
                reportDisplayType,
                outletShifts
              )
            ) {
              if (
                (allOutletsUserOrdersDoneTodayRange[j].settlementDetails &&
                  allOutletsUserOrdersDoneTodayRange[j].settlementDetails.repeatCust)
                ||
                allOutletsUserOrdersDoneTodayRange[j].repeatCust
              ) {
                datasetDict.repeatCust.data[
                  i
                ].value += 1;
                // record.value += allOutletsUserOrdersDoneToday[j][yAxisKey];

                datasetDict.repeatCust.data[
                  i
                ].toolText = `${datasetDict.repeatCust.data[
                  i
                ].value.toFixed(0)}`;
              }
              else {
                datasetDict.newCust.data[
                  i
                ].value += 1;

                datasetDict.newCust.data[
                  i
                ].toolText = `${datasetDict.newCust.data[
                  i
                ].value.toFixed(0)}`;
              }
            }
          }

          // if (record.value > yMaxValue) {
          //     yMaxValue = record.value;
          // }

          // record.value = record.value.toFixed(0);

          // parsedData.push(record);
        }
      } else if (moment(endDate).diff(startDate, 'month') <= 6) {
        var prevMonth = moment(startDate);

        const diffWeeks = moment(endDate).diff(startDate, 'week');

        for (var i = 0; i < allOutlets.length; i++) {
          datasetDict[allOutlets[i].value] = {
            seriesname: allOutlets[i].label,
            data: Array.from(Array(diffWeeks)).map(() => {
              return {
                value: 0,
                toolText: `0`,
              };
            }),
          };
        }

        for (var i = 0; i < diffWeeks; i++) {
          const currDate = moment(startDate).add(i, 'week');

          if (moment(currDate).isSame(prevMonth, 'month')) {
            currWeekCount++;
          } else {
            prevMonth = moment(currDate);

            currWeekCount = 0;
            currWeekCount++;
          }

          category.push({
            // label: moment(currDate).format('MMM') + ' W' + currWeekCount,
            label:
              `${moment(currDate).format('MMM')
              } W${countWeekdayOccurrencesInMonth(currDate)}`,
          });

          for (var j = 0; j < allOutletsUserOrdersDoneTodayRange.length; j++) {
            if (
              // allOutletsUserOrdersDoneTodayRange[j].outletId === allOutlets[i].uniqueId &&
              // moment(currDate).isSame(
              //   moment(allOutletsUserOrdersDoneTodayRange[j].createdAt),
              //   'week',
              // )
              compareOrderDateByDisplayType(
                currDate,
                allOutletsUserOrdersDoneTodayRange[j].createdAt,
                DATE_COMPARE_TYPE.IS_SAME,
                'week',
                reportDisplayType,
                outletShifts
              )
            ) {
              if (
                (allOutletsUserOrdersDoneTodayRange[j].settlementDetails &&
                  allOutletsUserOrdersDoneTodayRange[j].settlementDetails.repeatCust)
                ||
                allOutletsUserOrdersDoneTodayRange[j].repeatCust
              ) {
                datasetDict.repeatCust.data[
                  i
                ].value += 1;
                // record.value += allOutletsUserOrdersDoneToday[j][yAxisKey];

                datasetDict.repeatCust.data[
                  i
                ].toolText = `${datasetDict.repeatCust.data[
                  i
                ].value.toFixed(0)}`;
              }
              else {
                datasetDict.newCust.data[
                  i
                ].value += 1;

                datasetDict.newCust.data[
                  i
                ].toolText = `${datasetDict.newCust.data[
                  i
                ].value.toFixed(0)}`;
              }
            }
          }

          // if (record.value > yMaxValue) {
          //     yMaxValue = record.value;
          // }

          // record.value = record.value.toFixed(0);

          // parsedData.push(record);
        }
      }
      // if (moment(endDate).diff(startDate, 'month') <= 6) // for now no need conditions first, for more than 6 months interval
      else {
        const diffYears = moment(endDate).diff(startDate, 'month');

        for (var i = 0; i < allOutlets.length; i++) {
          datasetDict[allOutlets[i].value] = {
            seriesname: allOutlets[i].label,
            data: Array.from(Array(diffYears)).map(() => {
              return {
                value: 0,
                toolText: `0`,
              };
            }),
          };
        }

        for (var i = 0; i < diffYears; i++) {
          const currDate = moment(startDate).add(i, 'month');

          category.push({
            label: moment(currDate).format("YY' MMM"),
          });

          for (var j = 0; j < allOutletsUserOrdersDoneTodayRange.length; j++) {
            if (
              // allOutletsUserOrdersDoneTodayRange[j].outletId === allOutlets[i].uniqueId &&
              // moment(currDate).isSame(
              //   moment(allOutletsUserOrdersDoneTodayRange[j].createdAt),
              //   'month',
              // )
              compareOrderDateByDisplayType(
                currDate,
                allOutletsUserOrdersDoneTodayRange[j].createdAt,
                DATE_COMPARE_TYPE.IS_SAME,
                'month',
                reportDisplayType,
                outletShifts
              )
            ) {
              if (
                (allOutletsUserOrdersDoneTodayRange[j].settlementDetails &&
                  allOutletsUserOrdersDoneTodayRange[j].settlementDetails.repeatCust)
                ||
                allOutletsUserOrdersDoneTodayRange[j].repeatCust
              ) {
                datasetDict.repeatCust.data[
                  i
                ].value += 1;
                // record.value += allOutletsUserOrdersDoneToday[j][yAxisKey];

                datasetDict.repeatCust.data[
                  i
                ].toolText = `${datasetDict.repeatCust.data[
                  i
                ].value.toFixed(0)}`;
              }
              else {
                datasetDict.newCust.data[
                  i
                ].value += 1;

                datasetDict.newCust.data[
                  i
                ].toolText = `${datasetDict.newCust.data[
                  i
                ].value.toFixed(0)}`;
              }
            }
          }

          // if (record.value > yMaxValue) {
          //     yMaxValue = record.value;
          // }

          // record.value = record.value.toFixed(0);

          // parsedData.push(record);
        }
      }
    }

    // const allOutletsUserOrdersDoneToday = allOutletsUserOrdersDone.filter(order => {
    //     return moment().isSame(moment(order.createdAt), 'year');
    // });

    // for (var i = 0; i < 12; i++) {
    //     var record = {
    //         label: MONTH_TO_CHART_LABEL[i],
    //         value: 0,
    //     };

    //     const currMonth = moment().startOf('year').add(i, 'month');

    //     for (var j = 0; j < allOutletsUserOrdersDoneToday.length; j++) {
    //         // if (allOutletsUserOrdersDoneToday[j].outletId === allOutlets[i].uniqueId) {
    //         //     record.value += allOutletsUserOrdersDoneToday[j].finalPrice;

    //         //     // if (allOutletsUserOrdersDoneToday[j].finalPrice > yMaxValue) {
    //         //     //     yMaxValue += allOutletsUserOrdersDoneToday[j].finalPrice;
    //         //     // }
    //         // }

    //         if (moment(currMonth).isSame(moment(allOutletsUserOrdersDoneToday[j].createdAt), 'month')) {
    //             record.value += allOutletsUserOrdersDoneToday[j].finalPrice;
    //         }
    //     }

    //     // yMaxValue += record.value;
    //     if (record.value > yMaxValue) {
    //         yMaxValue = record.value;
    //     }

    //     record.value = record.value.toFixed(0);

    //     parsedData.push(record);
    // }

    for (var j = 0; j < allOutlets.length; j++) {
      var outletId = allOutlets[j].value;

      var currTotal = 0;

      for (var k = 0; k < datasetDict[outletId].data.length; k++) {
        // currTotal += datasetDict[outletId].data[k].value;

        currTotal = datasetDict[outletId].data[k].value;

        if (currTotal > yMaxValue) {
          yMaxValue = currTotal;
        }
      }

      // if (currTotal > yMaxValue) {
      //     yMaxValue = currTotal;
      // }
    }

    var chartLegend = [];

    const dataset = Object.entries(datasetDict).map(([key, value], index) => {
      const mappedIndex =
        index % CHART_COLORS[CHART_TYPE.DASHBOARD_LINE_CHART_SALES].length; // might got more than 10 preset colors, set to infinite list instead

      chartLegend.push({
        itemName: value.seriesname,
        itemSku: value.seriesname,
        chartColor:
          CHART_COLORS[CHART_TYPE.DASHBOARD_LINE_CHART_SALES][mappedIndex],
      });

      return {
        ...value,
        color: CHART_COLORS[CHART_TYPE.DASHBOARD_LINE_CHART_SALES][mappedIndex],
      };
    });

    var output = {};

    output.type = CHART_DATA[CHART_TYPE.DASHBOARD_LINE_CHART_SALES].type;
    output.dataFormat =
      CHART_DATA[CHART_TYPE.DASHBOARD_LINE_CHART_SALES].dataFormat;
    output.dataSource = {
      chart: CHART_DATA[CHART_TYPE.DASHBOARD_LINE_CHART_SALES].chart,

      categories: [
        {
          category,
        },
      ],
      dataset,

      annotations,

      // data: parsedData,
    };

    // // console.log('parsedData');
    // // console.log(parsedData);
    // console.log(yMaxValue);

    if (output.type !== 'HLinearGauge') {
      // const yAxisStep = Math.ceil((yMaxValue / 10) / 10) * 10;

      // const yMaxValueParsed = Math.ceil(yMaxValue / yAxisStep);
      // const yAxisMaxValue = yMaxValueParsed === 1 ? 2 : yMaxValueParsed;
      // const numDivLines = yMaxValueParsed === 1 ? 1 : yMaxValueParsed - 1;

      const baseNumberLength = yMaxValue.toFixed(0).length - 1;
      const baseNumber = parseInt(
        `1${Array(baseNumberLength)
          .fill()
          .map((num) => 0)
          .join('')}`,
      );
      var maxNumber = baseNumber;

      if (yMaxValue > baseNumber * 5) {
        maxNumber = baseNumber * 10;
      } else if (yMaxValue > baseNumber * 3) {
        maxNumber = baseNumber * 5;
      } else if (yMaxValue > baseNumber * 2) {
        maxNumber = baseNumber * 3;
      } else {
        maxNumber = baseNumber * 2;
      }

      maxNumber = getSuitableYAxisMaxValue(maxNumber);

      maxNumber = Math.max(maxNumber, 10);

      output.dataSource.chart = {
        ...output.dataSource.chart,
        labelDisplay: 'WRAP',
        // labelDisplay: 'STAGGER',
        // labelStep: 2,
        bgColor: '#ffffff',
        canvasBgColor: '#ffffff',
        bgAlpha: 100,

        useEllipsesWhenOverflow: 1,

        // showYAxisValues: 0,
        // yaxismaxvalue: yAxisMaxValue * yAxisStep,
        yaxismaxvalue: maxNumber,
        yaxisminvalue: yMinValue,
        adjustDiv: 0,
        // numDivLines: numDivLines,
        numDivLines: 9,

        // yaxisminvalue: 0,
        formatNumberScale: 0,
        decimals: 0,
      };

      // // console.log('/////////////////////////////////////////////////')

      // // console.log({
      //     yMaxValue: yMaxValue,
      //     yMaxValueParsed: yMaxValueParsed,
      //     yaxismaxvalue: yAxisMaxValue * yAxisStep,
      //     yaxisminvalue: yMinValue,
      //     adjustDiv: 0,
      //     numDivLines: numDivLines,
      // });

      // // console.log('/////////////////////////////////////////////////')

      return {
        chartData: output,
        chartLegend,
      };
    }

    // if (output.type === 'column2D') {
    //     if (type === CHART_TYPE.S_MILK) {
    //         // for (let i = 0; i < output.dataSource.data.length; i++) {
    //         //     if (output.dataSource.data[i].value.length > 0) {
    //         //         yMaxValue = parseInt(output.dataSource.data[i].value) > yMaxValue ? parseInt(output.dataSource.data[i].value) : yMaxValue;

    //         //     }
    //         // }

    //         // const yMaxValueParsed = Math.ceil(yMaxValue / 5);
    //         // const yAxisMaxValue = yMaxValueParsed === 1 ? 2 : yMaxValueParsed;
    //         // const numDivLines = yMaxValueParsed === 1 ? 1 : yMaxValueParsed - 1;

    //         // output.dataSource.chart = {
    //         //     ...output.dataSource.chart,
    //         //     adjustDiv: 0,
    //         //     numDivLines: numDivLines,
    //         //     yaxismaxvalue: yAxisMaxValue * 5,
    //         //     yaxisminvalue: yMinValue,
    //         //     formatNumberScale: 0,
    //         // };
    //     }
    //     else if (type === CHART_TYPE.S_SHIT_PEE) {
    //         for (let i = 0; i < output.dataSource.data.length; i++) {
    //             if (output.dataSource.data[i].value.length > 0) {
    //                 yMaxValue = parseInt(output.dataSource.data[i].value) > yMaxValue ? parseInt(output.dataSource.data[i].value) : yMaxValue;
    //             }
    //         }

    //         output.dataSource.chart = {
    //             ...output.dataSource.chart,
    //             adjustDiv: 0,
    //             numDivLines: yMaxValue,
    //             yaxismaxvalue: yMaxValue + 1,
    //             yaxisminvalue: yMinValue,
    //             formatNumberScale: 0,
    //         };
    //     }
    // }

    // console.log('sales line chart output');
    // console.log(output);

    return output;
  } catch (ex) {
    console.log('chart error');
    console.log(ex);

    return false;
  }
};

export const getDataForChartReportAovSales = (
  allOutletsParam,
  allOutletsUserOrdersDone,
  chartPeriod,
  yAxisKey,
  xAxisKey,
  startDate = new Date(),
  endDate = new Date(),

  reportDisplayType = REPORT_DISPLAY_TYPE.DAY,
  outletShifts = [],
) => {
  try {
    // endDate = moment(endDate).add(1, 'day');
    // endDate = moment(endDate).endOf('day');

    // console.log('data before chart');

    var parsedData = [];

    var yMinValue = 0;
    var yMaxValue = 0;

    var category = [];
    var datasetDict = {};

    var outletNameDict = {};
    // for (var i = 0; i < allOutlets.length; i++) {
    //   outletNameDict[allOutlets[i].uniqueId] = allOutlets[i].name;
    // }

    outletNameDict.avgPos = 'AVG POS';
    outletNameDict.avgQr = 'AVG QR';
    // outletNameDict['pastOrderTrend'] = 'Comparison';

    var allOutlets = [
      {
        label: 'AVG POS',
        value: 'avgPos',
        types: [APP_TYPE.MERCHANT, APP_TYPE.WAITER],
      },
      {
        label: 'AVG QR',
        value: 'avgQr',
        types: [APP_TYPE.WEB_ORDER],
      },
      // {
      //   label: 'Comparison',
      //   value: 'pastOrderTrend',
      // },
    ];

    var typeToValueDict = {
      [APP_TYPE.MERCHANT]: 'avgPos',
      [APP_TYPE.WAITER]: 'avgPos',

      [APP_TYPE.WEB_ORDER]: 'avgQr',
      [APP_TYPE.USER]: 'avgQr',

      [undefined]: 'avgPos',
    };

    // var annotations = {
    //     "groups": [{
    //         "items": [{
    //             //Text annotation 1
    //             "type": "text",
    //             "text": 'Hello',
    //             //Define the attributes needed to create a text annotation
    //         }, {
    //             //Text annotation n
    //             "type": "text",
    //             "text": 'Hi',
    //             //Define the attributes needed to create a text annotation
    //         }]
    //     }]
    // };

    var annotations = {
      groups: [
        {
          items: [],
        },
      ],
    };
    var annotationsItems = [];

    if (chartPeriod === CHART_PERIOD.TODAY) {
      const allOutletsUserOrdersDoneToday = allOutletsUserOrdersDone.filter(
        (order) => {
          // return moment().isSame(moment(order.createdAt), 'day');
          return compareOrderDateByDisplayType(
            currDate,
            order.createdAt,
            DATE_COMPARE_TYPE.IS_SAME,
            'day',
            reportDisplayType,
            outletShifts
          )
        },
      );

      for (var i = 0; i < allOutlets.length; i++) {
        datasetDict[allOutlets[i].value] = {
          seriesname: allOutlets[i].label,
          data: Array.from(Array(24)).map(() => {
            return {
              value: 0,
              toolText: `RM 0.00`,
              totalOrders: 0,
              totalSales: 0,
            };
          }),
        };
      }

      for (var i = 0; i < 24; i++) {
        const currHour = moment().startOf('day').add(i, 'hour');

        // var record = {
        //     label: i.toString().padStart(2, '0'),
        //     value: 0,
        // };

        category.push({
          label: i.toString().padStart(2, '0'),
        });

        var orderCount = 0;

        for (var j = 0; j < allOutletsUserOrdersDoneToday.length; j++) {
          if (
            // moment(currHour).isSame(
            //   moment(allOutletsUserOrdersDoneToday[j].createdAt),
            //   'hour',
            // )
            compareOrderDateByDisplayType(
              currHour,
              allOutletsUserOrdersDoneToday[j].createdAt,
              DATE_COMPARE_TYPE.IS_SAME,
              'hour',
              reportDisplayType,
              outletShifts
            )
          ) {
            var typeParsed = typeToValueDict[allOutletsUserOrdersDoneToday[j].appType ? allOutletsUserOrdersDoneToday[j].appType : APP_TYPE.MERCHANT];

            datasetDict[typeParsed].data[
              i
            ].value += allOutletsUserOrdersDoneToday[j][yAxisKey];

            datasetDict[typeParsed].data[
              i
            ].toolText = `RM ${datasetDict[typeParsed].data[
              i
            ].value.toFixed(2)}`;

            orderCount += 1;

            datasetDict[typeParsed].data[
              i
            ].totalOrders += 1;

            datasetDict[typeParsed].data[
              i
            ].totalSales += allOutletsUserOrdersDoneToday[j][yAxisKey];
          }
        }

        if (orderCount > 0) {
          for (var countIndex = 0; countIndex < allOutlets.length; countIndex++) {
            var type = allOutlets[countIndex].value;

            if (datasetDict[type].data[
              i
            ].totalOrders > 0) {
              datasetDict[type].data[
                i
              ].value = datasetDict[type].data[
                i
              ].value / datasetDict[type].data[i].totalOrders;

              datasetDict[type].data[
                i
              ].toolText = `RM ${datasetDict[type].data[
                i
              ].value.toFixed(2)}`;
            }
          }
        }

        // if (record.value > yMaxValue) {
        //     yMaxValue = record.value;
        // }

        // record.value = record.value.toFixed(0);

        // parsedData.push(record);
      }
    } else if (chartPeriod === CHART_PERIOD.THIS_WEEK) {
      // const allOutletsUserOrdersDoneToday = allOutletsUserOrdersDone.filter(order => {
      //     return moment().isSame(moment(order.createdAt), 'week');
      // });

      const startDate = moment(Date.now()).subtract(7, 'days').startOf('day');
      const endDate = moment().endOf('day');

      const allOutletsUserOrdersDoneToday = allOutletsUserOrdersDone.filter(
        (order) => {
          return (
            moment(startDate).isSameOrBefore(order.createdAt) &&
            moment(endDate).isAfter(order.createdAt)
          );
        },
      );

      // console.log('allOutletsUserOrdersDoneToday');
      // console.log(allOutletsUserOrdersDoneToday);

      for (var i = 0; i < allOutlets.length; i++) {
        datasetDict[allOutlets[i].value] = {
          seriesname: allOutlets[i].label,
          data: Array.from(Array(moment(endDate).diff(startDate, 'day'))).map(
            () => {
              return {
                value: 0,
                toolText: `RM 0.00`,
                totalOrders: 0,
                totalSales: 0,
              };
            },
          ),
        };
      }

      for (var i = 0; i < moment(endDate).diff(startDate, 'day'); i++) {
        // const currDay = moment().startOf('week').add(i, 'day');
        const currDay = moment(startDate).add(i, 'day');

        // var record = {
        //     label: moment().day(i).format('ddd'),
        //     value: 0,
        // };

        category.push({
          label: moment(startDate).add(i, 'day').format('ddd'),
        });

        var orderCount = 0;

        for (var j = 0; j < allOutletsUserOrdersDoneToday.length; j++) {
          if (
            // moment(currDay).isSame(
            //   moment(allOutletsUserOrdersDoneToday[j].createdAt),
            //   'day',
            // )
            compareOrderDateByDisplayType(
              currDay,
              allOutletsUserOrdersDoneToday[j].createdAt,
              DATE_COMPARE_TYPE.IS_SAME,
              'day',
              reportDisplayType,
              outletShifts
            )
          ) {
            // var typeParsed = typeToValueDict[allOutletsUserOrdersDoneToday[j].appType];
            var typeParsed = typeToValueDict[allOutletsUserOrdersDoneToday[j].appType ? allOutletsUserOrdersDoneToday[j].appType : APP_TYPE.MERCHANT];

            datasetDict[typeParsed].data[
              i
            ].value += allOutletsUserOrdersDoneToday[j][yAxisKey];

            datasetDict[typeParsed].data[
              i
            ].toolText = `RM ${datasetDict[typeParsed].data[
              i
            ].value.toFixed(2)}`;

            orderCount += 1;

            datasetDict[typeParsed].data[
              i
            ].totalOrders += 1;

            datasetDict[typeParsed].data[
              i
            ].totalSales += allOutletsUserOrdersDoneToday[j][yAxisKey];
          }
        }

        if (orderCount > 0) {
          for (var countIndex = 0; countIndex < allOutlets.length; countIndex++) {
            var type = allOutlets[countIndex].value;

            if (datasetDict[type].data[
              i
            ].totalOrders > 0) {
              datasetDict[type].data[
                i
              ].value = datasetDict[type].data[
                i
              ].value / datasetDict[type].data[i].totalOrders;

              datasetDict[type].data[
                i
              ].toolText = `RM ${datasetDict[type].data[
                i
              ].value.toFixed(2)}`;
            }
          }
        }

        // if (record.value > yMaxValue) {
        //     yMaxValue = record.value;
        // }

        // record.value = record.value.toFixed(0);

        // parsedData.push(record);
      }
    } else if (chartPeriod === CHART_PERIOD.THIS_MONTH) {
      // const allOutletsUserOrdersDoneToday = allOutletsUserOrdersDone.filter(order => {
      //     return moment().isSame(moment(order.createdAt), 'month');
      // });

      // const startDate = moment().subtract(moment(startDate).daysInMonth(), 'day').startOf('day');
      // const endDate = moment().endOf('day');

      const allOutletsUserOrdersDoneToday = allOutletsUserOrdersDone.filter(
        (order) => {
          return (
            moment(startDate).isSameOrBefore(order.createdAt) &&
            moment(endDate).isAfter(order.createdAt)
          );
        },
      );

      if (xAxisKey === CHART_X_AXIS_TYPE.DAY) {
        for (var i = 0; i < allOutlets.length; i++) {
          datasetDict[allOutlets[i].value] = {
            seriesname: allOutlets[i].label,
            data: Array.from(Array(moment(startDate).daysInMonth())).map(() => {
              return {
                value: 0,
                toolText: `RM 0.00`,
                totalOrders: 0,
                totalSales: 0,
              };
            }),
          };
        }

        for (var i = 0; i < moment(startDate).daysInMonth(); i++) {
          // for (var i = 0; i < 31; i++) {
          // const currDay = moment().startOf('month').add(i, 'day');
          const currDay = moment(startDate).add(i, 'day');

          // var record = {
          //     label: i.toString().padStart(2, '0'),
          //     value: 0,
          // };

          category.push({
            label: (i + 1).toString().padStart(2, '0'),
          });

          var orderCount = 0;

          for (var j = 0; j < allOutletsUserOrdersDoneToday.length; j++) {
            if (
              // moment(currDay).isSame(
              //   moment(allOutletsUserOrdersDoneToday[j].createdAt),
              //   'day',
              // )
              compareOrderDateByDisplayType(
                currDay,
                allOutletsUserOrdersDoneToday[j].createdAt,
                DATE_COMPARE_TYPE.IS_SAME,
                'day',
                reportDisplayType,
                outletShifts
              )
            ) {
              // var typeParsed = typeToValueDict[allOutletsUserOrdersDoneToday[j].appType];
              var typeParsed = typeToValueDict[allOutletsUserOrdersDoneToday[j].appType ? allOutletsUserOrdersDoneToday[j].appType : APP_TYPE.MERCHANT];

              datasetDict[typeParsed].data[
                i
              ].value += allOutletsUserOrdersDoneToday[j][yAxisKey];

              datasetDict[typeParsed].data[
                i
              ].toolText = `RM ${datasetDict[typeParsed].data[
                i
              ].value.toFixed(2)}`;

              orderCount += 1;

              datasetDict[typeParsed].data[
                i
              ].totalOrders += 1;

              datasetDict[typeParsed].data[
                i
              ].totalSales += allOutletsUserOrdersDoneToday[j][yAxisKey];
            }
          }

          if (orderCount > 0) {
            for (var countIndex = 0; countIndex < allOutlets.length; countIndex++) {
              var type = allOutlets[countIndex].value;

              if (datasetDict[type].data[
                i
              ].totalOrders > 0) {
                datasetDict[type].data[
                  i
                ].value = datasetDict[type].data[
                  i
                ].value / datasetDict[type].data[i].totalOrders;

                datasetDict[type].data[
                  i
                ].toolText = `RM ${datasetDict[type].data[
                  i
                ].value.toFixed(2)}`;
              }
            }
          }

          // if (record.value > yMaxValue) {
          //     yMaxValue = record.value;
          // }

          // record.value = record.value.toFixed(0);

          // parsedData.push(record);
        }
      }
      else if (xAxisKey === CHART_X_AXIS_TYPE.WEEK || xAxisKey === CHART_X_AXIS_TYPE.MONTH) {
        var currWeekCount = 0;
        var prevMonth = moment(startDate);

        for (var i = 0; i < allOutlets.length; i++) {
          datasetDict[allOutlets[i].value] = {
            seriesname: allOutlets[i].label,
            data: Array.from(Array(moment(endDate).diff(startDate, 'week'))).map(
              () => {
                return {
                  value: 0,
                  toolText: `RM 0.00`,
                  totalOrders: 0,
                  totalSales: 0,
                };
              },
            ),
          };
        }

        for (var i = 0; i < moment(endDate).diff(startDate, 'week'); i++) {
          const currWeek = moment(startDate).add(i, 'week');

          if (moment(currWeek).isSame(prevMonth, 'month')) {
            currWeekCount++;
          } else {
            prevMonth = moment(currWeek);

            currWeekCount = 0;
            currWeekCount++;
          }

          // var record = {
          //     label: (currWeekCount > 1 ? '' : moment(currWeek).format('MMM')) + ' W' + currWeekCount,
          //     value: 0,
          // };

          category.push({
            label:
              `${currWeekCount > 1 ? '' : moment(currWeek).format('MMM')
              } W${currWeekCount}`,
          });

          var orderCount = 0;

          for (var j = 0; j < allOutletsUserOrdersDoneToday.length; j++) {
            if (
              // moment(currWeek).isSame(
              //   moment(allOutletsUserOrdersDoneToday[j].createdAt),
              //   'week',
              // )
              compareOrderDateByDisplayType(
                currWeek,
                allOutletsUserOrdersDoneToday[j].createdAt,
                DATE_COMPARE_TYPE.IS_SAME,
                'week',
                reportDisplayType,
                outletShifts
              )
            ) {
              // var typeParsed = typeToValueDict[allOutletsUserOrdersDoneToday[j].appType];
              var typeParsed = typeToValueDict[allOutletsUserOrdersDoneToday[j].appType ? allOutletsUserOrdersDoneToday[j].appType : APP_TYPE.MERCHANT];

              datasetDict[typeParsed].data[
                i
              ].value += allOutletsUserOrdersDoneToday[j][yAxisKey];

              datasetDict[typeParsed].data[
                i
              ].toolText = `RM ${datasetDict[typeParsed].data[
                i
              ].value.toFixed(2)}`;

              orderCount += 1;

              datasetDict[typeParsed].data[
                i
              ].totalOrders += 1;

              datasetDict[typeParsed].data[
                i
              ].totalSales += allOutletsUserOrdersDoneToday[j][yAxisKey];
            }
          }

          if (orderCount > 0) {
            for (var countIndex = 0; countIndex < allOutlets.length; countIndex++) {
              var type = allOutlets[countIndex].value;

              if (datasetDict[type].data[
                i
              ].totalOrders > 0) {
                datasetDict[type].data[
                  i
                ].value = datasetDict[type].data[
                  i
                ].value / datasetDict[type].data[i].totalOrders;

                datasetDict[type].data[
                  i
                ].toolText = `RM ${datasetDict[type].data[
                  i
                ].value.toFixed(2)}`;
              }
            }
          }

          // if (record.value > yMaxValue) {
          //     yMaxValue = record.value;
          // }

          // record.value = record.value.toFixed(0);

          // parsedData.push(record);
        }
      }
    } else if (chartPeriod === CHART_PERIOD.THREE_MONTHS) {
      const startDate = moment().subtract(2, 'month').startOf('month');
      const endDate = moment().endOf('month');

      const allOutletsUserOrdersDoneToday = allOutletsUserOrdersDone.filter(
        (order) => {
          return (
            moment(startDate).isSameOrBefore(order.createdAt) &&
            moment(endDate).isAfter(order.createdAt)
          );
        },
      );

      if (xAxisKey === CHART_X_AXIS_TYPE.DAY) {
        for (var i = 0; i < allOutlets.length; i++) {
          datasetDict[allOutlets[i].value] = {
            seriesname: allOutlets[i].label,
            data: Array.from(Array(moment(endDate).diff(startDate, 'day'))).map(() => {
              return {
                value: 0,
                toolText: `RM 0.00`,
                totalOrders: 0,
                totalSales: 0,
              };
            }),
          };
        }

        for (var i = 0; i < moment(endDate).diff(startDate, 'day'); i++) {
          // for (var i = 0; i < 31; i++) {
          // const currDay = moment().startOf('month').add(i, 'day');
          const currDay = moment(startDate).add(i, 'day');

          // var record = {
          //     label: i.toString().padStart(2, '0'),
          //     value: 0,
          // };

          category.push({
            label: (i + 1).toString().padStart(2, '0'),
          });

          var orderCount = 0;

          for (var j = 0; j < allOutletsUserOrdersDoneToday.length; j++) {
            if (
              // moment(currDay).isSame(
              //   moment(allOutletsUserOrdersDoneToday[j].createdAt),
              //   'day',
              // )
              compareOrderDateByDisplayType(
                currDay,
                allOutletsUserOrdersDoneToday[j].createdAt,
                DATE_COMPARE_TYPE.IS_SAME,
                'day',
                reportDisplayType,
                outletShifts
              )
            ) {
              // var typeParsed = typeToValueDict[allOutletsUserOrdersDoneToday[j].appType];
              var typeParsed = typeToValueDict[allOutletsUserOrdersDoneToday[j].appType ? allOutletsUserOrdersDoneToday[j].appType : APP_TYPE.MERCHANT];

              datasetDict[typeParsed].data[
                i
              ].value += allOutletsUserOrdersDoneToday[j][yAxisKey];

              datasetDict[typeParsed].data[
                i
              ].toolText = `RM ${datasetDict[typeParsed].data[
                i
              ].value.toFixed(2)}`;

              orderCount += 1;

              datasetDict[typeParsed].data[
                i
              ].totalOrders += 1;

              datasetDict[typeParsed].data[
                i
              ].totalSales += allOutletsUserOrdersDoneToday[j][yAxisKey];
            }
          }

          if (orderCount > 0) {
            for (var countIndex = 0; countIndex < allOutlets.length; countIndex++) {
              var type = allOutlets[countIndex].value;

              if (datasetDict[type].data[
                i
              ].totalOrders > 0) {
                datasetDict[type].data[
                  i
                ].value = datasetDict[type].data[
                  i
                ].value / datasetDict[type].data[i].totalOrders;

                datasetDict[type].data[
                  i
                ].toolText = `RM ${datasetDict[type].data[
                  i
                ].value.toFixed(2)}`;
              }
            }
          }

          // if (record.value > yMaxValue) {
          //     yMaxValue = record.value;
          // }

          // record.value = record.value.toFixed(0);

          // parsedData.push(record);
        }
      }
      else if (xAxisKey === CHART_X_AXIS_TYPE.WEEK) {
        var currWeekCount = 0;
        var prevMonth = moment(startDate);

        for (var i = 0; i < allOutlets.length; i++) {
          datasetDict[allOutlets[i].value] = {
            seriesname: allOutlets[i].label,
            data: Array.from(Array(moment(endDate).diff(startDate, 'week'))).map(
              () => {
                return {
                  value: 0,
                  toolText: `RM 0.00`,
                  totalOrders: 0,
                  totalSales: 0,
                };
              },
            ),
          };
        }

        for (var i = 0; i < moment(endDate).diff(startDate, 'week'); i++) {
          const currWeek = moment(startDate).add(i, 'week');

          if (moment(currWeek).isSame(prevMonth, 'month')) {
            currWeekCount++;
          } else {
            prevMonth = moment(currWeek);

            currWeekCount = 0;
            currWeekCount++;
          }

          // var record = {
          //     label: (currWeekCount > 1 ? '' : moment(currWeek).format('MMM')) + ' W' + currWeekCount,
          //     value: 0,
          // };

          category.push({
            label:
              `${currWeekCount > 1 ? '' : moment(currWeek).format('MMM')
              } W${currWeekCount}`,
          });

          var orderCount = 0;

          for (var j = 0; j < allOutletsUserOrdersDoneToday.length; j++) {
            if (
              // moment(currWeek).isSame(
              //   moment(allOutletsUserOrdersDoneToday[j].createdAt),
              //   'week',
              // )
              compareOrderDateByDisplayType(
                currWeek,
                allOutletsUserOrdersDoneToday[j].createdAt,
                DATE_COMPARE_TYPE.IS_SAME,
                'week',
                reportDisplayType,
                outletShifts
              )
            ) {
              // var typeParsed = typeToValueDict[allOutletsUserOrdersDoneToday[j].appType];
              var typeParsed = typeToValueDict[allOutletsUserOrdersDoneToday[j].appType ? allOutletsUserOrdersDoneToday[j].appType : APP_TYPE.MERCHANT];

              datasetDict[typeParsed].data[
                i
              ].value += allOutletsUserOrdersDoneToday[j][yAxisKey];

              datasetDict[typeParsed].data[
                i
              ].toolText = `RM ${datasetDict[typeParsed].data[
                i
              ].value.toFixed(2)}`;

              orderCount += 1;

              datasetDict[typeParsed].data[
                i
              ].totalOrders += 1;

              datasetDict[typeParsed].data[
                i
              ].totalSales += allOutletsUserOrdersDoneToday[j][yAxisKey];
            }
          }

          if (orderCount > 0) {
            for (var countIndex = 0; countIndex < allOutlets.length; countIndex++) {
              var type = allOutlets[countIndex].value;

              if (datasetDict[type].data[
                i
              ].totalOrders > 0) {
                datasetDict[type].data[
                  i
                ].value = datasetDict[type].data[
                  i
                ].value / datasetDict[type].data[i].totalOrders;

                datasetDict[type].data[
                  i
                ].toolText = `RM ${datasetDict[type].data[
                  i
                ].value.toFixed(2)}`;
              }
            }
          }

          // if (record.value > yMaxValue) {
          //     yMaxValue = record.value;
          // }

          // record.value = record.value.toFixed(0);

          // parsedData.push(record);
        }
      }
      else if (xAxisKey === CHART_X_AXIS_TYPE.MONTH) {
        for (var i = 0; i < allOutlets.length; i++) {
          datasetDict[allOutlets[i].value] = {
            seriesname: allOutlets[i].label,
            data: Array.from(Array(3)).map(() => {
              return {
                value: 0,
                toolText: `RM 0.00`,
                totalOrders: 0,
                totalSales: 0,
              };
            }),
          };
        }

        var currMonth = moment(startDate).startOf('month');

        for (var i = 0; i < 3; i++) {
          // const currMonth = moment().startOf('year').add(i, 'month');
          var currMonth = moment(currMonth).add(i, 'month');

          // var record = {
          //     label: moment(currMonth).format('MMM'),
          //     value: 0,
          // };

          category.push({
            label: moment(currMonth).format('MMM'),
          });

          var orderCount = 0;

          for (var j = 0; j < allOutletsUserOrdersDoneToday.length; j++) {
            if (
              // moment(currMonth).isSame(
              //   moment(allOutletsUserOrdersDoneToday[j].createdAt),
              //   'month',
              // )
              compareOrderDateByDisplayType(
                currMonth,
                allOutletsUserOrdersDoneToday[j].createdAt,
                DATE_COMPARE_TYPE.IS_SAME,
                'month',
                reportDisplayType,
                outletShifts
              )
            ) {
              // var typeParsed = typeToValueDict[allOutletsUserOrdersDoneToday[j].appType];
              var typeParsed = typeToValueDict[allOutletsUserOrdersDoneToday[j].appType ? allOutletsUserOrdersDoneToday[j].appType : APP_TYPE.MERCHANT];

              datasetDict[typeParsed].data[
                i
              ].value += allOutletsUserOrdersDoneToday[j][yAxisKey];

              datasetDict[typeParsed].data[
                i
              ].toolText = `RM ${datasetDict[typeParsed].data[
                i
              ].value.toFixed(2)}`;

              orderCount += 1;

              datasetDict[typeParsed].data[
                i
              ].totalOrders += 1;

              datasetDict[typeParsed].data[
                i
              ].totalSales += allOutletsUserOrdersDoneToday[j][yAxisKey];
            }
          }

          if (orderCount > 0) {
            for (var countIndex = 0; countIndex < allOutlets.length; countIndex++) {
              var type = allOutlets[countIndex].value;

              if (datasetDict[type].data[
                i
              ].totalOrders > 0) {
                datasetDict[type].data[
                  i
                ].value = datasetDict[type].data[
                  i
                ].value / datasetDict[type].data[i].totalOrders;

                datasetDict[type].data[
                  i
                ].toolText = `RM ${datasetDict[type].data[
                  i
                ].value.toFixed(2)}`;
              }
            }
          }

          // if (record.value > yMaxValue) {
          //     yMaxValue = record.value;
          // }

          // record.value = record.value.toFixed(0);

          // parsedData.push(record);
        }
      }
    } else if (chartPeriod === CHART_PERIOD.SIX_MONTHS) {
      const startDate = moment().subtract(5, 'month').startOf('month');
      const endDate = moment().endOf('month');

      const allOutletsUserOrdersDoneToday = allOutletsUserOrdersDone.filter(
        (order) => {
          return (
            moment(startDate).isSameOrBefore(order.createdAt) &&
            moment(endDate).isAfter(order.createdAt)
          );
        },
      );

      if (xAxisKey === CHART_X_AXIS_TYPE.DAY || xAxisKey === CHART_X_AXIS_TYPE.WEEK) {
        for (var i = 0; i < allOutlets.length; i++) {
          datasetDict[allOutlets[i].value] = {
            seriesname: allOutlets[i].label,
            data: Array.from(Array(moment(endDate).diff(startDate, 'week'))).map(
              () => {
                return {
                  value: 0,
                  toolText: `RM 0.00`,
                  totalOrders: 0,
                  totalSales: 0,
                };
              },
            ),
          };
        }

        for (var i = 0; i < moment(endDate).diff(startDate, 'week'); i++) {
          const currWeek = moment(startDate).add(i, 'week');

          if (moment(currWeek).isSame(prevMonth, 'month')) {
            currWeekCount++;
          } else {
            prevMonth = moment(currWeek);

            currWeekCount = 0;
            currWeekCount++;
          }

          // var record = {
          //     label: 'W' + currWeekCount + ' ' + (currWeekCount > 1 ? '' : moment(currWeek).format('MMM')),
          //     value: 0,
          // };

          category.push({
            label:
              `W${currWeekCount
              } ${currWeekCount > 1 ? '' : moment(currWeek).format('MMM')}`,
          });

          var orderCount = 0;

          for (var j = 0; j < allOutletsUserOrdersDoneToday.length; j++) {
            if (
              // moment(currWeek).isSame(
              //   moment(allOutletsUserOrdersDoneToday[j].createdAt),
              //   'week',
              // )
              compareOrderDateByDisplayType(
                currWeek,
                allOutletsUserOrdersDoneToday[j].createdAt,
                DATE_COMPARE_TYPE.IS_SAME,
                'week',
                reportDisplayType,
                outletShifts
              )
            ) {
              // var typeParsed = typeToValueDict[allOutletsUserOrdersDoneToday[j].appType];
              var typeParsed = typeToValueDict[allOutletsUserOrdersDoneToday[j].appType ? allOutletsUserOrdersDoneToday[j].appType : APP_TYPE.MERCHANT];

              datasetDict[typeParsed].data[
                i
              ].value += allOutletsUserOrdersDoneToday[j][yAxisKey];

              datasetDict[typeParsed].data[
                i
              ].toolText = `RM ${datasetDict[typeParsed].data[
                i
              ].value.toFixed(2)}`;

              orderCount += 1;

              datasetDict[typeParsed].data[
                i
              ].totalOrders += 1;

              datasetDict[typeParsed].data[
                i
              ].totalSales += allOutletsUserOrdersDoneToday[j][yAxisKey];
            }
          }

          if (orderCount > 0) {
            for (var countIndex = 0; countIndex < allOutlets.length; countIndex++) {
              var type = allOutlets[countIndex].value;

              if (datasetDict[type].data[
                i
              ].totalOrders > 0) {
                datasetDict[type].data[
                  i
                ].value = datasetDict[type].data[
                  i
                ].value / datasetDict[type].data[i].totalOrders;

                datasetDict[type].data[
                  i
                ].toolText = `RM ${datasetDict[type].data[
                  i
                ].value.toFixed(2)}`;
              }
            }
          }

          // if (record.value > yMaxValue) {
          //     yMaxValue = record.value;
          // }

          // record.value = record.value.toFixed(0);

          // parsedData.push(record);
        }
      }
      else if (xAxisKey === CHART_X_AXIS_TYPE.MONTH) {
        for (var i = 0; i < allOutlets.length; i++) {
          datasetDict[allOutlets[i].value] = {
            seriesname: allOutlets[i].label,
            data: Array.from(Array(6)).map(() => {
              return {
                value: 0,
                toolText: `RM 0.00`,
                totalOrders: 0,
                totalSales: 0,
              };
            }),
          };
        }

        var currMonth = moment(startDate).startOf('month');

        for (var i = 0; i < 6; i++) {
          // const currMonth = moment().startOf('year').add(i, 'month');
          var currMonth = moment(currMonth).add(i, 'month');

          // var record = {
          //     label: moment(currMonth).format('MMM'),
          //     value: 0,
          // };

          category.push({
            label: moment(currMonth).format('MMM'),
          });

          var orderCount = 0;

          for (var j = 0; j < allOutletsUserOrdersDoneToday.length; j++) {
            if (
              // moment(currMonth).isSame(
              //   moment(allOutletsUserOrdersDoneToday[j].createdAt),
              //   'month',
              // )
              compareOrderDateByDisplayType(
                currMonth,
                allOutletsUserOrdersDoneToday[j].createdAt,
                DATE_COMPARE_TYPE.IS_SAME,
                'month',
                reportDisplayType,
                outletShifts
              )
            ) {
              // var typeParsed = typeToValueDict[allOutletsUserOrdersDoneToday[j].appType];
              var typeParsed = typeToValueDict[allOutletsUserOrdersDoneToday[j].appType ? allOutletsUserOrdersDoneToday[j].appType : APP_TYPE.MERCHANT];

              datasetDict[typeParsed].data[
                i
              ].value += allOutletsUserOrdersDoneToday[j][yAxisKey];

              datasetDict[typeParsed].data[
                i
              ].toolText = `RM ${datasetDict[typeParsed].data[
                i
              ].value.toFixed(2)}`;

              orderCount += 1;

              datasetDict[typeParsed].data[
                i
              ].totalOrders += 1;

              datasetDict[typeParsed].data[
                i
              ].totalSales += allOutletsUserOrdersDoneToday[j][yAxisKey];
            }
          }

          if (orderCount > 0) {
            for (var countIndex = 0; countIndex < allOutlets.length; countIndex++) {
              var type = allOutlets[countIndex].value;

              if (datasetDict[type].data[
                i
              ].totalOrders > 0) {
                datasetDict[type].data[
                  i
                ].value = datasetDict[type].data[
                  i
                ].value / datasetDict[type].data[i].totalOrders;

                datasetDict[type].data[
                  i
                ].toolText = `RM ${datasetDict[type].data[
                  i
                ].value.toFixed(2)}`;
              }
            }
          }

          // if (record.value > yMaxValue) {
          //     yMaxValue = record.value;
          // }

          // record.value = record.value.toFixed(0);

          // parsedData.push(record);
        }
      }
    } else if (chartPeriod === CHART_PERIOD.THIS_YEAR) {
      const allOutletsUserOrdersDoneToday = allOutletsUserOrdersDone.filter(
        (order) => {
          return moment().isSame(moment(order.createdAt), 'year');
        },
      );

      if (xAxisKey === CHART_X_AXIS_TYPE.DAY || xAxisKey === CHART_X_AXIS_TYPE.WEEK) {
        for (var i = 0; i < allOutlets.length; i++) {
          datasetDict[allOutlets[i].value] = {
            seriesname: allOutlets[i].label,
            data: Array.from(Array(moment(endDate).diff(startDate, 'week'))).map(
              () => {
                return {
                  value: 0,
                  toolText: `RM 0.00`,
                  totalOrders: 0,
                  totalSales: 0,
                };
              },
            ),
          };
        }

        for (var i = 0; i < moment(endDate).diff(startDate, 'week'); i++) {
          const currWeek = moment(startDate).add(i, 'week');

          if (moment(currWeek).isSame(prevMonth, 'month')) {
            currWeekCount++;
          } else {
            prevMonth = moment(currWeek);

            currWeekCount = 0;
            currWeekCount++;
          }

          // var record = {
          //     label: 'W' + currWeekCount + ' ' + (currWeekCount > 1 ? '' : moment(currWeek).format('MMM')),
          //     value: 0,
          // };

          category.push({
            label:
              `W${currWeekCount
              } ${currWeekCount > 2 ? '' : moment(currWeek).format('MMM')}`,
          });

          var orderCount = 0;

          for (var j = 0; j < allOutletsUserOrdersDoneToday.length; j++) {
            if (
              // moment(currWeek).isSame(
              //   moment(allOutletsUserOrdersDoneToday[j].createdAt),
              //   'week',
              // )
              compareOrderDateByDisplayType(
                currWeek,
                allOutletsUserOrdersDoneToday[j].createdAt,
                DATE_COMPARE_TYPE.IS_SAME,
                'week',
                reportDisplayType,
                outletShifts
              )
            ) {
              // var typeParsed = typeToValueDict[allOutletsUserOrdersDoneToday[j].appType];
              var typeParsed = typeToValueDict[allOutletsUserOrdersDoneToday[j].appType ? allOutletsUserOrdersDoneToday[j].appType : APP_TYPE.MERCHANT];

              datasetDict[typeParsed].data[
                i
              ].value += allOutletsUserOrdersDoneToday[j][yAxisKey];

              datasetDict[typeParsed].data[
                i
              ].toolText = `RM ${datasetDict[typeParsed].data[
                i
              ].value.toFixed(2)}`;

              orderCount += 1;

              datasetDict[typeParsed].data[
                i
              ].totalOrders += 1;

              datasetDict[typeParsed].data[
                i
              ].totalSales += allOutletsUserOrdersDoneToday[j][yAxisKey];
            }
          }

          if (orderCount > 0) {
            for (var countIndex = 0; countIndex < allOutlets.length; countIndex++) {
              var type = allOutlets[countIndex].value;

              if (datasetDict[type].data[
                i
              ].totalOrders > 0) {
                datasetDict[type].data[
                  i
                ].value = datasetDict[type].data[
                  i
                ].value / datasetDict[type].data[i].totalOrders;

                datasetDict[type].data[
                  i
                ].toolText = `RM ${datasetDict[type].data[
                  i
                ].value.toFixed(2)}`;
              }
            }
          }

          // if (record.value > yMaxValue) {
          //     yMaxValue = record.value;
          // }

          // record.value = record.value.toFixed(0);

          // parsedData.push(record);
        }
      }
      else if (xAxisKey === CHART_X_AXIS_TYPE.MONTH) {
        for (var i = 0; i < allOutlets.length; i++) {
          datasetDict[allOutlets[i].value] = {
            seriesname: allOutlets[i].label,
            data: Array.from(Array(12)).map(() => {
              return {
                value: 0,
                toolText: `RM 0.00`,
                totalOrders: 0,
                totalSales: 0,
              };
            }),
          };
        }

        for (var i = 0; i < 12; i++) {
          const currMonth = moment().startOf('year').add(i, 'month');

          // var record = {
          //     label: moment(currMonth).format('MMM'),
          //     value: 0,
          // };

          category.push({
            label: moment(currMonth).format('MMM'),
          });

          var orderCount = 0;

          for (var j = 0; j < allOutletsUserOrdersDoneToday.length; j++) {
            if (
              // moment(currMonth).isSame(
              //   moment(allOutletsUserOrdersDoneToday[j].createdAt),
              //   'month',
              // )
              compareOrderDateByDisplayType(
                currMonth,
                allOutletsUserOrdersDoneToday[j].createdAt,
                DATE_COMPARE_TYPE.IS_SAME,
                'mohth',
                reportDisplayType,
                outletShifts
              )
            ) {
              // var typeParsed = typeToValueDict[allOutletsUserOrdersDoneToday[j].appType];
              var typeParsed = typeToValueDict[allOutletsUserOrdersDoneToday[j].appType ? allOutletsUserOrdersDoneToday[j].appType : APP_TYPE.MERCHANT];

              datasetDict[typeParsed].data[
                i
              ].value += allOutletsUserOrdersDoneToday[j][yAxisKey];

              datasetDict[typeParsed].data[
                i
              ].toolText = `RM ${datasetDict[typeParsed].data[
                i
              ].value.toFixed(2)}`;

              orderCount += 1;

              datasetDict[typeParsed].data[
                i
              ].totalOrders += 1;

              datasetDict[typeParsed].data[
                i
              ].totalSales += allOutletsUserOrdersDoneToday[j][yAxisKey];
            }
          }

          if (orderCount > 0) {
            for (var countIndex = 0; countIndex < allOutlets.length; countIndex++) {
              var type = allOutlets[countIndex].value;

              if (datasetDict[type].data[
                i
              ].totalOrders > 0) {
                datasetDict[type].data[
                  i
                ].value = datasetDict[type].data[
                  i
                ].value / datasetDict[type].data[i].totalOrders;

                datasetDict[type].data[
                  i
                ].toolText = `RM ${datasetDict[type].data[
                  i
                ].value.toFixed(2)}`;
              }
            }
          }

          // if (record.value > yMaxValue) {
          //     yMaxValue = record.value;
          // }

          // record.value = record.value.toFixed(0);

          // parsedData.push(record);
        }
      }
    } else if (chartPeriod === CHART_PERIOD.YTD) {
      const startDate = moment().subtract(11, 'month').startOf('month');
      const endDate = moment().endOf('month');

      const allOutletsUserOrdersDoneToday = allOutletsUserOrdersDone.filter(
        (order) => {
          return (
            moment(startDate).isSameOrBefore(order.createdAt) &&
            moment(endDate).isAfter(order.createdAt)
          );
        },
      );

      if (xAxisKey === CHART_X_AXIS_TYPE.DAY || xAxisKey === CHART_X_AXIS_TYPE.WEEK) {
        for (var i = 0; i < allOutlets.length; i++) {
          datasetDict[allOutlets[i].value] = {
            seriesname: allOutlets[i].label,
            data: Array.from(Array(moment(endDate).diff(startDate, 'week'))).map(
              () => {
                return {
                  value: 0,
                  toolText: `RM 0.00`,
                  totalOrders: 0,
                  totalSales: 0,
                };
              },
            ),
          };
        }

        for (var i = 0; i < moment(endDate).diff(startDate, 'week'); i++) {
          const currWeek = moment(startDate).add(i, 'week');

          if (moment(currWeek).isSame(prevMonth, 'month')) {
            currWeekCount++;
          } else {
            prevMonth = moment(currWeek);

            currWeekCount = 0;
            currWeekCount++;
          }

          // var record = {
          //     label: 'W' + currWeekCount + ' ' + (currWeekCount > 1 ? '' : moment(currWeek).format('MMM')),
          //     value: 0,
          // };

          category.push({
            label:
              `W${currWeekCount
              } ${currWeekCount > 2 ? '' : moment(currWeek).format('MMM')}`,
          });

          var orderCount = 0;

          for (var j = 0; j < allOutletsUserOrdersDoneToday.length; j++) {
            if (
              // moment(currWeek).isSame(
              //   moment(allOutletsUserOrdersDoneToday[j].createdAt),
              //   'week',
              // )
              compareOrderDateByDisplayType(
                currWeek,
                allOutletsUserOrdersDoneToday[j].createdAt,
                DATE_COMPARE_TYPE.IS_SAME,
                'week',
                reportDisplayType,
                outletShifts
              )
            ) {
              // var typeParsed = typeToValueDict[allOutletsUserOrdersDoneToday[j].appType];
              var typeParsed = typeToValueDict[allOutletsUserOrdersDoneToday[j].appType ? allOutletsUserOrdersDoneToday[j].appType : APP_TYPE.MERCHANT];

              datasetDict[typeParsed].data[
                i
              ].value += allOutletsUserOrdersDoneToday[j][yAxisKey];

              datasetDict[typeParsed].data[
                i
              ].toolText = `RM ${datasetDict[typeParsed].data[
                i
              ].value.toFixed(2)}`;

              orderCount += 1;

              datasetDict[typeParsed].data[
                i
              ].totalOrders += 1;

              datasetDict[typeParsed].data[
                i
              ].totalSales += allOutletsUserOrdersDoneToday[j][yAxisKey];
            }
          }

          if (orderCount > 0) {
            for (var countIndex = 0; countIndex < allOutlets.length; countIndex++) {
              var type = allOutlets[countIndex].value;

              if (datasetDict[type].data[
                i
              ].totalOrders > 0) {
                datasetDict[type].data[
                  i
                ].value = datasetDict[type].data[
                  i
                ].value / datasetDict[type].data[i].totalOrders;

                datasetDict[type].data[
                  i
                ].toolText = `RM ${datasetDict[type].data[
                  i
                ].value.toFixed(2)}`;
              }
            }
          }

          // if (record.value > yMaxValue) {
          //     yMaxValue = record.value;
          // }

          // record.value = record.value.toFixed(0);

          // parsedData.push(record);
        }
      }
      else if (xAxisKey === CHART_X_AXIS_TYPE.MONTH) {
        for (var i = 0; i < allOutlets.length; i++) {
          datasetDict[allOutlets[i].value] = {
            seriesname: allOutlets[i].label,
            data: Array.from(Array(12)).map(() => {
              return {
                value: 0,
                toolText: `RM 0.00`,
                totalOrders: 0,
                totalSales: 0,
              };
            }),
          };
        }

        for (var i = 0; i < 12; i++) {
          const currMonth = moment(startDate).add(i, 'month');

          // var record = {
          //     label: moment(currMonth).format('YY\' MMM'),
          //     value: 0,
          // };

          category.push({
            label: moment(currMonth).format("YY' MMM"),
          });

          var orderCount = 0;

          for (var j = 0; j < allOutletsUserOrdersDoneToday.length; j++) {
            if (
              // moment(currMonth).isSame(
              //   moment(allOutletsUserOrdersDoneToday[j].createdAt),
              //   'month',
              // )
              compareOrderDateByDisplayType(
                currMonth,
                allOutletsUserOrdersDoneToday[j].createdAt,
                DATE_COMPARE_TYPE.IS_SAME,
                'month',
                reportDisplayType,
                outletShifts
              )
            ) {
              // var typeParsed = typeToValueDict[allOutletsUserOrdersDoneToday[j].appType];
              var typeParsed = typeToValueDict[allOutletsUserOrdersDoneToday[j].appType ? allOutletsUserOrdersDoneToday[j].appType : APP_TYPE.MERCHANT];

              datasetDict[typeParsed].data[
                i
              ].value += allOutletsUserOrdersDoneToday[j][yAxisKey];

              datasetDict[typeParsed].data[
                i
              ].toolText = `RM ${datasetDict[typeParsed].data[
                i
              ].value.toFixed(2)}`;

              orderCount += 1;

              datasetDict[typeParsed].data[
                i
              ].totalOrders += 1;

              datasetDict[typeParsed].data[
                i
              ].totalSales += allOutletsUserOrdersDoneToday[j][yAxisKey];
            }
          }

          if (orderCount > 0) {
            for (var countIndex = 0; countIndex < allOutlets.length; countIndex++) {
              var type = allOutlets[countIndex].value;

              if (datasetDict[type].data[
                i
              ].totalOrders > 0) {
                datasetDict[type].data[
                  i
                ].value = datasetDict[type].data[
                  i
                ].value / datasetDict[type].data[i].totalOrders;

                datasetDict[type].data[
                  i
                ].toolText = `RM ${datasetDict[type].data[
                  i
                ].value.toFixed(2)}`;
              }
            }
          }

          // if (record.value > yMaxValue) {
          //     yMaxValue = record.value;
          // }

          // record.value = record.value.toFixed(0);

          // parsedData.push(record);
        }
      }
    } else if (chartPeriod === CHART_PERIOD.NONE) {
      const allOutletsUserOrdersDoneTodayRange =
        allOutletsUserOrdersDone.filter((order) => {
          return (
            moment(startDate).isSameOrBefore(order.createdAt) &&
            moment(endDate).isAfter(order.createdAt)
          );
        });

      if (
        moment(endDate).diff(startDate, 'day') <=
        moment(startDate).daysInMonth()
      ) {
        endDate = moment(endDate).add(1, 'day');

        const diffDays = moment(endDate).diff(startDate, 'day');

        var prevDate = moment(startDate).add(-1, 'month');

        for (var i = 0; i < allOutlets.length; i++) {
          datasetDict[allOutlets[i].value] = {
            seriesname: allOutlets[i].label,
            data: Array.from(Array(diffDays)).map(() => {
              return {
                value: 0,
                toolText: `RM 0.00`,
                totalOrders: 0,
                totalSales: 0,
              };
            }),
          };
        }

        for (var i = 0; i < diffDays; i++) {
          const currDate = moment(startDate).add(i, 'day');

          category.push({
            //'label': moment(currDate).format('DD MMM'),
            label: !moment(currDate).isSame(prevDate, 'month')
              ? moment(currDate).format('DD MMM')
              : moment(currDate).format('DD'),
            //'label': (i > 1 ? moment(currDate).format('DD') : moment(currDate).format('DD MMM'))
          });

          prevDate = currDate;

          var orderCount = 0;

          for (var j = 0; j < allOutletsUserOrdersDoneTodayRange.length; j++) {
            if (
              // allOutletsUserOrdersDoneTodayRange[j].outletId === allOutlets[i].uniqueId &&
              // moment(currDate).isSame(
              //   moment(allOutletsUserOrdersDoneTodayRange[j].createdAt),
              //   'day',
              // )
              compareOrderDateByDisplayType(
                currDate,
                allOutletsUserOrdersDoneTodayRange[j].createdAt,
                DATE_COMPARE_TYPE.IS_SAME,
                'day',
                reportDisplayType,
                outletShifts
              )
            ) {
              // var typeParsed = typeToValueDict[allOutletsUserOrdersDoneTodayRange[j].appType];
              var typeParsed = typeToValueDict[allOutletsUserOrdersDoneTodayRange[j].appType ? allOutletsUserOrdersDoneTodayRange[j].appType : APP_TYPE.MERCHANT];

              datasetDict[typeParsed].data[
                i
              ].value += allOutletsUserOrdersDoneTodayRange[j][yAxisKey];

              datasetDict[typeParsed].data[
                i
              ].toolText = `RM ${datasetDict[typeParsed].data[
                i
              ].value.toFixed(2)}`;

              orderCount += 1;

              datasetDict[typeParsed].data[
                i
              ].totalOrders += 1;

              datasetDict[typeParsed].data[
                i
              ].totalSales += allOutletsUserOrdersDoneTodayRange[j][yAxisKey];
            }
          }

          if (orderCount > 0) {
            for (var countIndex = 0; countIndex < allOutlets.length; countIndex++) {
              var type = allOutlets[countIndex].value;

              if (datasetDict[type].data[
                i
              ].totalOrders > 0) {
                datasetDict[type].data[
                  i
                ].value = datasetDict[type].data[
                  i
                ].value / datasetDict[type].data[i].totalOrders;

                datasetDict[type].data[
                  i
                ].toolText = `RM ${datasetDict[type].data[
                  i
                ].value.toFixed(2)}`;
              }
            }
          }

          // if (record.value > yMaxValue) {
          //     yMaxValue = record.value;
          // }

          // record.value = record.value.toFixed(0);

          // parsedData.push(record);
        }
      } else if (moment(endDate).diff(startDate, 'month') <= 6) {
        var prevMonth = moment(startDate);

        const diffWeeks = moment(endDate).diff(startDate, 'week');

        for (var i = 0; i < allOutlets.length; i++) {
          datasetDict[allOutlets[i].value] = {
            seriesname: allOutlets[i].label,
            data: Array.from(Array(diffWeeks)).map(() => {
              return {
                value: 0,
                toolText: `RM 0.00`,
                totalOrders: 0,
                totalSales: 0,
              };
            }),
          };
        }

        for (var i = 0; i < diffWeeks; i++) {
          const currDate = moment(startDate).add(i, 'week');

          if (moment(currDate).isSame(prevMonth, 'month')) {
            currWeekCount++;
          } else {
            prevMonth = moment(currDate);

            currWeekCount = 0;
            currWeekCount++;
          }

          category.push({
            // label: moment(currDate).format('MMM') + ' W' + currWeekCount,
            label:
              `${moment(currDate).format('MMM')
              } W${countWeekdayOccurrencesInMonth(currDate)}`,
          });

          var orderCount = 0;

          for (var j = 0; j < allOutletsUserOrdersDoneTodayRange.length; j++) {
            if (
              // allOutletsUserOrdersDoneTodayRange[j].outletId === allOutlets[i].uniqueId &&
              // moment(currDate).isSame(
              //   moment(allOutletsUserOrdersDoneTodayRange[j].createdAt),
              //   'week',
              // )
              compareOrderDateByDisplayType(
                currDate,
                allOutletsUserOrdersDoneTodayRange[j].createdAt,
                DATE_COMPARE_TYPE.IS_SAME,
                'week',
                reportDisplayType,
                outletShifts
              )
            ) {
              // var typeParsed = typeToValueDict[allOutletsUserOrdersDoneTodayRange[j].appType];
              var typeParsed = typeToValueDict[allOutletsUserOrdersDoneTodayRange[j].appType ? allOutletsUserOrdersDoneTodayRange[j].appType : APP_TYPE.MERCHANT];

              datasetDict[typeParsed].data[
                i
              ].value += allOutletsUserOrdersDoneTodayRange[j][yAxisKey];

              datasetDict[typeParsed].data[
                i
              ].toolText = `RM ${datasetDict[typeParsed].data[
                i
              ].value.toFixed(2)}`;

              orderCount += 1;

              datasetDict[typeParsed].data[
                i
              ].totalOrders += 1;

              datasetDict[typeParsed].data[
                i
              ].totalSales += allOutletsUserOrdersDoneTodayRange[j][yAxisKey];
            }
          }

          if (orderCount > 0) {
            for (var countIndex = 0; countIndex < allOutlets.length; countIndex++) {
              var type = allOutlets[countIndex].value;

              if (datasetDict[type].data[
                i
              ].totalOrders > 0) {
                datasetDict[type].data[
                  i
                ].value = datasetDict[type].data[
                  i
                ].value / datasetDict[type].data[i].totalOrders;

                datasetDict[type].data[
                  i
                ].toolText = `RM ${datasetDict[type].data[
                  i
                ].value.toFixed(2)}`;
              }
            }
          }

          // if (record.value > yMaxValue) {
          //     yMaxValue = record.value;
          // }

          // record.value = record.value.toFixed(0);

          // parsedData.push(record);
        }
      }
      // if (moment(endDate).diff(startDate, 'month') <= 6) // for now no need conditions first, for more than 6 months interval
      else {
        const diffYears = moment(endDate).diff(startDate, 'month');

        for (var i = 0; i < allOutlets.length; i++) {
          datasetDict[allOutlets[i].value] = {
            seriesname: allOutlets[i].label,
            data: Array.from(Array(diffYears)).map(() => {
              return {
                value: 0,
                toolText: `RM 0.00`,
                totalOrders: 0,
                totalSales: 0,
              };
            }),
          };
        }

        for (var i = 0; i < diffYears; i++) {
          const currDate = moment(startDate).add(i, 'month');

          category.push({
            label: moment(currDate).format("YY' MMM"),
          });

          var orderCount = 0;

          for (var j = 0; j < allOutletsUserOrdersDoneTodayRange.length; j++) {
            if (
              // allOutletsUserOrdersDoneTodayRange[j].outletId === allOutlets[i].uniqueId &&
              // moment(currDate).isSame(
              //   moment(allOutletsUserOrdersDoneTodayRange[j].createdAt),
              //   'month',
              // )
              compareOrderDateByDisplayType(
                currDate,
                allOutletsUserOrdersDoneTodayRange[j].createdAt,
                DATE_COMPARE_TYPE.IS_SAME,
                'month',
                reportDisplayType,
                outletShifts
              )
            ) {
              // var typeParsed = typeToValueDict[allOutletsUserOrdersDoneTodayRange[j].appType];
              var typeParsed = typeToValueDict[allOutletsUserOrdersDoneTodayRange[j].appType ? allOutletsUserOrdersDoneTodayRange[j].appType : APP_TYPE.MERCHANT];

              datasetDict[typeParsed].data[
                i
              ].value += allOutletsUserOrdersDoneTodayRange[j][yAxisKey];

              datasetDict[typeParsed].data[
                i
              ].toolText = `RM ${datasetDict[typeParsed].data[
                i
              ].value.toFixed(2)}`;

              orderCount += 1;

              datasetDict[typeParsed].data[
                i
              ].totalOrders += 1;

              datasetDict[typeParsed].data[
                i
              ].totalSales += allOutletsUserOrdersDoneTodayRange[j][yAxisKey];
            }
          }

          if (orderCount > 0) {
            for (var countIndex = 0; countIndex < allOutlets.length; countIndex++) {
              var type = allOutlets[countIndex].value;

              if (datasetDict[type].data[
                i
              ].totalOrders > 0) {
                datasetDict[type].data[
                  i
                ].value = datasetDict[type].data[
                  i
                ].value / datasetDict[type].data[i].totalOrders;

                datasetDict[type].data[
                  i
                ].toolText = `RM ${datasetDict[type].data[
                  i
                ].value.toFixed(2)}`;
              }
            }
          }

          // if (record.value > yMaxValue) {
          //     yMaxValue = record.value;
          // }

          // record.value = record.value.toFixed(0);

          // parsedData.push(record);
        }
      }
    }

    // const allOutletsUserOrdersDoneToday = allOutletsUserOrdersDone.filter(order => {
    //     return moment().isSame(moment(order.createdAt), 'year');
    // });

    // for (var i = 0; i < 12; i++) {
    //     var record = {
    //         label: MONTH_TO_CHART_LABEL[i],
    //         value: 0,
    //     };

    //     const currMonth = moment().startOf('year').add(i, 'month');

    //     for (var j = 0; j < allOutletsUserOrdersDoneToday.length; j++) {
    //         // if (allOutletsUserOrdersDoneToday[j].outletId === allOutlets[i].uniqueId) {
    //         //     record.value += allOutletsUserOrdersDoneToday[j].finalPrice;

    //         //     // if (allOutletsUserOrdersDoneToday[j].finalPrice > yMaxValue) {
    //         //     //     yMaxValue += allOutletsUserOrdersDoneToday[j].finalPrice;
    //         //     // }
    //         // }

    //         if (moment(currMonth).isSame(moment(allOutletsUserOrdersDoneToday[j].createdAt), 'month')) {
    //             record.value += allOutletsUserOrdersDoneToday[j].finalPrice;
    //         }
    //     }

    //     // yMaxValue += record.value;
    //     if (record.value > yMaxValue) {
    //         yMaxValue = record.value;
    //     }

    //     record.value = record.value.toFixed(0);

    //     parsedData.push(record);
    // }

    for (var j = 0; j < allOutlets.length; j++) {
      var outletId = allOutlets[j].value;

      var currTotal = 0;

      for (var k = 0; k < datasetDict[outletId].data.length; k++) {
        // currTotal += datasetDict[outletId].data[k].value;

        currTotal = datasetDict[outletId].data[k].value;

        if (currTotal > yMaxValue) {
          yMaxValue = currTotal;
        }
      }

      // if (currTotal > yMaxValue) {
      //     yMaxValue = currTotal;
      // }
    }

    var chartLegend = [];

    const dataset = Object.entries(datasetDict).map(([key, value], index) => {
      const mappedIndex =
        index % CHART_COLORS[CHART_TYPE.REPORT_AOV_SALES].length; // might got more than 10 preset colors, set to infinite list instead

      chartLegend.push({
        itemName: value.seriesname,
        itemSku: value.seriesname,
        chartColor:
          CHART_COLORS[CHART_TYPE.REPORT_AOV_SALES][mappedIndex],
      });

      return {
        ...value,
        color: CHART_COLORS[CHART_TYPE.REPORT_AOV_SALES][mappedIndex],
      };
    });

    var output = {};

    output.type = CHART_DATA[CHART_TYPE.REPORT_AOV_SALES].type;
    output.dataFormat =
      CHART_DATA[CHART_TYPE.REPORT_AOV_SALES].dataFormat;
    output.dataSource = {
      chart: CHART_DATA[CHART_TYPE.REPORT_AOV_SALES].chart,

      categories: [
        {
          category,
        },
      ],
      dataset,

      annotations,

      // data: parsedData,
    };

    // // console.log('parsedData');
    // // console.log(parsedData);
    // console.log(yMaxValue);

    if (output.type !== 'HLinearGauge') {
      // const yAxisStep = Math.ceil((yMaxValue / 10) / 10) * 10;

      // const yMaxValueParsed = Math.ceil(yMaxValue / yAxisStep);
      // const yAxisMaxValue = yMaxValueParsed === 1 ? 2 : yMaxValueParsed;
      // const numDivLines = yMaxValueParsed === 1 ? 1 : yMaxValueParsed - 1;

      const baseNumberLength = yMaxValue.toFixed(0).length - 1;
      const baseNumber = parseInt(
        `1${Array(baseNumberLength)
          .fill()
          .map((num) => 0)
          .join('')}`,
      );
      var maxNumber = baseNumber;

      if (yMaxValue > baseNumber * 5) {
        maxNumber = baseNumber * 10;
      } else if (yMaxValue > baseNumber * 3) {
        maxNumber = baseNumber * 5;
      } else if (yMaxValue > baseNumber * 2) {
        maxNumber = baseNumber * 3;
      } else {
        maxNumber = baseNumber * 2;
      }

      maxNumber = getSuitableYAxisMaxValue(maxNumber);

      maxNumber = Math.max(maxNumber, 100);

      output.dataSource.chart = {
        ...output.dataSource.chart,
        labelDisplay: 'WRAP',
        // labelDisplay: 'STAGGER',
        // labelStep: 2,
        bgColor: '#ffffff',
        canvasBgColor: '#ffffff',
        bgAlpha: 100,

        useEllipsesWhenOverflow: 1,

        // showYAxisValues: 0,
        // yaxismaxvalue: yAxisMaxValue * yAxisStep,
        yaxismaxvalue: maxNumber,
        yaxisminvalue: yMinValue,
        adjustDiv: 0,
        // numDivLines: numDivLines,
        numDivLines: 9,

        // yaxisminvalue: 0,
        formatNumberScale: 0,
        decimals: 0,
      };

      // // console.log('/////////////////////////////////////////////////')

      // // console.log({
      //     yMaxValue: yMaxValue,
      //     yMaxValueParsed: yMaxValueParsed,
      //     yaxismaxvalue: yAxisMaxValue * yAxisStep,
      //     yaxisminvalue: yMinValue,
      //     adjustDiv: 0,
      //     numDivLines: numDivLines,
      // });

      // // console.log('/////////////////////////////////////////////////')

      return {
        chartData: output,
        chartLegend,
      };
    }

    // if (output.type === 'column2D') {
    //     if (type === CHART_TYPE.S_MILK) {
    //         // for (let i = 0; i < output.dataSource.data.length; i++) {
    //         //     if (output.dataSource.data[i].value.length > 0) {
    //         //         yMaxValue = parseInt(output.dataSource.data[i].value) > yMaxValue ? parseInt(output.dataSource.data[i].value) : yMaxValue;

    //         //     }
    //         // }

    //         // const yMaxValueParsed = Math.ceil(yMaxValue / 5);
    //         // const yAxisMaxValue = yMaxValueParsed === 1 ? 2 : yMaxValueParsed;
    //         // const numDivLines = yMaxValueParsed === 1 ? 1 : yMaxValueParsed - 1;

    //         // output.dataSource.chart = {
    //         //     ...output.dataSource.chart,
    //         //     adjustDiv: 0,
    //         //     numDivLines: numDivLines,
    //         //     yaxismaxvalue: yAxisMaxValue * 5,
    //         //     yaxisminvalue: yMinValue,
    //         //     formatNumberScale: 0,
    //         // };
    //     }
    //     else if (type === CHART_TYPE.S_SHIT_PEE) {
    //         for (let i = 0; i < output.dataSource.data.length; i++) {
    //             if (output.dataSource.data[i].value.length > 0) {
    //                 yMaxValue = parseInt(output.dataSource.data[i].value) > yMaxValue ? parseInt(output.dataSource.data[i].value) : yMaxValue;
    //             }
    //         }

    //         output.dataSource.chart = {
    //             ...output.dataSource.chart,
    //             adjustDiv: 0,
    //             numDivLines: yMaxValue,
    //             yaxismaxvalue: yMaxValue + 1,
    //             yaxisminvalue: yMinValue,
    //             formatNumberScale: 0,
    //         };
    //     }
    // }

    // console.log('sales line chart output');
    // console.log(output);

    return output;
  } catch (ex) {
    console.log('chart error');
    console.log(ex);

    return false;
  }
};

export const getDataForChartReportOrderCount = (
  allOutletsParam,
  allOutletsUserOrdersDone,
  chartPeriod,
  yAxisKey,
  xAxisKey,
  startDate = new Date(),
  endDate = new Date(),
) => {
  try {
    // endDate = moment(endDate).add(1, 'day');
    // endDate = moment(endDate).endOf('day');

    // console.log('data before chart');

    var parsedData = [];

    var yMinValue = 0;
    var yMaxValue = 0;

    var category = [];
    var datasetDict = {};

    var outletNameDict = {};
    // for (var i = 0; i < allOutlets.length; i++) {
    //   outletNameDict[allOutlets[i].uniqueId] = allOutlets[i].name;
    // }

    outletNameDict[ORDER_TYPE.DINEIN] = 'Dine in';
    outletNameDict[ORDER_TYPE.PICKUP] = 'Pick up';
    outletNameDict[ORDER_TYPE.DELIVERY] = 'Delivery';

    var allOutlets = [
      {
        label: 'Dine in',
        value: ORDER_TYPE.DINEIN,
      },
      {
        label: 'Pick up',
        value: ORDER_TYPE.PICKUP,
      },
      {
        label: 'Delivery',
        value: ORDER_TYPE.DELIVERY,
      },
    ];

    // var annotations = {
    //     "groups": [{
    //         "items": [{
    //             //Text annotation 1
    //             "type": "text",
    //             "text": 'Hello',
    //             //Define the attributes needed to create a text annotation
    //         }, {
    //             //Text annotation n
    //             "type": "text",
    //             "text": 'Hi',
    //             //Define the attributes needed to create a text annotation
    //         }]
    //     }]
    // };

    var annotations = {
      groups: [
        {
          items: [],
        },
      ],
    };
    var annotationsItems = [];

    if (chartPeriod === CHART_PERIOD.TODAY) {
      const allOutletsUserOrdersDoneToday = allOutletsUserOrdersDone.filter(
        (order) => {
          return moment().isSame(moment(order.createdAt), 'day');
        },
      );

      for (var i = 0; i < allOutlets.length; i++) {
        datasetDict[allOutlets[i].value] = {
          seriesname: allOutlets[i].label,
          data: Array.from(Array(24)).map(() => {
            return {
              value: 0,
              toolText: `0`,
            };
          }),
        };
      }

      for (var i = 0; i < 24; i++) {
        const currHour = moment().startOf('day').add(i, 'hour');

        // var record = {
        //     label: i.toString().padStart(2, '0'),
        //     value: 0,
        // };

        category.push({
          label: i.toString().padStart(2, '0'),
        });

        for (var j = 0; j < allOutletsUserOrdersDoneToday.length; j++) {
          if (
            compareOrderDateByDisplayType(
              currHour,
              allOutletsUserOrdersDoneToday[j].createdAt,
              DATE_COMPARE_TYPE.IS_SAME,
              'hour',
              reportDisplayType,
              outletShifts
            )
          ) {
            datasetDict[allOutletsUserOrdersDoneToday[j].orderType].data[
              i
            ].value += 1;

            datasetDict[allOutletsUserOrdersDoneToday[j].orderType].data[
              i
            ].toolText = `${datasetDict[allOutletsUserOrdersDoneToday[j].orderType].data[
              i
            ].value.toFixed(0)}`;
          }
        }

        // if (record.value > yMaxValue) {
        //     yMaxValue = record.value;
        // }

        // record.value = record.value.toFixed(0);

        // parsedData.push(record);
      }
    } else if (chartPeriod === CHART_PERIOD.THIS_WEEK) {
      // const allOutletsUserOrdersDoneToday = allOutletsUserOrdersDone.filter(order => {
      //     return moment().isSame(moment(order.createdAt), 'week');
      // });

      const startDate = moment(Date.now()).subtract(7, 'days').startOf('day');
      const endDate = moment().endOf('day');

      const allOutletsUserOrdersDoneToday = allOutletsUserOrdersDone.filter(
        (order) => {
          return (
            moment(startDate).isSameOrBefore(order.createdAt) &&
            moment(endDate).isAfter(order.createdAt)
          );
        },
      );

      // console.log('allOutletsUserOrdersDoneToday');
      // console.log(allOutletsUserOrdersDoneToday);

      for (var i = 0; i < allOutlets.length; i++) {
        datasetDict[allOutlets[i].value] = {
          seriesname: allOutlets[i].label,
          data: Array.from(Array(moment(endDate).diff(startDate, 'day'))).map(
            () => {
              return {
                value: 0,
                toolText: `0`,
              };
            },
          ),
        };
      }

      for (var i = 0; i < moment(endDate).diff(startDate, 'day'); i++) {
        // const currDay = moment().startOf('week').add(i, 'day');
        const currDay = moment(startDate).add(i, 'day');

        // var record = {
        //     label: moment().day(i).format('ddd'),
        //     value: 0,
        // };

        category.push({
          label: moment(startDate).add(i, 'day').format('ddd'),
        });

        for (var j = 0; j < allOutletsUserOrdersDoneToday.length; j++) {
          if (
            moment(currDay).isSame(
              moment(allOutletsUserOrdersDoneToday[j].createdAt),
              'day',
            )
          ) {
            datasetDict[allOutletsUserOrdersDoneToday[j].orderType].data[
              i
            ].value += 1;

            datasetDict[allOutletsUserOrdersDoneToday[j].orderType].data[
              i
            ].toolText = `${datasetDict[allOutletsUserOrdersDoneToday[j].orderType].data[
              i
            ].value.toFixed(0)}`;
          }
        }

        // if (record.value > yMaxValue) {
        //     yMaxValue = record.value;
        // }

        // record.value = record.value.toFixed(0);

        // parsedData.push(record);
      }
    } else if (chartPeriod === CHART_PERIOD.THIS_MONTH) {
      // const allOutletsUserOrdersDoneToday = allOutletsUserOrdersDone.filter(order => {
      //     return moment().isSame(moment(order.createdAt), 'month');
      // });

      // const startDate = moment().subtract(moment(startDate).daysInMonth(), 'day').startOf('day');
      // const endDate = moment().endOf('day');

      const allOutletsUserOrdersDoneToday = allOutletsUserOrdersDone.filter(
        (order) => {
          return (
            moment(startDate).isSameOrBefore(order.createdAt) &&
            moment(endDate).isAfter(order.createdAt)
          );
        },
      );

      if (xAxisKey === CHART_X_AXIS_TYPE.DAY) {
        for (var i = 0; i < allOutlets.length; i++) {
          datasetDict[allOutlets[i].value] = {
            seriesname: allOutlets[i].label,
            data: Array.from(Array(moment(startDate).daysInMonth())).map(() => {
              return {
                value: 0,
                toolText: `0`,
              };
            }),
          };
        }

        for (var i = 0; i < moment(startDate).daysInMonth(); i++) {
          // for (var i = 0; i < 31; i++) {
          // const currDay = moment().startOf('month').add(i, 'day');
          const currDay = moment(startDate).add(i, 'day');

          // var record = {
          //     label: i.toString().padStart(2, '0'),
          //     value: 0,
          // };

          category.push({
            label: (i + 1).toString().padStart(2, '0'),
          });

          for (var j = 0; j < allOutletsUserOrdersDoneToday.length; j++) {
            if (
              moment(currDay).isSame(
                moment(allOutletsUserOrdersDoneToday[j].createdAt),
                'day',
              )
            ) {
              datasetDict[allOutletsUserOrdersDoneToday[j].orderType].data[
                i
              ].value += 1;

              datasetDict[allOutletsUserOrdersDoneToday[j].orderType].data[
                i
              ].toolText = `${datasetDict[allOutletsUserOrdersDoneToday[j].orderType].data[
                i
              ].value.toFixed(0)}`;
            }
          }

          // if (record.value > yMaxValue) {
          //     yMaxValue = record.value;
          // }

          // record.value = record.value.toFixed(0);

          // parsedData.push(record);
        }
      }
      else if (xAxisKey === CHART_X_AXIS_TYPE.WEEK || xAxisKey === CHART_X_AXIS_TYPE.MONTH) {
        var currWeekCount = 0;
        var prevMonth = moment(startDate);

        for (var i = 0; i < allOutlets.length; i++) {
          datasetDict[allOutlets[i].value] = {
            seriesname: allOutlets[i].label,
            data: Array.from(Array(moment(endDate).diff(startDate, 'week'))).map(
              () => {
                return {
                  value: 0,
                  toolText: `0`,
                };
              },
            ),
          };
        }

        for (var i = 0; i < moment(endDate).diff(startDate, 'week'); i++) {
          const currWeek = moment(startDate).add(i, 'week');

          if (moment(currWeek).isSame(prevMonth, 'month')) {
            currWeekCount++;
          } else {
            prevMonth = moment(currWeek);

            currWeekCount = 0;
            currWeekCount++;
          }

          // var record = {
          //     label: (currWeekCount > 1 ? '' : moment(currWeek).format('MMM')) + ' W' + currWeekCount,
          //     value: 0,
          // };

          category.push({
            label:
              `${currWeekCount > 1 ? '' : moment(currWeek).format('MMM')
              } W${currWeekCount}`,
          });

          for (var j = 0; j < allOutletsUserOrdersDoneToday.length; j++) {
            if (
              moment(currWeek).isSame(
                moment(allOutletsUserOrdersDoneToday[j].createdAt),
                'week',
              )
            ) {
              datasetDict[allOutletsUserOrdersDoneToday[j].orderType].data[
                i
              ].value += 1;

              datasetDict[allOutletsUserOrdersDoneToday[j].orderType].data[
                i
              ].toolText = `${datasetDict[allOutletsUserOrdersDoneToday[j].orderType].data[
                i
              ].value.toFixed(0)}`;
            }
          }

          // if (record.value > yMaxValue) {
          //     yMaxValue = record.value;
          // }

          // record.value = record.value.toFixed(0);

          // parsedData.push(record);
        }
      }
    } else if (chartPeriod === CHART_PERIOD.THREE_MONTHS) {
      const startDate = moment().subtract(2, 'month').startOf('month');
      const endDate = moment().endOf('month');

      const allOutletsUserOrdersDoneToday = allOutletsUserOrdersDone.filter(
        (order) => {
          return (
            moment(startDate).isSameOrBefore(order.createdAt) &&
            moment(endDate).isAfter(order.createdAt)
          );
        },
      );

      if (xAxisKey === CHART_X_AXIS_TYPE.DAY) {
        for (var i = 0; i < allOutlets.length; i++) {
          datasetDict[allOutlets[i].value] = {
            seriesname: allOutlets[i].label,
            data: Array.from(Array(moment(endDate).diff(startDate, 'day'))).map(() => {
              return {
                value: 0,
                toolText: `0`,
              };
            }),
          };
        }

        for (var i = 0; i < moment(endDate).diff(startDate, 'day'); i++) {
          // for (var i = 0; i < 31; i++) {
          // const currDay = moment().startOf('month').add(i, 'day');
          const currDay = moment(startDate).add(i, 'day');

          // var record = {
          //     label: i.toString().padStart(2, '0'),
          //     value: 0,
          // };

          category.push({
            label: (i + 1).toString().padStart(2, '0'),
          });

          for (var j = 0; j < allOutletsUserOrdersDoneToday.length; j++) {
            if (
              moment(currDay).isSame(
                moment(allOutletsUserOrdersDoneToday[j].createdAt),
                'day',
              )
            ) {
              datasetDict[allOutletsUserOrdersDoneToday[j].orderType].data[
                i
              ].value += 1;

              datasetDict[allOutletsUserOrdersDoneToday[j].orderType].data[
                i
              ].toolText = `${datasetDict[allOutletsUserOrdersDoneToday[j].orderType].data[
                i
              ].value.toFixed(0)}`;
            }
          }

          // if (record.value > yMaxValue) {
          //     yMaxValue = record.value;
          // }

          // record.value = record.value.toFixed(0);

          // parsedData.push(record);
        }
      }
      else if (xAxisKey === CHART_X_AXIS_TYPE.WEEK) {
        var currWeekCount = 0;
        var prevMonth = moment(startDate);

        for (var i = 0; i < allOutlets.length; i++) {
          datasetDict[allOutlets[i].value] = {
            seriesname: allOutlets[i].label,
            data: Array.from(Array(moment(endDate).diff(startDate, 'week'))).map(
              () => {
                return {
                  value: 0,
                  toolText: `0`,
                };
              },
            ),
          };
        }

        for (var i = 0; i < moment(endDate).diff(startDate, 'week'); i++) {
          const currWeek = moment(startDate).add(i, 'week');

          if (moment(currWeek).isSame(prevMonth, 'month')) {
            currWeekCount++;
          } else {
            prevMonth = moment(currWeek);

            currWeekCount = 0;
            currWeekCount++;
          }

          // var record = {
          //     label: (currWeekCount > 1 ? '' : moment(currWeek).format('MMM')) + ' W' + currWeekCount,
          //     value: 0,
          // };

          category.push({
            label:
              `${currWeekCount > 1 ? '' : moment(currWeek).format('MMM')
              } W${currWeekCount}`,
          });

          for (var j = 0; j < allOutletsUserOrdersDoneToday.length; j++) {
            if (
              moment(currWeek).isSame(
                moment(allOutletsUserOrdersDoneToday[j].createdAt),
                'week',
              )
            ) {
              datasetDict[allOutletsUserOrdersDoneToday[j].orderType].data[
                i
              ].value += 1;

              datasetDict[allOutletsUserOrdersDoneToday[j].orderType].data[
                i
              ].toolText = `${datasetDict[allOutletsUserOrdersDoneToday[j].orderType].data[
                i
              ].value.toFixed(0)}`;
            }
          }

          // if (record.value > yMaxValue) {
          //     yMaxValue = record.value;
          // }

          // record.value = record.value.toFixed(0);

          // parsedData.push(record);
        }
      }
      else if (xAxisKey === CHART_X_AXIS_TYPE.MONTH) {
        for (var i = 0; i < allOutlets.length; i++) {
          datasetDict[allOutlets[i].value] = {
            seriesname: allOutlets[i].label,
            data: Array.from(Array(3)).map(() => {
              return {
                value: 0,
                toolText: `0`,
              };
            }),
          };
        }

        var currMonth = moment(startDate).startOf('month');

        for (var i = 0; i < 3; i++) {
          // const currMonth = moment().startOf('year').add(i, 'month');
          var currMonth = moment(currMonth).add(i, 'month');

          // var record = {
          //     label: moment(currMonth).format('MMM'),
          //     value: 0,
          // };

          category.push({
            label: moment(currMonth).format('MMM'),
          });

          for (var j = 0; j < allOutletsUserOrdersDoneToday.length; j++) {
            if (
              moment(currMonth).isSame(
                moment(allOutletsUserOrdersDoneToday[j].createdAt),
                'month',
              )
            ) {
              datasetDict[allOutletsUserOrdersDoneToday[j].orderType].data[
                i
              ].value += 1;

              datasetDict[allOutletsUserOrdersDoneToday[j].orderType].data[
                i
              ].toolText = `${datasetDict[allOutletsUserOrdersDoneToday[j].orderType].data[
                i
              ].value.toFixed(0)}`;
            }
          }

          // if (record.value > yMaxValue) {
          //     yMaxValue = record.value;
          // }

          // record.value = record.value.toFixed(0);

          // parsedData.push(record);
        }
      }
    } else if (chartPeriod === CHART_PERIOD.SIX_MONTHS) {
      const startDate = moment().subtract(5, 'month').startOf('month');
      const endDate = moment().endOf('month');

      const allOutletsUserOrdersDoneToday = allOutletsUserOrdersDone.filter(
        (order) => {
          return (
            moment(startDate).isSameOrBefore(order.createdAt) &&
            moment(endDate).isAfter(order.createdAt)
          );
        },
      );

      if (xAxisKey === CHART_X_AXIS_TYPE.DAY || xAxisKey === CHART_X_AXIS_TYPE.WEEK) {
        for (var i = 0; i < allOutlets.length; i++) {
          datasetDict[allOutlets[i].value] = {
            seriesname: allOutlets[i].label,
            data: Array.from(Array(moment(endDate).diff(startDate, 'week'))).map(
              () => {
                return {
                  value: 0,
                  toolText: `0`,
                };
              },
            ),
          };
        }

        for (var i = 0; i < moment(endDate).diff(startDate, 'week'); i++) {
          const currWeek = moment(startDate).add(i, 'week');

          if (moment(currWeek).isSame(prevMonth, 'month')) {
            currWeekCount++;
          } else {
            prevMonth = moment(currWeek);

            currWeekCount = 0;
            currWeekCount++;
          }

          // var record = {
          //     label: 'W' + currWeekCount + ' ' + (currWeekCount > 1 ? '' : moment(currWeek).format('MMM')),
          //     value: 0,
          // };

          category.push({
            label:
              `W${currWeekCount
              } ${currWeekCount > 1 ? '' : moment(currWeek).format('MMM')}`,
          });

          for (var j = 0; j < allOutletsUserOrdersDoneToday.length; j++) {
            if (
              moment(currWeek).isSame(
                moment(allOutletsUserOrdersDoneToday[j].createdAt),
                'week',
              )
            ) {
              datasetDict[allOutletsUserOrdersDoneToday[j].orderType].data[
                i
              ].value += 1;

              datasetDict[allOutletsUserOrdersDoneToday[j].orderType].data[
                i
              ].toolText = `${datasetDict[allOutletsUserOrdersDoneToday[j].orderType].data[
                i
              ].value.toFixed(0)}`;
            }
          }

          // if (record.value > yMaxValue) {
          //     yMaxValue = record.value;
          // }

          // record.value = record.value.toFixed(0);

          // parsedData.push(record);
        }
      }
      else if (xAxisKey === CHART_X_AXIS_TYPE.MONTH) {
        for (var i = 0; i < allOutlets.length; i++) {
          datasetDict[allOutlets[i].value] = {
            seriesname: allOutlets[i].label,
            data: Array.from(Array(6)).map(() => {
              return {
                value: 0,
                toolText: `0`,
              };
            }),
          };
        }

        var currMonth = moment(startDate).startOf('month');

        for (var i = 0; i < 6; i++) {
          // const currMonth = moment().startOf('year').add(i, 'month');
          var currMonth = moment(currMonth).add(i, 'month');

          // var record = {
          //     label: moment(currMonth).format('MMM'),
          //     value: 0,
          // };

          category.push({
            label: moment(currMonth).format('MMM'),
          });

          for (var j = 0; j < allOutletsUserOrdersDoneToday.length; j++) {
            if (
              moment(currMonth).isSame(
                moment(allOutletsUserOrdersDoneToday[j].createdAt),
                'month',
              )
            ) {
              datasetDict[allOutletsUserOrdersDoneToday[j].orderType].data[
                i
              ].value += 1;

              datasetDict[allOutletsUserOrdersDoneToday[j].orderType].data[
                i
              ].toolText = `${datasetDict[allOutletsUserOrdersDoneToday[j].orderType].data[
                i
              ].value.toFixed(0)}`;
            }
          }

          // if (record.value > yMaxValue) {
          //     yMaxValue = record.value;
          // }

          // record.value = record.value.toFixed(0);

          // parsedData.push(record);
        }
      }
    } else if (chartPeriod === CHART_PERIOD.THIS_YEAR) {
      const allOutletsUserOrdersDoneToday = allOutletsUserOrdersDone.filter(
        (order) => {
          return moment().isSame(moment(order.createdAt), 'year');
        },
      );

      if (xAxisKey === CHART_X_AXIS_TYPE.DAY || xAxisKey === CHART_X_AXIS_TYPE.WEEK) {
        for (var i = 0; i < allOutlets.length; i++) {
          datasetDict[allOutlets[i].value] = {
            seriesname: allOutlets[i].label,
            data: Array.from(Array(moment(endDate).diff(startDate, 'week'))).map(
              () => {
                return {
                  value: 0,
                  toolText: `0`,
                };
              },
            ),
          };
        }

        for (var i = 0; i < moment(endDate).diff(startDate, 'week'); i++) {
          const currWeek = moment(startDate).add(i, 'week');

          if (moment(currWeek).isSame(prevMonth, 'month')) {
            currWeekCount++;
          } else {
            prevMonth = moment(currWeek);

            currWeekCount = 0;
            currWeekCount++;
          }

          // var record = {
          //     label: 'W' + currWeekCount + ' ' + (currWeekCount > 1 ? '' : moment(currWeek).format('MMM')),
          //     value: 0,
          // };

          category.push({
            label:
              `W${currWeekCount
              } ${currWeekCount > 2 ? '' : moment(currWeek).format('MMM')}`,
          });

          for (var j = 0; j < allOutletsUserOrdersDoneToday.length; j++) {
            if (
              moment(currWeek).isSame(
                moment(allOutletsUserOrdersDoneToday[j].createdAt),
                'week',
              )
            ) {
              datasetDict[allOutletsUserOrdersDoneToday[j].orderType].data[
                i
              ].value += 1;

              datasetDict[allOutletsUserOrdersDoneToday[j].orderType].data[
                i
              ].toolText = `${datasetDict[allOutletsUserOrdersDoneToday[j].orderType].data[
                i
              ].value.toFixed(0)}`;
            }
          }

          // if (record.value > yMaxValue) {
          //     yMaxValue = record.value;
          // }

          // record.value = record.value.toFixed(0);

          // parsedData.push(record);
        }
      }
      else if (xAxisKey === CHART_X_AXIS_TYPE.MONTH) {
        for (var i = 0; i < allOutlets.length; i++) {
          datasetDict[allOutlets[i].value] = {
            seriesname: allOutlets[i].label,
            data: Array.from(Array(12)).map(() => {
              return {
                value: 0,
                toolText: `0`,
              };
            }),
          };
        }

        for (var i = 0; i < 12; i++) {
          const currMonth = moment().startOf('year').add(i, 'month');

          // var record = {
          //     label: moment(currMonth).format('MMM'),
          //     value: 0,
          // };

          category.push({
            label: moment(currMonth).format('MMM'),
          });

          for (var j = 0; j < allOutletsUserOrdersDoneToday.length; j++) {
            if (
              moment(currMonth).isSame(
                moment(allOutletsUserOrdersDoneToday[j].createdAt),
                'month',
              )
            ) {
              datasetDict[allOutletsUserOrdersDoneToday[j].orderType].data[
                i
              ].value += 1;

              datasetDict[allOutletsUserOrdersDoneToday[j].orderType].data[
                i
              ].toolText = `${datasetDict[allOutletsUserOrdersDoneToday[j].orderType].data[
                i
              ].value.toFixed(0)}`;
            }
          }

          // if (record.value > yMaxValue) {
          //     yMaxValue = record.value;
          // }

          // record.value = record.value.toFixed(0);

          // parsedData.push(record);
        }
      }
    } else if (chartPeriod === CHART_PERIOD.YTD) {
      const startDate = moment().subtract(11, 'month').startOf('month');
      const endDate = moment().endOf('month');

      const allOutletsUserOrdersDoneToday = allOutletsUserOrdersDone.filter(
        (order) => {
          return (
            moment(startDate).isSameOrBefore(order.createdAt) &&
            moment(endDate).isAfter(order.createdAt)
          );
        },
      );

      if (xAxisKey === CHART_X_AXIS_TYPE.DAY || xAxisKey === CHART_X_AXIS_TYPE.WEEK) {
        for (var i = 0; i < allOutlets.length; i++) {
          datasetDict[allOutlets[i].value] = {
            seriesname: allOutlets[i].label,
            data: Array.from(Array(moment(endDate).diff(startDate, 'week'))).map(
              () => {
                return {
                  value: 0,
                  toolText: `0`,
                };
              },
            ),
          };
        }

        for (var i = 0; i < moment(endDate).diff(startDate, 'week'); i++) {
          const currWeek = moment(startDate).add(i, 'week');

          if (moment(currWeek).isSame(prevMonth, 'month')) {
            currWeekCount++;
          } else {
            prevMonth = moment(currWeek);

            currWeekCount = 0;
            currWeekCount++;
          }

          // var record = {
          //     label: 'W' + currWeekCount + ' ' + (currWeekCount > 1 ? '' : moment(currWeek).format('MMM')),
          //     value: 0,
          // };

          category.push({
            label:
              `W${currWeekCount
              } ${currWeekCount > 2 ? '' : moment(currWeek).format('MMM')}`,
          });

          for (var j = 0; j < allOutletsUserOrdersDoneToday.length; j++) {
            if (
              moment(currWeek).isSame(
                moment(allOutletsUserOrdersDoneToday[j].createdAt),
                'week',
              )
            ) {
              datasetDict[allOutletsUserOrdersDoneToday[j].orderType].data[
                i
              ].value += 1;

              datasetDict[allOutletsUserOrdersDoneToday[j].orderType].data[
                i
              ].toolText = `${datasetDict[allOutletsUserOrdersDoneToday[j].orderType].data[
                i
              ].value.toFixed(0)}`;
            }
          }

          // if (record.value > yMaxValue) {
          //     yMaxValue = record.value;
          // }

          // record.value = record.value.toFixed(0);

          // parsedData.push(record);
        }
      }
      else if (xAxisKey === CHART_X_AXIS_TYPE.MONTH) {
        for (var i = 0; i < allOutlets.length; i++) {
          datasetDict[allOutlets[i].value] = {
            seriesname: allOutlets[i].label,
            data: Array.from(Array(12)).map(() => {
              return {
                value: 0,
                toolText: `0`,
              };
            }),
          };
        }

        for (var i = 0; i < 12; i++) {
          const currMonth = moment(startDate).add(i, 'month');

          // var record = {
          //     label: moment(currMonth).format('YY\' MMM'),
          //     value: 0,
          // };

          category.push({
            label: moment(currMonth).format("YY' MMM"),
          });

          for (var j = 0; j < allOutletsUserOrdersDoneToday.length; j++) {
            if (
              moment(currMonth).isSame(
                moment(allOutletsUserOrdersDoneToday[j].createdAt),
                'month',
              )
            ) {
              datasetDict[allOutletsUserOrdersDoneToday[j].orderType].data[
                i
              ].value += 1;

              datasetDict[allOutletsUserOrdersDoneToday[j].orderType].data[
                i
              ].toolText = `${datasetDict[allOutletsUserOrdersDoneToday[j].orderType].data[
                i
              ].value.toFixed(0)}`;
            }
          }

          // if (record.value > yMaxValue) {
          //     yMaxValue = record.value;
          // }

          // record.value = record.value.toFixed(0);

          // parsedData.push(record);
        }
      }
    } else if (chartPeriod === CHART_PERIOD.NONE) {
      const allOutletsUserOrdersDoneTodayRange =
        allOutletsUserOrdersDone.filter((order) => {
          return (
            moment(startDate).isSameOrBefore(order.createdAt) &&
            moment(endDate).isAfter(order.createdAt)
          );
        });

      if (
        moment(endDate).diff(startDate, 'day') <=
        moment(startDate).daysInMonth()
      ) {
        endDate = moment(endDate).add(1, 'day');

        const diffDays = moment(endDate).diff(startDate, 'day');

        var prevDate = moment(startDate).add(-1, 'month');

        for (var i = 0; i < allOutlets.length; i++) {
          datasetDict[allOutlets[i].value] = {
            seriesname: allOutlets[i].label,
            data: Array.from(Array(diffDays)).map(() => {
              return {
                value: 0,
                toolText: `0`,
              };
            }),
          };
        }

        for (var i = 0; i < diffDays; i++) {
          const currDate = moment(startDate).add(i, 'day');

          category.push({
            //'label': moment(currDate).format('DD MMM'),
            label: !moment(currDate).isSame(prevDate, 'month')
              ? moment(currDate).format('DD MMM')
              : moment(currDate).format('DD'),
            //'label': (i > 1 ? moment(currDate).format('DD') : moment(currDate).format('DD MMM'))
          });

          prevDate = currDate;

          for (var j = 0; j < allOutletsUserOrdersDoneTodayRange.length; j++) {
            if (
              // allOutletsUserOrdersDoneTodayRange[j].outletId === allOutlets[i].uniqueId &&
              moment(currDate).isSame(
                moment(allOutletsUserOrdersDoneTodayRange[j].createdAt),
                'day',
              )
            ) {
              datasetDict[allOutletsUserOrdersDoneTodayRange[j].orderType].data[
                i
              ].value += 1;

              datasetDict[allOutletsUserOrdersDoneTodayRange[j].orderType].data[
                i
              ].toolText = `${datasetDict[allOutletsUserOrdersDoneTodayRange[j].orderType].data[
                i
              ].value.toFixed(0)}`;
            }
          }

          // if (record.value > yMaxValue) {
          //     yMaxValue = record.value;
          // }

          // record.value = record.value.toFixed(0);

          // parsedData.push(record);
        }
      } else if (moment(endDate).diff(startDate, 'month') <= 6) {
        var prevMonth = moment(startDate);

        const diffWeeks = moment(endDate).diff(startDate, 'week');

        for (var i = 0; i < allOutlets.length; i++) {
          datasetDict[allOutlets[i].value] = {
            seriesname: allOutlets[i].label,
            data: Array.from(Array(diffWeeks)).map(() => {
              return {
                value: 0,
                toolText: `0`,
              };
            }),
          };
        }

        for (var i = 0; i < diffWeeks; i++) {
          const currDate = moment(startDate).add(i, 'week');

          if (moment(currDate).isSame(prevMonth, 'month')) {
            currWeekCount++;
          } else {
            prevMonth = moment(currDate);

            currWeekCount = 0;
            currWeekCount++;
          }

          category.push({
            // label: moment(currDate).format('MMM') + ' W' + currWeekCount,
            label:
              `${moment(currDate).format('MMM')
              } W${countWeekdayOccurrencesInMonth(currDate)}`,
          });

          for (var j = 0; j < allOutletsUserOrdersDoneTodayRange.length; j++) {
            if (
              // allOutletsUserOrdersDoneTodayRange[j].outletId === allOutlets[i].uniqueId &&
              moment(currDate).isSame(
                moment(allOutletsUserOrdersDoneTodayRange[j].createdAt),
                'week',
              )
            ) {
              datasetDict[allOutletsUserOrdersDoneTodayRange[j].orderType].data[
                i
              ].value += 1;

              datasetDict[allOutletsUserOrdersDoneTodayRange[j].orderType].data[
                i
              ].toolText = `${datasetDict[allOutletsUserOrdersDoneTodayRange[j].orderType].data[
                i
              ].value.toFixed(0)}`;
            }
          }

          // if (record.value > yMaxValue) {
          //     yMaxValue = record.value;
          // }

          // record.value = record.value.toFixed(0);

          // parsedData.push(record);
        }
      }
      // if (moment(endDate).diff(startDate, 'month') <= 6) // for now no need conditions first, for more than 6 months interval
      else {
        const diffYears = moment(endDate).diff(startDate, 'month');

        for (var i = 0; i < allOutlets.length; i++) {
          datasetDict[allOutlets[i].value] = {
            seriesname: allOutlets[i].label,
            data: Array.from(Array(diffYears)).map(() => {
              return {
                value: 0,
                toolText: `0`,
              };
            }),
          };
        }

        for (var i = 0; i < diffYears; i++) {
          const currDate = moment(startDate).add(i, 'month');

          category.push({
            label: moment(currDate).format("YY' MMM"),
          });

          for (var j = 0; j < allOutletsUserOrdersDoneTodayRange.length; j++) {
            if (
              // allOutletsUserOrdersDoneTodayRange[j].outletId === allOutlets[i].uniqueId &&
              moment(currDate).isSame(
                moment(allOutletsUserOrdersDoneTodayRange[j].createdAt),
                'month',
              )
            ) {
              datasetDict[allOutletsUserOrdersDoneTodayRange[j].orderType].data[
                i
              ].value += 1;

              datasetDict[allOutletsUserOrdersDoneTodayRange[j].orderType].data[
                i
              ].toolText = `${datasetDict[allOutletsUserOrdersDoneTodayRange[j].orderType].data[
                i
              ].value.toFixed(0)}`;
            }
          }

          // if (record.value > yMaxValue) {
          //     yMaxValue = record.value;
          // }

          // record.value = record.value.toFixed(0);

          // parsedData.push(record);
        }
      }
    }

    // const allOutletsUserOrdersDoneToday = allOutletsUserOrdersDone.filter(order => {
    //     return moment().isSame(moment(order.createdAt), 'year');
    // });

    // for (var i = 0; i < 12; i++) {
    //     var record = {
    //         label: MONTH_TO_CHART_LABEL[i],
    //         value: 0,
    //     };

    //     const currMonth = moment().startOf('year').add(i, 'month');

    //     for (var j = 0; j < allOutletsUserOrdersDoneToday.length; j++) {
    //         // if (allOutletsUserOrdersDoneToday[j].outletId === allOutlets[i].uniqueId) {
    //         //     record.value += allOutletsUserOrdersDoneToday[j].finalPrice;

    //         //     // if (allOutletsUserOrdersDoneToday[j].finalPrice > yMaxValue) {
    //         //     //     yMaxValue += allOutletsUserOrdersDoneToday[j].finalPrice;
    //         //     // }
    //         // }

    //         if (moment(currMonth).isSame(moment(allOutletsUserOrdersDoneToday[j].createdAt), 'month')) {
    //             record.value += allOutletsUserOrdersDoneToday[j].finalPrice;
    //         }
    //     }

    //     // yMaxValue += record.value;
    //     if (record.value > yMaxValue) {
    //         yMaxValue = record.value;
    //     }

    //     record.value = record.value.toFixed(0);

    //     parsedData.push(record);
    // }

    for (var j = 0; j < allOutlets.length; j++) {
      var outletId = allOutlets[j].value;

      var currTotal = 0;

      for (var k = 0; k < datasetDict[outletId].data.length; k++) {
        // currTotal += datasetDict[outletId].data[k].value;

        currTotal = datasetDict[outletId].data[k].value;

        if (currTotal > yMaxValue) {
          yMaxValue = currTotal;
        }
      }

      // if (currTotal > yMaxValue) {
      //     yMaxValue = currTotal;
      // }
    }

    var chartLegend = [];

    const dataset = Object.entries(datasetDict).map(([key, value], index) => {
      const mappedIndex =
        index % CHART_COLORS[CHART_TYPE.REPORT_ORDER_COUNT].length; // might got more than 10 preset colors, set to infinite list instead

      chartLegend.push({
        itemName: value.seriesname,
        itemSku: value.seriesname,
        chartColor:
          CHART_COLORS[CHART_TYPE.REPORT_ORDER_COUNT][mappedIndex],
      });

      return {
        ...value,
        color: CHART_COLORS[CHART_TYPE.REPORT_ORDER_COUNT][mappedIndex],
      };
    });

    var output = {};

    output.type = CHART_DATA[CHART_TYPE.REPORT_ORDER_COUNT].type;
    output.dataFormat =
      CHART_DATA[CHART_TYPE.REPORT_ORDER_COUNT].dataFormat;
    output.dataSource = {
      chart: CHART_DATA[CHART_TYPE.REPORT_ORDER_COUNT].chart,

      categories: [
        {
          category,
        },
      ],
      dataset,

      annotations,

      // data: parsedData,
    };

    // // console.log('parsedData');
    // // console.log(parsedData);
    // console.log(yMaxValue);

    if (output.type !== 'HLinearGauge') {
      // const yAxisStep = Math.ceil((yMaxValue / 10) / 10) * 10;

      // const yMaxValueParsed = Math.ceil(yMaxValue / yAxisStep);
      // const yAxisMaxValue = yMaxValueParsed === 1 ? 2 : yMaxValueParsed;
      // const numDivLines = yMaxValueParsed === 1 ? 1 : yMaxValueParsed - 1;

      const baseNumberLength = yMaxValue.toFixed(0).length - 1;
      const baseNumber = parseInt(
        `1${Array(baseNumberLength)
          .fill()
          .map((num) => 0)
          .join('')}`,
      );
      var maxNumber = baseNumber;

      if (yMaxValue > baseNumber * 5) {
        maxNumber = baseNumber * 10;
      } else if (yMaxValue > baseNumber * 3) {
        maxNumber = baseNumber * 5;
      } else if (yMaxValue > baseNumber * 2) {
        maxNumber = baseNumber * 3;
      } else {
        maxNumber = baseNumber * 2;
      }

      maxNumber = getSuitableYAxisMaxValue(maxNumber);

      maxNumber = Math.max(maxNumber, 10);

      output.dataSource.chart = {
        ...output.dataSource.chart,
        labelDisplay: 'WRAP',
        // labelDisplay: 'STAGGER',
        // labelStep: 2,
        bgColor: '#ffffff',
        canvasBgColor: '#ffffff',
        bgAlpha: 100,

        useEllipsesWhenOverflow: 1,

        // showYAxisValues: 0,
        // yaxismaxvalue: yAxisMaxValue * yAxisStep,
        yaxismaxvalue: maxNumber,
        yaxisminvalue: yMinValue,
        adjustDiv: 0,
        // numDivLines: numDivLines,
        numDivLines: 9,

        // yaxisminvalue: 0,
        formatNumberScale: 0,
        decimals: 0,
      };

      // // console.log('/////////////////////////////////////////////////')

      // // console.log({
      //     yMaxValue: yMaxValue,
      //     yMaxValueParsed: yMaxValueParsed,
      //     yaxismaxvalue: yAxisMaxValue * yAxisStep,
      //     yaxisminvalue: yMinValue,
      //     adjustDiv: 0,
      //     numDivLines: numDivLines,
      // });

      // // console.log('/////////////////////////////////////////////////')

      return {
        chartData: output,
        chartLegend,
      };
    }

    // if (output.type === 'column2D') {
    //     if (type === CHART_TYPE.S_MILK) {
    //         // for (let i = 0; i < output.dataSource.data.length; i++) {
    //         //     if (output.dataSource.data[i].value.length > 0) {
    //         //         yMaxValue = parseInt(output.dataSource.data[i].value) > yMaxValue ? parseInt(output.dataSource.data[i].value) : yMaxValue;

    //         //     }
    //         // }

    //         // const yMaxValueParsed = Math.ceil(yMaxValue / 5);
    //         // const yAxisMaxValue = yMaxValueParsed === 1 ? 2 : yMaxValueParsed;
    //         // const numDivLines = yMaxValueParsed === 1 ? 1 : yMaxValueParsed - 1;

    //         // output.dataSource.chart = {
    //         //     ...output.dataSource.chart,
    //         //     adjustDiv: 0,
    //         //     numDivLines: numDivLines,
    //         //     yaxismaxvalue: yAxisMaxValue * 5,
    //         //     yaxisminvalue: yMinValue,
    //         //     formatNumberScale: 0,
    //         // };
    //     }
    //     else if (type === CHART_TYPE.S_SHIT_PEE) {
    //         for (let i = 0; i < output.dataSource.data.length; i++) {
    //             if (output.dataSource.data[i].value.length > 0) {
    //                 yMaxValue = parseInt(output.dataSource.data[i].value) > yMaxValue ? parseInt(output.dataSource.data[i].value) : yMaxValue;
    //             }
    //         }

    //         output.dataSource.chart = {
    //             ...output.dataSource.chart,
    //             adjustDiv: 0,
    //             numDivLines: yMaxValue,
    //             yaxismaxvalue: yMaxValue + 1,
    //             yaxisminvalue: yMinValue,
    //             formatNumberScale: 0,
    //         };
    //     }
    // }

    // console.log('sales line chart output');
    // console.log(output);

    return output;
  } catch (ex) {
    console.log('chart error');
    console.log(ex);

    return false;
  }
};

export const getDataForChartReportOrderCountMultiColumn = (
  allOutletsParam,
  allOutletsUserOrdersDone,
  chartPeriod,
  yAxisKey,
  xAxisKey,
  startDate = new Date(),
  endDate = new Date(),

  reportDisplayType = REPORT_DISPLAY_TYPE.DAY,
  outletShifts = [],
) => {
  try {
    // endDate = moment(endDate).add(1, 'day');
    // endDate = moment(endDate).endOf('day');

    // console.log('data before chart');

    var parsedData = [];

    var yMinValue = 0;
    var yMaxValue = 0;

    var category = [];
    var datasetDict = {};

    var outletNameDict = {};
    // for (var i = 0; i < allOutlets.length; i++) {
    //   outletNameDict[allOutlets[i].uniqueId] = allOutlets[i].name;
    // }

    outletNameDict[ORDER_TYPE.DINEIN] = 'Dine in';
    outletNameDict[ORDER_TYPE.PICKUP] = 'Pick up';
    outletNameDict[ORDER_TYPE.DELIVERY] = 'Delivery';

    var allOutlets = [
      {
        label: 'Dine in (POS)',
        value: `${ORDER_TYPE.DINEIN}-POS`,
        types: [APP_TYPE.MERCHANT, APP_TYPE.WAITER],
      },
      {
        label: 'Pick up (POS)',
        value: `${ORDER_TYPE.PICKUP}-POS`,
        types: [APP_TYPE.MERCHANT, APP_TYPE.WAITER],
      },
      {
        label: 'Delivery (POS)',
        value: `${ORDER_TYPE.DELIVERY}-POS`,
        types: [APP_TYPE.MERCHANT, APP_TYPE.WAITER],
      },

      {
        label: 'Dine in (QR)',
        value: `${ORDER_TYPE.DINEIN}-QR`,
        types: [APP_TYPE.WEB_ORDER],
      },
      {
        label: 'Pick up (QR)',
        value: `${ORDER_TYPE.PICKUP}-QR`,
        types: [APP_TYPE.WEB_ORDER],
      },
      {
        label: 'Delivery (QR)',
        value: `${ORDER_TYPE.DELIVERY}-QR`,
        types: [APP_TYPE.WEB_ORDER],
      },
    ];

    var appTypeToValueDict = {
      [APP_TYPE.MERCHANT]: '-POS',
      [APP_TYPE.WAITER]: '-POS',

      [APP_TYPE.WEB_ORDER]: '-QR',
      [APP_TYPE.USER]: '-QR',

      [undefined]: '-POS',
    };

    // var annotations = {
    //     "groups": [{
    //         "items": [{
    //             //Text annotation 1
    //             "type": "text",
    //             "text": 'Hello',
    //             //Define the attributes needed to create a text annotation
    //         }, {
    //             //Text annotation n
    //             "type": "text",
    //             "text": 'Hi',
    //             //Define the attributes needed to create a text annotation
    //         }]
    //     }]
    // };

    var annotations = {
      groups: [
        {
          items: [],
        },
      ],
    };
    var annotationsItems = [];

    if (chartPeriod === CHART_PERIOD.TODAY) {
      const allOutletsUserOrdersDoneToday = allOutletsUserOrdersDone.filter(
        (order) => {
          // return moment().isSame(moment(order.createdAt), 'day');
          return compareOrderDateByDisplayType(
            moment().valueOf(),
            order.createdAt,
            DATE_COMPARE_TYPE.IS_SAME,
            'day',
            reportDisplayType,
            outletShifts
          );
        },
      );

      for (var i = 0; i < allOutlets.length; i++) {
        datasetDict[allOutlets[i].value] = {
          seriesname: allOutlets[i].label,
          data: Array.from(Array(24)).map(() => {
            return {
              value: 0,
              toolText: `0`,
            };
          }),
        };
      }

      for (var i = 0; i < 24; i++) {
        const currHour = moment().startOf('day').add(i, 'hour');

        // var record = {
        //     label: i.toString().padStart(2, '0'),
        //     value: 0,
        // };

        category.push({
          label: i.toString().padStart(2, '0'),
        });

        for (var j = 0; j < allOutletsUserOrdersDoneToday.length; j++) {
          if (
            // moment(currHour).isSame(
            //   moment(allOutletsUserOrdersDoneToday[j].createdAt),
            //   'hour',
            // )
            compareOrderDateByDisplayType(
              currHour,
              allOutletsUserOrdersDoneToday[j].createdAt,
              DATE_COMPARE_TYPE.IS_SAME,
              'hour',
              reportDisplayType,
              outletShifts
            )
          ) {
            var typeParsed = `${allOutletsUserOrdersDoneToday[j].orderType ? allOutletsUserOrdersDoneToday[j].orderType : ORDER_TYPE.DINEIN}${appTypeToValueDict[allOutletsUserOrdersDoneToday[j].appType ? allOutletsUserOrdersDoneToday[j].appType : APP_TYPE.MERCHANT]}`;

            datasetDict[typeParsed].data[
              i
            ].value += 1;

            datasetDict[typeParsed].data[
              i
            ].toolText = `${datasetDict[typeParsed].data[
              i
            ].value.toFixed(0)}`;
          }
        }

        // if (record.value > yMaxValue) {
        //     yMaxValue = record.value;
        // }

        // record.value = record.value.toFixed(0);

        // parsedData.push(record);
      }
    } else if (chartPeriod === CHART_PERIOD.THIS_WEEK) {
      // const allOutletsUserOrdersDoneToday = allOutletsUserOrdersDone.filter(order => {
      //     return moment().isSame(moment(order.createdAt), 'week');
      // });

      const startDate = moment(Date.now()).subtract(7, 'days').startOf('day');
      const endDate = moment().endOf('day');

      const allOutletsUserOrdersDoneToday = allOutletsUserOrdersDone.filter(
        (order) => {
          return (
            moment(startDate).isSameOrBefore(order.createdAt) &&
            moment(endDate).isAfter(order.createdAt)
          );
        },
      );

      // console.log('allOutletsUserOrdersDoneToday');
      // console.log(allOutletsUserOrdersDoneToday);

      for (var i = 0; i < allOutlets.length; i++) {
        datasetDict[allOutlets[i].value] = {
          seriesname: allOutlets[i].label,
          data: Array.from(Array(moment(endDate).diff(startDate, 'day'))).map(
            () => {
              return {
                value: 0,
                toolText: `0`,
              };
            },
          ),
        };
      }

      for (var i = 0; i < moment(endDate).diff(startDate, 'day'); i++) {
        // const currDay = moment().startOf('week').add(i, 'day');
        const currDay = moment(startDate).add(i, 'day');

        // var record = {
        //     label: moment().day(i).format('ddd'),
        //     value: 0,
        // };

        category.push({
          label: moment(startDate).add(i, 'day').format('ddd'),
        });

        for (var j = 0; j < allOutletsUserOrdersDoneToday.length; j++) {
          if (
            // moment(currDay).isSame(
            //   moment(allOutletsUserOrdersDoneToday[j].createdAt),
            //   'day',
            // )
            compareOrderDateByDisplayType(
              currDay,
              allOutletsUserOrdersDoneToday[j].createdAt,
              DATE_COMPARE_TYPE.IS_SAME,
              'day',
              reportDisplayType,
              outletShifts
            )
          ) {
            // var typeParsed = `${allOutletsUserOrdersDoneToday[j].orderType}${appTypeToValueDict[allOutletsUserOrdersDoneToday[j].appType]}`;
            var typeParsed = `${allOutletsUserOrdersDoneToday[j].orderType ? allOutletsUserOrdersDoneToday[j].orderType : ORDER_TYPE.DINEIN}${appTypeToValueDict[allOutletsUserOrdersDoneToday[j].appType ? allOutletsUserOrdersDoneToday[j].appType : APP_TYPE.MERCHANT]}`;

            datasetDict[typeParsed].data[
              i
            ].value += 1;

            datasetDict[typeParsed].data[
              i
            ].toolText = `${datasetDict[typeParsed].data[
              i
            ].value.toFixed(0)}`;
          }
        }

        // if (record.value > yMaxValue) {
        //     yMaxValue = record.value;
        // }

        // record.value = record.value.toFixed(0);

        // parsedData.push(record);
      }
    } else if (chartPeriod === CHART_PERIOD.THIS_MONTH) {
      // const allOutletsUserOrdersDoneToday = allOutletsUserOrdersDone.filter(order => {
      //     return moment().isSame(moment(order.createdAt), 'month');
      // });

      // const startDate = moment().subtract(moment(startDate).daysInMonth(), 'day').startOf('day');
      // const endDate = moment().endOf('day');

      const allOutletsUserOrdersDoneToday = allOutletsUserOrdersDone.filter(
        (order) => {
          return (
            moment(startDate).isSameOrBefore(order.createdAt) &&
            moment(endDate).isAfter(order.createdAt)
          );
        },
      );

      if (xAxisKey === CHART_X_AXIS_TYPE.DAY) {
        for (var i = 0; i < allOutlets.length; i++) {
          datasetDict[allOutlets[i].value] = {
            seriesname: allOutlets[i].label,
            data: Array.from(Array(moment(startDate).daysInMonth())).map(() => {
              return {
                value: 0,
                toolText: `0`,
              };
            }),
          };
        }

        for (var i = 0; i < moment(startDate).daysInMonth(); i++) {
          // for (var i = 0; i < 31; i++) {
          // const currDay = moment().startOf('month').add(i, 'day');
          const currDay = moment(startDate).add(i, 'day');

          // var record = {
          //     label: i.toString().padStart(2, '0'),
          //     value: 0,
          // };

          category.push({
            label: (i + 1).toString().padStart(2, '0'),
          });

          for (var j = 0; j < allOutletsUserOrdersDoneToday.length; j++) {
            if (
              // moment(currDay).isSame(
              //   moment(allOutletsUserOrdersDoneToday[j].createdAt),
              //   'day',
              // )
              compareOrderDateByDisplayType(
                currDay,
                allOutletsUserOrdersDoneToday[j].createdAt,
                DATE_COMPARE_TYPE.IS_SAME,
                'day',
                reportDisplayType,
                outletShifts
              )
            ) {
              // var typeParsed = `${allOutletsUserOrdersDoneToday[j].orderType}${appTypeToValueDict[allOutletsUserOrdersDoneToday[j].appType]}`;
              var typeParsed = `${allOutletsUserOrdersDoneToday[j].orderType ? allOutletsUserOrdersDoneToday[j].orderType : ORDER_TYPE.DINEIN}${appTypeToValueDict[allOutletsUserOrdersDoneToday[j].appType ? allOutletsUserOrdersDoneToday[j].appType : APP_TYPE.MERCHANT]}`;

              datasetDict[typeParsed].data[
                i
              ].value += 1;

              datasetDict[typeParsed].data[
                i
              ].toolText = `${datasetDict[typeParsed].data[
                i
              ].value.toFixed(0)}`;
            }
          }

          // if (record.value > yMaxValue) {
          //     yMaxValue = record.value;
          // }

          // record.value = record.value.toFixed(0);

          // parsedData.push(record);
        }
      }
      else if (xAxisKey === CHART_X_AXIS_TYPE.WEEK || xAxisKey === CHART_X_AXIS_TYPE.MONTH) {
        var currWeekCount = 0;
        var prevMonth = moment(startDate);

        for (var i = 0; i < allOutlets.length; i++) {
          datasetDict[allOutlets[i].value] = {
            seriesname: allOutlets[i].label,
            data: Array.from(Array(moment(endDate).diff(startDate, 'week'))).map(
              () => {
                return {
                  value: 0,
                  toolText: `0`,
                };
              },
            ),
          };
        }

        for (var i = 0; i < moment(endDate).diff(startDate, 'week'); i++) {
          const currWeek = moment(startDate).add(i, 'week');

          if (moment(currWeek).isSame(prevMonth, 'month')) {
            currWeekCount++;
          } else {
            prevMonth = moment(currWeek);

            currWeekCount = 0;
            currWeekCount++;
          }

          // var record = {
          //     label: (currWeekCount > 1 ? '' : moment(currWeek).format('MMM')) + ' W' + currWeekCount,
          //     value: 0,
          // };

          category.push({
            label:
              `${currWeekCount > 1 ? '' : moment(currWeek).format('MMM')
              } W${currWeekCount}`,
          });

          for (var j = 0; j < allOutletsUserOrdersDoneToday.length; j++) {
            if (
              // moment(currWeek).isSame(
              //   moment(allOutletsUserOrdersDoneToday[j].createdAt),
              //   'week',
              // )
              compareOrderDateByDisplayType(
                currWeek,
                allOutletsUserOrdersDoneToday[j].createdAt,
                DATE_COMPARE_TYPE.IS_SAME,
                'day',
                reportDisplayType,
                outletShifts
              )
            ) {
              // var typeParsed = `${allOutletsUserOrdersDoneToday[j].orderType}${appTypeToValueDict[allOutletsUserOrdersDoneToday[j].appType]}`;
              var typeParsed = `${allOutletsUserOrdersDoneToday[j].orderType ? allOutletsUserOrdersDoneToday[j].orderType : ORDER_TYPE.DINEIN}${appTypeToValueDict[allOutletsUserOrdersDoneToday[j].appType ? allOutletsUserOrdersDoneToday[j].appType : APP_TYPE.MERCHANT]}`;

              datasetDict[typeParsed].data[
                i
              ].value += 1;

              datasetDict[typeParsed].data[
                i
              ].toolText = `${datasetDict[typeParsed].data[
                i
              ].value.toFixed(0)}`;
            }
          }

          // if (record.value > yMaxValue) {
          //     yMaxValue = record.value;
          // }

          // record.value = record.value.toFixed(0);

          // parsedData.push(record);
        }
      }
    } else if (chartPeriod === CHART_PERIOD.THREE_MONTHS) {
      const startDate = moment().subtract(2, 'month').startOf('month');
      const endDate = moment().endOf('month');

      const allOutletsUserOrdersDoneToday = allOutletsUserOrdersDone.filter(
        (order) => {
          return (
            moment(startDate).isSameOrBefore(order.createdAt) &&
            moment(endDate).isAfter(order.createdAt)
          );
        },
      );

      if (xAxisKey === CHART_X_AXIS_TYPE.DAY) {
        for (var i = 0; i < allOutlets.length; i++) {
          datasetDict[allOutlets[i].value] = {
            seriesname: allOutlets[i].label,
            data: Array.from(Array(moment(endDate).diff(startDate, 'day'))).map(() => {
              return {
                value: 0,
                toolText: `0`,
              };
            }),
          };
        }

        for (var i = 0; i < moment(endDate).diff(startDate, 'day'); i++) {
          // for (var i = 0; i < 31; i++) {
          // const currDay = moment().startOf('month').add(i, 'day');
          const currDay = moment(startDate).add(i, 'day');

          // var record = {
          //     label: i.toString().padStart(2, '0'),
          //     value: 0,
          // };

          category.push({
            label: (i + 1).toString().padStart(2, '0'),
          });

          for (var j = 0; j < allOutletsUserOrdersDoneToday.length; j++) {
            if (
              // moment(currDay).isSame(
              //   moment(allOutletsUserOrdersDoneToday[j].createdAt),
              //   'day',
              // )
              compareOrderDateByDisplayType(
                currDay,
                allOutletsUserOrdersDoneToday[j].createdAt,
                DATE_COMPARE_TYPE.IS_SAME,
                'day',
                reportDisplayType,
                outletShifts
              )
            ) {
              // var typeParsed = `${allOutletsUserOrdersDoneToday[j].orderType}${appTypeToValueDict[allOutletsUserOrdersDoneToday[j].appType]}`;
              var typeParsed = `${allOutletsUserOrdersDoneToday[j].orderType ? allOutletsUserOrdersDoneToday[j].orderType : ORDER_TYPE.DINEIN}${appTypeToValueDict[allOutletsUserOrdersDoneToday[j].appType ? allOutletsUserOrdersDoneToday[j].appType : APP_TYPE.MERCHANT]}`;

              datasetDict[typeParsed].data[
                i
              ].value += 1;

              datasetDict[typeParsed].data[
                i
              ].toolText = `${datasetDict[typeParsed].data[
                i
              ].value.toFixed(0)}`;
            }
          }

          // if (record.value > yMaxValue) {
          //     yMaxValue = record.value;
          // }

          // record.value = record.value.toFixed(0);

          // parsedData.push(record);
        }
      }
      else if (xAxisKey === CHART_X_AXIS_TYPE.WEEK) {
        var currWeekCount = 0;
        var prevMonth = moment(startDate);

        for (var i = 0; i < allOutlets.length; i++) {
          datasetDict[allOutlets[i].value] = {
            seriesname: allOutlets[i].label,
            data: Array.from(Array(moment(endDate).diff(startDate, 'week'))).map(
              () => {
                return {
                  value: 0,
                  toolText: `0`,
                };
              },
            ),
          };
        }

        for (var i = 0; i < moment(endDate).diff(startDate, 'week'); i++) {
          const currWeek = moment(startDate).add(i, 'week');

          if (moment(currWeek).isSame(prevMonth, 'month')) {
            currWeekCount++;
          } else {
            prevMonth = moment(currWeek);

            currWeekCount = 0;
            currWeekCount++;
          }

          // var record = {
          //     label: (currWeekCount > 1 ? '' : moment(currWeek).format('MMM')) + ' W' + currWeekCount,
          //     value: 0,
          // };

          category.push({
            label:
              `${currWeekCount > 1 ? '' : moment(currWeek).format('MMM')
              } W${currWeekCount}`,
          });

          for (var j = 0; j < allOutletsUserOrdersDoneToday.length; j++) {
            if (
              // moment(currWeek).isSame(
              //   moment(allOutletsUserOrdersDoneToday[j].createdAt),
              //   'week',
              // )
              compareOrderDateByDisplayType(
                currWeek,
                allOutletsUserOrdersDoneToday[j].createdAt,
                DATE_COMPARE_TYPE.IS_SAME,
                'week',
                reportDisplayType,
                outletShifts
              )
            ) {
              // var typeParsed = `${allOutletsUserOrdersDoneToday[j].orderType}${appTypeToValueDict[allOutletsUserOrdersDoneToday[j].appType]}`;
              var typeParsed = `${allOutletsUserOrdersDoneToday[j].orderType ? allOutletsUserOrdersDoneToday[j].orderType : ORDER_TYPE.DINEIN}${appTypeToValueDict[allOutletsUserOrdersDoneToday[j].appType ? allOutletsUserOrdersDoneToday[j].appType : APP_TYPE.MERCHANT]}`;

              datasetDict[typeParsed].data[
                i
              ].value += 1;

              datasetDict[typeParsed].data[
                i
              ].toolText = `${datasetDict[typeParsed].data[
                i
              ].value.toFixed(0)}`;
            }
          }

          // if (record.value > yMaxValue) {
          //     yMaxValue = record.value;
          // }

          // record.value = record.value.toFixed(0);

          // parsedData.push(record);
        }
      }
      else if (xAxisKey === CHART_X_AXIS_TYPE.MONTH) {
        for (var i = 0; i < allOutlets.length; i++) {
          datasetDict[allOutlets[i].value] = {
            seriesname: allOutlets[i].label,
            data: Array.from(Array(3)).map(() => {
              return {
                value: 0,
                toolText: `0`,
              };
            }),
          };
        }

        var currMonth = moment(startDate).startOf('month');

        for (var i = 0; i < 3; i++) {
          // const currMonth = moment().startOf('year').add(i, 'month');
          var currMonth = moment(currMonth).add(i, 'month');

          // var record = {
          //     label: moment(currMonth).format('MMM'),
          //     value: 0,
          // };

          category.push({
            label: moment(currMonth).format('MMM'),
          });

          for (var j = 0; j < allOutletsUserOrdersDoneToday.length; j++) {
            if (
              // moment(currMonth).isSame(
              //   moment(allOutletsUserOrdersDoneToday[j].createdAt),
              //   'month',
              // )
              compareOrderDateByDisplayType(
                currMonth,
                allOutletsUserOrdersDoneToday[j].createdAt,
                DATE_COMPARE_TYPE.IS_SAME,
                'month',
                reportDisplayType,
                outletShifts
              )
            ) {
              // var typeParsed = `${allOutletsUserOrdersDoneToday[j].orderType}${appTypeToValueDict[allOutletsUserOrdersDoneToday[j].appType]}`;
              var typeParsed = `${allOutletsUserOrdersDoneToday[j].orderType ? allOutletsUserOrdersDoneToday[j].orderType : ORDER_TYPE.DINEIN}${appTypeToValueDict[allOutletsUserOrdersDoneToday[j].appType ? allOutletsUserOrdersDoneToday[j].appType : APP_TYPE.MERCHANT]}`;

              datasetDict[typeParsed].data[
                i
              ].value += 1;

              datasetDict[typeParsed].data[
                i
              ].toolText = `${datasetDict[typeParsed].data[
                i
              ].value.toFixed(0)}`;
            }
          }

          // if (record.value > yMaxValue) {
          //     yMaxValue = record.value;
          // }

          // record.value = record.value.toFixed(0);

          // parsedData.push(record);
        }
      }
    } else if (chartPeriod === CHART_PERIOD.SIX_MONTHS) {
      const startDate = moment().subtract(5, 'month').startOf('month');
      const endDate = moment().endOf('month');

      const allOutletsUserOrdersDoneToday = allOutletsUserOrdersDone.filter(
        (order) => {
          return (
            moment(startDate).isSameOrBefore(order.createdAt) &&
            moment(endDate).isAfter(order.createdAt)
          );
        },
      );

      if (xAxisKey === CHART_X_AXIS_TYPE.DAY || xAxisKey === CHART_X_AXIS_TYPE.WEEK) {
        for (var i = 0; i < allOutlets.length; i++) {
          datasetDict[allOutlets[i].value] = {
            seriesname: allOutlets[i].label,
            data: Array.from(Array(moment(endDate).diff(startDate, 'week'))).map(
              () => {
                return {
                  value: 0,
                  toolText: `0`,
                };
              },
            ),
          };
        }

        for (var i = 0; i < moment(endDate).diff(startDate, 'week'); i++) {
          const currWeek = moment(startDate).add(i, 'week');

          if (moment(currWeek).isSame(prevMonth, 'month')) {
            currWeekCount++;
          } else {
            prevMonth = moment(currWeek);

            currWeekCount = 0;
            currWeekCount++;
          }

          // var record = {
          //     label: 'W' + currWeekCount + ' ' + (currWeekCount > 1 ? '' : moment(currWeek).format('MMM')),
          //     value: 0,
          // };

          category.push({
            label:
              `W${currWeekCount
              } ${currWeekCount > 1 ? '' : moment(currWeek).format('MMM')}`,
          });

          for (var j = 0; j < allOutletsUserOrdersDoneToday.length; j++) {
            if (
              // moment(currWeek).isSame(
              //   moment(allOutletsUserOrdersDoneToday[j].createdAt),
              //   'week',
              // )
              compareOrderDateByDisplayType(
                currWeek,
                allOutletsUserOrdersDoneToday[j].createdAt,
                DATE_COMPARE_TYPE.IS_SAME,
                'week',
                reportDisplayType,
                outletShifts
              )
            ) {
              // var typeParsed = `${allOutletsUserOrdersDoneToday[j].orderType}${appTypeToValueDict[allOutletsUserOrdersDoneToday[j].appType]}`;
              var typeParsed = `${allOutletsUserOrdersDoneToday[j].orderType ? allOutletsUserOrdersDoneToday[j].orderType : ORDER_TYPE.DINEIN}${appTypeToValueDict[allOutletsUserOrdersDoneToday[j].appType ? allOutletsUserOrdersDoneToday[j].appType : APP_TYPE.MERCHANT]}`;

              datasetDict[typeParsed].data[
                i
              ].value += 1;

              datasetDict[typeParsed].data[
                i
              ].toolText = `${datasetDict[typeParsed].data[
                i
              ].value.toFixed(0)}`;
            }
          }

          // if (record.value > yMaxValue) {
          //     yMaxValue = record.value;
          // }

          // record.value = record.value.toFixed(0);

          // parsedData.push(record);
        }
      }
      else if (xAxisKey === CHART_X_AXIS_TYPE.MONTH) {
        for (var i = 0; i < allOutlets.length; i++) {
          datasetDict[allOutlets[i].value] = {
            seriesname: allOutlets[i].label,
            data: Array.from(Array(6)).map(() => {
              return {
                value: 0,
                toolText: `0`,
              };
            }),
          };
        }

        var currMonth = moment(startDate).startOf('month');

        for (var i = 0; i < 6; i++) {
          // const currMonth = moment().startOf('year').add(i, 'month');
          var currMonth = moment(currMonth).add(i, 'month');

          // var record = {
          //     label: moment(currMonth).format('MMM'),
          //     value: 0,
          // };

          category.push({
            label: moment(currMonth).format('MMM'),
          });

          for (var j = 0; j < allOutletsUserOrdersDoneToday.length; j++) {
            if (
              // moment(currMonth).isSame(
              //   moment(allOutletsUserOrdersDoneToday[j].createdAt),
              //   'month',
              // )
              compareOrderDateByDisplayType(
                currMonth,
                allOutletsUserOrdersDoneToday[j].createdAt,
                DATE_COMPARE_TYPE.IS_SAME,
                'month',
                reportDisplayType,
                outletShifts
              )
            ) {
              // var typeParsed = `${allOutletsUserOrdersDoneToday[j].orderType}${appTypeToValueDict[allOutletsUserOrdersDoneToday[j].appType]}`;
              var typeParsed = `${allOutletsUserOrdersDoneToday[j].orderType ? allOutletsUserOrdersDoneToday[j].orderType : ORDER_TYPE.DINEIN}${appTypeToValueDict[allOutletsUserOrdersDoneToday[j].appType ? allOutletsUserOrdersDoneToday[j].appType : APP_TYPE.MERCHANT]}`;

              datasetDict[typeParsed].data[
                i
              ].value += 1;

              datasetDict[typeParsed].data[
                i
              ].toolText = `${datasetDict[typeParsed].data[
                i
              ].value.toFixed(0)}`;
            }
          }

          // if (record.value > yMaxValue) {
          //     yMaxValue = record.value;
          // }

          // record.value = record.value.toFixed(0);

          // parsedData.push(record);
        }
      }
    } else if (chartPeriod === CHART_PERIOD.THIS_YEAR) {
      const allOutletsUserOrdersDoneToday = allOutletsUserOrdersDone.filter(
        (order) => {
          return moment().isSame(moment(order.createdAt), 'year');
        },
      );

      if (xAxisKey === CHART_X_AXIS_TYPE.DAY || xAxisKey === CHART_X_AXIS_TYPE.WEEK) {
        for (var i = 0; i < allOutlets.length; i++) {
          datasetDict[allOutlets[i].value] = {
            seriesname: allOutlets[i].label,
            data: Array.from(Array(moment(endDate).diff(startDate, 'week'))).map(
              () => {
                return {
                  value: 0,
                  toolText: `0`,
                };
              },
            ),
          };
        }

        for (var i = 0; i < moment(endDate).diff(startDate, 'week'); i++) {
          const currWeek = moment(startDate).add(i, 'week');

          if (moment(currWeek).isSame(prevMonth, 'month')) {
            currWeekCount++;
          } else {
            prevMonth = moment(currWeek);

            currWeekCount = 0;
            currWeekCount++;
          }

          // var record = {
          //     label: 'W' + currWeekCount + ' ' + (currWeekCount > 1 ? '' : moment(currWeek).format('MMM')),
          //     value: 0,
          // };

          category.push({
            label:
              `W${currWeekCount
              } ${currWeekCount > 2 ? '' : moment(currWeek).format('MMM')}`,
          });

          for (var j = 0; j < allOutletsUserOrdersDoneToday.length; j++) {
            if (
              // moment(currWeek).isSame(
              //   moment(allOutletsUserOrdersDoneToday[j].createdAt),
              //   'week',
              // )
              compareOrderDateByDisplayType(
                currWeek,
                allOutletsUserOrdersDoneToday[j].createdAt,
                DATE_COMPARE_TYPE.IS_SAME,
                'week',
                reportDisplayType,
                outletShifts
              )
            ) {
              // var typeParsed = `${allOutletsUserOrdersDoneToday[j].orderType}${appTypeToValueDict[allOutletsUserOrdersDoneToday[j].appType]}`;
              var typeParsed = `${allOutletsUserOrdersDoneToday[j].orderType ? allOutletsUserOrdersDoneToday[j].orderType : ORDER_TYPE.DINEIN}${appTypeToValueDict[allOutletsUserOrdersDoneToday[j].appType ? allOutletsUserOrdersDoneToday[j].appType : APP_TYPE.MERCHANT]}`;

              datasetDict[typeParsed].data[
                i
              ].value += 1;

              datasetDict[typeParsed].data[
                i
              ].toolText = `${datasetDict[typeParsed].data[
                i
              ].value.toFixed(0)}`;
            }
          }

          // if (record.value > yMaxValue) {
          //     yMaxValue = record.value;
          // }

          // record.value = record.value.toFixed(0);

          // parsedData.push(record);
        }
      }
      else if (xAxisKey === CHART_X_AXIS_TYPE.MONTH) {
        for (var i = 0; i < allOutlets.length; i++) {
          datasetDict[allOutlets[i].value] = {
            seriesname: allOutlets[i].label,
            data: Array.from(Array(12)).map(() => {
              return {
                value: 0,
                toolText: `0`,
              };
            }),
          };
        }

        for (var i = 0; i < 12; i++) {
          const currMonth = moment().startOf('year').add(i, 'month');

          // var record = {
          //     label: moment(currMonth).format('MMM'),
          //     value: 0,
          // };

          category.push({
            label: moment(currMonth).format('MMM'),
          });

          for (var j = 0; j < allOutletsUserOrdersDoneToday.length; j++) {
            if (
              // moment(currMonth).isSame(
              //   moment(allOutletsUserOrdersDoneToday[j].createdAt),
              //   'month',
              // )
              compareOrderDateByDisplayType(
                currMonth,
                allOutletsUserOrdersDoneToday[j].createdAt,
                DATE_COMPARE_TYPE.IS_SAME,
                'month',
                reportDisplayType,
                outletShifts
              )
            ) {
              // var typeParsed = `${allOutletsUserOrdersDoneToday[j].orderType}${appTypeToValueDict[allOutletsUserOrdersDoneToday[j].appType]}`;
              var typeParsed = `${allOutletsUserOrdersDoneToday[j].orderType ? allOutletsUserOrdersDoneToday[j].orderType : ORDER_TYPE.DINEIN}${appTypeToValueDict[allOutletsUserOrdersDoneToday[j].appType ? allOutletsUserOrdersDoneToday[j].appType : APP_TYPE.MERCHANT]}`;

              datasetDict[typeParsed].data[
                i
              ].value += 1;

              datasetDict[typeParsed].data[
                i
              ].toolText = `${datasetDict[typeParsed].data[
                i
              ].value.toFixed(0)}`;
            }
          }

          // if (record.value > yMaxValue) {
          //     yMaxValue = record.value;
          // }

          // record.value = record.value.toFixed(0);

          // parsedData.push(record);
        }
      }
    } else if (chartPeriod === CHART_PERIOD.YTD) {
      const startDate = moment().subtract(11, 'month').startOf('month');
      const endDate = moment().endOf('month');

      const allOutletsUserOrdersDoneToday = allOutletsUserOrdersDone.filter(
        (order) => {
          return (
            moment(startDate).isSameOrBefore(order.createdAt) &&
            moment(endDate).isAfter(order.createdAt)
          );
        },
      );

      if (xAxisKey === CHART_X_AXIS_TYPE.DAY || xAxisKey === CHART_X_AXIS_TYPE.WEEK) {
        for (var i = 0; i < allOutlets.length; i++) {
          datasetDict[allOutlets[i].value] = {
            seriesname: allOutlets[i].label,
            data: Array.from(Array(moment(endDate).diff(startDate, 'week'))).map(
              () => {
                return {
                  value: 0,
                  toolText: `0`,
                };
              },
            ),
          };
        }

        for (var i = 0; i < moment(endDate).diff(startDate, 'week'); i++) {
          const currWeek = moment(startDate).add(i, 'week');

          if (moment(currWeek).isSame(prevMonth, 'month')) {
            currWeekCount++;
          } else {
            prevMonth = moment(currWeek);

            currWeekCount = 0;
            currWeekCount++;
          }

          // var record = {
          //     label: 'W' + currWeekCount + ' ' + (currWeekCount > 1 ? '' : moment(currWeek).format('MMM')),
          //     value: 0,
          // };

          category.push({
            label:
              `W${currWeekCount
              } ${currWeekCount > 2 ? '' : moment(currWeek).format('MMM')}`,
          });

          for (var j = 0; j < allOutletsUserOrdersDoneToday.length; j++) {
            if (
              // moment(currWeek).isSame(
              //   moment(allOutletsUserOrdersDoneToday[j].createdAt),
              //   'week',
              // )
              compareOrderDateByDisplayType(
                currWeek,
                allOutletsUserOrdersDoneToday[j].createdAt,
                DATE_COMPARE_TYPE.IS_SAME,
                'week',
                reportDisplayType,
                outletShifts
              )
            ) {
              // var typeParsed = `${allOutletsUserOrdersDoneToday[j].orderType}${appTypeToValueDict[allOutletsUserOrdersDoneToday[j].appType]}`;
              var typeParsed = `${allOutletsUserOrdersDoneToday[j].orderType ? allOutletsUserOrdersDoneToday[j].orderType : ORDER_TYPE.DINEIN}${appTypeToValueDict[allOutletsUserOrdersDoneToday[j].appType ? allOutletsUserOrdersDoneToday[j].appType : APP_TYPE.MERCHANT]}`;

              datasetDict[typeParsed].data[
                i
              ].value += 1;

              datasetDict[typeParsed].data[
                i
              ].toolText = `${datasetDict[typeParsed].data[
                i
              ].value.toFixed(0)}`;
            }
          }

          // if (record.value > yMaxValue) {
          //     yMaxValue = record.value;
          // }

          // record.value = record.value.toFixed(0);

          // parsedData.push(record);
        }
      }
      else if (xAxisKey === CHART_X_AXIS_TYPE.MONTH) {
        for (var i = 0; i < allOutlets.length; i++) {
          datasetDict[allOutlets[i].value] = {
            seriesname: allOutlets[i].label,
            data: Array.from(Array(12)).map(() => {
              return {
                value: 0,
                toolText: `0`,
              };
            }),
          };
        }

        for (var i = 0; i < 12; i++) {
          const currMonth = moment(startDate).add(i, 'month');

          // var record = {
          //     label: moment(currMonth).format('YY\' MMM'),
          //     value: 0,
          // };

          category.push({
            label: moment(currMonth).format("YY' MMM"),
          });

          for (var j = 0; j < allOutletsUserOrdersDoneToday.length; j++) {
            if (
              // moment(currMonth).isSame(
              //   moment(allOutletsUserOrdersDoneToday[j].createdAt),
              //   'month',
              // )
              compareOrderDateByDisplayType(
                currMonth,
                allOutletsUserOrdersDoneToday[j].createdAt,
                DATE_COMPARE_TYPE.IS_SAME,
                'month',
                reportDisplayType,
                outletShifts
              )
            ) {
              // var typeParsed = `${allOutletsUserOrdersDoneToday[j].orderType}${appTypeToValueDict[allOutletsUserOrdersDoneToday[j].appType]}`;
              var typeParsed = `${allOutletsUserOrdersDoneToday[j].orderType ? allOutletsUserOrdersDoneToday[j].orderType : ORDER_TYPE.DINEIN}${appTypeToValueDict[allOutletsUserOrdersDoneToday[j].appType ? allOutletsUserOrdersDoneToday[j].appType : APP_TYPE.MERCHANT]}`;

              datasetDict[typeParsed].data[
                i
              ].value += 1;

              datasetDict[typeParsed].data[
                i
              ].toolText = `${datasetDict[typeParsed].data[
                i
              ].value.toFixed(0)}`;
            }
          }

          // if (record.value > yMaxValue) {
          //     yMaxValue = record.value;
          // }

          // record.value = record.value.toFixed(0);

          // parsedData.push(record);
        }
      }
    } else if (chartPeriod === CHART_PERIOD.NONE) {
      const allOutletsUserOrdersDoneTodayRange =
        allOutletsUserOrdersDone.filter((order) => {
          return (
            moment(startDate).isSameOrBefore(order.createdAt) &&
            moment(endDate).isAfter(order.createdAt)
          );
        });

      if (
        moment(endDate).diff(startDate, 'day') <=
        moment(startDate).daysInMonth()
      ) {
        endDate = moment(endDate).add(1, 'day');

        const diffDays = moment(endDate).diff(startDate, 'day');

        var prevDate = moment(startDate).add(-1, 'month');

        for (var i = 0; i < allOutlets.length; i++) {
          datasetDict[allOutlets[i].value] = {
            seriesname: allOutlets[i].label,
            data: Array.from(Array(diffDays)).map(() => {
              return {
                value: 0,
                toolText: `0`,
              };
            }),
          };
        }

        for (var i = 0; i < diffDays; i++) {
          const currDate = moment(startDate).add(i, 'day');

          category.push({
            //'label': moment(currDate).format('DD MMM'),
            label: !moment(currDate).isSame(prevDate, 'month')
              ? moment(currDate).format('DD MMM')
              : moment(currDate).format('DD'),
            //'label': (i > 1 ? moment(currDate).format('DD') : moment(currDate).format('DD MMM'))
          });

          prevDate = currDate;

          for (var j = 0; j < allOutletsUserOrdersDoneTodayRange.length; j++) {
            if (
              // allOutletsUserOrdersDoneTodayRange[j].outletId === allOutlets[i].uniqueId &&
              // moment(currDate).isSame(
              //   moment(allOutletsUserOrdersDoneTodayRange[j].createdAt),
              //   'day',
              // )
              compareOrderDateByDisplayType(
                currDate,
                allOutletsUserOrdersDoneTodayRange[j].createdAt,
                DATE_COMPARE_TYPE.IS_SAME,
                'day',
                reportDisplayType,
                outletShifts
              )
            ) {
              // var typeParsed = `${allOutletsUserOrdersDoneTodayRange[j].orderType}${appTypeToValueDict[allOutletsUserOrdersDoneTodayRange[j].appType]}`;
              var typeParsed = `${allOutletsUserOrdersDoneTodayRange[j].orderType ? allOutletsUserOrdersDoneTodayRange[j].orderType : ORDER_TYPE.DINEIN}${appTypeToValueDict[allOutletsUserOrdersDoneTodayRange[j].appType ? allOutletsUserOrdersDoneTodayRange[j].appType : APP_TYPE.MERCHANT]}`;

              datasetDict[typeParsed].data[
                i
              ].value += 1;

              datasetDict[typeParsed].data[
                i
              ].toolText = `${datasetDict[typeParsed].data[
                i
              ].value.toFixed(0)}`;
            }
          }

          // if (record.value > yMaxValue) {
          //     yMaxValue = record.value;
          // }

          // record.value = record.value.toFixed(0);

          // parsedData.push(record);
        }
      } else if (moment(endDate).diff(startDate, 'month') <= 6) {
        var prevMonth = moment(startDate);

        const diffWeeks = moment(endDate).diff(startDate, 'week');

        for (var i = 0; i < allOutlets.length; i++) {
          datasetDict[allOutlets[i].value] = {
            seriesname: allOutlets[i].label,
            data: Array.from(Array(diffWeeks)).map(() => {
              return {
                value: 0,
                toolText: `0`,
              };
            }),
          };
        }

        for (var i = 0; i < diffWeeks; i++) {
          const currDate = moment(startDate).add(i, 'week');

          if (moment(currDate).isSame(prevMonth, 'month')) {
            currWeekCount++;
          } else {
            prevMonth = moment(currDate);

            currWeekCount = 0;
            currWeekCount++;
          }

          category.push({
            // label: moment(currDate).format('MMM') + ' W' + currWeekCount,
            label:
              `${moment(currDate).format('MMM')
              } W${countWeekdayOccurrencesInMonth(currDate)}`,
          });

          for (var j = 0; j < allOutletsUserOrdersDoneTodayRange.length; j++) {
            if (
              // allOutletsUserOrdersDoneTodayRange[j].outletId === allOutlets[i].uniqueId &&
              // moment(currDate).isSame(
              //   moment(allOutletsUserOrdersDoneTodayRange[j].createdAt),
              //   'week',
              // )
              compareOrderDateByDisplayType(
                currDate,
                allOutletsUserOrdersDoneTodayRange[j].createdAt,
                DATE_COMPARE_TYPE.IS_SAME,
                'week',
                reportDisplayType,
                outletShifts
              )
            ) {
              // var typeParsed = `${allOutletsUserOrdersDoneTodayRange[j].orderType}${appTypeToValueDict[allOutletsUserOrdersDoneTodayRange[j].appType]}`;
              var typeParsed = `${allOutletsUserOrdersDoneTodayRange[j].orderType ? allOutletsUserOrdersDoneTodayRange[j].orderType : ORDER_TYPE.DINEIN}${appTypeToValueDict[allOutletsUserOrdersDoneTodayRange[j].appType ? allOutletsUserOrdersDoneTodayRange[j].appType : APP_TYPE.MERCHANT]}`;

              datasetDict[typeParsed].data[
                i
              ].value += 1;

              datasetDict[typeParsed].data[
                i
              ].toolText = `${datasetDict[typeParsed].data[
                i
              ].value.toFixed(0)}`;
            }
          }

          // if (record.value > yMaxValue) {
          //     yMaxValue = record.value;
          // }

          // record.value = record.value.toFixed(0);

          // parsedData.push(record);
        }
      }
      // if (moment(endDate).diff(startDate, 'month') <= 6) // for now no need conditions first, for more than 6 months interval
      else {
        const diffYears = moment(endDate).diff(startDate, 'month');

        for (var i = 0; i < allOutlets.length; i++) {
          datasetDict[allOutlets[i].value] = {
            seriesname: allOutlets[i].label,
            data: Array.from(Array(diffYears)).map(() => {
              return {
                value: 0,
                toolText: `0`,
              };
            }),
          };
        }

        for (var i = 0; i < diffYears; i++) {
          const currDate = moment(startDate).add(i, 'month');

          category.push({
            label: moment(currDate).format("YY' MMM"),
          });

          for (var j = 0; j < allOutletsUserOrdersDoneTodayRange.length; j++) {
            if (
              // allOutletsUserOrdersDoneTodayRange[j].outletId === allOutlets[i].uniqueId &&
              // moment(currDate).isSame(
              //   moment(allOutletsUserOrdersDoneTodayRange[j].createdAt),
              //   'month',
              // )
              compareOrderDateByDisplayType(
                currDate,
                allOutletsUserOrdersDoneTodayRange[j].createdAt,
                DATE_COMPARE_TYPE.IS_SAME,
                'month',
                reportDisplayType,
                outletShifts
              )
            ) {
              // var typeParsed = `${allOutletsUserOrdersDoneTodayRange[j].orderType}${appTypeToValueDict[allOutletsUserOrdersDoneTodayRange[j].appType]}`;
              var typeParsed = `${allOutletsUserOrdersDoneTodayRange[j].orderType ? allOutletsUserOrdersDoneTodayRange[j].orderType : ORDER_TYPE.DINEIN}${appTypeToValueDict[allOutletsUserOrdersDoneTodayRange[j].appType ? allOutletsUserOrdersDoneTodayRange[j].appType : APP_TYPE.MERCHANT]}`;

              datasetDict[typeParsed].data[
                i
              ].value += 1;

              datasetDict[typeParsed].data[
                i
              ].toolText = `${datasetDict[typeParsed].data[
                i
              ].value.toFixed(0)}`;
            }
          }

          // if (record.value > yMaxValue) {
          //     yMaxValue = record.value;
          // }

          // record.value = record.value.toFixed(0);

          // parsedData.push(record);
        }
      }
    }

    // const allOutletsUserOrdersDoneToday = allOutletsUserOrdersDone.filter(order => {
    //     return moment().isSame(moment(order.createdAt), 'year');
    // });

    // for (var i = 0; i < 12; i++) {
    //     var record = {
    //         label: MONTH_TO_CHART_LABEL[i],
    //         value: 0,
    //     };

    //     const currMonth = moment().startOf('year').add(i, 'month');

    //     for (var j = 0; j < allOutletsUserOrdersDoneToday.length; j++) {
    //         // if (allOutletsUserOrdersDoneToday[j].outletId === allOutlets[i].uniqueId) {
    //         //     record.value += allOutletsUserOrdersDoneToday[j].finalPrice;

    //         //     // if (allOutletsUserOrdersDoneToday[j].finalPrice > yMaxValue) {
    //         //     //     yMaxValue += allOutletsUserOrdersDoneToday[j].finalPrice;
    //         //     // }
    //         // }

    //         if (moment(currMonth).isSame(moment(allOutletsUserOrdersDoneToday[j].createdAt), 'month')) {
    //             record.value += allOutletsUserOrdersDoneToday[j].finalPrice;
    //         }
    //     }

    //     // yMaxValue += record.value;
    //     if (record.value > yMaxValue) {
    //         yMaxValue = record.value;
    //     }

    //     record.value = record.value.toFixed(0);

    //     parsedData.push(record);
    // }

    for (var j = 0; j < allOutlets.length; j++) {
      var outletId = allOutlets[j].value;

      var currTotal = 0;

      for (var k = 0; k < datasetDict[outletId].data.length; k++) {
        // currTotal += datasetDict[outletId].data[k].value;

        currTotal = datasetDict[outletId].data[k].value;

        if (currTotal > yMaxValue) {
          yMaxValue = currTotal;
        }
      }

      // if (currTotal > yMaxValue) {
      //     yMaxValue = currTotal;
      // }
    }

    var chartLegend = [];

    const dataset = Object.entries(datasetDict).slice(0, 3).map(([key, value], index) => {
      const mappedIndex =
        index % CHART_COLORS[CHART_TYPE.REPORT_ORDER_COUNT_MULTI_COLUMN].length; // might got more than 10 preset colors, set to infinite list instead

      chartLegend.push({
        itemName: value.seriesname,
        itemSku: value.seriesname,
        chartColor:
          CHART_COLORS[CHART_TYPE.REPORT_ORDER_COUNT_MULTI_COLUMN][mappedIndex],
      });

      return {
        ...value,
        color: CHART_COLORS[CHART_TYPE.REPORT_ORDER_COUNT_MULTI_COLUMN][mappedIndex],
      };
    });

    const dataset2 = Object.entries(datasetDict).slice(3, 6).map(([key, value], index) => {
      const mappedIndex =
        (index + 3) % CHART_COLORS[CHART_TYPE.REPORT_ORDER_COUNT_MULTI_COLUMN].length; // might got more than 10 preset colors, set to infinite list instead

      chartLegend.push({
        itemName: value.seriesname,
        itemSku: value.seriesname,
        chartColor:
          CHART_COLORS[CHART_TYPE.REPORT_ORDER_COUNT_MULTI_COLUMN][mappedIndex],
      });

      return {
        ...value,
        color: CHART_COLORS[CHART_TYPE.REPORT_ORDER_COUNT_MULTI_COLUMN][mappedIndex],
      };
    });

    var output = {};

    output.type = CHART_DATA[CHART_TYPE.REPORT_ORDER_COUNT_MULTI_COLUMN].type;
    output.dataFormat =
      CHART_DATA[CHART_TYPE.REPORT_ORDER_COUNT_MULTI_COLUMN].dataFormat;
    output.dataSource = {
      chart: CHART_DATA[CHART_TYPE.REPORT_ORDER_COUNT_MULTI_COLUMN].chart,

      categories: [
        {
          category,
        },
      ],
      // dataset: dataset,
      dataset: [
        {
          dataset,
        },
        {
          dataset: dataset2,
        },
      ],

      annotations,

      // data: parsedData,
    };

    // // console.log('parsedData');
    // // console.log(parsedData);
    // console.log(yMaxValue);

    if (output.type !== 'HLinearGauge') {
      // const yAxisStep = Math.ceil((yMaxValue / 10) / 10) * 10;

      // const yMaxValueParsed = Math.ceil(yMaxValue / yAxisStep);
      // const yAxisMaxValue = yMaxValueParsed === 1 ? 2 : yMaxValueParsed;
      // const numDivLines = yMaxValueParsed === 1 ? 1 : yMaxValueParsed - 1;

      const baseNumberLength = yMaxValue.toFixed(0).length - 1;
      const baseNumber = parseInt(
        `1${Array(baseNumberLength)
          .fill()
          .map((num) => 0)
          .join('')}`,
      );
      var maxNumber = baseNumber;

      if (yMaxValue > baseNumber * 5) {
        maxNumber = baseNumber * 10;
      } else if (yMaxValue > baseNumber * 3) {
        maxNumber = baseNumber * 5;
      } else if (yMaxValue > baseNumber * 2) {
        maxNumber = baseNumber * 3;
      } else {
        maxNumber = baseNumber * 2;
      }

      maxNumber = getSuitableYAxisMaxValue(maxNumber);

      maxNumber = Math.max(maxNumber, 10);

      output.dataSource.chart = {
        ...output.dataSource.chart,
        labelDisplay: 'WRAP',
        // labelDisplay: 'STAGGER',
        // labelStep: 2,
        bgColor: '#ffffff',
        canvasBgColor: '#ffffff',
        bgAlpha: 100,

        useEllipsesWhenOverflow: 1,

        // showYAxisValues: 0,
        // yaxismaxvalue: yAxisMaxValue * yAxisStep,
        yaxismaxvalue: maxNumber,
        yaxisminvalue: yMinValue,
        adjustDiv: 0,
        // numDivLines: numDivLines,
        numDivLines: 9,

        // yaxisminvalue: 0,
        formatNumberScale: 0,
        decimals: 0,
      };

      // // console.log('/////////////////////////////////////////////////')

      // // console.log({
      //     yMaxValue: yMaxValue,
      //     yMaxValueParsed: yMaxValueParsed,
      //     yaxismaxvalue: yAxisMaxValue * yAxisStep,
      //     yaxisminvalue: yMinValue,
      //     adjustDiv: 0,
      //     numDivLines: numDivLines,
      // });

      // // console.log('/////////////////////////////////////////////////')

      return {
        chartData: output,
        chartLegend,
      };
    }

    // if (output.type === 'column2D') {
    //     if (type === CHART_TYPE.S_MILK) {
    //         // for (let i = 0; i < output.dataSource.data.length; i++) {
    //         //     if (output.dataSource.data[i].value.length > 0) {
    //         //         yMaxValue = parseInt(output.dataSource.data[i].value) > yMaxValue ? parseInt(output.dataSource.data[i].value) : yMaxValue;

    //         //     }
    //         // }

    //         // const yMaxValueParsed = Math.ceil(yMaxValue / 5);
    //         // const yAxisMaxValue = yMaxValueParsed === 1 ? 2 : yMaxValueParsed;
    //         // const numDivLines = yMaxValueParsed === 1 ? 1 : yMaxValueParsed - 1;

    //         // output.dataSource.chart = {
    //         //     ...output.dataSource.chart,
    //         //     adjustDiv: 0,
    //         //     numDivLines: numDivLines,
    //         //     yaxismaxvalue: yAxisMaxValue * 5,
    //         //     yaxisminvalue: yMinValue,
    //         //     formatNumberScale: 0,
    //         // };
    //     }
    //     else if (type === CHART_TYPE.S_SHIT_PEE) {
    //         for (let i = 0; i < output.dataSource.data.length; i++) {
    //             if (output.dataSource.data[i].value.length > 0) {
    //                 yMaxValue = parseInt(output.dataSource.data[i].value) > yMaxValue ? parseInt(output.dataSource.data[i].value) : yMaxValue;
    //             }
    //         }

    //         output.dataSource.chart = {
    //             ...output.dataSource.chart,
    //             adjustDiv: 0,
    //             numDivLines: yMaxValue,
    //             yaxismaxvalue: yMaxValue + 1,
    //             yaxisminvalue: yMinValue,
    //             formatNumberScale: 0,
    //         };
    //     }
    // }

    // console.log('sales line chart output');
    // console.log(output);

    return output;
  } catch (ex) {
    console.log('chart error');
    console.log(ex);

    return false;
  }
};

export const getDataForChartReportProductSales = (
  productSalesTemp,
  outletItems,
  allOutlets,
  startDate,
  endDate,
  yAxisKey,
  xAxisKey,

  reportDisplayType = REPORT_DISPLAY_TYPE.DAY,
  outletShifts = [],

  selectedOutletShift = {},
  currOutletShift = { uniqueId: '' },
) => {
  try {
    // endDate = moment(endDate).add(1, 'day');

    // console.log('data before chart');

    // // endDate = moment(endDate).add(1, 'day');

    const productSales = productSalesTemp
      .slice(0)
      .sort((a, b) => b.totalSales - a.totalSales)
      .slice(0, 50); // from 10 to 50

    var parsedData = [];

    var yMinValue = 0;
    var yMaxValue = 0;

    var category = [];
    var datasetDict = {};

    if (xAxisKey === CHART_X_AXIS_TYPE.TIME) {
      if (
        moment(endDate).diff(startDate, 'day') <=
        moment(startDate).daysInMonth()
      ) {
        endDate = moment(endDate).add(1, 'day');

        const diffDays = moment(endDate).diff(startDate, 'day');

        var prevDate = moment(startDate).add(-1, 'month');

        for (var i = 0; i < diffDays; i++) {
          const currDate = moment(startDate).add(i, 'day');

          category.push({
            //'label': moment(currDate).format('DD MMM'),
            label: !moment(currDate).isSame(prevDate, 'month')
              ? moment(currDate).format('DD MMM')
              : moment(currDate).format('DD'),
            //'label': (i > 1 ? moment(currDate).format('DD') : moment(currDate).format('DD MMM'))
          });

          prevDate = currDate;

          var currTotal = 0;

          for (var j = 0; j < productSales.length; j++) {
            const currProduct = productSales[j];

            if (datasetDict[currProduct.productName]) {
              // do nothing for now
            } else {
              datasetDict[currProduct.productName] = {
                seriesname: currProduct.productName,
                data: Array.from(Array(diffDays)).map(() => {
                  return { value: 0 };
                }),
              };
            }

            for (var k = 0; k < outletItems.length; k++) {
              const currItem = outletItems[k];

              if (
                currItem.itemId === currProduct.productId &&
                // moment(currDate).isSame(currItem.orderCompletedDate, 'day')
                compareOrderDateByDisplayType(
                  currDate,
                  currItem.orderCompletedDate,
                  DATE_COMPARE_TYPE.IS_SAME,
                  'day',
                  reportDisplayType,
                  outletShifts
                )
              ) {
                // means same item

                // datasetDict[currProduct.productName].data[i].value += currItem.price * currItem.quantity;
                datasetDict[currProduct.productName].data[i].value +=
                  currItem[yAxisKey];
              }
            }

            currTotal += datasetDict[currProduct.productName].data[i].value;
            datasetDict[currProduct.productName].data[i].value =
              datasetDict[currProduct.productName].data[i].value.toFixed(0);
          }

          if (currTotal > yMaxValue) {
            yMaxValue = currTotal;
          }
        }
      } else if (moment(endDate).diff(startDate, 'month') <= 6) {
        var currWeekCount = 0;
        var prevMonth = moment(startDate);

        const diffWeeks = moment(endDate).diff(startDate, 'week');

        for (var i = 0; i < diffWeeks; i++) {
          const currDate = moment(startDate).add(i, 'week');

          if (moment(currDate).isSame(prevMonth, 'month')) {
            currWeekCount++;
          } else {
            prevMonth = moment(currDate);

            currWeekCount = 0;
            currWeekCount++;
          }

          category.push({
            // label: moment(currDate).format('MMM') + ' W' + currWeekCount,
            label:
              `${moment(currDate).format('MMM')
              } W${countWeekdayOccurrencesInMonth(currDate)}`,
          });

          var currTotal = 0;

          for (var j = 0; j < productSales.length; j++) {
            const currProduct = productSales[j];

            if (datasetDict[currProduct.productName]) {
              // do nothing for now
            } else {
              datasetDict[currProduct.productName] = {
                seriesname: currProduct.productName,
                data: Array.from(Array(diffWeeks)).map(() => {
                  return { value: 0 };
                }),
              };
            }

            for (var k = 0; k < outletItems.length; k++) {
              const currItem = outletItems[k];

              if (
                currItem.itemId === currProduct.productId &&
                // moment(currDate).isSame(currItem.orderCompletedDate, 'week')
                compareOrderDateByDisplayType(
                  currDate,
                  currItem.orderCompletedDate,
                  DATE_COMPARE_TYPE.IS_SAME,
                  'week',
                  reportDisplayType,
                  outletShifts
                )
              ) {
                // means same item

                datasetDict[currProduct.productName].data[i].value +=
                  currItem.price * currItem.quantity;
              }
            }

            currTotal += datasetDict[currProduct.productName].data[i].value;
            datasetDict[currProduct.productName].data[i].value =
              datasetDict[currProduct.productName].data[i].value.toFixed(0);
          }

          if (currTotal > yMaxValue) {
            yMaxValue = currTotal;
          }
        }
      }
      // if (moment(endDate).diff(startDate, 'month') <= 6) // for now no need conditions first, for more than 6 months interval
      else {
        const diffYears = moment(endDate).diff(startDate, 'month');

        for (var i = 0; i < diffYears; i++) {
          const currDate = moment(startDate).add(i, 'month');

          category.push({
            label: moment(currDate).format("YY' MMM"),
          });

          var currTotal = 0;

          for (var j = 0; j < productSales.length; j++) {
            const currProduct = productSales[j];

            if (datasetDict[currProduct.productName]) {
              // do nothing for now
            } else {
              datasetDict[currProduct.productName] = {
                seriesname: currProduct.productName,
                data: Array.from(Array(diffYears)).map(() => {
                  return { value: 0 };
                }),
              };
            }

            for (var k = 0; k < outletItems.length; k++) {
              const currItem = outletItems[k];

              if (
                currItem.itemId === currProduct.productId &&
                // moment(currDate).isSame(currItem.orderCompletedDate, 'month')
                compareOrderDateByDisplayType(
                  currDate,
                  currItem.orderCompletedDate,
                  DATE_COMPARE_TYPE.IS_SAME,
                  'month',
                  reportDisplayType,
                  outletShifts
                )
              ) {
                // means same item

                datasetDict[currProduct.productName].data[i].value +=
                  currItem.price * currItem.quantity;
              }
            }

            currTotal += datasetDict[currProduct.productName].data[i].value;
            datasetDict[currProduct.productName].data[i].value =
              datasetDict[currProduct.productName].data[i].value.toFixed(0);
          }

          if (currTotal > yMaxValue) {
            yMaxValue = currTotal;
          }
        }
      }
    } else if (xAxisKey === CHART_X_AXIS_TYPE.OUTLET) {
      for (var i = 0; i < allOutlets.length; i++) {
        const currOutlet = allOutlets[i];

        category.push({
          //'label': moment(currDate).format('DD MMM'),
          label: currOutlet.name,
          //'label': (i > 1 ? moment(currDate).format('DD') : moment(currDate).format('DD MMM'))
        });

        var currTotal = 0;

        for (var j = 0; j < productSales.length; j++) {
          const currProduct = productSales[j];

          if (datasetDict[currProduct.productName]) {
            // do nothing for now
          } else {
            datasetDict[currProduct.productName] = {
              seriesname: currProduct.productName,
              data: Array.from(Array(allOutlets.length)).map(() => {
                return { value: 0 };
              }),
            };
          }

          for (var k = 0; k < outletItems.length; k++) {
            const currItem = outletItems[k];

            if (
              currItem.itemId === currProduct.productId &&
              moment(startDate).isSameOrBefore(currItem.orderCompletedDate) &&
              moment(endDate).isAfter(currItem.orderCompletedDate) &&
              currOutlet.uniqueId === currItem.outletId
            ) {
              // means same item

              // datasetDict[currProduct.productName].data[i].value += currItem.price * currItem.quantity;
              datasetDict[currProduct.productName].data[i].value +=
                currItem[yAxisKey];
            }
          }

          currTotal += datasetDict[currProduct.productName].data[i].value;
          datasetDict[currProduct.productName].data[i].value =
            datasetDict[currProduct.productName].data[i].value.toFixed(0);
        }

        if (currTotal > yMaxValue) {
          yMaxValue = currTotal;
        }
      }
    }

    // console.log('category');
    // console.log(category);
    // console.log('datasetDict');
    // console.log(datasetDict);

    var chartLegend = [];

    const dataset = Object.entries(datasetDict).map(([key, value], index) => {
      chartLegend.push({
        itemName: value.seriesname,
        itemSku: value.seriesname,
        chartColor: CHART_COLORS[CHART_TYPE.REPORT_PRODUCT_SALES][index],
      });

      return {
        ...value,
        color: CHART_COLORS[CHART_TYPE.REPORT_PRODUCT_SALES][index],
      };
    });

    var output = {};

    output.type = CHART_DATA[CHART_TYPE.REPORT_PRODUCT_SALES].type;
    output.dataFormat = CHART_DATA[CHART_TYPE.REPORT_PRODUCT_SALES].dataFormat;
    output.dataSource = {
      chart: CHART_DATA[CHART_TYPE.REPORT_PRODUCT_SALES].chart,

      categories: [
        {
          category,
        },
      ],
      dataset,
    };

    // const yAxisStep = Math.ceil((yMaxValue / 10) / 10) * 10;

    // const yMaxValueParsed = Math.ceil(yMaxValue / yAxisStep);
    // const yAxisMaxValue = yMaxValueParsed === 1 ? 2 : yMaxValueParsed;
    // const numDivLines = yMaxValueParsed === 1 ? 1 : yMaxValueParsed - 1;

    const baseNumberLength = yMaxValue.toFixed(0).length - 1;
    const baseNumber = parseInt(
      `1${Array(baseNumberLength)
        .fill()
        .map((num) => 0)
        .join('')}`,
    );
    var maxNumber = baseNumber;

    if (yMaxValue > baseNumber * 5) {
      maxNumber = baseNumber * 10;
    } else if (yMaxValue > baseNumber * 3) {
      maxNumber = baseNumber * 5;
    } else if (yMaxValue > baseNumber * 2) {
      maxNumber = baseNumber * 3;
    } else {
      maxNumber = baseNumber * 2;
    }

    maxNumber = Math.max(maxNumber, 100);

    output.dataSource.chart = {
      ...output.dataSource.chart,
      labelDisplay: 'WRAP',
      // labelStep: 2,
      bgColor: '#ffffff',
      canvasBgColor: '#ffffff',
      bgAlpha: 100,

      // showYAxisValues: 0,
      // yaxismaxvalue: yAxisMaxValue * yAxisStep,
      yaxismaxvalue: maxNumber,
      yaxisminvalue: yMinValue,
      adjustDiv: 0,
      // numDivLines: numDivLines,
      numDivLines: 9,

      // yaxisminvalue: 0,
      formatNumberScale: 0,
      decimals: 0,
    };

    // console.log('chart output');
    // console.log(output);

    return {
      chartData: output,
      chartLegend,
    };
  } catch (ex) {
    console.log('chart error');
    console.log(ex);
    return false;
  }
};

export const getDataForChartReportUpsellSales = (
  productSalesTemp,
  outletItems,
  allOutlets,
  startDate,
  endDate,
  yAxisKey,
  xAxisKey,

  productSalesOriginal,
  productSalesUpsellingParam,
  filterProductIdOriginal,
  filterProductIdUpselling,

  outletItemsUpselling,

  reportDisplayType = REPORT_DISPLAY_TYPE.DAY,
  outletShifts = [],
) => {
  try {
    // endDate = moment(endDate).add(1, 'day');

    // console.log('data before chart');

    // // endDate = moment(endDate).add(1, 'day');

    // const productSales = productSalesTemp
    //   .slice(0)
    //   .sort((a, b) => b.totalSales - a.totalSales)
    //   .slice(0, 10);

    const productSales = productSalesOriginal
      .filter(a => a.productId === filterProductIdOriginal)
      .slice(0)
      // .sort((a, b) => b.totalSales - a.totalSales)
      // .slice(0, 10)
      ;

    const productSalesUpselling = productSalesUpsellingParam
      .filter(a => a.productId === filterProductIdUpselling)
      .slice(0)
      // .sort((a, b) => b.totalSales - a.totalSales)
      // .slice(0, 10)
      ;

    var parsedData = [];

    var yMinValue = 0;
    var yMaxValue = 0;

    var category = [];
    var datasetDict = {};

    if (xAxisKey === CHART_X_AXIS_TYPE.TIME) {
      if (
        moment(endDate).diff(startDate, 'day') <=
        moment(startDate).daysInMonth()
      ) {
        endDate = moment(endDate).add(1, 'day');

        const diffDays = moment(endDate).diff(startDate, 'day');

        var prevDate = moment(startDate).add(-1, 'month');

        for (var i = 0; i < diffDays; i++) {
          const currDate = moment(startDate).add(i, 'day');

          category.push({
            //'label': moment(currDate).format('DD MMM'),
            label: !moment(currDate).isSame(prevDate, 'month')
              ? moment(currDate).format('DD MMM')
              : moment(currDate).format('DD'),
            //'label': (i > 1 ? moment(currDate).format('DD') : moment(currDate).format('DD MMM'))
          });

          prevDate = currDate;

          var currTotal = 0;

          for (var j = 0; j < productSales.length; j++) {
            const currProduct = productSales[j];

            if (datasetDict[currProduct.productName]) {
              // do nothing for now
            } else {
              datasetDict[currProduct.productName] = {
                seriesname: currProduct.productName,
                data: Array.from(Array(diffDays)).map(() => {
                  return { value: 0 };
                }),
              };
            }

            for (var k = 0; k < outletItems.length; k++) {
              const currItem = outletItems[k];

              if (
                currItem.itemId === currProduct.productId &&
                // moment(currDate).isSame(currItem.orderCompletedDate, 'day')
                compareOrderDateByDisplayType(
                  currDate,
                  currItem.orderCompletedDate,
                  DATE_COMPARE_TYPE.IS_SAME,
                  'day',
                  reportDisplayType,
                  outletShifts
                )
              ) {
                // means same item

                // datasetDict[currProduct.productName].data[i].value += currItem.price * currItem.quantity;
                datasetDict[currProduct.productName].data[i].value +=
                  currItem[yAxisKey];
              }
            }

            currTotal += datasetDict[currProduct.productName].data[i].value;
            datasetDict[currProduct.productName].data[i].value =
              datasetDict[currProduct.productName].data[i].value.toFixed(0);
          }

          if (currTotal > yMaxValue) {
            yMaxValue = currTotal;
          }

          ////////////////////////////////////////////

          currTotal = 0;

          for (var j = 0; j < productSalesUpselling.length; j++) {
            const currProduct = productSalesUpselling[j];

            if (datasetDict[`${currProduct.productName} (Upselling)`]) {
              // do nothing for now
            } else {
              datasetDict[`${currProduct.productName} (Upselling)`] = {
                seriesname: `${currProduct.productName} (Upselling)`,
                data: Array.from(Array(diffDays)).map(() => {
                  return { value: 0 };
                }),
                // dashed: 1,
              };
            }

            for (var k = 0; k < outletItemsUpselling.length; k++) {
              const currItem = outletItemsUpselling[k];

              if (
                currItem.itemId === currProduct.productId &&
                // moment(currDate).isSame(currItem.orderCompletedDate, 'day')
                compareOrderDateByDisplayType(
                  currDate,
                  currItem.orderCompletedDate,
                  DATE_COMPARE_TYPE.IS_SAME,
                  'day',
                  reportDisplayType,
                  outletShifts
                )
              ) {
                // means same item

                // datasetDict[currProduct.productName].data[i].value += currItem.price * currItem.quantity;
                datasetDict[`${currProduct.productName} (Upselling)`].data[i].value +=
                  currItem[yAxisKey];
              }
            }

            currTotal += datasetDict[`${currProduct.productName} (Upselling)`].data[i].value;
            datasetDict[`${currProduct.productName} (Upselling)`].data[i].value =
              datasetDict[`${currProduct.productName} (Upselling)`].data[i].value.toFixed(0);
          }

          if (currTotal > yMaxValue) {
            yMaxValue = currTotal;
          }
        }
      } else if (moment(endDate).diff(startDate, 'month') <= 6) {
        var currWeekCount = 0;
        var prevMonth = moment(startDate);

        const diffWeeks = moment(endDate).diff(startDate, 'week');

        for (var i = 0; i < diffWeeks; i++) {
          const currDate = moment(startDate).add(i, 'week');

          if (moment(currDate).isSame(prevMonth, 'month')) {
            currWeekCount++;
          } else {
            prevMonth = moment(currDate);

            currWeekCount = 0;
            currWeekCount++;
          }

          category.push({
            // label: moment(currDate).format('MMM') + ' W' + currWeekCount,
            label:
              `${moment(currDate).format('MMM')
              } W${countWeekdayOccurrencesInMonth(currDate)}`,
          });

          var currTotal = 0;

          for (var j = 0; j < productSales.length; j++) {
            const currProduct = productSales[j];

            if (datasetDict[currProduct.productName]) {
              // do nothing for now
            } else {
              datasetDict[currProduct.productName] = {
                seriesname: currProduct.productName,
                data: Array.from(Array(diffWeeks)).map(() => {
                  return { value: 0 };
                }),
              };
            }

            for (var k = 0; k < outletItems.length; k++) {
              const currItem = outletItems[k];

              if (
                currItem.itemId === currProduct.productId &&
                // moment(currDate).isSame(currItem.orderCompletedDate, 'week')
                compareOrderDateByDisplayType(
                  currDate,
                  currItem.orderCompletedDate,
                  DATE_COMPARE_TYPE.IS_SAME,
                  'week',
                  reportDisplayType,
                  outletShifts
                )
              ) {
                // means same item

                datasetDict[currProduct.productName].data[i].value +=
                  currItem.price * currItem.quantity;
              }
            }

            currTotal += datasetDict[currProduct.productName].data[i].value;
            datasetDict[currProduct.productName].data[i].value =
              datasetDict[currProduct.productName].data[i].value.toFixed(0);
          }

          if (currTotal > yMaxValue) {
            yMaxValue = currTotal;
          }

          ////////////////////////////////////////////////////////////////////

          currTotal = 0;

          for (var j = 0; j < productSalesUpselling.length; j++) {
            const currProduct = productSalesUpselling[j];

            if (datasetDict[`${currProduct.productName} (Upselling)`]) {
              // do nothing for now
            } else {
              datasetDict[`${currProduct.productName} (Upselling)`] = {
                seriesname: `${currProduct.productName} (Upselling)`,
                data: Array.from(Array(diffWeeks)).map(() => {
                  return { value: 0 };
                }),
              };
            }

            for (var k = 0; k < outletItemsUpselling.length; k++) {
              const currItem = outletItemsUpselling[k];

              if (
                currItem.itemId === currProduct.productId &&
                // moment(currDate).isSame(currItem.orderCompletedDate, 'week')
                compareOrderDateByDisplayType(
                  currDate,
                  currItem.orderCompletedDate,
                  DATE_COMPARE_TYPE.IS_SAME,
                  'week',
                  reportDisplayType,
                  outletShifts
                )
              ) {
                // means same item

                datasetDict[`${currProduct.productName} (Upselling)`].data[i].value +=
                  currItem.price * currItem.quantity;
              }
            }

            currTotal += datasetDict[`${currProduct.productName} (Upselling)`].data[i].value;
            datasetDict[`${currProduct.productName} (Upselling)`].data[i].value =
              datasetDict[`${currProduct.productName} (Upselling)`].data[i].value.toFixed(0);
          }

          if (currTotal > yMaxValue) {
            yMaxValue = currTotal;
          }
        }
      }
      // if (moment(endDate).diff(startDate, 'month') <= 6) // for now no need conditions first, for more than 6 months interval
      else {
        const diffYears = moment(endDate).diff(startDate, 'month');

        for (var i = 0; i < diffYears; i++) {
          const currDate = moment(startDate).add(i, 'month');

          category.push({
            label: moment(currDate).format("YY' MMM"),
          });

          var currTotal = 0;

          for (var j = 0; j < productSales.length; j++) {
            const currProduct = productSales[j];

            if (datasetDict[currProduct.productName]) {
              // do nothing for now
            } else {
              datasetDict[currProduct.productName] = {
                seriesname: currProduct.productName,
                data: Array.from(Array(diffYears)).map(() => {
                  return { value: 0 };
                }),
              };
            }

            for (var k = 0; k < outletItems.length; k++) {
              const currItem = outletItems[k];

              if (
                currItem.itemId === currProduct.productId &&
                // moment(currDate).isSame(currItem.orderCompletedDate, 'month')
                compareOrderDateByDisplayType(
                  currDate,
                  currItem.orderCompletedDate,
                  DATE_COMPARE_TYPE.IS_SAME,
                  'month',
                  reportDisplayType,
                  outletShifts
                )
              ) {
                // means same item

                datasetDict[currProduct.productName].data[i].value +=
                  currItem.price * currItem.quantity;
              }
            }

            currTotal += datasetDict[currProduct.productName].data[i].value;
            datasetDict[currProduct.productName].data[i].value =
              datasetDict[currProduct.productName].data[i].value.toFixed(0);
          }

          if (currTotal > yMaxValue) {
            yMaxValue = currTotal;
          }

          //////////////////////////////////////////////////////////

          currTotal = 0;

          for (var j = 0; j < productSalesUpselling.length; j++) {
            const currProduct = productSalesUpselling[j];

            if (datasetDict[`${currProduct.productName} (Upselling)`]) {
              // do nothing for now
            } else {
              datasetDict[`${currProduct.productName} (Upselling)`] = {
                seriesname: `${currProduct.productName} (Upselling)`,
                data: Array.from(Array(diffYears)).map(() => {
                  return { value: 0 };
                }),
              };
            }

            for (var k = 0; k < outletItemsUpselling.length; k++) {
              const currItem = outletItemsUpselling[k];

              if (
                currItem.itemId === currProduct.productId &&
                // moment(currDate).isSame(currItem.orderCompletedDate, 'month')
                compareOrderDateByDisplayType(
                  currDate,
                  currItem.orderCompletedDate,
                  DATE_COMPARE_TYPE.IS_SAME,
                  'month',
                  reportDisplayType,
                  outletShifts
                )
              ) {
                // means same item

                datasetDict[`${currProduct.productName} (Upselling)`].data[i].value +=
                  currItem.price * currItem.quantity;
              }
            }

            currTotal += datasetDict[`${currProduct.productName} (Upselling)`].data[i].value;
            datasetDict[`${currProduct.productName} (Upselling)`].data[i].value =
              datasetDict[`${currProduct.productName} (Upselling)`].data[i].value.toFixed(0);
          }

          if (currTotal > yMaxValue) {
            yMaxValue = currTotal;
          }
        }
      }
    }
    // else if (xAxisKey === CHART_X_AXIS_TYPE.OUTLET) {
    //   for (var i = 0; i < allOutlets.length; i++) {
    //     const currOutlet = allOutlets[i];

    //     category.push({
    //       //'label': moment(currDate).format('DD MMM'),
    //       label: currOutlet.name,
    //       //'label': (i > 1 ? moment(currDate).format('DD') : moment(currDate).format('DD MMM'))
    //     });

    //     var currTotal = 0;

    //     for (var j = 0; j < productSales.length; j++) {
    //       const currProduct = productSales[j];

    //       if (datasetDict[currProduct.productName]) {
    //         // do nothing for now
    //       } else {
    //         datasetDict[currProduct.productName] = {
    //           seriesname: currProduct.productName,
    //           data: Array.from(Array(allOutlets.length)).map(() => {
    //             return { value: 0 };
    //           }),
    //         };
    //       }

    //       for (var k = 0; k < outletItems.length; k++) {
    //         const currItem = outletItems[k];

    //         if (
    //           currItem.itemId === currProduct.productId &&
    //           moment(startDate).isSameOrBefore(currItem.orderCompletedDate) &&
    //           moment(endDate).isAfter(currItem.orderCompletedDate) &&
    //           currOutlet.uniqueId === currItem.outletId
    //         ) {
    //           // means same item

    //           // datasetDict[currProduct.productName].data[i].value += currItem.price * currItem.quantity;
    //           datasetDict[currProduct.productName].data[i].value +=
    //             currItem[yAxisKey];
    //         }
    //       }

    //       currTotal += datasetDict[currProduct.productName].data[i].value;
    //       datasetDict[currProduct.productName].data[i].value =
    //         datasetDict[currProduct.productName].data[i].value.toFixed(0);
    //     }

    //     if (currTotal > yMaxValue) {
    //       yMaxValue = currTotal;
    //     }
    //   }
    // }

    // console.log('category');
    // console.log(category);
    // console.log('datasetDict');
    // console.log(datasetDict);

    var chartLegend = [];

    const dataset = Object.entries(datasetDict).map(([key, value], index) => {
      chartLegend.push({
        itemName: value.seriesname,
        itemSku: value.seriesname,
        chartColor: CHART_COLORS[CHART_TYPE.REPORT_UPSELL_SALES][index],
      });

      return {
        ...value,
        color: CHART_COLORS[CHART_TYPE.REPORT_UPSELL_SALES][index],
      };
    });

    var output = {};

    output.type = CHART_DATA[CHART_TYPE.REPORT_UPSELL_SALES].type;
    output.dataFormat = CHART_DATA[CHART_TYPE.REPORT_UPSELL_SALES].dataFormat;
    output.dataSource = {
      chart: CHART_DATA[CHART_TYPE.REPORT_UPSELL_SALES].chart,

      categories: [
        {
          category,
        },
      ],
      dataset,
    };

    // const yAxisStep = Math.ceil((yMaxValue / 10) / 10) * 10;

    // const yMaxValueParsed = Math.ceil(yMaxValue / yAxisStep);
    // const yAxisMaxValue = yMaxValueParsed === 1 ? 2 : yMaxValueParsed;
    // const numDivLines = yMaxValueParsed === 1 ? 1 : yMaxValueParsed - 1;

    const baseNumberLength = yMaxValue.toFixed(0).length - 1;
    const baseNumber = parseInt(
      `1${Array(baseNumberLength)
        .fill()
        .map((num) => 0)
        .join('')}`,
    );
    var maxNumber = baseNumber;

    if (yMaxValue > baseNumber * 5) {
      maxNumber = baseNumber * 10;
    } else if (yMaxValue > baseNumber * 3) {
      maxNumber = baseNumber * 5;
    } else if (yMaxValue > baseNumber * 2) {
      maxNumber = baseNumber * 3;
    } else {
      maxNumber = baseNumber * 2;
    }

    maxNumber = Math.max(maxNumber, 100);

    output.dataSource.chart = {
      ...output.dataSource.chart,
      labelDisplay: 'WRAP',
      // labelStep: 2,
      bgColor: '#ffffff',
      canvasBgColor: '#ffffff',
      bgAlpha: 100,

      // showYAxisValues: 0,
      // yaxismaxvalue: yAxisMaxValue * yAxisStep,
      yaxismaxvalue: maxNumber,
      yaxisminvalue: yMinValue,
      adjustDiv: 0,
      // numDivLines: numDivLines,
      numDivLines: 9,

      // yaxisminvalue: 0,
      formatNumberScale: 0,
      decimals: 0,
    };

    // console.log('chart output');
    // console.log(output);

    return {
      chartData: output,
      chartLegend,
    };
  } catch (ex) {
    console.log('chart error');
    console.log(ex);
    return false;
  }
};

export const filterChartItems = (item, appliedChartFilterQueries) => {
  var isValid = false;

  if (appliedChartFilterQueries.length > 0) {
    var matchedFilterNum = 0;

    for (var i = 0; i < appliedChartFilterQueries.length; i++) {
      const appliedChartFilterQuery = appliedChartFilterQueries[i];

      if (appliedChartFilterQuery.fieldNameType === CHART_FIELD_TYPE.STRING) {
        // proceed with string type

        if (appliedChartFilterQuery.fieldSpecial === 'cartItems') {
          // cartItems field

          if (
            appliedChartFilterQuery.fieldCompare === CHART_FIELD_COMPARE_DICT.IS
          ) {
            var isMatched = false;

            for (
              var i = 0;
              i < item[appliedChartFilterQuery.fieldSpecial].length;
              i++
            ) {
              if (
                item[appliedChartFilterQuery.fieldSpecial][i][
                appliedChartFilterQuery.fieldNameKey
                ] === appliedChartFilterQuery.fieldDataValue
              ) {
                matchedFilterNum++;
                isMatched = true;
                break;
              }
            }

            if (isMatched) {
              continue;
            }
          } else if (
            appliedChartFilterQuery.fieldCompare ===
            CHART_FIELD_COMPARE_DICT.LIKE
          ) {
            try {
              var isMatched = false;

              for (
                var i = 0;
                i < item[appliedChartFilterQuery.fieldSpecial].length;
                i++
              ) {
                if (
                  appliedChartFilterQuery.fieldDataValue
                    .toLowerCase()
                    .includes(
                      item[appliedChartFilterQuery.fieldSpecial][i][
                        appliedChartFilterQuery.fieldNameKey
                      ].toLowerCase(),
                    ) ||
                  item[appliedChartFilterQuery.fieldSpecial][i][
                    appliedChartFilterQuery.fieldNameKey
                  ]
                    .toLowerCase()
                    .includes(
                      appliedChartFilterQuery.fieldDataValue.toLowerCase(),
                    )
                ) {
                  matchedFilterNum++;
                  isMatched = true;
                  break;
                }
              }

              if (isMatched) {
                continue;
              }
            } catch (ex) {
              console.log('chart error');
              console.error(ex);
            }
          } else if (
            appliedChartFilterQuery.fieldCompare ===
            CHART_FIELD_COMPARE_DICT.NOT
          ) {
            var isMatched = false;

            for (
              var i = 0;
              i < item[appliedChartFilterQuery.fieldSpecial].length;
              i++
            ) {
              if (
                item[appliedChartFilterQuery.fieldSpecial][i][
                appliedChartFilterQuery.fieldNameKey
                ] !== appliedChartFilterQuery.fieldDataValue
              ) {
                matchedFilterNum++;
                isMatched = true;
                break;
              }
            }

            if (isMatched) {
              continue;
            }
          } else if (
            appliedChartFilterQuery.fieldCompare ===
            CHART_FIELD_COMPARE_DICT.MORE_THAN
          ) {
            var isMatched = false;

            for (
              var i = 0;
              i < item[appliedChartFilterQuery.fieldSpecial].length;
              i++
            ) {
              if (
                item[appliedChartFilterQuery.fieldSpecial][i][
                appliedChartFilterQuery.fieldNameKey
                ] > appliedChartFilterQuery.fieldDataValue
              ) {
                matchedFilterNum++;
                isMatched = true;
                break;
              }
            }

            if (isMatched) {
              continue;
            }
          } else if (
            appliedChartFilterQuery.fieldCompare ===
            CHART_FIELD_COMPARE_DICT.MORE_OR_EQUAL_THAN
          ) {
            var isMatched = false;

            for (
              var i = 0;
              i < item[appliedChartFilterQuery.fieldSpecial].length;
              i++
            ) {
              if (
                item[appliedChartFilterQuery.fieldSpecial][i][
                appliedChartFilterQuery.fieldNameKey
                ] >= appliedChartFilterQuery.fieldDataValue
              ) {
                matchedFilterNum++;
                isMatched = true;
                break;
              }
            }

            if (isMatched) {
              continue;
            }
          } else if (
            appliedChartFilterQuery.fieldCompare ===
            CHART_FIELD_COMPARE_DICT.LESS_THAN
          ) {
            var isMatched = false;

            for (
              var i = 0;
              i < item[appliedChartFilterQuery.fieldSpecial].length;
              i++
            ) {
              if (
                item[appliedChartFilterQuery.fieldSpecial][i][
                appliedChartFilterQuery.fieldNameKey
                ] < appliedChartFilterQuery.fieldDataValue
              ) {
                matchedFilterNum++;
                isMatched = true;
                break;
              }
            }

            if (isMatched) {
              continue;
            }
          } else if (
            appliedChartFilterQuery.fieldCompare ===
            CHART_FIELD_COMPARE_DICT.LESS_OR_EQUAL_THAN
          ) {
            var isMatched = false;

            for (
              var i = 0;
              i < item[appliedChartFilterQuery.fieldSpecial].length;
              i++
            ) {
              if (
                item[appliedChartFilterQuery.fieldSpecial][i][
                appliedChartFilterQuery.fieldNameKey
                ] <= appliedChartFilterQuery.fieldDataValue
              ) {
                matchedFilterNum++;
                isMatched = true;
                break;
              }
            }

            if (isMatched) {
              continue;
            }
          }
        } else {
          // ordinary string field

          if (
            appliedChartFilterQuery.fieldCompare === CHART_FIELD_COMPARE_DICT.IS
          ) {
            if (
              item[appliedChartFilterQuery.fieldNameKey] ===
              appliedChartFilterQuery.fieldDataValue
            ) {
              matchedFilterNum++;
              continue;
            }
          } else if (
            appliedChartFilterQuery.fieldCompare ===
            CHART_FIELD_COMPARE_DICT.LIKE
          ) {
            try {
              if (
                (item[appliedChartFilterQuery.fieldNameKey].length > 0 &&
                  appliedChartFilterQuery.fieldDataValue
                    .toLowerCase()
                    .includes(
                      item[appliedChartFilterQuery.fieldNameKey].toLowerCase(),
                    )) ||
                item[appliedChartFilterQuery.fieldNameKey]
                  .toLowerCase()
                  .includes(
                    appliedChartFilterQuery.fieldDataValue.toLowerCase(),
                  )
              ) {
                matchedFilterNum++;
                continue;
              }
            } catch (ex) {
              console.log('chart error');
              console.error(ex);
            }
          } else if (
            appliedChartFilterQuery.fieldCompare ===
            CHART_FIELD_COMPARE_DICT.NOT
          ) {
            if (
              item[appliedChartFilterQuery.fieldNameKey] !==
              appliedChartFilterQuery.fieldDataValue
            ) {
              matchedFilterNum++;
              continue;
            }
          } else if (
            appliedChartFilterQuery.fieldCompare ===
            CHART_FIELD_COMPARE_DICT.MORE_THAN
          ) {
            if (
              item[appliedChartFilterQuery.fieldNameKey] >
              appliedChartFilterQuery.fieldDataValue
            ) {
              matchedFilterNum++;
              continue;
            }
          } else if (
            appliedChartFilterQuery.fieldCompare ===
            CHART_FIELD_COMPARE_DICT.MORE_OR_EQUAL_THAN
          ) {
            if (
              item[appliedChartFilterQuery.fieldNameKey] >=
              appliedChartFilterQuery.fieldDataValue
            ) {
              matchedFilterNum++;
              continue;
            }
          } else if (
            appliedChartFilterQuery.fieldCompare ===
            CHART_FIELD_COMPARE_DICT.LESS_THAN
          ) {
            if (
              item[appliedChartFilterQuery.fieldNameKey] <
              appliedChartFilterQuery.fieldDataValue
            ) {
              matchedFilterNum++;
              continue;
            }
          } else if (
            appliedChartFilterQuery.fieldCompare ===
            CHART_FIELD_COMPARE_DICT.LESS_OR_EQUAL_THAN
          ) {
            if (
              item[appliedChartFilterQuery.fieldNameKey] <=
              appliedChartFilterQuery.fieldDataValue
            ) {
              matchedFilterNum++;
              continue;
            }
          }
        }
      } else if (
        appliedChartFilterQuery.fieldNameType === CHART_FIELD_TYPE.NUMBER
      ) {
        // proceed with string type

        if (
          appliedChartFilterQuery.fieldCompare === CHART_FIELD_COMPARE_DICT.IS
        ) {
          if (
            item[appliedChartFilterQuery.fieldNameKey] ===
            appliedChartFilterQuery.fieldDataValue
          ) {
            matchedFilterNum++;
            continue;
          }
        } else if (
          appliedChartFilterQuery.fieldCompare === CHART_FIELD_COMPARE_DICT.LIKE
        ) {
          try {
            if (
              item[appliedChartFilterQuery.fieldNameKey] ===
              appliedChartFilterQuery.fieldDataValue
            ) {
              matchedFilterNum++;
              continue;
            }
          } catch (ex) {
            console.log('chart error');
            console.error(ex);
          }
        } else if (
          appliedChartFilterQuery.fieldCompare === CHART_FIELD_COMPARE_DICT.NOT
        ) {
          if (
            item[appliedChartFilterQuery.fieldNameKey] !==
            appliedChartFilterQuery.fieldDataValue
          ) {
            matchedFilterNum++;
            continue;
          }
        } else if (
          appliedChartFilterQuery.fieldCompare ===
          CHART_FIELD_COMPARE_DICT.MORE_THAN
        ) {
          if (
            item[appliedChartFilterQuery.fieldNameKey] >
            appliedChartFilterQuery.fieldDataValue
          ) {
            matchedFilterNum++;
            continue;
          }
        } else if (
          appliedChartFilterQuery.fieldCompare ===
          CHART_FIELD_COMPARE_DICT.MORE_OR_EQUAL_THAN
        ) {
          if (
            item[appliedChartFilterQuery.fieldNameKey] >=
            appliedChartFilterQuery.fieldDataValue
          ) {
            matchedFilterNum++;
            continue;
          }
        } else if (
          appliedChartFilterQuery.fieldCompare ===
          CHART_FIELD_COMPARE_DICT.LESS_THAN
        ) {
          if (
            item[appliedChartFilterQuery.fieldNameKey] <
            appliedChartFilterQuery.fieldDataValue
          ) {
            matchedFilterNum++;
            continue;
          }
        } else if (
          appliedChartFilterQuery.fieldCompare ===
          CHART_FIELD_COMPARE_DICT.LESS_OR_EQUAL_THAN
        ) {
          if (
            item[appliedChartFilterQuery.fieldNameKey] <=
            appliedChartFilterQuery.fieldDataValue
          ) {
            matchedFilterNum++;
            continue;
          }
        }
      } else if (
        appliedChartFilterQuery.fieldNameType === CHART_FIELD_TYPE.DATETIME
      ) {
        // proceed with string type

        if (
          appliedChartFilterQuery.fieldCompare === CHART_FIELD_COMPARE_DICT.IS
        ) {
          if (
            moment(item[appliedChartFilterQuery.fieldNameKey]).isSame(
              appliedChartFilterQuery.fieldDataValue,
              'day',
            )
          ) {
            matchedFilterNum++;
            continue;
          }
        } else if (
          appliedChartFilterQuery.fieldCompare === CHART_FIELD_COMPARE_DICT.LIKE
        ) {
          try {
            if (
              moment(item[appliedChartFilterQuery.fieldNameKey]).isSame(
                appliedChartFilterQuery.fieldDataValue,
                'day',
              )
            ) {
              matchedFilterNum++;
              continue;
            }
          } catch (ex) {
            console.log('chart error');
            console.error(ex);
          }
        } else if (
          appliedChartFilterQuery.fieldCompare === CHART_FIELD_COMPARE_DICT.NOT
        ) {
          if (
            !moment(item[appliedChartFilterQuery.fieldNameKey]).isSame(
              appliedChartFilterQuery.fieldDataValue,
              'day',
            )
          ) {
            matchedFilterNum++;
            continue;
          }
        } else if (
          appliedChartFilterQuery.fieldCompare ===
          CHART_FIELD_COMPARE_DICT.MORE_THAN
        ) {
          if (
            moment(item[appliedChartFilterQuery.fieldNameKey]).isAfter(
              appliedChartFilterQuery.fieldDataValue,
              'day',
            )
          ) {
            matchedFilterNum++;
            continue;
          }
        } else if (
          appliedChartFilterQuery.fieldCompare ===
          CHART_FIELD_COMPARE_DICT.MORE_OR_EQUAL_THAN
        ) {
          if (
            moment(item[appliedChartFilterQuery.fieldNameKey]).isSameOrAfter(
              appliedChartFilterQuery.fieldDataValue,
              'day',
            )
          ) {
            matchedFilterNum++;
            continue;
          }
        } else if (
          appliedChartFilterQuery.fieldCompare ===
          CHART_FIELD_COMPARE_DICT.LESS_THAN
        ) {
          if (
            moment(item[appliedChartFilterQuery.fieldNameKey]).isBefore(
              appliedChartFilterQuery.fieldDataValue,
              'day',
            )
          ) {
            matchedFilterNum++;
            continue;
          }
        } else if (
          appliedChartFilterQuery.fieldCompare ===
          CHART_FIELD_COMPARE_DICT.LESS_OR_EQUAL_THAN
        ) {
          if (
            moment(item[appliedChartFilterQuery.fieldNameKey]).isSameOrBefore(
              appliedChartFilterQuery.fieldDataValue,
              'day',
            )
          ) {
            matchedFilterNum++;
            continue;
          }
        }
      }
    }

    if (matchedFilterNum === appliedChartFilterQueries.length) {
      isValid = true;
    }
  } else {
    isValid = true;
  }

  return isValid;
};

export const getDataForReservationGuestStats = (
  userReservations,
  crmUsers,
  // chartPeriod,
  // yAxisKey,
  startDate = new Date(),
  endDate = new Date(),
) => {
  try {
    // console.log('data before chart');

    var parsedData = [];

    var yMinValue = 0;
    var yMaxValue = 0;

    var category = [];
    var datasetDict = {};

    // const userReservationsFiltered = userReservations.filter(
    //   (reservation) => {
    //     return moment(reservation.createdAt).isSameOrAfter(moment(startDate)) &&
    //       moment(reservation.createdAt).isBefore(moment(endDate));
    //   },
    // );

    const userReservationsFiltered = userReservations;

    const newGuestsNum = userReservationsFiltered.filter(reservation => {
      if (reservation.userId) {
        // check whether is new user or previous user

        // flow: when reservation made, if new user, will create a new crmUser and its createdAt will be later than the reservation's createdAt

        var findResult = crmUsers.find(crmUser => {
          if (crmUser.userId === reservation.userId ||
            crmUser.number === reservation.userPhone ||
            crmUser.userEmail === reservation.userEmail) {
            if (moment(crmUser.createdAt).isAfter(reservation.createdAt)) {
              // means is new

              return true;
            }
            else {
              // means is old

              return false;
            }
          }
          else {
            // not match

            return false;
          }
        });

        if (findResult) {
          // means is the new user

          return true;
        }
        else {
          return false;
        }
      }
      else {
        // if no userId, assume is new

        return true;
      }
    }).length;

    parsedData = [
      {
        'label': 'New guests',
        'value': newGuestsNum.toFixed(0),
      },
      {
        'label': 'Repeat guests',
        'value': (userReservationsFiltered.length - newGuestsNum).toFixed(0),
      },
    ];

    ///////////////////////////////////////////////////////////////

    var output = {};

    output.type = CHART_DATA[CHART_TYPE.RESERVATION_GUEST_STATS].type;
    output.dataFormat =
      CHART_DATA[CHART_TYPE.RESERVATION_GUEST_STATS].dataFormat;
    output.dataSource = {
      chart: CHART_DATA[CHART_TYPE.RESERVATION_GUEST_STATS].chart,

      data: parsedData,
    };

    // // console.log('parsedData');
    // // console.log(parsedData);
    // // console.log(yMaxValue);

    // if (output.type !== 'HLinearGauge') {
    //   const baseNumberLength = yMaxValue.toFixed(0).length - 1;
    //   const baseNumber = parseInt(
    //     '1' +
    //     Array(baseNumberLength)
    //       .fill()
    //       .map((num) => 0)
    //       .join(''),
    //   );
    //   var maxNumber = baseNumber;

    //   if (yMaxValue > baseNumber * 5) {
    //     maxNumber = baseNumber * 10;
    //   } else if (yMaxValue > baseNumber * 3) {
    //     maxNumber = baseNumber * 5;
    //   } else if (yMaxValue > baseNumber * 2) {
    //     maxNumber = baseNumber * 3;
    //   } else {
    //     maxNumber = baseNumber * 2;
    //   }

    //   maxNumber = Math.max(maxNumber, 100);

    //   output.dataSource.chart = {
    //     ...output.dataSource.chart,
    //     labelDisplay: 'WRAP',
    //     // labelStep: 2,
    //     bgColor: '#ffffff',
    //     canvasBgColor: '#ffffff',
    //     bgAlpha: 100,

    //     useEllipsesWhenOverflow: 1,

    //     // showYAxisValues: 0,
    //     // yaxismaxvalue: yAxisMaxValue * yAxisStep,
    //     yaxismaxvalue: maxNumber,
    //     yaxisminvalue: yMinValue,
    //     adjustDiv: 0,
    //     // numDivLines: numDivLines,
    //     numDivLines: 9,

    //     // yaxisminvalue: 0,
    //     formatNumberScale: 0,
    //     decimals: 0,
    //   };

    //   return {
    //     chartData: output,
    //     chartLegend: chartLegend,
    //   };
    // }

    // console.log('sales line chart output');
    // console.log(output);

    return output;
  } catch (ex) {
    console.log('chart error');
    console.log(ex);
    return false;
  }
};

export const getSuitableYAxisMaxValue = (maxValueParam) => {
  var maxValue = maxValueParam;

  if (maxValue <= 10) {
    maxValue = 10;
  }
  else if (maxValue <= 20) {
    maxValue = 20;
  }
  else if (maxValue <= 40) {
    maxValue = 40;
  }
  else if (maxValue <= 50) {
    maxValue = 50;
  }
  else if (maxValue <= 100) {
    maxValue = 100;
  }
  else if (maxValue <= 200) {
    maxValue = 200;
  }
  else if (maxValue <= 400) {
    maxValue = 400;
  }
  else if (maxValue <= 500) {
    maxValue = 500;
  }
  else if (maxValue <= 1000) {
    maxValue = 1000;
  }
  else if (maxValue <= 2000) {
    maxValue = 2000;
  }
  else if (maxValue <= 4000) {
    maxValue = 4000;
  }
  else if (maxValue <= 4000) {
    maxValue = 4000;
  }
  else if (maxValue <= 5000) {
    maxValue = 5000;
  }
  else if (maxValue <= 10000) {
    maxValue = 10000;
  }
  else {
    // do nothing
  }

  return maxValue;
};
