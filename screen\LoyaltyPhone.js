import { Text } from "react-native-fast-text";
import React, { Component, useReducer, useState, useEffect, useCallback } from 'react';
import {
  StyleSheet,
  Image,
  View,
  Alert,
  TouchableOpacity,
  Modal as ModalComponent,
  Dimensions,
  TextInput,
  KeyboardAvoidingView,
  ActivityIndicator,
  Touchable,
  Platform,
  useWindowDimensions,
} from 'react-native';
import { ModalView as ModalViewComponent } from 'react-native-multiple-modals';
import { ScrollView, FlatList } from "react-native-gesture-handler";
import Colors from '../constant/Colors';
import Styles from '../constant/Styles';
import SideBar from './SideBar';
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as User from '../util/User';
import Icon from 'react-native-vector-icons/Ionicons';
import ApiClient from '../util/ApiClient';
import API from '../constant/API';
import DropDownPicker from 'react-native-dropdown-picker';
import Feather from 'react-native-vector-icons/Feather';
import EvilIcons from 'react-native-vector-icons/EvilIcons';
import moment from 'moment';
import DateTimePickerModal from 'react-native-modal-datetime-picker';
import {
  isTablet
} from '../util/common';
import {
  MERCHANT_VOUCHER_CODE_FORMAT,
  MERCHANT_VOUCHER_TYPE,
  SEGMENT_TYPE,
  EXPAND_TAB_TYPE,
} from '../constant/common';
import { CommonStore } from '../store/commonStore';
import { UserStore } from '../store/userStore';
import { MerchantStore } from '../store/merchantStore';
import { OutletStore } from '../store/outletStore';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import RNPickerSelect from 'react-native-picker-select';
import { useKeyboard } from '../hooks';
import { get } from 'react-native/Libraries/Utilities/PixelRatio';
import AsyncImage from '../components/asyncImage';
import Entypo from 'react-native-vector-icons/Entypo';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
// import { DIMENTIONS } from 'react-native-numeric-input';
import AntDesign from 'react-native-vector-icons/AntDesign';
import { LOYALTY_CAMPAIGN_CREDIT_TRANSACTION_TYPE_PARSED } from '../constant/loyalty';
import GCalendar from '../assets/svg/GCalendar';
import APILocal from '../util/apiLocalReplacers';
// import { Row } from 'react-native-table-component';
import { useFocusEffect } from '@react-navigation/native';
import UserIdleWrapper from '../components/userIdleWrapper';

//////////////////////////////////////////////////////////////////////////////////////////////////////////
const ModalView = Platform.OS === 'android' ? ModalComponent : ModalViewComponent;

const CREDIT_SECTION = {
  HOME: 'HOME',
  REGISTER: 'REGISTER',
  ADD_CASHBACK: 'ADD_CASHBACK',
  REDEEM_CREDIT: 'REDEEM_CREDIT',
  ADD_CREDIT: 'ADD_CREDIT',
  DEDUCT_CREDIT: 'DEDUCT_CREDIT',
};

//////////////////////////////////////////////////////////////////////////////////////////////////////////

const LoyaltyPhone = (props) => {
  const { navigation } = props;

  ///////////////////////////////////////////////////////////

  const [isMounted, setIsMounted] = useState(true);

  useFocusEffect(
    useCallback(() => {
      setIsMounted(true);
      return () => {
        setIsMounted(false);
      };
    }, [])
  );

  ///////////////////////////////////////////////////////////

  const { width: windowWidth, height: windowHeight } = useWindowDimensions();

  //////////////////////////////////////////////////////////////////////////////////////////////////////////

  const merchantUserId = UserStore.useState((s) => s.firebaseUid);
  const merchantUserName = UserStore.useState((s) => s.name);
  const merchantId = UserStore.useState((s) => s.merchantId);
  const merchantName = MerchantStore.useState((s) => s.name);
  const merchantLogo = MerchantStore.useState((s) => s.logo);

  const allOutlets = MerchantStore.useState((s) => s.allOutlets);

  const isLoading = CommonStore.useState((s) => s.isLoading);
  const [switchMerchant, setSwitchMerchant] = useState(isTablet() ? false : true);
  const allOutletsItemsSkuDict = OutletStore.useState(
    (s) => s.allOutletsItemsSkuDict,
  );
  const outletSelectDropdownView = CommonStore.useState(
    (s) => s.outletSelectDropdownView,
  );

  const [startDate, setStartDate] = useState(moment());
  const [showStartDatePicker, setShowStartDatePicker] = useState(false);

  const currOutlet = MerchantStore.useState((s) => s.currOutlet);
  const crmUsers = OutletStore.useState((s) => s.crmUsers);

  const selectedCustomerLCCTransactions = OutletStore.useState(
    (s) => s.selectedCustomerLCCTransactions,
  );
  const selectedCustomerLCCBalance = OutletStore.useState(
    (s) => s.selectedCustomerLCCBalance,
  );

  const selectedCustomerEdit = CommonStore.useState(
    (s) => s.selectedCustomerEdit,
  );

  const loyaltyCampaigns = OutletStore.useState((s) => s.loyaltyCampaigns);

  const selectedCustomerUserLoyaltyCampaigns = OutletStore.useState(
    (s) => s.selectedCustomerUserLoyaltyCampaigns,
  );

  const taggableVouchers = OutletStore.useState((s) => s.taggableVouchers);

  const selectedCustomerUserTaggableVouchers = OutletStore.useState(
    (s) => s.selectedCustomerUserTaggableVouchersView,
  );

  const [creditSection, setCreditSection] = useState(CREDIT_SECTION.ADD_CASHBACK);

  const [phoneNumber, setPhoneNumber] = useState('+60');
  const [userName, setUserName] = useState('');
  const [cashbackModal, setCashbackModal] = useState(false);
  const [cashbackdoneModal, setCashbackdoneModal] = useState(false);
  const [redeemCreditModal, setRedeemCreditModal] = useState(false);
  const [addCreditModal, setAddCreditModal] = useState(false);
  const [deductCreditModal, setDeductCreditModal] = useState(false);
  const [HistoryModal, setHistoryModal] = useState(false);
  const [campaignModal, setCampaignModal] = useState(false);
  const [amount, setAmount] = useState('');
  const [addnotes, setAddnotes] = useState(false);
  const [notetext, setNotetext] = useState('');
  const [registermodal, setRegistermodal] = useState(false);
  const [registerDetail, setRegisterDetail] = useState(false);
  const [redeemdone, setRedeemdone] = useState(false);
  const [addCreditdone, setAddCreditdone] = useState(false);
  const [deductCreditdone, setDeductCreditdone] = useState(false);
  const [rev_date, setRev_date] = useState(moment().startOf('day'));
  const [rev_date1, setRev_date1] = useState(moment().endOf('day'));
  const [showDateTimePicker, setShowDateTimePicker] = useState(false);
  const [email, setEmail] = useState('');

  const [phoneNumberChecking, setPhoneNumberChecking] = useState(false);
  const [currCRMUser, setCurrCRMUser] = useState(null);

  const isAlphaUser = UserStore.useState((s) => s.isAlphaUser);
  const expandTab = CommonStore.useState((s) => s.expandTab);
  const currPageStack = CommonStore.useState((s) => s.currPageStack);

  useEffect(() => {
    setPhoneNumberChecking(true);

    if (phoneNumber.length === 12 || phoneNumber.length === 13) {
      checkPhoneNumberData();
    }
  }, [phoneNumber, crmUsers]);

  const checkPhoneNumberData = () => {
    const phoneNumberParsed = phoneNumber.slice(2);
    const phoneNumberParsedCountry = phoneNumber.slice(1);

    const crmUser = crmUsers.find(
      (crmUser) =>
        crmUser.number === phoneNumberParsedCountry ||
        crmUser.number === phoneNumberParsed,
    );

    if (crmUser) {
      // means got the same user found

      CommonStore.update((s) => {
        s.selectedCustomerEdit = crmUser;
      });

      setPhoneNumberChecking(false);

      setCurrCRMUser(crmUser);
    } else {
      // no existing user found

      setPhoneNumberChecking(false);

      setCurrCRMUser(null);
    }
  };

  const claimCashback = () => {
    var body = {
      amount: parseFloat(amount),

      userName: userName,
      userPhone: phoneNumber.slice(1),
      // emailAddress: email,
      // dob: startDate,

      merchantId: merchantId,
      merchantName: merchantName,
      merchantLogo: merchantLogo,

      outletId: currOutlet.uniqueId,
      outletName: currOutlet.name,
      outletCover: currOutlet.cover,
    };

    // console.log(body);

    setPhoneNumberChecking(true);

    // ApiClient.POST(API.loyaltyCampaignAddCashback, body, false)
    APILocal.loyaltyCampaignAddCashback({ body: body, uid: merchantUserId })
      .then(
        (result) => {
          if (result && result.status === 'success') {
            Alert.alert(
              'Success',
              'Cashback has been claimed',
              [
                {
                  text: 'OK',
                  onPress: () => {
                    setCashbackModal(false);
                    //setAmount('');
                    //setPhoneNumber('+60');
                    //setCurrCRMUser(null);
                    setPhoneNumberChecking(false);

                    setTimeout(() => {
                      setCashbackdoneModal(true);
                    }, 1000);
                  },
                },
              ],
              { cancelable: false },
            );
          } else {
            setCashbackModal(false);
            //setAmount('');
            //setPhoneNumber('+60');
            //setCurrCRMUser(null);
            setPhoneNumberChecking(false);
          }
        },
      );
  };
  const reedemcredit = () => {
    var body = {
      amount: parseFloat(amount),

      userName: userName,
      userPhone: phoneNumber.slice(1),
      // emailAddress: email,
      // dob: startDate,

      merchantId: merchantId,
      merchantName: merchantName,
      merchantLogo: merchantLogo,

      outletId: currOutlet.uniqueId,
      outletName: currOutlet.name,
      outletCover: currOutlet.cover,
    };

    // console.log(body);

    setPhoneNumberChecking(true);

    // ApiClient.POST(API.loyaltyCampaignAddCashback, body, false)
    APILocal.loyaltyCampaignAddCashback({ body: body, uid: merchantUserId })
      .then(
        (result) => {
          if (result && result.status === 'success') {
            Alert.alert(
              'Success',
              'Credit has been redeemed',
              [
                {
                  text: 'OK',
                  onPress: () => {
                    setRedeemCreditModal(false);
                    //setAmount('');
                    //setPhoneNumber('+60');
                    //setCurrCRMUser(null);
                    setPhoneNumberChecking(false);
                    setTimeout(() => {
                      setRedeemdone(true);
                    }, 1000);
                  },
                },
              ],
              { cancelable: false },
            );
          } else {
            setRedeemCreditModal(false);
            //setAmount('');
            //setPhoneNumber('+60');
            //setCurrCRMUser(null);
            setPhoneNumberChecking(false);
          }
        },
      );
  };

  const addCredit = () => {
    var body = {
      amount: parseFloat(amount),

      userName: userName,
      userPhone: phoneNumber.slice(1),

      merchantId: merchantId,
      merchantName: merchantName,
      merchantLogo: merchantLogo,

      outletId: currOutlet.uniqueId,
      outletName: currOutlet.name,
      outletCover: currOutlet.cover,
    };

    // console.log(body);

    setPhoneNumberChecking(true);

    // ApiClient.POST(API.loyaltyCampaignAddCredit, body, false)
    APILocal.loyaltyCampaignAddCredit({ body: body, uid: merchantUserId })
      .then((result) => {
        if (result && result.status === 'success') {
          Alert.alert(
            'Success',
            'Credit has been added',
            [
              {
                text: 'OK',
                onPress: () => {
                  setAddCreditModal(false);
                  //setAmount('');
                  //setPhoneNumber('+60');
                  //setCurrCRMUser(null);
                  setPhoneNumberChecking(false);
                  setTimeout(() => {
                    setAddCreditdone(true);
                  }, 1000);
                },
              },
            ],
            { cancelable: false },
          );
        } else {
          setAddCreditModal(false);
          //setAmount('');
          //setPhoneNumber('+60');
          //setCurrCRMUser(null);
          setPhoneNumberChecking(false);
        }
      });
  };

  const deductCredit = () => {
    var body = {
      amount: -parseFloat(amount),

      userName: userName,
      userPhone: phoneNumber.slice(1),

      merchantId: merchantId,
      merchantName: merchantName,
      merchantLogo: merchantLogo,

      outletId: currOutlet.uniqueId,
      outletName: currOutlet.name,
      outletCover: currOutlet.cover,
    };

    // console.log(body);

    setPhoneNumberChecking(true);

    // ApiClient.POST(API.loyaltyCampaignAddCredit, body, false)
    APILocal.loyaltyCampaignAddCredit({ body: body, uid: merchantUserId })
      .then((result) => {
        if (result && result.status === 'success') {
          Alert.alert(
            'Success',
            'Credit has been deducted',
            [
              {
                text: 'OK',
                onPress: () => {
                  setDeductCreditModal(false);
                  //setAmount('');
                  //setPhoneNumber('+60');
                  //setCurrCRMUser(null);
                  setPhoneNumberChecking(false);
                  setTimeout(() => {
                    setDeductCreditdone(true);
                  }, 1000);
                },
              },
            ],
            { cancelable: false },
          );
        } else {
          setDeductCreditModal(false);
          //setAmount('');
          //setPhoneNumber('+60');
          //setCurrCRMUser(null);
          setPhoneNumberChecking(false);
        }
      });
  };

  const PhoneonNumPadBtn = (key) => {
    var plus = phoneNumber.split('+')[1];
    if (key >= 0 || key == '+') {
      var phoneLength = 12;
      if (phoneNumber.startsWith('+6011')) {
        phoneLength = 13;
      }

      if (phoneNumber.includes('+'))
        if (phoneNumber.length < phoneLength && plus.length < phoneLength)
          setPhoneNumber(phoneNumber + key);
      if (!phoneNumber.includes('+')) {
        if (phoneNumber.length < phoneLength) setPhoneNumber(phoneNumber + key);
      }
    } else {
      if (phoneNumber.length > 0) setPhoneNumber(phoneNumber.slice(0, key));
    }
  };

  const onNumPadBtn = (key) => {
    var decimal = amount.split('.')[1];
    if (key >= 0 || key == '.') {
      if (amount.includes('.'))
        if (amount.length < 12 && decimal.length < 2) setAmount(amount + key);
      if (!amount.includes('.')) {
        if (amount.length < 12) setAmount(amount + key);
      }
    } else {
      if (amount.length > 0) setAmount(amount.slice(0, key));
    }
  };

  const renderViewHistory = ({ item }) => {
    return (
      <View
        style={{
          flexDirection: 'row',
          paddingVertical: 10,
          alignItems: 'center',
          paddingHorizontal: 10,
        }}>
        <Text style={{ fontSize: switchMerchant ? 10 : 16, width: '20%' }}>
          {selectedCustomerEdit ? selectedCustomerEdit.name : ''}
        </Text>
        <Text style={{ fontSize: switchMerchant ? 10 : 16, width: '25%' }}>
          {selectedCustomerEdit ? selectedCustomerEdit.number : ''}
        </Text>
        <View style={{ flexDirection: 'column', width: '20%' }}>
          <Text style={{ fontSize: switchMerchant ? 10 : 16 }}>
            {
              LOYALTY_CAMPAIGN_CREDIT_TRANSACTION_TYPE_PARSED[
              item.transactionType
              ]
            }
          </Text>
          <Text style={{ fontSize: switchMerchant ? 10 : 16 }}>
            RM {item.amount.toFixed(2)}
          </Text>
        </View>
        <View style={{ flexDirection: 'column', width: '20%' }}>
          <Text style={{ fontSize: switchMerchant ? 10 : 16 }}>
            {moment(item.createdAt).format('DD MMM YYYY')}
          </Text>
          <Text style={{ fontSize: switchMerchant ? 10 : 16 }}>
            {moment(item.createdAt).format('hh:mm A')}
          </Text>
        </View>
        <View stlye={{ width: '15%' }}>
          <Text
            style={{
              fontSize: switchMerchant ? 10 : 16,
              fontFamily: 'NunitoSans-SemiBold',
              backgroundColor: Colors.primaryColor,
              color: Colors.whiteColor,
              padding: 8,
              borderRadius: 5,
            }}>
            Success
          </Text>
        </View>
      </View>
    );
  };

  const renderLoyaltyCampaign = ({ item }) => {
    return (
      <TouchableOpacity
        onPress={() => {
          // setShowLoyaltyAutomatedCampaign(true);
          // setIsRem1(false);
          // setIsRem2(false);
          // setIsRem3(false);
          // setIs1st(false);
          // setIsRisk(true);
          // setIsLapsed(false);
          // setIsLost(false);
          // setIsBirthday(false);
          // setIsGrowth(false);
          // setIsSpender(false);
          // setIsSignup(false);

          // CommonStore.update((s) => {
          //   s.selectedLoyaltyCampaignEdit = item;
          // });

          // props.navigation.navigate('NewLoyaltyCampaign');

          // console.log(item);

          Alert.alert(
            `${item.campaignName}`,
            `Are you sure you want to redeem this voucher?`,
            [
              {
                text: 'YES',
                onPress: () => {
                  redeemUserTaggableVoucherByMerchant(item);
                },
              },
              { text: 'NO', onPress: () => { }, style: 'cancel' },
            ],
            { cancelable: false },
          );
        }}
        style={{
          paddingHorizontal: windowWidth * 0.01,
          paddingVertical: windowHeight * 0.02,
          paddingTop: windowHeight * 0.01,
          borderBottomColor: '#EBEDEF',
          borderBottomWidth: 1,
          width: '100%',
        }}>
        <View
          style={{
            // borderWidth: 1,
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'space-between',
          }}>
          <View>
            <Text
              style={{
                fontFamily: 'NunitoSans-Bold',
                fontSize: switchMerchant ? 10 : 14,
                // color: switch1stVisit ? Colors.primaryColor : Colors.tabRed,
                color: Colors.primaryColor,
              }}>
              <Text>{item.isActive ? 'Active' : 'Inactive'}</Text>
            </Text>
            <Text
              style={{
                fontFamily: 'NunitoSans-Bold',
                fontSize: switchMerchant ? 10 : 14,
              }}>
              {item.campaignName}
            </Text>
            <Text
              style={{
                fontFamily: 'NunitoSans-Bold',
                fontSize: switchMerchant ? 10 : 14,
                color: '#808B96',
              }}>
              {/* {`Send ${dropRiskVisited} after last visit - ${moment(
                riskTime,
              ).format('hh:mm A')} - ${dropRiskExp} expiration`} */}
              {item.campaignDescription}
            </Text>
          </View>
          {/* <View
            style={{
              flexDirection: 'row',
            }}>
            <Text
              style={{
                fontFamily: 'NunitoSans-Bold',
                fontSize: switchMerchant ? 10 : 14,
              }}>
              {dropRiskVisited}
            </Text>
            <Plus
              name="chevron-right"
              size={switchMerchant ? 20 : 25}
              color={Colors.darkBgColor}
              style={{ bottom: 1 }}
            />
          </View> */}
        </View>
      </TouchableOpacity>
    );
  };

  const redeemUserTaggableVoucherByMerchant = (taggableVoucher) => {
    const userTaggableVoucher = selectedCustomerUserTaggableVouchers.find(userVoucher => {
      return (userVoucher
        .voucherId ===
        taggableVoucher.uniqueId &&
        userVoucher
          .redeemDate === null);
    });

    if (userTaggableVoucher) {
      var body = {
        // loyaltyCampaignId: loyaltyCampaign.uniqueId,
        taggableVoucherId: taggableVoucher.uniqueId,

        userTaggableVoucherId: userTaggableVoucher.uniqueId,
      };

      // ApiClient.POST(API.redeemUserTaggableVoucherByMerchant, body, false)
      APILocal.redeemUserTaggableVoucherByMerchant({ body: body, uid: merchantUserId })
        .then(
          (result) => {
            if (result && result.status === 'success') {
              Alert.alert(
                'Success',
                'Voucher has been redeemed',
                [
                  {
                    text: 'OK',
                    onPress: () => {
                      // setCashbackModal(false);
                      // //setAmount('');
                      // //setPhoneNumber('+60');
                      // //setCurrCRMUser(null);
                      // setPhoneNumberChecking(false);

                      // setTimeout(() => {
                      //   setCashbackdoneModal(true);
                      // }, 1000);
                    },
                  },
                ],
                { cancelable: false },
              );
            } else {
              Alert.alert('Info', 'Unable to redeem voucher');

              // setCashbackModal(false);
              // //setAmount('');
              // //setPhoneNumber('+60');
              // //setCurrCRMUser(null);
              // setPhoneNumberChecking(false);
            }
          },
        );
    }
    else {
      Alert.alert('Info', 'This voucher has been used');
    }
  };

  //////////////////////////////////////////////////////////////////////////////////////////////////////////

  navigation.setOptions({
    headerLeft: () => (
      <TouchableOpacity
        onPress={() => {
          if (isAlphaUser || true) {
            navigation.navigate('MenuOrderingScreen');

            CommonStore.update((s) => {
              s.currPage = 'MenuOrderingScreen';
              s.currPageStack = [...currPageStack, 'MenuOrderingScreen'];
            });
          }
          else {
            navigation.navigate('Table');

            CommonStore.update((s) => {
              s.currPage = 'Table';
              s.currPageStack = [...currPageStack, 'Table'];
            });
          }
          if (expandTab !== EXPAND_TAB_TYPE.OPERATION) {
            CommonStore.update((s) => {
              s.expandTab = EXPAND_TAB_TYPE.OPERATION;
            });
          }
        }}
        style={styles.headerLeftStyle}>
        <Image
          style={{
            width: 124,
            height: 26,
          }}
          resizeMode="contain"
          source={require('../assets/image/logo.png')}
        />
      </TouchableOpacity>
    ),
    headerTitle: () => (
      <View
        style={[
          {
            // justifyContent: Platform.OS === 'android' ? 'center' : 'flex-start',
            // alignItems: Platform.OS === 'android' ? 'center' : 'flex-start',
            // marginRight: Platform.OS === 'ios' ? "27%" : 0,
            // bottom: switchMerchant ? '2%' : 0,
            ...global.getHeaderTitleStyle(),
          },
          // windowWidth >= 768 && switchMerchant
          //   ? { right: windowWidth * 0.1 }
          //   : {},
          // windowWidth <= 768
          //   ? { right: 20 }
          //   : {},
        ]}>
        <Text
          style={{
            fontSize: switchMerchant ? 20 : 24,
            // lineHeight: 25,
            textAlign: 'left',
            alignItems: 'flex-start',
            justifyContent: 'flex-start',
            fontFamily: 'NunitoSans-Bold',
            color: Colors.whiteColor,
            opacity: 1,
            paddingLeft: Platform.OS === 'ios' ? "1%" : 0,
          }}>
          Earn & Redeem
        </Text>
      </View>
    ),
    headerRight: () => (
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
        }}>
        {outletSelectDropdownView()}
        <View
          style={{
            backgroundColor: 'white',
            width: 0.5,
            height: windowHeight * 0.025,
            opacity: 0.8,
            marginHorizontal: 15,
            bottom: -1,
          }}></View>
        <TouchableOpacity
          onPress={() => {
    if (global.currUserRole === 'admin') {
        navigation.navigate('Setting');
    }
}}
          style={{ flexDirection: 'row', alignItems: 'center' }}>
          <Text
            style={[{
              fontFamily: 'NunitoSans-SemiBold',
              fontSize: switchMerchant ? 10 : 16,
              color: Colors.secondaryColor,
              marginRight: 15,
            }, switchMerchant ? { width: windowWidth / 8 } : {}]}
            numberOfLines={switchMerchant ? 1 : 1}
          >

            {merchantUserName}
          </Text>
          <View
            style={{
              marginRight: 30,
              width: windowHeight * 0.05,
              height: windowHeight * 0.05,
              borderRadius: windowHeight * 0.05 * 0.5,
              alignItems: 'center',
              justifyContent: 'center',
              backgroundColor: 'white',
            }}>
            <Image
              style={{
                width: windowHeight * 0.035,
                height: windowHeight * 0.035,
                alignSelf: 'center',
              }}
              source={require('../assets/image/profile-pic.jpg')}
            />
          </View>
        </TouchableOpacity>
      </View>
    ),
  });
  //////////////////////////////////////////////////////////////////

  return (
    <UserIdleWrapper disabled={!isMounted}>
      <View
        style={[
          styles.container,
          !isTablet()
            ? {
              transform: [{ scaleX: 1 }, { scaleY: 1 }],
            }
            : {},
        ]}>
        {/* <View
          style={[
            styles.sidebar,
            !isTablet() ? {} : {},
            switchMerchant
              ? {
                // width: '10%'
              }
              : {},
          ]}>
          <SideBar navigation={props.navigation} selectedTab={9} />
        </View> */}

        <DateTimePickerModal
          isVisible={showStartDatePicker}
          supportedOrientations={['portrait', 'landscape']}
          mode={'date'}
          onConfirm={(text) => {
            setStartDate(moment(text).local());

            setShowStartDatePicker(false);
          }}
          onCancel={() => {
            setShowStartDatePicker(false);
          }}
        />
        <View>
          <View style={{
            alignItems: 'flex-end',
            paddingVertical: 20,
            paddingRight: 30,
          }}>
            <TouchableOpacity
              style={{
                justifyContent: 'center',
                backgroundColor: '#0A1F44',
                borderRadius: 5,
                //width: 160,
                paddingHorizontal: 15,
                height: switchMerchant ? 37 : 42,
                alignItems: 'center',
                shadowOffset: {
                  width: 0,
                  height: 2,
                },
                shadowOpacity: 0.22,
                shadowRadius: 3.22,
                elevation: 1,
                zIndex: -1,
              }}
              onPress={() => {
                setHistoryModal(true);
              }}>
              <Text
                style={{
                  color: Colors.whiteColor,
                  marginLeft: 5,
                  fontSize: switchMerchant ? 12 : 16,
                  fontFamily: 'NunitoSans-Bold',
                }}>
                VIEW HISTORY
              </Text>
            </TouchableOpacity>
          </View>
          <View
            style={{
              backgroundColor: Colors.whiteColor,
              width: switchMerchant
                ? windowWidth * 0.8
                : windowWidth * 0.87,
              height: windowHeight * 0.76,
              // marginTop: 30,
              marginHorizontal: switchMerchant ? 25 : 30,
              marginBottom: switchMerchant ? 0 : 30,
              alignSelf: 'center',
              justifyContent: 'center',
              alignItems: 'center',
              borderRadius: 5,
              shadowOpacity: 0,
              shadowColor: '#000',
              shadowOffset: {
                width: 0,
                height: 2,
              },
              shadowOpacity: 0.22,
              shadowRadius: 3.22,
              elevation: 3,
            }}>

            {/* ///////////// HOME /////////////// */}
            {creditSection === CREDIT_SECTION.HOME ? (
              <View
                style={{
                  flexDirection: 'column',
                  justifyContent: switchMerchant
                    ? 'space-between'
                    : 'space-evenly',
                  flex: 1,
                }}>
                {/* <TouchableOpacity
                onPress={() => {
                  setCreditSection(CREDIT_SECTION.REGISTER);
                }}>
                <View style={switchMerchant ? {
                  borderWidth: 1,
                  borderRadius: 5,
                  padding: 5,
                  alignItems: 'center',
                  alignSelf: 'center',
                  justifyContent: 'center',
                  width: windowWidth * 0.27,
                } : [styles.creditbutton]}>
                  <Text style={{ fontSize: switchMerchant ? 12 : 20 }}>
                    Register
                  </Text>
                </View>
              </TouchableOpacity> */}
                <View
                  style={[{
                    flexDirection: 'row',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                  }]}>
                  <View
                    style={[
                      {
                        width: '40%',
                        height: 250,
                        backgroundColor: Colors.tabMint,
                        borderRadius: 10,
                        flexDirection: 'row',
                        alignItems: 'center',
                        justifyContent: 'center',
                        paddingHorizontal: 20,
                        paddingVertical: 15,
                        shadowColor: '#000',
                        shadowOffset: {
                          width: 0,
                          height: 2,
                        },
                        shadowOpacity: 0.22,
                        shadowRadius: 3.22,
                        elevation: 3,
                      },
                    ]}>
                    <TouchableOpacity
                      onPress={() => {
                        setCreditSection(CREDIT_SECTION.ADD_CASHBACK);
                      }}>
                      <View style={{ alignItems: 'center', justifyContent: 'center' }}>
                        <MaterialCommunityIcons
                          name="cash-plus"
                          size={switchMerchant ? 60 : 90}
                          style={{ color: Colors.whiteColor, paddingRight: 5, paddingBottom: 10, }}
                        />
                        <Text
                          style={[
                            {
                              fontFamily: 'NunitoSans-Bold',
                              color: Colors.whiteColor,
                              fontSize: 24,
                              textAlign: 'center',
                            },
                          ]}>
                          Earn Point
                        </Text>
                      </View>
                    </TouchableOpacity>
                  </View>

                  <View
                    style={[
                      {
                        width: '40%',
                        height: 250,
                        backgroundColor: Colors.tabRed,
                        borderRadius: 10,
                        flexDirection: 'row',
                        alignItems: 'center',
                        justifyContent: 'center',
                        paddingHorizontal: 20,
                        paddingVertical: 15,
                        shadowColor: '#000',
                        shadowOffset: {
                          width: 0,
                          height: 2,
                        },
                        shadowOpacity: 0.22,
                        shadowRadius: 3.22,
                        elevation: 3,
                      },
                    ]}>
                    <TouchableOpacity
                      onPress={() => {
                        setCreditSection(CREDIT_SECTION.DEDUCT_CREDIT);
                      }}>
                      <View style={{ alignItems: 'center', }}>
                        <MaterialCommunityIcons
                          name="cash-minus"
                          size={switchMerchant ? 60 : 90}
                          style={{ color: Colors.whiteColor, paddingRight: 5, paddingBottom: 10, }}
                        />
                        <Text
                          style={[
                            {
                              fontFamily: 'NunitoSans-Bold',
                              color: Colors.whiteColor,
                              fontSize: 24,
                              alignSelf: 'center',
                            },
                          ]}>
                          Deduct Point
                        </Text>
                      </View>
                    </TouchableOpacity>
                  </View>
                </View>
                {/*<TouchableOpacity
                onPress={() => {
                  
                }}>
                <View
                  style={
                    switchMerchant
                      ? {
                        borderWidth: 1,
                        borderRadius: 5,
                        padding: 5,
                        alignItems: 'center',
                        alignSelf: 'center',
                        justifyContent: 'center',
                        width: windowWidth * 0.27,
                      }
                      : [styles.creditbutton]
                  }>
                  <Text style={{ fontSize: switchMerchant ? 12 : 20 }}>
                    Earn Point
                  </Text>
                </View>
              </TouchableOpacity>
                </View>*/}
                <View
                  style={[{
                    flexDirection: 'row',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                  }]}>
                  <View
                    style={[
                      {
                        width: '40%',
                        height: 250,
                        backgroundColor: Colors.tabCyan,
                        borderRadius: 10,
                        flexDirection: 'row',
                        alignItems: 'center',
                        justifyContent: 'center',
                        paddingHorizontal: 20,
                        paddingVertical: 15,
                        shadowColor: '#000',
                        shadowOffset: {
                          width: 0,
                          height: 2,
                        },
                        shadowOpacity: 0.22,
                        shadowRadius: 3.22,
                        elevation: 3,
                      },
                    ]}>
                    <TouchableOpacity
                      onPress={() => {
                        setCreditSection(CREDIT_SECTION.REDEEM_CREDIT);
                      }}>
                      <View style={{ alignItems: 'center', }}>
                        <MaterialIcons
                          name="redeem"
                          size={switchMerchant ? 60 : 85}
                          style={{ color: Colors.whiteColor, paddingRight: 5, paddingBottom: 10, }}
                        />
                        <Text
                          style={[
                            {
                              fontFamily: 'NunitoSans-Bold',
                              color: Colors.whiteColor,
                              fontSize: 24,
                              alignSelf: 'center',
                            },
                          ]}>
                          Redeem Rewards
                        </Text>
                      </View>
                    </TouchableOpacity>
                  </View>
                  <View
                    style={[
                      {
                        width: '40%',
                        height: 250,
                        backgroundColor: Colors.tabGold,
                        borderRadius: 10,
                        flexDirection: 'row',
                        alignItems: 'center',
                        justifyContent: 'center',
                        paddingHorizontal: 20,
                        paddingVertical: 15,
                        shadowColor: '#000',
                        shadowOffset: {
                          width: 0,
                          height: 2,
                        },
                        shadowOpacity: 0.22,
                        shadowRadius: 3.22,
                        elevation: 3,
                      },
                    ]}>
                    <TouchableOpacity
                      onPress={() => {
                        setCreditSection(CREDIT_SECTION.ADD_CREDIT);
                      }}>
                      <View style={{ alignItems: 'center', }}>
                        <Entypo
                          name="credit"
                          size={switchMerchant ? 60 : 80}
                          style={{ color: Colors.whiteColor, paddingRight: 5, paddingBottom: 10, }}
                        />
                        <Text
                          style={[
                            {
                              fontFamily: 'NunitoSans-Bold',
                              color: Colors.whiteColor,
                              fontSize: 24,
                              alignSelf: 'center',
                            },
                          ]}>
                          Topup Points
                        </Text>
                      </View>
                    </TouchableOpacity>
                  </View>
                </View>
                {/*<View
              style={{
                flexDirection: 'row',
                justifyContent: switchMerchant
                  ? 'space-between'
                  : 'space-evenly',
                flex: 1,
              }}>
              <TouchableOpacity
                onPress={() => {
                  setCreditSection(CREDIT_SECTION.REDEEM_CREDIT);
                }}>
                <View
                  style={
                    switchMerchant
                      ? {
                        borderWidth: 1,
                        borderRadius: 5,
                        padding: 5,
                        alignItems: 'center',
                        alignSelf: 'center',
                        justifyContent: 'center',
                        width: windowWidth * 0.27,
                      }
                      : [styles.creditbutton]
                  }>
                  <Text style={{ fontSize: switchMerchant ? 12 : 20 }}>
                    Redeem Rewards
                  </Text>
                </View>
              </TouchableOpacity>

              <TouchableOpacity
                onPress={() => {
                  setCreditSection(CREDIT_SECTION.ADD_CREDIT);
                }}>
                <View
                  style={
                    switchMerchant
                      ? {
                        borderWidth: 1,
                        borderRadius: 5,
                        padding: 5,
                        alignItems: 'center',
                        alignSelf: 'center',
                        justifyContent: 'center',
                        width: windowWidth * 0.27,
                      }
                      : [styles.creditbutton]
                  }>
                  <Text style={{ fontSize: switchMerchant ? 12 : 20 }}>
                    Topup Point
                  </Text>
                </View>
              </TouchableOpacity>*/}

                {/*<TouchableOpacity
                onPress={() => {
                  setCreditSection(CREDIT_SECTION.DEDUCT_CREDIT);
                }}>
                <View
                  style={
                    switchMerchant
                      ? {
                        borderWidth: 1,
                        borderRadius: 5,
                        padding: 5,
                        alignItems: 'center',
                        alignSelf: 'center',
                        justifyContent: 'center',
                        width: windowWidth * 0.27,
                      }
                      : [styles.creditbutton]
                  }>
                  <Text style={{ fontSize: switchMerchant ? 12 : 20 }}>
                    Deduct Point
                  </Text>
                </View>
                </TouchableOpacity>*/}

                {/* <TouchableOpacity
                onPress={() => {
                  setHistoryModal(true);
                }}>
                <View
                  style={
                    switchMerchant
                      ? {
                        borderWidth: 1,
                        borderRadius: 5,
                        padding: 5,
                        alignItems: 'center',
                        alignSelf: 'center',
                        justifyContent: 'center',
                        width: windowWidth * 0.27,
                      }
                      : [styles.creditbutton]
                  }>
                  <Text style={{ fontSize: switchMerchant ? 12 : 20 }}>
                    View History
                  </Text>
                </View>
                </TouchableOpacity> */}
              </View>
            ) : (
              <></>
            )}
            {/* ///////////// Register /////////////// */}

            {creditSection === CREDIT_SECTION.REGISTER ? (
              <View
                style={{
                  flex: 1,
                  width: '100%',
                  height: '100%',
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  backgroundColor: Colors.highlightColor,
                }}>
                <View
                  style={{
                    width:
                      Platform.OS === 'ios'
                        ? windowWidth * 0.44
                        : windowWidth * 0.43,
                    backgroundColor: Colors.whiteColor,
                    borderRadius: 5,
                  }}>
                  {/* <View
                  style={{
                    flexDirection: 'row',
                  }}>
                  <TouchableOpacity
                    style={{
                      height: windowHeight * 0.055,
                      flexDirection: 'row',
                      alignContent: 'center',
                      alignItems: 'center',
                      marginTop: 10,
                      marginLeft: 10,
                    }}
                    onPress={() => {
                      setCreditSection(CREDIT_SECTION.HOME);
                      setPhoneNumber('+60');
                      setAmount('');
                    }}>
                    <Feather
                      name="chevron-left"
                      size={switchMerchant ? 20 : 30}
                      color={Colors.primaryColor}
                    />
                    <Text
                      style={{
                        fontFamily: 'NunitoSans-Bold',
                        fontSize: switchMerchant ? 14 : 17,
                        color: Colors.primaryColor,
                        marginBottom: Platform.OS === 'ios' ? 0 : 1,
                      }}>
                      Back
                    </Text>
                  </TouchableOpacity>
                </View> */}
                  <View
                    style={{ paddingLeft: 20, paddingTop: switchMerchant ? 5 : 10 }}>
                    <Text style={{ fontSize: switchMerchant ? 16 : 24 }}>
                      {currOutlet.name}
                    </Text>
                    <Text
                      style={{
                        fontSize: switchMerchant ? 16 : 24,
                        fontWeight: 'bold',
                        paddingVertical: switchMerchant ? 2 : 10,
                      }}>
                      Register Here
                    </Text>
                    <Text style={{ fontSize: switchMerchant ? 10 : 16 }}>
                      Receive Instant Cashback And Earn Loyalty Rewards when You
                      Spend
                    </Text>
                  </View>
                  <View
                    style={{
                      paddingLeft: 20,
                      paddingTop: switchMerchant ? 10 : 20,
                      flex: 1,
                    }}>
                    <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                      <Text
                        style={{
                          fontSize: switchMerchant ? 12 : 22,
                          fontWeight: 'bold',
                          backgroundColor: Colors.highlightColor,
                          color: Colors.primaryColor,
                          borderRadius: 25,
                          alignSelf: 'center',
                          alignItems: 'center',
                          justifyContent: 'center',
                          paddingHorizontal: switchMerchant ? 10 : 20,
                          paddingVertical: switchMerchant ? 5 : 10,
                          marginRight: 10,
                        }}>
                        a
                      </Text>
                      <Text style={{ fontSize: switchMerchant ? 10 : 16 }}>
                        Enter Your Phone Number To Get Started
                      </Text>
                    </View>
                    <View
                      style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                        paddingVertical: switchMerchant ? 10 : 20,
                      }}>
                      <Text
                        style={{
                          fontSize: switchMerchant ? 12 : 22,
                          fontWeight: 'bold',
                          backgroundColor: Colors.highlightColor,
                          color: Colors.primaryColor,
                          borderRadius: 25,
                          alignSelf: 'center',
                          alignItems: 'center',
                          justifyContent: 'center',
                          paddingHorizontal: switchMerchant ? 10 : 20,
                          paddingVertical: switchMerchant ? 5 : 10,
                          marginRight: 10,
                        }}>
                        b
                      </Text>
                      <Text
                        style={{
                          fontSize: switchMerchant ? 10 : 16,
                          paddingVertical: switchMerchant ? 0 : 10,
                        }}>
                        Sign Up & Start Earning Credit And Rewards
                      </Text>
                    </View>
                    <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                      <Text
                        style={{
                          fontSize: switchMerchant ? 12 : 22,
                          fontWeight: 'bold',
                          backgroundColor: Colors.highlightColor,
                          color: Colors.primaryColor,
                          borderRadius: 25,
                          alignSelf: 'center',
                          alignItems: 'center',
                          justifyContent: 'center',
                          paddingHorizontal: switchMerchant ? 10 : 20,
                          paddingVertical: switchMerchant ? 5 : 10,
                          marginRight: 10,
                        }}>
                        c
                      </Text>
                      <Text style={{ fontSize: switchMerchant ? 10 : 16 }}>
                        Enjoy Your Credit & Claim Rewards On Future Visits
                      </Text>
                    </View>
                  </View>
                  <View
                    style={{
                      flex: 1,
                      paddingHorizontal: 20,
                      paddingTop: 20,
                      justifyContent: 'center',
                    }}>
                    {/* <View
                    style={{
                      flexDirection: 'row',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                    }}>
                    <Text style={{ fontSize: switchMerchant ? 10 : 14 }}>
                      Powered By{' '}
                      <Text
                        style={{
                          color: Colors.primaryColor,
                          fontSize: switchMerchant ? 12 : 16,
                        }}>
                        KooDoo
                      </Text>
                    </Text>
                    <TouchableOpacity style={{ flexDirection: 'row' }}>
                      <Feather
                        name="help-circle"
                        size={switchMerchant ? 15 : 20}
                        style={{ color: Colors.primaryColor, paddingRight: 5 }}
                      />
                      <Text
                        style={{
                          color: Colors.primaryColor,
                          fontSize: switchMerchant ? 10 : 14,
                        }}>
                        Help
                      </Text>
                    </TouchableOpacity>
                  </View> */}
                  </View>
                </View>
                <View
                  style={{
                    width:
                      Platform.OS === 'ios'
                        ? windowWidth * 0.42
                        : windowWidth * 0.43,
                    backgroundColor: Colors.whiteColor,
                    borderRadius: 5,
                  }}>
                  <View
                    style={{
                      alignItems: 'center',
                      justifyContent: 'center',
                      flex: 1,
                      paddingTop: Platform.OS === 'ios' ? 0 : 10,
                      marginTop: Platform.OS === 'ios' ? 10 : 0,
                    }}>
                    <Text
                      style={{
                        fontSize: switchMerchant ? 15 : 25,
                        fontWeight: 'bold',
                      }}>
                      Enter Your Phone Number
                    </Text>
                    <Text
                      style={{
                        fontSize: switchMerchant ? 8 : 14,
                        alignSelf: 'center',
                        alignItems: 'center',
                        textAlign: 'center',
                        width: '80%',
                      }}>
                      Already Signed Up? Simply Enter Your Number And We'll Send An
                      SMS Link Where You Can View Your Account
                    </Text>
                  </View>
                  <View
                    style={{
                      justifyContent: 'center',
                      alignItems: 'center',
                      flex: 1,
                      paddingTop: switchMerchant ? 5 : 0,
                    }}>
                    <Text style={{ fontSize: switchMerchant ? 22 : 45 }}>
                      {phoneNumber}
                    </Text>
                  </View>
                  <View>
                    <View
                      style={{
                        flexDirection: 'row',
                        flexWrap: 'wrap',
                        justifyContent: 'space-between',
                        alignItems: 'center',
                        alignSelf: 'center',
                        width: Platform.OS === 'ios' ? 270 : '45%',
                        paddingTop: switchMerchant ? 10 : 30,
                      }}>
                      <View
                        style={{
                          flexDirection: 'row',
                          justifyContent: 'space-between',
                          alignSelf: 'center',
                          alignItems: 'center',
                          width: '100%',
                        }}>
                        <TouchableOpacity
                          onPress={() => {
                            PhoneonNumPadBtn(1);
                          }}>
                          <View
                            style={[
                              styles.pinBtn,
                              switchMerchant
                                ? {
                                  width: switchMerchant ? 35 : 70,
                                  height: switchMerchant ? 35 : 70,
                                  marginBottom: 7,
                                }
                                : { width: 70, height: 70 },
                            ]}>
                            <Text
                              style={[
                                styles.pinNo,
                                { fontSize: switchMerchant ? 10 : 20 },
                              ]}>
                              1
                            </Text>
                          </View>
                        </TouchableOpacity>

                        <TouchableOpacity
                          onPress={() => {
                            PhoneonNumPadBtn(2);
                          }}>
                          <View
                            style={[
                              styles.pinBtn,
                              switchMerchant
                                ? {
                                  width: switchMerchant ? 35 : 70,
                                  height: switchMerchant ? 35 : 70,
                                  marginBottom: 7,
                                }
                                : { width: 70, height: 70 },
                            ]}>
                            <Text
                              style={[
                                styles.pinNo,
                                { fontSize: switchMerchant ? 10 : 20 },
                              ]}>
                              2
                            </Text>
                          </View>
                        </TouchableOpacity>

                        <TouchableOpacity
                          onPress={() => {
                            PhoneonNumPadBtn(3);
                          }}>
                          <View
                            style={[
                              styles.pinBtn,
                              switchMerchant
                                ? {
                                  width: switchMerchant ? 35 : 70,
                                  height: switchMerchant ? 35 : 70,
                                  marginBottom: 7,
                                }
                                : { width: 70, height: 70 },
                            ]}>
                            <Text
                              style={[
                                styles.pinNo,
                                { fontSize: switchMerchant ? 10 : 20 },
                              ]}>
                              3
                            </Text>
                          </View>
                        </TouchableOpacity>
                      </View>
                      <View
                        style={{
                          flexDirection: 'row',
                          justifyContent: 'space-between',
                          alignSelf: 'center',
                          alignItems: 'center',
                          width: '100%',
                        }}>
                        <TouchableOpacity
                          onPress={() => {
                            PhoneonNumPadBtn(4);
                          }}>
                          <View
                            style={[
                              styles.pinBtn,
                              switchMerchant
                                ? {
                                  width: switchMerchant ? 35 : 70,
                                  height: switchMerchant ? 35 : 70,
                                  marginBottom: 7,
                                }
                                : { width: 70, height: 70 },
                            ]}>
                            <Text
                              style={[
                                styles.pinNo,
                                { fontSize: switchMerchant ? 10 : 20 },
                              ]}>
                              4
                            </Text>
                          </View>
                        </TouchableOpacity>

                        <TouchableOpacity
                          onPress={() => {
                            PhoneonNumPadBtn(5);
                          }}>
                          <View
                            style={[
                              styles.pinBtn,
                              switchMerchant
                                ? {
                                  width: switchMerchant ? 35 : 70,
                                  height: switchMerchant ? 35 : 70,
                                  marginBottom: 7,
                                }
                                : { width: 70, height: 70 },
                            ]}>
                            <Text
                              style={[
                                styles.pinNo,
                                { fontSize: switchMerchant ? 10 : 20 },
                              ]}>
                              5
                            </Text>
                          </View>
                        </TouchableOpacity>

                        <TouchableOpacity
                          onPress={() => {
                            PhoneonNumPadBtn(6);
                          }}>
                          <View
                            style={[
                              styles.pinBtn,
                              switchMerchant
                                ? {
                                  width: switchMerchant ? 35 : 70,
                                  height: switchMerchant ? 35 : 70,
                                  marginBottom: 7,
                                }
                                : { width: 70, height: 70 },
                            ]}>
                            <Text
                              style={[
                                styles.pinNo,
                                { fontSize: switchMerchant ? 10 : 20 },
                              ]}>
                              6
                            </Text>
                          </View>
                        </TouchableOpacity>
                      </View>

                      <View
                        style={{
                          flexDirection: 'row',
                          justifyContent: 'space-between',
                          alignSelf: 'center',
                          alignItems: 'center',
                          width: '100%',
                        }}>
                        <TouchableOpacity
                          onPress={() => {
                            PhoneonNumPadBtn(7);
                          }}>
                          <View
                            style={[
                              styles.pinBtn,
                              switchMerchant
                                ? {
                                  width: switchMerchant ? 35 : 70,
                                  height: switchMerchant ? 35 : 70,
                                  marginBottom: 7,
                                }
                                : { width: 70, height: 70 },
                            ]}>
                            <Text
                              style={[
                                styles.pinNo,
                                { fontSize: switchMerchant ? 10 : 20 },
                              ]}>
                              7
                            </Text>
                          </View>
                        </TouchableOpacity>

                        <TouchableOpacity
                          onPress={() => {
                            PhoneonNumPadBtn(8);
                          }}>
                          <View
                            style={[
                              styles.pinBtn,
                              switchMerchant
                                ? {
                                  width: switchMerchant ? 35 : 70,
                                  height: switchMerchant ? 35 : 70,
                                  marginBottom: 7,
                                }
                                : { width: 70, height: 70 },
                            ]}>
                            <Text
                              style={[
                                styles.pinNo,
                                { fontSize: switchMerchant ? 10 : 20 },
                              ]}>
                              8
                            </Text>
                          </View>
                        </TouchableOpacity>

                        <TouchableOpacity
                          onPress={() => {
                            PhoneonNumPadBtn(9);
                          }}>
                          <View
                            style={[
                              styles.pinBtn,
                              switchMerchant
                                ? {
                                  width: switchMerchant ? 35 : 70,
                                  height: switchMerchant ? 35 : 70,
                                  marginBottom: 7,
                                }
                                : { width: 70, height: 70 },
                            ]}>
                            <Text
                              style={[
                                styles.pinNo,
                                { fontSize: switchMerchant ? 10 : 20 },
                              ]}>
                              9
                            </Text>
                          </View>
                        </TouchableOpacity>
                      </View>
                      <View
                        style={{
                          flexDirection: 'row',
                          justifyContent: 'space-between',
                          alignSelf: 'center',
                          alignItems: 'center',
                          width: '100%',
                        }}>
                        <TouchableOpacity
                          onPress={() => {
                            if (phoneNumber.includes('+')) {
                              return;
                            }
                            else {
                              PhoneonNumPadBtn('+');
                            }
                          }}>
                          <View
                            style={[
                              styles.pinBtn,
                              switchMerchant
                                ? {
                                  width: switchMerchant ? 35 : 70,
                                  height: switchMerchant ? 35 : 70,
                                  marginBottom: 7,
                                }
                                : { width: 70, height: 70 },
                            ]}>
                            <Text
                              style={[
                                styles.pinNo,
                                { fontSize: switchMerchant ? 10 : 20 },
                              ]}>
                              +
                            </Text>
                          </View>
                        </TouchableOpacity>

                        <TouchableOpacity
                          onPress={() => {
                            PhoneonNumPadBtn(0);
                          }}>
                          <View
                            style={[
                              styles.pinBtn,
                              switchMerchant
                                ? {
                                  width: switchMerchant ? 35 : 70,
                                  height: switchMerchant ? 35 : 70,
                                  marginBottom: 7,
                                }
                                : { width: 70, height: 70 },
                            ]}>
                            <Text
                              style={[
                                styles.pinNo,
                                { fontSize: switchMerchant ? 10 : 20 },
                              ]}>
                              0
                            </Text>
                          </View>
                        </TouchableOpacity>

                        <TouchableOpacity
                          onPress={() => {
                            if (phoneNumber != '+6') {
                              PhoneonNumPadBtn(-1);
                            }
                          }}>
                          <View
                            style={[
                              styles.pinBtn,
                              switchMerchant
                                ? {
                                  width: switchMerchant ? 35 : 70,
                                  height: switchMerchant ? 35 : 70,
                                  marginBottom: 7,
                                }
                                : { width: 70, height: 70 },
                            ]}>
                            <Feather
                              name="chevron-left"
                              size={switchMerchant ? 13 : 30}
                              color={'black'}
                              style={{}}
                            />
                          </View>
                        </TouchableOpacity>
                      </View>
                    </View>
                  </View>
                  <View
                    style={{
                      flex: 1,
                      flexDirection: 'row',
                      alignItems: 'center',
                      alignSelf: 'center',
                    }}>
                    <TouchableOpacity
                      onPress={() => {
                        setRegisterDetail(true);
                      }}
                      disabled={phoneNumberChecking}>
                      <View
                        style={{
                          borderRadius: 10,
                          backgroundColor: phoneNumberChecking
                            ? 'grey'
                            : Colors.primaryColor,
                          paddingVertical: switchMerchant ? 4 : 10,
                          paddingHorizontal: switchMerchant ? 15 : 25,
                          marginBottom: switchMerchant ? 5 : 0,
                        }}>
                        <Text
                          style={{
                            color: Colors.whiteColor,
                            fontSize: switchMerchant ? 10 : 16,
                            fontFamily: 'NunitoSans-Bold',
                          }}>
                          Get Started
                        </Text>
                      </View>
                    </TouchableOpacity>
                  </View>
                </View>
                <ModalView
                  visible={registerDetail}
                  supportedOrientations={['landscape', 'portrait']}
                  style={{}}
                  animationType={'fade'}
                  transparent={true}>
                  <View
                    style={{
                      flex: 1,
                      backgroundColor: Colors.modalBgColor,
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}>
                    <View
                      style={{
                        width: switchMerchant
                          ? windowWidth * 0.77
                          : windowWidth * 0.6,
                        height: switchMerchant
                          ? windowHeight * 0.85
                          : windowHeight * 0.75,
                        backgroundColor: Colors.whiteColor,
                        borderRadius: 5,
                        padding: switchMerchant ? 15 : 30,
                      }}>
                      <TouchableOpacity
                        style={{
                          position: 'absolute',
                          right: windowWidth * 0.02,
                          top: windowWidth * 0.02,

                          elevation: 1000,
                          zIndex: 1000,
                        }}
                        onPress={() => {
                          setRegisterDetail(false);
                          setAmount('');
                        }}>
                        <AntDesign
                          name="closecircle"
                          size={switchMerchant ? 15 : 25}
                          color={Colors.fieldtTxtColor}
                        />
                      </TouchableOpacity>
                      <View style={{ alignSelf: 'center', alignItems: 'center' }}>
                        <Text
                          style={{
                            fontSize: switchMerchant ? 16 : 24,
                            fontFamily: 'NunitoSans-Bold',
                            textAlign: 'center',
                          }}>
                          Enter Your Details To Start Earning Cashback & Loyalty
                          Rewards!
                        </Text>
                      </View>
                      <ScrollView
                        scrollEnabled={true}
                        showsVerticalScrollIndicator={false}>
                        <View style={{ paddingHorizontal: 50, paddingVertical: 20 }}>
                          <View style={{}}>
                            <Text style={{ fontSize: switchMerchant ? 10 : 16 }}>
                              Phone Number
                            </Text>
                            <Text
                              style={{
                                fontSize: switchMerchant ? 10 : 16,
                                fontFamily: 'NunitoSans-Bold',
                              }}>
                              {phoneNumber}
                            </Text>
                          </View>
                          <View style={{ marginTop: 15 }}>
                            <Text style={{ fontSize: switchMerchant ? 10 : 16 }}>
                              First Name
                            </Text>
                            <TextInput
                              style={{
                                borderBottomWidth: 0.5,
                                borderBottomColor: 'black',
                                fontSize: switchMerchant ? 10 : 14,
                                height: 35,
                                padding: 0,
                                width: '70%',
                              }}></TextInput>
                          </View>
                          <View style={{ marginTop: 15 }}>
                            <Text style={{ fontSize: switchMerchant ? 10 : 16 }}>
                              Last Name
                            </Text>
                            <TextInput
                              style={{
                                borderBottomWidth: 0.5,
                                borderBottomColor: 'black',
                                fontSize: switchMerchant ? 10 : 14,
                                height: 35,
                                padding: 0,
                                width: '70%',
                              }}></TextInput>
                          </View>
                          <View style={{ marginTop: 15 }}>
                            <Text style={{ fontSize: switchMerchant ? 10 : 16 }}>
                              Email
                            </Text>
                            <TextInput
                              style={{
                                borderBottomWidth: 0.5,
                                borderBottomColor: 'black',
                                fontSize: switchMerchant ? 10 : 14,
                                height: 35,
                                padding: 0,
                                width: '70%',
                              }}></TextInput>
                          </View>
                          <View style={{ marginTop: 15 }}>
                            <Text style={{ fontSize: switchMerchant ? 10 : 16 }}>
                              Date Of Birth (DD/MM/YYYY)
                            </Text>
                            <DateTimePickerModal
                              isVisible={showDateTimePicker}
                              mode={'date'}
                              onConfirm={(text) => {
                                setRev_date(moment(text));
                                setShowDateTimePicker(false);
                              }}
                              onCancel={() => {
                                setShowDateTimePicker(false);
                              }}
                              maximumDate={moment(rev_date1).toDate()}
                            />
                            <TouchableOpacity
                              style={{
                                borderBottomWidth: 0.5,
                                borderBottomColor: 'black',
                                height: 35,
                                justifyContent: 'center',
                                width: '70%',
                              }}
                              onPress={() => {
                                setShowDateTimePicker(true);
                              }}>
                              <Text style={{}}>
                                {moment(rev_date).format('DD/MM/yyyy')}
                              </Text>
                            </TouchableOpacity>
                          </View>
                          <View style={{ marginTop: 15 }}>
                            <Text
                              style={{
                                fontSize: switchMerchant ? 10 : 16,
                                marginBottom: 5,
                              }}>
                              Gender
                            </Text>
                            <DropDownPicker
                              containerStyle={{
                                height: switchMerchant ? 30 : 35,
                                zIndex: 1000,
                              }}
                              arrowColor={'black'}
                              arrowSize={switchMerchant ? 10 : 20}
                              arrowStyle={{ fontWeight: 'bold' }}
                              labelStyle={{
                                fontFamily: 'NunitoSans-Regular',
                                color: 'black',
                                fontSize: switchMerchant ? 10 : 14,
                              }}
                              style={{
                                width: '70%',
                                paddingVertical: 0,
                                backgroundColor: Colors.fieldtBgColor,
                                borderRadius: 10,
                                fontFamily: 'NunitoSans-Regular',
                                fontSize: switchMerchant ? 10 : 14,
                              }}
                              placeholderStyle={{
                                color: 'black',
                                fontFamily: 'NunitoSans-Regular',
                                fontSize: switchMerchant ? 10 : 14,
                              }}
                              items={[
                                { label: 'Male', value: 'Male' },
                                { label: 'Female', value: 'Female' },
                                { label: 'Others', value: 'Others' },
                              ]}
                              itemStyle={{
                                justifyContent: 'flex-start',
                                marginLeft: 5,
                                zIndex: 1000,
                                fontFamily: 'NunitoSans-Regular',
                                fontSize: switchMerchant ? 10 : 14,
                              }}
                              placeholder={'now cant select need fix'}
                              onChangeItem={(item) => { }}
                              dropDownMaxHeight={150}
                              dropDownStyle={{
                                fontSize: switchMerchant ? 10 : 16,
                                width: '70%',
                                height: switchMerchant ? 100 : 125,
                                backgroundColor: Colors.fieldtBgColor,
                                borderRadius: 10,
                                borderWidth: 1,
                                textAlign: 'left',
                                zIndex: 1000,
                              }}
                            />
                          </View>
                          <View
                            style={{
                              flexDirection: 'row',
                              marginTop: 20,
                              alignSelf: 'center',
                              alignItems: 'center',
                            }}>
                            <Text style={{ fontSize: switchMerchant ? 10 : 14 }}>
                              {`By Signing Up You Agree To The `}
                            </Text>
                            <TouchableOpacity>
                              <Text
                                style={{
                                  borderBottomColor: 'black',
                                  borderBottomWidth: 0.5,
                                  fontSize: switchMerchant ? 10 : 14,
                                }}>
                                Terms Of Use
                              </Text>
                            </TouchableOpacity>
                            <Text style={{ fontSize: switchMerchant ? 10 : 14 }}>
                              {` & `}
                            </Text>
                            <TouchableOpacity>
                              <Text
                                style={{
                                  borderBottomColor: 'black',
                                  borderBottomWidth: 0.5,
                                  fontSize: switchMerchant ? 10 : 14,
                                }}>
                                Privacy Policy
                              </Text>
                            </TouchableOpacity>
                          </View>
                          <View
                            style={{
                              alignItems: 'center',
                              alignSelf: 'center',
                              marginTop: 25,
                            }}>
                            <TouchableOpacity
                              onPress={() => {
                                setRegisterDetail(false);
                              }}>
                              <View
                                style={{
                                  borderRadius: 10,
                                  backgroundColor: Colors.primaryColor,
                                  paddingVertical: switchMerchant ? 4 : 10,
                                  paddingHorizontal: switchMerchant ? 15 : 30,
                                  marginBottom: switchMerchant ? 5 : 0,
                                }}>
                                <Text
                                  style={{
                                    color: Colors.whiteColor,
                                    fontSize: switchMerchant ? 10 : 16,
                                    fontFamily: 'NunitoSans-Bold',
                                  }}>
                                  Join The Loyalty Program
                                </Text>
                              </View>
                            </TouchableOpacity>
                          </View>
                        </View>
                      </ScrollView>
                    </View>
                  </View>
                </ModalView>
              </View>
            ) : (
              <></>
            )}
            {/* ///////////// Add Cashback /////////////// */}

            {creditSection === CREDIT_SECTION.ADD_CASHBACK ? (
              <View
                style={{
                  flex: 1,
                  width: '100%',
                  height: '100%',
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  backgroundColor: Colors.highlightColor,
                }}>
                <View
                  style={{
                    width:
                      Platform.OS === 'ios'
                        ? windowWidth * 0.44
                        : windowWidth * 0.43,
                    backgroundColor: Colors.whiteColor,
                    borderRadius: 5,
                  }}>
                  {/* <View
                  style={{
                    flexDirection: 'row',
                  }}>
                  <TouchableOpacity
                    style={{
                      height: windowHeight * 0.055,
                      flexDirection: 'row',
                      alignContent: 'center',
                      alignItems: 'center',
                      marginTop: 10,
                      marginLeft: 10,
                    }}
                    onPress={() => {
                      setCreditSection(CREDIT_SECTION.HOME);
                      setPhoneNumber('+60');
                    }}>
                    <Feather
                      name="chevron-left"
                      size={switchMerchant ? 20 : 30}
                      color={Colors.primaryColor}
                    />
                    <Text
                      style={{
                        fontFamily: 'NunitoSans-Bold',
                        fontSize: switchMerchant ? 14 : 17,
                        color: Colors.primaryColor,
                        marginBottom: Platform.OS === 'ios' ? 0 : 1,
                      }}>
                      Back
                    </Text>
                  </TouchableOpacity>
                </View> */}
                  <View
                    style={{ paddingLeft: 20, paddingTop: switchMerchant ? 5 : 10, paddingTop: 15, }}>
                    <Text style={{ fontSize: switchMerchant ? 16 : 24 }}>
                      {currOutlet.name}
                    </Text>
                    <Text
                      style={{
                        fontSize: switchMerchant ? 16 : 24,
                        fontWeight: 'bold',
                        paddingVertical: switchMerchant ? 2 : 10,
                      }}>
                      Get Your Cashback Here
                    </Text>
                    <Text style={{ fontSize: switchMerchant ? 10 : 16 }}>
                      Receive Instant Cashback When You Spend
                    </Text>
                  </View>
                  <View
                    style={{
                      paddingLeft: 20,
                      paddingTop: switchMerchant ? 10 : 20,
                      flex: 1,
                    }}>
                    <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                      <Text
                        style={{
                          fontSize: switchMerchant ? 12 : 22,
                          fontWeight: 'bold',
                          backgroundColor: Colors.highlightColor,
                          color: Colors.primaryColor,
                          borderRadius: 25,
                          alignSelf: 'center',
                          alignItems: 'center',
                          justifyContent: 'center',
                          paddingHorizontal: switchMerchant ? 10 : 20,
                          paddingVertical: switchMerchant ? 5 : 10,
                          marginRight: 10,
                        }}>
                        a
                      </Text>
                      <Text style={{ fontSize: switchMerchant ? 10 : 16 }}>
                        Enter Your Phone Number
                      </Text>
                    </View>
                    <View
                      style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                        paddingVertical: switchMerchant ? 10 : 20,
                      }}>
                      <Text
                        style={{
                          fontSize: switchMerchant ? 12 : 22,
                          fontWeight: 'bold',
                          backgroundColor: Colors.highlightColor,
                          color: Colors.primaryColor,
                          borderRadius: 25,
                          alignSelf: 'center',
                          alignItems: 'center',
                          justifyContent: 'center',
                          paddingHorizontal: switchMerchant ? 10 : 20,
                          paddingVertical: switchMerchant ? 5 : 10,
                          marginRight: 10,
                        }}>
                        b
                      </Text>
                      <Text
                        style={{
                          fontSize: switchMerchant ? 10 : 16,
                          paddingVertical: switchMerchant ? 0 : 10,
                        }}>
                        You Will Receive Cashback Based On Your Spendings
                      </Text>
                    </View>
                    <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                      <Text
                        style={{
                          fontSize: switchMerchant ? 12 : 22,
                          fontWeight: 'bold',
                          backgroundColor: Colors.highlightColor,
                          color: Colors.primaryColor,
                          borderRadius: 25,
                          alignSelf: 'center',
                          alignItems: 'center',
                          justifyContent: 'center',
                          paddingHorizontal: switchMerchant ? 10 : 20,
                          paddingVertical: switchMerchant ? 5 : 10,
                          marginRight: 10,
                        }}>
                        c
                      </Text>
                      <Text style={{ fontSize: switchMerchant ? 10 : 16 }}>
                        Claim Your Cashback & Enjoy Savings
                      </Text>
                    </View>
                  </View>
                  <View
                    style={[{
                      paddingTop: 15,
                      flexDirection: 'row',
                      alignItems: 'center',
                      justifyContent: 'space-evenly',
                      paddingBottom: 30,
                    }]}>
                    {/* <View
                    style={{
                      flexDirection: 'row',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                      paddingBottom: 25,
                    }}>
                    <Text style={{ fontSize: switchMerchant ? 10 : 14 }}>
                      Powered By{' '}
                      <Text
                        style={{
                          color: Colors.primaryColor,
                          fontSize: switchMerchant ? 12 : 16,
                        }}>
                        KooDoo
                      </Text>
                    </Text>
                    <TouchableOpacity style={{ flexDirection: 'row' }}>
                      <Feather
                        name="help-circle"
                        size={switchMerchant ? 15 : 20}
                        style={{ color: Colors.primaryColor, paddingRight: 5 }}
                      />
                      <Text
                        style={{
                          color: Colors.primaryColor,
                          fontSize: switchMerchant ? 10 : 14,
                        }}>
                        Help
                      </Text>
                    </TouchableOpacity>
                  </View> */}
                  </View>
                  <View
                    style={[{
                      paddingTop: 15,
                      flexDirection: 'row',
                      alignItems: 'center',
                      justifyContent: 'space-evenly',
                      paddingBottom: 30,
                    }]}>

                    <TouchableOpacity
                      style={[
                        {
                          width: '25%',
                          height: 140,
                          backgroundColor: Colors.tabRed,
                          borderRadius: 10,
                          flexDirection: 'row',
                          alignItems: 'center',
                          justifyContent: 'center',
                          paddingHorizontal: 20,
                          paddingVertical: 15,
                          shadowColor: '#000',
                          shadowOffset: {
                            width: 0,
                            height: 2,
                          },
                          shadowOpacity: 0.22,
                          shadowRadius: 3.22,
                          elevation: 3,
                        },
                      ]}
                      onPress={() => {
                        setCreditSection(CREDIT_SECTION.DEDUCT_CREDIT);
                      }}>

                      <View style={{ alignItems: 'center', }}>
                        <MaterialCommunityIcons
                          name="cash-minus"
                          size={switchMerchant ? 30 : 45}
                          style={{ color: Colors.whiteColor, }}
                        />
                        <Text
                          style={[
                            {
                              fontWeight: 'bold',
                              color: Colors.whiteColor,
                              fontSize: 18,
                              textAlign: 'center',
                              paddingBottom: 5,
                              paddingVertical: 10,
                            },
                          ]}>
                          Deduct Point
                        </Text>
                      </View>
                    </TouchableOpacity>

                    <TouchableOpacity
                      style={[
                        {
                          width: '25%',
                          height: 140,
                          backgroundColor: Colors.tabCyan,
                          borderRadius: 10,
                          flexDirection: 'row',
                          alignItems: 'center',
                          justifyContent: 'center',
                          paddingHorizontal: 20,
                          paddingVertical: 15,
                          shadowColor: '#000',
                          shadowOffset: {
                            width: 0,
                            height: 2,
                          },
                          shadowOpacity: 0.22,
                          shadowRadius: 3.22,
                          elevation: 3,
                        },
                      ]}
                      onPress={() => {
                        setCreditSection(CREDIT_SECTION.REDEEM_CREDIT);
                      }}>
                      <View style={{ alignItems: 'center', }}>
                        <MaterialIcons
                          name="redeem"
                          size={switchMerchant ? 30 : 45}
                          style={{ color: Colors.whiteColor, }}
                        />
                        <Text
                          style={[
                            {
                              fontWeight: 'bold',
                              color: Colors.whiteColor,
                              fontSize: 18,
                              textAlign: 'center',
                              paddingBottom: 5,
                              paddingVertical: 10,
                            },
                          ]}>
                          Redeem Rewards
                        </Text>
                      </View>
                    </TouchableOpacity>
                    <TouchableOpacity
                      style={[
                        {
                          width: '25%',
                          height: 140,
                          backgroundColor: Colors.tabGold,
                          borderRadius: 10,
                          flexDirection: 'row',
                          alignItems: 'center',
                          justifyContent: 'center',
                          paddingHorizontal: 20,
                          paddingVertical: 15,
                          shadowColor: '#000',
                          shadowOffset: {
                            width: 0,
                            height: 2,
                          },
                          shadowOpacity: 0.22,
                          shadowRadius: 3.22,
                          elevation: 3,
                        },
                      ]}
                      onPress={() => {
                        setCreditSection(CREDIT_SECTION.ADD_CREDIT);
                      }}>

                      <View style={{ alignItems: 'center', }}>
                        <Entypo
                          name="credit"
                          size={switchMerchant ? 30 : 45}
                          style={{ color: Colors.whiteColor, }}
                        />
                        <Text
                          style={[
                            {
                              fontWeight: 'bold',
                              color: Colors.whiteColor,
                              fontSize: 18,
                              textAlign: 'center',
                              paddingBottom: 5,
                              paddingVertical: 10,
                            },
                          ]}>
                          Topup Points
                        </Text>
                      </View>
                    </TouchableOpacity>
                  </View>
                </View>
                <View
                  style={{
                    width:
                      Platform.OS === 'ios'
                        ? windowWidth * 0.42
                        : windowWidth * 0.43,
                    backgroundColor: Colors.whiteColor,
                    borderRadius: 5,
                  }}>
                  <View
                    style={{
                      alignItems: 'center',
                      justifyContent: 'center',
                      flex: 1,
                      paddingTop: Platform.OS === 'ios' ? 0 : 10,
                      marginTop: Platform.OS === 'ios' ? 10 : 0,
                    }}>
                    <Text
                      style={{
                        fontSize: switchMerchant ? 15 : 25,
                        fontWeight: 'bold',
                      }}>
                      Enter Your Phone Number
                    </Text>
                  </View>
                  <View
                    style={{
                      justifyContent: 'center',
                      alignItems: 'center',
                      flex: 1,
                    }}>
                    <Text style={{ fontSize: switchMerchant ? 22 : 45 }}>
                      {phoneNumber}
                    </Text>
                  </View>
                  <View>
                    <View
                      style={{
                        flexDirection: 'row',
                        flexWrap: 'wrap',
                        justifyContent: 'space-between',
                        alignItems: 'center',
                        alignSelf: 'center',
                        width: Platform.OS === 'ios' ? 270 : '45%',
                        paddingTop: switchMerchant ? 10 : 30,
                      }}>
                      <View
                        style={{
                          flexDirection: 'row',
                          justifyContent: 'space-between',
                          alignSelf: 'center',
                          alignItems: 'center',
                          width: '100%',
                        }}>
                        <TouchableOpacity
                          onPress={() => {
                            PhoneonNumPadBtn(1);
                          }}>
                          <View
                            style={[
                              styles.pinBtn,
                              switchMerchant
                                ? {
                                  width: switchMerchant ? 35 : 70,
                                  height: switchMerchant ? 35 : 70,
                                  marginBottom: 7,
                                }
                                : { width: 70, height: 70 },
                            ]}>
                            <Text
                              style={[
                                styles.pinNo,
                                { fontSize: switchMerchant ? 10 : 20 },
                              ]}>
                              1
                            </Text>
                          </View>
                        </TouchableOpacity>

                        <TouchableOpacity
                          onPress={() => {
                            PhoneonNumPadBtn(2);
                          }}>
                          <View
                            style={[
                              styles.pinBtn,
                              switchMerchant
                                ? {
                                  width: switchMerchant ? 35 : 70,
                                  height: switchMerchant ? 35 : 70,
                                  marginBottom: 7,
                                }
                                : { width: 70, height: 70 },
                            ]}>
                            <Text
                              style={[
                                styles.pinNo,
                                { fontSize: switchMerchant ? 10 : 20 },
                              ]}>
                              2
                            </Text>
                          </View>
                        </TouchableOpacity>

                        <TouchableOpacity
                          onPress={() => {
                            PhoneonNumPadBtn(3);
                          }}>
                          <View
                            style={[
                              styles.pinBtn,
                              switchMerchant
                                ? {
                                  width: switchMerchant ? 35 : 70,
                                  height: switchMerchant ? 35 : 70,
                                  marginBottom: 7,
                                }
                                : { width: 70, height: 70 },
                            ]}>
                            <Text
                              style={[
                                styles.pinNo,
                                { fontSize: switchMerchant ? 10 : 20 },
                              ]}>
                              3
                            </Text>
                          </View>
                        </TouchableOpacity>
                      </View>
                      <View
                        style={{
                          flexDirection: 'row',
                          justifyContent: 'space-between',
                          alignSelf: 'center',
                          alignItems: 'center',
                          width: '100%',
                        }}>
                        <TouchableOpacity
                          onPress={() => {
                            PhoneonNumPadBtn(4);
                          }}>
                          <View
                            style={[
                              styles.pinBtn,
                              switchMerchant
                                ? {
                                  width: switchMerchant ? 35 : 70,
                                  height: switchMerchant ? 35 : 70,
                                  marginBottom: 7,
                                }
                                : { width: 70, height: 70 },
                            ]}>
                            <Text
                              style={[
                                styles.pinNo,
                                { fontSize: switchMerchant ? 10 : 20 },
                              ]}>
                              4
                            </Text>
                          </View>
                        </TouchableOpacity>

                        <TouchableOpacity
                          onPress={() => {
                            PhoneonNumPadBtn(5);
                          }}>
                          <View
                            style={[
                              styles.pinBtn,
                              switchMerchant
                                ? {
                                  width: switchMerchant ? 35 : 70,
                                  height: switchMerchant ? 35 : 70,
                                  marginBottom: 7,
                                }
                                : { width: 70, height: 70 },
                            ]}>
                            <Text
                              style={[
                                styles.pinNo,
                                { fontSize: switchMerchant ? 10 : 20 },
                              ]}>
                              5
                            </Text>
                          </View>
                        </TouchableOpacity>

                        <TouchableOpacity
                          onPress={() => {
                            PhoneonNumPadBtn(6);
                          }}>
                          <View
                            style={[
                              styles.pinBtn,
                              switchMerchant
                                ? {
                                  width: switchMerchant ? 35 : 70,
                                  height: switchMerchant ? 35 : 70,
                                  marginBottom: 7,
                                }
                                : { width: 70, height: 70 },
                            ]}>
                            <Text
                              style={[
                                styles.pinNo,
                                { fontSize: switchMerchant ? 10 : 20 },
                              ]}>
                              6
                            </Text>
                          </View>
                        </TouchableOpacity>
                      </View>

                      <View
                        style={{
                          flexDirection: 'row',
                          justifyContent: 'space-between',
                          alignSelf: 'center',
                          alignItems: 'center',
                          width: '100%',
                        }}>
                        <TouchableOpacity
                          onPress={() => {
                            PhoneonNumPadBtn(7);
                          }}>
                          <View
                            style={[
                              styles.pinBtn,
                              switchMerchant
                                ? {
                                  width: switchMerchant ? 35 : 70,
                                  height: switchMerchant ? 35 : 70,
                                  marginBottom: 7,
                                }
                                : { width: 70, height: 70 },
                            ]}>
                            <Text
                              style={[
                                styles.pinNo,
                                { fontSize: switchMerchant ? 10 : 20 },
                              ]}>
                              7
                            </Text>
                          </View>
                        </TouchableOpacity>

                        <TouchableOpacity
                          onPress={() => {
                            PhoneonNumPadBtn(8);
                          }}>
                          <View
                            style={[
                              styles.pinBtn,
                              switchMerchant
                                ? {
                                  width: switchMerchant ? 35 : 70,
                                  height: switchMerchant ? 35 : 70,
                                  marginBottom: 7,
                                }
                                : { width: 70, height: 70 },
                            ]}>
                            <Text
                              style={[
                                styles.pinNo,
                                { fontSize: switchMerchant ? 10 : 20 },
                              ]}>
                              8
                            </Text>
                          </View>
                        </TouchableOpacity>

                        <TouchableOpacity
                          onPress={() => {
                            PhoneonNumPadBtn(9);
                          }}>
                          <View
                            style={[
                              styles.pinBtn,
                              switchMerchant
                                ? {
                                  width: switchMerchant ? 35 : 70,
                                  height: switchMerchant ? 35 : 70,
                                  marginBottom: 7,
                                }
                                : { width: 70, height: 70 },
                            ]}>
                            <Text
                              style={[
                                styles.pinNo,
                                { fontSize: switchMerchant ? 10 : 20 },
                              ]}>
                              9
                            </Text>
                          </View>
                        </TouchableOpacity>
                      </View>
                      <View
                        style={{
                          flexDirection: 'row',
                          justifyContent: 'space-between',
                          alignSelf: 'center',
                          alignItems: 'center',
                          width: '100%',
                        }}>
                        <TouchableOpacity
                          onPress={() => {
                            if (phoneNumber.includes('+')) {
                              return;
                            }
                            else {
                              PhoneonNumPadBtn('+');
                            }
                          }}>
                          <View
                            style={[
                              styles.pinBtn,
                              switchMerchant
                                ? {
                                  width: switchMerchant ? 35 : 70,
                                  height: switchMerchant ? 35 : 70,
                                  marginBottom: 7,
                                }
                                : { width: 70, height: 70 },
                            ]}>
                            <Text
                              style={[
                                styles.pinNo,
                                { fontSize: switchMerchant ? 10 : 20 },
                              ]}>
                              +
                            </Text>
                          </View>
                        </TouchableOpacity>

                        <TouchableOpacity
                          onPress={() => {
                            PhoneonNumPadBtn(0);
                          }}>
                          <View
                            style={[
                              styles.pinBtn,
                              switchMerchant
                                ? {
                                  width: switchMerchant ? 35 : 70,
                                  height: switchMerchant ? 35 : 70,
                                  marginBottom: 7,
                                }
                                : { width: 70, height: 70 },
                            ]}>
                            <Text
                              style={[
                                styles.pinNo,
                                { fontSize: switchMerchant ? 10 : 20 },
                              ]}>
                              0
                            </Text>
                          </View>
                        </TouchableOpacity>

                        <TouchableOpacity
                          onPress={() => {
                            if (phoneNumber != '+6') {
                              PhoneonNumPadBtn(-1);
                            }
                          }}>
                          <View
                            style={[
                              styles.pinBtn,
                              switchMerchant
                                ? {
                                  width: switchMerchant ? 35 : 70,
                                  height: switchMerchant ? 35 : 70,
                                  marginBottom: 7,
                                }
                                : { width: 70, height: 70 },
                            ]}>
                            <Feather
                              name="chevron-left"
                              size={switchMerchant ? 13 : 30}
                              color={'black'}
                              style={{}}
                            />
                          </View>
                        </TouchableOpacity>
                      </View>
                    </View>
                  </View>
                  <View
                    style={{
                      flex: 1,
                      flexDirection: 'row',
                      alignItems: 'center',
                      alignSelf: 'center',
                    }}>
                    <TouchableOpacity
                      onPress={() => {
                        setCashbackModal(true);
                        setUserName(currCRMUser ? currCRMUser.name : '');
                      }}
                      disabled={phoneNumberChecking}>
                      <View
                        style={{
                          borderRadius: 10,
                          backgroundColor: phoneNumberChecking
                            ? 'grey'
                            : Colors.primaryColor,
                          paddingVertical: switchMerchant ? 4 : 10,
                          paddingHorizontal: switchMerchant ? 15 : 25,
                          marginBottom: switchMerchant ? 5 : 15,
                        }}>
                        <Text
                          style={{
                            color: Colors.whiteColor,
                            fontSize: switchMerchant ? 10 : 16,
                            fontFamily: 'NunitoSans-Bold',
                          }}>
                          Claim Cashback
                        </Text>
                      </View>
                    </TouchableOpacity>
                  </View>
                </View>
                <ModalView
                  visible={cashbackModal}
                  supportedOrientations={['landscape', 'portrait']}
                  style={{}}
                  animationType={'fade'}
                  transparent={true}>
                  <View
                    style={{
                      flex: 1,
                      backgroundColor: Colors.modalBgColor,
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}>
                    <View
                      style={{
                        width: switchMerchant
                          ? windowWidth * 0.77
                          : windowWidth * 0.77,
                        height: switchMerchant
                          ? windowHeight * 0.9
                          : windowHeight * 0.85,
                        backgroundColor: Colors.whiteColor,
                        borderRadius: 5,
                      }}>
                      <TouchableOpacity
                        style={{
                          position: 'absolute',
                          right: windowWidth * 0.02,
                          top: windowWidth * 0.02,

                          elevation: 1000,
                          zIndex: 1000,
                        }}
                        onPress={() => {
                          setCashbackModal(false);
                          setAmount('');
                        }}>
                        <AntDesign
                          name="closecircle"
                          size={switchMerchant ? 15 : 25}
                          color={Colors.fieldtTxtColor}
                        />
                      </TouchableOpacity>
                      <View style={{ flexDirection: 'row', flex: 1 }}>
                        <View style={{ width: '50%', padding: 30 }}>
                          {currCRMUser ? (
                            <View style={{ flexDirection: 'column', flex: 1 }}>
                              <Text
                                style={{
                                  fontSize: switchMerchant ? 16 : 24,
                                  fontFamily: 'NunitoSans-Bold',
                                }}>
                                Customer Details
                              </Text>
                              <Text
                                style={{
                                  fontSize: switchMerchant ? 10 : 16,
                                  paddingTop: switchMerchant ? 15 : 30,
                                }}>
                                First Name
                              </Text>
                              <Text
                                style={{
                                  fontSize: switchMerchant ? 10 : 16,
                                  fontFamily: 'NunitoSans-Bold',
                                  paddingTop: 10,
                                }}>
                                {currCRMUser ? currCRMUser.name : ''}
                              </Text>

                              <Text
                                style={{
                                  fontSize: switchMerchant ? 10 : 16,
                                  paddingTop: switchMerchant ? 15 : 30,
                                }}>
                                Phone Number
                              </Text>
                              <Text
                                style={{
                                  fontSize: switchMerchant ? 10 : 16,
                                  fontFamily: 'NunitoSans-Bold',
                                  paddingTop: 10,
                                }}>
                                {currCRMUser ? currCRMUser.number : ''}
                              </Text>

                              <View
                                style={{
                                  flexDirection: 'row',
                                  paddingTop: switchMerchant ? 15 : 30,
                                }}>
                                <Text
                                  style={{
                                    fontSize: switchMerchant ? 10 : 16,
                                    width: '40%',
                                  }}>
                                  Available Credit
                                </Text>
                                <Text
                                  style={{
                                    fontSize: switchMerchant ? 10 : 16,
                                    width: '50%',
                                  }}>
                                  Tier
                                </Text>
                              </View>
                              <View style={{ flexDirection: 'row', paddingTop: 10 }}>
                                <Text
                                  style={{
                                    fontSize: switchMerchant ? 10 : 16,
                                    width: '40%',
                                    fontFamily: 'NunitoSans-Bold',
                                  }}>
                                  RM {selectedCustomerLCCBalance.toFixed(2)}
                                </Text>
                                <Text
                                  style={{
                                    fontSize: switchMerchant ? 10 : 16,
                                    width: '50%',
                                    fontFamily: 'NunitoSans-Bold',
                                  }}>
                                  {selectedCustomerEdit &&
                                    selectedCustomerEdit.levelName
                                    ? selectedCustomerEdit.levelName
                                    : 'N/A'}
                                </Text>
                              </View>
                            </View>
                          ) : (
                            <>
                              <ScrollView style={{ flexDirection: 'column', flex: 1, }}>
                                <Text
                                  style={{
                                    fontSize: switchMerchant ? 16 : 24,
                                    fontFamily: 'NunitoSans-Bold',
                                  }}>
                                  Enter Guest Details
                                </Text>
                                <Text
                                  style={{
                                    fontSize: switchMerchant ? 10 : 16,
                                    paddingVertical: switchMerchant ? 7 : 30,
                                  }}>
                                  First Name *
                                </Text>
                                <TextInput
                                  style={{
                                    fontSize: switchMerchant ? 10 : 16,
                                    color: 'black',
                                    fontFamily: 'NunitoSans-Regular',
                                    borderBottomWidth: 1,
                                    borderBottomColor: 'black',
                                    width: switchMerchant ? 200 : 300,
                                    height: 40,
                                  }}
                                  placeholder=""
                                  placeholderTextColor={Platform.select({
                                    ios: '#a9a9a9',
                                  })}
                                  onChangeText={(text) => {
                                    setUserName(text);
                                  }}
                                  defaultValue={userName}
                                />
                                <Text
                                  style={{
                                    fontSize: switchMerchant ? 10 : 16,
                                    paddingTop: switchMerchant ? 20 : 30,
                                  }}>
                                  Phone Number
                                </Text>
                                <Text
                                  style={{
                                    fontSize: switchMerchant ? 10 : 16,
                                    fontFamily: 'NunitoSans-Bold',
                                    paddingTop: 10,
                                  }}>
                                  {phoneNumber}
                                </Text>
                                <Text
                                  style={{
                                    fontSize: switchMerchant ? 10 : 16,
                                    paddingVertical: switchMerchant ? 7 : 30,
                                  }}>
                                  Email Address
                                </Text>
                                <TextInput
                                  style={{
                                    fontSize: switchMerchant ? 10 : 16,
                                    color: 'black',
                                    fontFamily: 'NunitoSans-Regular',
                                    borderBottomWidth: 1,
                                    borderBottomColor: 'black',
                                    width: switchMerchant ? 200 : 300,
                                    height: 40,
                                  }}
                                  placeholder=""
                                  placeholderTextColor={Platform.select({
                                    ios: '#a9a9a9',
                                  })}
                                  onChangeText={(text) => {
                                    setEmail(text);
                                  }}
                                  defaultValue={email}
                                />
                                <Text
                                  style={{
                                    fontSize: switchMerchant ? 10 : 16,
                                    paddingVertical: switchMerchant ? 7 : 30,
                                  }}>
                                  Birthday
                                </Text>
                                <TouchableOpacity
                                  onPress={() => {
                                    setShowStartDatePicker(true);
                                  }}
                                  style={{
                                    // height: 50,
                                    height: switchMerchant ? 35 : 40,
                                    paddingHorizontal: switchMerchant ? 7 : 20,
                                    backgroundColor: Colors.fieldtBgColor,
                                    //marginBottom: 20,
                                    width: switchMerchant ? 100 : 140,
                                    //marginHorizontal: 10,
                                    flexDirection: 'row',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    alignContent: 'center',
                                    borderColor: '#E5E5E5',
                                    borderWidth: 1,
                                    fontFamily: 'NunitoSans-Regular',
                                    // fontSize: switchMerchant ? 11 : 14,
                                    borderRadius: 12,
                                  }}>
                                  <Text
                                    style={{
                                      //marginLeft: 15,
                                      color: 'black',
                                      fontSize: switchMerchant ? 10 : 14,
                                      textAlign: 'center',
                                    }}>
                                    {moment(startDate).format('DD MMM YYYY')}
                                  </Text>
                                </TouchableOpacity>
                              </ScrollView>
                              <View
                                style={{
                                  justifyContent: 'flex-start',
                                  alignSelf: 'center',
                                }}>
                                {/* <TouchableOpacity
                                onPress={() => {
                                  setCashbackModal(false);
                                  setAmount('');
                                }}>
                                <View
                                  style={{
                                    borderRadius: 10,
                                    backgroundColor: 'grey',
                                    paddingVertical: switchMerchant ? 5 : 10,
                                    paddingHorizontal: switchMerchant ? 30 : 50,
                                  }}>
                                  <Text
                                    style={{
                                      color: Colors.whiteColor,
                                      fontSize: switchMerchant ? 10 : 16,
                                      fontFamily: 'NunitoSans-Bold',
                                    }}>
                                    Continue
                                  </Text>
                                </View>
                              </TouchableOpacity> */}
                              </View>
                            </>
                          )}
                        </View>

                        <View style={{ width: '50%' }}>
                          <View
                            style={{
                              alignItems: 'center',
                              justifyContent: 'center',
                            }}>
                            <Text
                              style={{
                                fontSize: switchMerchant ? 15 : 20,
                                fontWeight: 'bold',
                                margin: switchMerchant ? 1 : 12,
                                paddingTop: 20,
                              }}>
                              Enter The Total Amount Spent
                            </Text>
                            <View
                              style={{
                                justifyContent: 'center',
                                flexDirection: 'row',
                                alignItems: 'center',
                                alignSelf: 'center',
                              }}>
                              <Text
                                style={{
                                  fontSize: switchMerchant ? 13 : 20,
                                  color: Colors.fieldtTxtColor,
                                }}>
                                RM
                              </Text>
                            </View>
                            <View style={{ justifyContent: 'center' }}>
                              <Text style={{ fontSize: switchMerchant ? 22 : 50 }}>
                                {amount.length === 0 ? '0' : amount}
                              </Text>
                            </View>
                          </View>
                          <View>
                            <View
                              style={{
                                flexDirection: 'row',
                                flexWrap: 'wrap',
                                justifyContent: 'space-between',
                                alignItems: 'center',
                                alignSelf: 'center',
                                width: '55%',
                                paddingTop: switchMerchant ? 5 : 30,
                              }}>
                              <View
                                style={{
                                  flexDirection: 'row',
                                  justifyContent: 'space-between',
                                  alignSelf: 'center',
                                  alignItems: 'center',
                                  width: '100%',
                                }}>
                                <TouchableOpacity
                                  onPress={() => {
                                    onNumPadBtn(1);
                                  }}>
                                  <View
                                    style={[
                                      styles.pinBtn,
                                      switchMerchant
                                        ? {
                                          width: switchMerchant ? 35 : 70,
                                          height: switchMerchant ? 35 : 70,
                                          marginBottom: 7,
                                        }
                                        : { width: 70, height: 70 },
                                    ]}>
                                    <Text
                                      style={[
                                        styles.pinNo,
                                        { fontSize: switchMerchant ? 10 : 20 },
                                      ]}>
                                      1
                                    </Text>
                                  </View>
                                </TouchableOpacity>

                                <TouchableOpacity
                                  onPress={() => {
                                    onNumPadBtn(2);
                                  }}>
                                  <View
                                    style={[
                                      styles.pinBtn,
                                      switchMerchant
                                        ? {
                                          width: switchMerchant ? 35 : 70,
                                          height: switchMerchant ? 35 : 70,
                                          marginBottom: 7,
                                        }
                                        : { width: 70, height: 70 },
                                    ]}>
                                    <Text
                                      style={[
                                        styles.pinNo,
                                        { fontSize: switchMerchant ? 10 : 20 },
                                      ]}>
                                      2
                                    </Text>
                                  </View>
                                </TouchableOpacity>

                                <TouchableOpacity
                                  onPress={() => {
                                    onNumPadBtn(3);
                                  }}>
                                  <View
                                    style={[
                                      styles.pinBtn,
                                      switchMerchant
                                        ? {
                                          width: switchMerchant ? 35 : 70,
                                          height: switchMerchant ? 35 : 70,
                                          marginBottom: 7,
                                        }
                                        : { width: 70, height: 70 },
                                    ]}>
                                    <Text
                                      style={[
                                        styles.pinNo,
                                        { fontSize: switchMerchant ? 10 : 20 },
                                      ]}>
                                      3
                                    </Text>
                                  </View>
                                </TouchableOpacity>
                              </View>
                              <View
                                style={{
                                  flexDirection: 'row',
                                  justifyContent: 'space-between',
                                  alignSelf: 'center',
                                  alignItems: 'center',
                                  width: '100%',
                                }}>
                                <TouchableOpacity
                                  onPress={() => {
                                    onNumPadBtn(4);
                                  }}>
                                  <View
                                    style={[
                                      styles.pinBtn,
                                      switchMerchant
                                        ? {
                                          width: switchMerchant ? 35 : 70,
                                          height: switchMerchant ? 35 : 70,
                                          marginBottom: 7,
                                        }
                                        : { width: 70, height: 70 },
                                    ]}>
                                    <Text
                                      style={[
                                        styles.pinNo,
                                        { fontSize: switchMerchant ? 10 : 20 },
                                      ]}>
                                      4
                                    </Text>
                                  </View>
                                </TouchableOpacity>

                                <TouchableOpacity
                                  onPress={() => {
                                    onNumPadBtn(5);
                                  }}>
                                  <View
                                    style={[
                                      styles.pinBtn,
                                      switchMerchant
                                        ? {
                                          width: switchMerchant ? 35 : 70,
                                          height: switchMerchant ? 35 : 70,
                                          marginBottom: 7,
                                        }
                                        : { width: 70, height: 70 },
                                    ]}>
                                    <Text
                                      style={[
                                        styles.pinNo,
                                        { fontSize: switchMerchant ? 10 : 20 },
                                      ]}>
                                      5
                                    </Text>
                                  </View>
                                </TouchableOpacity>

                                <TouchableOpacity
                                  onPress={() => {
                                    onNumPadBtn(6);
                                  }}>
                                  <View
                                    style={[
                                      styles.pinBtn,
                                      switchMerchant
                                        ? {
                                          width: switchMerchant ? 35 : 70,
                                          height: switchMerchant ? 35 : 70,
                                          marginBottom: 7,
                                        }
                                        : { width: 70, height: 70 },
                                    ]}>
                                    <Text
                                      style={[
                                        styles.pinNo,
                                        { fontSize: switchMerchant ? 10 : 20 },
                                      ]}>
                                      6
                                    </Text>
                                  </View>
                                </TouchableOpacity>
                              </View>
                              <View
                                style={{
                                  flexDirection: 'row',
                                  justifyContent: 'space-between',
                                  alignSelf: 'center',
                                  alignItems: 'center',
                                  width: '100%',
                                }}>
                                <TouchableOpacity
                                  onPress={() => {
                                    onNumPadBtn(7);
                                  }}>
                                  <View
                                    style={[
                                      styles.pinBtn,
                                      switchMerchant
                                        ? {
                                          width: switchMerchant ? 35 : 70,
                                          height: switchMerchant ? 35 : 70,
                                          marginBottom: 7,
                                        }
                                        : { width: 70, height: 70 },
                                    ]}>
                                    <Text
                                      style={[
                                        styles.pinNo,
                                        { fontSize: switchMerchant ? 10 : 20 },
                                      ]}>
                                      7
                                    </Text>
                                  </View>
                                </TouchableOpacity>

                                <TouchableOpacity
                                  onPress={() => {
                                    onNumPadBtn(8);
                                  }}>
                                  <View
                                    style={[
                                      styles.pinBtn,
                                      switchMerchant
                                        ? {
                                          width: switchMerchant ? 35 : 70,
                                          height: switchMerchant ? 35 : 70,
                                          marginBottom: 7,
                                        }
                                        : { width: 70, height: 70 },
                                    ]}>
                                    <Text
                                      style={[
                                        styles.pinNo,
                                        { fontSize: switchMerchant ? 10 : 20 },
                                      ]}>
                                      8
                                    </Text>
                                  </View>
                                </TouchableOpacity>

                                <TouchableOpacity
                                  onPress={() => {
                                    onNumPadBtn(9);
                                  }}>
                                  <View
                                    style={[
                                      styles.pinBtn,
                                      switchMerchant
                                        ? {
                                          width: switchMerchant ? 35 : 70,
                                          height: switchMerchant ? 35 : 70,
                                          marginBottom: 7,
                                        }
                                        : { width: 70, height: 70 },
                                    ]}>
                                    <Text
                                      style={[
                                        styles.pinNo,
                                        { fontSize: switchMerchant ? 10 : 20 },
                                      ]}>
                                      9
                                    </Text>
                                  </View>
                                </TouchableOpacity>
                              </View>
                              <View
                                style={{
                                  flexDirection: 'row',
                                  justifyContent: 'space-between',
                                  alignSelf: 'center',
                                  alignItems: 'center',
                                  width: '100%',
                                }}>
                                <TouchableOpacity
                                  onPress={() => {
                                    onNumPadBtn('+');
                                  }}>
                                  <View
                                    style={[
                                      styles.pinBtn,
                                      switchMerchant
                                        ? {
                                          width: switchMerchant ? 35 : 70,
                                          height: switchMerchant ? 35 : 70,
                                          marginBottom: 7,
                                        }
                                        : { width: 70, height: 70 },
                                    ]}>
                                    <Text
                                      style={[
                                        styles.pinNo,
                                        { fontSize: switchMerchant ? 10 : 20 },
                                      ]}>
                                      +
                                    </Text>
                                  </View>
                                </TouchableOpacity>

                                <TouchableOpacity
                                  onPress={() => {
                                    onNumPadBtn(0);
                                  }}>
                                  <View
                                    style={[
                                      styles.pinBtn,
                                      switchMerchant
                                        ? {
                                          width: switchMerchant ? 35 : 70,
                                          height: switchMerchant ? 35 : 70,
                                          marginBottom: 7,
                                        }
                                        : { width: 70, height: 70 },
                                    ]}>
                                    <Text
                                      style={[
                                        styles.pinNo,
                                        { fontSize: switchMerchant ? 10 : 20 },
                                      ]}>
                                      0
                                    </Text>
                                  </View>
                                </TouchableOpacity>

                                <TouchableOpacity
                                  onPress={() => {
                                    onNumPadBtn(-1);
                                  }}>
                                  <View
                                    style={[
                                      styles.pinBtn,
                                      switchMerchant
                                        ? {
                                          width: switchMerchant ? 35 : 70,
                                          height: switchMerchant ? 35 : 70,
                                          marginBottom: 7,
                                        }
                                        : { width: 70, height: 70 },
                                    ]}>
                                    <Feather
                                      name="chevron-left"
                                      size={switchMerchant ? 13 : 30}
                                      color={'black'}
                                      style={{}}
                                    />
                                  </View>
                                </TouchableOpacity>
                              </View>
                            </View>

                            <View
                              style={{
                                width: '100%',
                                alignItems: 'center',
                                alignSelf: 'center',
                              }}>
                              <TouchableOpacity
                                onPress={() => {
                                  if (amount && parseFloat(amount) > 0 && userName !== '') {
                                    claimCashback();
                                    // setCashbackdoneModal(true);
                                  } else {
                                    Alert.alert(
                                      'Info',
                                      'Amount must be more than 0\nPlease input first name',
                                    );
                                  }
                                }}
                                disabled={phoneNumberChecking}
                                style={{
                                  width: switchMerchant ? '50%' : '45%',
                                }}>
                                <View
                                  style={{
                                    borderRadius: 10,
                                    backgroundColor: phoneNumberChecking
                                      ? 'grey'
                                      : Colors.primaryColor,
                                    paddingVertical: switchMerchant ? 5 : 10,
                                    paddingHorizontal: 25,
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                  }}>
                                  <Text
                                    style={{
                                      color: Colors.whiteColor,
                                      fontSize: switchMerchant ? 10 : 16,
                                      fontFamily: 'NunitoSans-Bold',
                                    }}>
                                    Claim Cashback
                                  </Text>
                                </View>
                              </TouchableOpacity>
                            </View>
                          </View>
                        </View>
                      </View>
                    </View>
                  </View>
                </ModalView>
                <ModalView
                  visible={cashbackdoneModal}
                  supportedOrientations={['landscape', 'portrait']}
                  style={{}}
                  animationType={'fade'}
                  transparent={true}>
                  <View
                    style={{
                      flex: 1,
                      backgroundColor: Colors.modalBgColor,
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}>
                    <View
                      style={{
                        width: switchMerchant
                          ? windowWidth * 0.7
                          : windowWidth * 0.77,
                        height: switchMerchant
                          ? windowHeight * 0.85
                          : windowHeight * 0.75,
                        backgroundColor: Colors.whiteColor,
                        borderRadius: 5,
                      }}>
                      <ScrollView
                        scrollEnabled={true}
                        showsVerticalScrollIndicator={false}>
                        <TouchableOpacity
                          style={{
                            position: 'absolute',
                            right: windowWidth * 0.02,
                            top: windowWidth * 0.02,

                            elevation: 1000,
                            zIndex: 1000,
                          }}
                          onPress={() => {
                            setCashbackdoneModal(false);
                            setAmount('');
                            setAddnotes(false);
                            setNotetext('');
                          }}>
                          <AntDesign
                            name="closecircle"
                            size={switchMerchant ? 15 : 25}
                            color={Colors.fieldtTxtColor}
                          />
                        </TouchableOpacity>
                        <View
                          style={{
                            justifyContent: 'center',
                            alignSelf: 'center',
                            alignItems: 'center',
                            paddingVertical: switchMerchant ? 15 : 30,
                          }}>
                          <Text
                            style={{
                              fontSize: switchMerchant ? 16 : 24,
                              fontFamily: 'NunitoSans-Bold',
                            }}>
                            Credit added successfully
                          </Text>
                          <Text
                            style={{
                              fontSize: switchMerchant ? 10 : 16,
                            }}>
                            {currCRMUser ? currCRMUser.name : userName} will receive
                            credits in a moment
                          </Text>
                        </View>
                        <View
                          style={{
                            flexDirection: 'row',
                            alignSelf: 'center',
                            width: switchMerchant ? '80%' : '70%',
                            justifyContent: 'center',
                            paddingTop: switchMerchant ? 10 : 30,
                          }}>
                          <Text
                            style={{
                              fontSize: switchMerchant ? 10 : 16,
                              width: '20%',
                            }}>
                            Name
                          </Text>
                          <Text
                            style={{
                              fontSize: switchMerchant ? 10 : 16,
                              width: '40%',
                            }}>
                            Phone Number
                          </Text>
                          <Text
                            style={{
                              fontSize: switchMerchant ? 10 : 16,
                              width: '20%',
                            }}>
                            Total Spend
                          </Text>
                          <Text
                            style={{
                              fontSize: switchMerchant ? 10 : 16,
                              width: '20%',
                            }}>
                            Cashback
                          </Text>
                        </View>
                        <View
                          style={{
                            flexDirection: 'row',
                            alignSelf: 'center',
                            width: switchMerchant ? '80%' : '70%',
                            justifyContent: 'center',
                            paddingTop: switchMerchant ? 5 : 10,
                          }}>
                          <Text
                            style={{
                              fontSize: switchMerchant ? 10 : 16,
                              fontFamily: 'NunitoSans-Bold',
                              width: '20%',
                            }}>
                            {currCRMUser ? currCRMUser.name : userName}
                          </Text>
                          <Text
                            style={{
                              fontSize: switchMerchant ? 10 : 16,
                              fontFamily: 'NunitoSans-Bold',
                              width: '40%',
                            }}>
                            {currCRMUser ? currCRMUser.number : phoneNumber}
                          </Text>
                          <Text
                            style={{
                              fontSize: switchMerchant ? 10 : 16,
                              fontFamily: 'NunitoSans-Bold',
                              width: '20%',
                            }}>
                            RM {amount}
                          </Text>
                          <Text
                            style={{
                              fontSize: switchMerchant ? 10 : 16,
                              fontFamily: 'NunitoSans-Bold',
                              width: '20%',
                            }}>
                            {/* RM {amount * 0.01} */}
                            RM{' '}
                            {(selectedCustomerLCCTransactions[0]
                              ? selectedCustomerLCCTransactions[0].amount
                              : 0
                            ).toFixed(2)}
                          </Text>
                        </View>
                        <View
                          style={{
                            justifyContent: 'flex-start',
                            alignSelf: 'center',
                            paddingTop: switchMerchant ? 10 : 40,
                          }}>
                          <TouchableOpacity
                            onPress={() => {
                              {
                                addnotes == true
                                  ? setAddnotes(false)
                                  : setAddnotes(true);
                              }
                            }}>
                            <Text
                              style={{
                                color: Colors.primaryColor,
                                fontSize: switchMerchant ? 10 : 16,
                                borderBottomWidth: 1,
                                borderBottomColor: Colors.primaryColor,
                              }}>
                              Add notes
                            </Text>
                          </TouchableOpacity>
                        </View>
                        {addnotes == true ? (
                          <TextInput
                            style={{
                              fontSize: switchMerchant ? 10 : 16,
                              color: 'black',
                              fontFamily: 'NunitoSans-Regular',
                              borderBottomWidth: 0.5,
                              borderBottomColor: 'black',
                              width: '40%',
                              height: switchMerchant ? 35 : 40,
                              alignSelf: 'center',
                              alignItems: 'center',
                              marginVertical: switchMerchant ? 5 : 20,
                            }}
                            placeholder=""
                            placeholderTextColor={Platform.select({
                              ios: '#a9a9a9',
                            })}
                            onChangeText={(text) => {
                              setNotetext(text);
                            }}></TextInput>
                        ) : (
                          <View
                            style={{
                              height: switchMerchant ? 35 : 40,
                              marginVertical: switchMerchant ? 5 : 20,
                            }}></View>
                        )}
                        <View
                          style={{
                            justifyContent: 'flex-start',
                            alignSelf: 'center',
                            paddingTop: switchMerchant ? 10 : 20,
                          }}>
                          <TouchableOpacity
                            onPress={() => {
                              setCashbackdoneModal(false);
                              setAddnotes(false);
                              setNotetext('');
                              setAmount('');
                              setPhoneNumber('+60');
                              setCreditSection(CREDIT_SECTION.HOME);
                            }}>
                            <View
                              style={{
                                borderRadius: 10,
                                backgroundColor: Colors.primaryColor,
                                paddingVertical: switchMerchant ? 5 : 10,
                                paddingHorizontal: 50,
                                marginTop: 20,
                              }}>
                              <Text
                                style={{
                                  color: Colors.whiteColor,
                                  fontSize: switchMerchant ? 10 : 16,
                                  fontFamily: 'NunitoSans-Bold',
                                }}>
                                Done
                              </Text>
                            </View>
                          </TouchableOpacity>
                        </View>
                        <View
                          style={{
                            flexDirection: 'row',
                            alignSelf: 'center',
                            paddingTop: 30,
                            alignItems: 'center',
                          }}>
                          <TouchableOpacity
                            onPress={() => {
                              setCashbackdoneModal(false);
                              setAddnotes(false);
                              setNotetext('');
                              setAmount('');
                              setPhoneNumber(`${phoneNumber}`);
                              setCreditSection(CREDIT_SECTION.ADD_CASHBACK);
                            }}>
                            <Text
                              style={{
                                fontSize: switchMerchant ? 10 : 16,
                                color: Colors.primaryColor,
                              }}>
                              Also Add Cashback?
                            </Text>
                          </TouchableOpacity>
                          <Text
                            style={{
                              marginHorizontal: 20,
                              fontSize: switchMerchant ? 10 : 14,
                            }}>
                            or
                          </Text>
                          <TouchableOpacity
                            onPress={() => {
                              setCashbackdoneModal(false);
                              setAddnotes(false);
                              setNotetext('');
                              setAmount('');
                              setPhoneNumber(`${phoneNumber}`);
                              setCreditSection(CREDIT_SECTION.REDEEM_CREDIT);
                            }}>
                            <Text
                              style={{
                                fontSize: switchMerchant ? 10 : 16,
                                color: Colors.primaryColor,
                              }}>
                              Want to redeem a campaign?
                            </Text>
                          </TouchableOpacity>
                        </View>
                        <View
                          style={{
                            flexDirection: 'row',
                            alignSelf: 'center',
                            paddingTop: 20,
                          }}>
                          <TouchableOpacity
                            onPress={() => {
                              setCashbackdoneModal(false);
                              setAddnotes(false);
                              setNotetext('');
                              setAmount('');
                              setPhoneNumber(`${phoneNumber}`);
                              setCreditSection(CREDIT_SECTION.DEDUCT_CREDIT);
                            }}>
                            <Text
                              style={{
                                fontSize: switchMerchant ? 10 : 16,
                                color: Colors.tabRed,
                              }}>
                              Want to deduct credit?
                            </Text>
                          </TouchableOpacity>
                          <Text
                            style={{
                              marginHorizontal: 20,
                              fontSize: switchMerchant ? 10 : 14,
                            }}>
                            or
                          </Text>
                          <TouchableOpacity
                            onPress={() => {
                              setCashbackdoneModal(false);
                              setAddnotes(false);
                              setNotetext('');
                              setAmount('');
                              setPhoneNumber(`${phoneNumber}`);
                              setCreditSection(CREDIT_SECTION.ADD_CREDIT);
                            }}>
                            <Text
                              style={{
                                fontSize: switchMerchant ? 10 : 16,
                                color: Colors.primaryColor,
                                marginBottom: switchMerchant ? 30 : 0,
                              }}>
                              Want to add extra credit?
                            </Text>
                          </TouchableOpacity>
                        </View>
                      </ScrollView>
                    </View>
                  </View>
                </ModalView>
              </View>
            ) : (
              <></>
            )}
            {/* ///////////// Redeem Credit /////////////// */}

            {creditSection === CREDIT_SECTION.REDEEM_CREDIT ? (
              <View
                style={{
                  flex: 1,
                  width: '100%',
                  height: '100%',
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  backgroundColor: Colors.highlightColor,
                }}>
                <View
                  style={{
                    width:
                      Platform.OS === 'ios'
                        ? windowWidth * 0.44
                        : windowWidth * 0.43,
                    backgroundColor: Colors.whiteColor,
                    borderRadius: 5,
                  }}>
                  {/* <View
                  style={{
                    flexDirection: 'row',
                  }}>
                  <TouchableOpacity
                    style={{
                      height: windowHeight * 0.055,
                      flexDirection: 'row',
                      alignContent: 'center',
                      alignItems: 'center',
                      marginTop: 10,
                      marginLeft: 10,
                    }}
                    onPress={() => {
                      setCreditSection(CREDIT_SECTION.HOME);
                      setPhoneNumber('+60');
                    }}>
                    <Feather
                      name="chevron-left"
                      size={switchMerchant ? 20 : 30}
                      color={Colors.primaryColor}
                    />
                    <Text
                      style={{
                        fontFamily: 'NunitoSans-Bold',
                        fontSize: switchMerchant ? 14 : 17,
                        color: Colors.primaryColor,
                        marginBottom: Platform.OS === 'ios' ? 0 : 1,
                      }}>
                      Back
                    </Text>
                  </TouchableOpacity>
                </View> */}
                  <View
                    style={{ paddingLeft: 20, paddingTop: switchMerchant ? 5 : 10 }}>
                    <Text style={{ fontSize: switchMerchant ? 16 : 24 }}>
                      {currOutlet.name}
                    </Text>
                    <Text
                      style={{
                        fontSize: switchMerchant ? 16 : 24,
                        fontWeight: 'bold',
                        paddingVertical: switchMerchant ? 2 : 10,
                      }}>
                      Redeem Credit Here
                    </Text>
                    <Text style={{ fontSize: switchMerchant ? 10 : 16 }}>
                      Use Your Credits To Purchase Goods
                    </Text>
                  </View>
                  <View
                    style={{
                      paddingLeft: 20,
                      paddingTop: switchMerchant ? 10 : 20,
                      flex: 1,
                    }}>
                    <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                      <Text
                        style={{
                          fontSize: switchMerchant ? 12 : 22,
                          fontWeight: 'bold',
                          backgroundColor: Colors.highlightColor,
                          color: Colors.primaryColor,
                          borderRadius: 25,
                          alignSelf: 'center',
                          alignItems: 'center',
                          justifyContent: 'center',
                          paddingHorizontal: switchMerchant ? 10 : 20,
                          paddingVertical: switchMerchant ? 5 : 10,
                          marginRight: 10,
                        }}>
                        a
                      </Text>
                      <Text style={{ fontSize: switchMerchant ? 10 : 16 }}>
                        Enter Your Phone Number
                      </Text>
                    </View>
                    <View
                      style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                        paddingVertical: switchMerchant ? 10 : 20,
                      }}>
                      <Text
                        style={{
                          fontSize: switchMerchant ? 12 : 22,
                          fontWeight: 'bold',
                          backgroundColor: Colors.highlightColor,
                          color: Colors.primaryColor,
                          borderRadius: 25,
                          alignSelf: 'center',
                          alignItems: 'center',
                          justifyContent: 'center',
                          paddingHorizontal: switchMerchant ? 10 : 20,
                          paddingVertical: switchMerchant ? 5 : 10,
                          marginRight: 10,
                        }}>
                        b
                      </Text>
                      <Text
                        style={{
                          fontSize: switchMerchant ? 10 : 16,
                          paddingVertical: switchMerchant ? 0 : 10,
                        }}>
                        Let The Staff Know What You Would Like To Redeem
                      </Text>
                    </View>
                    <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                      <Text
                        style={{
                          fontSize: switchMerchant ? 12 : 22,
                          fontWeight: 'bold',
                          backgroundColor: Colors.highlightColor,
                          color: Colors.primaryColor,
                          borderRadius: 25,
                          alignSelf: 'center',
                          alignItems: 'center',
                          justifyContent: 'center',
                          paddingHorizontal: switchMerchant ? 10 : 20,
                          paddingVertical: switchMerchant ? 5 : 10,
                          marginRight: 10,
                        }}>
                        c
                      </Text>
                      <Text style={{ fontSize: switchMerchant ? 10 : 16 }}>
                        Enjoy Your Selection, Without Dealing With Any Cash
                      </Text>
                    </View>
                  </View>
                  <View
                    style={{
                      flex: 1,
                      paddingHorizontal: 20,
                      paddingTop: 20,
                      justifyContent: 'center',
                    }}>
                    {/* <View
                    style={{
                      flexDirection: 'row',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                    }}>
                    <Text style={{ fontSize: switchMerchant ? 10 : 14 }}>
                      Powered By{' '}
                      <Text
                        style={{
                          color: Colors.primaryColor,
                          fontSize: switchMerchant ? 12 : 16,
                        }}>
                        KooDoo
                      </Text>
                    </Text>
                    <TouchableOpacity style={{ flexDirection: 'row' }}>
                      <Feather
                        name="help-circle"
                        size={switchMerchant ? 15 : 20}
                        style={{ color: Colors.primaryColor, paddingRight: 5 }}
                      />
                      <Text
                        style={{
                          color: Colors.primaryColor,
                          fontSize: switchMerchant ? 10 : 14,
                        }}>
                        Help
                      </Text>
                    </TouchableOpacity>
                  </View> */}
                  </View>
                  <View
                    style={[{
                      paddingTop: 15,
                      flexDirection: 'row',
                      alignItems: 'center',
                      justifyContent: 'space-evenly',
                      paddingBottom: 30,
                    }]}>

                    <TouchableOpacity
                      style={[
                        {
                          width: '25%',
                          height: 140,
                          backgroundColor: Colors.tabMint,
                          borderRadius: 10,
                          flexDirection: 'row',
                          alignItems: 'center',
                          justifyContent: 'center',
                          paddingHorizontal: 20,
                          paddingVertical: 15,
                          shadowColor: '#000',
                          shadowOffset: {
                            width: 0,
                            height: 2,
                          },
                          shadowOpacity: 0.22,
                          shadowRadius: 3.22,
                          elevation: 3,
                        },
                      ]}
                      onPress={() => {
                        setCreditSection(CREDIT_SECTION.ADD_CASHBACK);
                      }}>

                      <View style={{ alignItems: 'center', }}>
                        <MaterialCommunityIcons
                          name="cash-plus"
                          size={switchMerchant ? 30 : 45}
                          style={{ color: Colors.whiteColor, }}
                        />
                        <Text
                          style={[
                            {
                              fontWeight: 'bold',
                              color: Colors.whiteColor,
                              fontSize: 18,
                              textAlign: 'center',
                              paddingBottom: 5,
                              paddingVertical: 10,
                            },
                          ]}>
                          Earn Point
                        </Text>
                      </View>
                    </TouchableOpacity>

                    <TouchableOpacity
                      style={[
                        {
                          width: '25%',
                          height: 140,
                          backgroundColor: Colors.tabRed,
                          borderRadius: 10,
                          flexDirection: 'row',
                          alignItems: 'center',
                          justifyContent: 'center',
                          paddingHorizontal: 20,
                          paddingVertical: 15,
                          shadowColor: '#000',
                          shadowOffset: {
                            width: 0,
                            height: 2,
                          },
                          shadowOpacity: 0.22,
                          shadowRadius: 3.22,
                          elevation: 3,
                        },
                      ]}
                      onPress={() => {
                        setCreditSection(CREDIT_SECTION.DEDUCT_CREDIT);
                      }}>
                      <View style={{ alignItems: 'center', }}>
                        <MaterialIcons
                          name="redeem"
                          size={switchMerchant ? 30 : 45}
                          style={{ color: Colors.whiteColor, }}
                        />
                        <Text
                          style={[
                            {
                              fontWeight: 'bold',
                              color: Colors.whiteColor,
                              fontSize: 18,
                              textAlign: 'center',
                              paddingBottom: 5,
                              paddingVertical: 10,
                            },
                          ]}>
                          Deduct Point
                        </Text>
                      </View>
                    </TouchableOpacity>
                    <TouchableOpacity
                      style={[
                        {
                          width: '25%',
                          height: 140,
                          backgroundColor: Colors.tabGold,
                          borderRadius: 10,
                          flexDirection: 'row',
                          alignItems: 'center',
                          justifyContent: 'center',
                          paddingHorizontal: 20,
                          paddingVertical: 15,
                          shadowColor: '#000',
                          shadowOffset: {
                            width: 0,
                            height: 2,
                          },
                          shadowOpacity: 0.22,
                          shadowRadius: 3.22,
                          elevation: 3,
                        },
                      ]}
                      onPress={() => {
                        setCreditSection(CREDIT_SECTION.ADD_CREDIT);
                      }}>

                      <View style={{ alignItems: 'center', }}>
                        <Entypo
                          name="credit"
                          size={switchMerchant ? 30 : 45}
                          style={{ color: Colors.whiteColor, }}
                        />
                        <Text
                          style={[
                            {
                              fontWeight: 'bold',
                              color: Colors.whiteColor,
                              fontSize: 18,
                              textAlign: 'center',
                              paddingBottom: 5,
                              paddingVertical: 10,
                            },
                          ]}>
                          Topup Points
                        </Text>
                      </View>
                    </TouchableOpacity>
                  </View>
                </View>
                <View
                  style={{
                    width:
                      Platform.OS === 'ios'
                        ? windowWidth * 0.42
                        : windowWidth * 0.43,
                    backgroundColor: Colors.whiteColor,
                    borderRadius: 5,
                  }}>
                  <View
                    style={{
                      alignItems: 'center',
                      justifyContent: 'center',
                      flex: 1,
                      paddingTop: Platform.OS === 'ios' ? 0 : 10,
                      marginTop: Platform.OS === 'ios' ? 10 : 0,
                    }}>
                    <Text
                      style={{
                        fontSize: switchMerchant ? 15 : 25,
                        fontWeight: 'bold',
                      }}>
                      Enter Your Phone Number
                    </Text>
                  </View>
                  <View
                    style={{
                      justifyContent: 'center',
                      alignItems: 'center',
                      flex: 1,
                    }}>
                    <Text style={{ fontSize: switchMerchant ? 22 : 45 }}>
                      {phoneNumber}
                    </Text>
                  </View>
                  <View>
                    <View
                      style={{
                        flexDirection: 'row',
                        flexWrap: 'wrap',
                        justifyContent: 'space-between',
                        alignItems: 'center',
                        alignSelf: 'center',
                        width: Platform.OS === 'ios' ? 270 : '45%',
                        paddingTop: switchMerchant ? 10 : 30,
                      }}>
                      <View
                        style={{
                          flexDirection: 'row',
                          justifyContent: 'space-between',
                          alignSelf: 'center',
                          alignItems: 'center',
                          width: '100%',
                        }}>
                        <TouchableOpacity
                          onPress={() => {
                            PhoneonNumPadBtn(1);
                          }}>
                          <View
                            style={[
                              styles.pinBtn,
                              switchMerchant
                                ? {
                                  width: switchMerchant ? 35 : 70,
                                  height: switchMerchant ? 35 : 70,
                                  marginBottom: 7,
                                }
                                : { width: 70, height: 70 },
                            ]}>
                            <Text
                              style={[
                                styles.pinNo,
                                { fontSize: switchMerchant ? 10 : 20 },
                              ]}>
                              1
                            </Text>
                          </View>
                        </TouchableOpacity>

                        <TouchableOpacity
                          onPress={() => {
                            PhoneonNumPadBtn(2);
                          }}>
                          <View
                            style={[
                              styles.pinBtn,
                              switchMerchant
                                ? {
                                  width: switchMerchant ? 35 : 70,
                                  height: switchMerchant ? 35 : 70,
                                  marginBottom: 7,
                                }
                                : { width: 70, height: 70 },
                            ]}>
                            <Text
                              style={[
                                styles.pinNo,
                                { fontSize: switchMerchant ? 10 : 20 },
                              ]}>
                              2
                            </Text>
                          </View>
                        </TouchableOpacity>

                        <TouchableOpacity
                          onPress={() => {
                            PhoneonNumPadBtn(3);
                          }}>
                          <View
                            style={[
                              styles.pinBtn,
                              switchMerchant
                                ? {
                                  width: switchMerchant ? 35 : 70,
                                  height: switchMerchant ? 35 : 70,
                                  marginBottom: 7,
                                }
                                : { width: 70, height: 70 },
                            ]}>
                            <Text
                              style={[
                                styles.pinNo,
                                { fontSize: switchMerchant ? 10 : 20 },
                              ]}>
                              3
                            </Text>
                          </View>
                        </TouchableOpacity>
                      </View>
                      <View
                        style={{
                          flexDirection: 'row',
                          justifyContent: 'space-between',
                          alignSelf: 'center',
                          alignItems: 'center',
                          width: '100%',
                        }}>
                        <TouchableOpacity
                          onPress={() => {
                            PhoneonNumPadBtn(4);
                          }}>
                          <View
                            style={[
                              styles.pinBtn,
                              switchMerchant
                                ? {
                                  width: switchMerchant ? 35 : 70,
                                  height: switchMerchant ? 35 : 70,
                                  marginBottom: 7,
                                }
                                : { width: 70, height: 70 },
                            ]}>
                            <Text
                              style={[
                                styles.pinNo,
                                { fontSize: switchMerchant ? 10 : 20 },
                              ]}>
                              4
                            </Text>
                          </View>
                        </TouchableOpacity>

                        <TouchableOpacity
                          onPress={() => {
                            PhoneonNumPadBtn(5);
                          }}>
                          <View
                            style={[
                              styles.pinBtn,
                              switchMerchant
                                ? {
                                  width: switchMerchant ? 35 : 70,
                                  height: switchMerchant ? 35 : 70,
                                  marginBottom: 7,
                                }
                                : { width: 70, height: 70 },
                            ]}>
                            <Text
                              style={[
                                styles.pinNo,
                                { fontSize: switchMerchant ? 10 : 20 },
                              ]}>
                              5
                            </Text>
                          </View>
                        </TouchableOpacity>

                        <TouchableOpacity
                          onPress={() => {
                            PhoneonNumPadBtn(6);
                          }}>
                          <View
                            style={[
                              styles.pinBtn,
                              switchMerchant
                                ? {
                                  width: switchMerchant ? 35 : 70,
                                  height: switchMerchant ? 35 : 70,
                                  marginBottom: 7,
                                }
                                : { width: 70, height: 70 },
                            ]}>
                            <Text
                              style={[
                                styles.pinNo,
                                { fontSize: switchMerchant ? 10 : 20 },
                              ]}>
                              6
                            </Text>
                          </View>
                        </TouchableOpacity>
                      </View>

                      <View
                        style={{
                          flexDirection: 'row',
                          justifyContent: 'space-between',
                          alignSelf: 'center',
                          alignItems: 'center',
                          width: '100%',
                        }}>
                        <TouchableOpacity
                          onPress={() => {
                            PhoneonNumPadBtn(7);
                          }}>
                          <View
                            style={[
                              styles.pinBtn,
                              switchMerchant
                                ? {
                                  width: switchMerchant ? 35 : 70,
                                  height: switchMerchant ? 35 : 70,
                                  marginBottom: 7,
                                }
                                : { width: 70, height: 70 },
                            ]}>
                            <Text
                              style={[
                                styles.pinNo,
                                { fontSize: switchMerchant ? 10 : 20 },
                              ]}>
                              7
                            </Text>
                          </View>
                        </TouchableOpacity>

                        <TouchableOpacity
                          onPress={() => {
                            PhoneonNumPadBtn(8);
                          }}>
                          <View
                            style={[
                              styles.pinBtn,
                              switchMerchant
                                ? {
                                  width: switchMerchant ? 35 : 70,
                                  height: switchMerchant ? 35 : 70,
                                  marginBottom: 7,
                                }
                                : { width: 70, height: 70 },
                            ]}>
                            <Text
                              style={[
                                styles.pinNo,
                                { fontSize: switchMerchant ? 10 : 20 },
                              ]}>
                              8
                            </Text>
                          </View>
                        </TouchableOpacity>

                        <TouchableOpacity
                          onPress={() => {
                            PhoneonNumPadBtn(9);
                          }}>
                          <View
                            style={[
                              styles.pinBtn,
                              switchMerchant
                                ? {
                                  width: switchMerchant ? 35 : 70,
                                  height: switchMerchant ? 35 : 70,
                                  marginBottom: 7,
                                }
                                : { width: 70, height: 70 },
                            ]}>
                            <Text
                              style={[
                                styles.pinNo,
                                { fontSize: switchMerchant ? 10 : 20 },
                              ]}>
                              9
                            </Text>
                          </View>
                        </TouchableOpacity>
                      </View>
                      <View
                        style={{
                          flexDirection: 'row',
                          justifyContent: 'space-between',
                          alignSelf: 'center',
                          alignItems: 'center',
                          width: '100%',
                        }}>
                        <TouchableOpacity
                          onPress={() => {
                            if (phoneNumber.includes('+')) {
                              return;
                            }
                            else {
                              PhoneonNumPadBtn('+');
                            }
                          }}>
                          <View
                            style={[
                              styles.pinBtn,
                              switchMerchant
                                ? {
                                  width: switchMerchant ? 35 : 70,
                                  height: switchMerchant ? 35 : 70,
                                  marginBottom: 7,
                                }
                                : { width: 70, height: 70 },
                            ]}>
                            <Text
                              style={[
                                styles.pinNo,
                                { fontSize: switchMerchant ? 10 : 20 },
                              ]}>
                              +
                            </Text>
                          </View>
                        </TouchableOpacity>

                        <TouchableOpacity
                          onPress={() => {
                            PhoneonNumPadBtn(0);
                          }}>
                          <View
                            style={[
                              styles.pinBtn,
                              switchMerchant
                                ? {
                                  width: switchMerchant ? 35 : 70,
                                  height: switchMerchant ? 35 : 70,
                                  marginBottom: 7,
                                }
                                : { width: 70, height: 70 },
                            ]}>
                            <Text
                              style={[
                                styles.pinNo,
                                { fontSize: switchMerchant ? 10 : 20 },
                              ]}>
                              0
                            </Text>
                          </View>
                        </TouchableOpacity>

                        <TouchableOpacity
                          onPress={() => {
                            if (phoneNumber != '+6') {
                              PhoneonNumPadBtn(-1);
                            }
                          }}>
                          <View
                            style={[
                              styles.pinBtn,
                              switchMerchant
                                ? {
                                  width: switchMerchant ? 35 : 70,
                                  height: switchMerchant ? 35 : 70,
                                  marginBottom: 7,
                                }
                                : { width: 70, height: 70 },
                            ]}>
                            <Feather
                              name="chevron-left"
                              size={switchMerchant ? 13 : 30}
                              color={'black'}
                              style={{}}
                            />
                          </View>
                        </TouchableOpacity>
                      </View>
                    </View>
                  </View>
                  <View
                    style={{
                      flex: 1,
                      flexDirection: 'row',
                      alignItems: 'center',
                      alignSelf: 'center',
                    }}>
                    <TouchableOpacity
                      onPress={() => {
                        if (currCRMUser) {
                          setCampaignModal(true);
                        } else {
                          Alert.alert('Info', 'No existing user found');
                        }
                      }}
                      disabled={phoneNumberChecking}>
                      <View
                        style={{
                          borderRadius: 10,
                          backgroundColor: phoneNumberChecking
                            ? 'grey'
                            : Colors.primaryColor,
                          paddingVertical: switchMerchant ? 4 : 10,
                          paddingHorizontal: switchMerchant ? 15 : 25,
                          marginBottom: switchMerchant ? 5 : 15,
                        }}>
                        <Text
                          style={{
                            color: Colors.whiteColor,
                            fontSize: switchMerchant ? 10 : 16,
                            fontFamily: 'NunitoSans-Bold',
                          }}>
                          Redeem Credit
                        </Text>
                      </View>
                    </TouchableOpacity>
                  </View>
                </View>
                <ModalView
                  visible={campaignModal}
                  supportedOrientations={['landscape', 'portrait']}
                  style={{}}
                  animationType={'fade'}
                  transparent={true}>
                  <View
                    style={{
                      flex: 1,
                      backgroundColor: Colors.modalBgColor,
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}>
                    <View
                      style={{
                        width: switchMerchant
                          ? windowWidth * 0.7
                          : windowWidth * 0.77,
                        height: switchMerchant
                          ? windowHeight * 0.85
                          : windowHeight * 0.8,
                        backgroundColor: Colors.whiteColor,
                        borderRadius: 5,
                      }}>
                      <TouchableOpacity
                        style={{
                          position: 'absolute',
                          right: windowWidth * 0.02,
                          top: windowWidth * 0.02,

                          elevation: 1000,
                          zIndex: 1000,
                        }}
                        onPress={() => {
                          setCampaignModal(false);
                          setAmount('');
                        }}>
                        <AntDesign
                          name="closecircle"
                          size={switchMerchant ? 15 : 25}
                          color={Colors.fieldtTxtColor}
                        />
                      </TouchableOpacity>
                      <View
                        style={{
                          justifyContent: 'center',
                          alignSelf: 'center',
                          alignItems: 'center',
                          paddingVertical: switchMerchant ? 15 : 30,
                        }}>
                        <Text
                          style={{
                            fontSize: switchMerchant ? 16 : 24,
                            fontFamily: 'NunitoSans-Bold',
                          }}>
                          Eligible Vouchers
                        </Text>
                      </View>
                      {/* Campaign List start */}
                      <View
                        style={{
                          height: switchMerchant ? '53%' : '55%',
                          alignSelf: 'center',
                        }}>
                        {taggableVouchers.filter((taggableVoucher) => {
                          // var isValid = false;

                          for (
                            var i = 0;
                            i < selectedCustomerUserTaggableVouchers.length;
                            i++
                          ) {
                            if (
                              selectedCustomerUserTaggableVouchers[i]
                                .voucherId === taggableVoucher.uniqueId &&
                              selectedCustomerUserTaggableVouchers[i] &&
                              selectedCustomerUserTaggableVouchers[i].redeemDate ===
                              null
                            ) {
                              return true;
                            }
                          }
                        }).length <= 0 ? (
                          <Text style={{ fontSize: switchMerchant ? 10 : 16 }}>
                            No Redeemable Vouchers Found
                          </Text>
                        ) : (
                          <FlatList
                            data={taggableVouchers.filter((taggableVoucher) => {
                              // var isValid = false;

                              for (
                                var i = 0;
                                i < selectedCustomerUserTaggableVouchers.length;
                                i++
                              ) {
                                if (
                                  selectedCustomerUserTaggableVouchers[i]
                                    .voucherId ===
                                  taggableVoucher.uniqueId &&
                                  selectedCustomerUserTaggableVouchers[i]
                                    .redeemDate === null
                                ) {
                                  return true;
                                }
                              }
                            })}
                            renderItem={renderLoyaltyCampaign}
                            keyExtractor={(item, index) => String(index)}
                            style={{
                              // width: '100%',
                              width: windowWidth * 0.6,
                              // backgroundColor: 'red',
                            }}
                          // initialNumToRender={8}
                          />
                        )}
                      </View>
                      {/* Campaign List end */}
                      <View
                        style={{
                          flexDirection: 'row',
                          alignSelf: 'center',
                          width: switchMerchant ? '60%' : '50%',
                          justifyContent: 'center',
                          paddingTop: switchMerchant ? 15 : 30,
                        }}>
                        <Text
                          style={{
                            fontSize: switchMerchant ? 10 : 16,
                            width: '25%',
                          }}>
                          Name
                        </Text>
                        <Text
                          style={{
                            fontSize: switchMerchant ? 10 : 16,
                            width: '40%',
                          }}>
                          Phone Number
                        </Text>
                        <Text
                          style={{
                            fontSize: switchMerchant ? 10 : 16,
                            width: '35%',
                          }}>
                          Balance
                        </Text>
                      </View>
                      <View
                        style={{
                          flexDirection: 'row',
                          alignSelf: 'center',
                          width: switchMerchant ? '60%' : '50%',
                          justifyContent: 'center',
                          paddingTop: switchMerchant ? 5 : 10,
                        }}>
                        <Text
                          style={{
                            fontSize: switchMerchant ? 10 : 16,
                            fontFamily: 'NunitoSans-Bold',
                            width: '25%',
                          }}>
                          {currCRMUser ? currCRMUser.name : ''}
                        </Text>
                        <Text
                          style={{
                            fontSize: switchMerchant ? 10 : 16,
                            fontFamily: 'NunitoSans-Bold',
                            width: '40%',
                          }}>
                          {currCRMUser ? currCRMUser.number : ''}
                        </Text>
                        <Text
                          style={{
                            fontSize: switchMerchant ? 10 : 16,
                            fontFamily: 'NunitoSans-Bold',
                            width: '35%',
                          }}>
                          RM {selectedCustomerLCCBalance.toFixed(2)}
                        </Text>
                      </View>
                      <View
                        style={{
                          justifyContent: 'flex-start',
                          alignSelf: 'center',
                          paddingTop: switchMerchant ? 10 : 20,
                        }}>
                        <TouchableOpacity
                          onPress={() => {
                            setCampaignModal(false);
                            setTimeout(() => {
                              // setRedeemCreditModal(true);

                              setCreditSection(CREDIT_SECTION.DEDUCT_CREDIT);
                              setDeductCreditModal(true);
                            }, 1000);
                          }}>
                          <View
                            style={{
                              borderRadius: 10,
                              borderWidth: 1,
                              borderColor: Colors.primaryColor,
                              paddingVertical: switchMerchant ? 5 : 10,
                              paddingHorizontal: 50,
                            }}>
                            <Text
                              style={{
                                color: Colors.primaryColor,
                                fontSize: switchMerchant ? 10 : 16,
                                fontFamily: 'NunitoSans-Bold',
                              }}>
                              {/* Redeem Cashback */}
                              Redeem Credit
                            </Text>
                          </View>
                        </TouchableOpacity>
                      </View>
                    </View>
                  </View>
                </ModalView>
                <ModalView
                  visible={redeemCreditModal}
                  supportedOrientations={['landscape', 'portrait']}
                  style={{}}
                  animationType={'fade'}
                  transparent={true}>
                  <View
                    style={{
                      flex: 1,
                      backgroundColor: Colors.modalBgColor,
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}>
                    <View
                      style={{
                        width: switchMerchant
                          ? windowWidth * 0.77
                          : windowWidth * 0.77,
                        height: switchMerchant
                          ? windowHeight * 0.85
                          : windowHeight * 0.8,
                        backgroundColor: Colors.whiteColor,
                        borderRadius: 5,
                      }}>
                      <TouchableOpacity
                        style={{
                          position: 'absolute',
                          right: windowWidth * 0.02,
                          top: windowWidth * 0.02,

                          elevation: 1000,
                          zIndex: 1000,
                        }}
                        onPress={() => {
                          setRedeemCreditModal(false);
                          setAmount('');
                        }}>
                        <AntDesign
                          name="closecircle"
                          size={switchMerchant ? 15 : 25}
                          color={Colors.fieldtTxtColor}
                        />
                      </TouchableOpacity>
                      <View style={{ flexDirection: 'row', flex: 1 }}>
                        <View style={{ width: '50%', padding: 30 }}>
                          <View style={{ flexDirection: 'column', flex: 1 }}>
                            <Text
                              style={{
                                fontSize: switchMerchant ? 16 : 24,
                                fontFamily: 'NunitoSans-Bold',
                              }}>
                              Customer Details
                            </Text>
                            <Text
                              style={{
                                fontSize: switchMerchant ? 10 : 16,
                                paddingTop: switchMerchant ? 15 : 30,
                              }}>
                              First Name
                            </Text>
                            <Text
                              style={{
                                fontSize: switchMerchant ? 10 : 16,
                                fontFamily: 'NunitoSans-Bold',
                                paddingTop: 10,
                              }}>
                              {currCRMUser ? currCRMUser.name : ''}
                            </Text>

                            <Text
                              style={{
                                fontSize: switchMerchant ? 10 : 16,
                                paddingTop: switchMerchant ? 15 : 30,
                              }}>
                              Phone Number
                            </Text>
                            <Text
                              style={{
                                fontSize: switchMerchant ? 10 : 16,
                                fontFamily: 'NunitoSans-Bold',
                                paddingTop: 10,
                              }}>
                              {currCRMUser ? currCRMUser.number : ''}
                            </Text>

                            <View
                              style={{
                                flexDirection: 'row',
                                paddingTop: switchMerchant ? 15 : 30,
                              }}>
                              <Text
                                style={{
                                  fontSize: switchMerchant ? 10 : 16,
                                  width: '40%',
                                }}>
                                Available Credit
                              </Text>
                              <Text
                                style={{
                                  fontSize: switchMerchant ? 10 : 16,
                                  width: '50%',
                                }}>
                                Tier
                              </Text>
                            </View>
                            <View style={{ flexDirection: 'row', paddingTop: 10 }}>
                              <Text
                                style={{
                                  fontSize: switchMerchant ? 10 : 16,
                                  width: '40%',
                                  fontFamily: 'NunitoSans-Bold',
                                }}>
                                RM {selectedCustomerLCCBalance.toFixed(2)}
                              </Text>
                              <Text
                                style={{
                                  fontSize: switchMerchant ? 10 : 16,
                                  width: '50%',
                                  fontFamily: 'NunitoSans-Bold',
                                }}>
                                {selectedCustomerEdit &&
                                  selectedCustomerEdit.levelName
                                  ? selectedCustomerEdit.levelName
                                  : 'N/A'}
                              </Text>
                            </View>
                          </View>
                        </View>

                        <View style={{ width: '50%' }}>
                          <View
                            style={{
                              alignItems: 'center',
                              justifyContent: 'center',
                            }}>
                            <Text
                              style={{
                                fontSize: switchMerchant ? 15 : 20,
                                fontWeight: 'bold',
                                margin: switchMerchant ? 1 : 12,
                                paddingTop: 20,
                              }}>
                              Enter The Total Amount Spent
                            </Text>
                            <View
                              style={{
                                justifyContent: 'center',
                                flexDirection: 'row',
                                alignItems: 'center',
                                alignSelf: 'center',
                              }}>
                              <Text
                                style={{
                                  fontSize: switchMerchant ? 13 : 20,
                                  color: Colors.fieldtTxtColor,
                                }}>
                                RM
                              </Text>
                            </View>
                            <View style={{ justifyContent: 'center' }}>
                              <Text style={{ fontSize: switchMerchant ? 22 : 50 }}>
                                {amount.length === 0 ? '0' : amount}
                              </Text>
                            </View>
                          </View>
                          <View>
                            <View
                              style={{
                                flexDirection: 'row',
                                flexWrap: 'wrap',
                                justifyContent: 'space-between',
                                alignItems: 'center',
                                alignSelf: 'center',
                                width: '55%',
                                paddingTop: switchMerchant ? 5 : 30,
                              }}>
                              <View
                                style={{
                                  flexDirection: 'row',
                                  justifyContent: 'space-between',
                                  alignSelf: 'center',
                                  alignItems: 'center',
                                  width: '100%',
                                }}>
                                <TouchableOpacity
                                  onPress={() => {
                                    onNumPadBtn(1);
                                  }}>
                                  <View
                                    style={[
                                      styles.pinBtn,
                                      switchMerchant
                                        ? {
                                          width: switchMerchant ? 35 : 70,
                                          height: switchMerchant ? 35 : 70,
                                          marginBottom: 7,
                                        }
                                        : { width: 70, height: 70 },
                                    ]}>
                                    <Text
                                      style={[
                                        styles.pinNo,
                                        { fontSize: switchMerchant ? 10 : 20 },
                                      ]}>
                                      1
                                    </Text>
                                  </View>
                                </TouchableOpacity>

                                <TouchableOpacity
                                  onPress={() => {
                                    onNumPadBtn(2);
                                  }}>
                                  <View
                                    style={[
                                      styles.pinBtn,
                                      switchMerchant
                                        ? {
                                          width: switchMerchant ? 35 : 70,
                                          height: switchMerchant ? 35 : 70,
                                          marginBottom: 7,
                                        }
                                        : { width: 70, height: 70 },
                                    ]}>
                                    <Text
                                      style={[
                                        styles.pinNo,
                                        { fontSize: switchMerchant ? 10 : 20 },
                                      ]}>
                                      2
                                    </Text>
                                  </View>
                                </TouchableOpacity>

                                <TouchableOpacity
                                  onPress={() => {
                                    onNumPadBtn(3);
                                  }}>
                                  <View
                                    style={[
                                      styles.pinBtn,
                                      switchMerchant
                                        ? {
                                          width: switchMerchant ? 35 : 70,
                                          height: switchMerchant ? 35 : 70,
                                          marginBottom: 7,
                                        }
                                        : { width: 70, height: 70 },
                                    ]}>
                                    <Text
                                      style={[
                                        styles.pinNo,
                                        { fontSize: switchMerchant ? 10 : 20 },
                                      ]}>
                                      3
                                    </Text>
                                  </View>
                                </TouchableOpacity>
                              </View>
                              <View
                                style={{
                                  flexDirection: 'row',
                                  justifyContent: 'space-between',
                                  alignSelf: 'center',
                                  alignItems: 'center',
                                  width: '100%',
                                }}>
                                <TouchableOpacity
                                  onPress={() => {
                                    onNumPadBtn(4);
                                  }}>
                                  <View
                                    style={[
                                      styles.pinBtn,
                                      switchMerchant
                                        ? {
                                          width: switchMerchant ? 35 : 70,
                                          height: switchMerchant ? 35 : 70,
                                          marginBottom: 7,
                                        }
                                        : { width: 70, height: 70 },
                                    ]}>
                                    <Text
                                      style={[
                                        styles.pinNo,
                                        { fontSize: switchMerchant ? 10 : 20 },
                                      ]}>
                                      4
                                    </Text>
                                  </View>
                                </TouchableOpacity>

                                <TouchableOpacity
                                  onPress={() => {
                                    onNumPadBtn(5);
                                  }}>
                                  <View
                                    style={[
                                      styles.pinBtn,
                                      switchMerchant
                                        ? {
                                          width: switchMerchant ? 35 : 70,
                                          height: switchMerchant ? 35 : 70,
                                          marginBottom: 7,
                                        }
                                        : { width: 70, height: 70 },
                                    ]}>
                                    <Text
                                      style={[
                                        styles.pinNo,
                                        { fontSize: switchMerchant ? 10 : 20 },
                                      ]}>
                                      5
                                    </Text>
                                  </View>
                                </TouchableOpacity>

                                <TouchableOpacity
                                  onPress={() => {
                                    onNumPadBtn(6);
                                  }}>
                                  <View
                                    style={[
                                      styles.pinBtn,
                                      switchMerchant
                                        ? {
                                          width: switchMerchant ? 35 : 70,
                                          height: switchMerchant ? 35 : 70,
                                          marginBottom: 7,
                                        }
                                        : { width: 70, height: 70 },
                                    ]}>
                                    <Text
                                      style={[
                                        styles.pinNo,
                                        { fontSize: switchMerchant ? 10 : 20 },
                                      ]}>
                                      6
                                    </Text>
                                  </View>
                                </TouchableOpacity>
                              </View>
                              <View
                                style={{
                                  flexDirection: 'row',
                                  justifyContent: 'space-between',
                                  alignSelf: 'center',
                                  alignItems: 'center',
                                  width: '100%',
                                }}>
                                <TouchableOpacity
                                  onPress={() => {
                                    onNumPadBtn(7);
                                  }}>
                                  <View
                                    style={[
                                      styles.pinBtn,
                                      switchMerchant
                                        ? {
                                          width: switchMerchant ? 35 : 70,
                                          height: switchMerchant ? 35 : 70,
                                          marginBottom: 7,
                                        }
                                        : { width: 70, height: 70 },
                                    ]}>
                                    <Text
                                      style={[
                                        styles.pinNo,
                                        { fontSize: switchMerchant ? 10 : 20 },
                                      ]}>
                                      7
                                    </Text>
                                  </View>
                                </TouchableOpacity>

                                <TouchableOpacity
                                  onPress={() => {
                                    onNumPadBtn(8);
                                  }}>
                                  <View
                                    style={[
                                      styles.pinBtn,
                                      switchMerchant
                                        ? {
                                          width: switchMerchant ? 35 : 70,
                                          height: switchMerchant ? 35 : 70,
                                          marginBottom: 7,
                                        }
                                        : { width: 70, height: 70 },
                                    ]}>
                                    <Text
                                      style={[
                                        styles.pinNo,
                                        { fontSize: switchMerchant ? 10 : 20 },
                                      ]}>
                                      8
                                    </Text>
                                  </View>
                                </TouchableOpacity>

                                <TouchableOpacity
                                  onPress={() => {
                                    onNumPadBtn(9);
                                  }}>
                                  <View
                                    style={[
                                      styles.pinBtn,
                                      switchMerchant
                                        ? {
                                          width: switchMerchant ? 35 : 70,
                                          height: switchMerchant ? 35 : 70,
                                          marginBottom: 7,
                                        }
                                        : { width: 70, height: 70 },
                                    ]}>
                                    <Text
                                      style={[
                                        styles.pinNo,
                                        { fontSize: switchMerchant ? 10 : 20 },
                                      ]}>
                                      9
                                    </Text>
                                  </View>
                                </TouchableOpacity>
                              </View>
                              <View
                                style={{
                                  flexDirection: 'row',
                                  justifyContent: 'space-between',
                                  alignSelf: 'center',
                                  alignItems: 'center',
                                  width: '100%',
                                }}>
                                <TouchableOpacity
                                  onPress={() => {
                                    onNumPadBtn('+');
                                  }}>
                                  <View
                                    style={[
                                      styles.pinBtn,
                                      switchMerchant
                                        ? {
                                          width: switchMerchant ? 35 : 70,
                                          height: switchMerchant ? 35 : 70,
                                          marginBottom: 7,
                                        }
                                        : { width: 70, height: 70 },
                                    ]}>
                                    <Text
                                      style={[
                                        styles.pinNo,
                                        { fontSize: switchMerchant ? 10 : 20 },
                                      ]}>
                                      +
                                    </Text>
                                  </View>
                                </TouchableOpacity>

                                <TouchableOpacity
                                  onPress={() => {
                                    onNumPadBtn(0);
                                  }}>
                                  <View
                                    style={[
                                      styles.pinBtn,
                                      switchMerchant
                                        ? {
                                          width: switchMerchant ? 35 : 70,
                                          height: switchMerchant ? 35 : 70,
                                          marginBottom: 7,
                                        }
                                        : { width: 70, height: 70 },
                                    ]}>
                                    <Text
                                      style={[
                                        styles.pinNo,
                                        { fontSize: switchMerchant ? 10 : 20 },
                                      ]}>
                                      0
                                    </Text>
                                  </View>
                                </TouchableOpacity>

                                <TouchableOpacity
                                  onPress={() => {
                                    onNumPadBtn(-1);
                                  }}>
                                  <View
                                    style={[
                                      styles.pinBtn,
                                      switchMerchant
                                        ? {
                                          width: switchMerchant ? 35 : 70,
                                          height: switchMerchant ? 35 : 70,
                                          marginBottom: 7,
                                        }
                                        : { width: 70, height: 70 },
                                    ]}>
                                    <Feather
                                      name="chevron-left"
                                      size={switchMerchant ? 13 : 30}
                                      color={'black'}
                                      style={{}}
                                    />
                                  </View>
                                </TouchableOpacity>
                              </View>
                            </View>
                            <View
                              style={{
                                width: '100%',
                                alignItems: 'center',
                                alignSelf: 'center',
                              }}>
                              <TouchableOpacity
                                onPress={() => {
                                  if (amount && parseFloat(amount) > 0) {
                                    reedemcredit();
                                    // setRedeemdone(true);
                                  } else {
                                    Alert.alert(
                                      'Info',
                                      'Amount must be more than 0',
                                    );
                                  }
                                }}
                                disabled={phoneNumberChecking}
                                style={{
                                  width: switchMerchant ? '50%' : '45%',
                                }}>
                                <View
                                  style={{
                                    borderRadius: 10,
                                    backgroundColor: phoneNumberChecking
                                      ? 'grey'
                                      : Colors.primaryColor,
                                    paddingVertical: switchMerchant ? 5 : 10,
                                    paddingHorizontal: 25,
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                  }}>
                                  <Text
                                    style={{
                                      color: Colors.whiteColor,
                                      fontSize: switchMerchant ? 10 : 16,
                                      fontFamily: 'NunitoSans-Bold',
                                    }}>
                                    Redeem
                                  </Text>
                                </View>
                              </TouchableOpacity>
                            </View>
                          </View>
                        </View>
                      </View>
                    </View>
                  </View>
                </ModalView>
                <ModalView
                  visible={redeemdone}
                  supportedOrientations={['landscape', 'portrait']}
                  style={{}}
                  animationType={'fade'}
                  transparent={true}>
                  <View
                    style={{
                      flex: 1,
                      backgroundColor: Colors.modalBgColor,
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}>
                    <View
                      style={{
                        width: switchMerchant
                          ? windowWidth * 0.7
                          : windowWidth * 0.77,
                        height: switchMerchant
                          ? windowHeight * 0.85
                          : windowHeight * 0.7,
                        backgroundColor: Colors.whiteColor,
                        borderRadius: 5,
                      }}>
                      <ScrollView
                        scrollEnabled={true}
                        showsVerticalScrollIndicator={false}>
                        <TouchableOpacity
                          style={{
                            position: 'absolute',
                            right: windowWidth * 0.02,
                            top: windowWidth * 0.02,

                            elevation: 1000,
                            zIndex: 1000,
                          }}
                          onPress={() => {
                            setRedeemdone(false);
                            setAmount('');
                            setAddnotes(false);
                            setNotetext('');
                          }}>
                          <AntDesign
                            name="closecircle"
                            size={switchMerchant ? 15 : 25}
                            color={Colors.fieldtTxtColor}
                          />
                        </TouchableOpacity>
                        <View
                          style={{
                            justifyContent: 'center',
                            alignSelf: 'center',
                            alignItems: 'center',
                            paddingVertical: switchMerchant ? 15 : 30,
                          }}>
                          <Text
                            style={{
                              fontSize: switchMerchant ? 16 : 24,
                              fontFamily: 'NunitoSans-Bold',
                            }}>
                            Redeem successfully
                          </Text>
                          <Text
                            style={{
                              fontSize: switchMerchant ? 10 : 16,
                            }}>
                            {currCRMUser ? currCRMUser.name : userName} will receive
                            credits in a moment
                          </Text>
                        </View>
                        <View
                          style={{
                            flexDirection: 'row',
                            alignSelf: 'center',
                            width: switchMerchant ? '80%' : '70%',
                            justifyContent: 'center',
                            paddingTop: switchMerchant ? 10 : 30,
                          }}>
                          <Text
                            style={{
                              fontSize: switchMerchant ? 10 : 16,
                              width: '20%',
                            }}>
                            Name
                          </Text>
                          <Text
                            style={{
                              fontSize: switchMerchant ? 10 : 16,
                              width: '40%',
                            }}>
                            Phone Number
                          </Text>
                          <Text
                            style={{
                              fontSize: switchMerchant ? 10 : 16,
                              width: '20%',
                            }}>
                            Total Spend
                          </Text>
                          <Text
                            style={{
                              fontSize: switchMerchant ? 10 : 16,
                              width: '20%',
                            }}>
                            Cashback
                            {/* Redeemed Credit */}
                          </Text>
                        </View>
                        <View
                          style={{
                            flexDirection: 'row',
                            alignSelf: 'center',
                            width: switchMerchant ? '80%' : '70%',
                            justifyContent: 'center',
                            paddingTop: switchMerchant ? 5 : 10,
                          }}>
                          <Text
                            style={{
                              fontSize: switchMerchant ? 10 : 16,
                              fontFamily: 'NunitoSans-Bold',
                              width: '20%',
                            }}>
                            {currCRMUser ? currCRMUser.name : userName}
                          </Text>
                          <Text
                            style={{
                              fontSize: switchMerchant ? 10 : 16,
                              fontFamily: 'NunitoSans-Bold',
                              width: '40%',
                            }}>
                            {currCRMUser ? currCRMUser.number : phoneNumber}
                          </Text>
                          <Text
                            style={{
                              fontSize: switchMerchant ? 10 : 16,
                              fontFamily: 'NunitoSans-Bold',
                              width: '20%',
                            }}>
                            RM {amount}
                          </Text>
                          <Text
                            style={{
                              fontSize: switchMerchant ? 10 : 16,
                              fontFamily: 'NunitoSans-Bold',
                              width: '20%',
                            }}>
                            RM{' '}
                            {(selectedCustomerLCCTransactions[0]
                              ? selectedCustomerLCCTransactions[0].amount
                              : 0
                            ).toFixed(2)}
                          </Text>
                        </View>
                        <View
                          style={{
                            justifyContent: 'flex-start',
                            alignSelf: 'center',
                            paddingTop: switchMerchant ? 10 : 40,
                          }}>
                          <TouchableOpacity
                            onPress={() => {
                              {
                                addnotes == true
                                  ? setAddnotes(false)
                                  : setAddnotes(true);
                              }
                            }}>
                            <Text
                              style={{
                                color: Colors.primaryColor,
                                fontSize: switchMerchant ? 10 : 16,
                                borderBottomWidth: 1,
                                borderBottomColor: Colors.primaryColor,
                              }}>
                              Add notes
                            </Text>
                          </TouchableOpacity>
                        </View>
                        {addnotes == true ? (
                          <TextInput
                            style={{
                              fontSize: switchMerchant ? 10 : 16,
                              color: 'black',
                              fontFamily: 'NunitoSans-Regular',
                              borderBottomWidth: 0.5,
                              borderBottomColor: 'black',
                              width: '40%',
                              height: switchMerchant ? 35 : 40,
                              alignSelf: 'center',
                              alignItems: 'center',
                              marginVertical: switchMerchant ? 5 : 20,
                            }}
                            placeholder=""
                            placeholderTextColor={Platform.select({
                              ios: '#a9a9a9',
                            })}
                            onChangeText={(text) => {
                              setNotetext(text);
                            }}></TextInput>
                        ) : (
                          <View
                            style={{
                              height: switchMerchant ? 35 : 40,
                              marginVertical: switchMerchant ? 5 : 20,
                            }}></View>
                        )}
                        <View
                          style={{
                            justifyContent: 'flex-start',
                            alignSelf: 'center',
                            paddingTop: switchMerchant ? 10 : 20,
                          }}>
                          <TouchableOpacity
                            onPress={() => {
                              setRedeemdone(false);
                              setAddnotes(false);
                              setNotetext('');
                              setAmount('');
                              setPhoneNumber('+60');
                              setCreditSection(CREDIT_SECTION.HOME);
                            }}>
                            <View
                              style={{
                                borderRadius: 10,
                                backgroundColor: Colors.primaryColor,
                                paddingVertical: switchMerchant ? 5 : 10,
                                paddingHorizontal: 50,
                                marginTop: 20,
                              }}>
                              <Text
                                style={{
                                  color: Colors.whiteColor,
                                  fontSize: switchMerchant ? 10 : 16,
                                  fontFamily: 'NunitoSans-Bold',
                                }}>
                                Done
                              </Text>
                            </View>
                          </TouchableOpacity>
                        </View>
                        <View
                          style={{
                            flexDirection: 'row',
                            alignSelf: 'center',
                            paddingTop: 30,
                            alignItems: 'center',
                          }}>
                          <TouchableOpacity
                            onPress={() => {
                              setRedeemdone(false);
                              setAddnotes(false);
                              setNotetext('');
                              setAmount('');
                              setPhoneNumber(`${phoneNumber}`);
                              setCreditSection(CREDIT_SECTION.ADD_CASHBACK);
                            }}>
                            <Text
                              style={{
                                fontSize: switchMerchant ? 10 : 16,
                                color: Colors.primaryColor,
                              }}>
                              Also Add Cashback?
                            </Text>
                          </TouchableOpacity>
                          <Text
                            style={{
                              marginHorizontal: 20,
                              fontSize: switchMerchant ? 10 : 14,
                            }}>
                            or
                          </Text>
                          <TouchableOpacity
                            onPress={() => {
                              setRedeemdone(false);
                              setAddnotes(false);
                              setNotetext('');
                              setAmount('');
                              setPhoneNumber(`${phoneNumber}`);
                              setCreditSection(CREDIT_SECTION.REDEEM_CREDIT);
                            }}>
                            <Text
                              style={{
                                fontSize: switchMerchant ? 10 : 16,
                                color: Colors.primaryColor,
                              }}>
                              Want to redeem a campaign?
                            </Text>
                          </TouchableOpacity>
                        </View>
                        <View
                          style={{
                            flexDirection: 'row',
                            alignSelf: 'center',
                            paddingTop: 20,
                          }}>
                          <TouchableOpacity
                            onPress={() => {
                              setRedeemdone(false);
                              setAddnotes(false);
                              setNotetext('');
                              setAmount('');
                              setPhoneNumber(`${phoneNumber}`);
                              setCreditSection(CREDIT_SECTION.DEDUCT_CREDIT);
                            }}>
                            <Text
                              style={{
                                fontSize: switchMerchant ? 10 : 16,
                                color: Colors.tabRed,
                              }}>
                              Want to deduct credit?
                            </Text>
                          </TouchableOpacity>
                          <Text
                            style={{
                              marginHorizontal: 20,
                              fontSize: switchMerchant ? 10 : 14,
                            }}>
                            or
                          </Text>
                          <TouchableOpacity
                            onPress={() => {
                              setRedeemdone(false);
                              setAddnotes(false);
                              setNotetext('');
                              setAmount('');
                              setPhoneNumber(`${phoneNumber}`);
                              setCreditSection(CREDIT_SECTION.ADD_CREDIT);
                            }}>
                            <Text
                              style={{
                                fontSize: switchMerchant ? 10 : 16,
                                color: Colors.primaryColor,
                                marginBottom: switchMerchant ? 30 : 0,
                              }}>
                              Want to add extra credit?
                            </Text>
                          </TouchableOpacity>
                        </View>
                      </ScrollView>
                    </View>
                  </View>
                </ModalView>
              </View>
            ) : (
              <></>
            )}

            {/* ///////////// Add Credit /////////////// */}

            {creditSection === CREDIT_SECTION.ADD_CREDIT ? (
              <View
                style={{
                  flex: 1,
                  width: '100%',
                  height: '100%',
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  backgroundColor: Colors.highlightColor,
                }}>
                <View
                  style={{
                    width:
                      Platform.OS === 'ios'
                        ? windowWidth * 0.44
                        : windowWidth * 0.43,
                    backgroundColor: Colors.whiteColor,
                    borderRadius: 5,
                  }}>
                  {/* <View
                  style={{
                    flexDirection: 'row',
                  }}>
                  <TouchableOpacity
                    style={{
                      height: windowHeight * 0.055,
                      flexDirection: 'row',
                      alignContent: 'center',
                      alignItems: 'center',
                      marginTop: 10,
                      marginLeft: 10,
                    }}
                    onPress={() => {
                      setCreditSection(CREDIT_SECTION.HOME);
                      setPhoneNumber('+60');
                    }}>
                    <Feather
                      name="chevron-left"
                      size={switchMerchant ? 20 : 30}
                      color={Colors.primaryColor}
                    />
                    <Text
                      style={{
                        fontFamily: 'NunitoSans-Bold',
                        fontSize: switchMerchant ? 14 : 17,
                        color: Colors.primaryColor,
                        marginBottom: Platform.OS === 'ios' ? 0 : 1,
                      }}>
                      Back
                    </Text>
                  </TouchableOpacity>
                </View> */}
                  <View
                    style={{ paddingLeft: 20, paddingTop: switchMerchant ? 5 : 10 }}>
                    <Text style={{ fontSize: switchMerchant ? 16 : 24 }}>
                      {currOutlet.name}
                    </Text>
                    <Text
                      style={{
                        fontSize: switchMerchant ? 16 : 24,
                        fontWeight: 'bold',
                        paddingVertical: switchMerchant ? 2 : 10,
                      }}>
                      Add Extra Credit Here
                    </Text>
                    <Text style={{ fontSize: switchMerchant ? 10 : 16 }}>
                      If You Nedd To Add Bonus Credit To An Account
                    </Text>
                  </View>
                  <View
                    style={{
                      paddingLeft: 20,
                      paddingTop: switchMerchant ? 10 : 20,
                      flex: 1,
                    }}>
                    <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                      <Text
                        style={{
                          fontSize: switchMerchant ? 12 : 22,
                          fontWeight: 'bold',
                          backgroundColor: Colors.highlightColor,
                          color: Colors.primaryColor,
                          borderRadius: 25,
                          alignSelf: 'center',
                          alignItems: 'center',
                          justifyContent: 'center',
                          paddingHorizontal: switchMerchant ? 10 : 20,
                          paddingVertical: switchMerchant ? 5 : 10,
                          marginRight: 10,
                        }}>
                        a
                      </Text>
                      <Text style={{ fontSize: switchMerchant ? 10 : 16 }}>
                        Enter Guest Phone Number
                      </Text>
                    </View>
                    <View
                      style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                        paddingVertical: switchMerchant ? 10 : 20,
                      }}>
                      <Text
                        style={{
                          fontSize: switchMerchant ? 12 : 22,
                          fontWeight: 'bold',
                          backgroundColor: Colors.highlightColor,
                          color: Colors.primaryColor,
                          borderRadius: 25,
                          alignSelf: 'center',
                          alignItems: 'center',
                          justifyContent: 'center',
                          paddingHorizontal: switchMerchant ? 10 : 20,
                          paddingVertical: switchMerchant ? 5 : 10,
                          marginRight: 10,
                        }}>
                        b
                      </Text>
                      <Text
                        style={{
                          fontSize: switchMerchant ? 10 : 16,
                          paddingVertical: 10,
                        }}>
                        {
                          'Fill In The Amount To Add\nThis Is Not Same As Claiming Cashback'
                        }
                      </Text>
                    </View>
                    <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                      <Text
                        style={{
                          fontSize: switchMerchant ? 12 : 22,
                          fontWeight: 'bold',
                          backgroundColor: Colors.highlightColor,
                          color: Colors.primaryColor,
                          borderRadius: 25,
                          alignSelf: 'center',
                          alignItems: 'center',
                          justifyContent: 'center',
                          paddingHorizontal: switchMerchant ? 10 : 20,
                          paddingVertical: switchMerchant ? 5 : 10,
                          marginRight: 10,
                        }}>
                        c
                      </Text>
                      <Text style={{ fontSize: switchMerchant ? 10 : 16 }}>
                        Enter Management Password For Security Clearance
                      </Text>
                    </View>
                  </View>
                  <View
                    style={{
                      flex: 1,
                      paddingHorizontal: 20,
                      paddingTop: 20,
                      justifyContent: 'center',
                    }}>
                    {/* <View
                    style={{
                      flexDirection: 'row',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                    }}>
                    <Text style={{ fontSize: switchMerchant ? 10 : 14 }}>
                      Powered By{' '}
                      <Text
                        style={{
                          color: Colors.primaryColor,
                          fontSize: switchMerchant ? 12 : 16,
                        }}>
                        KooDoo
                      </Text>
                    </Text>
                    <TouchableOpacity style={{ flexDirection: 'row' }}>
                      <Feather
                        name="help-circle"
                        size={switchMerchant ? 15 : 20}
                        style={{ color: Colors.primaryColor, paddingRight: 5 }}
                      />
                      <Text
                        style={{
                          color: Colors.primaryColor,
                          fontSize: switchMerchant ? 10 : 14,
                        }}>
                        Help
                      </Text>
                    </TouchableOpacity>
                  </View> */}
                  </View>
                  <View
                    style={[{
                      paddingTop: 15,
                      flexDirection: 'row',
                      alignItems: 'center',
                      justifyContent: 'space-evenly',
                      paddingBottom: 30,
                    }]}>

                    <TouchableOpacity
                      style={[
                        {
                          width: '25%',
                          height: 140,
                          backgroundColor: Colors.tabMint,
                          borderRadius: 10,
                          flexDirection: 'row',
                          alignItems: 'center',
                          justifyContent: 'center',
                          paddingHorizontal: 20,
                          paddingVertical: 15,
                          shadowColor: '#000',
                          shadowOffset: {
                            width: 0,
                            height: 2,
                          },
                          shadowOpacity: 0.22,
                          shadowRadius: 3.22,
                          elevation: 3,
                        },
                      ]}
                      onPress={() => {
                        setCreditSection(CREDIT_SECTION.ADD_CASHBACK);
                      }}>

                      <View style={{ alignItems: 'center', }}>
                        <MaterialCommunityIcons
                          name="cash-plus"
                          size={switchMerchant ? 30 : 45}
                          style={{ color: Colors.whiteColor, }}
                        />
                        <Text
                          style={[
                            {
                              fontWeight: 'bold',
                              color: Colors.whiteColor,
                              fontSize: 18,
                              textAlign: 'center',
                              paddingBottom: 5,
                              paddingVertical: 10,
                            },
                          ]}>
                          Earn Point
                        </Text>
                      </View>
                    </TouchableOpacity>
                    <TouchableOpacity
                      style={[
                        {
                          width: '25%',
                          height: 140,
                          backgroundColor: Colors.tabRed,
                          borderRadius: 10,
                          flexDirection: 'row',
                          alignItems: 'center',
                          justifyContent: 'center',
                          paddingHorizontal: 20,
                          paddingVertical: 15,
                          shadowColor: '#000',
                          shadowOffset: {
                            width: 0,
                            height: 2,
                          },
                          shadowOpacity: 0.22,
                          shadowRadius: 3.22,
                          elevation: 3,
                        },
                      ]}
                      onPress={() => {
                        setCreditSection(CREDIT_SECTION.DEDUCT_CREDIT);
                      }}>

                      <View style={{ alignItems: 'center', }}>
                        <MaterialCommunityIcons
                          name="cash-minus"
                          size={switchMerchant ? 30 : 45}
                          style={{ color: Colors.whiteColor, }}
                        />
                        <Text
                          style={[
                            {
                              fontWeight: 'bold',
                              color: Colors.whiteColor,
                              fontSize: 18,
                              textAlign: 'center',
                              paddingBottom: 5,
                              paddingVertical: 10,
                            },
                          ]}>
                          Deduct Point
                        </Text>
                      </View>
                    </TouchableOpacity>

                    <TouchableOpacity
                      style={[
                        {
                          width: '25%',
                          height: 140,
                          backgroundColor: Colors.tabCyan,
                          borderRadius: 10,
                          flexDirection: 'row',
                          alignItems: 'center',
                          justifyContent: 'center',
                          paddingHorizontal: 20,
                          paddingVertical: 15,
                          shadowColor: '#000',
                          shadowOffset: {
                            width: 0,
                            height: 2,
                          },
                          shadowOpacity: 0.22,
                          shadowRadius: 3.22,
                          elevation: 3,
                        },
                      ]}
                      onPress={() => {
                        setCreditSection(CREDIT_SECTION.REDEEM_CREDIT);
                      }}>
                      <View style={{ alignItems: 'center', }}>
                        <MaterialIcons
                          name="redeem"
                          size={switchMerchant ? 30 : 45}
                          style={{ color: Colors.whiteColor, }}
                        />
                        <Text
                          style={[
                            {
                              fontWeight: 'bold',
                              color: Colors.whiteColor,
                              fontSize: 18,
                              textAlign: 'center',
                              paddingBottom: 5,
                              paddingVertical: 10,
                            },
                          ]}>
                          Redeem Rewards
                        </Text>
                      </View>
                    </TouchableOpacity>

                  </View>
                </View>
                <View
                  style={{
                    width:
                      Platform.OS === 'ios'
                        ? windowWidth * 0.42
                        : windowWidth * 0.43,
                    backgroundColor: Colors.whiteColor,
                    borderRadius: 5,
                  }}>
                  <View
                    style={{
                      alignItems: 'center',
                      justifyContent: 'center',
                      flex: 1,
                      paddingTop: Platform.OS === 'ios' ? 0 : 10,
                      marginTop: Platform.OS === 'ios' ? 10 : 0,
                    }}>
                    <Text
                      style={{
                        fontSize: switchMerchant ? 15 : 25,
                        fontWeight: 'bold',
                      }}>
                      Enter Your Phone Number
                    </Text>
                  </View>
                  <View
                    style={{
                      justifyContent: 'center',
                      alignItems: 'center',
                      flex: 1,
                    }}>
                    <Text style={{ fontSize: switchMerchant ? 22 : 45 }}>
                      {phoneNumber}
                    </Text>
                  </View>
                  <View>
                    <View
                      style={{
                        flexDirection: 'row',
                        flexWrap: 'wrap',
                        justifyContent: 'space-between',
                        alignItems: 'center',
                        alignSelf: 'center',
                        width: Platform.OS === 'ios' ? 270 : '45%',
                        paddingTop: switchMerchant ? 10 : 30,
                      }}>
                      <View
                        style={{
                          flexDirection: 'row',
                          justifyContent: 'space-between',
                          alignSelf: 'center',
                          alignItems: 'center',
                          width: '100%',
                        }}>
                        <TouchableOpacity
                          onPress={() => {
                            PhoneonNumPadBtn(1);
                          }}>
                          <View
                            style={[
                              styles.pinBtn,
                              switchMerchant
                                ? {
                                  width: switchMerchant ? 35 : 70,
                                  height: switchMerchant ? 35 : 70,
                                  marginBottom: 7,
                                }
                                : { width: 70, height: 70 },
                            ]}>
                            <Text
                              style={[
                                styles.pinNo,
                                { fontSize: switchMerchant ? 10 : 20 },
                              ]}>
                              1
                            </Text>
                          </View>
                        </TouchableOpacity>

                        <TouchableOpacity
                          onPress={() => {
                            PhoneonNumPadBtn(2);
                          }}>
                          <View
                            style={[
                              styles.pinBtn,
                              switchMerchant
                                ? {
                                  width: switchMerchant ? 35 : 70,
                                  height: switchMerchant ? 35 : 70,
                                  marginBottom: 7,
                                }
                                : { width: 70, height: 70 },
                            ]}>
                            <Text
                              style={[
                                styles.pinNo,
                                { fontSize: switchMerchant ? 10 : 20 },
                              ]}>
                              2
                            </Text>
                          </View>
                        </TouchableOpacity>

                        <TouchableOpacity
                          onPress={() => {
                            PhoneonNumPadBtn(3);
                          }}>
                          <View
                            style={[
                              styles.pinBtn,
                              switchMerchant
                                ? {
                                  width: switchMerchant ? 35 : 70,
                                  height: switchMerchant ? 35 : 70,
                                  marginBottom: 7,
                                }
                                : { width: 70, height: 70 },
                            ]}>
                            <Text
                              style={[
                                styles.pinNo,
                                { fontSize: switchMerchant ? 10 : 20 },
                              ]}>
                              3
                            </Text>
                          </View>
                        </TouchableOpacity>
                      </View>
                      <View
                        style={{
                          flexDirection: 'row',
                          justifyContent: 'space-between',
                          alignSelf: 'center',
                          alignItems: 'center',
                          width: '100%',
                        }}>
                        <TouchableOpacity
                          onPress={() => {
                            PhoneonNumPadBtn(4);
                          }}>
                          <View
                            style={[
                              styles.pinBtn,
                              switchMerchant
                                ? {
                                  width: switchMerchant ? 35 : 70,
                                  height: switchMerchant ? 35 : 70,
                                  marginBottom: 7,
                                }
                                : { width: 70, height: 70 },
                            ]}>
                            <Text
                              style={[
                                styles.pinNo,
                                { fontSize: switchMerchant ? 10 : 20 },
                              ]}>
                              4
                            </Text>
                          </View>
                        </TouchableOpacity>

                        <TouchableOpacity
                          onPress={() => {
                            PhoneonNumPadBtn(5);
                          }}>
                          <View
                            style={[
                              styles.pinBtn,
                              switchMerchant
                                ? {
                                  width: switchMerchant ? 35 : 70,
                                  height: switchMerchant ? 35 : 70,
                                  marginBottom: 7,
                                }
                                : { width: 70, height: 70 },
                            ]}>
                            <Text
                              style={[
                                styles.pinNo,
                                { fontSize: switchMerchant ? 10 : 20 },
                              ]}>
                              5
                            </Text>
                          </View>
                        </TouchableOpacity>

                        <TouchableOpacity
                          onPress={() => {
                            PhoneonNumPadBtn(6);
                          }}>
                          <View
                            style={[
                              styles.pinBtn,
                              switchMerchant
                                ? {
                                  width: switchMerchant ? 35 : 70,
                                  height: switchMerchant ? 35 : 70,
                                  marginBottom: 7,
                                }
                                : { width: 70, height: 70 },
                            ]}>
                            <Text
                              style={[
                                styles.pinNo,
                                { fontSize: switchMerchant ? 10 : 20 },
                              ]}>
                              6
                            </Text>
                          </View>
                        </TouchableOpacity>
                      </View>

                      <View
                        style={{
                          flexDirection: 'row',
                          justifyContent: 'space-between',
                          alignSelf: 'center',
                          alignItems: 'center',
                          width: '100%',
                        }}>
                        <TouchableOpacity
                          onPress={() => {
                            PhoneonNumPadBtn(7);
                          }}>
                          <View
                            style={[
                              styles.pinBtn,
                              switchMerchant
                                ? {
                                  width: switchMerchant ? 35 : 70,
                                  height: switchMerchant ? 35 : 70,
                                  marginBottom: 7,
                                }
                                : { width: 70, height: 70 },
                            ]}>
                            <Text
                              style={[
                                styles.pinNo,
                                { fontSize: switchMerchant ? 10 : 20 },
                              ]}>
                              7
                            </Text>
                          </View>
                        </TouchableOpacity>

                        <TouchableOpacity
                          onPress={() => {
                            PhoneonNumPadBtn(8);
                          }}>
                          <View
                            style={[
                              styles.pinBtn,
                              switchMerchant
                                ? {
                                  width: switchMerchant ? 35 : 70,
                                  height: switchMerchant ? 35 : 70,
                                  marginBottom: 7,
                                }
                                : { width: 70, height: 70 },
                            ]}>
                            <Text
                              style={[
                                styles.pinNo,
                                { fontSize: switchMerchant ? 10 : 20 },
                              ]}>
                              8
                            </Text>
                          </View>
                        </TouchableOpacity>

                        <TouchableOpacity
                          onPress={() => {
                            PhoneonNumPadBtn(9);
                          }}>
                          <View
                            style={[
                              styles.pinBtn,
                              switchMerchant
                                ? {
                                  width: switchMerchant ? 35 : 70,
                                  height: switchMerchant ? 35 : 70,
                                  marginBottom: 7,
                                }
                                : { width: 70, height: 70 },
                            ]}>
                            <Text
                              style={[
                                styles.pinNo,
                                { fontSize: switchMerchant ? 10 : 20 },
                              ]}>
                              9
                            </Text>
                          </View>
                        </TouchableOpacity>
                      </View>
                      <View
                        style={{
                          flexDirection: 'row',
                          justifyContent: 'space-between',
                          alignSelf: 'center',
                          alignItems: 'center',
                          width: '100%',
                        }}>
                        <TouchableOpacity
                          onPress={() => {
                            if (phoneNumber.includes('+')) {
                              return;
                            }
                            else {
                              PhoneonNumPadBtn('+');
                            }
                          }}>
                          <View
                            style={[
                              styles.pinBtn,
                              switchMerchant
                                ? {
                                  width: switchMerchant ? 35 : 70,
                                  height: switchMerchant ? 35 : 70,
                                  marginBottom: 7,
                                }
                                : { width: 70, height: 70 },
                            ]}>
                            <Text
                              style={[
                                styles.pinNo,
                                { fontSize: switchMerchant ? 10 : 20 },
                              ]}>
                              +
                            </Text>
                          </View>
                        </TouchableOpacity>

                        <TouchableOpacity
                          onPress={() => {
                            PhoneonNumPadBtn(0);
                          }}>
                          <View
                            style={[
                              styles.pinBtn,
                              switchMerchant
                                ? {
                                  width: switchMerchant ? 35 : 70,
                                  height: switchMerchant ? 35 : 70,
                                  marginBottom: 7,
                                }
                                : { width: 70, height: 70 },
                            ]}>
                            <Text
                              style={[
                                styles.pinNo,
                                { fontSize: switchMerchant ? 10 : 20 },
                              ]}>
                              0
                            </Text>
                          </View>
                        </TouchableOpacity>

                        <TouchableOpacity
                          onPress={() => {
                            if (phoneNumber != '+6') {
                              PhoneonNumPadBtn(-1);
                            }
                          }}>
                          <View
                            style={[
                              styles.pinBtn,
                              switchMerchant
                                ? {
                                  width: switchMerchant ? 35 : 70,
                                  height: switchMerchant ? 35 : 70,
                                  marginBottom: 7,
                                }
                                : { width: 70, height: 70 },
                            ]}>
                            <Feather
                              name="chevron-left"
                              size={switchMerchant ? 13 : 30}
                              color={'black'}
                              style={{}}
                            />
                          </View>
                        </TouchableOpacity>
                      </View>
                    </View>
                  </View>
                  <View
                    style={{
                      flex: 1,
                      flexDirection: 'row',
                      alignItems: 'center',
                      alignSelf: 'center',
                    }}>
                    <TouchableOpacity
                      onPress={() => {
                        if (currCRMUser) {
                          setAddCreditModal(true);
                        } else {
                          Alert.alert('Info', 'No existing user found');
                        }
                      }}
                      disabled={phoneNumberChecking}>
                      <View
                        style={{
                          borderRadius: 10,
                          backgroundColor: phoneNumberChecking
                            ? 'grey'
                            : Colors.primaryColor,
                          paddingVertical: switchMerchant ? 4 : 10,
                          paddingHorizontal: switchMerchant ? 15 : 25,
                          marginBottom: switchMerchant ? 5 : 15,
                        }}>
                        <Text
                          style={{
                            color: Colors.whiteColor,
                            fontSize: switchMerchant ? 10 : 16,
                            fontFamily: 'NunitoSans-Bold',
                          }}>
                          Add Extra Credit
                        </Text>
                      </View>
                    </TouchableOpacity>
                  </View>
                </View>
                <ModalView
                  visible={addCreditModal}
                  supportedOrientations={['landscape', 'portrait']}
                  style={{}}
                  animationType={'fade'}
                  transparent={true}>
                  <View
                    style={{
                      flex: 1,
                      backgroundColor: Colors.modalBgColor,
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}>
                    <View
                      style={{
                        width: switchMerchant
                          ? windowWidth * 0.77
                          : windowWidth * 0.77,
                        height: switchMerchant
                          ? windowHeight * 0.85
                          : windowHeight * 0.85,
                        backgroundColor: Colors.whiteColor,
                        borderRadius: 5,
                      }}>
                      <TouchableOpacity
                        style={{
                          position: 'absolute',
                          right: windowWidth * 0.02,
                          top: windowWidth * 0.02,

                          elevation: 1000,
                          zIndex: 1000,
                        }}
                        onPress={() => {
                          setAddCreditModal(false);
                          setAmount('');
                        }}>
                        <AntDesign
                          name="closecircle"
                          size={switchMerchant ? 15 : 25}
                          color={Colors.fieldtTxtColor}
                        />
                      </TouchableOpacity>
                      <View style={{ flexDirection: 'row', flex: 1 }}>
                        <View style={{ width: '50%', padding: 30 }}>
                          <View style={{ flexDirection: 'column', flex: 1 }}>
                            <Text
                              style={{
                                fontSize: switchMerchant ? 16 : 24,
                                fontFamily: 'NunitoSans-Bold',
                              }}>
                              Customer Details
                            </Text>
                            <Text
                              style={{
                                fontSize: switchMerchant ? 10 : 16,
                                paddingTop: switchMerchant ? 15 : 30,
                              }}>
                              First Name
                            </Text>
                            <Text
                              style={{
                                fontSize: switchMerchant ? 10 : 16,
                                fontFamily: 'NunitoSans-Bold',
                                paddingTop: 10,
                              }}>
                              {currCRMUser ? currCRMUser.name : ''}
                            </Text>

                            <Text
                              style={{
                                fontSize: switchMerchant ? 10 : 16,
                                paddingTop: switchMerchant ? 15 : 30,
                              }}>
                              Phone Number
                            </Text>
                            <Text
                              style={{
                                fontSize: switchMerchant ? 10 : 16,
                                fontFamily: 'NunitoSans-Bold',
                                paddingTop: 10,
                              }}>
                              {currCRMUser ? currCRMUser.number : ''}
                            </Text>

                            <View style={{ flexDirection: 'row', paddingTop: 30 }}>
                              <Text
                                style={{
                                  fontSize: switchMerchant ? 10 : 16,
                                  width: '40%',
                                }}>
                                Available Credit
                              </Text>
                              <Text
                                style={{
                                  fontSize: switchMerchant ? 10 : 16,
                                  width: '50%',
                                }}>
                                Tier
                              </Text>
                            </View>
                            <View style={{ flexDirection: 'row', paddingTop: 10 }}>
                              <Text
                                style={{
                                  fontSize: switchMerchant ? 10 : 16,
                                  width: '40%',
                                  fontFamily: 'NunitoSans-Bold',
                                }}>
                                RM {selectedCustomerLCCBalance.toFixed(2)}
                              </Text>
                              <Text
                                style={{
                                  fontSize: switchMerchant ? 10 : 16,
                                  width: '50%',
                                  fontFamily: 'NunitoSans-Bold',
                                }}>
                                {selectedCustomerEdit &&
                                  selectedCustomerEdit.levelName
                                  ? selectedCustomerEdit.levelName
                                  : 'N/A'}
                              </Text>
                            </View>
                          </View>
                        </View>

                        <View style={{ width: '50%' }}>
                          <View
                            style={{
                              alignItems: 'center',
                              justifyContent: 'center',
                            }}>
                            <Text
                              style={{
                                fontSize: switchMerchant ? 15 : 20,
                                fontWeight: 'bold',
                                margin: switchMerchant ? 1 : 12,
                                paddingTop: 20,
                              }}>
                              Enter The Bonus Amount To Add
                            </Text>
                            <View
                              style={{
                                justifyContent: 'center',
                                flexDirection: 'row',
                                alignItems: 'center',
                                alignSelf: 'center',
                              }}>
                              <Text
                                style={{
                                  fontSize: switchMerchant ? 13 : 20,
                                  color: Colors.fieldtTxtColor,
                                }}>
                                RM
                              </Text>
                            </View>
                            <View style={{ justifyContent: 'center' }}>
                              <Text style={{ fontSize: switchMerchant ? 22 : 50 }}>
                                {amount.length === 0 ? '0' : amount}
                              </Text>
                            </View>
                          </View>
                          <View>
                            <View
                              style={{
                                flexDirection: 'row',
                                flexWrap: 'wrap',
                                justifyContent: 'space-between',
                                alignItems: 'center',
                                alignSelf: 'center',
                                width: '55%',
                                paddingTop: switchMerchant ? 5 : 30,
                              }}>
                              <View
                                style={{
                                  flexDirection: 'row',
                                  justifyContent: 'space-between',
                                  alignSelf: 'center',
                                  alignItems: 'center',
                                  width: '100%',
                                }}>
                                <TouchableOpacity
                                  onPress={() => {
                                    onNumPadBtn(1);
                                  }}>
                                  <View
                                    style={[
                                      styles.pinBtn,
                                      switchMerchant
                                        ? {
                                          width: switchMerchant ? 35 : 70,
                                          height: switchMerchant ? 35 : 70,
                                          marginBottom: 7,
                                        }
                                        : { width: 70, height: 70 },
                                    ]}>
                                    <Text
                                      style={[
                                        styles.pinNo,
                                        { fontSize: switchMerchant ? 10 : 20 },
                                      ]}>
                                      1
                                    </Text>
                                  </View>
                                </TouchableOpacity>

                                <TouchableOpacity
                                  onPress={() => {
                                    onNumPadBtn(2);
                                  }}>
                                  <View
                                    style={[
                                      styles.pinBtn,
                                      switchMerchant
                                        ? {
                                          width: switchMerchant ? 35 : 70,
                                          height: switchMerchant ? 35 : 70,
                                          marginBottom: 7,
                                        }
                                        : { width: 70, height: 70 },
                                    ]}>
                                    <Text
                                      style={[
                                        styles.pinNo,
                                        { fontSize: switchMerchant ? 10 : 20 },
                                      ]}>
                                      2
                                    </Text>
                                  </View>
                                </TouchableOpacity>

                                <TouchableOpacity
                                  onPress={() => {
                                    onNumPadBtn(3);
                                  }}>
                                  <View
                                    style={[
                                      styles.pinBtn,
                                      switchMerchant
                                        ? {
                                          width: switchMerchant ? 35 : 70,
                                          height: switchMerchant ? 35 : 70,
                                          marginBottom: 7,
                                        }
                                        : { width: 70, height: 70 },
                                    ]}>
                                    <Text
                                      style={[
                                        styles.pinNo,
                                        { fontSize: switchMerchant ? 10 : 20 },
                                      ]}>
                                      3
                                    </Text>
                                  </View>
                                </TouchableOpacity>
                              </View>
                              <View
                                style={{
                                  flexDirection: 'row',
                                  justifyContent: 'space-between',
                                  alignSelf: 'center',
                                  alignItems: 'center',
                                  width: '100%',
                                }}>
                                <TouchableOpacity
                                  onPress={() => {
                                    onNumPadBtn(4);
                                  }}>
                                  <View
                                    style={[
                                      styles.pinBtn,
                                      switchMerchant
                                        ? {
                                          width: switchMerchant ? 35 : 70,
                                          height: switchMerchant ? 35 : 70,
                                          marginBottom: 7,
                                        }
                                        : { width: 70, height: 70 },
                                    ]}>
                                    <Text
                                      style={[
                                        styles.pinNo,
                                        { fontSize: switchMerchant ? 10 : 20 },
                                      ]}>
                                      4
                                    </Text>
                                  </View>
                                </TouchableOpacity>

                                <TouchableOpacity
                                  onPress={() => {
                                    onNumPadBtn(5);
                                  }}>
                                  <View
                                    style={[
                                      styles.pinBtn,
                                      switchMerchant
                                        ? {
                                          width: switchMerchant ? 35 : 70,
                                          height: switchMerchant ? 35 : 70,
                                          marginBottom: 7,
                                        }
                                        : { width: 70, height: 70 },
                                    ]}>
                                    <Text
                                      style={[
                                        styles.pinNo,
                                        { fontSize: switchMerchant ? 10 : 20 },
                                      ]}>
                                      5
                                    </Text>
                                  </View>
                                </TouchableOpacity>

                                <TouchableOpacity
                                  onPress={() => {
                                    onNumPadBtn(6);
                                  }}>
                                  <View
                                    style={[
                                      styles.pinBtn,
                                      switchMerchant
                                        ? {
                                          width: switchMerchant ? 35 : 70,
                                          height: switchMerchant ? 35 : 70,
                                          marginBottom: 7,
                                        }
                                        : { width: 70, height: 70 },
                                    ]}>
                                    <Text
                                      style={[
                                        styles.pinNo,
                                        { fontSize: switchMerchant ? 10 : 20 },
                                      ]}>
                                      6
                                    </Text>
                                  </View>
                                </TouchableOpacity>
                              </View>
                              <View
                                style={{
                                  flexDirection: 'row',
                                  justifyContent: 'space-between',
                                  alignSelf: 'center',
                                  alignItems: 'center',
                                  width: '100%',
                                }}>
                                <TouchableOpacity
                                  onPress={() => {
                                    onNumPadBtn(7);
                                  }}>
                                  <View
                                    style={[
                                      styles.pinBtn,
                                      switchMerchant
                                        ? {
                                          width: switchMerchant ? 35 : 70,
                                          height: switchMerchant ? 35 : 70,
                                          marginBottom: 7,
                                        }
                                        : { width: 70, height: 70 },
                                    ]}>
                                    <Text
                                      style={[
                                        styles.pinNo,
                                        { fontSize: switchMerchant ? 10 : 20 },
                                      ]}>
                                      7
                                    </Text>
                                  </View>
                                </TouchableOpacity>

                                <TouchableOpacity
                                  onPress={() => {
                                    onNumPadBtn(8);
                                  }}>
                                  <View
                                    style={[
                                      styles.pinBtn,
                                      switchMerchant
                                        ? {
                                          width: switchMerchant ? 35 : 70,
                                          height: switchMerchant ? 35 : 70,
                                          marginBottom: 7,
                                        }
                                        : { width: 70, height: 70 },
                                    ]}>
                                    <Text
                                      style={[
                                        styles.pinNo,
                                        { fontSize: switchMerchant ? 10 : 20 },
                                      ]}>
                                      8
                                    </Text>
                                  </View>
                                </TouchableOpacity>

                                <TouchableOpacity
                                  onPress={() => {
                                    onNumPadBtn(9);
                                  }}>
                                  <View
                                    style={[
                                      styles.pinBtn,
                                      switchMerchant
                                        ? {
                                          width: switchMerchant ? 35 : 70,
                                          height: switchMerchant ? 35 : 70,
                                          marginBottom: 7,
                                        }
                                        : { width: 70, height: 70 },
                                    ]}>
                                    <Text
                                      style={[
                                        styles.pinNo,
                                        { fontSize: switchMerchant ? 10 : 20 },
                                      ]}>
                                      9
                                    </Text>
                                  </View>
                                </TouchableOpacity>
                              </View>
                              <View
                                style={{
                                  flexDirection: 'row',
                                  justifyContent: 'space-between',
                                  alignSelf: 'center',
                                  alignItems: 'center',
                                  width: '100%',
                                }}>
                                <TouchableOpacity
                                  onPress={() => {
                                    onNumPadBtn('+');
                                  }}>
                                  <View
                                    style={[
                                      styles.pinBtn,
                                      switchMerchant
                                        ? {
                                          width: switchMerchant ? 35 : 70,
                                          height: switchMerchant ? 35 : 70,
                                          marginBottom: 7,
                                        }
                                        : { width: 70, height: 70 },
                                    ]}>
                                    <Text
                                      style={[
                                        styles.pinNo,
                                        { fontSize: switchMerchant ? 10 : 20 },
                                      ]}>
                                      +
                                    </Text>
                                  </View>
                                </TouchableOpacity>

                                <TouchableOpacity
                                  onPress={() => {
                                    onNumPadBtn(0);
                                  }}>
                                  <View
                                    style={[
                                      styles.pinBtn,
                                      switchMerchant
                                        ? {
                                          width: switchMerchant ? 35 : 70,
                                          height: switchMerchant ? 35 : 70,
                                          marginBottom: 7,
                                        }
                                        : { width: 70, height: 70 },
                                    ]}>
                                    <Text
                                      style={[
                                        styles.pinNo,
                                        { fontSize: switchMerchant ? 10 : 20 },
                                      ]}>
                                      0
                                    </Text>
                                  </View>
                                </TouchableOpacity>

                                <TouchableOpacity
                                  onPress={() => {
                                    onNumPadBtn(-1);
                                  }}>
                                  <View
                                    style={[
                                      styles.pinBtn,
                                      switchMerchant
                                        ? {
                                          width: switchMerchant ? 35 : 70,
                                          height: switchMerchant ? 35 : 70,
                                          marginBottom: 7,
                                        }
                                        : { width: 70, height: 70 },
                                    ]}>
                                    <Feather
                                      name="chevron-left"
                                      size={switchMerchant ? 13 : 30}
                                      color={'black'}
                                      style={{}}
                                    />
                                  </View>
                                </TouchableOpacity>
                              </View>
                            </View>
                            <View
                              style={{
                                justifyContent: 'flex-start',
                                alignSelf: 'center',
                                alignItems: 'center',
                              }}>
                              <TouchableOpacity
                                onPress={() => {
                                  // setAddCreditModal(false)
                                  // setAmount('')

                                  if (amount && parseFloat(amount) > 0) {
                                    addCredit();
                                    // setAddCreditdone(true);
                                  } else {
                                    Alert.alert(
                                      'Info',
                                      'Amount must be more than 0',
                                    );
                                  }
                                }}
                                disabled={phoneNumberChecking}>
                                <View
                                  style={{
                                    borderRadius: 10,
                                    backgroundColor: phoneNumberChecking
                                      ? 'grey'
                                      : Colors.primaryColor,
                                    paddingVertical: switchMerchant ? 5 : 10,
                                    paddingHorizontal: 25,
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                  }}>
                                  <Text
                                    style={{
                                      color: Colors.whiteColor,
                                      fontSize: switchMerchant ? 10 : 16,
                                      fontFamily: 'NunitoSans-Bold',
                                    }}>
                                    Add Extra Credit
                                  </Text>
                                </View>
                              </TouchableOpacity>
                            </View>
                          </View>
                        </View>
                      </View>
                    </View>
                  </View>
                </ModalView>
                <ModalView
                  visible={addCreditdone}
                  supportedOrientations={['landscape', 'portrait']}
                  style={{}}
                  animationType={'fade'}
                  transparent={true}>
                  <View
                    style={{
                      flex: 1,
                      backgroundColor: Colors.modalBgColor,
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}>
                    <View
                      style={{
                        width: switchMerchant
                          ? windowWidth * 0.7
                          : windowWidth * 0.77,
                        height: switchMerchant
                          ? windowHeight * 0.85
                          : windowHeight * 0.7,
                        backgroundColor: Colors.whiteColor,
                        borderRadius: 5,
                      }}>
                      <ScrollView
                        scrollEnabled={true}
                        showsVerticalScrollIndicator={false}>
                        <TouchableOpacity
                          style={{
                            position: 'absolute',
                            right: windowWidth * 0.02,
                            top: windowWidth * 0.02,

                            elevation: 1000,
                            zIndex: 1000,
                          }}
                          onPress={() => {
                            setAddCreditdone(false);
                            setAmount('');
                            setAddnotes(false);
                            setNotetext('');
                          }}>
                          <AntDesign
                            name="closecircle"
                            size={switchMerchant ? 15 : 25}
                            color={Colors.fieldtTxtColor}
                          />
                        </TouchableOpacity>
                        <View
                          style={{
                            justifyContent: 'center',
                            alignSelf: 'center',
                            alignItems: 'center',
                            paddingVertical: switchMerchant ? 15 : 30,
                          }}>
                          <Text
                            style={{
                              fontSize: switchMerchant ? 16 : 24,
                              fontFamily: 'NunitoSans-Bold',
                            }}>
                            Add Credit successfully
                          </Text>
                          <Text
                            style={{
                              fontSize: switchMerchant ? 10 : 16,
                            }}>
                            {currCRMUser ? currCRMUser.name : userName} will receive
                            credits in a moment
                          </Text>
                        </View>
                        <View
                          style={{
                            flexDirection: 'row',
                            alignSelf: 'center',
                            width: switchMerchant ? '80%' : '70%',
                            justifyContent: 'center',
                            paddingTop: switchMerchant ? 10 : 30,
                          }}>
                          <Text
                            style={{
                              fontSize: switchMerchant ? 10 : 16,
                              width: '20%',
                            }}>
                            Name
                          </Text>
                          <Text
                            style={{
                              fontSize: switchMerchant ? 10 : 16,
                              width: '40%',
                            }}>
                            Phone Number
                          </Text>
                          {/* <Text
                          style={{
                            fontSize: switchMerchant ? 10 : 16,
                            width: '20%',
                          }}>
                          Total Spend
                        </Text> */}
                          <Text
                            style={{
                              fontSize: switchMerchant ? 10 : 16,
                              width: '20%',
                            }}>
                            {/* Cashback */}
                            Added Credit
                          </Text>
                        </View>
                        <View
                          style={{
                            flexDirection: 'row',
                            alignSelf: 'center',
                            width: switchMerchant ? '80%' : '70%',
                            justifyContent: 'center',
                            paddingTop: switchMerchant ? 5 : 10,
                          }}>
                          <Text
                            style={{
                              fontSize: switchMerchant ? 10 : 16,
                              fontFamily: 'NunitoSans-Bold',
                              width: '20%',
                            }}>
                            {currCRMUser ? currCRMUser.name : userName}
                          </Text>
                          <Text
                            style={{
                              fontSize: switchMerchant ? 10 : 16,
                              fontFamily: 'NunitoSans-Bold',
                              width: '40%',
                            }}>
                            {currCRMUser ? currCRMUser.number : phoneNumber}
                          </Text>
                          {/* <Text
                          style={{
                            fontSize: switchMerchant ? 10 : 16,
                            fontFamily: 'NunitoSans-Bold',
                            width: '20%',
                          }}>
                          RM {amount}
                        </Text> */}
                          <Text
                            style={{
                              fontSize: switchMerchant ? 10 : 16,
                              fontFamily: 'NunitoSans-Bold',
                              width: '20%',
                            }}>
                            RM{' '}
                            {(selectedCustomerLCCTransactions[0]
                              ? selectedCustomerLCCTransactions[0].amount
                              : 0
                            ).toFixed(2)}
                          </Text>
                        </View>
                        <View
                          style={{
                            justifyContent: 'flex-start',
                            alignSelf: 'center',
                            paddingTop: switchMerchant ? 10 : 40,
                          }}>
                          <TouchableOpacity
                            onPress={() => {
                              {
                                addnotes == true
                                  ? setAddnotes(false)
                                  : setAddnotes(true);
                              }
                            }}>
                            <Text
                              style={{
                                color: Colors.primaryColor,
                                fontSize: switchMerchant ? 10 : 16,
                                borderBottomWidth: 1,
                                borderBottomColor: Colors.primaryColor,
                              }}>
                              Add notes
                            </Text>
                          </TouchableOpacity>
                        </View>
                        {addnotes == true ? (
                          <TextInput
                            style={{
                              fontSize: switchMerchant ? 10 : 16,
                              color: 'black',
                              fontFamily: 'NunitoSans-Regular',
                              borderBottomWidth: 0.5,
                              borderBottomColor: 'black',
                              width: '40%',
                              height: switchMerchant ? 35 : 40,
                              alignSelf: 'center',
                              alignItems: 'center',
                              marginVertical: switchMerchant ? 5 : 20,
                            }}
                            placeholder=""
                            placeholderTextColor={Platform.select({
                              ios: '#a9a9a9',
                            })}
                            onChangeText={(text) => {
                              setNotetext(text);
                            }}></TextInput>
                        ) : (
                          <View
                            style={{
                              height: switchMerchant ? 35 : 40,
                              marginVertical: switchMerchant ? 5 : 20,
                            }}></View>
                        )}
                        <View
                          style={{
                            justifyContent: 'flex-start',
                            alignSelf: 'center',
                            paddingTop: switchMerchant ? 10 : 20,
                          }}>
                          <TouchableOpacity
                            onPress={() => {
                              setAddCreditdone(false);
                              setAddnotes(false);
                              setNotetext('');
                              setAmount('');
                              setPhoneNumber('+60');
                              setCreditSection(CREDIT_SECTION.HOME);
                            }}>
                            <View
                              style={{
                                borderRadius: 10,
                                backgroundColor: Colors.primaryColor,
                                paddingVertical: switchMerchant ? 5 : 10,
                                paddingHorizontal: 50,
                                marginTop: 20,
                              }}>
                              <Text
                                style={{
                                  color: Colors.whiteColor,
                                  fontSize: switchMerchant ? 10 : 16,
                                  fontFamily: 'NunitoSans-Bold',
                                }}>
                                Done
                              </Text>
                            </View>
                          </TouchableOpacity>
                        </View>
                        <View
                          style={{
                            flexDirection: 'row',
                            alignSelf: 'center',
                            paddingTop: 30,
                            alignItems: 'center',
                          }}>
                          <TouchableOpacity
                            onPress={() => {
                              setAddCreditdone(false);
                              setAddnotes(false);
                              setNotetext('');
                              setAmount('');
                              setPhoneNumber(`${phoneNumber}`);
                              setCreditSection(CREDIT_SECTION.ADD_CASHBACK);
                            }}>
                            <Text
                              style={{
                                fontSize: switchMerchant ? 10 : 16,
                                color: Colors.primaryColor,
                              }}>
                              Also Add Cashback?
                            </Text>
                          </TouchableOpacity>
                          <Text
                            style={{
                              marginHorizontal: 20,
                              fontSize: switchMerchant ? 10 : 14,
                            }}>
                            or
                          </Text>
                          <TouchableOpacity
                            onPress={() => {
                              setAddCreditdone(false);
                              setAddnotes(false);
                              setNotetext('');
                              setAmount('');
                              setPhoneNumber(`${phoneNumber}`);
                              setCreditSection(CREDIT_SECTION.REDEEM_CREDIT);
                            }}>
                            <Text
                              style={{
                                fontSize: switchMerchant ? 10 : 16,
                                color: Colors.primaryColor,
                              }}>
                              Want to redeem a campaign?
                            </Text>
                          </TouchableOpacity>
                        </View>
                        <View
                          style={{
                            flexDirection: 'row',
                            alignSelf: 'center',
                            paddingTop: 20,
                          }}>
                          <TouchableOpacity
                            onPress={() => {
                              setAddCreditdone(false);
                              setAddnotes(false);
                              setNotetext('');
                              setAmount('');
                              setPhoneNumber(`${phoneNumber}`);
                              setCreditSection(CREDIT_SECTION.DEDUCT_CREDIT);
                            }}>
                            <Text
                              style={{
                                fontSize: switchMerchant ? 10 : 16,
                                color: Colors.tabRed,
                              }}>
                              Want to deduct credit?
                            </Text>
                          </TouchableOpacity>
                          <Text
                            style={{
                              marginHorizontal: 20,
                              fontSize: switchMerchant ? 10 : 14,
                            }}>
                            or
                          </Text>
                          <TouchableOpacity
                            onPress={() => {
                              setAddCreditdone(false);
                              setAddnotes(false);
                              setNotetext('');
                              setAmount('');
                              setPhoneNumber(`${phoneNumber}`);
                              setCreditSection(CREDIT_SECTION.ADD_CREDIT);
                            }}>
                            <Text
                              style={{
                                fontSize: switchMerchant ? 10 : 16,
                                color: Colors.primaryColor,
                                marginBottom: switchMerchant ? 30 : 0,
                              }}>
                              Want to add extra credit?
                            </Text>
                          </TouchableOpacity>
                        </View>
                      </ScrollView>
                    </View>
                  </View>
                </ModalView>
              </View>
            ) : (
              <></>
            )}
            {/* ///////////// Deduct Credit /////////////// */}

            {creditSection === CREDIT_SECTION.DEDUCT_CREDIT ? (
              <View
                style={{
                  flex: 1,
                  width: '100%',
                  height: '100%',
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  backgroundColor: Colors.highlightColor,
                }}>
                <View
                  style={{
                    width:
                      Platform.OS === 'ios'
                        ? windowWidth * 0.44
                        : windowWidth * 0.43,
                    backgroundColor: Colors.whiteColor,
                    borderRadius: 5,
                  }}>
                  {/* <View
                  style={{
                    flexDirection: 'row',
                  }}>
                  <TouchableOpacity
                    style={{
                      height: windowHeight * 0.055,
                      flexDirection: 'row',
                      alignContent: 'center',
                      alignItems: 'center',
                      marginTop: 10,
                      marginLeft: 10,
                    }}
                    onPress={() => {
                      setCreditSection(CREDIT_SECTION.HOME);
                      setPhoneNumber('+60');
                    }}>
                    <Feather
                      name="chevron-left"
                      size={switchMerchant ? 20 : 30}
                      color={Colors.primaryColor}
                    />
                    <Text
                      style={{
                        fontFamily: 'NunitoSans-Bold',
                        fontSize: switchMerchant ? 14 : 17,
                        color: Colors.primaryColor,
                        marginBottom: Platform.OS === 'ios' ? 0 : 1,
                      }}>
                      Back
                    </Text>
                  </TouchableOpacity>
                </View> */}
                  <View
                    style={{ paddingLeft: 20, paddingTop: switchMerchant ? 5 : 10 }}>
                    <Text style={{ fontSize: switchMerchant ? 16 : 24 }}>
                      {currOutlet.name}
                    </Text>
                    <Text
                      style={{
                        fontSize: switchMerchant ? 16 : 24,
                        fontWeight: 'bold',
                        paddingVertical: switchMerchant ? 2 : 10,
                      }}>
                      Deduct Credit Here
                    </Text>
                    <Text style={{ fontSize: switchMerchant ? 10 : 16 }}>
                      Mistakes Happen, Correct Them Here
                    </Text>
                  </View>
                  <View
                    style={{
                      paddingLeft: 20,
                      paddingTop: switchMerchant ? 10 : 20,
                      flex: 1,
                    }}>
                    <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                      <Text
                        style={{
                          fontSize: switchMerchant ? 12 : 22,
                          fontWeight: 'bold',
                          backgroundColor: Colors.highlightColor,
                          color: Colors.primaryColor,
                          borderRadius: 25,
                          alignSelf: 'center',
                          alignItems: 'center',
                          justifyContent: 'center',
                          paddingHorizontal: switchMerchant ? 10 : 20,
                          paddingVertical: switchMerchant ? 5 : 10,
                          marginRight: 10,
                        }}>
                        a
                      </Text>
                      <Text style={{ fontSize: switchMerchant ? 10 : 16 }}>
                        Enter Guest Phone Number
                      </Text>
                    </View>
                    <View
                      style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                        paddingVertical: switchMerchant ? 10 : 20,
                      }}>
                      <Text
                        style={{
                          fontSize: switchMerchant ? 12 : 22,
                          fontWeight: 'bold',
                          backgroundColor: Colors.highlightColor,
                          color: Colors.primaryColor,
                          borderRadius: 25,
                          alignSelf: 'center',
                          alignItems: 'center',
                          justifyContent: 'center',
                          paddingHorizontal: switchMerchant ? 10 : 20,
                          paddingVertical: switchMerchant ? 5 : 10,
                          marginRight: 10,
                        }}>
                        b
                      </Text>
                      <Text
                        style={{
                          fontSize: switchMerchant ? 10 : 16,
                          paddingVertical: switchMerchant ? 0 : 10,
                        }}>
                        {
                          'Fill In The Amount To Deduct\nThis Is Not Same As Redeeming'
                        }
                      </Text>
                    </View>
                    <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                      <Text
                        style={{
                          fontSize: switchMerchant ? 12 : 22,
                          fontWeight: 'bold',
                          backgroundColor: Colors.highlightColor,
                          color: Colors.primaryColor,
                          borderRadius: 25,
                          alignSelf: 'center',
                          alignItems: 'center',
                          justifyContent: 'center',
                          paddingHorizontal: switchMerchant ? 10 : 20,
                          paddingVertical: switchMerchant ? 5 : 10,
                          marginRight: 10,
                        }}>
                        c
                      </Text>
                      <Text style={{ fontSize: switchMerchant ? 10 : 16 }}>
                        Enter Management Password For Security Clearance
                      </Text>
                    </View>
                  </View>
                  <View
                    style={{
                      flex: 1,
                      paddingHorizontal: 20,
                      paddingTop: 20,
                      justifyContent: 'center',
                    }}>
                    {/* <View
                    style={{
                      flexDirection: 'row',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                    }}>
                    <Text style={{ fontSize: switchMerchant ? 10 : 14 }}>
                      Powered By{' '}
                      <Text
                        style={{
                          color: Colors.primaryColor,
                          fontSize: switchMerchant ? 12 : 16,
                        }}>
                        KooDoo
                      </Text>
                    </Text>
                    <TouchableOpacity style={{ flexDirection: 'row' }}>
                      <Feather
                        name="help-circle"
                        size={switchMerchant ? 15 : 20}
                        style={{ color: Colors.primaryColor, paddingRight: 5 }}
                      />
                      <Text
                        style={{
                          color: Colors.primaryColor,
                          fontSize: switchMerchant ? 10 : 14,
                        }}>
                        Help
                      </Text>
                    </TouchableOpacity>
                  </View> */}
                  </View>
                  <View
                    style={[{
                      paddingTop: 15,
                      flexDirection: 'row',
                      alignItems: 'center',
                      justifyContent: 'space-evenly',
                      paddingBottom: 30,
                    }]}>

                    <TouchableOpacity
                      style={[
                        {
                          width: '25%',
                          height: 140,
                          backgroundColor: Colors.tabMint,
                          borderRadius: 10,
                          flexDirection: 'row',
                          alignItems: 'center',
                          justifyContent: 'center',
                          paddingHorizontal: 20,
                          paddingVertical: 15,
                          shadowColor: '#000',
                          shadowOffset: {
                            width: 0,
                            height: 2,
                          },
                          shadowOpacity: 0.22,
                          shadowRadius: 3.22,
                          elevation: 3,
                        },
                      ]}
                      onPress={() => {
                        setCreditSection(CREDIT_SECTION.ADD_CASHBACK);
                      }}>

                      <View style={{ alignItems: 'center', }}>
                        <MaterialCommunityIcons
                          name="cash-plus"
                          size={switchMerchant ? 30 : 45}
                          style={{ color: Colors.whiteColor, }}
                        />
                        <Text
                          style={[
                            {
                              fontWeight: 'bold',
                              color: Colors.whiteColor,
                              fontSize: 18,
                              textAlign: 'center',
                              paddingBottom: 5,
                              paddingVertical: 10,
                            },
                          ]}>
                          Earn Point
                        </Text>
                      </View>
                    </TouchableOpacity>

                    <TouchableOpacity
                      style={[
                        {
                          width: '25%',
                          height: 140,
                          backgroundColor: Colors.tabCyan,
                          borderRadius: 10,
                          flexDirection: 'row',
                          alignItems: 'center',
                          justifyContent: 'center',
                          paddingHorizontal: 20,
                          paddingVertical: 15,
                          shadowColor: '#000',
                          shadowOffset: {
                            width: 0,
                            height: 2,
                          },
                          shadowOpacity: 0.22,
                          shadowRadius: 3.22,
                          elevation: 3,
                        },
                      ]}
                      onPress={() => {
                        setCreditSection(CREDIT_SECTION.REDEEM_CREDIT);
                      }}>
                      <View style={{ alignItems: 'center', }}>
                        <MaterialIcons
                          name="redeem"
                          size={switchMerchant ? 30 : 45}
                          style={{ color: Colors.whiteColor, }}
                        />
                        <Text
                          style={[
                            {
                              fontWeight: 'bold',
                              color: Colors.whiteColor,
                              fontSize: 18,
                              textAlign: 'center',
                              paddingBottom: 5,
                              paddingVertical: 10,
                            },
                          ]}>
                          Redeem Rewards
                        </Text>
                      </View>
                    </TouchableOpacity>
                    <TouchableOpacity
                      style={[
                        {
                          width: '25%',
                          height: 140,
                          backgroundColor: Colors.tabGold,
                          borderRadius: 10,
                          flexDirection: 'row',
                          alignItems: 'center',
                          justifyContent: 'center',
                          paddingHorizontal: 20,
                          paddingVertical: 15,
                          shadowColor: '#000',
                          shadowOffset: {
                            width: 0,
                            height: 2,
                          },
                          shadowOpacity: 0.22,
                          shadowRadius: 3.22,
                          elevation: 3,
                        },
                      ]}
                      onPress={() => {
                        setCreditSection(CREDIT_SECTION.ADD_CREDIT);
                      }}>

                      <View style={{ alignItems: 'center', }}>
                        <Entypo
                          name="credit"
                          size={switchMerchant ? 30 : 45}
                          style={{ color: Colors.whiteColor, }}
                        />
                        <Text
                          style={[
                            {
                              fontWeight: 'bold',
                              color: Colors.whiteColor,
                              fontSize: 18,
                              textAlign: 'center',
                              paddingBottom: 5,
                              paddingVertical: 10,
                            },
                          ]}>
                          Topup Points
                        </Text>
                      </View>
                    </TouchableOpacity>
                  </View>
                </View>
                <View
                  style={{
                    width:
                      Platform.OS === 'ios'
                        ? windowWidth * 0.42
                        : windowWidth * 0.43,
                    backgroundColor: Colors.whiteColor,
                    borderRadius: 5,
                  }}>
                  <View
                    style={{
                      alignItems: 'center',
                      justifyContent: 'center',
                      flex: 1,
                      paddingTop: Platform.OS === 'ios' ? 0 : 10,
                      marginTop: Platform.OS === 'ios' ? 10 : 0,
                    }}>
                    <Text
                      style={{
                        fontSize: switchMerchant ? 15 : 25,
                        fontWeight: 'bold',
                      }}>
                      Enter Your Phone Number
                    </Text>
                  </View>
                  <View
                    style={{
                      justifyContent: 'center',
                      alignItems: 'center',
                      flex: 1,
                    }}>
                    <Text style={{ fontSize: switchMerchant ? 22 : 45 }}>
                      {phoneNumber}
                    </Text>
                  </View>
                  <View>
                    <View
                      style={{
                        flexDirection: 'row',
                        flexWrap: 'wrap',
                        justifyContent: 'space-between',
                        alignItems: 'center',
                        alignSelf: 'center',
                        width: Platform.OS === 'ios' ? 270 : '45%',
                        paddingTop: switchMerchant ? 10 : 30,
                      }}>
                      <View
                        style={{
                          flexDirection: 'row',
                          justifyContent: 'space-between',
                          alignSelf: 'center',
                          alignItems: 'center',
                          width: '100%',
                        }}>
                        <TouchableOpacity
                          onPress={() => {
                            PhoneonNumPadBtn(1);
                          }}>
                          <View
                            style={[
                              styles.pinBtn,
                              switchMerchant
                                ? {
                                  width: switchMerchant ? 35 : 70,
                                  height: switchMerchant ? 35 : 70,
                                  marginBottom: 7,
                                }
                                : { width: 70, height: 70 },
                            ]}>
                            <Text
                              style={[
                                styles.pinNo,
                                { fontSize: switchMerchant ? 10 : 20 },
                              ]}>
                              1
                            </Text>
                          </View>
                        </TouchableOpacity>

                        <TouchableOpacity
                          onPress={() => {
                            PhoneonNumPadBtn(2);
                          }}>
                          <View
                            style={[
                              styles.pinBtn,
                              switchMerchant
                                ? {
                                  width: switchMerchant ? 35 : 70,
                                  height: switchMerchant ? 35 : 70,
                                  marginBottom: 7,
                                }
                                : { width: 70, height: 70 },
                            ]}>
                            <Text
                              style={[
                                styles.pinNo,
                                { fontSize: switchMerchant ? 10 : 20 },
                              ]}>
                              2
                            </Text>
                          </View>
                        </TouchableOpacity>

                        <TouchableOpacity
                          onPress={() => {
                            PhoneonNumPadBtn(3);
                          }}>
                          <View
                            style={[
                              styles.pinBtn,
                              switchMerchant
                                ? {
                                  width: switchMerchant ? 35 : 70,
                                  height: switchMerchant ? 35 : 70,
                                  marginBottom: 7,
                                }
                                : { width: 70, height: 70 },
                            ]}>
                            <Text
                              style={[
                                styles.pinNo,
                                { fontSize: switchMerchant ? 10 : 20 },
                              ]}>
                              3
                            </Text>
                          </View>
                        </TouchableOpacity>
                      </View>
                      <View
                        style={{
                          flexDirection: 'row',
                          justifyContent: 'space-between',
                          alignSelf: 'center',
                          alignItems: 'center',
                          width: '100%',
                        }}>
                        <TouchableOpacity
                          onPress={() => {
                            PhoneonNumPadBtn(4);
                          }}>
                          <View
                            style={[
                              styles.pinBtn,
                              switchMerchant
                                ? {
                                  width: switchMerchant ? 35 : 70,
                                  height: switchMerchant ? 35 : 70,
                                  marginBottom: 7,
                                }
                                : { width: 70, height: 70 },
                            ]}>
                            <Text
                              style={[
                                styles.pinNo,
                                { fontSize: switchMerchant ? 10 : 20 },
                              ]}>
                              4
                            </Text>
                          </View>
                        </TouchableOpacity>

                        <TouchableOpacity
                          onPress={() => {
                            PhoneonNumPadBtn(5);
                          }}>
                          <View
                            style={[
                              styles.pinBtn,
                              switchMerchant
                                ? {
                                  width: switchMerchant ? 35 : 70,
                                  height: switchMerchant ? 35 : 70,
                                  marginBottom: 7,
                                }
                                : { width: 70, height: 70 },
                            ]}>
                            <Text
                              style={[
                                styles.pinNo,
                                { fontSize: switchMerchant ? 10 : 20 },
                              ]}>
                              5
                            </Text>
                          </View>
                        </TouchableOpacity>

                        <TouchableOpacity
                          onPress={() => {
                            PhoneonNumPadBtn(6);
                          }}>
                          <View
                            style={[
                              styles.pinBtn,
                              switchMerchant
                                ? {
                                  width: switchMerchant ? 35 : 70,
                                  height: switchMerchant ? 35 : 70,
                                  marginBottom: 7,
                                }
                                : { width: 70, height: 70 },
                            ]}>
                            <Text
                              style={[
                                styles.pinNo,
                                { fontSize: switchMerchant ? 10 : 20 },
                              ]}>
                              6
                            </Text>
                          </View>
                        </TouchableOpacity>
                      </View>

                      <View
                        style={{
                          flexDirection: 'row',
                          justifyContent: 'space-between',
                          alignSelf: 'center',
                          alignItems: 'center',
                          width: '100%',
                        }}>
                        <TouchableOpacity
                          onPress={() => {
                            PhoneonNumPadBtn(7);
                          }}>
                          <View
                            style={[
                              styles.pinBtn,
                              switchMerchant
                                ? {
                                  width: switchMerchant ? 35 : 70,
                                  height: switchMerchant ? 35 : 70,
                                  marginBottom: 7,
                                }
                                : { width: 70, height: 70 },
                            ]}>
                            <Text
                              style={[
                                styles.pinNo,
                                { fontSize: switchMerchant ? 10 : 20 },
                              ]}>
                              7
                            </Text>
                          </View>
                        </TouchableOpacity>

                        <TouchableOpacity
                          onPress={() => {
                            PhoneonNumPadBtn(8);
                          }}>
                          <View
                            style={[
                              styles.pinBtn,
                              switchMerchant
                                ? {
                                  width: switchMerchant ? 35 : 70,
                                  height: switchMerchant ? 35 : 70,
                                  marginBottom: 7,
                                }
                                : { width: 70, height: 70 },
                            ]}>
                            <Text
                              style={[
                                styles.pinNo,
                                { fontSize: switchMerchant ? 10 : 20 },
                              ]}>
                              8
                            </Text>
                          </View>
                        </TouchableOpacity>

                        <TouchableOpacity
                          onPress={() => {
                            PhoneonNumPadBtn(9);
                          }}>
                          <View
                            style={[
                              styles.pinBtn,
                              switchMerchant
                                ? {
                                  width: switchMerchant ? 35 : 70,
                                  height: switchMerchant ? 35 : 70,
                                  marginBottom: 7,
                                }
                                : { width: 70, height: 70 },
                            ]}>
                            <Text
                              style={[
                                styles.pinNo,
                                { fontSize: switchMerchant ? 10 : 20 },
                              ]}>
                              9
                            </Text>
                          </View>
                        </TouchableOpacity>
                      </View>
                      <View
                        style={{
                          flexDirection: 'row',
                          justifyContent: 'space-between',
                          alignSelf: 'center',
                          alignItems: 'center',
                          width: '100%',
                        }}>
                        <TouchableOpacity
                          onPress={() => {
                            if (phoneNumber.includes('+')) {
                              return;
                            }
                            else {
                              PhoneonNumPadBtn('+');
                            }
                          }}>
                          <View
                            style={[
                              styles.pinBtn,
                              switchMerchant
                                ? {
                                  width: switchMerchant ? 35 : 70,
                                  height: switchMerchant ? 35 : 70,
                                  marginBottom: 7,
                                }
                                : { width: 70, height: 70 },
                            ]}>
                            <Text
                              style={[
                                styles.pinNo,
                                { fontSize: switchMerchant ? 10 : 20 },
                              ]}>
                              +
                            </Text>
                          </View>
                        </TouchableOpacity>

                        <TouchableOpacity
                          onPress={() => {
                            PhoneonNumPadBtn(0);
                          }}>
                          <View
                            style={[
                              styles.pinBtn,
                              switchMerchant
                                ? {
                                  width: switchMerchant ? 35 : 70,
                                  height: switchMerchant ? 35 : 70,
                                  marginBottom: 7,
                                }
                                : { width: 70, height: 70 },
                            ]}>
                            <Text
                              style={[
                                styles.pinNo,
                                { fontSize: switchMerchant ? 10 : 20 },
                              ]}>
                              0
                            </Text>
                          </View>
                        </TouchableOpacity>

                        <TouchableOpacity
                          onPress={() => {
                            if (phoneNumber != '+6') {
                              PhoneonNumPadBtn(-1);
                            }
                          }}>
                          <View
                            style={[
                              styles.pinBtn,
                              switchMerchant
                                ? {
                                  width: switchMerchant ? 35 : 70,
                                  height: switchMerchant ? 35 : 70,
                                  marginBottom: 7,
                                }
                                : { width: 70, height: 70 },
                            ]}>
                            <Feather
                              name="chevron-left"
                              size={switchMerchant ? 13 : 30}
                              color={'black'}
                              style={{}}
                            />
                          </View>
                        </TouchableOpacity>
                      </View>
                    </View>
                  </View>
                  <View
                    style={{
                      flex: 1,
                      flexDirection: 'row',
                      alignItems: 'center',
                      alignSelf: 'center',
                    }}>
                    <TouchableOpacity
                      onPress={() => {
                        if (currCRMUser) {
                          setDeductCreditModal(true);
                        } else {
                          Alert.alert('Info', 'No existing user found');
                        }
                      }}
                      disabled={phoneNumberChecking}>
                      <View
                        style={{
                          borderRadius: 10,
                          backgroundColor: phoneNumberChecking
                            ? 'grey'
                            : Colors.primaryColor,
                          paddingVertical: switchMerchant ? 4 : 10,
                          paddingHorizontal: switchMerchant ? 15 : 25,
                          marginBottom: switchMerchant ? 5 : 15,
                        }}>
                        <Text
                          style={{
                            color: Colors.whiteColor,
                            fontSize: switchMerchant ? 10 : 16,
                            fontFamily: 'NunitoSans-Bold',
                          }}>
                          Deduct Credit
                        </Text>
                      </View>
                    </TouchableOpacity>
                  </View>
                </View>
                <ModalView
                  visible={deductCreditModal}
                  supportedOrientations={['landscape', 'portrait']}
                  style={{}}
                  animationType={'fade'}
                  transparent={true}>
                  <View
                    style={{
                      flex: 1,
                      backgroundColor: Colors.modalBgColor,
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}>
                    <View
                      style={{
                        width: switchMerchant
                          ? windowWidth * 0.77
                          : windowWidth * 0.77,
                        height: switchMerchant
                          ? windowHeight * 0.85
                          : windowHeight * 0.85,
                        backgroundColor: Colors.whiteColor,
                        borderRadius: 5,
                      }}>
                      <TouchableOpacity
                        style={{
                          position: 'absolute',
                          right: windowWidth * 0.02,
                          top: windowWidth * 0.02,

                          elevation: 1000,
                          zIndex: 1000,
                        }}
                        onPress={() => {
                          setDeductCreditModal(false);
                          setAmount('');
                        }}>
                        <AntDesign
                          name="closecircle"
                          size={switchMerchant ? 15 : 25}
                          color={Colors.fieldtTxtColor}
                        />
                      </TouchableOpacity>
                      <View style={{ flexDirection: 'row', flex: 1 }}>
                        <View style={{ width: '50%', padding: 30 }}>
                          <View style={{ flexDirection: 'column', flex: 1 }}>
                            <Text
                              style={{
                                fontSize: switchMerchant ? 16 : 24,
                                fontFamily: 'NunitoSans-Bold',
                              }}>
                              Customer Details
                            </Text>
                            <Text
                              style={{
                                fontSize: switchMerchant ? 10 : 16,
                                paddingTop: switchMerchant ? 15 : 30,
                              }}>
                              First Name
                            </Text>
                            <Text
                              style={{
                                fontSize: switchMerchant ? 10 : 16,
                                fontFamily: 'NunitoSans-Bold',
                                paddingTop: 10,
                              }}>
                              {currCRMUser ? currCRMUser.name : ''}
                            </Text>

                            <Text
                              style={{
                                fontSize: switchMerchant ? 10 : 16,
                                paddingTop: switchMerchant ? 15 : 30,
                              }}>
                              Phone Number
                            </Text>
                            <Text
                              style={{
                                fontSize: switchMerchant ? 10 : 16,
                                fontFamily: 'NunitoSans-Bold',
                                paddingTop: 10,
                              }}>
                              {currCRMUser ? currCRMUser.number : ''}
                            </Text>

                            <View
                              style={{
                                flexDirection: 'row',
                                paddingTop: switchMerchant ? 15 : 30,
                              }}>
                              <Text
                                style={{
                                  fontSize: switchMerchant ? 10 : 16,
                                  width: '40%',
                                }}>
                                Available Credit
                              </Text>
                              <Text
                                style={{
                                  fontSize: switchMerchant ? 10 : 16,
                                  width: '50%',
                                }}>
                                Tier
                              </Text>
                            </View>
                            <View style={{ flexDirection: 'row', paddingTop: 10 }}>
                              <Text
                                style={{
                                  fontSize: switchMerchant ? 10 : 16,
                                  width: '40%',
                                  fontFamily: 'NunitoSans-Bold',
                                }}>
                                RM {selectedCustomerLCCBalance.toFixed(2)}
                              </Text>
                              <Text
                                style={{
                                  fontSize: switchMerchant ? 10 : 16,
                                  width: '50%',
                                  fontFamily: 'NunitoSans-Bold',
                                }}>
                                {selectedCustomerEdit &&
                                  selectedCustomerEdit.levelName
                                  ? selectedCustomerEdit.levelName
                                  : 'N/A'}
                              </Text>
                            </View>
                          </View>
                        </View>

                        <View style={{ width: '50%' }}>
                          <View
                            style={{
                              alignItems: 'center',
                              justifyContent: 'center',
                            }}>
                            <Text
                              style={{
                                fontSize: switchMerchant ? 15 : 20,
                                fontWeight: 'bold',
                                margin: switchMerchant ? 1 : 12,
                                paddingTop: 20,
                              }}>
                              Enter The Amount To Deduct
                            </Text>
                            <View
                              style={{
                                justifyContent: 'center',
                                flexDirection: 'row',
                                alignItems: 'center',
                                alignSelf: 'center',
                              }}>
                              <Text
                                style={{
                                  fontSize: switchMerchant ? 13 : 20,
                                  color: Colors.fieldtTxtColor,
                                }}>
                                RM
                              </Text>
                            </View>
                            <View style={{ justifyContent: 'center' }}>
                              <Text style={{ fontSize: switchMerchant ? 22 : 50 }}>
                                {amount.length === 0 ? '0' : amount}
                              </Text>
                            </View>
                          </View>
                          <View>
                            <View
                              style={{
                                flexDirection: 'row',
                                flexWrap: 'wrap',
                                justifyContent: 'space-between',
                                alignItems: 'center',
                                alignSelf: 'center',
                                width: '55%',
                                paddingTop: switchMerchant ? 5 : 30,
                              }}>
                              <View
                                style={{
                                  flexDirection: 'row',
                                  justifyContent: 'space-between',
                                  alignSelf: 'center',
                                  alignItems: 'center',
                                  width: '100%',
                                }}>
                                <TouchableOpacity
                                  onPress={() => {
                                    onNumPadBtn(1);
                                  }}>
                                  <View
                                    style={[
                                      styles.pinBtn,
                                      switchMerchant
                                        ? {
                                          width: switchMerchant ? 35 : 70,
                                          height: switchMerchant ? 35 : 70,
                                          marginBottom: 7,
                                        }
                                        : { width: 70, height: 70 },
                                    ]}>
                                    <Text
                                      style={[
                                        styles.pinNo,
                                        { fontSize: switchMerchant ? 10 : 20 },
                                      ]}>
                                      1
                                    </Text>
                                  </View>
                                </TouchableOpacity>

                                <TouchableOpacity
                                  onPress={() => {
                                    onNumPadBtn(2);
                                  }}>
                                  <View
                                    style={[
                                      styles.pinBtn,
                                      switchMerchant
                                        ? {
                                          width: switchMerchant ? 35 : 70,
                                          height: switchMerchant ? 35 : 70,
                                          marginBottom: 7,
                                        }
                                        : { width: 70, height: 70 },
                                    ]}>
                                    <Text
                                      style={[
                                        styles.pinNo,
                                        { fontSize: switchMerchant ? 10 : 20 },
                                      ]}>
                                      2
                                    </Text>
                                  </View>
                                </TouchableOpacity>

                                <TouchableOpacity
                                  onPress={() => {
                                    onNumPadBtn(3);
                                  }}>
                                  <View
                                    style={[
                                      styles.pinBtn,
                                      switchMerchant
                                        ? {
                                          width: switchMerchant ? 35 : 70,
                                          height: switchMerchant ? 35 : 70,
                                          marginBottom: 7,
                                        }
                                        : { width: 70, height: 70 },
                                    ]}>
                                    <Text
                                      style={[
                                        styles.pinNo,
                                        { fontSize: switchMerchant ? 10 : 20 },
                                      ]}>
                                      3
                                    </Text>
                                  </View>
                                </TouchableOpacity>
                              </View>
                              <View
                                style={{
                                  flexDirection: 'row',
                                  justifyContent: 'space-between',
                                  alignSelf: 'center',
                                  alignItems: 'center',
                                  width: '100%',
                                }}>
                                <TouchableOpacity
                                  onPress={() => {
                                    onNumPadBtn(4);
                                  }}>
                                  <View
                                    style={[
                                      styles.pinBtn,
                                      switchMerchant
                                        ? {
                                          width: switchMerchant ? 35 : 70,
                                          height: switchMerchant ? 35 : 70,
                                          marginBottom: 7,
                                        }
                                        : { width: 70, height: 70 },
                                    ]}>
                                    <Text
                                      style={[
                                        styles.pinNo,
                                        { fontSize: switchMerchant ? 10 : 20 },
                                      ]}>
                                      4
                                    </Text>
                                  </View>
                                </TouchableOpacity>

                                <TouchableOpacity
                                  onPress={() => {
                                    onNumPadBtn(5);
                                  }}>
                                  <View
                                    style={[
                                      styles.pinBtn,
                                      switchMerchant
                                        ? {
                                          width: switchMerchant ? 35 : 70,
                                          height: switchMerchant ? 35 : 70,
                                          marginBottom: 7,
                                        }
                                        : { width: 70, height: 70 },
                                    ]}>
                                    <Text
                                      style={[
                                        styles.pinNo,
                                        { fontSize: switchMerchant ? 10 : 20 },
                                      ]}>
                                      5
                                    </Text>
                                  </View>
                                </TouchableOpacity>

                                <TouchableOpacity
                                  onPress={() => {
                                    onNumPadBtn(6);
                                  }}>
                                  <View
                                    style={[
                                      styles.pinBtn,
                                      switchMerchant
                                        ? {
                                          width: switchMerchant ? 35 : 70,
                                          height: switchMerchant ? 35 : 70,
                                          marginBottom: 7,
                                        }
                                        : { width: 70, height: 70 },
                                    ]}>
                                    <Text
                                      style={[
                                        styles.pinNo,
                                        { fontSize: switchMerchant ? 10 : 20 },
                                      ]}>
                                      6
                                    </Text>
                                  </View>
                                </TouchableOpacity>
                              </View>
                              <View
                                style={{
                                  flexDirection: 'row',
                                  justifyContent: 'space-between',
                                  alignSelf: 'center',
                                  alignItems: 'center',
                                  width: '100%',
                                }}>
                                <TouchableOpacity
                                  onPress={() => {
                                    onNumPadBtn(7);
                                  }}>
                                  <View
                                    style={[
                                      styles.pinBtn,
                                      switchMerchant
                                        ? {
                                          width: switchMerchant ? 35 : 70,
                                          height: switchMerchant ? 35 : 70,
                                          marginBottom: 7,
                                        }
                                        : { width: 70, height: 70 },
                                    ]}>
                                    <Text
                                      style={[
                                        styles.pinNo,
                                        { fontSize: switchMerchant ? 10 : 20 },
                                      ]}>
                                      7
                                    </Text>
                                  </View>
                                </TouchableOpacity>

                                <TouchableOpacity
                                  onPress={() => {
                                    onNumPadBtn(8);
                                  }}>
                                  <View
                                    style={[
                                      styles.pinBtn,
                                      switchMerchant
                                        ? {
                                          width: switchMerchant ? 35 : 70,
                                          height: switchMerchant ? 35 : 70,
                                          marginBottom: 7,
                                        }
                                        : { width: 70, height: 70 },
                                    ]}>
                                    <Text
                                      style={[
                                        styles.pinNo,
                                        { fontSize: switchMerchant ? 10 : 20 },
                                      ]}>
                                      8
                                    </Text>
                                  </View>
                                </TouchableOpacity>

                                <TouchableOpacity
                                  onPress={() => {
                                    onNumPadBtn(9);
                                  }}>
                                  <View
                                    style={[
                                      styles.pinBtn,
                                      switchMerchant
                                        ? {
                                          width: switchMerchant ? 35 : 70,
                                          height: switchMerchant ? 35 : 70,
                                          marginBottom: 7,
                                        }
                                        : { width: 70, height: 70 },
                                    ]}>
                                    <Text
                                      style={[
                                        styles.pinNo,
                                        { fontSize: switchMerchant ? 10 : 20 },
                                      ]}>
                                      9
                                    </Text>
                                  </View>
                                </TouchableOpacity>
                              </View>
                              <View
                                style={{
                                  flexDirection: 'row',
                                  justifyContent: 'space-between',
                                  alignSelf: 'center',
                                  alignItems: 'center',
                                  width: '100%',
                                }}>
                                <TouchableOpacity
                                  onPress={() => {
                                    onNumPadBtn('+');
                                  }}>
                                  <View
                                    style={[
                                      styles.pinBtn,
                                      switchMerchant
                                        ? {
                                          width: switchMerchant ? 35 : 70,
                                          height: switchMerchant ? 35 : 70,
                                          marginBottom: 7,
                                        }
                                        : { width: 70, height: 70 },
                                    ]}>
                                    <Text
                                      style={[
                                        styles.pinNo,
                                        { fontSize: switchMerchant ? 10 : 20 },
                                      ]}>
                                      +
                                    </Text>
                                  </View>
                                </TouchableOpacity>

                                <TouchableOpacity
                                  onPress={() => {
                                    onNumPadBtn(0);
                                  }}>
                                  <View
                                    style={[
                                      styles.pinBtn,
                                      switchMerchant
                                        ? {
                                          width: switchMerchant ? 35 : 70,
                                          height: switchMerchant ? 35 : 70,
                                          marginBottom: 7,
                                        }
                                        : { width: 70, height: 70 },
                                    ]}>
                                    <Text
                                      style={[
                                        styles.pinNo,
                                        { fontSize: switchMerchant ? 10 : 20 },
                                      ]}>
                                      0
                                    </Text>
                                  </View>
                                </TouchableOpacity>

                                <TouchableOpacity
                                  onPress={() => {
                                    onNumPadBtn(-1);
                                  }}>
                                  <View
                                    style={[
                                      styles.pinBtn,
                                      switchMerchant
                                        ? {
                                          width: switchMerchant ? 35 : 70,
                                          height: switchMerchant ? 35 : 70,
                                          marginBottom: 7,
                                        }
                                        : { width: 70, height: 70 },
                                    ]}>
                                    <Feather
                                      name="chevron-left"
                                      size={switchMerchant ? 13 : 30}
                                      color={'black'}
                                      style={{}}
                                    />
                                  </View>
                                </TouchableOpacity>
                              </View>
                            </View>
                            <View
                              style={{
                                justifyContent: 'flex-start',
                                alignItems: 'center',
                                alignSelf: 'center',
                              }}>
                              <TouchableOpacity
                                onPress={() => {
                                  // setAddCreditModal(false)
                                  // setAmount('')

                                  if (amount && parseFloat(amount) > 0) {
                                    deductCredit();
                                    // setDeductCreditdone(true);
                                  } else {
                                    Alert.alert(
                                      'Info',
                                      'Amount must be more than 0',
                                    );
                                  }
                                }}
                                disabled={phoneNumberChecking}>
                                <View
                                  style={{
                                    borderRadius: 10,
                                    backgroundColor: phoneNumberChecking
                                      ? 'grey'
                                      : Colors.primaryColor,
                                    paddingVertical: switchMerchant ? 5 : 10,
                                    paddingHorizontal: 25,
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                  }}>
                                  <Text
                                    style={{
                                      color: Colors.whiteColor,
                                      fontSize: switchMerchant ? 10 : 16,
                                      fontFamily: 'NunitoSans-Bold',
                                    }}>
                                    Deduct Credit
                                  </Text>
                                </View>
                              </TouchableOpacity>
                            </View>
                          </View>
                        </View>
                      </View>
                    </View>
                  </View>
                </ModalView>
                <ModalView
                  visible={deductCreditdone}
                  supportedOrientations={['landscape', 'portrait']}
                  style={{}}
                  animationType={'fade'}
                  transparent={true}>
                  <View
                    style={{
                      flex: 1,
                      backgroundColor: Colors.modalBgColor,
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}>
                    <View
                      style={{
                        width: switchMerchant
                          ? windowWidth * 0.7
                          : windowWidth * 0.77,
                        height: switchMerchant
                          ? windowHeight * 0.85
                          : windowHeight * 0.8,
                        backgroundColor: Colors.whiteColor,
                        borderRadius: 5,
                      }}>
                      <ScrollView
                        scrollEnabled={true}
                        showsVerticalScrollIndicator={false}>
                        <TouchableOpacity
                          style={{
                            position: 'absolute',
                            right: windowWidth * 0.02,
                            top: windowWidth * 0.02,

                            elevation: 1000,
                            zIndex: 1000,
                          }}
                          onPress={() => {
                            setDeductCreditdone(false);
                            setAmount('');
                            setAddnotes(false);
                            setNotetext('');
                          }}>
                          <AntDesign
                            name="closecircle"
                            size={switchMerchant ? 15 : 25}
                            color={Colors.fieldtTxtColor}
                          />
                        </TouchableOpacity>
                        <View
                          style={{
                            justifyContent: 'center',
                            alignSelf: 'center',
                            alignItems: 'center',
                            paddingVertical: switchMerchant ? 15 : 30,
                          }}>
                          <Text
                            style={{
                              fontSize: switchMerchant ? 16 : 24,
                              fontFamily: 'NunitoSans-Bold',
                            }}>
                            Deduct credit successfully
                          </Text>
                          <Text
                            style={{
                              fontSize: switchMerchant ? 10 : 16,
                            }}>
                            {currCRMUser ? currCRMUser.name : userName} will deduct
                            credits in a moment
                          </Text>
                        </View>
                        <View
                          style={{
                            flexDirection: 'row',
                            alignSelf: 'center',
                            width: switchMerchant ? '80%' : '70%',
                            justifyContent: 'center',
                            paddingTop: switchMerchant ? 10 : 30,
                          }}>
                          <Text
                            style={{
                              fontSize: switchMerchant ? 10 : 16,
                              width: '20%',
                            }}>
                            Name
                          </Text>
                          <Text
                            style={{
                              fontSize: switchMerchant ? 10 : 16,
                              width: '40%',
                            }}>
                            Phone Number
                          </Text>
                          {/* <Text
                          style={{
                            fontSize: switchMerchant ? 10 : 16,
                            width: '20%',
                          }}>
                          Total Spend
                        </Text> */}
                          <Text
                            style={{
                              fontSize: switchMerchant ? 10 : 16,
                              width: '20%',
                            }}>
                            {/* Cashback */}
                            Deducted Credit
                          </Text>
                        </View>
                        <View
                          style={{
                            flexDirection: 'row',
                            alignSelf: 'center',
                            width: switchMerchant ? '80%' : '70%',
                            justifyContent: 'center',
                            paddingTop: switchMerchant ? 5 : 10,
                          }}>
                          <Text
                            style={{
                              fontSize: switchMerchant ? 10 : 16,
                              fontFamily: 'NunitoSans-Bold',
                              width: '20%',
                            }}>
                            {currCRMUser ? currCRMUser.name : userName}
                          </Text>
                          <Text
                            style={{
                              fontSize: switchMerchant ? 10 : 16,
                              fontFamily: 'NunitoSans-Bold',
                              width: '40%',
                            }}>
                            {currCRMUser ? currCRMUser.number : phoneNumber}
                          </Text>
                          {/* <Text
                          style={{
                            fontSize: switchMerchant ? 10 : 16,
                            fontFamily: 'NunitoSans-Bold',
                            width: '20%',
                          }}>
                          RM {amount}
                        </Text> */}
                          <Text
                            style={{
                              fontSize: switchMerchant ? 10 : 16,
                              fontFamily: 'NunitoSans-Bold',
                              width: '20%',
                            }}>
                            RM{' '}
                            {(selectedCustomerLCCTransactions[0]
                              ? selectedCustomerLCCTransactions[0].amount
                              : 0
                            ).toFixed(2)}
                          </Text>
                        </View>
                        <View
                          style={{
                            justifyContent: 'flex-start',
                            alignSelf: 'center',
                            paddingTop: switchMerchant ? 10 : 40,
                          }}>
                          <TouchableOpacity
                            onPress={() => {
                              {
                                addnotes == true
                                  ? setAddnotes(false)
                                  : setAddnotes(true);
                              }
                            }}>
                            <Text
                              style={{
                                color: Colors.primaryColor,
                                fontSize: switchMerchant ? 10 : 16,
                                borderBottomWidth: 1,
                                borderBottomColor: Colors.primaryColor,
                              }}>
                              Add notes
                            </Text>
                          </TouchableOpacity>
                        </View>
                        {addnotes == true ? (
                          <TextInput
                            style={{
                              fontSize: switchMerchant ? 10 : 16,
                              color: 'black',
                              fontFamily: 'NunitoSans-Regular',
                              borderBottomWidth: 0.5,
                              borderBottomColor: 'black',
                              width: '40%',
                              height: switchMerchant ? 35 : 40,
                              alignSelf: 'center',
                              alignItems: 'center',
                              marginVertical: switchMerchant ? 5 : 20,
                            }}
                            placeholder=""
                            placeholderTextColor={Platform.select({
                              ios: '#a9a9a9',
                            })}
                            onChangeText={(text) => {
                              setNotetext(text);
                            }}></TextInput>
                        ) : (
                          <View
                            style={{
                              height: switchMerchant ? 35 : 40,
                              marginVertical: switchMerchant ? 5 : 20,
                            }}></View>
                        )}
                        <View
                          style={{
                            justifyContent: 'flex-start',
                            alignSelf: 'center',
                            paddingTop: switchMerchant ? 10 : 20,
                          }}>
                          <TouchableOpacity
                            onPress={() => {
                              setDeductCreditdone(false);
                              setAddnotes(false);
                              setNotetext('');
                              setAmount('');
                              setPhoneNumber('+60');
                              setCreditSection(CREDIT_SECTION.HOME);
                            }}>
                            <View
                              style={{
                                borderRadius: 10,
                                backgroundColor: Colors.primaryColor,
                                paddingVertical: switchMerchant ? 5 : 10,
                                paddingHorizontal: 50,
                                marginTop: 20,
                              }}>
                              <Text
                                style={{
                                  color: Colors.whiteColor,
                                  fontSize: switchMerchant ? 10 : 16,
                                  fontFamily: 'NunitoSans-Bold',
                                }}>
                                Done
                              </Text>
                            </View>
                          </TouchableOpacity>
                        </View>
                        <View
                          style={{
                            flexDirection: 'row',
                            alignSelf: 'center',
                            paddingTop: 30,
                            alignItems: 'center',
                          }}>
                          <TouchableOpacity
                            onPress={() => {
                              setDeductCreditdone(false);
                              setAddnotes(false);
                              setNotetext('');
                              setAmount('');
                              setPhoneNumber(`${phoneNumber}`);
                              setCreditSection(CREDIT_SECTION.ADD_CASHBACK);
                            }}>
                            <Text
                              style={{
                                fontSize: switchMerchant ? 10 : 16,
                                color: Colors.primaryColor,
                              }}>
                              Also Add Cashback?
                            </Text>
                          </TouchableOpacity>
                          <Text
                            style={{
                              marginHorizontal: 20,
                              fontSize: switchMerchant ? 10 : 14,
                            }}>
                            or
                          </Text>
                          <TouchableOpacity
                            onPress={() => {
                              setDeductCreditdone(false);
                              setAddnotes(false);
                              setNotetext('');
                              setAmount('');
                              setPhoneNumber(`${phoneNumber}`);
                              setCreditSection(CREDIT_SECTION.REDEEM_CREDIT);
                            }}>
                            <Text
                              style={{
                                fontSize: switchMerchant ? 10 : 16,
                                color: Colors.primaryColor,
                              }}>
                              Want to redeem a campaign?
                            </Text>
                          </TouchableOpacity>
                        </View>
                        <View
                          style={{
                            flexDirection: 'row',
                            alignSelf: 'center',
                            paddingTop: 20,
                          }}>
                          <TouchableOpacity
                            onPress={() => {
                              setDeductCreditdone(false);
                              setAddnotes(false);
                              setNotetext('');
                              setAmount('');
                              setPhoneNumber(`${phoneNumber}`);
                              setCreditSection(CREDIT_SECTION.DEDUCT_CREDIT);
                            }}>
                            <Text
                              style={{
                                fontSize: switchMerchant ? 10 : 16,
                                color: Colors.tabRed,
                              }}>
                              Want to deduct credit?
                            </Text>
                          </TouchableOpacity>
                          <Text
                            style={{
                              marginHorizontal: 20,
                              fontSize: switchMerchant ? 10 : 14,
                            }}>
                            or
                          </Text>
                          <TouchableOpacity
                            onPress={() => {
                              setDeductCreditdone(false);
                              setAddnotes(false);
                              setNotetext('');
                              setAmount('');
                              setPhoneNumber(`${phoneNumber}`);
                              setCreditSection(CREDIT_SECTION.ADD_CREDIT);
                            }}>
                            <Text
                              style={{
                                fontSize: switchMerchant ? 10 : 16,
                                color: Colors.primaryColor,
                                marginBottom: switchMerchant ? 30 : 0,
                              }}>
                              Want to add extra credit?
                            </Text>
                          </TouchableOpacity>
                        </View>
                      </ScrollView>
                    </View>
                  </View>
                </ModalView>
              </View>
            ) : (
              <></>
            )}

            {/*///////////// History //////////////// */}
            <ModalView
              visible={HistoryModal}
              supportedOrientations={['landscape', 'portrait']}
              style={{}}
              animationType={'fade'}
              transparent={true}>
              <View
                style={{
                  flex: 1,
                  backgroundColor: Colors.modalBgColor,
                  alignItems: 'center',
                  justifyContent: 'center',
                }}>
                <View
                  style={{
                    width: switchMerchant
                      ? windowWidth * 0.8
                      : windowWidth * 0.87,
                    height: windowHeight * 0.77,
                    backgroundColor: Colors.whiteColor,
                    borderRadius: 5,
                    padding: 20,
                  }}>
                  <TouchableOpacity
                    style={{
                      position: 'absolute',
                      right: windowWidth * 0.02,
                      top: windowWidth * 0.02,

                      elevation: 1000,
                      zIndex: 1000,
                    }}
                    onPress={() => {
                      setHistoryModal(false);
                    }}>
                    <AntDesign
                      name="closecircle"
                      size={switchMerchant ? 15 : 25}
                      color={Colors.fieldtTxtColor}
                    />
                  </TouchableOpacity>
                  <View style={{}}>
                    <Text
                      style={{
                        fontSize: switchMerchant ? 16 : 24,
                        fontFamily: 'NunitoSans-Bold',
                      }}>
                      {currOutlet.name}
                    </Text>
                  </View>
                  {/* List Title */}
                  <View
                    style={{
                      flexDirection: 'row',
                      backgroundColor: '#F2F3F7',
                      padding: 10,
                      marginTop: 20,
                      borderRadius: 5,
                    }}>
                    <Text
                      style={{
                        fontSize: switchMerchant ? 10 : 16,
                        fontFamily: 'NunitoSans-Bold',
                        width: '20%',
                      }}>
                      Name
                    </Text>
                    <Text
                      style={{
                        fontSize: switchMerchant ? 10 : 16,
                        fontFamily: 'NunitoSans-Bold',
                        width: '25%',
                      }}>
                      Phone
                    </Text>
                    <Text
                      style={{
                        fontSize: switchMerchant ? 10 : 16,
                        fontFamily: 'NunitoSans-Bold',
                        width: '20%',
                      }}>
                      Action
                    </Text>
                    <Text
                      style={{
                        fontSize: switchMerchant ? 10 : 16,
                        fontFamily: 'NunitoSans-Bold',
                        width: '20%',
                      }}>
                      Date
                    </Text>
                    <Text
                      style={{
                        fontSize: switchMerchant ? 10 : 16,
                        fontFamily: 'NunitoSans-Bold',
                        width: '15%',
                      }}>
                      Status
                    </Text>
                  </View>
                  {/* List */}

                  <FlatList
                    data={selectedCustomerLCCTransactions}
                    // extraData={selectedCustomerLCCTransactions}
                    renderItem={renderViewHistory}
                    keyExtractor={(item, index) => index}
                    contentContainerStyle={
                      {
                        // paddingLeft: 10,
                        // paddingTop: 20,
                        // width: windowWidth * 0.88,
                        // backgroundColor: 'red'
                      }
                    }
                  />
                </View>
              </View>
            </ModalView>
          </View>
        </View>
      </View >
    </UserIdleWrapper>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.highlightColor,
    flexDirection: 'row',
    fontFamily: 'NunitoSans-Regular',
  },
  list: {
    backgroundColor: Colors.whiteColor,
    width: Dimensions.get('window').width * 0.87,
    height: Dimensions.get('window').height * 0.66,
    marginTop: 10,
    marginHorizontal: 30,
    borderRadius: 5,
    shadowOpacity: 0,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
  },
  listItem: {
    fontFamily: 'NunitoSans-Regular',
    marginLeft: 20,
    color: Colors.descriptionColor,
    fontSize: 16,
  },
  sidebar: {
    width: Dimensions.get('window').width * Styles.sideBarWidth,
    // shadowColor: '#000',
    // shadowOffset: {
    //   width: 0,
    //   height: 8,
    // },
    // shadowOpacity: 0.44,
    // shadowRadius: 10.32,
    // elevation: 16,
  },
  content: {
    padding: 20,
    width: Dimensions.get('window').width * (1 - Styles.sideBarWidth),
    // backgroundColor: 'lightgrey',
    backgroundColor: Colors.fieldtBgColor,
  },
  textInput: {
    fontFamily: 'NunitoSans-Regular',
    width: 300,
    height: 50,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 10,
    marginRight: 0,
    flex: 1,
    flexDirection: 'row',
  },
  textSize: {
    fontSize: 19,
    fontFamily: 'NunitoSans-SemiBold',
  },
  creditbutton: {
    borderWidth: 1,
    borderRadius: 5,
    padding: 10,
    alignItems: 'center',
    alignSelf: 'center',
    justifyContent: 'center',
    width: Dimensions.get('window').width * 0.3,
  },
  creditbuttonsecond: {
    borderWidth: 1,
    borderRadius: 5,
    padding: 10,
    flexDirection: 'column',
    alignItems: 'center',
    alignSelf: 'center',
    justifyContent: 'center',
    width: Dimensions.get('window').width * 0.10,
    height: Dimensions.get('window').height * 0.15,
  },
  pinBtn: {
    backgroundColor: Colors.lightPrimary,
    width: 115,
    height: 60,
    marginBottom: 16,
    alignContent: 'center',
    justifyContent: 'center',
    borderColor: Colors.fieldtTxtColor,
    alignItems: 'center',
    borderRadius: 10,
  },
  pinNo: {
    fontSize: 20,
    fontFamily: 'NunitoSans-Bold',
  },
  headerLeftStyle: {
    width: Dimensions.get('window').width * 0.17,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default LoyaltyPhone;
