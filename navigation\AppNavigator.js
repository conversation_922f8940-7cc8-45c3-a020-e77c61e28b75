import * as React from 'react';
import { Platform, Dimensions, useWindowDimensions, View } from 'react-native';
import { NavigationContainer } from '@react-navigation/native';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import {
  createStackNavigator,
  TransitionSpecs,
  HeaderStyleInterpolators,
  CardStyleInterpolators,
} from '@react-navigation/stack';
import { default as TabletScreenSwitcher } from 'react-native-tablet-switcher';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import Icon from 'react-native-vector-icons/FontAwesome5';
// import page
import analytics from '@react-native-firebase/analytics';
import HistoryScreen from '../screen/HistoryScreen';
import KitchenScreen from '../screen/KitchenScreen';
import TableScreen from '../screen/TableScreen';
import OrderScreen from '../screen/OrderScreen';
import TakeawayScreen from '../screen/TakeawayScreen';
import OtherDeliveryScreen from '../screen/OtherDeliveryScreen';
import ReservationScreen from '../screen/ReservationScreen';
import QueueScreen from '../screen/QueueScreen';
import QueueConfigScreen from '../screen/QueueConfigScreen';
import SettingScreen from '../screen/SettingScreen';
import SettingShiftScreen from '../screen/SettingShiftScreen';
import SettingTaxScreen from '../screen/SettingTaxScreen';
import SettingReceiptScreen from '../screen/SettingReceiptScreen';
import SettingRedemptionScreen from '../screen/SettingRedemptionScreen';
import SettingOrderScreen from '../screen/SettingOrderScreen';
import SettingPrinterScreen from '../screen/SettingPrinterScreen';
import SettingPaymentScreen from '../screen/SettingPaymentScreen';
import SettingGrabScreen from '../screen/SettingGrabScreen';
import EmployeeScreen from '../screen/EmployeeScreen';
import VoucherScreen from '../screen/VoucherScreen';
import PromotionListScreen from '../screen/PromotionListScreen';
import AutoPushNotificationScreen from '../screen/AutoPushNotificationScreen';
import ManualPushNotificationScreen from '../screen/ManualPushNotificationScreen';
import LocationBasedNotificationScreen from '../screen/LocationBasedNotificationScreen';
import InventoryScreen from '../screen/InventoryScreen';
import InventoryProductScreen from '../screen/InventoryProductScreen';
import PinLogin from '../screen/PinLogin';
import RedemptionScreen from '../screen/RedemptionScreen';
import RedemptionExpiredScreen from '../screen/RedemptionExpiredScreen';
import RedemptionRedeemedScreen from '../screen/RedemptionRedeemedScreen';
import AllTransaction from '../screen/AllTransaction';
// import ReportStockValue from '../screen/ReportStockValue';
import ReportSaleByShift from '../screen/ReportSaleByShift';
import ReportShiftPayInOut from '../screen/ReportShiftPayInOut';
import ReportSalesOvertime from '../screen/ReportSalesOvertime';
import ReportSalesRevisit from '../screen/ReportSalesRevisit';
import ReportSalesUpselling from '../screen/ReportSalesUpselling';
// import ReportSalesProduct from '../screen/ReportSalesProduct';
// import ReportSalesCategory from '../screen/ReportSalesCategory';
import ReportSalesVariant from '../screen/ReportSalesVariant';
import ReportSalesAddOns from '../screen/ReportSalesAddOns';
// import ReportSalesSKU from '../screen/ReportSalesSKU';
import ReportSalesTransaction from '../screen/ReportSalesTransaction';
import ReportSalesPaymentMethod from '../screen/ReportSalesPaymentMethod';
// import ReportSalesByRedemption from '../screen/ReportSalesByRedemption';
import ReportSalesRefund from '../screen/ReportSalesRefund';
// import ReportSalesAnalysis from '../screen/ReportSalesAnalysis';
import ReportSalesAov from '../screen/ReportSalesAov';
import ReportSalesOrderCount from '../screen/ReportSalesOrderCount';
import ReportSalesRevisitCount from '../screen/ReportSalesRevisitCount';
import Colors from '../constant/Colors';
import ProductScreen from '../screen/ProductScreen';
import Styles from '../constant/Styles';
import DashboardScreen from '../screen/DashboardScreen';
import ProductAddScreen from '../screen/ProductAddScreen';
import ProductMenuScreen from '../screen/ProductMenuScreen';
import StockTransferScreen from '../screen/StockTransferScreen';
import StockTransferProductScreen from '../screen/StockTransferProductScreen';
import StockTakeScreen from '../screen/StockTakeScreen';
import StockTakeProductScreen from '../screen/StockTakeProductScreen';
import StockReturnProductScreen from '../screen/StockReturnProductScreen';
import PurchaseOrderScreen from '../screen/PurchaseOrderScreen';
import StockInsertScreen from '../screen/StockInsertScreen';
import PurchaseOrderProductScreen from '../screen/PurchaseOrderProductScreen';
import OutletMenuScreen from '../screen/OutletMenuScreen';
import MenuItemDetailsScreen from '../screen/MenuItemDetailsScreen';
import CartScreen from '../screen/CartScreen';
import SupplierScreen from '../screen/SupplierScreen';
import SupplierProductScreen from '../screen/SupplierProductScreen';
import ProductCategoryScreen from '../screen/ProductCategoryScreen';
import Catalog from '../screen/Catalog';
import VariantAddOnScreen from '../screen/VariantAddOnScreen';
import PreorderPackageScreen from '../screen/PreorderPackageScreen';
import CrmScreen from '../screen/CrmScreen';
import NewCustomer from '../screen/NewCustomer';
import SegmentScreen from '../screen/SegmentScreen';
import CustomerReviewScreen from '../screen/CustomerReviewScreen';
import SettingEindemnityForm from '../screen/SettingE-indemnityForm';
import SettingCredit from '../screen/SettingCredit';
import SettingSetCredit from '../screen/SettingSetCredit';
import SettingBooking from '../screen/SettingBooking';
import SettingQuestionaire from '../screen/SettingQuestionaire';
import SettingNotification from '../screen/SettingNotification';
import SettingReservationScreen from '../screen/SettingReservationScreen';
import SettingLoyaltyScreen from '../screen/SettingLoyaltyScreen';
import NewCampaignScreen from '../screen/NewCampaignScreen';
import SettingQuestionairePreview from '../screen/SettingQuestionairePreview';
import LoyaltyPointsRate from '../screen/LoyaltyPointsRate';
import LoyaltyStampScreen from '../screen/LoyaltyStampScreen';
import GlobalCreditScreen from '../screen/GlobalCredit';
import Campaign from '../screen/Campaign';
import InventoryDetail from '../screen/InventoryDetail';
import VoucherReport from '../screen/VoucherReport';
import TaggableVoucherReport from '../screen/TaggableVoucherReport';
import TopUpCreditReport from '../screen/TopUpCreditReport';
import PromotionReport from '../screen/PromotionReport';
// import FloorScreen from '../screen/FloorScreen'
import { CommonStore } from '../store/commonStore';
import {
  getTransformForHeaderInsideNavigation,
  isOutletDisplay,
  isTablet,
  isTabletStatic,
} from '../util/common';
import FeedbackScreen from '../screen/FeedbackScreen';
import Guests from '../screen/Guests';
import CreateGuests from '../screen/CreateGuests';
import AnalyticReservationScreen from '../screen/AnalyticReservationScreen';
import NewSettingsScreen from '../screen/NewSettingsScreen';
import DetailsScreen from '../screen/DetailsScreen';
import NewLoyaltyCampaign from '../screen/NewLoyaltyCampaignScreen';
import LoyaltySignUpCampaign from '../screen/LoyaltySignUpCampaignScreen';
import LoyaltyPhone from '../screen/LoyaltyPhone';
import LoyaltyPayEarn from '../screen/LoyaltyPayEarn';
import LoyaltyReport from '../screen/LoyaltyReport';
import AiCampaignScreen from '../screen/AiCampaignScreen';
import VenueSettingsModalScreen from '../screen/VenueSettingsModalScreen';
import CalendarScreen from '../screen/CalendarScreen';
import DetailsGuestTags from '../screen/DetailsGuestTags';
import MainLoyaltySettingsScreen from '../screen/components/LoyaltySettingsScreen';
import GuestDetailsScreen from '../screen/GuestDetailsScreen';
import LoyaltySignUp from '../screen/LoyaltySignUp';
import ReportActivityLog from '../screen/ReportActivityLog';
import VenueSettingsTableSetupScreen from '../screen/VenueSettingsTableSetupScreen';
import VenueSettingsCombinationScreen from '../screen/VenueSettingsCombinationScreen';
import VenueSettingsReservationScreen from '../screen/VenueSettingsReservationScreen';
import TaggableVoucherListScreen from '../screen/TaggableVoucherListScreen';
import NewTaggableVoucherScreen from '../screen/NewTaggableVoucherScreen';
import EmployeeTimeSheet from '../screen/EmployeeTimeSheet';
import MenuOrderingScreen from '../screen/MenuOrderingScreen';
import MoMenuItemDetailsScreen from '../screen/MoMenuItemDetailsScreen';
import MoOutletMenuScreen from '../screen/MoOutletMenuScreen';
import MoTableScreen from '../screen/MoTableScreen';
import MoCartScreen from '../screen/MoCartScreen';
import SettingDepositScreen from '../screen/SettingDepositScreen';
import SettingIntervalScreen from '../screen/SettingIntervalScreen';
import { ORDER_TYPE } from '../constant/common';
import LoyaltyStampType from '../screen/LoyaltyStampType';
import NewLoyaltyStampType from '../screen/NewLoyaltyStampType';
import LoyaltyRewardRedemption from '../screen/LoyaltyRewardRedemption';
import TopupCreditTypeScreen from '../screen/TopupCreditTypeScreen';
import NewTopupCreditTypeScreen from '../screen/NewTopupCreditTypeScreen';
import LoyaltyTableScreen from '../screen/LoyaltyTableScreen';
import UpsellingList from '../screen/UpsellingListScreen';
import UpsellingCampaign from '../screen/UpsellingCampaignScreen';
import UpsellingReport from '../screen/UpsellingReport';
import ReportCategoryProduct from '../screen/ReportCategoryProduct';
import OrderDashboard from '../screen/OrderDashboard';
import OdOrderList from '../screen/OdOrderList';
import OdOrderDetails from '../screen/OdOrderDetails';
import SharedVariantAddOnScreen from '../screen/SharedVariantAddOnScreen';
import AiLoyaltyVoucherScreen from '../screen/AiLoyaltyVoucherScreen';
import SideBar from '../screen/SideBar';
import HomeScreen from '../screen/HomeScreen';
import ProfileScreen from '../screen/ProfileScreen';
import DashboardRA from '../screen/DashboardRA';
import FulfillmentAssistant from '../screen/FulfillmentAssistant';
import StockAdjustmentRA from '../screen/StockAdjustmentRA.js';
import RackStockAdjustment from '../screen/RackStockAdjustment';
import RackStockTransfer from '../screen/RackStockTransfer';

const Tab = createBottomTabNavigator();
// const Stack = createStackNavigator();
const Stack = createNativeStackNavigator();
// const MainStack = createStackNavigator();

const tabScreenOptions = ({ route }) => ({
  tabBarStyle: {
    display: 'none',
  },

  // headerTitleStyle: { color: Colors.whiteColor, marginLeft: Platform.OS == 'ios' ? Dimensions.get('screen').width * 0.05 : Dimensions.get('screen').width * 0.3, fontFamily: 'NunitoSans-Bold', fontSize: 32 },
  // headerTintColor: Colors.darkColor,
  // headerStyle: {
  //   backgroundColor: Colors.darkBgColor,
  //   elevation: 0,
  //   shadowOpacity: 0,
  //   shadowColor: '#000',
  //   shadowOffset: {
  //     width: 0,
  //     height: 1,
  //   },
  //   shadowOpacity: 0.22,
  //   shadowRadius: 3.22,
  //   elevation: 3,

  //   height: isTabletStatic() ? Dimensions.get('screen').height * 0.08 : fixedHeight * 0.1,
  // },
  // tabBarVisible: false,
});

/// //////////////////////////////////////////////////

function DashboardScreenStack() {
  const fixedHeight =
    Dimensions.get('window').height > Dimensions.get('window').width
      ? Dimensions.get('window').width
      : Dimensions.get('window').height;

  const { width: windowWidth, height: windowHeight } = useWindowDimensions();

  const headerOption = {
    headerTitleStyle: {
      color: Colors.whiteColor,
      marginLeft:
        Platform.OS === 'ios'
          ? Dimensions.get('window').width * 0.05
          : Dimensions.get('window').width * 0.3,
      fontFamily: 'NunitoSans-Bold',
      fontSize: 32,
    },
    headerTintColor: Colors.darkColor,
    headerStyle: {
      backgroundColor: Colors.darkBgColor,
      shadowColor: '#000',
      shadowOffset: {
        width: 0,
        height: 1,
      },
      shadowOpacity: 0.22,
      shadowRadius: 3.22,
      elevation: 3,

      height: isTablet
        ? Dimensions.get('window').height * 0.08
        : Platform.OS === 'ios'
          ? Dimensions.get('window').height * 0.15
          : fixedHeight * 0.17,
      // height: 0,

      // ...global.simulateTabletMode && {
      //   transform: [
      //     { scaleX: 0.42 },
      //     { scaleY: 0.42 },
      //     { translateX: -420 },
      //     { translateY: -40 },
      //   ],
      // },

      ...getTransformForHeaderInsideNavigation(),
    },
    tabBarVisible: false,

    // gestureDirection: 'horizontal',
    // transitionSpec: {
    //   open: TransitionSpecs.TransitionIOSSpec,
    //   close: TransitionSpecs.TransitionIOSSpec,
    // },
    // headerStyleInterpolator: HeaderStyleInterpolators.forFade,
    // cardStyleInterpolator: CardStyleInterpolators.forHorizontalIOS,
    // cardStyleInterpolator: ({ current, next, layouts }) => {
    //   return {
    //     cardStyle: {
    //       transform: [
    //         {
    //           translateX: current.progress.interpolate({
    //             inputRange: [0, 1],
    //             outputRange: [layouts.screen.width, 0],
    //           }),
    //         },
    //         {
    //           rotate: current.progress.interpolate({
    //             inputRange: [0, 1],
    //             outputRange: [1, 0],
    //           }),
    //         },
    //         {
    //           scale: next
    //             ? next.progress.interpolate({
    //               inputRange: [0, 1],
    //               outputRange: [1, 0.9],
    //             })
    //             : 1,
    //         },
    //       ],
    //     },
    //     overlayStyle: {
    //       opacity: current.progress.interpolate({
    //         inputRange: [0, 1],
    //         outputRange: [0, 0.5],
    //       }),
    //     },
    //   };
    // },
  };
  const isModalOpened = CommonStore.useState(s => s.isModalOpened);

  const getHeaderOptions = () => {
    if (isOutletDisplay()) {
      return { headerShown: false };
    } else {
      if (
        Platform.OS === 'ios'
        // true
      ) {
        return isModalOpened ? { headerShown: false } : headerOption;
      } else {
        return headerOption;
      }
    }
  };

  console.log('c-render - AppNavigator.js > DashboardScreenStack');

  return (
    <View
      style={{
        flexDirection: 'row',
        flex: 1,
        minHeight: '100%',
      }}>
      {/* <>
        {
          (isOutletDisplay()
            // || (isModalOpened && Platform.OS === 'ios')
          )
            ?
            <>
            </>
            :
            <View
              style={[
                !isTablet()
                  ? {
                    width: windowWidth * 0.08,
                  }
                  : {},
                // switchMerchant
                false
                  ? {
                    width: '10%',
                  }
                  : {},
                {
                  width: windowWidth * 0.08,
                },
              ]}>
              <SideBar
                // navigation={props.navigation}
                selectedTab={1}
                expandOperation
              />
            </View>
        }
      </> */}

      <Stack.Navigator>
        <Stack.Screen
          name="Assistant"
          component={DashboardRA}
          options={headerOption}
        />
        <Stack.Screen
          name="Home"
          component={HomeScreen}
          options={headerOption}
        />
        <Stack.Screen
          name="Profile"
          component={ProfileScreen}
          options={headerOption}
        />
        <Stack.Screen
          name="Dashboard"
          component={DashboardScreen}
          options={getHeaderOptions()}
        />
        <Stack.Screen
          name="Table"
          component={TableScreen}
          options={getHeaderOptions()}
        />
        <Stack.Screen
          name="Kitchen"
          component={KitchenScreen}
          options={getHeaderOptions()}
        />
        <Stack.Screen
          name="OutletMenu"
          component={OutletMenuScreen}
          options={getHeaderOptions()}
        />
        <Stack.Screen
          name="MenuItemDetails"
          component={MenuItemDetailsScreen}
          options={getHeaderOptions()}
        />
        <Stack.Screen
          name="Cart"
          component={CartScreen}
          options={getHeaderOptions()}
        />
        <Stack.Screen
          name="Order"
          component={OrderScreen}
          options={getHeaderOptions()}
        />
        <Stack.Screen
          name="Takeaway"
          component={TakeawayScreen}
          options={getHeaderOptions()}
        />
        <Stack.Screen
          name="OtherDelivery"
          component={OtherDeliveryScreen}
          options={getHeaderOptions()}
        />
        <Stack.Screen
          name="Queue"
          component={QueueScreen}
          options={getHeaderOptions()}
        />
        <Stack.Screen
          name="QueueConfig"
          component={QueueConfigScreen}
          options={getHeaderOptions()}
        />
        <Stack.Screen
          name="History"
          component={HistoryScreen}
          options={getHeaderOptions()}
        />
        <Stack.Screen
          name="PurchaseOrder"
          component={PurchaseOrderScreen}
          options={getHeaderOptions()}
        />
        <Stack.Screen
          name="StockInsert"
          component={StockInsertScreen}
          options={getHeaderOptions()}
        />
        <Stack.Screen
          name="Supplier"
          component={SupplierScreen}
          options={getHeaderOptions()}
        />
        <Stack.Screen
          name="SupplierProduct"
          component={SupplierProductScreen}
          options={getHeaderOptions()}
        />
        <Stack.Screen
          name="PurchaseOrderProduct"
          component={PurchaseOrderProductScreen}
          options={getHeaderOptions()}
        />
        <Stack.Screen
          name="ProductCategory"
          component={ProductCategoryScreen}
          options={getHeaderOptions()}
        />
        <Stack.Screen
          name="Catalog"
          component={Catalog}
          options={getHeaderOptions()}
        />
        <Stack.Screen
          name="VariantAddOn"
          component={VariantAddOnScreen}
          options={getHeaderOptions()}
        />
        <Stack.Screen
          name="SharedVariantAddOn"
          component={SharedVariantAddOnScreen}
          options={getHeaderOptions()}
        />
        <Stack.Screen
          name="PreorderPackage"
          component={PreorderPackageScreen}
          options={getHeaderOptions()}
        />
        <Stack.Screen
          name="CrmScreen"
          component={CrmScreen}
          options={getHeaderOptions()}
        />
        <Stack.Screen
          name="NewCustomer"
          component={NewCustomer}
          options={getHeaderOptions()}
        />
        <Stack.Screen
          name="CustomerReviewScreen"
          component={CustomerReviewScreen}
          options={getHeaderOptions()}
        />
        <Stack.Screen
          name="StockTake"
          component={StockTakeScreen}
          options={getHeaderOptions()}
        />
        <Stack.Screen
          name="StockTakeProduct"
          component={StockTakeProductScreen}
          options={getHeaderOptions()}
        />
        <Stack.Screen
          name="StockTransfer"
          component={StockTransferScreen}
          options={getHeaderOptions()}
        />
        <Stack.Screen
          name="StockTransferProduct"
          component={StockTransferProductScreen}
          options={getHeaderOptions()}
        />
        <Stack.Screen
          name="StockReturnProduct"
          component={StockReturnProductScreen}
          options={getHeaderOptions()}
        />
        <Stack.Screen
          name="AllTransaction"
          component={AllTransaction}
          options={getHeaderOptions()}
        />
        <Stack.Screen
          name="ReportSaleByShift"
          component={ReportSaleByShift}
          options={getHeaderOptions()}
        />
        <Stack.Screen
          name="ReportShiftPayInOut"
          component={ReportShiftPayInOut}
          options={getHeaderOptions()}
        />
        <Stack.Screen
          name="ReportSalesOvertime"
          component={ReportSalesOvertime}
          options={getHeaderOptions()}
        />
        <Stack.Screen
          name="ReportSalesRevisit"
          component={ReportSalesRevisit}
          options={getHeaderOptions()}
        />
        <Stack.Screen
          name="LoyaltySettings"
          component={MainLoyaltySettingsScreen}
          options={getHeaderOptions()}
        />
        <Stack.Screen
          name="Reservation"
          component={ReservationScreen}
          options={getHeaderOptions()}
        />
        <Stack.Screen
          name="Product"
          component={ProductScreen}
          options={getHeaderOptions()}
        />
        <Stack.Screen
          name="ProductAdd"
          component={ProductAddScreen}
          options={getHeaderOptions()}
        />
        <Stack.Screen
          name="ProductMenu"
          component={ProductMenuScreen}
          options={getHeaderOptions()}
        />
        <Stack.Screen
          name="Setting"
          component={SettingScreen}
          options={getHeaderOptions()}
        />
        <Stack.Screen
          name="PinLogin"
          component={PinLogin}
          options={getHeaderOptions()}
        />
        <Stack.Screen
          name="SettingShift"
          component={SettingShiftScreen}
          options={getHeaderOptions()}
        />
        <Stack.Screen
          name="SettingTax"
          component={SettingTaxScreen}
          options={getHeaderOptions()}
        />
        <Stack.Screen
          name="SettingReceipt"
          component={SettingReceiptScreen}
          options={getHeaderOptions()}
        />
        <Stack.Screen
          name="SettingRedemption"
          component={SettingRedemptionScreen}
          options={getHeaderOptions()}
        />
        <Stack.Screen
          name="SettingOrder"
          component={SettingOrderScreen}
          options={getHeaderOptions()}
        />
        <Stack.Screen
          name="VenueSettingsTableSetupScreen"
          component={VenueSettingsTableSetupScreen}
          options={getHeaderOptions()}
        />
        <Stack.Screen
          name="VenueSettingsCombinationScreen"
          component={VenueSettingsCombinationScreen}
          options={getHeaderOptions()}
        />
        <Stack.Screen
          name="VenueSettingsReservationScreen"
          component={VenueSettingsReservationScreen}
          options={getHeaderOptions()}
        />
        <Stack.Screen
          name="Employee"
          component={EmployeeScreen}
          options={getHeaderOptions()}
        />
        <Stack.Screen
          name="Voucher"
          component={VoucherScreen}
          options={getHeaderOptions()}
        />
        <Stack.Screen
          name="PromotionList"
          component={PromotionListScreen}
          options={getHeaderOptions()}
        />
        <Stack.Screen
          name="AutoPushNotification"
          component={AutoPushNotificationScreen}
          options={getHeaderOptions()}
        />
        <Stack.Screen
          name="ManualPushNotification"
          component={ManualPushNotificationScreen}
          options={getHeaderOptions()}
        />
        <Stack.Screen
          name="LocationBasedNotification"
          component={LocationBasedNotificationScreen}
          options={getHeaderOptions()}
        />
        <Stack.Screen
          name="Redemption"
          component={RedemptionScreen}
          options={getHeaderOptions()}
        />
        <Stack.Screen
          name="RedemptionExpired"
          component={RedemptionExpiredScreen}
          options={getHeaderOptions()}
        />
        <Stack.Screen
          name="RedemptionRedeemed"
          component={RedemptionRedeemedScreen}
          options={getHeaderOptions()}
        />
        <Stack.Screen
          name="Inventory"
          component={InventoryScreen}
          options={getHeaderOptions()}
        />
        <Stack.Screen
          name="InventoryProduct"
          component={InventoryProductScreen}
          options={getHeaderOptions()}
        />
        <Stack.Screen
          name="ReportSalesVariant"
          component={ReportSalesVariant}
          options={getHeaderOptions()}
        />
        <Stack.Screen
          name="ReportSalesAddOns"
          component={ReportSalesAddOns}
          options={getHeaderOptions()}
        />
        <Stack.Screen
          name="ReportSalesTransaction"
          component={ReportSalesTransaction}
          options={getHeaderOptions()}
        />
        <Stack.Screen
          name="ReportSalesPaymentMethod"
          component={ReportSalesPaymentMethod}
          options={getHeaderOptions()}
        />
        <Stack.Screen
          name="ReportSalesUpselling"
          component={ReportSalesUpselling}
          options={getHeaderOptions()}
        />
        <Stack.Screen
          name="ReportSalesRefund"
          component={ReportSalesRefund}
          options={getHeaderOptions()}
        />
        <Stack.Screen
          name="ReportSalesAov"
          component={ReportSalesAov}
          options={getHeaderOptions()}
        />
        <Stack.Screen
          name="ReportSalesOrderCount"
          component={ReportSalesOrderCount}
          options={getHeaderOptions()}
        />
        <Stack.Screen
          name="ReportSalesRevisitCount"
          component={ReportSalesRevisitCount}
          options={getHeaderOptions()}
        />
        <Stack.Screen
          name="ReportCategoryProduct"
          component={ReportCategoryProduct}
          options={getHeaderOptions()}
        />
        <Stack.Screen
          name="SettingE-indemnityForm"
          component={SettingEindemnityForm}
          options={getHeaderOptions()}
        />
        <Stack.Screen
          name="SettingCredit"
          component={SettingCredit}
          options={getHeaderOptions()}
        />
        <Stack.Screen
          name="SettingSetCredit"
          component={SettingSetCredit}
          options={getHeaderOptions()}
        />
        <Stack.Screen
          name="SettingBooking"
          component={SettingBooking}
          options={getHeaderOptions()}
        />
        <Stack.Screen
          name="SettingQuestionaire"
          component={SettingQuestionaire}
          options={getHeaderOptions()}
        />
        <Stack.Screen
          name="SettingNotification"
          component={SettingNotification}
          options={getHeaderOptions()}
        />
        <Stack.Screen
          name="NewCampaign"
          component={NewCampaignScreen}
          options={getHeaderOptions()}
        />
        <Stack.Screen
          name="SettingQuestionairePreview"
          component={SettingQuestionairePreview}
          options={getHeaderOptions()}
        />
        <Stack.Screen
          name="LoyaltyPointsRate"
          component={LoyaltyPointsRate}
          options={getHeaderOptions()}
        />
        <Stack.Screen
          name="SegmentScreen"
          component={SegmentScreen}
          options={getHeaderOptions()}
        />
        <Stack.Screen
          name="LoyaltyStampScreen"
          component={LoyaltyStampScreen}
          options={getHeaderOptions()}
        />
        <Stack.Screen
          name="GlobalCreditScreen"
          component={GlobalCreditScreen}
          options={getHeaderOptions()}
        />
        <Stack.Screen
          name="Campaign"
          component={Campaign}
          options={getHeaderOptions()}
        />
        <Stack.Screen
          name="InventoryDetail"
          component={InventoryDetail}
          options={getHeaderOptions()}
        />
        <Stack.Screen
          name="VoucherReport"
          component={VoucherReport}
          options={getHeaderOptions()}
        />
        <Stack.Screen
          name="TaggableVoucherReport"
          component={TaggableVoucherReport}
          options={getHeaderOptions()}
        />
        <Stack.Screen
          name="TopUpCreditReport"
          component={TopUpCreditReport}
          options={getHeaderOptions()}
        />
        <Stack.Screen
          name="PromotionReport"
          component={PromotionReport}
          options={getHeaderOptions()}
        />
        <Stack.Screen
          name="SettingPrinter"
          component={SettingPrinterScreen}
          options={getHeaderOptions()}
        />
        <Stack.Screen
          name="SettingPayment"
          component={SettingPaymentScreen}
          options={getHeaderOptions()}
        />
        <Stack.Screen
          name="SettingGrab"
          component={SettingGrabScreen}
          options={getHeaderOptions()}
        />
        <Stack.Screen
          name="SettingReservation"
          component={SettingReservationScreen}
          options={getHeaderOptions()}
        />
        <Stack.Screen
          name="SettingLoyalty"
          component={SettingLoyaltyScreen}
          options={getHeaderOptions()}
        />
        <Stack.Screen
          name="FeedbackScreen"
          component={FeedbackScreen}
          options={getHeaderOptions()}
        />
        <Stack.Screen
          name="Guests"
          component={Guests}
          options={getHeaderOptions()}
        />
        <Stack.Screen
          name="CreateGuests"
          component={CreateGuests}
          options={getHeaderOptions()}
        />
        <Stack.Screen
          name="AnalyticReservationScreen"
          component={AnalyticReservationScreen}
          options={getHeaderOptions()}
        />
        <Stack.Screen
          name="NewSettingsScreen"
          component={NewSettingsScreen}
          options={getHeaderOptions()}
        />
        <Stack.Screen
          name="LoyaltySettingsScreen"
          component={MainLoyaltySettingsScreen}
          options={getHeaderOptions()}
        />
        <Stack.Screen
          name="DetailsScreen"
          component={DetailsScreen}
          options={getHeaderOptions()}
        />
        <Stack.Screen
          name="NewLoyaltyCampaign"
          component={NewLoyaltyCampaign}
          options={getHeaderOptions()}
        />
        <Stack.Screen
          name="LoyaltySignUpCampaign"
          component={LoyaltySignUpCampaign}
          options={getHeaderOptions()}
        />
        <Stack.Screen
          name="UpsellingList"
          component={UpsellingList}
          options={getHeaderOptions()}
        />
        <Stack.Screen
          name="UpsellingCampaign"
          component={UpsellingCampaign}
          options={getHeaderOptions()}
        />
        <Stack.Screen
          name="UpsellingReport"
          component={UpsellingReport}
          options={getHeaderOptions()}
        />
        <Stack.Screen
          name="VenueSettingsModalScreen"
          component={VenueSettingsModalScreen}
          options={getHeaderOptions()}
        />
        <Stack.Screen
          name="LoyaltyPhone"
          component={LoyaltyPhone}
          options={getHeaderOptions()}
        />
        <Stack.Screen
          name="LoyaltyPayEarn"
          component={LoyaltyPayEarn}
          options={getHeaderOptions()}
        />
        <Stack.Screen
          name="LoyaltyReport"
          component={LoyaltyReport}
          options={getHeaderOptions()}
        />
        <Stack.Screen
          name="AiCampaignScreen"
          component={AiCampaignScreen}
          options={getHeaderOptions()}
        />
        <Stack.Screen
          name="AiLoyaltyVoucherScreen"
          component={AiLoyaltyVoucherScreen}
          options={getHeaderOptions()}
        />
        <Stack.Screen
          name="CalendarScreen"
          component={CalendarScreen}
          options={getHeaderOptions()}
        />
        <Stack.Screen
          name="GuestDetailsScreen"
          component={GuestDetailsScreen}
          options={getHeaderOptions()}
        />
        <Stack.Screen
          name="DetailsGuestTags"
          component={DetailsGuestTags}
          options={getHeaderOptions()}
        />
        <Stack.Screen
          name="LoyaltySignUp"
          component={LoyaltySignUp}
          options={getHeaderOptions()}
        />
        <Stack.Screen
          name="ReportActivityLog"
          component={ReportActivityLog}
          options={getHeaderOptions()}
        />
        <Stack.Screen
          name="TaggableVoucherList"
          component={TaggableVoucherListScreen}
          options={getHeaderOptions()}
        />
        <Stack.Screen
          name="NewTaggableVoucher"
          component={NewTaggableVoucherScreen}
          options={getHeaderOptions()}
        />
        <Stack.Screen
          name="EmployeeTimeSheet"
          component={EmployeeTimeSheet}
          options={getHeaderOptions()}
        />
        <Stack.Screen
          name="MenuOrderingScreen"
          component={MenuOrderingScreen}
          options={getHeaderOptions()}
        />
        <Stack.Screen
          name="MoTable"
          component={MoTableScreen}
          options={getHeaderOptions()}
        />
        <Stack.Screen
          name="MoOutletMenu"
          component={MoOutletMenuScreen}
          options={getHeaderOptions()}
        />
        <Stack.Screen
          name="MoMenuItemDetails"
          component={MoMenuItemDetailsScreen}
          options={getHeaderOptions()}
        />
        <Stack.Screen
          name="MoCart"
          component={MoCartScreen}
          options={getHeaderOptions()}
        />
        <Stack.Screen
          name="SettingDeposit"
          component={SettingDepositScreen}
          options={getHeaderOptions()}
        />
        <Stack.Screen
          name="SettingInterval"
          component={SettingIntervalScreen}
          options={getHeaderOptions()}
        />
        <Stack.Screen
          name="LoyaltyStampType"
          component={LoyaltyStampType}
          options={getHeaderOptions()}
        />
        <Stack.Screen
          name="NewLoyaltyStampType"
          component={NewLoyaltyStampType}
          options={getHeaderOptions()}
        />
        <Stack.Screen
          name="LoyaltyRewardRedemption"
          component={LoyaltyRewardRedemption}
          options={getHeaderOptions()}
        />
        <Stack.Screen
          name="TopupCreditType"
          component={TopupCreditTypeScreen}
          options={getHeaderOptions()}
        />
        <Stack.Screen
          name="NewTopupCreditType"
          component={NewTopupCreditTypeScreen}
          options={getHeaderOptions()}
        />
        <Stack.Screen
          name="LoyaltyTable"
          component={LoyaltyTableScreen}
          options={getHeaderOptions()}
        />
        <Stack.Screen
          name="OrderDashboard"
          component={OrderDashboard}
          options={getHeaderOptions()}
        />
        <Stack.Screen
          name="OdOrderList"
          component={OdOrderList}
          options={getHeaderOptions()}
        />
        <Stack.Screen
          name="OdOrderDetails"
          component={OdOrderDetails}
          options={getHeaderOptions()}
        />


        <Stack.Screen
          name="StockAdjustment"
          component={StockAdjustmentRA}
          options={getHeaderOptions()}
        />

        <Stack.Screen
          name="RackStockAdjustment"
          component={RackStockAdjustment}
          options={headerOption}
        />

        <Stack.Screen
          name="RackStockTransfer"
          component={RackStockTransfer}
          options={headerOption}
        />

        <Stack.Screen
          name="FulfillmentAssistant"
          component={FulfillmentAssistant}
          options={{ title: "Fulfillment Assistant - KooDoo Assistant" }}
        />

      </Stack.Navigator>
    </View>
  );
}

/// //////////////////////////////////////////////////

export default function App() {
  const navigationRef = React.useRef(null);

  // commented to improve performance
  // const pageStack = CommonStore.useState(s => s.pageStack);

  // const simulateTabletMode = CommonStore.useState(s => s.simulateTabletMode);

  console.log('c-render - AppNavigator.js > App');

  return (
    // <TabletScreenSwitcher
    //   hideButton
    //   landscapeMode
    //   // {...panResponder.panHandlers}
    // >
    <NavigationContainer
      ref={navigationRef}
    // onStateChange={async () => {
    //   if (navigationRef && navigationRef.current && navigationRef.current.getCurrentRoute) {
    //     CommonStore.update(s => {
    //       s.pageStack = [
    //         ...pageStack,
    //         navigationRef.current.getCurrentRoute().name,
    //       ];
    //     });

    //     await analytics().logScreenView({
    //       screen_name: navigationRef.current.getCurrentRoute().name,
    //       screen_class: navigationRef.current.getCurrentRoute().name,
    //     });
    //   }
    // }}
    >
      {/* <MainStack.Navigator
    screenOptions={({ route }) => ({
      headerShown: false,
    })}>
    <MainStack.Screen name="Dashboard" component={DashboardScreen} />
    <MainStack.Screen name="Kitchen" component={KitchenScreen} />
    <MainStack.Screen name="Table" component={TableScreen} />
    <MainStack.Screen name="Order" component={OrderScreen} />
    <MainStack.Screen name="Takeaway" component={TakeawayScreen} />
    <MainStack.Screen name="Reservation" component={ReservationScreen} />
    <MainStack.Screen name="Queue" component={QueueScreen} />
    <MainStack.Screen name="Product" component={ProductScreen} />
    <MainStack.Screen name="ProductAdd" component={ProductAddScreen} />
    <MainStack.Screen name="ProductMenu" component={ProductMenuScreen} />
    <MainStack.Screen name="History" component={HistoryScreen} />
    <MainStack.Screen name="Setting" component={SettingScreen} />
    <MainStack.Screen name="SettingShift" component={SettingShiftScreen} />
    <MainStack.Screen name="SettingTax" component={SettingTaxScreen} />
    <MainStack.Screen name="SettingReceipt" component={SettingReceiptScreen} />
    <MainStack.Screen name="SettingRedemption" component={SettingRedemptionScreen} />
    <MainStack.Screen name="SettingOrder" component={SettingOrderScreen} />
    <MainStack.Screen name="Employee" component={EmployeeScreen} />
    <MainStack.Screen name="Voucher" component={VoucherScreen} />
    <MainStack.Screen name="PromotionList" component={PromotionListScreen} />
    <MainStack.Screen name="AutoPushNotification" component={AutoPushNotificationScreen} />
    <MainStack.Screen name="ManualPushNotification" component={ManualPushNotificationScreen} />
    <MainStack.Screen name="LocationBasedNotification" component={LocationBasedNotificationScreen} />
    <MainStack.Screen name="Inventory" component={InventoryScreen} />
    <MainStack.Screen name="PurchaseOrder" component={PurchaseOrderScreen} />
    <MainStack.Screen name="StockInsert" component={StockInsertScreen} />
    <MainStack.Screen name="Supplier" component={SupplierScreen} />
    <MainStack.Screen name="ProductCategory" component={ProductCategoryScreen} />
    <MainStack.Screen name="PreorderPackage" component={PreorderPackageScreen} />
    <MainStack.Screen name="CrmScreen" component={CrmScreen} />
    <MainStack.Screen name="NewCustomer" component={NewCustomer} />
    <MainStack.Screen name="SegmentScreen" component={SegmentScreen} />
    <MainStack.Screen name="StockTransfer" component={StockTransferScreen} />
    <MainStack.Screen name="StockTake" component={StockTakeScreen} />
    <MainStack.Screen name="Redemption" component={RedemptionScreen} />
    <MainStack.Screen name="RedemptionExpired" component={RedemptionExpiredScreen} />
    <MainStack.Screen name="RedemptionRedeemed" component={RedemptionRedeemedScreen} />
    <MainStack.Screen name="ReportSalesCategory" component={ReportSalesCategory} />
    <MainStack.Screen name="ReportSalesVariant" component={ReportSalesVariant} />
    <MainStack.Screen name="ReportSalesAddOns" component={ReportSalesAddOns} />
    <MainStack.Screen name="ReportSalesSKU" component={ReportSalesSKU} />
    <MainStack.Screen name="ReportSalesTransaction" component={ReportSalesTransaction} />
    <MainStack.Screen name="ReportSalesPaymentMethod" component={ReportSalesPaymentMethod} />
    <MainStack.Screen name="ReportSalesByRedemption" component={ReportSalesByRedemption} />
    <MainStack.Screen name="AllTransaction" component={AllTransaction} />
    <MainStack.Screen name="ReportStockValue" component={ReportStockValue} />
    <MainStack.Screen name="ReportSaleByShift" component={ReportSaleByShift} />
    <MainStack.Screen name="ReportSalesOvertime" component={ReportSalesOvertime} />
    <MainStack.Screen name="ReportSalesProduct" component={ReportSalesProduct} />
    <MainStack.Screen name="SettingE-indemnityForm" component={SettingEindemnityForm} />
    <MainStack.Screen name="SettingCredit" component={SettingCredit} />
    <MainStack.Screen name="SettingSetCredit" component={SettingSetCredit} />
    <MainStack.Screen name="SettingBooking" component={SettingBooking} />
    <MainStack.Screen name="SettingQuestionaire" component={SettingQuestionaire} />
    <MainStack.Screen name="SettingNotification" component={SettingNotification} />
    <MainStack.Screen name="NewCampaign" component={NewCampaignScreen} />
    <MainStack.Screen name="SettingQuestionairePreview" component={SettingQuestionairePreview} />
    <MainStack.Screen name="LoyaltyPointsRate" component={LoyaltyPointsRate} />
    <MainStack.Screen name="LoyaltyStampScreen" component={LoyaltyStampScreen} />
    <MainStack.Screen name="GlobalCreditScreen" component={GlobalCreditScreen} />
    <MainStack.Screen name="Campaign" component={Campaign} />
    <MainStack.Screen name="InventoryDetail" component={InventoryDetail} />
    <MainStack.Screen name="VoucherReport" component={VoucherReport} />
    <MainStack.Screen name="PromotionReport" component={PromotionReport} />
    <MainStack.Screen name="SettingPrinter" component={SettingPrinterScreen} />
  </MainStack.Navigator> */}

      <Tab.Navigator
        screenOptions={({ route }) => ({
          // tabBarVisible: route.state
          //   ? route.state.index > 0
          //     ? false
          //     : true
          //   : null,
          // tabBarVisible: true,
          tabBarIcon: ({ focused, color, size }) => {
            let iconName;

            if (route.name === 'Kitchen') {
              iconName = 'hamburger';
            } else if (route.name === 'Table') {
              iconName = 'th';
            } else if (route.name === 'Order') {
              iconName = 'sticky-note';
            } else if (route.name === 'Takeaway') {
              iconName = 'shopping-basket';
            } else if (route.name === 'Reservation') {
              iconName = 'users';
            } else if (route.name === 'Queue') {
              iconName = 'user-friends';
            } else if (route.name === 'Product') {
              iconName = 'apple-alt';
            } else if (route.name === 'History') {
              iconName = 'apple-alt';
            } else if (route.name === 'Employee') {
              iconName = 'apple-alt';
            } else if (route.name === 'Voucher') {
              iconName = 'apple-alt';
            } else if (route.name === 'Inventory') {
              iconName = 'apple-alt';
            } else if (route.name === 'AllTransaction') {
              iconName = 'apple-alt';
            }
            // else if (route.name === 'ReportStockValue') {
            //   iconName = 'apple-alt';
            // }
            else if (route.name === 'ReportSaleByShift') {
              iconName = 'apple-alt';
            } else if (route.name === 'ReportSalesOvertime') {
              iconName = 'apple-alt';
            } else if (route.name === 'ProductAdd') {
              iconName = 'apple-alt';
            } else if (route.name === 'ProductMenu') {
              iconName = 'apple-alt';
            }
            // You can return any component that you like here!
            return <Icon name={iconName} size={22} color={color} />;
          },
          headerShown: false,
          // tabBarVisible: false,
          tabBarStyle: {
            display: 'none',
          },

          tabBarActiveTintColor: Colors.primaryColor,
          // inactiveTintColor: '#ffffff',
          tabBarLabelStyle: {
            fontWeight: 'bold',
          },
          tabBarShowLabel: true,
        })}
        tabBarOptions={{
          activeTintColor: Colors.primaryColor,
          // inactiveTintColor: '#ffffff',
          labelStyle: {
            fontWeight: 'bold',
          },
          showLabel: true,
          style: {
            // backgroundColor: Colors.mainTxtColor,
            // borderTopWidth: 0,
          },
        }}>
        <Tab.Screen
          options={tabScreenOptions}
          name="Home"
          component={DashboardScreenStack}
        />

        {/* <Tab.Screen options={tabScreenOptions} name="WholeApp" component={WholeAppStack} />         */}

        {/* <Tab.Screen options={tabScreenOptions} name="Kitchen" component={KitchenScreenStack} />
    <Tab.Screen options={tabScreenOptions} name="Table" component={TableScreenStack} />
    <Tab.Screen options={tabScreenOptions} name="Order" component={OrderScreenStack} />
    <Tab.Screen options={tabScreenOptions} name="Takeaway" component={TakeawayScreenStack} />
    <Tab.Screen options={tabScreenOptions} name="OtherDelivery" component={OtherDeliveryScreenStack} />
    <Tab.Screen options={tabScreenOptions} name="Queue" component={QueueScreenStack} />
    <Tab.Screen options={tabScreenOptions} name="History" component={HistoryScreenStack} />

    <Tab.Screen options={tabScreenOptions} name="MenuOrderingScreen" component={MenuOrderingScreenStack} />
    <Tab.Screen options={tabScreenOptions} name="MoTable" component={MenuOrderingScreenStack} />
    <Tab.Screen options={tabScreenOptions} name="MoOutletMenu" component={MenuOrderingScreenStack} />
    <Tab.Screen options={tabScreenOptions} name="MoMenuItemDetails" component={MenuOrderingScreenStack} />
    <Tab.Screen options={tabScreenOptions} name="MoCart" component={MenuOrderingScreenStack} />

    <Tab.Screen options={tabScreenOptions} name="Reservation" component={ReservationScreenStack} />

    <Tab.Screen options={tabScreenOptions} name="Product" component={ProductScreenStack} />
    <Tab.Screen options={tabScreenOptions} name="ProductAdd" component={ProductAddScreenStack} />
    <Tab.Screen options={tabScreenOptions} name="ProductMenu" component={ProductMenuScreenStack} />
    <Tab.Screen options={tabScreenOptions} name="Setting" component={SettingScreenStack} />
    <Tab.Screen options={tabScreenOptions} name="SettingShift" component={SettingShiftScreenStack} />
    <Tab.Screen options={tabScreenOptions} name="SettingTax" component={SettingTaxScreenStack} />
    <Tab.Screen options={tabScreenOptions} name="SettingReceipt" component={SettingReceiptScreenStack} />
    <Tab.Screen options={tabScreenOptions} name="SettingRedemption" component={SettingRedemptionScreenStack} />
    <Tab.Screen options={tabScreenOptions} name="SettingOrder" component={SettingOrderScreenStack} />
    <Tab.Screen options={tabScreenOptions} name="Employee" component={EmployeeScreenStack} />
    <Tab.Screen options={tabScreenOptions} name="Voucher" component={VoucherScreenStack} />
    <Tab.Screen options={tabScreenOptions} name="PromotionList" component={PromotionListScreenStack} />
    <Tab.Screen options={tabScreenOptions} name="AutoPushNotification" component={AutoPushNotificationScreenStack} />
    <Tab.Screen options={tabScreenOptions} name="ManualPushNotification" component={ManualPushNotificationScreenStack} />
    <Tab.Screen options={tabScreenOptions} name="LocationBasedNotification" component={LocationBasedNotificationScreenStack} />
    <Tab.Screen options={tabScreenOptions} name="Inventory" component={InventoryScreenStack} />
    <Tab.Screen options={tabScreenOptions} name="InventoryProduct" component={InventoryProductScreenStack} />
    <Tab.Screen options={tabScreenOptions} name="PurchaseOrder" component={PurchaseOrderScreenStack} />
    <Tab.Screen options={tabScreenOptions} name="StockInsert" component={StockInsertScreenStack} />
    <Tab.Screen options={tabScreenOptions} name="Supplier" component={SupplierScreenStack} />
    <Tab.Screen options={tabScreenOptions} name="SupplierProduct" component={SupplierProductScreenStack} />
    <Tab.Screen options={tabScreenOptions} name="PurchaseOrderProduct" component={PurchaseOrderProductScreenStack} />
    <Tab.Screen options={tabScreenOptions} name="ProductCategory" component={ProductCategoryScreenStack} />
    <Tab.Screen options={tabScreenOptions} name="PreorderPackage" component={PreorderPackageScreenStack} />
    <Tab.Screen options={tabScreenOptions} name="CrmScreen" component={CrmScreenStack} />
    <Tab.Screen options={tabScreenOptions} name="NewCustomer" component={NewCustomerScreenStack} />
    <Tab.Screen options={tabScreenOptions} name="SegmentScreen" component={SegmentScreenStack} />
    <Tab.Screen options={tabScreenOptions} name="StockTransfer" component={StockTransferScreenStack} />
    <Tab.Screen options={tabScreenOptions} name="StockTransferProduct" component={StockTransferProductScreenStack} />
    <Tab.Screen options={tabScreenOptions} name="StockTake" component={StockTakeScreenStack} />
    <Tab.Screen options={tabScreenOptions} name="StockTakeProduct" component={StockTakeProductScreenStack} />
    <Tab.Screen options={tabScreenOptions} name="StockReturnProduct" component={StockReturnProductScreenStack} />
    <Tab.Screen options={tabScreenOptions} name="Redemption" component={RedemptionScreenStack} />
    <Tab.Screen options={tabScreenOptions} name="RedemptionExpired" component={RedemptionExpiredScreenStack} />
    <Tab.Screen options={tabScreenOptions} name="RedemptionRedeemed" component={RedemptionRedeemedScreenStack} />
    <Tab.Screen options={tabScreenOptions} name="ReportSalesCategory" component={ReportSalesCategoryStack} />
    <Tab.Screen options={tabScreenOptions} name="ReportSalesVariant" component={ReportSalesVariantStack} />
    <Tab.Screen options={tabScreenOptions} name="ReportSalesAddOns" component={ReportSalesAddOnsStack} />
    <Tab.Screen options={tabScreenOptions} name="ReportSalesSKU" component={ReportSalesSKUStack} />
    <Tab.Screen options={tabScreenOptions} name="ReportSalesTransaction" component={ReportSalesTransactionStack} />
    <Tab.Screen options={tabScreenOptions} name="ReportSalesPaymentMethod" component={ReportSalesPaymentMethodStack} />
    <Tab.Screen options={tabScreenOptions} name="ReportSalesByRedemption" component={ReportSalesByRedemptionStack} />
    <Tab.Screen options={tabScreenOptions} name="AllTransaction" component={AllTransactionStack} />
    <Tab.Screen options={tabScreenOptions} name="ReportStockValue" component={ReportStockValueStack} />
    <Tab.Screen options={tabScreenOptions} name="ReportSaleByShift" component={ReportSaleByShiftStack} />
    <Tab.Screen options={tabScreenOptions} name="ReportShiftPayInOut" component={ReportShiftPayInOutStack} />
    <Tab.Screen options={tabScreenOptions} name="ReportSalesOvertime" component={ReportSalesOvertimeStack} />
    <Tab.Screen options={tabScreenOptions} name="ReportSalesProduct" component={ReportSalesProductStack} />
    <Tab.Screen options={tabScreenOptions} name="ReportSalesRefund" component={ReportSalesRefundStack} />
    <Tab.Screen options={tabScreenOptions} name="SettingE-indemnityForm" component={SettingEindemnityFormStack} />
    <Tab.Screen options={tabScreenOptions} name="SettingCredit" component={SettingCreditStack} />
    <Tab.Screen options={tabScreenOptions} name="SettingSetCredit" component={SettingSetCreditStack} />
    <Tab.Screen options={tabScreenOptions} name="SettingBooking" component={SettingBookingStack} />
    <Tab.Screen options={tabScreenOptions} name="SettingQuestionaire" component={SettingQuestionaireStack} />
    <Tab.Screen options={tabScreenOptions} name="SettingNotification" component={SettingNotificationStack} />
    <Tab.Screen options={tabScreenOptions} name="NewCampaign" component={NewCampaignScreenStack} />
    <Tab.Screen options={tabScreenOptions} name="SettingQuestionairePreview" component={SettingQuestionairePreviewStack} />
    <Tab.Screen options={tabScreenOptions} name="LoyaltyPointsRate" component={LoyaltyPointsRateStack} />
    <Tab.Screen options={tabScreenOptions} name="LoyaltyStampScreen" component={LoyaltyStampScreenStack} />
    <Tab.Screen options={tabScreenOptions} name="GlobalCreditScreen" component={GlobalCreditScreenStack} />
    <Tab.Screen options={tabScreenOptions} name="Campaign" component={CampaignStack} />
    <Tab.Screen options={tabScreenOptions} name="InventoryDetail" component={InventoryDetailStack} />
    <Tab.Screen options={tabScreenOptions} name="VoucherReport" component={VoucherReportStack} />
    <Tab.Screen options={tabScreenOptions} name="TaggableVoucherReport" component={TaggableVoucherReportStack} />
    <Tab.Screen options={tabScreenOptions} name="PromotionReport" component={PromotionReportStack} />
    <Tab.Screen options={tabScreenOptions} name="FloorScreen" component={FloorScreenStack} />
    <Tab.Screen options={tabScreenOptions} name="SettingPrinter" component={SettingPrinterStack} />
    <Tab.Screen options={tabScreenOptions} name="SettingPayment" component={SettingPaymentStack} />
    <Tab.Screen options={tabScreenOptions} name="SettingReservation" component={SettingReservationStack} />
    <Tab.Screen options={tabScreenOptions} name="SettingLoyalty" component={SettingLoyaltyStack} />
    <Tab.Screen options={tabScreenOptions} name="FeedbackScreen" component={FeedbackScreenStack} />
    <Tab.Screen options={tabScreenOptions} name="Guests" component={GuestsStack} />
    <Tab.Screen options={tabScreenOptions} name="CreateGuests" component={CreateGuestsStack} />
    <Tab.Screen options={tabScreenOptions} name="DetailsScreen" component={DetailsScreenStack} />
    <Tab.Screen options={tabScreenOptions} name="AnalyticReservationScreen" component={AnalyticReservationScreenStack} />
    <Tab.Screen options={tabScreenOptions} name="NewSettingsScreen" component={NewSettingsScreenStack} />
    <Tab.Screen options={tabScreenOptions} name="LoyaltySettingsScreen" component={LoyaltySettingsScreenStack} />

    <Tab.Screen options={tabScreenOptions} name="NewLoyaltyCampaign" component={NewLoyaltyCampaignStack} />
    <Tab.Screen options={tabScreenOptions} name="LoyaltySignUpCampaign" component={LoyaltySignUpCampaignStack} />
    <Tab.Screen options={tabScreenOptions} name="LoyaltyPhone" component={LoyaltyPhoneStack} />
    <Tab.Screen options={tabScreenOptions} name="LoyaltyPayEarn" component={LoyaltyPayEarnStack} />
    <Tab.Screen options={tabScreenOptions} name="LoyaltyReport" component={LoyaltyReportStack} />

    <Tab.Screen options={tabScreenOptions} name="VenueSettingsModalScreen" component={VenueSettingsModalScreenStack} />
    <Tab.Screen options={tabScreenOptions} name="VenueSettingsTableSetupScreen" component={VenueSettingsTableSetupScreenStack} />
    <Tab.Screen options={tabScreenOptions} name="VenueSettingsCombinationScreen" component={VenueSettingsCombinationScreenStack} />
    <Tab.Screen options={tabScreenOptions} name="VenueSettingsReservationScreen" component={VenueSettingsReservationScreenStack} />
    <Tab.Screen options={tabScreenOptions} name="CalendarScreen" component={CalendarScreenStack} />

    <Tab.Screen options={tabScreenOptions} name="DetailsGuestTags" component={DetailsGuestTagsStack} />
    <Tab.Screen options={tabScreenOptions} name="GuestDetailsScreen" component={GuestDetailsScreenStack} />
    <Tab.Screen options={tabScreenOptions} name="LoyaltySignUp" component={LoyaltySignUpStack} />

    <Tab.Screen options={tabScreenOptions} name="ReportActivityLog" component={ReportActivityLogStack} />
    <Tab.Screen options={tabScreenOptions} name='EmployeeTimeSheet' component={EmployeeTimeSheetStack} />

    <Tab.Screen options={tabScreenOptions} name="TaggableVoucherList" component={TaggableVoucherListScreenStack} />
    <Tab.Screen options={tabScreenOptions} name="NewTaggableVoucher" component={NewTaggableVoucherScreenStack} />

    <Tab.Screen options={tabScreenOptions} name="SettingDepositScreen" component={SettingDepositScreenStack} />
    <Tab.Screen options={tabScreenOptions} name="SettingIntervalScreen" component={SettingIntervalScreenStack} />
    <Tab.Screen options={tabScreenOptions} name="LoyaltyStampType" component={LoyaltyStampTypeStack} />
    <Tab.Screen options={tabScreenOptions} name="NewLoyaltyStampType" component={NewLoyaltyStampTypeStack} />

    <Tab.Screen options={tabScreenOptions} name="LoyaltyRewardRedemption" component={LoyaltyRewardRedemptionStack} />

    <Tab.Screen options={tabScreenOptions} name="TopupCreditType" component={TopupCreditTypeStack} />
    <Tab.Screen options={tabScreenOptions} name="NewTopupCreditType" component={NewTopupCreditTypeStack} />

    <Tab.Screen options={tabScreenOptions} name="TopUpCreditReport" component={TopUpCreditReportStack} />

    <Tab.Screen options={tabScreenOptions} name="LoyaltyTable" component={LoyaltyTableStack} /> */}
      </Tab.Navigator>
    </NavigationContainer>
    // </TabletScreenSwitcher>
  );
}
