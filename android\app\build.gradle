apply plugin: "com.android.application"
apply plugin: 'com.google.firebase.firebase-perf'
apply plugin: 'com.google.gms.google-services'
apply plugin: 'com.google.firebase.crashlytics'
apply plugin: "org.jetbrains.kotlin.android"
apply plugin: "com.facebook.react"

// plugins {
// //   id("com.android.application")
//   // ...

//   // Make sure that you have the Google services Gradle plugin
//   id("com.google.gms.google-services")

//   // Add the Crashlytics Gradle plugin
//   id("com.google.firebase.crashlytics")
// }

/**
 * This is the configuration block to customize your React Native Android app.
 * By default you don't need to apply any configuration, just uncomment the lines you need.
 */
react {
    /* Folders */
    //   The root of your project, i.e. where "package.json" lives. Default is '..'
    // root = file("../")
    //   The folder where the react-native NPM package is. Default is ../node_modules/react-native
    // reactNativeDir = file("../node_modules/react-native")
    //   The folder where the react-native Codegen package is. Default is ../node_modules/@react-native/codegen
    // codegenDir = file("../node_modules/@react-native/codegen")
    //   The cli.js file which is the React Native CLI entrypoint. Default is ../node_modules/react-native/cli.js
    // cliFile = file("../node_modules/react-native/cli.js")

    /* Variants */
    //   The list of variants to that are debuggable. For those we're going to
    //   skip the bundling of the JS bundle and the assets. By default is just 'debug'.
    //   If you add flavors like lite, prod, etc. you'll have to list your debuggableVariants.
    // debuggableVariants = ["liteDebug", "prodDebug"]

    /* Bundling */
    //   A list containing the node command and its flags. Default is just 'node'.
    // nodeExecutableAndArgs = ["node"]
    //
    //   The command to run when bundling. By default is 'bundle'
    // bundleCommand = "ram-bundle"
    //
    //   The path to the CLI configuration file. Default is empty.
    // bundleConfig = file(../rn-cli.config.js)
    //
    //   The name of the generated asset file containing your JS bundle
    // bundleAssetName = "MyApplication.android.bundle"
    //
    //   The entry file for bundle generation. Default is 'index.android.js' or 'index.js'
    // entryFile = file("../js/MyApplication.android.js")
    //
    //   A list of extra flags to pass to the 'bundle' commands.
    //   See https://github.com/react-native-community/cli/blob/main/docs/commands.md#bundle
    // extraPackagerArgs = []

    /* Hermes Commands */
    //   The hermes compiler command to run. By default it is 'hermesc'
    // hermesCommand = "$rootDir/my-custom-hermesc/bin/hermesc"
    //
    //   The list of flags to pass to the Hermes compiler. By default is "-O", "-output-source-map"
    // hermesFlags = ["-O", "-output-source-map"]
}

/**
 * Set this to true to Run Proguard on Release builds to minify the Java bytecode.
 */
def enableProguardInReleaseBuilds = false

/**
 * The preferred build flavor of JavaScriptCore (JSC)
 *
 * For example, to use the international variant, you can use:
 * `def jscFlavor = 'org.webkit:android-jsc-intl:+'`
 *
 * The international variant includes ICU i18n library and necessary data
 * allowing to use e.g. `Date.toLocaleString` and `String.localeCompare` that
 * give correct results when using with locales other than en-US. Note that
 * this variant is about 6MiB larger per architecture than default.
 */
def jscFlavor = 'org.webkit:android-jsc:+'

android {
    ndkVersion rootProject.ext.ndkVersion
    buildToolsVersion rootProject.ext.buildToolsVersion
    compileSdk rootProject.ext.compileSdkVersion

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    namespace "com.mykoodoo.promoter"
    defaultConfig {
        applicationId "com.mykoodoo.promoter"
        minSdkVersion rootProject.ext.minSdkVersion
        targetSdkVersion rootProject.ext.targetSdkVersion
        versionCode 530
        versionName "1.4.1"

        // // Load the values from .properties file
        // def keystoreSecret = rootProject.file("secret.properties")
        // def propertiesSecret = new Properties()
        // propertiesSecret.load(new FileInputStream(keystoreSecret))
        
        // // Return empty key in case something goes wrong
        // def PROJECT_NUM = propertiesSecret.getProperty("PROJECT_NUM") ?: "\"\""
        // def FLAVOR_ENV = propertiesSecret.getProperty("FLAVOR_ENV") ?: "\"\""
        // def ACCESS_KEY = propertiesSecret.getProperty("ACCESS_KEY") ?: "\"\""
        // def SECRET_KEY = propertiesSecret.getProperty("SECRET_KEY") ?: "\"\""
        // def ATTESTATION_HOST = propertiesSecret.getProperty("ATTESTATION_HOST") ?: "\"HOST\""
        // def ATTESTATION_CERT_PINNING = propertiesSecret.getProperty("ATTESTATION_CERT_PINNING") ?: "\"SHA\""
        // def UNIQUE_ID = propertiesSecret.getProperty("UNIQUE_ID") ?: "\"\""
        // def DEVELOPER_ID = propertiesSecret.getProperty("DEVELOPER_ID") ?: "\"\""

        // buildConfigField "String", "PROJECT_NUM", PROJECT_NUM
        // buildConfigField "String", "FLAVOR_ENV", FLAVOR_ENV
        // buildConfigField "String", "ACCESS_KEY", ACCESS_KEY
        // buildConfigField "String", "SECRET_KEY", SECRET_KEY
        // buildConfigField "String", "ATTESTATION_HOST", ATTESTATION_HOST
        // buildConfigField "String", "ATTESTATION_CERT_PINNING", ATTESTATION_CERT_PINNING
        // buildConfigField "String", "UNIQUE_ID", UNIQUE_ID
        // buildConfigField "String", "DEVELOPER_ID", DEVELOPER_ID
    }
    signingConfigs {
        debug {
            storeFile file('debug.keystore')
            storePassword 'android'
            keyAlias 'androiddebugkey'
            keyPassword 'android'
        }

        // ssKey {
        //     keyAlias ''
        //     keyPassword ''
        //     storeFile file('')
        //     storePassword ''
        // }
    }
    buildTypes {
        debug {
            signingConfig signingConfigs.debug
        }
        release {
            // Caution! In production, you need to generate your own keystore file.
            // see https://reactnative.dev/docs/signed-apk-android.
            signingConfig signingConfigs.debug
            minifyEnabled enableProguardInReleaseBuilds
            proguardFiles getDefaultProguardFile("proguard-android.txt"), "proguard-rules.pro"
        
            /* Add the firebaseCrashlytics extension (by default,
            * it's disabled to improve build speeds) and set
            * nativeSymbolUploadEnabled to true along with a pointer to native libs. */

            firebaseCrashlytics {
                nativeSymbolUploadEnabled true
                unstrippedNativeLibsDir 'build/intermediates/merged_native_libs/release/out/lib'
            }
        }
    }

    lintOptions {
        // Ignore lint issues in `node_modules`
        // checkReleaseBuilds false // Skips lint checks on release builds
        // excludes = ['**/node_modules/**']  // Note: using excludes instead of exclude
        abortOnError false // Prevents the build from failing due to lint errors
        disable 'all' // Disables all lint checks
    }
}

dependencies {
    // The version of react-native is set by the React Native Gradle Plugin
    implementation("com.facebook.react:react-android")

    if (hermesEnabled.toBoolean()) {
        implementation("com.facebook.react:hermes-android")
    } else {
        implementation jscFlavor
    }

    // implementation fileTree(dir: 'libs', include: ['*.aar'])

    // implementation("com.google.firebase:firebase-messaging:24.0.0")
    // implementation("com.google.firebase:firebase-iid:19.0.1")

    // implementation platform('com.google.firebase:firebase-bom:30.3.1')
    // implementation 'com.google.firebase:firebase-crashlytics-ktx'
    // implementation 'com.google.firebase:firebase-analytics-ktx'
    
    // implementation 'com.google.firebase:firebase-messaging'
    // implementation 'com.google.firebase:firebase-iid:21.1.0'

    debugImplementation fileTree(dir: 'libs', include: ['*.jar', '*.aar'], excludes: ['ssmobile-pog-engine-release.aar'])
    releaseImplementation fileTree(dir: 'libs', include: ['*.jar', '*.aar'], excludes: ['ssmobile-pog-engine-debug.aar'])

    implementation 'com.google.android.play:integrity:1.0.2'
    implementation 'com.google.android.gms:play-services-location:21.0.1'
    implementation 'com.google.code.gson:gson:2.8.5'
    implementation 'com.squareup.okhttp3:okhttp:3.14.7'
    implementation 'androidx.room:room-runtime:2.2.5'
    implementation 'androidx.localbroadcastmanager:localbroadcastmanager:1.0.0'
    implementation 'com.jakewharton.timber:timber:4.7.0'
    implementation 'org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.3.70'
    implementation 'com.google.code.gson:gson:2.8.5'
    implementation 'androidx.lifecycle:lifecycle-process:2.2.0'

    implementation(project(':react-native-tcp-socket')) {
        exclude group: 'org.bouncycastle'
    }

    // debugImplementation 'com.squareup.leakcanary:leakcanary-android:2.14'
}

apply from: file("../../node_modules/react-native-vector-icons/fonts.gradle")
apply from: file("../../node_modules/@react-native-community/cli-platform-android/native_modules.gradle"); applyNativeModulesAppBuildGradle(project)
