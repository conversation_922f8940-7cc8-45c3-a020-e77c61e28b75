import { Store } from 'pullstate';
import moment from 'moment';

export const CommonStore = new Store({
    isLoading: false,
    isLoadingApplicableVoucher: false,
    isLoadingCustomerInfo: false,
    isLoadingPromoCodePromotion: false,
    isLoadingUserOrderSelectedTable: false,

    isOrdering: false,

    orderTables: [],
    switchMerchant: false,

    simulateTabletMode: false,
    windowWidthSimulate: 2160,
    windowHeightSimulate: 1620,
    fontScaleSimulate: 2,

    windowWidthForModal: 2160,
    windowHeightForModal: 1620,

    accumulator: {},

    isOnMenu: true,
    isOnCategory: true,

    ///////////////////////////////

    // isAuthenticating: true,
    isAuthenticating: false, // 2024-07-14 - changed to false

    selectedOutletSection: {},

    selectedOutletTable: {},

    selectedOutletTableIdList: [],

    /////////////////////////////////

    // scope_storage_access: false,
    // scope_storage_dir: '',

    currOutletTaxes: [],

    // Shared from user app

    outletsTaxDict: {},

    selectedOutletItems: [], // [ outlet_item, ... ]
    selectedOutletItemCategories: [], // [ outlet_item_category, ... ]

    selectedOutletItemCategory: {}, // outlet_item_category
    selectedOutletItem: {}, // outlet_item

    outletsItemAddOnDict: {}, // { outlet_item_id -> outlet_item_addon, ... }
    outletsItemAddOnChoiceDict: {}, // { outlet_item_addon_id -> outlet_item_addon_choice, ... }

    selectedOutletItemAddOn: {}, // { outlet_item_addon_id -> { outlet_item_addon_choice_id = true/false, ... }, ... }
    selectedOutletItemAddOnChoice: {}, // { outlet_item_addon_choice_id -> true/false, ... }

    selectedOutletItemAddOnOi: {},

    onUpdatingCartItem: null,

    onUpdatingCurrPendingOrder: null,
    modeAddCart: 'NORMAL',

    cartItems: [], // [ { itemId: outlet_item_id, choices: { outlet_item_addon_choice_id: true/false, ... } }, ... ]
    cartItemsMo: [],
    cartItemChoices: {}, // { outlet_item_id -> { outlet_item_addon_choice_id = true, ... }, ... }

    // Calendar data
    calendarDataArray: [],
    selectedCalendarData: moment().format('YYYY-MM-DD'),
    selectedCalendarArray: [],

    orderType: 'DINEIN',
    orderTypeSub: 'NORMAL',

    cartOutletItemsDict: {},
    cartOutletItemAddOnDict: {},
    cartOutletItemAddOnChoiceDict: {},

    cartItemsProcessed: [],
    cartItemsMoProcessed: [],

    /////////////////////////////////

    segments: [],
    // segmentsDict: {},

    merchantVouchers: [],
    merchantVouchersDict: {},

    merchantVoucherReportsVoucherIdDict: {},

    outletSupplyItems: [],
    outletSupplyItemsDict: {},
    outletSupplyItemsSkuDict: {},

    supplyItems: [],
    supplyItemsSkuDict: {},
    supplyItemsDict: {},
    suppliers: [],
    suppliersDict: {},

    purchaseOrders: [],
    stockTransfers: [],
    stockTakes: [],

    suppliersProduct: [],
    suppliersProductDict: {},

    purchaseOrdersProduct: [],
    stockTransfersProduct: [],
    stockTakesProduct: [],
    stockReturnsProduct: [],

    allOutletsSupplyItems: [],
    allOutletsSupplyItemsDict: {},
    allOutletsSupplyItemsSkuDict: {},

    allOutletsItemAddOn: [],
    allOutletsItemAddOnDict: {}, // pass outletitem uniqueid to get the addon obj
    allOutletsItemAddOnChoiceDict: {}, // pass addon uniqueid to get the addonchoice obj

    allOutletsItemAddOnIdDict: {}, // pass outletitem uniqueid to get the addon obj
    allOutletsItemAddOnChoiceIdDict: {}, // pass addon uniqueid to get the addonchoice obj

    selectedProductEdit: null,
    selectedOutletCategoryEdit: null,
    selectedSupplierEdit: null,
    selectedPurchaseOrderEdit: null,
    selectedStockTransferEdit: null,
    selectedStockTakeEdit: null,
    selectedStockReturnEdit: null,
    selectedVoucherEdit: null,
    selectedOutletEmployeeEdit: null,

    selectedPromotionEdit: null,

    selectedCustomerEdit: null,

    selectedGuestEdit: null, // pass selected guest

    selectedPreorderPackageEdit: null,

    selectedPointsRedeemPackageEdit: null,

    selectedSegmentEdit: null,

    selectedBeerDocketEdit: null,

    selectedPurchaseOrderEditFromSupplier: null,

    selectedLoyaltyStampEdit: null,

    selectedLoyaltyCampaignEdit: null,

    selectedTaggableVoucherEdit: null,

    selectedStampTypeEdit: null,

    selectedLoyaltySignUpCampaignEdit: null,

    selectedTopupCreditTypeEdit: null,

    selectedUpsellingCampaignEdit: null,

    userCart: {},

    currPage: 'Dashboard',
    currPageStack: ['Dashboard'],
    pageStack: ['Dashboard'],
    expandTab: 'DASHBOARD',

    timestamp: Date.now(),

    routeParams: {},

    outletSelectDropdownView: () => { },

    selectedOrderToPayUserId: '',
    selectedOrderToPayUserIdTableIdDict: {},

    selectedOrderToPayUserIdVoucherIdRedemptionList: [],
    selectedOrderToPayUserIdVoucherIdValidList: [],

    summaryCheckDict: {},
    summaryCancelledCheckDict: {},
    summaryDeliveredCheckDict: {},

    chatbotModalVisibility: false,
    chatbotMessages: [
        // {
        //     _id: 1,
        //     text: 'Hello developer',
        //     createdAt: new Date(),
        //     user: {
        //         _id: 2,
        //         name: 'React Native',
        //         avatar: 'https://placeimg.com/140/140/any',
        //     },
        // },
        // {
        //     _id: 1,
        //     text: 'Hello developer',
        //     createdAt: new Date(),
        //     user: {
        //         _id: 2,
        //         name: 'React Native',
        //         avatar: 'https://placeimg.com/140/140/any',
        //     },
        // },
    ],


    // Navigation param
    venueSettingPage: '',

    // selectedOutletPromotions: [],

    availablePromotions: [],
    availablePromoCodePromotions: [],
    selectedOutletPromotion: {},
    allPromotions: {},

    overrideItemPriceSkuDict: {},
    amountOffItemSkuDict: {},
    percentageOffItemSkuDict: {},
    buy1Free1ItemSkuDict: {},
    deliveryItemSkuDict: {},
    takeawayItemSkuDict: {},

    overrideCategoryPriceNameDict: {},
    amountOffCategoryNameDict: {},
    percentageOffCategoryNameDict: {},
    buy1Free1CategoryNameDict: {},
    deliveryCategoryNameDict: {},
    takeawayCategoryNameDict: {},

    overrideItemPriceSkuDictLC: {},
    fiItemSkuDictLC: {},
    amountOffItemSkuDictLC: {},
    percentageOffItemSkuDictLC: {},
    buy1Free1ItemSkuDictLC: {},
    deliveryItemSkuDictLC: {},
    takeawayItemSkuDictLC: {},

    overrideCategoryPriceNameDictLC: {},
    fiCategoryNameDictLC: {},
    amountOffCategoryNameDictLC: {},
    percentageOffCategoryNameDictLC: {},
    buy1Free1CategoryNameDictLC: {},
    deliveryCategoryNameDictLC: {},
    takeawayCategoryNameDictLC: {},

    allMenuVouchers: [],

    currOutletOrderNumber: {},

    isCheckingOutTakeaway: false,
    checkingOutTakeawayOrder: {},
    checkingOutTakeawayOrderList: [],
    checkingOutTakeawayTimestamp: Date.now(),

    isCounterOrdering: false, // 2022-06-10 Fixes

    selectedLoyaltyCampaign: {},
    selectedGuestDetails: {},

    shiftClosedModal: false,

    selectedReservationTableId: '',
    selectedReservationTableCode: '',

    selectedTaggableVoucher: {},

    selectedPromoCodePromotion: {},

    isNewTaggableVoucherScreenMounted: false,
    isNewCampaignScreenMounted: false,
    isNewLoyaltyCampaignScreenMounted: false,
    isSupplierProductScreenMounted: false,

    isOfflineReady: false,

    // clearDashboardDataFunc: null,

    moPax: '1',
    moMethod: 'dinein',

    enteredPinNo: '',

    isUserActive: true,

    supportCodeData: null,

    topupCreditTypeOrder: {},

    timestampPromotion: Date.now(),

    cartItemsTableIdDict: {},

    /////////////////////////////////

    // Mo-screens specific

    selectedOutletTableMo: {},
    orderTypeMo: 'DINEIN',
    orderTypeSubMo: 'NORMAL',

    /////////////////////////////////

    timestampOutletCategory: Date.now(),

    /////////////////////////////////

    // 11/10/2022 For Available On function (Time) - Greg

    timeCheckItem: Date.now(),

    /////////////////////////////////

    // 2022-11-14 - Printer checking modal

    printerCheckingModalVisibility: false,

    /////////////////////////////////

    loyaltyStampGetItemSkuDict: {},
    loyaltyStampGetCategoryNameDict: {},
    loyaltyStampBuyItemSkuDict: {},
    loyaltyStampBuyCategoryNameDict: {},

    userLoyaltyStampGetLsItemDict: {},

    /////////////////////////////////

    historyStartDate: moment().startOf('month').startOf('day').valueOf(),
    historyEndDate: moment().endOf('month').endOf('day').valueOf(),
    // historyStartDate: moment().month(9).date(2).startOf('day').valueOf(),
    // historyEndDate: moment().month(9).date(2).endOf('day').valueOf(),

    outletReviewStartDate: moment().startOf('month').startOf('day').valueOf(),
    outletReviewEndDate: moment().endOf('month').endOf('day').valueOf(),

    /////////////////////////////////

    newSelectedOutletItemAddOnDetails: {},

    /////////////////////////////////

    deviceInfo: {
        uid: '', // unique id
        dt: Date.now(),
        dtf: Date.now(), // date time format
        dn: '', // device name
        did: '', // device id
        dm: '', // device model
        os: '',
        ver: '',
    },

    /////////////////////////////////

    selectedOrder: {},

    /////////////////////////////////

    sideBarScrollX: 0,
    sideBarScrollY: 0,

    /////////////////////////////////

    networkNotStable: false,

    reportOutletIdList: [],

    /////////////////////////////////

    showPinUnlockModal: false,
    pinUnlockType: '',

    /////////////////////////////////

    cacheActualUserOrderDictDt: null,

    /////////////////////////////////

    // isPaymentLoading: false,

    /////////////////////////////////

    // forceShowApp: null, // for xiaomi pad 6 auto sign out issue fixes

    moSearch: '',

    /////////////////////////////////

    sunmiSerialNo: '',
    iminSerialNo: '',

    /////////////////////////////////

    customerDisplaySupport: false,

    /////////////////////////////////

    isModalOpened: false,
    modalCount: 0,

    /////////////////////////////////

    selectedAddOnIdForChoiceQtyDict: {}, // { OutletItemAddOn.uniqueId: 0 }

    /////////////////////////////////

    // 2025-01-08 - reservation screen changes

    reservationScreenSection: 'MAIN',

    /////////////////////////////////

    selectedOutletCategoryList: [],

    selectedCatalogEdit: null,

    /////////////////////////////////

    // timerUserOrderPrinting: null,
    timerUserOrderPrinting: Date.now(),
});