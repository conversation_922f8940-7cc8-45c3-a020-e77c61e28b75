import React, { Component } from 'react';
import { Alert, StyleSheet, View } from 'react-native';
import AppNavigator from '../navigation/AppNavigator';
import LoginScreen from './LoginScreen';
import PinLogin from './PinLogin';
import * as User from '../util/User';
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as Token from '../util/Token';
import {
    isTablet, logToFile
} from '../util/common';
import auth from '@react-native-firebase/auth';
import ApiClient from '../util/ApiClient';
import API from '../constant/API';
import { CommonStore } from '../store/commonStore';
import { UserStore } from '../store/userStore';
import { MerchantStore } from '../store/merchantStore';
import NetInfo from "@react-native-community/netinfo";
import { ROLE_TYPE } from '../constant/common';
import moment from 'moment';
import CustomerDisplay from '../components/customerDisplay';
// import ReactNativeZoomableView from '@dudigital/react-native-zoomable-view/src/ReactNativeZoomableView';

var hide = true;
class MainScreen extends Component {
    constructor({ navigation, props }) {
        super(props)
        this.state = {
            showApp: false,
            isPinLogin: false,
        }
    }

    componentDidMount() {
        // this.checkFirebaseAuth();

        this.checkAsyncStorage();

        this.checkLogin(); // test herks

        // this.checkPinLogin();
        //  User.setRefreshMainScreen(this.checkPinLogin.bind(this)); 

        User.setRefreshMainScreen(this.checkLogin.bind(this));
        User.setCheckAsyncStorage(this.checkAsyncStorage.bind(this));

        global.funcSwitchShowApp = this.switchShowApp.bind(this);
    }

    async checkFirebaseAuth() {
        // const switchMerchantRaw = await AsyncStorage.getItem('switchMerchant');        

        var isFirebaseSignInSuccess = false;

        const customToken = await AsyncStorage.getItem('customToken');
        const firebaseToken = await AsyncStorage.getItem('firebaseToken');

        if (customToken) {
            try {
                res = await auth().signInWithCustomToken(result.customToken);

                isFirebaseSignInSuccess = true;
            }
            catch (error) {
                // console.log('failed to login with custom token');
            }
        }

        if (isFirebaseSignInSuccess && firebaseToken) {
            ApiClient.GET(API.getToken + firebaseToken).then(async (result) => {
                if (result && result.merchantId) {
                    if (result && result.token) {
                        Token.setToken(result.token);
                        Token.setRefreshToken(result.refreshToken);
                        await AsyncStorage.setItem('accessToken', result.token ? result.token : '');

                        if (result.noSignoutFN !== undefined) {
                            global.noSignoutFN = result.noSignoutFN;
                        }

                        if (result.noSignoutC !== undefined) {
                            global.noSignoutC = result.noSignoutC;
                        }

                        if (result.noSignoutI !== undefined) {
                            global.noSignoutI = result.noSignoutI;
                        }

                        ApiClient.GET(API.userAdmin).then(async (userData) => {
                            User.setUserData(userData);
                            User.setName(userData.name);
                            User.setRefreshToken(userData.refreshToken);
                            User.setUserId(userData.firebaseUid);
                            User.setlogin(true);
                            User.setMerchantId(userData.merchantId);
                            User.setOutletId(userData.outletId);

                            if (userData.noSignoutFN !== undefined) {
                                global.noSignoutFN = userData.noSignoutFN;
                            }

                            if (userData.noSignoutC !== undefined) {
                                global.noSignoutC = userData.noSignoutC;
                            }

                            if (userData.noSignoutI !== undefined) {
                                global.noSignoutI = userData.noSignoutI;
                            }

                            if (userData.role === ROLE_TYPE.ADMIN) {
                                await AsyncStorage.setItem(
                                    'email',
                                    email
                                );
                                await AsyncStorage.setItem(
                                    'password',
                                    password
                                );
                            }

                            await AsyncStorage.setItem(
                                'loggedIn',
                                "true"
                            );
                            await AsyncStorage.setItem(
                                'userData',
                                JSON.stringify(userData)
                            );

                            // ApiClient.GET(API.outlet2 + User.getOutletId()).then((result) => {
                            //   User.setName(result.name);
                            //   User.setQueueStatus(result.queueStatus);
                            //   User.setRefreshCurrentAction(result.reservationStatus);
                            //   checkLogin(true)
                            // });

                            CommonStore.update(s => {
                                s.isAuthenticating = false;
                            });

                            checkLogin(false);
                        });
                    }
                } else {
                    Alert.alert('Login failed', "Invalid merchant account", [
                        { text: "OK", onPress: () => { } }
                    ],
                        { cancelable: false });
                }
            });
        }
        else {
            CommonStore.update(s => {
                s.isAuthenticating = false;
            });
        }
    }

    async checkAsyncStorage() {
        const email = await AsyncStorage.getItem('email');

        if (email) {
            // means login using email/password before

            this.setState({
                isPinLogin: true,
            });
        }
        else {
            this.setState({
                isPinLogin: false,
            });
        }

        ////////////////////////////////////////////

        // 2024-07-15 - no need first

        const supportCodeDataRaw = await AsyncStorage.getItem('supportCodeData');
        const supportCodeDataObj = JSON.parse(supportCodeDataRaw);

        if (supportCodeDataObj) {
            // means got the support code data

            CommonStore.update(s => {
                s.supportCodeData = supportCodeDataObj;
            });

            // try to check first

            if (moment().isSameOrAfter(supportCodeDataObj.endDateTime)) {
                // means expired already

                //   CommonStore.update(s => {
                //     s.supportCodeData = null;
                //   });

                //   logOutUser();

                UserStore.update((s) => {
                    s.avatar = '';
                    s.dob = null;
                    s.email = '';
                    s.gender = '';
                    s.name = '';
                    s.number = '';
                    s.outletId = '';
                    s.race = '';
                    s.state = '';
                    s.uniqueName = '';
                    s.updatedAt = null;
                    s.merchantId = '';
                    s.role = '';
                    s.refreshToken = '';
                    s.firebaseUid = '';
                    s.privileges = [];
                    s.screensToBlock = [];
                });

                await AsyncStorage.multiRemove([
                    'accessToken',
                    'userData',
                    'refreshToken',

                    'merchantLogoUrl',

                    // 'isAskedBluetooth',

                    // 'lastActivity',

                    'email',
                    'password',

                    'printerList',

                    'supportCodeData',
                ]);

                User.setlogin(false);
                User.setMerchantId(null);
                User.setUserData(null);
                User.setUserId(null);
                User.setRefreshToken(null);
                User.setOutletId(null);
                User.getRefreshMainScreen();

                this.setState({
                    isPinLogin: false,
                });

                global.funcSwitchShowApp(false);
            }
        }
    }

    // function here
    async checkLogin(isOffline) {
        // if (auth().currentUser) {
        //     AsyncStorage.multiGet(['accessToken', 'userData', 'refreshToken']).then((items, err) => {
        //         if (items) {
        //             if (items[0][1]) {
        //                 Token.setToken(items[0][1]);
        //                 Token.setRefreshToken(items[2][1]);
        //                 User.initFromJSON(JSON.parse(items[1][1]));
        //                 this.setState({ showApp: true })
        //             } else {
        //                 this.setState({ showApp: false })
        //             }
        //         }
        //     });
        // }

        AsyncStorage.multiGet(['accessToken', 'userData', 'refreshToken']).then(async (items, err) => {
            if (items) {
                // if (items[0] !== undefined) {
                //     global.udiData.asi0 = JSON.stringify(items[0]);
                // }

                // if (items[1] !== undefined) {
                //     global.udiData.asi1 = JSON.stringify(items[1]);
                // }

                if (items[0][1]) {
                    NetInfo.fetch().then(async state => {
                        // console.log("Connection type", state.type);
                        // console.log("Is connected?", state.isInternetReachable);
                        // console.log(state);

                        // support offline mode
                        const lastAccessToken = await AsyncStorage.getItem('last.accessToken');

                        if (state.isInternetReachable || lastAccessToken) {
                            if (isOffline) {
                                global.privileges = [
                                    "EMPLOYEES",
                                    "OPERATION",
                                    "PRODUCT",
                                    "INVENTORY",
                                    "INVENTORY_COMPOSITE",
                                    "DOCKET",
                                    "VOUCHER",
                                    "PROMOTION",
                                    "CRM",
                                    "LOYALTY",
                                    "TRANSACTIONS",
                                    "REPORT",
                                    "RESERVATIONS",

                                    // for action
                                    'REFUND_ORDER',

                                    'SETTINGS',

                                    'QUEUE',

                                    'OPEN_CASH_DRAWER',

                                    'KDS',

                                    'UPSELLING',

                                    // for Kitchen

                                    'REJECT_ITEM',
                                    'CANCEL_ORDER',
                                    //'REFUND_tORDER',

                                    'MANUAL_DISCOUNT',
                                ];

                                Token.setToken(items[0][1]);
                                Token.setRefreshToken(items[2][1]);
                                User.initFromJSON(JSON.parse(items[1][1]));

                                this.setState({ showApp: true });

                                const firebaseUid = await AsyncStorage.getItem('firebaseUid');
                                const merchantId = await AsyncStorage.getItem('merchantId');

                                const role = await AsyncStorage.getItem('role');
                                this.setState({ role });
                                // console.log('role', role)

                                const isAlphaUser = await AsyncStorage.getItem('isAlphaUser');

                                const isBetaUser = await AsyncStorage.getItem('isBetaUser');

                                const privilegesRaw = await AsyncStorage.getItem('privileges');

                                const screensToBlockRaw = await AsyncStorage.getItem('screensToBlock');

                                const currOutletId = await AsyncStorage.getItem('currOutletId');

                                var privileges = [];
                                if (privilegesRaw) {
                                    privileges = JSON.parse(privilegesRaw);
                                }

                                var screensToBlock = [];
                                if (screensToBlockRaw) {
                                    screensToBlock = JSON.parse(screensToBlockRaw);
                                }

                                global.currUserRole = role;

                                UserStore.update(s => {
                                    s.firebaseUid = firebaseUid;
                                    s.merchantId = merchantId;

                                    s.role = role;

                                    s.isAlphaUser = isAlphaUser === '1' ? true : false;

                                    s.isBetaUser = isBetaUser === '1' ? true : false;

                                    s.privileges = privileges;
                                    s.screensToBlock = screensToBlock;

                                    // s.isMasterAccount = isMasterAccount === '1' ? true : false; // 2022-01-26 - at mount will run this cause breaking logic
                                });

                                if (global.isOfflineAccess) {
                                    const isMasterAccount = await AsyncStorage.getItem('isMasterAccount');

                                    UserStore.update(s => {
                                        s.isMasterAccount = isMasterAccount === '1' ? true : false; // 2022-01-26 - at mount will run this cause breaking logic
                                    });
                                }

                                global.privileges_state = privileges;

                                // if (role === ROLE_TYPE.ADMIN) {
                                //     global.privileges = [
                                //         "EMPLOYEES",
                                //         "OPERATION",
                                //         "PRODUCT",
                                //         "INVENTORY",
                                //         "INVENTORY_COMPOSITE",
                                //         "DOCKET",
                                //         "VOUCHER",
                                //         "PROMOTION",
                                //         "CRM",
                                //         "LOYALTY",
                                //         "TRANSACTIONS",
                                //         "REPORT",
                                //         "RESERVATIONS",

                                //         // for action
                                //         'REFUND_ORDER',

                                //         'SETTINGS',

                                //         'QUEUE',

                                //         'OPEN_CASH_DRAWER',

                                //         'KDS',
                                //     ];
                                // }

                                if (role === ROLE_TYPE.FRONTLINER || role === ROLE_TYPE.STORE_MANAGER) {
                                    // clock in 
                                    let dateTime = Date.now();

                                    let body = {
                                        employeeId: firebaseUid,
                                        loginTime: dateTime,

                                        merchantId,
                                        outletId: currOutletId,
                                    }

                                    // 2022-06-16 - No need first
                                    // ApiClient.POST(API.updateUserClockInOut, body).then((result) => {
                                    //     // console.log('updateUserClockIn', result);
                                    // });
                                }

                                if (currOutletId) {
                                    MerchantStore.update(s => {
                                        s.currOutletId = currOutletId;
                                    });
                                }

                                CommonStore.update(s => {
                                    s.isAuthenticating = false;
                                });
                            }
                            else {
                                if (
                                    (global.currOutlet && global.currOutlet.noSignoutFN)
                                    ||
                                    global.noSignoutFN
                                ) {
                                    // do nothing

                                    global.udiData.mns3 = `${moment().format('YYYY-MM-DD, HH:mm:ss.SSS')}`;

                                    logToFile(`MainScreen | no sign out 3`);
                                }
                                else {
                                    global.udiData.ms3 = `${moment().format('YYYY-MM-DD, HH:mm:ss.SSS')}`;

                                    logToFile(`MainScreen | auto signout 3`);

                                    if (global.signInAlready) {
                                        global.udiData.msiay3 = `${moment().format('YYYY-MM-DD, HH:mm:ss.SSS')}`;
                                    }
                                    else {
                                        global.udiData.msian3 = `${moment().format('YYYY-MM-DD, HH:mm:ss.SSS')}`;

                                        // 2024-07-14 xiaomi pad 6 need to set this disabled to sign in

                                        this.setState({ showApp: false });

                                        CommonStore.update(s => {
                                            s.isAuthenticating = false;
                                        });
                                    }
                                }
                            }
                        }
                        else {
                            // hide first, for offline mode support

                            // Alert.alert('Error', 'Not connected to the internet.');

                            // this.setState({ showApp: false });

                            // CommonStore.update(s => {
                            //     s.isAuthenticating = false;
                            // });
                        }
                    });
                } else {
                    if (
                        (global.currOutlet && global.currOutlet.noSignoutFN)
                        ||
                        global.noSignoutFN
                    ) {
                        // do nothing

                        global.udiData.mns1 = `${moment().format('YYYY-MM-DD, HH:mm:ss.SSS')}`;

                        logToFile(`MainScreen | no sign out 1`);
                    }
                    else {
                        global.udiData.ms1 = `${moment().format('YYYY-MM-DD, HH:mm:ss.SSS')}`;

                        logToFile(`MainScreen | auto signout 1`);

                        if (global.signInAlready) {
                            global.udiData.msiay1 = `${moment().format('YYYY-MM-DD, HH:mm:ss.SSS')}`;
                        }
                        else {
                            global.udiData.msian1 = `${moment().format('YYYY-MM-DD, HH:mm:ss.SSS')}`;

                            // 2024-07-14 - xiaomi pad 6 need to set this disabled to sign in

                            this.setState({ showApp: false });

                            CommonStore.update(s => {
                                s.isAuthenticating = false;
                            });
                        }
                    }
                }
            }
            else {
                if (
                    (global.currOutlet && global.currOutlet.noSignoutFN)
                    ||
                    global.noSignoutFN
                ) {
                    // do nothing

                    global.udiData.mns2 = `${moment().format('YYYY-MM-DD, HH:mm:ss.SSS')}`;

                    logToFile(`MainScreen | no sign out 2`);
                }
                else {
                    global.udiData.ms2 = `${moment().format('YYYY-MM-DD, HH:mm:ss.SSS')}`;

                    logToFile(`MainScreen | auto signout 2`);

                    if (global.signInAlready) {
                        global.udiData.msiay2 = `${moment().format('YYYY-MM-DD, HH:mm:ss.SSS')}`;
                    }
                    else {
                        global.udiData.msian2 = `${moment().format('YYYY-MM-DD, HH:mm:ss.SSS')}`;

                        // 2024-07-14 - xiaomi pad 6 need to set this disabled to sign in

                        this.setState({ showApp: false });

                        CommonStore.update(s => {
                            s.isAuthenticating = false;
                        });
                    }
                }
            }
        });
    }


    async switchLogin() {
        this.setState({ isPinLogin: !this.state.isPinLogin });
    }

    async changeToPinLogin() {
        const email = await AsyncStorage.getItem('email');

        if (email) {
            this.setState({ isPinLogin: true });
        }
        else {
            // need login once, before able to sign in

            Alert.alert('Info', 'Please login to the account first, before sign in.')
        }
    }

    switchShowApp(value) {
        this.setState({ showApp: value });
    }


    // function end

    render() {
        console.log('c-render - MainScreen.js');

        return ((!this.state.showApp && global.forceShowApp === null) ? // <AppNavigator />
            (!this.state.isPinLogin ? <LoginScreen
                checkLogin={this.checkLogin.bind(this)}
                switchLogin={this.switchLogin.bind(this)}
                changeToPinLogin={this.changeToPinLogin.bind(this)}
                navigation={this.props.navigation} /> : <PinLogin
                checkLogin={this.checkLogin.bind(this)}
                switchLogin={this.switchLogin.bind(this)}
                switchShowApp={this.switchShowApp.bind(this)} />) : (
                <>
                    <AppNavigator />

                    <CustomerDisplay />
                </>
            ));
    }
}

const styles = StyleSheet.create({});
export default MainScreen;
