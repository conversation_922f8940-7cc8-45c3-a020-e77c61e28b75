import { Text } from "react-native-fast-text";
import React, { Component, useReducer, useState, useEffect, useCallback } from 'react';
import {
  StyleSheet,
  Image,
  View,
  Alert,
  TouchableOpacity,
  Modal as ModalComponent,
  Dimensions,
  TextInput,
  KeyboardAvoidingView,
  ActivityIndicator,
  useWindowDimensions,
  TouchableHighlight,
} from 'react-native';
import { ModalView as ModalViewComponent } from 'react-native-multiple-modals';
import { ScrollView, FlatList } from "react-native-gesture-handler";
import CheckBox from 'react-native-check-box';
// import CheckBox from '@react-native-community/checkbox';
import Colors from '../constant/Colors';
import Styles from '../constant/Styles';
import SideBar from './SideBar';
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as User from '../util/User';
import Icon from 'react-native-vector-icons/Ionicons';
import Icon1 from 'react-native-vector-icons/Feather';
import Ionicon from 'react-native-vector-icons/Ionicons';
//import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import ApiClient from '../util/ApiClient';
import API from '../constant/API';
import DropDownPicker from 'react-native-dropdown-picker';
import GCalendar from '../assets/svg/GCalendar';
import Feather from 'react-native-vector-icons/Feather';
import EvilIcons from 'react-native-vector-icons/EvilIcons';
import moment from 'moment';
import DateTimePickerModal from 'react-native-modal-datetime-picker';
import {
  getTransformForScreenInsideNavigation,
  isTablet, parseImagePickerResponse, parseValidPriceText
} from '../util/common';
import {
  MERCHANT_VOUCHER_CODE_FORMAT,
  MERCHANT_VOUCHER_TYPE,
  SEGMENT_TYPE,
  ORDER_TYPE,
  ORDER_TYPE_PARSED,
  ORDER_TYPE_DROP_DOWN_LIST,
  EXPAND_TAB_TYPE,
  UPSELL_BY_TYPE,
  UPSELL_BY_TYPE_DROPDOWN_LIST,
  UPSELLING_SECTION_DROPDOWN_LIST,
} from '../constant/common';
import { CommonStore } from '../store/commonStore';
import { UserStore } from '../store/userStore';
import { MerchantStore } from '../store/merchantStore';
import { OutletStore } from '../store/outletStore';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import RNPickerSelect from 'react-native-picker-select';
import { useKeyboard } from '../hooks';
import { get } from 'react-native/Libraries/Utilities/PixelRatio';
import { launchCamera, launchImageLibrary } from 'react-native-image-picker';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import Switch from 'react-native-switch-pro';
import {
  EFFECTIVE_DAY_DROPDOWN_LIST,
  EFFECTIVE_DAY_DROPDOWN_LIST2,
  TARGET_USER_GROUP_DROPDOWN_LIST,
  PROMOTION_TYPE_VARIATION,
  PROMOTION_TYPE_VARIATION_DROPDOWN_LIST,
  EFFECTIVE_TYPE,
  CRM_SEGMENT_DROPDOWN_LIST,
} from '../constant/promotions';
import { areArraysEqual, uploadImageToFirebaseStorage, isObjectEqual } from '../util/common';
import AsyncImage from '../components/asyncImage';
import 'react-native-get-random-values';
import { v4 as uuidv4 } from 'uuid';
import AntDesign from 'react-native-vector-icons/AntDesign';
// import NumericInput from 'react-native-numeric-input';
import FontAwesome5 from 'react-native-vector-icons/FontAwesome5';
import { getAllExternalFilesDirs } from '@dr.pogodin/react-native-fs';
import {
  LOYALTY_CAMPAIGN_DROPDOWN_LIST,
  LOYALTY_CAMPAIGN_TYPE,
  LOYALTY_CAMPAIGN_TYPE_PARSED,
  LOYALTY_PROMOTION_TYPE,
  LOYALTY_PROMOTION_TYPE_DROPDOWN_LIST,
} from '../constant/loyalty';
import APILocal from '../util/apiLocalReplacers';
import { useFocusEffect } from '@react-navigation/native';
import UserIdleWrapper from '../components/userIdleWrapper';
import DraggableFlatList from 'react-native-draggable-flatlist';
import { logEventAnalytics } from '../util/common';
import { ANALYTICS, ANALYTICS_PARSED } from '../constant/analytics';

//////////////////////////////////////////////////////////////////////////////////////////////////////////
const ModalView = Platform.OS === 'android' ? ModalComponent : ModalViewComponent;

const UpsellingCampaignScreen = (props) => {
  const { navigation } = props;

  ///////////////////////////////////////////////////////////

  const [isMounted, setIsMounted] = useState(true);

  useFocusEffect(
    useCallback(() => {
      setIsMounted(true);
      return () => {
        CommonStore.update(s => {
          s.isUpsellingCampaignScreenMounted = false;
        });

        setIsMounted(false);
      };
    }, [])
  );

  ///////////////////////////////////////////////////////////

  const [keyboardHeight] = useKeyboard();

  const { width: windowWidth, height: windowHeight } = useWindowDimensions();

  const [campaignName, setCampaignName] = useState('');
  const [isEnableSellOnline, setIsEnableSellOnline] = useState(false);

  const [selectedTargetUserGroup, setSelectedTargetUserGroup] = useState(
    TARGET_USER_GROUP_DROPDOWN_LIST[0].value,
  );
  const [selectedTargetSegmentGroupList, setSelectedTargetSegmentGroupList] =
    useState([CRM_SEGMENT_DROPDOWN_LIST[0].value]);
  const [switchMerchant, setSwitchMerchant] = useState(isTablet() ? false : true);
  const [promoCode, setPromoCode] = useState('');
  const [isPromoCodeUsageLimit, setIsPromoCodeUsageLimit] = useState(false);
  const [promoCodeUsageLimit, setPromoCodeUsageLimit] = useState('');
  const [promoCodeUsageLimitTicked, setPromoCodeUsageLimitTicked] =
    useState(false);

  const [selectedEffectiveType, setSelectedEffectiveType] = useState(
    EFFECTIVE_TYPE.DAY,
  );
  const [selectedEffectiveTypeOptions, setSelectedEffectiveTypeOptions] =
    useState([EFFECTIVE_DAY_DROPDOWN_LIST[0].value]);
  const [selectedEffectiveDay, setSelectedEffectiveDay] = useState(
    EFFECTIVE_DAY_DROPDOWN_LIST[0].value,
  );
  const [showEffectiveTimeStartPicker, setShowEffectiveTimeStartPicker] =
    useState(false);
  const [showEffectiveTimeEndPicker, setShowEffectiveTimeEndPicker] =
    useState(false);
  const [effectiveTimeStart, setEffectiveTimeStart] = useState(moment());
  const [effectiveTimeEnd, setEffectiveTimeEnd] = useState(moment());

  const [showPromoDateStartPicker, setShowPromoDateStartPicker] =
    useState(false);
  const [showPromoDateEndPicker, setShowPromoDateEndPicker] = useState(false);
  const [showPromoTimeStartPicker, setShowPromoTimeStartPicker] =
    useState(false);
  const [showPromoTimeEndPicker, setShowPromoTimeEndPicker] = useState(false);
  const [promoDateStart, setPromoDateStart] = useState(moment());
  const [promoDateEnd, setPromoDateEnd] = useState(moment());
  const [promoTimeStart, setPromoTimeStart] = useState(moment());
  const [promoTimeEnd, setPromoTimeEnd] = useState(moment());

  const [campaignDescription, setCampaignDescription] = useState('');

  const [selectedPromotionType, setSelectedPromotionType] = useState(
    LOYALTY_PROMOTION_TYPE_DROPDOWN_LIST[0].value,
  );

  const [
    isRequireSpecificProductPurchase,
    setIsRequireSpecificProductPurchase,
  ] = useState(false);

  const [image, setImage] = useState('');
  const [imageType, setImageType] = useState('');
  const [isImageChanged, setIsImageChanged] = useState(false);

  const [outletDropdownList, setOutletDropdownList] = useState([]);
  const [selectedOutletList, setSelectedOutletList] = useState([]); // multi-outlets

  //const [availabilityDropdownList, setAvailabilityDropdownList] = useState([]); // DropDownList for Dine in, TakeAway, Delivery
  const [selectedAvailability, setSelectedAvailability] = useState([
    ORDER_TYPE_DROP_DOWN_LIST[0].value,
  ]); // Enable Promotion for Dine in, TakeAway, Delivery
  const [selectAllDays, setSelectAllDays] = useState(false);

  ///////////////////////////////////////////////////////////////////////////////////////////////////////////

  const [isChecked, setIsChecked] = useState(false);
  const [showDateTimePicker_Date, setShowDateTimePicker_Date] = useState(false);
  const [showDateTimePicker_Time, setShowDateTimePicker_Time] = useState(false);
  //const [isLoading1, setIsLoading1] = useState(false);

  /////////////////////////////////////////////////////////////////////////////////////////////////////////

  const userId = UserStore.useState((s) => s.firebaseUid);
  const userName = UserStore.useState((s) => s.name);
  const merchantName = MerchantStore.useState((s) => s.name);

  const [temp, setTemp] = useState('');

  ////////////////////////////////Combo Set/Package/Bundle Function//////////////////////////////////////////

  const [packageItemPrice, setPackageItemPrice] = useState('0');
  const [packageMinQuantity, setPackageMinQuantity] = useState('0');
  const [packagePriceFinal, setPackagePriceFinal] = useState(0);

  const [packageTaxDropdownList, setPackageTaxDropdownList] = useState([]);
  const [packageTax, setPackageTax] = useState('');

  const [packageProductDropdownList, setPackageProductDropdownList] = useState(
    [],
  );
  const [packageselectedProductList, setPackageSelectedProductList] = useState(
    [],
  );

  const [packageCategoryDropdownList, setPackageCategoryDropdownList] =
    useState([]);
  const [packageSelectedCategoryList, setPackageSelectedCategoryList] =
    useState([]);

  ////////////////////////////////Override Function//////////////////////////////////////////

  const [overrideItemPrice, setOverrideItemPrice] = useState('0');
  const [overridePriceFinal, setOverridePriceFinal] = useState(0);
  // const [overrideMinQuantity, setOverrideMinQuantity] = useState('');

  const [overrideTaxDropdownList, setOverrideTaxDropdownList] = useState([]);
  const [overrideTax, setOverrideTax] = useState('');

  const [overrideProductDropdownList, setOverrideProductDropdownList] =
    useState([]);
  const [overrideSelectedProductList, setOverrideSelectedProductList] =
    useState([]);

  const [overrideCategoryDropdownList, setOverrideCategoryDropdownList] =
    useState([]);
  const [overrideSelectedCategoryList, setOverrideSelectedCategoryList] =
    useState([]);

  ///////////////////////////////////////////////////////////////////////////////////////////

  // Override in array form

  const [overrideItems, setOverrideItems] = useState([
    {
      priceBeforeTax: 0,
      variation: PROMOTION_TYPE_VARIATION.SPECIFIC_PRODUCTS,
      variationItems: [],
      variationItemsSku: [],
    },
  ]);

  const [variationItemsProducts, setVariationItemsProducts] = useState([]);
  const [variationItemsCategories, setVariationItemsCategories] = useState([]);

  ///////////////////////////////Cashback Function////////////////////////////////////////////

  const [cashbackPercentage, setCashbackPercentage] = useState('');
  const [cashbackMinQuantity, setCashbackMinQuantity] = useState('');
  const [cashbackExpired, setCashbackExpired] = useState('');

  const [cashbackExpiredDropdownList, setCashbackExpiredDropdownList] =
    useState([]);
  const [cashbackSelectedExpiredList, setCashbackSelectedExpiredList] =
    useState([]);

  const [cashbackProductDropdownList, setCashbackProductDropdownList] =
    useState([]);
  const [cashbackSelectedProductList, setCashbackSelectedProductList] =
    useState([]);

  const [cashbackCategoryDropdownList, setCashbackCategoryDropdownList] =
    useState([]);
  const [cashbackSelectedCategoryList, setCashbackSelectedCategoryList] =
    useState([]);

  ///////////////////////////////Buy 1 Free 1 Function////////////////////////////////////////////

  const [buyInput, setBuyInput] = useState('1');
  const [getInput, setGetInput] = useState('1');
  const [getPriceInput, setGetPriceInput] = useState('0');

  const [buy1Free1ProductDropdownList, setBuy1Free1ProductDropdownList] =
    useState([]);
  const [buy1Free1SelectedProductList, setBuy1Free1SelectedProductList] =
    useState([]);

  const [selectedVariationB1F1, setSelectedVariationB1F1] = useState(
    PROMOTION_TYPE_VARIATION.SPECIFIC_PRODUCTS,
  );

  const [selectedVariationItemsB1F1, setSelectedVariationItemsB1F1] = useState(
    [],
  );

  const [selectedVariationItemsSkuB1F1, setSelectedVariationItemsSkuB1F1] =
    useState([]); // for multi-outlets

  const [variationItemsB1F1DropdownList, setVariationItemsB1F1DropdownList] =
    useState([]);

  ///////////////////////////////Take amount off////////////////////////////////////////////

  const [amountOffPrice, setAmountOffPrice] = useState('0');
  const [amountOffMinQuantity, setAmountOffMinQuantity] = useState('0');
  const [amountOffMaxQuantity, setAmountOffMaxQuantity] = useState('0');

  const [amountOffProductDropdownList, setAmountOffProductDropdownList] =
    useState([]);
  const [amountOffSelectedProductList, setAmountOffSelectedProductList] =
    useState([]);

  const [amountOffCategoryDropdownList, setAmountOffCategoryDropdownList] =
    useState([]);
  const [amountOffSelectedCategoryList, setAmountOffSelectedCategoryList] =
    useState([]);

  ///////////////////////////////Take percentage off////////////////////////////////////////////

  const [percentageOffPrice, setPercentageOffPrice] = useState('0');
  const [percentageOffMinQuantity, setPercentageOffMinQuantity] = useState('0');
  const [percentageOffMaxQuantity, setPercentageOffMaxQuantity] = useState('0');

  const [
    percentageOffProductDropdownList,
    setPercentageOffProductDropdownList,
  ] = useState([]);
  const [
    percentageOffSelectedProductList,
    setPercentageOffSelectedProductList,
  ] = useState([]);

  const [
    percentageOffCategoryDropdownList,
    setPercentageOffCategoryDropdownList,
  ] = useState([]);
  const [
    percentageOffSelectedCategoryList,
    setPercentageOffSelectedCategoryList,
  ] = useState([]);

  ///////////////////////////////Buy A Get B For % Discount////////////////////////////////////////////

  const [percentageDiscount, setPercentageDiscount] = useState('0');

  ///////////////////////////////Buy AB Get Voucher////////////////////////////////////////////

  const [selectedVoucher, setSelectedVoucher] = useState([]);
  const [selectedVoucherItems, setSelectedVoucherItems] = useState([]);
  const merchantVouchers = CommonStore.useState((s) => s.merchantVouchers);

  ///////////////////////////////Delivery////////////////////////////////////////////

  const [myTextInput, setMyTextInput] = useState(React.createRef());
  const [deliveryFreeFlag, setDeliveryFreeFlag] = useState(false);
  const [deliveryFreeAboveAmount, setDeliveryFreeAboveAmount] = useState('0');
  const [deliveryDiscountAmount, setDeliveryDiscountAmount] = useState('0');
  const [deliveryDiscountAboveAmount, setDeliveryDiscountAboveAmount] =
    useState('0');

  ///////////////////////////////////Takeaway////////////////////////////////////////////////////////

  const [takeawayFreeFlag, setTakeawayFreeFlag] = useState(false);
  const [takeawayFreeAboveAmount, setTakeawayFreeAboveAmount] = useState('0');
  const [takeawayDiscountAmount, setTakeawayDiscountAmount] = useState('0');
  const [takeawayDiscountAboveAmount, setTakeawayDiscountAboveAmount] =
    useState('0');

  // common

  const [minSpend, setMinSpend] = useState('0');

  ///////////////////////////////////////////////////////////////////////////////////////////

  // Notification

  const [isEditNotification, setIsEditNotification] = useState(true);
  const [notificationTitle, setNotificationTitle] = useState('');
  const [isPushToSMS, setIsPushToSMS] = useState(false);
  const [isPushToApp, setIsPushToApp] = useState(true);
  const [isPushToEmail, setIsPushToEmail] = useState(false);
  const [notificationDescription, setNotificationDescription] = useState('Hi %userName%, we have sent you a voucher of a free cup of coffee, visit %outletName% to redeem now! Expires on %expiryDate%');
  const [showNotificationDatePicker, setShowNotificationDatePicker] =
    useState(false);
  const [showNotificationTimePicker, setShowNotificationTimePicker] =
    useState(false);
  const [notificationDate, setNotificationDate] = useState(moment());
  const [notificationTime, setNotificationTime] = useState(moment());
  const [notificationPromoCode, setNotificationPromoCode] = useState('');
  const [wordCount, setWordCount] = useState();

  ///////////////////////////////////////////////////////////////////////////////////////////

  // const [variationDropdownList, setVariationDropdownList] = useState(PROMOTION_TYPE_VARIATION_DROPDOWN_LIST);
  const [selectedVariation, setSelectedVariation] = useState(
    PROMOTION_TYPE_VARIATION.SPECIFIC_PRODUCTS,
  );

  const [variationItemsDropdownList, setVariationItemsDropdownList] = useState(
    [],
  );
  const [selectedVariationItems, setSelectedVariationItems] = useState([]);

  const [selectedVariationItemsSku, setSelectedVariationItemsSku] = useState(
    [],
  ); // for multi-outlets

  const [taxDropdownList, setTaxDropdownList] = useState([]);
  const [selectedTax, setSelectedTax] = useState('');
  const [selectedTaxName, setSelectedTaxName] = useState('');
  const [selectedTaxRate, setSelectedTaxRate] = useState(0);

  const [crmUserTagsDropdownList, setCrmUserTagsDropdownList] = useState([]);
  const [crmSegmentsDropdownList, setCrmSegmentsDropdownList] = useState([]);

  const [priceMin, setPriceMin] = useState('');
  const [priceMax, setPriceMax] = useState('');
  const [quantityMin, setQuantityMin] = useState('');
  const [quantityMax, setQuantityMax] = useState('');

  const [smsText, setSmsText] = useState('Hi %userName%, we have sent you a voucher of a free cup of coffee, visit %outletName% to redeem now! Expires on %expiryDate%');

  ///////////////////////////////////////////////////////////////////////////////////////////

  // const [outletItems, setOutletItems] = useState([]);
  const [outletCategories, setOutletCategories] = useState([]);

  const allOutlets = MerchantStore.useState((s) => s.allOutlets);
  const currOutletId = MerchantStore.useState((s) => s.currOutletId);
  // const outletItemsUnsorted = OutletStore.useState((s) => s.outletItems);
  const outletCategoriesUnsorted = OutletStore.useState(
    (s) => s.outletCategories,
  );
  const outletCategoriesDict = OutletStore.useState(
    (s) => s.outletCategoriesDict,
  );
  const outletsTaxDict = OutletStore.useState((s) => s.outletsTaxDict);
  const currOutletTaxes = CommonStore.useState((s) => s.currOutletTaxes);
  const isLoading = CommonStore.useState((s) => s.isLoading);

  const selectedUpsellingCampaignEdit = CommonStore.useState(
    (s) => s.selectedUpsellingCampaignEdit,
  );

  const merchantId = UserStore.useState((s) => s.merchantId);

  const allOutletsCategoriesUnique = OutletStore.useState(
    (s) => s.allOutletsCategoriesUnique,
  );

  const crmUserTags = OutletStore.useState((s) => s.crmUserTags);
  const crmSegments = OutletStore.useState((s) => s.crmSegments);

  ///////////////////////////////////////////////////////////////////////////////////////////
  //Push Button
  const [promotionsNotifications, setPromotionsNotifications] = useState([]);
  const loyaltyCampaigns = OutletStore.useState((s) => s.loyaltyCampaigns);
  //const [isAutoPush, setIsAutoPush] = useState(false);
  //const [isLoading1, setIsLoading1] = useState(false);
  const [promotionPushed, setPromotionPushed] = useState(false);

  const outletSelectDropdownView = CommonStore.useState(
    (s) => s.outletSelectDropdownView,
  );

  const [importModal, setImportModal] = useState(false);
  const [showImportListModal, setShowImportListModal] = useState(false);
  const [importSmsModal, setImportSmsModal] = useState(false);

  //////////////////////////////////////////////////////////////////////////////////////////
  //////////////////////////////////////////////////////////////////////////////////////////
  //////////////////////////////////////////////////////////////////////////////////////////

  // loyalty campaign type

  const [selectedLoyaltyCampaignType, setSelectedLoyaltyCampaignType] =
    useState(LOYALTY_CAMPAIGN_DROPDOWN_LIST[0].value);

  const [
    showLoyaltyCampaignSendTimePicker,
    setShowLoyaltyCampaignSendTimePicker,
  ] = useState(false);
  const [loyaltyCampaignSendTime, setLoyaltyCampaignSendTime] = useState(
    moment(),
  );

  const [loyaltyCampaignExpirationDays, setLoyaltyCampaignExpirationDays] =
    useState('30');

  const [
    loyaltyCampaignGuestNotVisitedDays,
    setLoyaltyCampaignGuestNotVisitedDays,
  ] = useState('');
  const [
    loyaltyCampaignGuestBirthdayBeforeDays,
    setLoyaltyCampaignGuestBirthdayBeforeDays,
  ] = useState('');
  const [
    loyaltyCampaignGuestAfterEveryVisits,
    setLoyaltyCampaignGuestAfterEveryVisits,
  ] = useState('');
  const [
    loyaltyCampaignGuestAfterEverySpends,
    setLoyaltyCampaignGuestAfterEverySpends,
  ] = useState('');

  //////////////////////////////////////////////////////////////////////////////////////////
  //////////////////////////////////////////////////////////////////////////////////////////

  // const [voucherCodeFormat, setVoucherCodeFormat] = useState(
  //   MERCHANT_VOUCHER_CODE_FORMAT.UNIQUE,
  // );

  // const [voucherCodeValueGeneric, setVoucherCodeValueGeneric] = useState(
  //   '',
  // );

  const [taggableVoucherDropdownList, setTaggableVoucherDropdownList] = useState([]);
  const [taggableVoucherId, setTaggableVoucherId] = useState('');

  const taggableVouchers = OutletStore.useState(s => s.taggableVouchers);

  const isAlphaUser = UserStore.useState((s) => s.isAlphaUser);
  const expandTab = CommonStore.useState((s) => s.expandTab);
  const currPageStack = CommonStore.useState((s) => s.currPageStack);

  ///////////////////////////////////////////////////////////

  // 2023-02-02 - Upselling campaign changes

  const [productList, setProductList] = useState([
    {
      levelId: uuidv4(),
      levelName: '',

      productName: '',
      productId: '',
      productSku: '',
      productPrice: 0,

      upsellPrice: '',

      tagIdList: [],

      orderIndex: 0,
      isActive: true,

      upsellByType: UPSELL_BY_TYPE.ORDER_ITEM,
    },
  ]);
  const [linkedPromotionId, setLinkedPromotionId] = useState('');
  const [linkedPromotionName, setLinkedPromotionName] = useState('');

  const [userTagDropdownList, setUserTagDropdownList] = useState([]);

  //////////////////////////////////////////////////////////////////////////////////////////

  // 2023-04-17 - Upselling section

  const [upsellingSection, setUpsellingSection] =
    useState(UPSELLING_SECTION_DROPDOWN_LIST[0].value);

  //////////////////////////////////////////////////////////////////////////////////////////

  const [outletItems, setOutletItems] = useState([]);

  const outletItemsUnsorted = OutletStore.useState(s => s.outletItems);

  useEffect(() => {
    var outletItemsTemp = outletItemsUnsorted.filter(item => outletCategoriesDict[item.categoryId] ? true : false)

    outletItemsTemp.sort((a, b) => a.name.localeCompare(b.name));

    setOutletItems(outletItemsTemp);
  }, [outletItemsUnsorted, outletCategoriesDict]);

  useEffect(() => {

    setVariationItemsDropdownList(outletItems.map(item => ({ label: item.name, value: item.uniqueId, sku: item.sku, price: item.price, })));

    // setVariationItemsProducts(outletItems.map(item => ({ label: item.name, value: item.uniqueId })));
    // setVariationItemsCategories(allOutletsCategoriesUnique.map(item => ({ label: item.name, value: item.uniqueId })));
  }, [
    outletItems,
    // outletCategories, 

    // allOutletsCategoriesUnique,

    // selectedVariation,

    // selectedVariationItems,

    // selectedPromotionEdit,
  ]);

  useEffect(() => {
    if (variationItemsDropdownList.length > 0 && productList.find(product => product.productId === '')) {
      var productListTemp = [];

      for (var i = 0; i < productList.length; i++) {
        if (productList[i].productId === '') {
          productListTemp.push({
            ...productList[i],
            productId: variationItemsDropdownList[0].value,
            productSku: variationItemsDropdownList[0].sku,
            productName: variationItemsDropdownList[0].label,
            productPrice: variationItemsDropdownList[0].price,

            upsellPrice: variationItemsDropdownList[0].price.toFixed(2),

            upsellByType: variationItemsDropdownList[0].upsellByType ? variationItemsDropdownList[0].upsellByType : UPSELL_BY_TYPE.ORDER_ITEM,
          });
        }
        else {
          productListTemp.push(productList[i]);
        }
      }

      setProductList(productListTemp);
    }
  }, [
    variationItemsDropdownList,

    productList,
  ]);

  //////////////////////////////////////////////////////////////////////////////////////////

  useEffect(() => {
    if (crmUserTags.length > 0) {
      setUserTagDropdownList(
        crmUserTags
          // .filter((item) => {
          //   var isExisted = false;

          //   for (var i = 0; i < userTagList.length; i++) {
          //     if (userTagList[i].uniqueId === item.uniqueId) {
          //       isExisted = true;
          //       break;
          //     }
          //   }

          //   return !isExisted;
          // })
          .map((item) => {
            return { label: item.name, value: item.uniqueId };
          }),
      );
    }
  }, [crmUserTags,
    // userTagList,
  ]);

  const setState = () => { };

  // useEffect(() => {
  //   if (outletsTaxDict[currOutletId] && outletsTaxDict[currOutletId].length > 0) {
  //     setTaxDropdownList(outletsTaxDict[currOutletId].map(item => ({ label: item.name, value: item.uniqueId })));

  //     if (selectedTax === '') {
  //       setSelectedTax(outletsTaxDict[currOutletId][0]);
  //     }
  //   }
  // }, [outletsTaxDict, currOutletId]);

  /////////////////////////////////////////////////////////////

  {/**Skip */ }
  const confirmDeletePromotion = () => {
    return Alert.alert(
      'Delete',
      'Are you sure you want to remove this promotion?',
      [
        {
          text: 'YES',
          onPress: () => {
            deletePromotion(item);
          },
        },
        {
          text: 'NO',
          onPress: () => { },
        },
      ],
    );
  };

  /////////////////////////////////////////////////////////////

  // quick hack to allow enough time to load/populate dropdown items first (product/category picker)

  const [dataBackup, setDataBackup] = useState(null);

  const isUpsellingCampaignScreenMounted = CommonStore.useState(s => s.isUpsellingCampaignScreenMounted);

  useEffect(() => {
    if (!isUpsellingCampaignScreenMounted && selectedUpsellingCampaignEdit && !dataBackup) {
      setDataBackup(selectedUpsellingCampaignEdit);

      CommonStore.update(s => {
        s.selectedUpsellingCampaignEdit = null;
      });
    }
  }, [selectedUpsellingCampaignEdit]);

  useEffect(() => {
    if (!isUpsellingCampaignScreenMounted && dataBackup) {
      setTimeout(() => {
        CommonStore.update(s => {
          s.isUpsellingCampaignScreenMounted = true;

          s.selectedUpsellingCampaignEdit = dataBackup;
        });

        setDataBackup(null);
      }, 1000);
    }
  }, [dataBackup]);

  /////////////////////////////////////////////////////////////

  useEffect(() => {
    // console.log('================================');
    // console.log('selectedUpsellingCampaignEdit');
    // console.log(selectedUpsellingCampaignEdit);

    if (
      selectedUpsellingCampaignEdit
      // currOutletTaxes.length > 0 &&
      // && variationItemsDropdownList.length > 0
      // taxDropdownList.length > 0
    ) {
      // insert info

      setCampaignName(selectedUpsellingCampaignEdit.campaignName);
      setCampaignDescription(selectedUpsellingCampaignEdit.campaignDescription);
      setIsEnableSellOnline(selectedUpsellingCampaignEdit.isEnableSellOnline);
      setSelectedAvailability(
        selectedUpsellingCampaignEdit.orderTypes || [
          ORDER_TYPE_DROP_DOWN_LIST[0].value,
        ],
      );
      setSelectedTargetUserGroup(selectedUpsellingCampaignEdit.targetUserGroup);
      if (selectedUpsellingCampaignEdit.targetSegmentGroupList) {
        const updatedSegmentGroupList = selectedUpsellingCampaignEdit.targetSegmentGroupList.filter(
          (segment) =>
            CRM_SEGMENT_DROPDOWN_LIST.concat(crmSegmentsDropdownList).some(
              (dropdownSegment) => dropdownSegment.value === segment
            )
        );
        setSelectedTargetSegmentGroupList(
          updatedSegmentGroupList
        );
      }

      setUpsellingSection(selectedUpsellingCampaignEdit.upsellingSection ? selectedUpsellingCampaignEdit.upsellingSection : UPSELLING_SECTION_DROPDOWN_LIST[0].value);

      if (selectedUpsellingCampaignEdit.effectiveType) {
        setSelectedEffectiveType(selectedUpsellingCampaignEdit.effectiveType);
      } else {
        setSelectedEffectiveType(EFFECTIVE_TYPE.DAY);
      }
      if (selectedUpsellingCampaignEdit.effectiveTypeOptions) {
        // setSelectedEffectiveTypeOptions(
        //   selectedUpsellingCampaignEdit.effectiveTypeOptions,
        // );

        var effectiveTypeOptionsTemp = [];

        for (var i = 0; i < selectedUpsellingCampaignEdit.effectiveTypeOptions.length; i++) {
          if (EFFECTIVE_DAY_DROPDOWN_LIST.find(item => item.value === selectedUpsellingCampaignEdit.effectiveTypeOptions[i])) {
            effectiveTypeOptionsTemp.push(selectedUpsellingCampaignEdit.effectiveTypeOptions[i]);
          }
        }
        setSelectedEffectiveTypeOptions(effectiveTypeOptionsTemp);
      }
      setSelectedEffectiveDay(selectedUpsellingCampaignEdit.effectiveDay);
      setEffectiveTimeStart(
        selectedUpsellingCampaignEdit.effectiveTimeStart
          ? selectedUpsellingCampaignEdit.effectiveTimeStart
          : moment(),
      );
      setEffectiveTimeEnd(
        selectedUpsellingCampaignEdit.effectiveTimeEnd
          ? selectedUpsellingCampaignEdit.effectiveTimeEnd
          : moment(),
      );

      setMinSpend(
        selectedUpsellingCampaignEdit.minSpend
          ? selectedUpsellingCampaignEdit.minSpend.toFixed(2)
          : '0',
      );

      setPromoCode(selectedUpsellingCampaignEdit.promoCode);
      setIsPromoCodeUsageLimit(
        selectedUpsellingCampaignEdit.isPromoCodeUsageLimit,
      );
      if (selectedUpsellingCampaignEdit.promoCodeUsageLimit) {
        setPromoCodeUsageLimit(
          selectedUpsellingCampaignEdit.promoCodeUsageLimit.toFixed(0),
        );
      }
      setPromoDateStart(selectedUpsellingCampaignEdit.promoDateStart);
      setPromoDateEnd(selectedUpsellingCampaignEdit.promoDateEnd);
      setPromoTimeStart(selectedUpsellingCampaignEdit.promoTimeStart);
      setPromoTimeEnd(selectedUpsellingCampaignEdit.promoTimeEnd);
      setImage(selectedUpsellingCampaignEdit.image);
      setIsImageChanged(false);
      setSelectedOutletList(selectedUpsellingCampaignEdit.outletIdList);

      setSelectedPromotionType(selectedUpsellingCampaignEdit.promotionType);

      // setSmsText(selectedUpsellingCampaignEdit.notificationText || '');

      setIsEditNotification(true);

      if (selectedUpsellingCampaignEdit.notification) {
        setNotificationTitle(selectedUpsellingCampaignEdit.notification.title);
        setIsPushToSMS(selectedUpsellingCampaignEdit.notification.isPushToSMS);
        setIsPushToApp(selectedUpsellingCampaignEdit.notification.isPushToApp);
        setIsPushToEmail(
          selectedUpsellingCampaignEdit.notification.isPushToEmail,
        );
        setNotificationDescription(
          selectedUpsellingCampaignEdit.notification.description,
        );
        setNotificationDate(selectedUpsellingCampaignEdit.notification.date);
        setNotificationTime(selectedUpsellingCampaignEdit.notification.time);
        setNotificationPromoCode(
          selectedUpsellingCampaignEdit.notification.promoCode,
        );

        setSmsText(
          selectedUpsellingCampaignEdit.notification.description,
        );
      } else {
        setNotificationTitle('');
        setIsPushToSMS(false);
        setIsPushToApp(true);
        setIsPushToEmail(false);
        setNotificationDescription('Hi %userName%, we have sent you a voucher of a free cup of coffee, visit %outletName% to redeem now! Expires on %expiryDate%');
        setNotificationDate(moment());
        setNotificationTime(moment());
        setNotificationPromoCode('');

        setSmsText(
          'Hi %userName%, we have sent you a voucher of a free cup of coffee, visit %outletName% to redeem now! Expires on %expiryDate%'
        );
      }

      //////////////////////////////////////////////////////////////////

      setSelectedLoyaltyCampaignType(
        selectedUpsellingCampaignEdit.loyaltyCampaignType,
      );

      if (
        selectedUpsellingCampaignEdit.loyaltyCampaignType ===
        LOYALTY_CAMPAIGN_TYPE.FIRST_VISIT
      ) {
        for (
          var i = 0;
          i < selectedUpsellingCampaignEdit.loyaltyCriteriaList.length;
          i++
        ) {
          const criteria = selectedUpsellingCampaignEdit.loyaltyCriteriaList[i];

          setLoyaltyCampaignSendTime(criteria.loyaltyCampaignSendTime);
          setLoyaltyCampaignExpirationDays(
            criteria.loyaltyCampaignExpirationDays.toFixed(0),
          );
        }
      } else if (
        selectedUpsellingCampaignEdit.loyaltyCampaignType ===
        LOYALTY_CAMPAIGN_TYPE.AT_RISK
      ) {
        for (
          var i = 0;
          i < selectedUpsellingCampaignEdit.loyaltyCriteriaList.length;
          i++
        ) {
          const criteria = selectedUpsellingCampaignEdit.loyaltyCriteriaList[i];

          setLoyaltyCampaignSendTime(criteria.loyaltyCampaignSendTime);
          setLoyaltyCampaignExpirationDays(
            criteria.loyaltyCampaignExpirationDays.toFixed(0),
          );
          setLoyaltyCampaignGuestNotVisitedDays(
            criteria.loyaltyCampaignGuestNotVisitedDays.toFixed(0),
          );
        }
      } else if (
        selectedUpsellingCampaignEdit.loyaltyCampaignType ===
        LOYALTY_CAMPAIGN_TYPE.BIRTHDAY
      ) {
        for (
          var i = 0;
          i < selectedUpsellingCampaignEdit.loyaltyCriteriaList.length;
          i++
        ) {
          const criteria = selectedUpsellingCampaignEdit.loyaltyCriteriaList[i];

          setLoyaltyCampaignSendTime(criteria.loyaltyCampaignSendTime);
          setLoyaltyCampaignExpirationDays(
            criteria.loyaltyCampaignExpirationDays.toFixed(0),
          );
          setLoyaltyCampaignGuestBirthdayBeforeDays(
            criteria.loyaltyCampaignGuestBirthdayBeforeDays.toFixed(0),
          );
        }
      } else if (
        selectedUpsellingCampaignEdit.loyaltyCampaignType ===
        LOYALTY_CAMPAIGN_TYPE.GROWTH
      ) {
        for (
          var i = 0;
          i < selectedUpsellingCampaignEdit.loyaltyCriteriaList.length;
          i++
        ) {
          const criteria = selectedUpsellingCampaignEdit.loyaltyCriteriaList[i];

          setLoyaltyCampaignSendTime(criteria.loyaltyCampaignSendTime);
          setLoyaltyCampaignExpirationDays(
            criteria.loyaltyCampaignExpirationDays.toFixed(0),
          );
          setLoyaltyCampaignGuestAfterEveryVisits(
            criteria.loyaltyCampaignGuestAfterEveryVisits.toFixed(0),
          );
        }
      } else if (
        selectedUpsellingCampaignEdit.loyaltyCampaignType ===
        LOYALTY_CAMPAIGN_TYPE.BIG_SPENDER
      ) {
        for (
          var i = 0;
          i < selectedUpsellingCampaignEdit.loyaltyCriteriaList.length;
          i++
        ) {
          const criteria = selectedUpsellingCampaignEdit.loyaltyCriteriaList[i];

          setLoyaltyCampaignSendTime(criteria.loyaltyCampaignSendTime);
          setLoyaltyCampaignExpirationDays(
            criteria.loyaltyCampaignExpirationDays.toFixed(0),
          );
          setLoyaltyCampaignGuestAfterEverySpends(
            criteria.loyaltyCampaignGuestAfterEverySpends.toFixed(2),
          );
        }
      } else if (
        selectedUpsellingCampaignEdit.loyaltyCampaignType ===
        LOYALTY_CAMPAIGN_TYPE.SIGN_UP
      ) {
        for (
          var i = 0;
          i < selectedUpsellingCampaignEdit.loyaltyCriteriaList.length;
          i++
        ) {
          const criteria = selectedUpsellingCampaignEdit.loyaltyCriteriaList[i];

          setLoyaltyCampaignSendTime(criteria.loyaltyCampaignSendTime);
          setLoyaltyCampaignExpirationDays(
            criteria.loyaltyCampaignExpirationDays.toFixed(0),
          );
        }
      }
      else if (
        selectedUpsellingCampaignEdit.loyaltyCampaignType ===
        LOYALTY_CAMPAIGN_TYPE.VOUCHER
      ) {
        for (
          var i = 0;
          i < selectedUpsellingCampaignEdit.loyaltyCriteriaList.length;
          i++
        ) {
          const criteria = selectedUpsellingCampaignEdit.loyaltyCriteriaList[i];

          setLoyaltyCampaignSendTime(criteria.loyaltyCampaignSendTime);
          setLoyaltyCampaignExpirationDays(
            criteria.loyaltyCampaignExpirationDays.toFixed(0),
          );
        }
      }

      //////////////////////////////////////////////////////////////////

      setTaggableVoucherId(selectedUpsellingCampaignEdit.taggableVoucherId || '');

      setLinkedPromotionId(selectedUpsellingCampaignEdit.linkedPromotionId || '');
      setLinkedPromotionName(selectedUpsellingCampaignEdit.linkedPromotionName || '');

      setProductList(selectedUpsellingCampaignEdit.productList.map(product => ({
        ...product,
        upsellPrice: product.upsellPrice ? product.upsellPrice.toFixed(2) : product.productPrice.toFixed(2),
        upsellByType: product.upsellByType ? product.upsellByType : UPSELL_BY_TYPE.ORDER_ITEM,
      })));

      //////////////////////////////////////////////////////////////////

      // var selectedOutletListTemp = [];
      // if (allOutletsItemsSkuDict[selectedUpsellingCampaignEdit.sku]) {
      //   for (var i = 0; i < allOutletsItemsSkuDict[selectedUpsellingCampaignEdit.sku].length; i++) {
      //     selectedOutletListTemp.push(allOutletsItemsSkuDict[selectedUpsellingCampaignEdit.sku][i].outletId);
      //   }
      // }
      // setSelectedOutletList(selectedOutletListTemp);

      // setPlace(selectedUpsellingCampaignEdit.outletId);
      // setItemName(selectedUpsellingCampaignEdit.name);
      // setItemDescription(selectedUpsellingCampaignEdit.description);
      // setImage(selectedUpsellingCampaignEdit.image);
      // setItemPrice(selectedUpsellingCampaignEdit.price.toFixed(2));

      // setSelectedOutletCategoryId(selectedUpsellingCampaignEdit.categoryId);

      // if (selectedUpsellingCampaignEdit.stockLinkItems) {
      //   setStockLinkItems(selectedUpsellingCampaignEdit.stockLinkItems);
      // }
    } else {
      // designed to always mounted, thus need clear manually...

      setCampaignName('');
      setCampaignDescription('');
      setIsEnableSellOnline(false);
      setSelectedAvailability([ORDER_TYPE_DROP_DOWN_LIST[0].value]);
      setSelectedTargetUserGroup(TARGET_USER_GROUP_DROPDOWN_LIST[0].value);
      setSelectedTargetSegmentGroupList([CRM_SEGMENT_DROPDOWN_LIST[0].value]);

      setUpsellingSection(UPSELLING_SECTION_DROPDOWN_LIST[0].value);

      setSelectedEffectiveType(EFFECTIVE_TYPE.DAY);
      setSelectedEffectiveTypeOptions([EFFECTIVE_DAY_DROPDOWN_LIST[0].value]);
      setSelectedEffectiveDay(EFFECTIVE_DAY_DROPDOWN_LIST[0].value);
      setPromoCode('');
      setIsPromoCodeUsageLimit(false);
      setPromoCodeUsageLimit('');
      setPromoDateStart(moment());
      setPromoDateEnd(moment());
      setPromoTimeStart(moment());
      setPromoTimeEnd(moment());
      setImage('');
      setIsImageChanged(false);
      setSelectedOutletList([]);

      setSelectedPromotionType(LOYALTY_PROMOTION_TYPE_DROPDOWN_LIST[0].value);

      // setSmsText('');

      setPackageItemPrice('0');
      setPackageMinQuantity('0');
      setPackagePriceFinal(0);

      setOverrideItemPrice('0');
      setOverridePriceFinal(0);

      // if (currOutletTaxes.length > 0) {
      //   setSelectedTax(currOutletTaxes[0].uniqueId);
      //   setSelectedTaxName(currOutletTaxes[0].name);
      //   setSelectedTaxRate(currOutletTaxes[0].rate);

      //   setPackageTax(currOutletTaxes[0].rate);
      //   setOverrideTax(currOutletTaxes[0].rate);
      // } else {
      //   setSelectedTax('');
      //   setSelectedTaxName('');
      //   setSelectedTaxRate(0);

      //   setPackageTax('');
      //   setOverrideTax('');
      // }

      setPriceMin('');
      setPriceMax('');
      setQuantityMin('');
      setQuantityMax('');

      // setSelectedVariation(PROMOTION_TYPE_VARIATION.SPECIFIC_PRODUCTS);
      // setSelectedVariationItems([]);
      // setSelectedVariationItemsSku([]);

      setOverrideItems([
        {
          priceBeforeTax: 0,
          variation: PROMOTION_TYPE_VARIATION.SPECIFIC_PRODUCTS,
          variationItems: [],
          variationItemsSku: [],
        },
      ]);

      //////////////////////////////////////////////

      setSelectedVariationB1F1(PROMOTION_TYPE_VARIATION.SPECIFIC_PRODUCTS);
      setSelectedVariationItemsB1F1([]);
      setSelectedVariationItemsSkuB1F1([]);

      //////////////////////////////////////////////

      setIsEditNotification(true);
      setNotificationTitle('');
      setIsPushToSMS(false);
      setIsPushToApp(true);
      setIsPushToEmail(false);
      setNotificationDescription('Hi %userName%, we have sent you a voucher of a free cup of coffee, visit %outletName% to redeem now! Expires on %expiryDate%');
      setNotificationDate(moment());
      setNotificationTime(moment());
      setNotificationPromoCode('');

      setSmsText('Hi %userName%, we have sent you a voucher of a free cup of coffee, visit %outletName% to redeem now! Expires on %expiryDate%');

      //////////////////////////////////////////////

      setSelectedLoyaltyCampaignType(LOYALTY_CAMPAIGN_DROPDOWN_LIST[0].value);
      setLoyaltyCampaignSendTime(moment().valueOf());
      setLoyaltyCampaignExpirationDays('30');
      setLoyaltyCampaignGuestNotVisitedDays('');
      setLoyaltyCampaignGuestBirthdayBeforeDays('');
      setLoyaltyCampaignGuestAfterEveryVisits('');
      setLoyaltyCampaignGuestAfterEverySpends('');

      //////////////////////////////////////////////

      setTaggableVoucherId('');

      setLinkedPromotionId('');
      setLinkedPromotionName('');

      setProductList([
        {
          levelId: uuidv4(),
          levelName: '',

          productName: '',
          productId: '',
          productSku: '',
          productPrice: 0,

          upsellPrice: '',

          tagIdList: [],

          orderIndex: 0,
          isActive: true,

          upsellByType: UPSELL_BY_TYPE.ORDER_ITEM,
        },
      ]);

      //////////////////////////////////////////////

      // setSelectedOutletList([]);

      // setPlace('');
      // setItemSKU('');
      // setItemName('');
      // setItemDescription('');
      // setImage('');
      // setItemPrice('0.00');

      // if (outletSupplyItems.length > 0) {
      //   setStockLinkItems([
      //     {
      //       outletSupplyItemId: outletSupplyItems[0].uniqueId,
      //       sku: outletSupplyItems[0].sku,
      //       name: outletSupplyItems[0].name,
      //       unit: outletSupplyItems[0].unit,
      //       quantityUsage: 0,
      //     }
      //   ]);

      // }
      // else {
      //   setStockLinkItems([
      //     {
      //       outletSupplyItemId: '',
      //       sku: '',
      //       name: '',
      //       unit: '',
      //       quantityUsage: 0,
      //     }
      //   ]);

      // }

      // setIsImageChanged(false);

      // setVariantGroupList([]);
      // setAddOnGroupList([]);
    }
  }, [
    selectedUpsellingCampaignEdit,
    currOutletTaxes,
    // variationItemsDropdownList,
    taxDropdownList,
  ], [selectedUpsellingCampaignEdit]);

  /////////////////////////////////////////////////////////////

  useEffect(() => {
    setTaggableVoucherDropdownList(
      [
        {
          label: 'N/A',
          value: '',
        }
      ].concat(
        taggableVouchers.map((item) => ({ label: `${item.campaignName} [${item.voucherType.replaceAll('_', ' ')}]`, value: item.uniqueId })),
      ),
    );
  }, [taggableVouchers]);

  /////////////////////////////////////////////////////////////

  useEffect(() => {
    // console.log('crmUserTagsDropdownList');
    // console.log(
    //   TARGET_USER_GROUP_DROPDOWN_LIST.concat(
    //     crmUserTags.map((item) => ({ label: item.name, value: item.uniqueId })),
    //   ),
    // );

    setCrmUserTagsDropdownList(
      TARGET_USER_GROUP_DROPDOWN_LIST.concat(
        crmUserTags.map((item) => ({ label: item.name, value: item.uniqueId })),
      ),
    );
  }, [crmUserTags]);

  useEffect(() => {
    var crmSegmentsDropdownListTemp = crmSegments.map((segment) => ({
      label: segment.name,
      value: segment.uniqueId,
    }));

    setCrmSegmentsDropdownList(
      crmSegmentsDropdownListTemp
    );

    if (selectedTargetSegmentGroupList.length > 0) {
      var selectedTargetSegmentGroupListTemp = [];

      var combinedCrmSegmentsDropdownListTemp = CRM_SEGMENT_DROPDOWN_LIST.concat(crmSegmentsDropdownListTemp);

      for (var i = 0; i < selectedTargetSegmentGroupList.length; i++) {
        if (combinedCrmSegmentsDropdownListTemp.find(item => item.value === selectedTargetSegmentGroupList[i])) {
          selectedTargetSegmentGroupListTemp.push(selectedTargetSegmentGroupList[i]);
        }
      }

      var isChanged = false;
      if (selectedTargetSegmentGroupList.length !== selectedTargetSegmentGroupListTemp.length) {
        isChanged = true;
      } else {
        for (var i = 0; i < selectedTargetSegmentGroupList.length; i++) {
          const isEqual = selectedTargetSegmentGroupList[i] === selectedTargetSegmentGroupListTemp[i];

          if (!isEqual) {
            isChanged = true;
            break;
          }
        }
      }

      if (isChanged) {
        setSelectedTargetSegmentGroupList(selectedTargetSegmentGroupListTemp);
      }
    }
  }, [crmSegments, selectedTargetSegmentGroupList]);

  useEffect(() => {
    if (
      selectedPromotionType ===
      LOYALTY_PROMOTION_TYPE.COMBO_SET_OR_PACKAGE_OR_BUNDLE &&
      packageItemPrice.length > 0 &&
      parseFloat(packageItemPrice) >= 0
    ) {
      const packageItemPriceFloat = parseFloat(packageItemPrice);

      setPackagePriceFinal(
        packageItemPriceFloat + packageItemPriceFloat * selectedTaxRate,
      );
    } else if (
      selectedPromotionType ===
      LOYALTY_PROMOTION_TYPE.OVERRIDE_EXISTING_PRICE &&
      overrideItemPrice.length > 0 &&
      parseFloat(overrideItemPrice) >= 0
    ) {
      const overrideItemPriceFloat = parseFloat(overrideItemPrice);

      setOverridePriceFinal(
        overrideItemPriceFloat + overrideItemPriceFloat * selectedTaxRate,
      );
    }
  }, [
    selectedPromotionType,

    packageItemPrice,
    overrideItemPrice,

    selectedTaxRate,
  ]);

  /////////////////////////////////////////////////////////////

  // useEffect(() => {
  //   if (currOutletTaxes.length > 0) {
  //     setTaxDropdownList(
  //       currOutletTaxes.map((item) => ({
  //         label: item.name,
  //         value: item.uniqueId,
  //       })),
  //     );

  //     if (selectedTax === '') {
  //       setSelectedTax(currOutletTaxes[0].uniqueId);

  //       for (var i = 0; i < currOutletTaxes.length; i++) {
  //         if (currOutletTaxes[i].uniqueId === currOutletTaxes[0].uniqueId) {
  //           setSelectedTaxName(currOutletTaxes[i].name);
  //           setSelectedTaxRate(currOutletTaxes[i].rate);
  //           break;
  //         }
  //       }
  //     }
  //   }
  // }, [currOutletTaxes, currOutletId]);

  ////////////////////////////////////////////////////////
  // useEffect(() => {
  //   //var selectedVariationItemsSkuTemp = [];
  //   var selectedVoucherTemp = [];

  //   // if (selectedVariation === PROMOTION_TYPE_VARIATION.SPECIFIC_PRODUCTS) {
  //   //   for (var i = 0; i < outletItems.length; i++) {
  //   //     if (selectedVariationItems.includes(outletItems[i].uniqueId)) {
  //   //       selectedVariationItemsSkuTemp.push(outletItems[i].sku);
  //   //     }
  //   //   }
  //   // }
  //   // else if (selectedVariation === PROMOTION_TYPE_VARIATION.PRODUCT_OF_CATEGORY) {
  //   //   for (var i = 0; i < allOutletsCategoriesUnique.length; i++) {
  //   //     if (selectedVariationItems.includes(allOutletsCategoriesUnique[i].uniqueId)) {
  //   //       selectedVariationItemsSkuTemp.push(allOutletsCategoriesUnique[i].name);
  //   //     }
  //   //   }
  //   // }

  //   for (var i=0; i < merchantVouchers.length; i++){
  //     if (selectedVoucherItems.includes(merchantVouchers[i].uniqueId)){
  //       selectedVoucherTemp.push(merchantVouchers[i].name);
  //     }
  //   }

  //   setSelectedVoucher(selectedVoucherTemp);
  // }, [
  //   selectedVoucher,
  //   merchantVouchers,
  // ]);

  ////////////////////////////////////////

  // change when pick better

  useEffect(() => {
    // var overrideItemsSkuArrayChangesList = [];

    var isDiff = false;

    var overrideItemsTemp = [];

    for (var index = 0; index < overrideItems.length; index++) {
      var skuArray = [];

      if (
        overrideItems[index].variation ===
        PROMOTION_TYPE_VARIATION.SPECIFIC_PRODUCTS
      ) {
        for (var i = 0; i < outletItems.length; i++) {
          if (
            overrideItems[index].variationItems.includes(
              outletItems[i].uniqueId,
            )
          ) {
            skuArray.push(outletItems[i].sku);
          }
        }
      } else if (
        overrideItems[index].variation ===
        PROMOTION_TYPE_VARIATION.PRODUCT_OF_CATEGORY
      ) {
        for (var i = 0; i < allOutletsCategoriesUnique.length; i++) {
          if (
            overrideItems[index].variationItems.includes(
              allOutletsCategoriesUnique[i].uniqueId,
            )
          ) {
            skuArray.push(allOutletsCategoriesUnique[i].name);
          }
        }
      }

      isDiff = !areArraysEqual(
        skuArray,
        overrideItems[index].variationItemsSku,
      );

      var overrideItemTemp = {
        priceBeforeTax: overrideItems[index].priceBeforeTax,
        variation: overrideItems[index].variation,
        variationItems: overrideItems[index].variationItems,
        variationItemsSku: skuArray,
      };

      overrideItemsTemp.push(overrideItemTemp);

      // if (isDiff) {

      //   // setOverrideItems(overrideItems.map((overrideItem, j) => (j === i ? {
      //   //   ...overrideItem,
      //   //   variationItemsSku: skuArray,
      //   // } : overrideItem)));

      //   // break;
      // }
    }

    if (isDiff) {
      setOverrideItems(overrideItemsTemp);
    }

    // setSelectedVariationItemsSku(selectedVariationItemsSkuTemp);
  }, [
    // selectedVariation,

    // selectedVariationItems,

    overrideItems,

    outletItems,

    allOutletsCategoriesUnique,
  ]);

  useEffect(() => {
    var selectedVariationItemsSkuTemp = [];

    if (selectedVariation === PROMOTION_TYPE_VARIATION.SPECIFIC_PRODUCTS) {
      for (var i = 0; i < outletItems.length; i++) {
        if (selectedVariationItems.includes(outletItems[i].uniqueId)) {
          selectedVariationItemsSkuTemp.push(outletItems[i].sku);
        }
      }
    } else if (
      selectedVariation === PROMOTION_TYPE_VARIATION.PRODUCT_OF_CATEGORY
    ) {
      for (var i = 0; i < allOutletsCategoriesUnique.length; i++) {
        if (
          selectedVariationItems.includes(
            allOutletsCategoriesUnique[i].uniqueId,
          )
        ) {
          selectedVariationItemsSkuTemp.push(
            allOutletsCategoriesUnique[i].name,
          );
        }
      }
    }

    setSelectedVariationItemsSku(selectedVariationItemsSkuTemp);
  }, [
    selectedVariation,

    selectedVariationItems,
    outletItems,

    allOutletsCategoriesUnique,
  ]);

  useEffect(() => {
    var selectedVariationItemsSkuB1F1Temp = [];

    if (selectedVariationB1F1 === PROMOTION_TYPE_VARIATION.SPECIFIC_PRODUCTS) {
      for (var i = 0; i < outletItems.length; i++) {
        if (selectedVariationItemsB1F1.includes(outletItems[i].uniqueId)) {
          selectedVariationItemsSkuB1F1Temp.push(outletItems[i].sku);
        }
      }
    } else if (
      selectedVariationB1F1 === PROMOTION_TYPE_VARIATION.PRODUCT_OF_CATEGORY
    ) {
      for (var i = 0; i < allOutletsCategoriesUnique.length; i++) {
        if (
          selectedVariationItemsB1F1.includes(
            allOutletsCategoriesUnique[i].uniqueId,
          )
        ) {
          selectedVariationItemsSkuB1F1Temp.push(
            allOutletsCategoriesUnique[i].name,
          );
        }
      }
    }

    setSelectedVariationItemsSkuB1F1(selectedVariationItemsSkuB1F1Temp);
  }, [
    selectedVariationB1F1,

    selectedVariationItemsB1F1,
    outletItems,

    allOutletsCategoriesUnique,
  ]);

  ////////////////////////////////////////////////////////

  // to solve product not show

  useEffect(() => {
    // if (
    //   selectedVariationItemsB1F1.length > 0 &&
    //   variationItemsB1F1DropdownList.find(
    //     (item) => item.value === selectedVariationItemsB1F1[0],
    //   ) === undefined
    // ) {
    //   // setSelectedVariationItemsB1F1(variationItemsB1F1DropdownList.length > 0 ? [variationItemsB1F1DropdownList[0].uniqueId] : []);
    //   setSelectedVariationItemsB1F1([]);
    // }

    // // console.log('condition');
    // // console.log(selectedVariationItems.length > 0);
    // // console.log(
    //   variationItemsDropdownList.find(
    //     (item) => item.value === selectedVariationItems[0],
    //   ),
    // );

    if (selectedVariationItemsB1F1.length > 0 &&
      selectedVariationItemsB1F1.filter(itemId => {
        return variationItemsB1F1DropdownList.find(item2 => item2.value === itemId) ? true : false;
      }).length !== selectedVariationItemsB1F1.length
    ) {
      // setSelectedVariationItemsB1F1(variationItemsB1F1DropdownList.length > 0 ? [variationItemsB1F1DropdownList[0].uniqueId] : []);
      setSelectedVariationItemsB1F1([]);
    }

    // if (
    //   selectedVariationItems.length > 0 &&
    //   variationItemsDropdownList.find(
    //     (item) => item.value === selectedVariationItems[0],
    //   ) === undefined
    // ) {
    //   // setSelectedVariationItems(variationItemsDropdownList.length > 0 ? [variationItemsDropdownList[0].uniqueId] : []);
    //   setSelectedVariationItems([]);
    // }

    if (selectedVariationItems.length > 0 &&
      selectedVariationItems.filter(itemId => {
        return variationItemsDropdownList.find(item2 => item2.value === itemId) ? true : false;
      }).length !== selectedVariationItems.length
    ) {
      // setSelectedVariationItems(variationItemsDropdownList.length > 0 ? [variationItemsDropdownList[0].uniqueId] : []);
      // hide first
      // setSelectedVariationItems([]);

      // 2023-05-16 - Only remove those that not existed

      const existedItems = selectedVariationItems.filter(itemId => {
        return variationItemsDropdownList.find(item2 => item2.value === itemId) ? true : false;
      });

      setSelectedVariationItems(existedItems);
    }

    var overrideItemsTemp = [];

    for (var i = 0; i < overrideItems.length; i++) {
      var overrideItemTemp = {
        priceBeforeTax: overrideItems[i].priceBeforeTax,
        variation: overrideItems[i].variation,
        variationItems: overrideItems[i].variationItems,
        variationItemsSku: overrideItems[i].variationItemsSku,
      };

      if (
        overrideItems[i].variationItems.length > 0 &&
        variationItemsDropdownList.find(
          (item) => item.value === overrideItems[i].variationItems[0],
        ) === undefined
      ) {
        overrideItemTemp.variationItems = [];
      }

      overrideItemsTemp.push(overrideItemTemp);
    }

    setOverrideItems(overrideItemsTemp);
  }, [variationItemsDropdownList, variationItemsB1F1DropdownList]);

  ////////////////////////////////////////////////////////

  useEffect(() => {
    // var outletItemsTemp = [...outletItemsUnsorted];

    var outletItemsTemp = outletItemsUnsorted.filter(item => outletCategoriesDict[item.categoryId] ? true : false)

    outletItemsTemp.sort((a, b) => a.name.localeCompare(b.name));

    setOutletItems(outletItemsTemp);
  }, [outletItemsUnsorted]);

  useEffect(() => {
    var outletCategoriesTemp = [...outletCategoriesUnsorted];

    outletCategoriesTemp.sort((a, b) => a.name.localeCompare(b.name));

    setOutletCategories(outletCategoriesTemp);
  }, [outletCategoriesUnsorted]);

  useEffect(() => {
    setOutletDropdownList(
      allOutlets.map((item) => ({
        label: item.name,
        value: item.uniqueId,
      })),
    );
  }, [allOutlets]);

  // useEffect(() => {
  //   setAvailabilityDropdownList(ORDER_TYPE.map(item => ({
  //     label: item.name, value: item.uniqueId,
  //   })))
  // }, [ORDER_TYPE])

  useEffect(() => {
    setPackageTaxDropdownList();
  }, []);

  useEffect(() => {
    setPackageProductDropdownList();
  }, []);

  useEffect(() => {
    setPackageCategoryDropdownList();
  }, []);

  /////////

  useEffect(() => {
    setOverrideTaxDropdownList();
  }, []);

  useEffect(() => {
    setOverrideProductDropdownList();
  }, []);

  useEffect(() => {
    setOverrideCategoryDropdownList();
  }, []);

  ////////

  useEffect(() => {
    setCashbackExpiredDropdownList();
  }, []);

  useEffect(() => {
    setCashbackProductDropdownList();
  }, []);

  useEffect(() => {
    setCashbackCategoryDropdownList();
  }, []);

  ////////

  useEffect(() => {
    setBuy1Free1ProductDropdownList();
  }, []);

  ///////

  useEffect(() => {
    setAmountOffProductDropdownList();
  }, []);

  useEffect(() => {
    setAmountOffCategoryDropdownList();
  }, []);

  ////////

  useEffect(() => {
    setPercentageOffProductDropdownList();
  }, []);

  useEffect(() => {
    setPercentageOffCategoryDropdownList();
  }, []);

  ///////

  useEffect(() => {
    var promotionsNotificationsTemp = [];

    for (var i = 0; i < loyaltyCampaigns.length; i++) {
      if (loyaltyCampaigns[i].notification) {
        var pushToTargetArr = [];

        if (loyaltyCampaigns[i].notification.isPushToSMS) {
          pushToTargetArr.push('SMS');
        }

        // if (loyaltyCampaigns[i].notification.isPushToEmail) {
        //   pushToTargetArr.push('Email');
        // }

        if (loyaltyCampaigns[i].notification.isPushToApp) {
          pushToTargetArr.push('App');
        }

        promotionsNotificationsTemp.push({
          ...loyaltyCampaigns[i],
          notification: {
            ...loyaltyCampaigns[i].notification,
            pushTargets: pushToTargetArr.join('/'),
          },
        });
      }
    }

    setPromotionsNotifications(promotionsNotificationsTemp);

    // setCurrentPage(1);
    // setPageCount(Math.ceil(promotionsNotificationsTemp.length / perPage));
  }, [loyaltyCampaigns]);

  ///////

  //To remove unwanted sidebar
  // navigation.dangerouslyGetParent().setOptions({
  //   tabBarVisible: false,
  // });

  //Header
  navigation.setOptions({
    headerLeft: () => (
      <TouchableOpacity
        onPress={() => {
          if (isAlphaUser || true) {
            navigation.navigate('MenuOrderingScreen');

            CommonStore.update((s) => {
              s.currPage = 'MenuOrderingScreen';
              s.currPageStack = [...currPageStack, 'MenuOrderingScreen'];
            });
          }
          else {
            navigation.navigate('Table');

            CommonStore.update((s) => {
              s.currPage = 'Table';
              s.currPageStack = [...currPageStack, 'Table'];
            });
          }
          if (expandTab !== EXPAND_TAB_TYPE.OPERATION) {
            CommonStore.update((s) => {
              s.expandTab = EXPAND_TAB_TYPE.OPERATION;
            });
          }

          logEventAnalytics({
            eventName: ANALYTICS.MODULE_UPSELLING_UPSELLING_CAMPAIGN_C_LOGO,
            eventNameParsed: ANALYTICS_PARSED.MODULE_UPSELLING_UPSELLING_CAMPAIGN_C_LOGO,
          });
        }}
        style={styles.headerLeftStyle}>
        <Image
          style={[{
            width: 124,
            height: 26,
          }, switchMerchant ? {
            transform: [
              { scaleX: 0.7 },
              { scaleY: 0.7 }
            ],
          } : {}]}
          resizeMode="contain"
          source={require('../assets/image/logo.png')}
        />
      </TouchableOpacity>
    ),
    headerTitle: () => (
      <View
        style={[
          {
            // justifyContent: Platform.OS === 'android' ? 'center' : 'flex-start',
            // alignItems: Platform.OS === 'android' ? 'center' : 'flex-start',
            // marginRight: Platform.OS === 'ios' ? "27%" : 0,
            // bottom: switchMerchant ? '2%' : 0,
            ...global.getHeaderTitleStyle(),
          },
          // windowWidth >= 768 && switchMerchant
          //   ? { right: windowWidth * 0.1 }
          //   : {},
          // windowWidth <= 768
          //   ? { right: 20 }
          //   : {},
        ]}>
        <Text
          style={{
            fontSize: switchMerchant ? 20 : 24,
            // lineHeight: 25,
            textAlign: 'left',
            alignItems: 'flex-start',
            justifyContent: 'flex-start',
            fontFamily: 'NunitoSans-Bold',
            color: Colors.whiteColor,
            opacity: 1,
            paddingLeft: Platform.OS === 'ios' ? "1%" : 0,
          }}>
          Upselling Campaign
        </Text>
      </View>
    ),
    headerRight: () => (
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
        }}>
        {outletSelectDropdownView()}
        <View
          style={{
            backgroundColor: 'white',
            width: 0.5,
            height: windowHeight * 0.025,
            opacity: 0.8,
            marginHorizontal: 15,
            bottom: -1,
          }} />
        <TouchableOpacity
          onPress={() => {
            if (global.currUserRole === 'admin') {
              navigation.navigate('Setting');
            }

            logEventAnalytics({
              eventName: ANALYTICS.MODULE_UPSELLING_UPSELLING_CAMPAIGN_C_PROFILE,
              eventNameParsed: ANALYTICS_PARSED.MODULE_UPSELLING_UPSELLING_CAMPAIGN_C_PROFILE,
            });
          }}
          style={{ flexDirection: 'row', alignItems: 'center' }}>
          <Text
            style={[{
              fontFamily: 'NunitoSans-SemiBold',
              fontSize: switchMerchant ? 10 : 16,
              color: Colors.secondaryColor,
              marginRight: 15,
            }, switchMerchant ? { width: windowWidth / 8 } : {}]}
            numberOfLines={switchMerchant ? 1 : 1}
          >
            {userName}
          </Text>
          <View
            style={{
              marginRight: 30,
              width: windowHeight * 0.05,
              height: windowHeight * 0.05,
              borderRadius: windowHeight * 0.05 * 0.5,
              alignItems: 'center',
              justifyContent: 'center',
              backgroundColor: 'white',
            }}>
            <Image
              style={{
                width: windowHeight * 0.035,
                height: windowHeight * 0.035,
                alignSelf: 'center',
              }}
              source={require('../assets/image/profile-pic.jpg')}
            />
          </View>
        </TouchableOpacity>
      </View>
    ),
  });

  /////////////////////////////////////////////////

  const handleChoosePhoto = () => {
    const imagePickerOptions = {
      mediaType: 'photo',
      quality: 0.5,
      includeBase64: false,
      maxWidth: 512,
      maxHeight: 512,
    };

    launchImageLibrary(imagePickerOptions, (response) => {
      if (response.didCancel) {
      } else if (response.error) {
        Alert.alert(response.error.toString());
      } else {
        const responseParsed = parseImagePickerResponse(response);

        // setState({ image: response.uri });
        setImage(responseParsed.uri);
        setImageType(responseParsed.uri.slice(responseParsed.uri.lastIndexOf('.')));

        setIsImageChanged(true);
      }
    });
  };

  {/**Skip */ }
  const canOnlyPushOnce = () => {
    Alert.alert(
      'Notice',
      'Each promotion can only be pushed once. Are you sure you want to proceed?',
      [
        {
          text: 'Push',
          onPress: () => {
            createPromotion(true);
          },
        },
        {
          text: 'Cancel',
          onPress: () => { },
        },
      ],
    );
  };

  const createPromotion = async (isAutoPush = false) => {
    var message = '';

    if (!campaignName) {
      message += 'Campaign name must be filled\n';
    }
    // if ( !promoCode ){
    //   message += 'Promo code must be filled\n'
    // }
    if (selectedAvailability.length <= 0) {
      message += 'Availability must be selected\n';
    }
    if (!campaignDescription) {
      message += 'Campaign description must be filled\n';
    }
    // if (!image) {
    //   message += 'Campaign image must be selected\n';
    // }
    if (selectedOutletList.length <= 0) {
      message += 'Outlet(s) must be selected\n';
    }
    if (smsText.length <= 0) {
      message += 'Notification/SMS message must be filled\n'
    }

    if (message.length > 0) {
      Alert.alert('Info', message);

      
    } else {
      // if (isPromoCodeUsageLimit) {
      //   if (!promoCodeUsageLimit) {
      //     Alert.alert(
      //       'Error',
      //       'Please fill in promo code usage',
      //       [{ text: 'OK', onPress: () => { } }],
      //       { cancelable: false },
      //     );
      //     return;
      //   }
      //   else if (parseInt(promoCodeUsageLimit) <= 0) {
      //     Alert.alert(
      //       'Error',
      //       'Promo code usage must be more than 0',
      //       [{ text: 'OK', onPress: () => { } }],
      //       { cancelable: false },
      //     );
      //     return;
      //   }
      // }

      // if (moment(promoDateEnd).isSameOrBefore(moment(promoDateStart))) {
      //   Alert.alert(
      //     'Error',
      //     'Promo start date must be before the Promo end date',
      //     [{ text: 'OK', onPress: () => { } }],
      //     { cancelable: false },
      //   );
      //   return;
      // }
      // else if (moment(promoDateEnd).isSame(moment(promoDateStart), 'day')) {
      //   Alert.alert(
      //     'Error',
      //     'Promo start date and Promo end date cannot be on the same day',
      //     [{ text: 'OK', onPress: () => { } }],
      //     { cancelable: false },
      //   );
      //   return;
      // }

      var notification = {};

      if (
        // isEditNotification
        true
      ) {
        var atLeastOnePush = false;

        if (isPushToSMS || isPushToApp || isPushToEmail) {
          atLeastOnePush = true;
        }

        if (!atLeastOnePush) {
          Alert.alert(
            'Error',
            'Please select at least one push to target',
            [{ text: 'OK', onPress: () => { } }],
            { cancelable: false },
          );
          return;
        }

        if (
          // !notificationTitle ||
          // !notificationDescription
          !smsText
          // !notificationPromoCode
        ) {
          Alert.alert(
            'Error',
            'Please fill in all required information:\nNotification/SMS Message',
            [{ text: 'OK', onPress: () => { } }],
            { cancelable: false },
          );
          return;
        }

        notification = {
          title: notificationTitle || '',
          description: smsText,
          // promoCode: notificationPromoCode,
          isPushToApp: isPushToApp || true,
          isPushToEmail: isPushToEmail || false,
          isPushToSMS: isPushToSMS || false,
          date: moment(notificationDate).valueOf(),
          time: moment(notificationTime).valueOf(),
          isAutoPush: false,
        };
      }

      ////////////////////////////////////////////////////////////////

      var criteriaList = [];

      ////////////////////////////////////////////////////////////////

      var loyaltyCriteriaList = [];

      if (selectedLoyaltyCampaignType === LOYALTY_CAMPAIGN_TYPE.FIRST_VISIT) {
        if (!loyaltyCampaignExpirationDays) {
          Alert.alert(
            'Error',
            'Please fill in all required information:\nExpiration (Days)',
            [{ text: 'OK', onPress: () => { } }],
            { cancelable: false },
          );
          return;
        }

        if (parseInt(loyaltyCampaignExpirationDays) <= 0) {
          Alert.alert(
            'Error',
            'Expiration (Days) must be more than 0',
            [{ text: 'OK', onPress: () => { } }],
            { cancelable: false },
          );
          return;
        }

        // maybe can add in item info in future to save bandwidth, for displaying purpose?

        loyaltyCriteriaList.push({
          // priceBeforeTax: +parseFloat(packageItemPrice).toFixed(2),
          loyaltyCampaignExpirationDays: parseInt(
            loyaltyCampaignExpirationDays,
          ),
          loyaltyCampaignSendTime: moment(loyaltyCampaignSendTime).valueOf(),
        });
      } else if (
        selectedLoyaltyCampaignType === LOYALTY_CAMPAIGN_TYPE.AT_RISK
      ) {
        if (
          !loyaltyCampaignExpirationDays ||
          !loyaltyCampaignGuestNotVisitedDays
        ) {
          Alert.alert(
            'Error',
            'Please fill in all required information:\nExpiration (Days)\nNot Visited (Days)',
            [{ text: 'OK', onPress: () => { } }],
            { cancelable: false },
          );
          return;
        }

        if (
          parseInt(loyaltyCampaignExpirationDays) <= 0 ||
          parseInt(loyaltyCampaignGuestNotVisitedDays) <= 0
        ) {
          Alert.alert(
            'Error',
            'Expiration (Days) and Not Visited (Days) must be more than 0',
            [{ text: 'OK', onPress: () => { } }],
            { cancelable: false },
          );
          return;
        }

        // maybe can add in item info in future to save bandwidth, for displaying purpose?

        loyaltyCriteriaList.push({
          // priceBeforeTax: +parseFloat(packageItemPrice).toFixed(2),
          loyaltyCampaignExpirationDays: parseInt(
            loyaltyCampaignExpirationDays,
          ),
          loyaltyCampaignSendTime: moment(loyaltyCampaignSendTime).valueOf(),
          loyaltyCampaignGuestNotVisitedDays: parseInt(
            loyaltyCampaignGuestNotVisitedDays,
          ),
        });
      } else if (
        selectedLoyaltyCampaignType === LOYALTY_CAMPAIGN_TYPE.BIRTHDAY
      ) {
        if (
          !loyaltyCampaignExpirationDays ||
          !loyaltyCampaignGuestBirthdayBeforeDays
        ) {
          Alert.alert(
            'Error',
            'Please fill in all required information:\nExpiration (Days)\nBefore Birthday (Days)',
            [{ text: 'OK', onPress: () => { } }],
            { cancelable: false },
          );
          return;
        }

        if (
          parseInt(loyaltyCampaignExpirationDays) <= 0 ||
          parseInt(loyaltyCampaignGuestBirthdayBeforeDays) <= 0
        ) {
          Alert.alert(
            'Error',
            'Expiration (Days) and Before Birthday (Days) must be more than 0',
            [{ text: 'OK', onPress: () => { } }],
            { cancelable: false },
          );
          return;
        }

        // maybe can add in item info in future to save bandwidth, for displaying purpose?

        loyaltyCriteriaList.push({
          // priceBeforeTax: +parseFloat(packageItemPrice).toFixed(2),
          loyaltyCampaignExpirationDays: parseInt(
            loyaltyCampaignExpirationDays,
          ),
          loyaltyCampaignSendTime: moment(loyaltyCampaignSendTime).valueOf(),
          loyaltyCampaignGuestBirthdayBeforeDays: parseInt(
            loyaltyCampaignGuestBirthdayBeforeDays,
          ),
        });
      } else if (selectedLoyaltyCampaignType === LOYALTY_CAMPAIGN_TYPE.GROWTH) {
        if (
          !loyaltyCampaignExpirationDays ||
          !loyaltyCampaignGuestAfterEveryVisits
        ) {
          Alert.alert(
            'Error',
            'Please fill in all required information:\nExpiration (Days)\nEvery Visits',
            [{ text: 'OK', onPress: () => { } }],
            { cancelable: false },
          );
          return;
        }

        if (
          parseInt(loyaltyCampaignExpirationDays) <= 0 ||
          parseInt(loyaltyCampaignGuestAfterEveryVisits) <= 0
        ) {
          Alert.alert(
            'Error',
            'Expiration (Days) and Every Visits must be more than 0',
            [{ text: 'OK', onPress: () => { } }],
            { cancelable: false },
          );
          return;
        }

        // maybe can add in item info in future to save bandwidth, for displaying purpose?

        loyaltyCriteriaList.push({
          // priceBeforeTax: +parseFloat(packageItemPrice).toFixed(2),
          loyaltyCampaignExpirationDays: parseInt(
            loyaltyCampaignExpirationDays,
          ),
          loyaltyCampaignSendTime: moment(loyaltyCampaignSendTime).valueOf(),
          loyaltyCampaignGuestAfterEveryVisits: parseInt(
            loyaltyCampaignGuestAfterEveryVisits,
          ),
        });
      } else if (
        selectedLoyaltyCampaignType === LOYALTY_CAMPAIGN_TYPE.BIG_SPENDER
      ) {
        if (
          !loyaltyCampaignExpirationDays ||
          !loyaltyCampaignGuestAfterEverySpends
        ) {
          Alert.alert(
            'Error',
            'Please fill in all required information:\nExpiration (Days)\nEvery Spends',
            [{ text: 'OK', onPress: () => { } }],
            { cancelable: false },
          );
          return;
        }

        if (
          parseInt(loyaltyCampaignExpirationDays) <= 0 ||
          parseFloat(loyaltyCampaignGuestAfterEverySpends) <= 0
        ) {
          Alert.alert(
            'Error',
            'Expiration (Days) and Every Spends must be more than 0',
            [{ text: 'OK', onPress: () => { } }],
            { cancelable: false },
          );
          return;
        }

        // maybe can add in item info in future to save bandwidth, for displaying purpose?

        loyaltyCriteriaList.push({
          // priceBeforeTax: +parseFloat(packageItemPrice).toFixed(2),
          loyaltyCampaignExpirationDays: parseInt(
            loyaltyCampaignExpirationDays,
          ),
          loyaltyCampaignSendTime: moment(loyaltyCampaignSendTime).valueOf(),
          loyaltyCampaignGuestAfterEverySpends: +parseFloat(
            loyaltyCampaignGuestAfterEverySpends,
          ).toFixed(2),
        });
      } else if (
        selectedLoyaltyCampaignType === LOYALTY_CAMPAIGN_TYPE.SIGN_UP
      ) {
        if (!loyaltyCampaignExpirationDays) {
          Alert.alert(
            'Error',
            'Please fill in all required information:\nExpiration (Days)',
            [{ text: 'OK', onPress: () => { } }],
            { cancelable: false },
          );
          return;
        }

        if (parseInt(loyaltyCampaignExpirationDays) <= 0) {
          Alert.alert(
            'Error',
            'Expiration (Days) must be more than 0',
            [{ text: 'OK', onPress: () => { } }],
            { cancelable: false },
          );
          return;
        }

        // maybe can add in item info in future to save bandwidth, for displaying purpose?

        loyaltyCriteriaList.push({
          // priceBeforeTax: +parseFloat(packageItemPrice).toFixed(2),
          loyaltyCampaignExpirationDays: parseInt(
            loyaltyCampaignExpirationDays,
          ),
          loyaltyCampaignSendTime: moment(loyaltyCampaignSendTime).valueOf(),
        });
      }
      else if (selectedLoyaltyCampaignType === LOYALTY_CAMPAIGN_TYPE.VOUCHER) {
        if (!loyaltyCampaignExpirationDays) {
          Alert.alert(
            'Error',
            'Please fill in all required information:\nExpiration (Days)',
            [{ text: 'OK', onPress: () => { } }],
            { cancelable: false },
          );
          return;
        }

        if (parseInt(loyaltyCampaignExpirationDays) <= 0) {
          Alert.alert(
            'Error',
            'Expiration (Days) must be more than 0',
            [{ text: 'OK', onPress: () => { } }],
            { cancelable: false },
          );
          return;
        }

        // maybe can add in item info in future to save bandwidth, for displaying purpose?

        loyaltyCriteriaList.push({
          // priceBeforeTax: +parseFloat(packageItemPrice).toFixed(2),
          loyaltyCampaignExpirationDays: parseInt(
            loyaltyCampaignExpirationDays,
          ),
          loyaltyCampaignSendTime: moment(loyaltyCampaignSendTime).valueOf(),
        });
      }

      ////////////////////////////////////////////////////////////////

      var taxIdList = []; // for each outlet
      var categoryIdList = []; // for each outlet

      var taxName = '';
      var taxRate = 0.05;

      for (var i = 0; i < currOutletTaxes.length; i++) {
        if (currOutletTaxes[i].uniqueId === selectedTax) {
          taxName = currOutletTaxes[i].name;
          taxRate = currOutletTaxes[i].rate;
          break;
        }
      }

      var outletNames = [];

      for (var i = 0; i < allOutlets.length; i++) {
        for (var j = 0; j < selectedOutletList.length; j++) {
          if (selectedOutletList[j].includes(allOutlets[i].uniqueId)) {
            outletNames.push(allOutlets[i].name);
            break;
          }
        }
      }

      // var categoryName = outletCategoriesDict[selectedOutletCategoryId].name;

      // for (var i = 0; i < selectedOutletList.length; i++) {
      //   const outletIdTemp = selectedOutletList[i];

      //   if (outletsTaxDict[outletIdTemp]) {
      //     taxIdList.push(outletsTaxDict[outletIdTemp].uniqueId);

      //     taxName = outletsTaxDict[outletIdTemp].name;
      //     taxRate = outletsTaxDict[outletIdTemp].rate;
      //   }
      //   else {
      //     taxIdList.push('');
      //   }

      //   if (allOutletsCategoriesNameDict[categoryName]) {
      //     var isSameCategoryNameExisted = false;
      //     var categoryIdTemp = '';

      //     for (var j = 0; j < allOutletsCategoriesNameDict[categoryName].length; j++) {
      //       if (allOutletsCategoriesNameDict[categoryName][j].outletId === outletIdTemp) {
      //         isSameCategoryNameExisted = true;
      //         categoryIdTemp = allOutletsCategoriesNameDict[categoryName][j].uniqueId;
      //         break;
      //       }
      //     }

      //     categoryIdList.push(categoryIdTemp);
      //   }
      //   else {
      //     categoryIdList.push('');
      //   }
      // }

      ///////////////////////////////////
      // upload image

      var promotionImagePath = '';
      var promotionCommonIdLocal = selectedUpsellingCampaignEdit
        ? selectedUpsellingCampaignEdit.commonId
        : uuidv4();

      if (image && imageType) {
        // promotionCommonIdLocal = uuidv4();

        promotionImagePath = await uploadImageToFirebaseStorage(
          {
            uri: image,
            type: imageType,
          },
          `/merchant/${merchantId}/promotion/${promotionCommonIdLocal}/image${imageType}`,
        );
      }

      ///////////////////////////////////

      if (selectedUpsellingCampaignEdit === null) {
        // means new item

        var body = {
          merchantId,
          merchantName,

          campaignName,
          isEnableSellOnline,
          orderTypes: selectedAvailability,
          targetUserGroup: selectedTargetUserGroup,
          targetSegmentGroupList: selectedTargetSegmentGroupList,

          upsellingSection,

          promoCode,
          isPromoCodeUsageLimit,
          promoCodeUsageLimit: parseInt(promoCodeUsageLimit),
          image: promotionImagePath,
          commonId: promotionCommonIdLocal,
          effectiveType: selectedEffectiveType,
          effectiveTypeOptions: selectedEffectiveTypeOptions,
          effectiveDay: selectedEffectiveDay,
          effectiveTimeStart: moment(effectiveTimeStart).valueOf(),
          effectiveTimeEnd: moment(effectiveTimeEnd).valueOf(),

          minSpend: parseFloat(minSpend),

          promoDateStart: moment(promoDateStart).valueOf(),
          promoDateEnd: moment(promoDateEnd).valueOf(),
          promoTimeStart: moment(promoTimeStart).valueOf(),
          promoTimeEnd: moment(promoTimeEnd).valueOf(),

          campaignDescription,

          promotionType: selectedPromotionType,

          criteriaList,

          isEditNotification: true,
          notification,

          //////////////
          // multi outlet supports

          outletIdList: selectedOutletList,
          outletNameList: outletNames,

          currOutletTaxName: taxName,
          currOutletTaxRate: taxRate,
          currOutletTaxId: selectedTax,
          currOutletId,

          loyaltyCampaignType: selectedLoyaltyCampaignType,
          loyaltyCriteriaList,

          notificationText: smsText,

          taggableVoucherId: taggableVoucherId || '',

          linkedPromotionId: linkedPromotionId || '',
          linkedPromotionName: linkedPromotionName || '',

          productList: productList.map(product => ({
            ...product,
            upsellPrice: product.upsellPrice.length > 0 ? parseFloat(product.upsellPrice) : 0,
          })),
        };

        // console.log(body);

        CommonStore.update((s) => {
          s.isLoading = true;
        });

        // ApiClient.POST(API.createLoyaltyCampaign, body, false)
        APILocal.createUpsellingCampaign({ body, uid: userId })
          .then(
            (result) => {
              if (result && result.status === 'success') {
                // if (isAutoPush) {
                //   if (isEditNotification) {
                //     ApiClient.POST(API.switchPromotionNotificationAutoPushStatus, {
                //       promotionId: result.promotion.uniqueId,
                //       notificationAutoPushStatus: true,
                //     }).then((result) => {
                //       if (result && result.status === 'success') {
                //         Alert.alert(
                //           'Success',
                //           "Promotion auto-push scheduled.",
                //           [
                //             {
                //               text: 'OK',
                //               onPress: () => {
                //                 navigation.navigate('PromotionList');
                //               },
                //             },
                //           ],
                //           { cancelable: false },
                //         );
                //       }
                //       CommonStore.update(s => {
                //         s.isLoading = false;
                //       });
                //     }).catch(err => { console.log(err) });
                //   }
                //   else {
                //     ApiClient.POST(API.pushPromotionNotificationManual, body).then((result) => {
                //       if (result && result.status === 'success') {
                //         Alert.alert(
                //           'Success',
                //           "Promotion pushed.",
                //           [
                //             {
                //               text: 'OK',
                //               onPress: () => {
                //                 navigation.navigate('PromotionList');
                //               },
                //             },
                //           ],
                //           { cancelable: false },
                //         );
                //       }
                //       CommonStore.update(s => {
                //         s.isLoading = false;
                //       });
                //     }).catch(err => { console.log(err) });
                //   }
                // }

                Alert.alert(
                  'Success',
                  'Upselling campaign has been added',
                  [
                    {
                      text: 'OK',
                      onPress: () => {
                        // navigation.navigate('PromotionList');

                        // props.navigation.navigate('SettingLoyalty');

                        props.navigation.navigate('UpsellingList');

                        logEventAnalytics({
                          eventName: ANALYTICS.MODULE_UPSELLING_UPSELLING_CAMPAIGN_SAVE_CREATE_ALERT_C_OK,
                          eventNameParsed: ANALYTICS_PARSED.MODULE_UPSELLING_UPSELLING_CAMPAIGN_SAVE_CREATE_ALERT_C_OK,
                        });
                      },
                    },
                  ],
                  { cancelable: false },
                );
              }

              CommonStore.update((s) => {
                s.isLoading = false;
              });
            },
          );
      } else if (selectedUpsellingCampaignEdit !== null) {
        // means existing item

        var body = {
          promotionId: selectedUpsellingCampaignEdit.uniqueId,

          merchantId,
          merchantName,

          campaignName,
          isEnableSellOnline,
          orderTypes: selectedAvailability,
          targetUserGroup: selectedTargetUserGroup,
          targetSegmentGroupList: selectedTargetSegmentGroupList,

          upsellingSection,

          promoCode,
          isPromoCodeUsageLimit,
          promoCodeUsageLimit: parseInt(promoCodeUsageLimit),

          image: promotionImagePath,
          commonId: promotionCommonIdLocal,
          isImageChanged,

          effectiveType: selectedEffectiveType,
          effectiveTypeOptions: selectedEffectiveTypeOptions,
          effectiveDay: selectedEffectiveDay,
          effectiveTimeStart: moment(effectiveTimeStart).valueOf(),
          effectiveTimeEnd: moment(effectiveTimeEnd).valueOf(),

          minSpend: parseFloat(minSpend),

          promoDateStart: moment(promoDateStart).valueOf(),
          promoDateEnd: moment(promoDateEnd).valueOf(),
          promoTimeStart: moment(promoTimeStart).valueOf(),
          promoTimeEnd: moment(promoTimeEnd).valueOf(),

          campaignDescription,

          promotionType: selectedPromotionType,

          criteriaList,

          isEditNotification: true,
          notification,

          //////////////
          // multi outlet supports

          outletIdList: selectedOutletList,
          outletNameList: outletNames,

          currOutletTaxName: taxName,
          currOutletTaxRate: taxRate,
          currOutletTaxId: selectedTax,
          currOutletId,

          loyaltyCampaignType: selectedLoyaltyCampaignType,
          loyaltyCriteriaList,

          notificationText: smsText,

          taggableVoucherId: taggableVoucherId || '',

          linkedPromotionId: linkedPromotionId || '',
          linkedPromotionName: linkedPromotionName || '',

          productList: productList.map(product => ({
            ...product,
            upsellPrice: product.upsellPrice.length > 0 ? parseFloat(product.upsellPrice) : 0,
          })),
        };

        // console.log(body);

        CommonStore.update((s) => {
          s.isLoading = true;
        });

        // ApiClient.POST(API.updateLoyaltyCampaign, body, false)
        APILocal.updateUpsellingCampaign({ body, uid: userId })
          .then(
            (result) => {
              if (result && result.status === 'success') {
                // Alert.alert(
                //   'Caution',
                //   'Each Promotion can only be Push ONCE',
                //   [
                //     {
                //       text: 'Push',
                //       onPress: () => { createPromotion(true) },
                //     },
                //     {
                //       text: 'Cancel',
                //       onPress: () => { createPromotion(false) },
                //     }
                //   ]
                // );

                // if (isAutoPush) {
                //   if (selectedUpsellingCampaignEdit.sendQuota > 0) {
                //     if (isEditNotification) {
                //       ApiClient.POST(API.switchPromotionNotificationAutoPushStatus, {
                //         promotionId: selectedUpsellingCampaignEdit.uniqueId,
                //         notificationAutoPushStatus: true,
                //       }).then((result) => {
                //         if (result && result.status === 'success') {
                //           Alert.alert(
                //             'Success',
                //             "Promotion auto-push scheduled.",
                //             [
                //               {
                //                 text: 'OK',
                //                 onPress: () => {
                //                   navigation.navigate('PromotionList');
                //                 },
                //               },
                //             ],
                //             { cancelable: false },
                //           );
                //         }
                //         CommonStore.update(s => {
                //           s.isLoading = false;
                //         });
                //       }).catch(err => { console.log(err) });
                //     }
                //     else {
                //       ApiClient.POST(API.pushPromotionNotificationManual, body).then((result) => {
                //         if (result && result.status === 'success') {
                //           Alert.alert(
                //             'Success',
                //             "Promotion pushed.",
                //             [
                //               {
                //                 text: 'OK',
                //                 onPress: () => {
                //                   navigation.navigate('PromotionList');
                //                 },
                //               },
                //             ],
                //             { cancelable: false },
                //           );
                //         }
                //         CommonStore.update(s => {
                //           s.isLoading = false;
                //         });
                //       }).catch(err => { console.log(err) });
                //     }
                //   }
                //   else {
                //     Alert.alert(
                //       'Error',
                //       "This promotion has been pushed before.",
                //       [
                //         {
                //           text: 'OK',
                //           onPress: () => {
                //             // navigation.navigate('PromotionList');
                //           },
                //         },
                //       ],
                //       { cancelable: false },
                //     );
                //   }
                // }

                Alert.alert(
                  'Success',
                  'Upselling campaign has been updated.',
                  [
                    {
                      text: 'OK',
                      onPress: () => {
                        // navigation.navigate('PromotionList');

                        // props.navigation.navigate('SettingLoyalty');

                        props.navigation.navigate('UpsellingList');

                        logEventAnalytics({
                          eventName: ANALYTICS.MODULE_UPSELLING_UPSELLING_CAMPAIGN_SAVE_UPDATE_ALERT_C_OK,
                          eventNameParsed: ANALYTICS_PARSED.MODULE_UPSELLING_UPSELLING_CAMPAIGN_SAVE_UPDATE_ALERT_C_OK,
                        });
                      },
                    },
                  ],
                  { cancelable: false },
                );
              }

              CommonStore.update((s) => {
                s.isLoading = false;
              });
            },
          );
      }
      // else {
      //   // means existing item

      //   var outletItemImagePath = '';
      //   var outletItemIdLocal = selectedUpsellingCampaignEdit.uniqueId;

      //   if (image && imageType && isImageChanged) {
      //     // outletItemIdLocal = selectedUpsellingCampaignEdit.uniqueId;

      //     outletItemImagePath = await uploadImageToFirebaseStorage({
      //       uri: image,
      //       type: imageType,
      //     }, `/merchant/${merchantId}/outletItem/${outletItemIdLocal}/image${imageType}`);
      //   }

      //   ///////////////////////////////////

      //   var body = {
      //     //<- this works, the database is updated
      //     // name: itemName,
      //     // code: '',
      //     // description: description,
      //     // price: itemPrice,
      //     // url: image,
      //     // materialTag: materialTag,
      //     // materialSKU: materialSKU,
      //     // categoryId: categoryId,
      //     // options: [
      //     //     {
      //     //         name: options,
      //     //         least: '1',
      //     //         choice: [{ label: options1, price: '1' }],
      //     //     },
      //     // ],
      //     // material: [{ name: material, qty: material }],
      //     // suppliers: [{ name: supplier }],

      //     merchantId: merchantId,
      //     // outletId: place,
      //     name: itemName,
      //     // categoryId: selectedOutletCategoryId,
      //     code: '',
      //     currency: 'MYR',
      //     description: itemDescription,
      //     image: outletItemImagePath,
      //     price: itemPrice,
      //     // taxId: outletsTaxDict[place].uniqueId,
      //     isActive: true,
      //     isComposite: true,
      //     isDelivery: true,
      //     isSellable: true,
      //     isTakeaway: true,
      //     options: '',
      //     prepareTime: 600,
      //     sku: itemName,

      //     stockLinkItems: stockLinkItems,

      //     outletItemIdLocal: outletItemIdLocal,

      //     variantGroupList: variantGroupList,
      //     addOnGroupList: addOnGroupList,

      //     //////////////
      //     // multi outlet supports

      //     categoryIdList: categoryIdList,
      //     categoryName: outletCategoriesDict[selectedOutletCategoryId].name,

      //     outletIdList: selectedOutletList,

      //     taxIdList: taxIdList,
      //     taxName: taxName,
      //     taxRate: taxRate,

      //     //////////////
      //     // for update purpose

      //     editItemId: selectedUpsellingCampaignEdit.uniqueId,
      //     editItemSku: selectedUpsellingCampaignEdit.sku,
      //     editItemImage: selectedUpsellingCampaignEdit.image,
      //   };

      //   // console.log(body);

      //   CommonStore.update(s => {
      //     s.isLoading = true;
      //   });

      //   ApiClient.POST(API.updateOutletItem, body, false).then((result) => {
      //     if (result && result.status === 'success') {
      //       Alert.alert(
      //         'Success',
      //         "Product updated.",
      //         [
      //           {
      //             text: 'OK',
      //             onPress: () => { },
      //           },
      //         ],
      //         { cancelable: false },
      //       );
      //     }

      //     CommonStore.update(s => {
      //       s.isLoading = false;
      //     });
      //   });
      // }
    }
  };

  // const pushSavedPromotion = () => {
  //   if ()

  // }

  // const pushPromotionNotificationManual = (promotion) => {
  //   // self.onButtonClickHandler();

  //   var body = {
  //     // outletId: User.getOutletId()
  //     promotionId: promotion.uniqueId,
  //   };

  //   ApiClient.POST(API.pushPromotionNotificationManual, body).then(result => {
  //     if (result && result.status === 'success') {
  //       Alert.alert(
  //         'Success',
  //         "Notification pushed.",
  //         [
  //           {
  //             text: 'OK',
  //             onPress: () => {
  //             },
  //           },
  //         ],
  //         { cancelable: false },
  //       );
  //     }
  //   }).catch(err => { console.log(err) });
  // };

  /////////////////////////////////////////////////

  const renderTierMenu = useCallback((params) => {
    const { item, drag, isActive } = params;

    const index = params.getIndex();

    return (
      (<View>
        <View
          onPress={() => {
            // setSelectedLoyaltyTierLevelId(item.levelId);

            // setEditTierName(item.levelName);
            // setEditTierSpend(item.levelTotalSpents.toFixed(2));
            // setEditTierVisits(item.levelTotalVisits.toString());
            // setDropEditCashback(item.levelCashbackRate.toFixed(2));

            // setEditTierModal(true);
            // setTierSelected(index);
          }}>
          {/* <View
          style={{
            flexDirection: 'row',
            paddingHorizontal: 20,
            paddingBottom: 15,
            paddingTop: 10,
            display: 'flex',
            justifyContent: 'space-between',
            flexDirection: 'row',
          }}> */}
          <View
            style={{
              height: switchMerchant
                ? windowHeight * 0.6
                : ((!switchMerchant && windowWidth < 1000 && windowHeight <= 610)) ?
                  windowHeight * 0.48 :
                  (!switchMerchant && Dimensions.get('screen').height < 670 && Dimensions.get('screen').width < 1350) ?
                    windowHeight * 0.48 :
                    windowHeight * 0.3,
              backgroundColor: '#ffffff',
              width: '100%',
              flexDirection: 'row',
              // padding: 20,
              // borderWidth: 1,
              justifyContent: 'space-between',
              alignItems: 'flex-start',

              borderBottomColor: '#EBEDEF',
              borderBottomWidth: index === productList.length - 1 ? 0 : 1,
              borderBottomLeftRadius:
                index === productList.length - 1 ? 10 : 0,
              borderBottomRightRadius:
                index === productList.length - 1 ? 10 : 0,
              borderTopRightRadius: index === 0 ? 10 : 0,
              borderTopLeftRadius: index === 0 ? 10 : 0,
            }}>
            <View
              style={{
                flexDirection: 'row',
                padding: 10,

                width: '35%',
              }}>
              <TouchableOpacity
                onLongPress={drag}
                onPress={() => {
                  // console.log(data.length);
                  // console.log(index);
                }}
                style={{
                  // width: '5%',

                  marginTop: -8,

                  display: 'flex',
                  justifyContent: 'center',
                }}
              >
                <View style={{ padding: 2 }}>
                  <Icon1
                    name="menu"
                    size={switchMerchant ? 15 : 20}
                    color="black"
                    style={{
                      // bottom: switchMerchant ? 2 : 1
                    }}
                  />
                </View>
              </TouchableOpacity>

              <View style={{ paddingLeft: 15, flexDirection: 'row' }}>
                {/* {console.log('variationItemsDropdownList')}
                {console.log(variationItemsDropdownList)}
                {console.log('item')}
                {console.log(item)} */}

                {
                  variationItemsDropdownList.length > 0
                    &&
                    (
                      (variationItemsDropdownList.find(option => option.value === item.productId))
                      // selectedVariationItems.filter(itemId => {
                      //   return variationItemsDropdownList.find(item2 => item2.value === itemId) ? true : false;
                      // }).length === selectedVariationItems.length
                      // ||
                      // selectedVariationItems.length === 0
                    )
                    ?
                    <DropDownPicker
                      containerStyle={{ height: switchMerchant ? 42 : 42, zIndex: 2 }}
                      arrowColor={'black'}
                      arrowSize={20}
                      arrowStyle={{ fontWeight: 'bold' }}
                      labelStyle={{ fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14 }}
                      style={{ width: 250, paddingVertical: 0, backgroundColor: Colors.fieldtBgColor, borderRadius: 10, fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14, marginTop: -8, }}
                      placeholderStyle={{ color: Colors.fieldtTxtColor, fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14 }}
                      placeholder={"Select a Product"}
                      // multipleText={'%d product(s) selected'}
                      items={variationItemsDropdownList}
                      itemStyle={{ justifyContent: 'flex-start', marginLeft: 5, zIndex: 2, fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14 }}
                      // customTickIcon={(press) => <Ionicon name={"checkbox-outline"} color={press ? Colors.fieldtBgColor : Colors.primaryColor} size={switchMerchant ? 20 : 25} />}
                      onChangeItem={(changedItem) => {
                        setProductList(productList.map(product => {
                          if (product.levelId === item.levelId) {
                            return {
                              ...product,
                              productId: changedItem.value,
                              productSku: changedItem.sku,
                              productName: changedItem.label,
                              productPrice: changedItem.price,

                              upsellPrice: changedItem.price.toFixed(2),
                            };
                          }
                          else {
                            return product;
                          }
                        }));

                        logEventAnalytics({
                          eventName: ANALYTICS.MODULE_UPSELLING_UPSELLING_CAMPAIGN_PRODUCT_LIST_ITEM_DD_NAME,
                          eventNameParsed: ANALYTICS_PARSED.MODULE_UPSELLING_UPSELLING_CAMPAIGN_PRODUCT_LIST_ITEM_DD_NAME,
                        });
                      }}
                      defaultValue={item.productId}
                      multiple={false}
                      searchable
                      dropDownMaxHeight={150}
                      dropDownStyle={{ width: 250, height: 150, backgroundColor: Colors.fieldtBgColor, borderRadius: 10, borderWidth: 1, textAlign: 'left', zIndex: 2, fontSize: switchMerchant ? 10 : 16, marginTop: -8, }}
                    />
                    :
                    <></>
                }

                {/* <Text
                  style={{
                    fontFamily: 'NunitoSans-Bold',
                    fontSize: switchMerchant ? 10 : 14,
                  }}>
                  {item.productName}
                </Text> */}
              </View>
            </View>

            <View style={{
              padding: 10, flexDirection: 'row', width: '8%',
              // paddingTop: 15,
              marginLeft: -18
            }}>
              <Text
                style={{
                  fontFamily: 'NunitoSans-Bold',
                  fontSize: switchMerchant ? 10 : 14,
                }}>
                {`RM ${parseFloat(item.productPrice).toFixed(2)}`}
              </Text>
            </View>

            <View style={{ padding: 10, flexDirection: 'row', width: '13%', paddingTop: 2, marginRight: -18 }}>
              <TextInput
                style={[
                  {
                    // marginTop: '2%',
                    padding: 5,
                    backgroundColor: Colors.fieldtBgColor,
                    width: Platform.OS == 'ios' ? '100%' : '100%',
                    height: switchMerchant ? 48 : 48,
                    // height: Platform.OS == 'ios' ? 100 : 100,
                    borderRadius: 5,
                    borderWidth: 1,
                    borderColor: '#E5E5E5',
                    // paddingTop: Platform.OS == 'ios' ? 10 : 10,
                    paddingLeft: 10,
                    fontFamily: 'NunitoSans-Regular',
                    fontSize: 14,
                  },
                  switchMerchant
                    ? {
                      fontSize: 10,
                      width: '92%',
                      height: 97,
                    }
                    : {},
                ]}
                // textAlignVertical={'top'}
                keyboardType='decimal-pad'
                placeholderStyle={{
                  fontFamily: 'NunitoSans-Regular',
                  fontSize: 14,
                }}
                placeholder="Upsell Price"
                placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                defaultValue={item.upsellPrice}
                multiline={false}
                //iOS
                clearTextOnFocus
                //////////////////////////////////////////////
                //Android
                onFocus={() => {
                  setTemp(item.upsellPrice)
                  setProductList(productList.map(product => {
                    if (product.levelId === item.levelId) {
                      return {
                        ...product,
                        upsellPrice: '',
                      };
                    }
                    else {
                      return product;
                    }
                  }));
                }}
                ///////////////////////////////////////////////
                //When textinput is not selected
                onEndEditing={() => {
                  if (item.upsellPrice == '') {
                    setProductList(productList.map(product => {
                      if (product.levelId === item.levelId) {
                        return {
                          ...product,
                          upsellPrice: temp,
                        };
                      }
                      else {
                        return product;
                      }
                    }));
                  }
                }}
                //////////////////////////////////////////////
                onChangeText={(text) => {
                  setProductList(productList.map(product => {
                    if (product.levelId === item.levelId) {
                      return {
                        ...product,
                        upsellPrice: parseValidPriceText(text),
                      };
                    }
                    else {
                      return product;
                    }
                  }));

                  logEventAnalytics({
                    eventName: ANALYTICS.MODULE_UPSELLING_UPSELLING_CAMPAIGN_PRODUCT_LIST_ITEM_TB_UPSELL_PRICE,
                    eventNameParsed: ANALYTICS_PARSED.MODULE_UPSELLING_UPSELLING_CAMPAIGN_PRODUCT_LIST_ITEM_TB_UPSELL_PRICE,
                  });
                }}
              />
            </View>

            <View style={{ padding: 10, flexDirection: 'row', width: '20%', height: 60 }}>
              {
                (
                  item.tagIdList.every((val) =>
                    userTagDropdownList
                      .map((tag) => tag.value)
                      .includes(val)
                  )
                  ||
                  item.tagIdList.length === 0
                )
                  ?
                  <DropDownPicker
                    style={{
                      height: switchMerchant ? 60 : 60,
                      width: switchMerchant ? 130 : 130,
                      marginLeft: 5,
                      paddingVertical: 0,
                      borderColor: Colors.primaryColor,
                      fontSize: switchMerchant ? 11 : 14,

                      marginTop: -8,
                    }}
                    dropDownStyle={{
                      marginLeft: 5,
                      marginTop: 1,
                      borderWidth: 1,
                      // borderColor: 'darkgreen',
                      width: switchMerchant ? 130 : 130,
                      height: 150,

                      marginTop: -8,
                    }}
                    arrowSize={switchMerchant ? 15 : 20}
                    arrowStyle={{
                      paddingVertical: switchMerchant ? 0 : -10,
                      alignSelf: 'center',
                    }}
                    // items={[{ label: 'Shampoo', value: 1 }, { label: 'Conditioner', value: 2 }, { label: 'Hair mask', value: 3 }]}
                    items={userTagDropdownList}
                    placeholder={'Select Tag(s)'}
                    multipleText={'%d Tag(s)'}
                    labelStyle={{ fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14 }}
                    onChangeItem={(items) => {
                      // setSelectedUserTagList(items);
                      setProductList(productList.map(product => {
                        if (product.levelId === item.levelId) {
                          return {
                            ...product,
                            tagIdList: items,
                          };
                        }
                        else {
                          return product;
                        }
                      }));

                      logEventAnalytics({
                        eventName: ANALYTICS.MODULE_UPSELLING_UPSELLING_CAMPAIGN_PRODUCT_LIST_ITEM_DD_TAGS,
                        eventNameParsed: ANALYTICS_PARSED.MODULE_UPSELLING_UPSELLING_CAMPAIGN_PRODUCT_LIST_ITEM_DD_TAGS,
                      });
                    }}
                    defaultValue={item.tagIdList}
                    multiple
                    searchable
                    dropDownMaxHeight={250}
                    onSearch={(text) => {
                      // setSearchingUserTagText(text);
                    }}
                    customTickIcon={(press) => (
                      <Ionicon
                        name={'checkbox-outline'}
                        color={
                          press ? Colors.fieldtBgColor : Colors.primaryColor
                        }
                        size={switchMerchant ? 17 : 25}
                      />
                    )}
                  />
                  :
                  <></>
              }
            </View>

            <View style={{ padding: 10, flexDirection: 'row', width: '15%', marginLeft: -30 }}>
              <DropDownPicker
                containerStyle={{ height: switchMerchant ? 42 : 42, zIndex: 2 }}
                arrowColor={'black'}
                arrowSize={20}
                arrowStyle={{ fontWeight: 'bold' }}
                labelStyle={{ fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14 }}
                style={{ width: 150, paddingVertical: 0, backgroundColor: Colors.fieldtBgColor, borderRadius: 10, fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14, marginTop: -8, }}
                placeholderStyle={{ color: Colors.fieldtTxtColor, fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14 }}
                placeholder={"Select a Type"}
                // multipleText={'%d product(s) selected'}
                items={UPSELL_BY_TYPE_DROPDOWN_LIST}
                itemStyle={{ justifyContent: 'flex-start', marginLeft: 5, zIndex: 2, fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14 }}
                // customTickIcon={(press) => <Ionicon name={"checkbox-outline"} color={press ? Colors.fieldtBgColor : Colors.primaryColor} size={switchMerchant ? 20 : 25} />}
                onChangeItem={(changedItem) => {
                  setProductList(productList.map(product => {
                    if (product.levelId === item.levelId) {
                      return {
                        ...product,

                        upsellByType: changedItem.value,
                      };
                    }
                    else {
                      return product;
                    }
                  }));

                  logEventAnalytics({
                    eventName: ANALYTICS.MODULE_UPSELLING_UPSELLING_CAMPAIGN_PRODUCT_LIST_ITEM_DD_TYPE,
                    eventNameParsed: ANALYTICS_PARSED.MODULE_UPSELLING_UPSELLING_CAMPAIGN_PRODUCT_LIST_ITEM_DD_TYPE,
                  });
                }}
                defaultValue={item.upsellByType}
                multiple={false}
                // searchable={true}
                dropDownMaxHeight={150}
                dropDownStyle={{ width: 150, height: 120, backgroundColor: Colors.fieldtBgColor, borderRadius: 10, borderWidth: 1, textAlign: 'left', zIndex: 2, fontSize: switchMerchant ? 10 : 16, marginTop: -8, }}
              />
            </View>

            <View style={{
              // padding: 10,
              paddingTop: 2,

              width: '5%',

              height: 48,

              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'center',

              marginLeft: 5,
            }}>
              <TouchableOpacity
                style={{

                  // marginLeft: 15,
                }}
                onPress={() => {
                  setProductList([
                    ...productList.slice(0, index),
                    ...productList.slice(index + 1),
                  ]);

                  logEventAnalytics({
                    eventName: ANALYTICS.MODULE_UPSELLING_UPSELLING_CAMPAIGN_PRODUCT_LIST_ITEM_C_MINUS,
                    eventNameParsed: ANALYTICS_PARSED.MODULE_UPSELLING_UPSELLING_CAMPAIGN_PRODUCT_LIST_ITEM_C_MINUS,
                  });
                }}
              >
                <Icon1
                  name="minus-circle"
                  size={switchMerchant ? 15 : 20}
                  color={Colors.tabRed}
                />
              </TouchableOpacity>
            </View>
          </View>
          {/* </View> */}
        </View>
      </View>)
    );
  });

  //Render start here
  return (
    <UserIdleWrapper disabled={!isMounted}>
      <View
        style={[
          styles.container,
          !isTablet()
            ? {
              transform: [{ scaleX: 1 }, { scaleY: 1 }],
            }
            : {},
          {
            ...getTransformForScreenInsideNavigation(),
            // paddingBottom: 100,
          }
        ]}>
        {/* <View
          style={[
            styles.sidebar,
            !isTablet() ? {} : {},
            switchMerchant
              ? {
                // width: '10%'
              }
              : {},
            {
              width: windowWidth * 0.08,
            }
          ]}>
          <SideBar
            navigation={props.navigation}
            selectedTab={11}
            expandPromotions
          />
        </View> */}
        <ScrollView horizontal scrollEnabled={switchMerchant}>
          {/* Modals */}

          <DateTimePickerModal
            isVisible={showPromoDateStartPicker}
            mode={'date'}
            onConfirm={(text) => {
              setPromoDateStart(moment(text));

              setShowPromoDateStartPicker(false);
            }}
            onCancel={() => {
              setShowPromoDateStartPicker(false);
            }}
          />

          <DateTimePickerModal
            isVisible={showPromoDateEndPicker}
            mode={'date'}
            onConfirm={(text) => {
              setPromoDateEnd(moment(text));

              setShowPromoDateEndPicker(false);
            }}
            onCancel={() => {
              setShowPromoDateEndPicker(false);
            }}
          />

          <DateTimePickerModal
            isVisible={showPromoTimeStartPicker}
            mode={'time'}
            onConfirm={(text) => {
              setPromoTimeStart(moment(text));

              setShowPromoTimeStartPicker(false);
            }}
            onCancel={() => {
              setShowPromoTimeStartPicker(false);
            }}
          />

          <DateTimePickerModal
            isVisible={showPromoTimeEndPicker}
            mode={'time'}
            onConfirm={(text) => {
              setPromoTimeEnd(moment(text));

              setShowPromoTimeEndPicker(false);
            }}
            onCancel={() => {
              setShowPromoTimeEndPicker(false);
            }}
          />

          <DateTimePickerModal
            isVisible={showEffectiveTimeStartPicker}
            mode={'time'}
            onConfirm={(text) => {
              setEffectiveTimeStart(moment(text));

              setShowEffectiveTimeStartPicker(false);
            }}
            onCancel={() => {
              setShowEffectiveTimeStartPicker(false);
            }}
          />

          <DateTimePickerModal
            isVisible={showEffectiveTimeEndPicker}
            mode={'time'}
            onConfirm={(text) => {
              setEffectiveTimeEnd(moment(text));

              setShowEffectiveTimeEndPicker(false);
            }}
            onCancel={() => {
              setShowEffectiveTimeEndPicker(false);
            }}
          />

          <DateTimePickerModal
            isVisible={showLoyaltyCampaignSendTimePicker}
            mode={'time'}
            onConfirm={(text) => {
              setLoyaltyCampaignSendTime(moment(text));

              setShowLoyaltyCampaignSendTimePicker(false);
            }}
            onCancel={() => {
              setShowLoyaltyCampaignSendTimePicker(false);
            }}
            date={moment(loyaltyCampaignSendTime).toDate()}
          />

          {/**Skip */}
          {/* Modals */}
          {/* ********************** Import Modal Start ************************ */}
          <ModalView
            supportedOrientations={['landscape', 'portrait']}
            visible={importModal}
            transparent
            animationType={'slide'}>
            <View style={[styles.modalContainer1, {}]}>
              <View
                style={[
                  styles.modalView,
                  {
                    top:
                      Platform.OS === 'ios' && keyboardHeight > 0
                        ? -keyboardHeight * 0.3
                        : 0,
                    width: 300,
                    height: 240,
                  },
                ]}>
                <TouchableOpacity
                  style={styles.closeButton}
                  onPress={() => {
                    // setState({ changeTable: false });
                    setImportModal(false);
                  }}>
                  <AntDesign
                    name="closecircle"
                    size={25}
                    color={Colors.fieldtTxtColor}
                  />
                </TouchableOpacity>
                <View style={styles.modalTitle}>
                  <Text style={[styles.modalTitleText, { fontSize: 16 }]}>
                    {/* Import Options */}
                    Import
                  </Text>
                </View>
                <View
                  style={{
                    alignItems: 'center',
                    top: '10%',
                  }}>
                  <TouchableOpacity
                    style={[
                      styles.modalSaveButton,
                      {
                        zIndex: -1,
                      },
                    ]}
                    onPress={() => {
                      setShowImportListModal(true);
                    }}>
                    <Text
                      style={[
                        styles.modalDescText,
                        { color: Colors.primaryColor },
                      ]}>
                      Import Template (Excel)
                    </Text>
                  </TouchableOpacity>
                </View>
              </View>
            </View>
          </ModalView>

          <ModalView
            supportedOrientations={['landscape', 'portrait']}
            visible={showImportListModal}
            transparent
            animationType={'slide'}>
            <View style={[styles.modalContainer1, {}]}>
              <View
                style={[
                  styles.modalView1,
                  {
                    top:
                      Platform.OS === 'ios' && keyboardHeight > 0
                        ? -keyboardHeight * 0.3
                        : 0,
                    //width: 400, height: 300,
                  },
                ]}>
                <TouchableOpacity
                  style={styles.closeButton}
                  onPress={() => {
                    setShowImportListModal(false);
                  }}>
                  <AntDesign
                    name="closecircle"
                    size={25}
                    color={Colors.fieldtTxtColor}
                  />
                </TouchableOpacity>
                <View style={styles.modalTitle}>
                  <Text
                    style={[
                      styles.modalTitleText,
                      { fontSize: 16, fontWeight: '500' },
                    ]}>
                    Imported List
                  </Text>
                </View>
                <View
                  style={{
                    //backgroundColor:'red',
                    height: 70,
                    marginVertical: 10,
                    marginTop: 15,
                    borderWidth: 1,
                    borderColor: '#E5E5E5',
                    height:
                      Platform.OS === 'ios'
                        ? windowWidth * 0.57
                        : windowWidth * 0.47,
                    width:
                      Platform.OS === 'ios'
                        ? windowWidth * 0.66
                        : windowWidth * 0.56,
                  }}>
                  <View style={{ width: '100%', marginTop: 0 }}>
                    <View
                      style={{
                        backgroundColor: Colors.whiteColor,
                        padding: 7,
                        paddingTop: 0,
                        height: '82%',
                      }}>
                      <View style={{ flexDirection: 'row' }}>
                        <View
                          style={{
                            flexDirection: 'row',
                            flex: 1.5,
                            borderRightColor: 'lightgrey',
                            alignItems: 'center',
                            justifyContent: 'flex-start',
                          }}>
                          <View style={{ flexDirection: 'column' }}>
                            <Text
                              style={{
                                fontSize: 15,
                                color: Colors.fieldtTxtColor,
                                fontFamily: 'NunitoSans-Bold',
                              }}>
                              Name
                            </Text>
                          </View>
                        </View>

                        <View
                          style={{
                            flexDirection: 'row',
                            flex: 1.8,
                            borderRightColor: 'lightgrey',
                            alignItems: 'center',
                            justifyContent: 'flex-start',
                          }}>
                          <View style={{ flexDirection: 'column' }}>
                            <Text
                              style={{
                                fontSize: 15,
                                color: Colors.fieldtTxtColor,
                                fontFamily: 'NunitoSans-Bold',
                              }}>
                              Email
                            </Text>
                          </View>
                        </View>

                        <View
                          style={{
                            flexDirection: 'row',
                            flex: 1.2,
                            borderRightColor: 'lightgrey',
                            alignItems: 'center',
                            justifyContent: 'flex-start',
                          }}>
                          <View style={{ flexDirection: 'column' }}>
                            <Text
                              style={{
                                fontSize: 15,
                                color: Colors.fieldtTxtColor,
                                fontFamily: 'NunitoSans-Bold',
                              }}>
                              Phone Number
                            </Text>
                          </View>
                        </View>

                        <View
                          style={{
                            flexDirection: 'row',
                            flex: 0.8,
                            borderRightColor: 'lightgrey',
                            alignItems: 'center',
                            justifyContent: 'flex-start',
                          }}>
                          <View style={{ flexDirection: 'column' }}>
                            <Text
                              style={{
                                fontSize: 15,
                                color: Colors.fieldtTxtColor,
                                fontFamily: 'NunitoSans-Bold',
                              }} />
                          </View>
                        </View>
                      </View>

                      <FlatList />
                    </View>
                  </View>
                  {/* <Table borderStyle={{ borderWidth: 1}}>
                      <Row data={TableData.tableHead} flexArr={[1, 1, 1, 1, 1, 1, 1]} style={{}}/>
                      <TableWrapper style={{}}>
                        <Col data={TableData.tableTitle} style={{flex:1,}} heightArr={[28,28,28,28]} textStyle={{}}/>
                        <Rows data={TableData.tableData} flexArr={[ 1, 2, 1]} style={{height: 28}} textStyle={{textAlign: 'center'}}/>
                      </TableWrapper>
                    </Table> */}
                </View>
                <View
                  style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                  <TouchableOpacity
                    style={{
                      borderWidth: 1,
                      borderRadius: 10,
                      padding: 5,
                      alignItems: 'center',
                      height: 35,
                      width: 85,
                      borderColor: Colors.primaryColor,
                      backgroundColor: Colors.primaryColor,
                    }}
                    onPress={() => {
                      //importSelectFile()
                      //importTemplate()
                    }}>
                    <Text
                      style={{
                        fontWeight: '600',
                        fontSize: 18,
                        textAlign: 'center',
                        color: Colors.whiteColor,
                      }}>
                      Import
                    </Text>
                  </TouchableOpacity>
                  <View
                    style={{ flexDirection: 'row', justifyContent: 'flex-end' }}>
                    <TouchableOpacity
                      style={{
                        borderWidth: 1,
                        borderRadius: 10,
                        padding: 5,
                        alignItems: 'center',
                        height: 35,
                        width: 85,
                        borderColor: Colors.primaryColor,
                        backgroundColor: Colors.whiteColor,
                        marginRight: 10,
                      }}
                      onPress={() => {
                        setImportModal(false);
                      }}>
                      <Text
                        style={{
                          fontWeight: '600',
                          fontSize: 18,
                          textAlign: 'center',
                          color: Colors.primaryColor,
                        }}>
                        Cancel
                      </Text>
                    </TouchableOpacity>
                    <TouchableOpacity
                      style={{
                        borderWidth: 1,
                        borderRadius: 10,
                        padding: 5,
                        alignItems: 'center',
                        height: 35,
                        width: 85,
                        borderColor: Colors.primaryColor,
                        backgroundColor: Colors.primaryColor,
                      }}>
                      <Text
                        style={{
                          fontWeight: '600',
                          fontSize: 18,
                          textAlign: 'center',
                          color: 'white',
                        }}>
                        Submit
                      </Text>
                    </TouchableOpacity>
                  </View>
                </View>
              </View>
            </View>
          </ModalView>

          <ModalView
            supportedOrientations={['landscape', 'portrait']}
            visible={importSmsModal}
            transparent
            animationType={'slide'}>
            <View style={[styles.modalContainer1, {}]}>
              <View
                style={[
                  styles.modalView2,
                  {
                    top:
                      Platform.OS === 'ios' && keyboardHeight > 0
                        ? -keyboardHeight * 0.3
                        : 0,
                    //width: 400, height: 300,
                  },
                ]}>
                <TouchableOpacity
                  style={styles.closeButton}
                  onPress={() => {
                    // setState({ changeTable: false });
                    setImportSmsModal(false);
                  }}>
                  <AntDesign
                    name="closecircle"
                    size={25}
                    color={Colors.fieldtTxtColor}
                  />
                </TouchableOpacity>
                <View style={{}}>
                  <View style={{ height: windowHeight * 0.1 }}>
                    <Text
                      style={{
                        textAlign: 'center',
                        fontWeight: '700',
                        fontSize: 30,
                      }}>
                      Insufficient Credit!
                    </Text>
                  </View>

                  <View
                    style={{
                      justifyContent: 'center',
                      alignItems: 'center',
                      height: windowHeight * 0.15,
                    }}>
                    <Text
                      style={{
                        textAlign: 'center',
                        color: Colors.descriptionColor,
                        fontSize: 25,
                        width: '100%',
                        alignSelf: 'center',
                      }}>
                      Please contact your account manager
                    </Text>
                  </View>
                </View>
              </View>
            </View>
          </ModalView>
          {/* ********************** Import Modal End ************************ */}

          <KeyboardAvoidingView>
            <View
              style={
                switchMerchant
                  ? {
                    flexDirection: 'row',
                    justifyContent: 'space-between',
                    width: windowWidth * 0.8,
                    marginHorizontal: 15,
                    //alignSelf: 'center',
                    margin: 10,
                    paddingHorizontal: 0,
                    paddingTop: 10,
                  }
                  : {
                    flexDirection: 'row',
                    justifyContent: 'space-between',
                    width: windowWidth * 0.9,
                    marginHorizontal: 15,
                    alignSelf: 'center',
                    margin: 10,
                    paddingHorizontal: 0,
                    paddingTop: 10,
                  }
              }>
              <TouchableOpacity
                style={{ width: 90, height: 35, justifyContent: 'center' }}
                onPress={() => {
                  // props.navigation.navigate('PromotionList');

                  props.navigation.navigate('UpsellingList');

                  logEventAnalytics({
                    eventName: ANALYTICS.MODULE_UPSELLING_UPSELLING_CAMPAIGN_C_BACK,
                    eventNameParsed: ANALYTICS_PARSED.MODULE_UPSELLING_UPSELLING_CAMPAIGN_C_BACK,
                  });
                }}>
                <View
                  style={{
                    flexDirection: 'row',
                    paddingHorizontal: '10%',
                    alignContent: 'center',
                    alignItems: 'center',
                    marginTop: switchMerchant ? 10 : 0,
                  }}>
                  <View style={{ justifyContent: 'center' }}>
                    <Feather
                      name="chevron-left"
                      size={switchMerchant ? 20 : 30}
                      style={{ color: Colors.primaryColor, alignSelf: 'center' }}
                    />
                  </View>
                  <Text
                    style={[
                      {
                        fontSize: 17,
                        color: Colors.primaryColor,
                        fontWeight: '600',
                        marginBottom: Platform.OS === 'ios' ? 0 : 1,
                      },
                      switchMerchant
                        ? {
                          fontSize: 14,
                        }
                        : {},
                    ]}>
                    Back
                  </Text>
                </View>
              </TouchableOpacity>
            </View>

            {/* <KeyBoardAwareScrollView style={styles.list}> */}
            <KeyboardAwareScrollView
              showsVerticalScrollIndicator={false}
              nestedScrollEnabled
              style={{
                backgroundColor: Colors.whiteColor,
                width: switchMerchant
                  ? windowWidth * 0.87
                  : windowWidth * 0.87,
                height: windowHeight * 0.825,
                marginTop: 0,
                marginHorizontal: switchMerchant ? 30 : 30,
                //alignSelf: 'center',
                borderRadius: 5,
                shadowOpacity: 0,
                shadowColor: '#000',
                shadowOffset: {
                  width: 1,
                  height: 2,
                },
                shadowOpacity: 0.22,
                shadowRadius: 3.22,
                elevation: 3,

                marginBottom: switchMerchant ? 28 : 28,
              }}>
              <View
                style={{
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  width: '100%',
                }}>
                <View
                  style={{
                    //flexDirection: 'row',
                    margin: 20,
                    marginBottom: 10,
                    width: '70%',
                  }}>
                  <View
                    style={{
                      width: '100%',
                      justifyContent: 'space-between',
                      flexDirection: 'row',
                    }}>
                    <Text
                      style={[
                        { fontFamily: 'NunitoSans-Bold', fontSize: 30 },
                        switchMerchant
                          ? {
                            fontSize: 20,
                          }
                          : {},
                      ]}>
                      {campaignName.length > 0
                        ? campaignName
                        : 'New Upselling Campaign'}
                    </Text>
                  </View>

                  {campaignDescription.length > 0 ? (
                    <View
                      style={{
                        width: '100%',
                        justifyContent: 'space-between',
                        flexDirection: 'row',
                        marginTop: 5,
                      }}>
                      <Text
                        style={{ fontFamily: 'NunitoSans-Regular', fontSize: 17 }}>
                        {campaignDescription}
                      </Text>
                    </View>
                  ) : (
                    <></>
                  )}
                </View>
                <View
                  style={{
                    margin: 20,
                    marginBottom: 10,
                  }}>
                  <View style={{}}>
                    <TouchableOpacity
                      style={[
                        {
                          justifyContent: 'center',
                          flexDirection: 'row',
                          borderWidth: 1,
                          borderColor: Colors.primaryColor,
                          backgroundColor: '#0A1F44',
                          borderRadius: 5,
                          width: 130,
                          paddingHorizontal: 10,
                          height: 40,
                          alignItems: 'center',
                          shadowOffset: {
                            width: 0,
                            height: 2,
                          },
                          shadowOpacity: 0.22,
                          shadowRadius: 3.22,
                          elevation: 1,
                          zIndex: -1,
                          marginBottom: 10,
                        },
                        switchMerchant
                          ? {
                            height: 35,
                            width: 120,
                          }
                          : {},
                      ]}
                      disabled={isLoading}
                      onPress={() => {
                        createPromotion();

                        logEventAnalytics({
                          eventName: ANALYTICS.MODULE_UPSELLING_UPSELLING_CAMPAIGN_C_SAVE,
                          eventNameParsed: ANALYTICS_PARSED.MODULE_UPSELLING_UPSELLING_CAMPAIGN_C_SAVE,
                        });
                      }}>
                      <Text
                        style={[
                          {
                            color: Colors.whiteColor,
                            fontSize: 16,
                            fontFamily: 'NunitoSans-Bold',
                          },
                          switchMerchant
                            ? {
                              fontSize: 10,
                            }
                            : {},
                        ]}>
                        {isLoading ? 'LOADING...' : 'SAVE'}
                      </Text>

                      {isLoading ? (
                        <ActivityIndicator
                          color={Colors.whiteColor}
                          size={'small'}
                        />
                      ) : (
                        <></>
                      )}
                    </TouchableOpacity>

                    {/* <TouchableOpacity
                    style={[
                      {
                        justifyContent: 'center',
                        flexDirection: 'row',
                        borderWidth: 1,
                        borderColor: Colors.tabRed,
                        backgroundColor: Colors.tabRed,
                        borderRadius: 5,
                        width: 130,
                        paddingHorizontal: 10,
                        height: 40,
                        alignItems: 'center',
                        shadowOffset: {
                          width: 0,
                          height: 2,
                        },
                        shadowOpacity: 0.22,
                        shadowRadius: 3.22,
                        elevation: 1,
                        zIndex: -1,
                        marginBottom: 10,
                      },
                      switchMerchant
                        ? {
                          height: 35,
                          width: 120,
                        }
                        : {},
                    ]}
                    onPress={() => {
                      confirmDeletePromotion();
                    }}>
                    <Text
                      style={[
                        {
                          color: Colors.whiteColor,
                          fontSize: 16,
                          fontFamily: 'NunitoSans-Bold',
                        },
                        switchMerchant
                          ? {
                            fontSize: 10,
                          }
                          : {},
                      ]}>
                      DELETE
                    </Text>
                  </TouchableOpacity> */}

                    {/* <TouchableOpacity
                    style={[
                      {
                        justifyContent: 'center',
                        flexDirection: 'row',
                        borderWidth: 1,
                        borderColor: Colors.primaryColor,
                        backgroundColor: '#0A1F44',
                        borderRadius: 5,
                        width: 130,
                        paddingHorizontal: 10,
                        height: 40,
                        alignItems: 'center',
                        shadowOffset: {
                          width: 0,
                          height: 2,
                        },
                        shadowOpacity: 0.22,
                        shadowRadius: 3.22,
                        elevation: 1,
                        zIndex: -1,
                        marginBottom: 10,
                      },
                      switchMerchant
                        ? {
                          height: 35,
                          width: 120,
                        }
                        : {},
                    ]}
                    onPress={() => {
                      canOnlyPushOnce();
                    }}>
                    <Text
                      style={[
                        {
                          color: Colors.whiteColor,
                          fontSize: 16,
                          fontFamily: 'NunitoSans-Bold',
                        },
                        switchMerchant
                          ? {
                            fontSize: 10,
                          }
                          : {},
                      ]}>
                      {isLoading ? 'LOADING...' : 'PUBLISH'}
                    </Text>

                    {isLoading ? (
                      <ActivityIndicator
                        color={Colors.whiteColor}
                        size={'small'}
                      />
                    ) : (
                      <></>
                    )}
                  </TouchableOpacity> */}
                  </View>
                </View>
              </View>

              <View
                style={{
                  flexDirection: 'column',
                  borderWidth: 1,
                  borderColor: '#c4c4c4',
                  width: '98%',
                  alignSelf: 'center',
                  flex: 1,
                  paddingBottom: 30,
                }}>
                <View
                  style={{ flexDirection: 'row', flex: 1, alignSelf: 'center' }}>
                  <View
                    style={{
                      flex: 4,
                      flexDirection: 'column',
                      marginVertical: 20,
                    }}>
                    <View style={{ flexDirection: 'column', marginLeft: 20 }}>
                      <TouchableOpacity
                        onPress={() => {
                          handleChoosePhoto();

                          logEventAnalytics({
                            eventName: ANALYTICS.MODULE_UPSELLING_UPSELLING_CAMPAIGN_C_UPLOAD_IMAGE,
                            eventNameParsed: ANALYTICS_PARSED.MODULE_UPSELLING_UPSELLING_CAMPAIGN_C_UPLOAD_IMAGE,
                          });
                        }}>
                        <View style={{ flexDirection: 'row', zIndex: -2 }}>
                          {image ? (
                            <View
                              style={{
                                backgroundColor: '#F7F7F7',
                                borderRadius: 5,
                                zIndex: 1,
                              }}>
                              <AsyncImage
                                source={{ uri: image }}
                                style={[
                                  {
                                    width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 200 : 260,
                                    height: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 160 : 200,
                                    borderRadius: 5
                                  },
                                  switchMerchant
                                    ? {
                                      width: 200,
                                      height: 160,
                                    }
                                    : {},
                                ]}
                                hideLoading
                              />
                              <View
                                style={{
                                  position: 'absolute',
                                  bottom: 5,
                                  right: 5,
                                  //opacity: 0.5,
                                }}>
                                <FontAwesome5
                                  name="edit"
                                  size={switchMerchant ? 10 : 23}
                                  color={Colors.primaryColor}
                                />
                              </View>
                            </View>
                          ) : (
                            <View
                              style={[
                                {
                                  backgroundColor: '#F7F7F7',
                                  borderRadius: 5,
                                  width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 200 : 260,
                                  height: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 160 : 200,
                                  alignItems: 'center',
                                  justifyContent: 'center',
                                },
                                switchMerchant
                                  ? {
                                    width: 200,
                                    height: 160,
                                  }
                                  : {},
                              ]}>
                              <Icon1
                                name="upload"
                                size={switchMerchant ? 100 : (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 100 : 150}
                                color="lightgrey"
                                style={{ zIndex: -1 }}
                              />
                              <View
                                style={{
                                  position: 'absolute',
                                  bottom: 5,
                                  right: 5,
                                  //opacity: 0.5,
                                }}>
                                <FontAwesome5
                                  name="edit"
                                  size={switchMerchant ? 10 : 23}
                                  color={Colors.primaryColor}
                                />
                              </View>
                            </View>
                          )}
                        </View>
                      </TouchableOpacity>

                      <Text
                        style={[
                          {
                            fontWeight: '500',
                            marginTop: 10,
                            fontFamily: 'NunitoSans-Bold',
                            fontSize: 14,
                          },
                          switchMerchant
                            ? {
                              fontSize: 10,
                            }
                            : {},
                        ]}>
                        Campaign Description
                      </Text>
                      <TextInput
                        style={[
                          {
                            marginTop: '2%',
                            padding: 5,
                            backgroundColor: Colors.fieldtBgColor,
                            width: Platform.OS == 'ios' ? '90%' : '85%',
                            height: Platform.OS == 'ios' ? 100 : 117,
                            borderRadius: 5,
                            borderWidth: 1,
                            borderColor: '#E5E5E5',
                            paddingTop: Platform.OS == 'ios' ? 10 : 10,
                            paddingLeft: 10,
                            fontFamily: 'NunitoSans-Regular',
                            fontSize: 14,
                          },
                          switchMerchant
                            ? {
                              fontSize: 10,
                              width: '92%',
                              height: 97,
                            }
                            : {},
                        ]}
                        textAlignVertical={'top'}
                        placeholderStyle={{
                          fontFamily: 'NunitoSans-Regular',
                          fontSize: 14,
                        }}
                        placeholder="Description..."
                        placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                        defaultValue={campaignDescription}
                        multiline
                        //iOS
                        // clearTextOnFocus={true}
                        //////////////////////////////////////////////
                        //Android
                        // onFocus={() => {
                        //   setTemp(campaignDescription)
                        //   setCampaignDescription('');
                        // }}
                        ///////////////////////////////////////////////
                        //When textinput is not selected
                        // onEndEditing={() => {
                        //   if (campaignDescription == '') {
                        //     setCampaignDescription(temp);
                        //   }
                        // }}
                        //////////////////////////////////////////////
                        onChangeText={(text) => {
                          setCampaignDescription(text);

                          logEventAnalytics({
                            eventName: ANALYTICS.MODULE_UPSELLING_UPSELLING_CAMPAIGN_TB_CAMPAIGN_DESCRIPTION,
                            eventNameParsed: ANALYTICS_PARSED.MODULE_UPSELLING_UPSELLING_CAMPAIGN_TB_CAMPAIGN_DESCRIPTION,
                          });
                        }}
                      />
                    </View>
                  </View>

                  <View
                    style={{
                      flex: 7,
                      marginVertical: 20,
                      marginHorizontal: 20,
                      marginLeft: 10,
                    }}>
                    <View style={{ flexDirection: 'row', flex: 1, zIndex: 1 }}>
                      <View
                        style={{
                          flex: 1,
                          marginRight: switchMerchant
                            ? '5%'
                            : windowWidth <= 1024
                              ? '3%'
                              : '2%',
                        }}>
                        <Text
                          style={[
                            {
                              alignSelf: 'flex-start',
                              fontFamily: 'NunitoSans-Bold',
                              fontSize: 14,
                              fontWeight: '500',
                            },
                            switchMerchant
                              ? {
                                fontSize: 10,
                              }
                              : {},
                          ]}>
                          Campaign Name
                        </Text>
                        <TextInput
                          placeholder="Campaign Name"
                          placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                          placeholderStyle={{
                            fontFamily: 'NunitoSans-Regular',
                            fontSize: 14,
                          }}
                          style={[
                            {
                              backgroundColor: Colors.fieldtBgColor,
                              width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 200 : 250,
                              height: 40,
                              borderRadius: 5,
                              padding: 5,
                              marginVertical: 5,
                              borderWidth: 1,
                              borderColor: '#E5E5E5',
                              paddingLeft: 10,
                              fontFamily: 'NunitoSans-Regular',
                              fontSize: 14,
                            },
                            switchMerchant
                              ? {
                                fontSize: 10,
                                width: 200,
                                height: 35,
                              }
                              : {},
                          ]}
                          //iOS
                          // clearTextOnFocus={true}
                          //////////////////////////////////////////////
                          //Android
                          // onFocus={() => {
                          //   setTemp(campaignName)
                          //   setCampaignName('');
                          // }}
                          ///////////////////////////////////////////////
                          //When textinput is not selected
                          // onEndEditing={() => {
                          //   if (campaignName == '') {
                          //     setCampaignName(temp);
                          //   }
                          // }}
                          //////////////////////////////////////////////
                          onChangeText={(text) => {
                            setCampaignName(text);

                            logEventAnalytics({
                              eventName: ANALYTICS.MODULE_UPSELLING_UPSELLING_CAMPAIGN_TB_CAMPAIGN_NAME,
                              eventNameParsed: ANALYTICS_PARSED.MODULE_UPSELLING_UPSELLING_CAMPAIGN_TB_CAMPAIGN_NAME,
                            });
                          }}
                          defaultValue={campaignName}
                        />
                        {/************************Got Two Types of CHeckBox/ If want change CheckBox remember change the imported CheckBox *************************/}

                        <View style={{ marginTop: 0 }}>
                          <Text style={[{ alignSelf: 'flex-start', fontWeight: '500', fontFamily: 'NunitoSans-Bold', fontSize: 14 }, switchMerchant ? {
                            fontSize: 10,
                          } : {}]}>
                            Promo Date
                          </Text>
                          <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                            <TouchableOpacity onPress={() => {
                              setShowPromoDateStartPicker(true);

                              logEventAnalytics({
                                eventName: ANALYTICS.MODULE_UPSELLING_UPSELLING_CAMPAIGN_PROMO_DATE_C_START_DATE,
                                eventNameParsed: ANALYTICS_PARSED.MODULE_UPSELLING_UPSELLING_CAMPAIGN_PROMO_DATE_C_START_DATE,
                              });
                            }}
                              style={[{
                                backgroundColor: Colors.fieldtBgColor,
                                width: 115,
                                padding: 5,
                                height: 40,
                                borderRadius: 5,
                                marginVertical: 5,
                                alignItems: 'center',
                                justifyContent: 'center',
                                borderWidth: 1,
                                borderColor: '#E5E5E5',
                                fontFamily: 'NunitoSans-Regular',
                                fontSize: 14
                              }, switchMerchant ? {
                                fontSize: 10,
                                width: 93,
                                height: 35,
                              } : {}]}
                            >
                              <Text style={[{ fontVariant: ['tabular-nums'], fontFamily: 'NunitoSans-Regular', fontSize: 14 }, switchMerchant ? {
                                fontSize: 10,
                              } : {}]}>{moment(promoDateStart).format('DD MMM YYYY')}</Text>
                            </TouchableOpacity>
                            <Text style={[{ marginLeft: '1%', marginRight: '1%', fontWeight: '500', fontFamily: 'NunitoSans-Regular', fontSize: 14 }, switchMerchant ? {
                              fontSize: 10,
                            } : {}]}>to</Text>
                            <TouchableOpacity onPress={() => {
                              setShowPromoDateEndPicker(true);

                              logEventAnalytics({
                                eventName: ANALYTICS.MODULE_UPSELLING_UPSELLING_CAMPAIGN_PROMO_DATE_C_END_DATE,
                                eventNameParsed: ANALYTICS_PARSED.MODULE_UPSELLING_UPSELLING_CAMPAIGN_PROMO_DATE_C_END_DATE,
                              });
                            }}
                              style={[{
                                backgroundColor: Colors.fieldtBgColor,
                                width: 115,
                                height: 40,
                                borderRadius: 5,
                                padding: 1,
                                marginVertical: 5,
                                alignItems: 'center',
                                justifyContent: 'center',
                                borderWidth: 1,
                                borderColor: '#E5E5E5',
                                fontFamily: 'NunitoSans-Regular',
                                fontSize: 14
                              }, switchMerchant ? {
                                fontSize: 10,
                                width: 93,
                                height: 35,
                              } : {}]}                       >
                              <Text style={[{ fontVariant: ['tabular-nums'], fontFamily: 'NunitoSans-Regular', fontSize: 14 }, switchMerchant ? {
                                fontSize: 10,
                              } : {}]}>{moment(promoDateEnd).format('DD MMM YYYY')}</Text>
                            </TouchableOpacity>
                          </View>
                        </View>

                        <Text
                          style={[
                            {
                              alignSelf: 'flex-start',
                              fontWeight: '500',
                              fontFamily: 'NunitoSans-Bold',
                              fontSize: 14,
                            },
                            switchMerchant
                              ? {
                                fontSize: 10,
                              }
                              : {},
                          ]}>
                          Effective On Every
                        </Text>
                        <View style={{ marginTop: '2%' }}>
                          {[{ label: 'Day', value: 'DAY' }].find(
                            (item) => item.value === selectedEffectiveType,
                          ) ? (
                            <DropDownPicker
                              containerStyle={[
                                { height: 40 },
                                switchMerchant ? { height: 35 } : {},
                              ]}
                              arrowColor={'black'}
                              arrowSize={20}
                              arrowStyle={{ fontWeight: 'bold' }}
                              labelStyle={{
                                fontFamily: 'NunitoSans-Regular',
                                fontSize: 14,
                              }}
                              style={[
                                {
                                  width: switchMerchant ? 185 : (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 200 : 250,
                                  paddingVertical: 0,
                                  backgroundColor: Colors.fieldtBgColor,
                                  borderRadius: 10,
                                },
                                switchMerchant
                                  ? {
                                    width: 200,
                                  }
                                  : {},
                              ]}
                              placeholderStyle={{
                                color: Colors.fieldtTxtColor,
                                fontFamily: 'NunitoSans-Regular',
                                fontSize: 14,
                              }}
                              // items={[{ label: 'Everyone', value: 1 }, { label: 'Active Member', value: 2 }, { label: 'Inactive Member', value: 3 }, { label: 'Female', value: 4 }, { label: 'Male', value: 5 }, { label: 'VIP Only', value: 6 }]}
                              items={[{ label: 'Day', value: 'DAY' }]}
                              //items={''}
                              itemStyle={{
                                justifyContent: 'flex-start',
                                marginLeft: 5,
                                fontFamily: 'NunitoSans-Regular',
                                fontSize: 14,
                              }}
                              placeholder={'Day'}
                              //multiple={true}
                              //customTickIcon={(press) => <Ionicon name={"checkbox-outline"} color={press ? Colors.fieldtBgColor : Colors.primaryColor} size={25} />}
                              // onChangeItem={item => {
                              //   setSelectedEffectiveDay(item.value);
                              // }}
                              onChangeItem={{}}
                              defaultValue={selectedEffectiveType}
                              //defaultValue={''}
                              dropDownMaxHeight={350}
                              dropDownStyle={[
                                {
                                  width: switchMerchant ? 185 : (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 200 : 250,
                                  height: 350,
                                  backgroundColor: Colors.fieldtBgColor,
                                  borderRadius: 10,
                                  borderWidth: 1,
                                },
                                switchMerchant
                                  ? {
                                    width: 200,
                                  }
                                  : {},
                              ]}
                              disabled
                              globalTextStyle={{
                                fontSize: switchMerchant ? 10 : 14,
                              }}
                            />
                          ) : null}
                        </View>
                        {/* <View style={{ marginTop: '2%', zIndex: -3 }}>
                        <Text
                          style={[
                            {
                              alignSelf: 'flex-start',
                              fontWeight: '500',
                              fontFamily: 'NunitoSans-Bold',
                              fontSize: 14,
                            },
                            switchMerchant
                              ? {
                                fontSize: 10,
                              }
                              : {},
                          ]}>
                          Voucher Code Format
                        </Text>
                        <View style={{ marginTop: '2%' }}>
                          <DropDownPicker
                            containerStyle={[
                              { height: 40 },
                              switchMerchant ? { height: 35 } : {},
                            ]}
                            arrowColor={'black'}
                            arrowSize={20}
                            arrowStyle={{ fontWeight: 'bold' }}
                            labelStyle={{
                              fontFamily: 'NunitoSans-Regular',
                              fontSize: 14,
                            }}
                            style={[
                              {
                                width: 250,
                                paddingVertical: 0,
                                backgroundColor: Colors.fieldtBgColor,
                                borderRadius: 10,
                              },
                              switchMerchant
                                ? {
                                  width: 200,
                                }
                                : {},
                            ]}
                            placeholderStyle={{
                              color: Colors.fieldtTxtColor,
                              fontFamily: 'NunitoSans-Regular',
                              fontSize: 14,
                            }}
                            //items={[{ label: 'Dine In', value: 1 }, { label: 'Takeaway', value: 2 }, { label: 'Delivery', value: 3 } ]}
                            // items={ORDER_TYPE_DROP_DOWN_LIST}
                            items={[
                              {
                                label: 'Generic',
                                value: MERCHANT_VOUCHER_CODE_FORMAT.GENERIC,
                              },
                              {
                                label: 'Unique',
                                value: MERCHANT_VOUCHER_CODE_FORMAT.UNIQUE,
                              },
                            ]}
                            itemStyle={{
                              justifyContent: 'flex-start',
                              marginLeft: 5,
                              zIndex: 2,
                              fontFamily: 'NunitoSans-Regular',
                              fontSize: 14,
                            }}
                            defaultValue={voucherCodeFormat}
                            placeholder={'Voucher Type'}
                            // multipleText={'%d availability selected'}
                            // multiple={true}
                            customTickIcon={(press) => (
                              <Ionicon
                                name={'checkbox-outline'}
                                color={
                                  press
                                    ? Colors.fieldtBgColor
                                    : Colors.primaryColor
                                }
                                size={25}
                              />
                            )}
                            onChangeItem={(item) => {
                              setVoucherCodeFormat(item.value);
                            }}
                            dropDownMaxHeight={100}
                            dropDownStyle={[
                              {
                                width: 250,
                                height: 100,
                                backgroundColor: Colors.fieldtBgColor,
                                borderRadius: 10,
                                borderWidth: 1,
                              },
                              switchMerchant
                                ? {
                                  width: 200,
                                }
                                : {},
                            ]}
                            globalTextStyle={{
                              fontSize: switchMerchant ? 10 : 14,
                            }}
                          />
                        </View>
                      </View> */}

                        <View style={{ marginTop: '2%', zIndex: -4 }}>
                          <Text
                            style={[
                              {
                                alignSelf: 'flex-start',
                                fontWeight: '500',
                                fontFamily: 'NunitoSans-Bold',
                                fontSize: 14,
                              },
                              switchMerchant
                                ? {
                                  fontSize: 10,
                                }
                                : {},
                            ]}>
                            Availability
                          </Text>
                          <View style={{ marginTop: '2%' }}>
                            <DropDownPicker
                              containerStyle={[
                                { height: 40 },
                                switchMerchant ? { height: 35 } : {},
                              ]}
                              arrowColor={'black'}
                              arrowSize={20}
                              arrowStyle={{ fontWeight: 'bold' }}
                              labelStyle={{
                                fontFamily: 'NunitoSans-Regular',
                                fontSize: 14,
                              }}
                              style={[
                                {
                                  width: switchMerchant ? 185 : (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 200 : 250,
                                  paddingVertical: 0,
                                  backgroundColor: Colors.fieldtBgColor,
                                  borderRadius: 10,
                                },
                                switchMerchant
                                  ? {
                                    width: 200,
                                  }
                                  : {},
                              ]}
                              placeholderStyle={{
                                color: Colors.fieldtTxtColor,
                                fontFamily: 'NunitoSans-Regular',
                                fontSize: 14,
                              }}
                              //items={[{ label: 'Dine In', value: 1 }, { label: 'Takeaway', value: 2 }, { label: 'Delivery', value: 3 } ]}
                              items={ORDER_TYPE_DROP_DOWN_LIST}
                              itemStyle={{
                                justifyContent: 'flex-start',
                                marginLeft: 5,
                                zIndex: 2,
                                fontFamily: 'NunitoSans-Regular',
                                fontSize: 14,
                              }}
                              defaultValue={selectedAvailability}
                              placeholder={'Select'}
                              multipleText={'%d availability selected'}
                              multiple
                              customTickIcon={(press) => (
                                <Ionicon
                                  name={'checkbox-outline'}
                                  color={
                                    press
                                      ? Colors.fieldtBgColor
                                      : Colors.primaryColor
                                  }
                                  size={25}
                                />
                              )}
                              onChangeItem={(items) => {
                                setSelectedAvailability(items);

                                logEventAnalytics({
                                  eventName: ANALYTICS.MODULE_UPSELLING_UPSELLING_CAMPAIGN_DD_AVAILABILITY,
                                  eventNameParsed: ANALYTICS_PARSED.MODULE_UPSELLING_UPSELLING_CAMPAIGN_DD_AVAILABILITY,
                                });
                              }}
                              dropDownMaxHeight={100}
                              dropDownStyle={[
                                {
                                  width: switchMerchant ? 185 : (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 200 : 250,
                                  height: 100,
                                  backgroundColor: Colors.fieldtBgColor,
                                  borderRadius: 10,
                                  borderWidth: 1,
                                },
                                switchMerchant
                                  ? {
                                    width: 200,
                                  }
                                  : {},
                              ]}
                              globalTextStyle={{
                                fontSize: switchMerchant ? 10 : 14,
                              }}
                            />
                          </View>
                        </View>

                        {/* 2023-02-02 - Hide first */}
                        {/* <Text
                          style={[
                            {
                              fontWeight: '500',
                              zIndex: -5,
                              marginTop: 5,
                              fontFamily: 'NunitoSans-Bold',
                              fontSize: 14,
                            },
                            switchMerchant
                              ? {
                                fontSize: 10,
                              }
                              : {},
                          ]}>
                          Loyalty Type
                        </Text>
                        <View style={{ marginTop: '2%', zIndex: -5 }}>
                          <DropDownPicker
                            containerStyle={[
                              { height: 40 },
                              switchMerchant ? { height: 35 } : {},
                            ]}
                            arrowColor={'black'}
                            arrowSize={20}
                            arrowStyle={{ fontWeight: 'bold' }}
                            labelStyle={{
                              fontFamily: 'NunitoSans-Regular',
                              fontSize: 14,
                            }}
                            style={[
                              {
                                width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 200 : 250,
                                paddingVertical: 0,
                                backgroundColor: Colors.fieldtBgColor,
                                borderRadius: 10,
                              },
                              switchMerchant
                                ? {
                                  width: 200,
                                }
                                : {},
                            ]}
                            placeholderStyle={{
                              color: Colors.fieldtTxtColor,
                              fontFamily: 'NunitoSans-Regular',
                              fontSize: 14,
                            }}
                            items={LOYALTY_CAMPAIGN_DROPDOWN_LIST}
                            itemStyle={{
                              justifyContent: 'flex-start',
                              marginLeft: 5,
                              zIndex: 2,
                              fontFamily: 'NunitoSans-Regular',
                              fontSize: 14,
                            }}
                            placeholder={'Select'}
                            //multiple={true}
                            customTickIcon={(press) => (
                              <Ionicon
                                name={'checkbox-outline'}
                                color={
                                  press
                                    ? Colors.fieldtBgColor
                                    : Colors.primaryColor
                                }
                                size={25}
                              />
                            )}
                            onChangeItem={(item) => {
                              setSelectedLoyaltyCampaignType(item.value);
                            }}
                            defaultValue={selectedLoyaltyCampaignType}
                            dropDownMaxHeight={150}
                            dropDownStyle={[
                              {
                                width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 200 : 250,
                                height: 150,
                                backgroundColor: Colors.fieldtBgColor,
                                borderRadius: 10,
                                borderWidth: 1,
                                textAlign: 'left',
                                zIndex: 2,
                              },
                              switchMerchant
                                ? {
                                  width: 200,
                                }
                                : {},
                            ]}
                            globalTextStyle={{
                              fontSize: switchMerchant ? 10 : 14,
                            }}
                          //dropDownStyle={{ borderWidth: 1, borderColor: '#E5E5E5', borderRadius: 5, width: 200, paddingVertical: 0, margin: 5, marginLeft: 0 }}
                          />
                        </View> */}

                      </View>
                      <View style={{ flex: 1, zIndex: -1, marginTop: 1 }}>
                        <Text
                          style={[
                            {
                              fontWeight: '500',
                              fontFamily: 'NunitoSans-Bold',
                              fontSize: 14,
                            },
                            switchMerchant
                              ? {
                                fontSize: 10,
                              }
                              : {},
                          ]}>
                          Apply To Outlets
                        </Text>
                        <View style={{ marginTop: 4 }}>
                          <DropDownPicker
                            containerStyle={[
                              { height: 40 },
                              switchMerchant
                                ? {
                                  height: 35,
                                }
                                : {},
                            ]}
                            arrowColor={'black'}
                            arrowSize={20}
                            arrowStyle={{ fontWeight: 'bold' }}
                            labelStyle={{
                              fontFamily: 'NunitoSans-Regular',
                              fontSize: 14,
                            }}
                            style={{
                              width: switchMerchant ? 185 : (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 200 : 230,
                              paddingVertical: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 0 : 5,
                              backgroundColor: Colors.fieldtBgColor,
                              borderRadius: 10,
                            }}
                            placeholderStyle={{
                              color: Colors.fieldtTxtColor,
                              fontFamily: 'NunitoSans-Regular',
                              fontSize: 14,
                            }}
                            // items={[{ label: 'Kota Damansara', value: 1 }, { label: 'Petaling Jaya', value: 2 }, { label: 'Loke Yo', value: 3 }, { label: 'Puchong', value: 4 }, { label: 'Daigong', value: 5 }, { label: 'KL', value: 6 }]}
                            items={outletDropdownList}
                            itemStyle={{
                              justifyContent: 'flex-start',
                              marginLeft: 5,
                              zIndex: 2,
                              fontFamily: 'NunitoSans-Regular',
                              fontSize: 14,
                            }}
                            defaultValue={selectedOutletList}
                            placeholder={'Select outlet(s)'}
                            multipleText={'%d outlet(s) selected'}
                            multiple
                            customTickIcon={(press) => (
                              <Ionicon
                                name={'checkbox-outline'}
                                color={
                                  press
                                    ? Colors.fieldtBgColor
                                    : Colors.primaryColor
                                }
                                size={25}
                              />
                            )}
                            onChangeItem={(items) => {
                              setSelectedOutletList(items);

                              logEventAnalytics({
                                eventName: ANALYTICS.MODULE_UPSELLING_UPSELLING_CAMPAIGN_DD_APPLY_TO_OUTLETS,
                                eventNameParsed: ANALYTICS_PARSED.MODULE_UPSELLING_UPSELLING_CAMPAIGN_DD_APPLY_TO_OUTLETS,
                              });
                            }}
                            dropDownMaxHeight={100}
                            dropDownStyle={{
                              width: switchMerchant ? 185 : (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 200 : 230,
                              height: 100,
                              backgroundColor: Colors.fieldtBgColor,
                              borderRadius: 10,
                              borderWidth: 1,
                            }}
                            globalTextStyle={{
                              fontSize: switchMerchant ? 10 : 14,
                            }}
                          />
                        </View>

                        <View style={{ marginTop: '2%', zIndex: -2, }}>
                          <Text style={[{ alignSelf: 'flex-start', fontWeight: '500', fontFamily: 'NunitoSans-Bold', fontSize: 14 }, switchMerchant ? {
                            fontSize: 10,
                          } : {}]}>
                            Promo Time
                          </Text>
                          <View style={{ flexDirection: 'row', alignItems: 'center', }}>
                            <TouchableOpacity onPress={() => {
                              setShowPromoTimeStartPicker(true);

                              logEventAnalytics({
                                eventName: ANALYTICS.MODULE_UPSELLING_UPSELLING_CAMPAIGN_PROMO_TIME_C_START_TIME,
                                eventNameParsed: ANALYTICS_PARSED.MODULE_UPSELLING_UPSELLING_CAMPAIGN_PROMO_TIME_C_START_TIME,
                              });
                            }}
                              style={[{
                                backgroundColor: Colors.fieldtBgColor,
                                // width: 115,
                                width: '45%',
                                height: 40,
                                borderRadius: 5,
                                padding: 1,
                                marginVertical: 5,
                                alignItems: 'center',
                                justifyContent: 'center',
                                borderWidth: 1,
                                borderColor: '#E5E5E5',
                                fontFamily: 'NunitoSans-Regular', fontSize: 14
                              }, switchMerchant ? {
                                height: 35
                              } : {}]}>
                              <Text style={[{ fontVariant: ['tabular-nums'], fontFamily: 'NunitoSans-Regular', fontSize: 14 }, switchMerchant ? {
                                fontSize: 10,
                              } : {}]}>{moment(promoTimeStart).format('hh:mm A')}</Text>
                            </TouchableOpacity>
                            <Text style={[{ width: '10%', fontWeight: '500', textAlign: 'center', fontFamily: 'NunitoSans-Regular', fontSize: 14 }, switchMerchant ? {
                              fontSize: 10,
                            } : {}]}>to</Text>
                            <TouchableOpacity onPress={() => {
                              setShowPromoTimeEndPicker(true);

                              logEventAnalytics({
                                eventName: ANALYTICS.MODULE_UPSELLING_UPSELLING_CAMPAIGN_PROMO_TIME_C_END_TIME,
                                eventNameParsed: ANALYTICS_PARSED.MODULE_UPSELLING_UPSELLING_CAMPAIGN_PROMO_TIME_C_END_TIME,
                              });
                            }}
                              style={[{
                                backgroundColor: Colors.fieldtBgColor,
                                // width: 115,
                                width: '44.5%',
                                height: 40,
                                borderRadius: 5,
                                padding: 1,
                                marginVertical: 5,
                                alignItems: 'center',
                                justifyContent: 'center',
                                borderWidth: 1,
                                borderColor: '#E5E5E5',
                                fontFamily: 'NunitoSans-Regular', fontSize: 14
                              }, switchMerchant ? {
                                height: 35
                              } : {}]}>
                              <Text style={[{ fontVariant: ['tabular-nums'], fontFamily: 'NunitoSans-Regular', fontSize: 14 }, switchMerchant ? {
                                fontSize: 10,
                              } : {}]}>{moment(promoTimeEnd).format('hh:mm A')}</Text>
                            </TouchableOpacity>
                          </View>
                        </View>

                        <View style={{ flexDirection: 'row', zIndex: -2, marginTop: 5 }}>
                          <Text
                            style={[
                              {
                                fontWeight: '500',
                                fontFamily: 'NunitoSans-Bold',
                                fontSize: 14,
                              },
                              switchMerchant
                                ? {
                                  fontSize: 10,
                                }
                                : {},
                            ]}>
                            Effective
                          </Text>
                          <Text
                            style={[
                              {
                                marginLeft: 5,
                                fontWeight: '500',
                                color: 'red',
                                fontFamily: 'NunitoSans-Bold',
                                fontSize: 14,
                              },
                              switchMerchant
                                ? {
                                  fontSize: 10,
                                }
                                : {},
                            ]}>
                            Options
                          </Text>
                        </View>

                        <View style={{ marginTop: '2%', zIndex: -2, flexDirection: 'row', alignItems: 'center' }}>
                          <DropDownPicker
                            containerStyle={[{ height: 40 }, switchMerchant ? {
                              height: 35
                            } : {}]}
                            arrowColor={'black'}
                            arrowSize={20}
                            arrowStyle={{ fontWeight: 'bold' }}
                            labelStyle={{ fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14 }}
                            style={{ width: switchMerchant ? 185 : (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 200 : 230, paddingVertical: 0, backgroundColor: Colors.fieldtBgColor, borderRadius: 10 }}
                            placeholderStyle={{ color: Colors.fieldtTxtColor, fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14 }}
                            // items={[{ label: 'Everyone', value: 1 }, { label: 'Active Member', value: 2 }, { label: 'Inactive Member', value: 3 }, { label: 'Female', value: 4 }, { label: 'Male', value: 5 }, { label: 'VIP Only', value: 6 }]}
                            items={EFFECTIVE_DAY_DROPDOWN_LIST}
                            itemStyle={{ justifyContent: 'flex-start', marginLeft: 5, fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14 }}
                            placeholder={'Monday'}
                            multiple
                            multipleText={'%d day(s) selected'}
                            customTickIcon={(press) => (
                              <Ionicon
                                name={'checkbox-outline'}
                                color={
                                  press
                                    ? Colors.fieldtBgColor
                                    : Colors.primaryColor
                                }
                                size={25}
                              />
                            )}
                            onChangeItem={(items) => {
                              // setSelectedEffectiveDay(item.value);
                              setSelectedEffectiveTypeOptions(items);

                              logEventAnalytics({
                                eventName: ANALYTICS.MODULE_UPSELLING_UPSELLING_CAMPAIGN_DD_EFFECTIVE_OPTIONS,
                                eventNameParsed: ANALYTICS_PARSED.MODULE_UPSELLING_UPSELLING_CAMPAIGN_DD_EFFECTIVE_OPTIONS,
                              });
                            }}
                            // defaultValue={selectedEffectiveDay}
                            defaultValue={selectedEffectiveTypeOptions}
                            dropDownMaxHeight={150}
                            dropDownStyle={{ width: switchMerchant ? 185 : (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 200 : 230, height: 150, backgroundColor: Colors.fieldtBgColor, borderRadius: 10, borderWidth: 1 }}
                            globalTextStyle={{
                              fontSize: switchMerchant ? 10 : 14,
                            }}
                          />
                          <TouchableHighlight
                            onPress={() => {
                              if (selectAllDays == true) {
                                setSelectedEffectiveTypeOptions([EFFECTIVE_DAY_DROPDOWN_LIST[0].value]);
                                setSelectAllDays(false);
                              }
                              else {
                                setSelectedEffectiveTypeOptions(EFFECTIVE_DAY_DROPDOWN_LIST.map(item => item.value))
                                // for(var i = 0; i< CHANNEL_TYPE_DROP_DOWN_LIST.length;)
                                // {
                                //   setSelectedAvailability([...selectedAvailability, CHANNEL_TYPE_DROP_DOWN_LIST[i++].value]);
                                //   // console.log('adding data', CHANNEL_TYPE_DROP_DOWN_LIST[i].value)
                                //   // console.log('selectedAvailability in array', selectedAvailability)
                                // }
                                setSelectAllDays(!selectAllDays);
                              }

                              logEventAnalytics({
                                eventName: ANALYTICS.MODULE_UPSELLING_UPSELLING_CAMPAIGN_EFFECTIVE_OPTIONS_C_CHECKMARK,
                                eventNameParsed: ANALYTICS_PARSED.MODULE_UPSELLING_UPSELLING_CAMPAIGN_EFFECTIVE_OPTIONS_C_CHECKMARK,
                              });
                            }}
                          >

                            <View style={{
                              borderRadius: 5,
                              backgroundColor: selectAllDays ? Colors.primaryColor : '#ACACAC',
                              marginRight: 2,
                              marginLeft: 2,
                              alignItems: 'center',
                              justifyContent: 'center',
                              width: 34,
                              height: 34
                            }}>
                              <Icon name="checkmark-sharp" size={20} color={'#FFFFFF'} style={{ margin: 2 }} />
                            </View>
                          </TouchableHighlight>
                        </View>

                        {/* <Text
                        style={[
                          {
                            alignSelf: 'flex-start',
                            fontFamily: 'NunitoSans-Bold',
                            fontSize: 14,
                            fontWeight: '500',

                            marginTop: '2%',

                            zIndex: -3,
                          },
                          switchMerchant
                            ? {
                              fontSize: 10,
                            }
                            : {},
                        ]}>
                        Voucher Generic Code
                      </Text>
                      <TextInput
                        placeholder="PROMO100"
                        placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                        placeholderStyle={{
                          fontFamily: 'NunitoSans-Regular',
                          fontSize: 14,
                        }}
                        style={[
                          {
                            backgroundColor: Colors.fieldtBgColor,
                            // width: 250,
                            width: '100%',
                            height: 40,
                            borderRadius: 5,
                            padding: 5,
                            marginVertical: 5,
                            borderWidth: 1,
                            borderColor: '#E5E5E5',
                            paddingLeft: 10,
                            fontFamily: 'NunitoSans-Regular',
                            fontSize: 14,

                            zIndex: -3,
                          },
                          switchMerchant
                            ? {
                              fontSize: 10,
                              width: 200,
                              height: 35,
                            }
                            : {},
                        ]}
                        onChangeText={(text) => {
                          setVoucherCodeValueGeneric(text);
                        }}
                        defaultValue={voucherCodeValueGeneric}
                      /> */}

                        <View style={{ zIndex: 2, marginTop: '2%', zIndex: -4 }}>
                          <Text
                            style={[
                              {
                                fontWeight: '500',
                                fontFamily: 'NunitoSans-Bold',
                                fontSize: 14,
                              },
                              switchMerchant
                                ? {
                                  fontSize: 10,
                                }
                                : {},
                            ]}>
                            Target Segment(s)
                          </Text>
                          {/* {
                        crmUserTagsDropdownList.find(item => item.value === selectedTargetUserGroup)
                          ?
                          <View style={{marginTop: '2%'}}>
                            <DropDownPicker containerStyle={{ height: 40, zIndex: 2}}
                              arrowColor={'black'}
                              arrowSize={20}
                              arrowStyle={{ fontWeight: 'bold' }}
                              labelStyle={{ fontFamily: 'NunitoSans-Regular', }}
                              style={{ width: 250, height: 40, paddingVertical: 0, backgroundColor: Colors.fieldtBgColor, borderRadius: 5}}
                              placeholderStyle={{ color: Colors.fieldtTxtColor }}
                              // items={[{ label: 'Everyone', value: 1 }, { label: 'Active Member', value: 2 }, { label: 'Inactive Member', value: 3 }, { label: 'Female', value: 4 }, { label: 'Male', value: 5 }, { label: 'VIP Only', value: 6 }]}
                              // items={TARGET_USER_GROUP_DROPDOWN_LIST}
                              itemStyle={{ justifyContent: 'flex-start', marginLeft: 5 }}
                              items={crmUserTagsDropdownList}
                              // placeholder={"Select"}
                              //multiple={true}
                              customTickIcon={(press) => <Ionicon name={"checkbox-outline"} color={press ? Colors.fieldtBgColor : Colors.primaryColor} size={25} />}
                              onChangeItem={item => {
                                setSelectedTargetUserGroup(item.value);
                              }}
                              defaultValue={selectedTargetUserGroup}
                              searchable={true}
                              dropDownMaxHeight={150}
                              dropDownStyle={{ width: 250, height: 150, backgroundColor: Colors.fieldtBgColor, borderRadius: 10, borderWidth: 1, }}
                            />
                          </View>
                          :
                          <></>
                      } */}

                          {(CRM_SEGMENT_DROPDOWN_LIST.concat(
                            crmSegmentsDropdownList,
                          ).find((item) =>
                            selectedTargetSegmentGroupList.includes(item.value),
                          ) || selectedTargetSegmentGroupList.length === 0) ? (
                            <View style={{ marginTop: '2%' }}>
                              <DropDownPicker
                                containerStyle={[
                                  { height: 40, zIndex: 2 },
                                  switchMerchant
                                    ? {
                                      height: 35,
                                    }
                                    : {},
                                ]}
                                arrowColor={'black'}
                                arrowSize={20}
                                arrowStyle={{ fontWeight: 'bold' }}
                                labelStyle={{
                                  fontFamily: 'NunitoSans-Regular',
                                  fontSize: 14,
                                }}
                                style={{
                                  width: switchMerchant ? 185 : (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 200 : 230,
                                  paddingVertical: 0,
                                  backgroundColor: Colors.fieldtBgColor,
                                  borderRadius: 10,
                                }}
                                placeholderStyle={{
                                  color: Colors.fieldtTxtColor,
                                  fontFamily: 'NunitoSans-Regular',
                                  fontSize: 14,
                                }}
                                // items={[{ label: 'Everyone', value: 1 }, { label: 'Active Member', value: 2 }, { label: 'Inactive Member', value: 3 }, { label: 'Female', value: 4 }, { label: 'Male', value: 5 }, { label: 'VIP Only', value: 6 }]}
                                // items={TARGET_USER_GROUP_DROPDOWN_LIST}
                                itemStyle={{
                                  justifyContent: 'flex-start',
                                  marginLeft: 5,
                                  fontFamily: 'NunitoSans-Regular',
                                  fontSize: 14,
                                }}
                                items={CRM_SEGMENT_DROPDOWN_LIST.concat(
                                  crmSegmentsDropdownList,
                                )}
                                // placeholder={"Select"}
                                multiple
                                multipleText={'%d segment(s) selected'}
                                customTickIcon={(press) => (
                                  <Ionicon
                                    name={'checkbox-outline'}
                                    color={
                                      press
                                        ? Colors.fieldtBgColor
                                        : Colors.primaryColor
                                    }
                                    size={25}
                                  />
                                )}
                                onChangeItem={(items) => {
                                  // setSelectedTargetUserGroup(item.value);
                                  setSelectedTargetSegmentGroupList(items);

                                  logEventAnalytics({
                                    eventName: ANALYTICS.MODULE_UPSELLING_UPSELLING_CAMPAIGN_DD_TARGET_SEGMENTS,
                                    eventNameParsed: ANALYTICS_PARSED.MODULE_UPSELLING_UPSELLING_CAMPAIGN_DD_TARGET_SEGMENTS,
                                  });
                                }}
                                defaultValue={selectedTargetSegmentGroupList}
                                searchable
                                dropDownMaxHeight={150}
                                dropDownStyle={{
                                  width: switchMerchant ? 185 : (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 200 : 230,
                                  height: 150,
                                  backgroundColor: Colors.fieldtBgColor,
                                  borderRadius: 10,
                                  borderWidth: 1,
                                }}
                                globalTextStyle={{
                                  fontSize: switchMerchant ? 10 : 14,
                                }}
                              />
                            </View>
                          ) : (
                            <></>
                          )}
                        </View>

                        <View style={{ marginTop: '2%', zIndex: -40 }}>
                          <Text
                            style={[
                              {
                                fontWeight: '500',
                                fontFamily: 'NunitoSans-Bold',
                                fontSize: 14,
                              },
                              switchMerchant
                                ? {
                                  fontSize: 10,
                                }
                                : {},
                            ]}>
                            Section To Show
                          </Text>
                          {/* {
                        crmUserTagsDropdownList.find(item => item.value === selectedTargetUserGroup)
                          ?
                          <View style={{marginTop: '2%'}}>
                            <DropDownPicker containerStyle={{ height: 40, zIndex: 2}}
                              arrowColor={'black'}
                              arrowSize={20}
                              arrowStyle={{ fontWeight: 'bold' }}
                              labelStyle={{ fontFamily: 'NunitoSans-Regular', }}
                              style={{ width: 250, height: 40, paddingVertical: 0, backgroundColor: Colors.fieldtBgColor, borderRadius: 5}}
                              placeholderStyle={{ color: Colors.fieldtTxtColor }}
                              // items={[{ label: 'Everyone', value: 1 }, { label: 'Active Member', value: 2 }, { label: 'Inactive Member', value: 3 }, { label: 'Female', value: 4 }, { label: 'Male', value: 5 }, { label: 'VIP Only', value: 6 }]}
                              // items={TARGET_USER_GROUP_DROPDOWN_LIST}
                              itemStyle={{ justifyContent: 'flex-start', marginLeft: 5 }}
                              items={crmUserTagsDropdownList}
                              // placeholder={"Select"}
                              //multiple={true}
                              customTickIcon={(press) => <Ionicon name={"checkbox-outline"} color={press ? Colors.fieldtBgColor : Colors.primaryColor} size={25} />}
                              onChangeItem={item => {
                                setSelectedTargetUserGroup(item.value);
                              }}
                              defaultValue={selectedTargetUserGroup}
                              searchable={true}
                              dropDownMaxHeight={150}
                              dropDownStyle={{ width: 250, height: 150, backgroundColor: Colors.fieldtBgColor, borderRadius: 10, borderWidth: 1, }}
                            />
                          </View>
                          :
                          <></>
                      } */}

                          <View style={{ marginTop: '2%' }}>
                            <DropDownPicker
                              containerStyle={[
                                { height: 40, zIndex: 2 },
                                switchMerchant
                                  ? {
                                    height: 35,
                                  }
                                  : {},
                              ]}
                              arrowColor={'black'}
                              arrowSize={20}
                              arrowStyle={{ fontWeight: 'bold' }}
                              labelStyle={{
                                fontFamily: 'NunitoSans-Regular',
                                fontSize: 14,
                              }}
                              style={{
                                width: switchMerchant ? 185 : (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 200 : 230,
                                paddingVertical: 0,
                                backgroundColor: Colors.fieldtBgColor,
                                borderRadius: 10,
                              }}
                              placeholderStyle={{
                                color: Colors.fieldtTxtColor,
                                fontFamily: 'NunitoSans-Regular',
                                fontSize: 14,
                              }}
                              // items={[{ label: 'Everyone', value: 1 }, { label: 'Active Member', value: 2 }, { label: 'Inactive Member', value: 3 }, { label: 'Female', value: 4 }, { label: 'Male', value: 5 }, { label: 'VIP Only', value: 6 }]}
                              // items={TARGET_USER_GROUP_DROPDOWN_LIST}
                              itemStyle={{
                                justifyContent: 'flex-start',
                                marginLeft: 5,
                                fontFamily: 'NunitoSans-Regular',
                                fontSize: 14,
                              }}
                              items={UPSELLING_SECTION_DROPDOWN_LIST}
                              // placeholder={"Select"}
                              multiple={false}
                              // multipleText={'%d segment(s) selected'}
                              // customTickIcon={(press) => (
                              //   <Ionicon
                              //     name={'checkbox-outline'}
                              //     color={
                              //       press
                              //         ? Colors.fieldtBgColor
                              //         : Colors.primaryColor
                              //     }
                              //     size={25}
                              //   />
                              // )}
                              onChangeItem={(item) => {
                                // setSelectedTargetUserGroup(item.value);
                                setUpsellingSection(item.value);

                                logEventAnalytics({
                                  eventName: ANALYTICS.MODULE_UPSELLING_UPSELLING_CAMPAIGN_DD_SECTION_TO_SHOW,
                                  eventNameParsed: ANALYTICS_PARSED.MODULE_UPSELLING_UPSELLING_CAMPAIGN_DD_SECTION_TO_SHOW,
                                });
                              }}
                              defaultValue={upsellingSection}
                              searchable
                              dropDownMaxHeight={150}
                              dropDownStyle={{
                                width: switchMerchant ? 185 : (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 200 : 230,
                                height: 150,
                                backgroundColor: Colors.fieldtBgColor,
                                borderRadius: 10,
                                borderWidth: 1,
                              }}
                              globalTextStyle={{
                                fontSize: switchMerchant ? 10 : 14,
                              }}
                            />
                          </View>
                        </View>

                        {/* 2023-02-02 - Hide first */}
                        {/* <Text
                          style={[
                            {
                              fontWeight: '500',
                              zIndex: -10,
                              marginTop: 5,
                              fontFamily: 'NunitoSans-Bold',
                              fontSize: 14,
                            },
                            switchMerchant
                              ? {
                                fontSize: 10,
                              }
                              : {},
                          ]}>
                          Voucher To Tag
                        </Text>
                        <View style={{ marginTop: '2%', zIndex: -10 }}>
                          <DropDownPicker
                            containerStyle={[
                              { height: 40 },
                              switchMerchant ? { height: 35 } : {},
                            ]}
                            arrowColor={'black'}
                            arrowSize={20}
                            arrowStyle={{ fontWeight: 'bold' }}
                            labelStyle={{
                              fontFamily: 'NunitoSans-Regular',
                              fontSize: 14,
                            }}
                            style={[
                              {
                                width: '100%',
                                paddingVertical: 0,
                                backgroundColor: Colors.fieldtBgColor,
                                borderRadius: 10,
                              },
                              switchMerchant
                                ? {
                                  width: '100%',
                                }
                                : {},
                            ]}
                            placeholderStyle={{
                              color: Colors.fieldtTxtColor,
                              fontFamily: 'NunitoSans-Regular',
                              fontSize: 14,
                            }}
                            items={taggableVoucherDropdownList}
                            itemStyle={{
                              justifyContent: 'flex-start',
                              marginLeft: 5,
                              zIndex: 2,
                              fontFamily: 'NunitoSans-Regular',
                              fontSize: 14,
                            }}
                            placeholder={'Select'}
                            //multiple={true}
                            customTickIcon={(press) => (
                              <Ionicon
                                name={'checkbox-outline'}
                                color={
                                  press
                                    ? Colors.fieldtBgColor
                                    : Colors.primaryColor
                                }
                                size={25}
                              />
                            )}
                            onChangeItem={(item) => {
                              setTaggableVoucherId(item.value);
                            }}
                            defaultValue={taggableVoucherId}
                            dropDownMaxHeight={150}
                            dropDownStyle={[
                              {
                                width: '100%',
                                height: 150,
                                backgroundColor: Colors.fieldtBgColor,
                                borderRadius: 10,
                                borderWidth: 1,
                                textAlign: 'left',
                                zIndex: 2,
                              },
                              switchMerchant
                                ? {
                                  width: '100%',
                                }
                                : {},
                            ]}
                            globalTextStyle={{
                              fontSize: switchMerchant ? 10 : 14,
                            }}
                          //dropDownStyle={{ borderWidth: 1, borderColor: '#E5E5E5', borderRadius: 5, width: 200, paddingVertical: 0, margin: 5, marginLeft: 0 }}
                          />
                        </View> */}

                        {/* <View style={{ flexDirection: 'row', marginTop: '2%', zIndex: -4, alignItems: 'center' }}>
                        <View style={{
                          width: '60%',
                          zIndex: -1
                        }}>
                          <Text style={[{ textAlign: 'left', fontWeight: '500', fontFamily: 'NunitoSans-Bold', fontSize: 14 }, switchMerchant ? {
                            fontSize: 10,
                          } : {}]}>
                            Promo Code
                          </Text>
                        </View>

                        <View style={{
                          width: '5%',
                        }}>
                        </View>

                        <View style={{
                          width: '35%',
                        }}>
                          <Text style={[{ textAlign: 'left', color: 'black', fontWeight: '500', fontFamily: 'NunitoSans-Bold', fontSize: 14 }, switchMerchant ? {
                            fontSize: 10,
                          } : {}]}>
                            Usage Limit
                          </Text>
                        </View>
                      </View>

                      <View style={{ flexDirection: 'row', marginTop: 5, zIndex: -4 }}>
                        <View style={{
                          width: '60%',
                          zIndex: -1,
                        }}>
                          <TextInput placeholder='Promo Code'
                            placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                            placeholderStyle={{fontFamily: 'NunitoSans-Regular', fontSize: 14}}
                            style={[{
                              backgroundColor: Colors.fieldtBgColor,
                              // width: 160,
                              height: 40,
                              borderRadius: 5,
                              padding: 5,
                              borderWidth: 1,
                              borderColor: '#E5E5E5',
                              paddingLeft: 10,
                              fontFamily: 'NunitoSans-Regular', 
                              fontSize: 14
                            }, switchMerchant ? {
                              fontSize: 10,
                              height: 35
                            } : {}]}
                            onChangeText={text => {
                              setPromoCode(text);
                            }}
                            defaultValue={promoCode}
                          />
                        </View>

                        <View style={{
                          width: '5%',
                        }}>
                        </View>

                        <View style={{
                          width: '35%',
                        }}>
                          <TextInput placeholder='100'
                            placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                            placeholderStyle={{fontFamily: 'NunitoSans-Regular', fontSize: 14}}
                            style={[{
                              backgroundColor: Colors.fieldtBgColor,
                              // width: 80,
                              height: 40,
                              borderRadius: 5,
                              padding: 5,
                              borderWidth: 1,
                              borderColor: '#E5E5E5',
                              paddingLeft: 27,
                              fontFamily: 'NunitoSans-Regular', 
                              fontSize: 14
                            }, switchMerchant ? {
                              fontSize: 10,
                              height: 35
                            } : {}]}
                            onChangeText={text => {
                              setPromoCodeUsageLimit(text);
                            }}
                            defaultValue={promoCodeUsageLimit}
                            keyboardType={"decimal-pad"}
                          />
                        </View>
                      </View> */}

                        {/* <View style={{ flexDirection: 'row', marginVertical: 5, marginTop: '5%' }}>
                        <CheckBox style={{}}
                          onClick={() => {
                            setState({
                              isChecked: !isChecked
                            })
                          }}
                          checkBoxColor={Colors.fieldtBgColor}
                          uncheckedCheckBoxColor={Colors.tabGrey}
                          checkedCheckBoxColor={Colors.primaryColor}
                          isChecked={isChecked}
                        />
                        <Text style={{ alignSelf: 'center', color: '#828282', fontWeight: '500' }}>
                          Promo Code Usage Limit
                                </Text>
                      </View> */}
                      </View>
                    </View>
                    <View style={{ flexDirection: 'column', marginTop: '5%' }}>
                      {/* <CheckBox style={{ marginLeft: '5%' }}
                      onClick={() => {
                        setIsRequireSpecificProductPurchase(!isRequireSpecificProductPurchase);
                      }}
                      checkBoxColor={Colors.fieldtBgColor}
                      uncheckedCheckBoxColor={Colors.tabGrey}
                      checkedCheckBoxColor={Colors.primaryColor}
                      isChecked={isRequireSpecificProductPurchase}
                    />
                    <Text style={{ marginLeft: '2%' }}>Require specific product purchase</Text> */}
                    </View>
                  </View>
                </View>

                <View style={{
                  paddingTop: 30, zIndex: -3,
                }}>
                  <View
                    style={{
                      flexDirection: 'row',
                      // alignItems: 'center',
                      justifyContent: 'space-between',
                      marginLeft: '2%',
                    }}
                  ><View style={{
                    flexDirection: 'row',

                  }}>
                      <Text
                        style={[
                          {
                            fontWeight: '500',
                            fontFamily: 'NunitoSans-Bold',
                            fontSize: 14,
                            // marginLeft: '2%',
                          },
                          switchMerchant
                            ? {
                              fontSize: 10,
                            }
                            : {},
                        ]}>
                        Product List
                      </Text>

                      <TouchableOpacity
                        style={{
                          flexDirection: 'row',
                          alignItems: 'center',

                          marginLeft: 15,
                        }}
                        onPress={() => {
                          if (variationItemsDropdownList.length > 0) {
                            setProductList([
                              ...productList,
                              {
                                levelId: uuidv4(),
                                levelName: '',

                                productName: variationItemsDropdownList[0].label,
                                productId: variationItemsDropdownList[0].value,
                                productSku: variationItemsDropdownList[0].sku,
                                productPrice: variationItemsDropdownList[0].price,

                                upsellPrice: variationItemsDropdownList[0].price.toFixed(2),

                                tagIdList: [],

                                orderIndex: productList.length,
                                isActive: true,

                                upsellByType: UPSELL_BY_TYPE.ORDER_ITEM,
                              }
                            ]);
                          }

                          logEventAnalytics({
                            eventName: ANALYTICS.MODULE_UPSELLING_UPSELLING_CAMPAIGN_PRODUCT_LIST_C_PLUS,
                            eventNameParsed: ANALYTICS_PARSED.MODULE_UPSELLING_UPSELLING_CAMPAIGN_PRODUCT_LIST_C_PLUS,
                          });
                        }}
                      >
                        <Icon1
                          name="plus-circle"
                          size={switchMerchant ? 15 : 20}
                          color={Colors.primaryColor}
                        />
                      </TouchableOpacity>
                    </View>
                    <View style={{
                      marginRight: '4%',
                      flexDirection: 'row',
                    }}>
                      <Text style={[
                        {
                          fontWeight: '500',
                          fontFamily: 'NunitoSans-Bold',
                          fontSize: 14,
                          // marginLeft: '2%',
                        },
                        switchMerchant
                          ? {
                            fontSize: 10,
                          }
                          : {},
                      ]}>
                        {`Current No. of Products: `}
                      </Text>
                      <Text style={[
                        {
                          fontWeight: '500',
                          fontFamily: 'NunitoSans-Bold',
                          fontSize: 14,
                          // marginLeft: '2%',
                          color: Colors.primaryColor,
                        },
                        switchMerchant
                          ? {
                            fontSize: 10,
                          }
                          : {},
                      ]}>
                        {`${productList.length}`}
                      </Text>
                    </View>
                  </View>


                  <View
                    style={{
                      marginTop: '1%',
                      borderWidth: 1,
                      borderColor: '#E5E5E5',
                      width: '95%',
                      // width: 690,
                      marginHorizontal: 20,
                    }}>
                    <DraggableFlatList
                      // simultaneousHandlers={scrollViewGHRef}
                      // nestedScrollEnabled={true}
                      // scrollEnabled={draggingScroll}
                      // autoscrollThreshold={scrollViewGHRef}
                      // scrollEnabled={true}
                      data={productList}
                      onDragBegin={() => { }}
                      onDragEnd={({ data, from, to }) => {
                        // setDummyTierData(data);

                        var productListTemp = [];
                        for (var i = 0; i < data.length; i++) {
                          productListTemp.push({
                            ...data[i],
                            orderIndex: i,
                          });
                        }

                        setProductList(productListTemp);
                      }}
                      // extraData={outletItems}
                      renderItem={renderTierMenu}
                      keyExtractor={(item, index) => `draggable-item-${index}`}
                      contentContainerStyle={{
                        // paddingLeft: 5,
                        // paddingRight: 5,
                        paddingTop: 10,
                      }}
                    />
                  </View>
                </View>
              </View>

              {/* <View style={{
            flexDirection: 'row', justifyContent: 'flex-end',
            width: '90%',
            marginTop: 20, marginBottom: 20,
          }}>
            <View style={{
              flexDirection: 'row', justifyContent: 'space-between',
              width: '25%',
            }}>
              <TouchableOpacity style={{
                borderWidth: 1, borderColor: Colors.primaryColor, borderRadius: 5, height: 35, width: '48%',
                justifyContent: 'center',
                alignItems: 'center',
              }}
                onPress={() => { props.navigation.goBack() }}
              >
                <Text style={{ alignSelf: 'center', fontSize: 16, fontFamily: 'Nunitosans-Regular', fontWeight: '500', color: Colors.primaryColor }}>
                  Cancel
                    </Text>
              </TouchableOpacity>
              <TouchableOpacity style={{
                borderWidth: 1,
                borderColor: Colors.primaryColor, backgroundColor: Colors.primaryColor, borderRadius: 5, height: 35, width: '48%',
                alignItems: 'center',
                flexDirection: 'row',
                justifyContent: 'center',
              }}
                disabled={isLoading}
                onPress={createPromotion}
              >
                <Text style={{ alignSelf: 'center', fontSize: 16, fontFamily: 'Nunitosans-Regular', fontWeight: '500', color: Colors.whiteColor }}>
                  {isLoading ? 'LOADING...' : 'Save'}
                </Text>

                {isLoading ?
                  <ActivityIndicator color={Colors.whiteColor} size={'small'} />
                  : <></>
                }
              </TouchableOpacity>
            </View>
          </View> */}

              {/* *************NOTIFICATION******************** */}

              <View style={{ height: 60 }} />
            </KeyboardAwareScrollView>
          </KeyboardAvoidingView>
        </ScrollView>
      </View>
    </UserIdleWrapper>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.highlightColor,
    flexDirection: 'row',
  },
  list: {
    backgroundColor: Colors.whiteColor,
    width: Dimensions.get('window').width * 0.87,
    height: Dimensions.get('window').height * 0.825,
    marginTop: 0,
    marginHorizontal: 20,
    alignSelf: 'center',
    borderRadius: 5,
    shadowOpacity: 0,
    shadowColor: '#000',
    shadowOffset: {
      width: 1,
      height: 2,
    },
    shadowOpacity: 0.22,
    shadowRadius: 3.22,
    elevation: 1,
  },
  listItem: {
    marginLeft: 20,
    color: Colors.descriptionColor,
    fontSize: 16,
  },
  sidebar: {
    //width: windowWidth * Styles.sideBarWidth,
    // shadowColor: '#000',
    // shadowOffset: {
    //   width: 0,
    //   height: 8,
    // },
    // shadowOpacity: 0.44,
    // shadowRadius: 10.32,

    // elevation: 16,
  },
  content: {
    padding: 16,
  },
  circleIcon: {
    width: 30,
    height: 30,
    // resizeMode: 'contain',
    marginRight: 10,
    alignSelf: 'center',
  },
  titleList: {
    backgroundColor: '#ffffff',
    flexDirection: 'row',
    paddingVertical: 20,
    paddingHorizontal: 20,
    //marginTop: 20,
    borderBottomWidth: StyleSheet.hairlineWidth,
    borderTopLeftRadius: 8,
    borderTopRightRadius: 8,

    alignItems: 'center',

    // shadowOpacity: 0,
    // shadowColor: '#000',
    // shadowOffset: {
    //   width: 0,
    //   height: 2,
    // },
    // shadowOpacity: 0.22,
    // shadowRadius: 3.22,
    // elevation: 3,
  },
  textTitle: {
    fontSize: 16,
    fontFamily: 'NunitoSans-Bold',
  },
  textItem: {
    fontSize: 14,
    fontFamily: 'NunitoSans-Regular',
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'center',
    // alignContent: 'center',
    alignItems: 'center',
    backgroundColor: 'white',
    padding: 20,
    paddingTop: 15,
    marginTop: 0,
    width: '100%',

    shadowOpacity: 0,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.22,
    shadowRadius: 3.22,
    elevation: 7,
  },
  addVoucher: {
    marginTop: 0,
    //justifyContent: 'center',
    alignItems: 'center',
    //alignContent: 'center',
    width: Dimensions.get('window').width * 0.78,
    backgroundColor: Colors.whiteColor,
    // marginRight: 100,

    borderRadius: 4,

    shadowOpacity: 0,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.22,
    shadowRadius: 3.22,
    elevation: 1,
  },
  textInput: {
    height: 50,
    paddingHorizontal: 20,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 5,
    marginBottom: 20,
    width: 300,
  },
  textInput1: {
    width: 250,
    height: 40,
    backgroundColor: 'white',
    borderRadius: 10,
    // marginLeft: '53%',
    flexDirection: 'row',
    alignContent: 'center',
    alignItems: 'center',

    marginRight: Dimensions.get('window').width * Styles.sideBarWidth,

    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.22,
    shadowRadius: 3.22,
    elevation: 3,
  },
  headerLeftStyle: {
    width: Dimensions.get('window').width * 0.17,
    justifyContent: 'center',
    alignItems: 'center',
  },
  textFieldInput: {
    height: 140,
    width: 650,
    //paddingHorizontal: 20,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 5,
    borderRadius: 5,
  },
  modalView: {
    height: 500,
    width: 415,
    backgroundColor: Colors.whiteColor,
    borderRadius: Dimensions.get('window').width * 0.03,
    padding: 12,
    paddingTop: 25,
    //paddingBottom: 30,
  },
  modalView1: {
    height:
      Platform.OS === 'ios'
        ? Dimensions.get('window').width * 0.7
        : Dimensions.get('window').width * 0.6,
    width:
      Platform.OS === 'ios'
        ? Dimensions.get('window').width * 0.7
        : Dimensions.get('window').width * 0.6,
    backgroundColor: Colors.whiteColor,
    borderRadius: Dimensions.get('window').width * 0.03,
    padding: 20,
    paddingTop: 25,
    //paddingBottom: 30,
  },
  modalView2: {
    height:
      Platform.OS === 'ios'
        ? Dimensions.get('window').width * 0.35
        : Dimensions.get('window').width * 0.25,
    width:
      Platform.OS === 'ios'
        ? Dimensions.get('window').width * 0.5
        : Dimensions.get('window').width * 0.4,
    backgroundColor: Colors.whiteColor,
    borderRadius: Dimensions.get('window').width * 0.03,
    padding: 20,
    paddingTop: 25,
    //paddingBottom: 30,
  },
  modalContainer1: {
    flex: 1,
    backgroundColor: Colors.modalBgColor,
    alignItems: 'center',
    justifyContent: 'center',
  },
  closeButton: {
    position: 'relative',
    alignSelf: 'flex-end',
    marginRight: -10,
    marginTop: -15,

    elevation: 1000,
    zIndex: 1000,
    backgroundColor: Colors.whiteColor,
    borderRadius: 12,
  },
  modalSaveButton: {
    width: Dimensions.get('window').width * 0.15,
    backgroundColor: Colors.fieldtBgColor,
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 8,

    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 3.22,
    elevation: 1,

    marginVertical: 10,
  },
  textInput8: {
    fontFamily: 'NunitoSans-Regular',
    width: 60,
    height: 40,
    flex: 1,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 5,
    paddingHorizontal: 5,
    borderColor: '#E5E5E5',
    borderWidth: 1,
  },
});

export default UpsellingCampaignScreen;
