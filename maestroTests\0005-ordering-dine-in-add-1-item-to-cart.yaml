# flowTest.yaml

appId: com.mykoodoo.promoter

---

- launchApp

- waitForAnimationToEnd:
    timeout: 20000

- runFlow: 
    file: "0002-pin-signin.yaml"

- tapOn:
    id: "SideBar.buttonOperationOrdering" # make sure the sidebar is fully loaded first

- tapOn:
    id: "MoCartScreen.buttonDineIn"

- tapOn:
    id: "MoCartScreen.buttonSelectTable"

- tapOn:
    text: "Auto Tests"

- tapOn:
    text: "AT1"

- tapOn:
    id: "MoOutletMenuScreen.Burger"
    retryTapIfNoChange: false

# - runFlow:
#     when:
#       visible: "ADD TO CART"
#     file: "0005a-ordering-click-back.yaml"

- tapOn:
    text: "Beef Steak Burger"    

- tapOn:
    text: "ADD TO CART"    

- swipe:
    start: 75%, 90%
    end: 75%, 10%

- tapOn:
    text: "PLACE ORDER"    

- tapOn:
    text: "AT1"    

- assertVisible:
    text: "PAY"
