package com.mykoodoo.promoter

import android.app.Activity
import android.content.Context
import android.hardware.display.DisplayManager
import android.os.Build
import android.view.Display

import java.text.SimpleDateFormat
import java.util.*

import com.facebook.react.bridge.ReactApplicationContext
import com.facebook.react.bridge.ReactContextBaseJavaModule
import com.facebook.react.bridge.ReactMethod
import com.facebook.react.bridge.Promise
import com.facebook.react.bridge.ReadableMap
import com.facebook.react.bridge.Callback
import com.facebook.react.bridge.Arguments
import com.facebook.react.bridge.WritableMap
import com.facebook.react.modules.core.DeviceEventManagerModule

// import my.com.softspace.ssmpossdk.Environment;
// import my.com.softspace.ssmpossdk.SSMPOSSDK;
// import my.com.softspace.ssmpossdk.SSMPOSSDKConfiguration;
// import my.com.softspace.ssmpossdk.transaction.MPOSTransaction;
// import my.com.softspace.ssmpossdk.transaction.MPOSTransactionOutcome;
// import my.com.softspace.ssmpossdk.transaction.MPOSTransactionParams;
// import static my.com.softspace.ssmpossdk.transaction.MPOSTransaction.TransactionEvents.TransactionResult.TransactionFailed;
// import static my.com.softspace.ssmpossdk.transaction.MPOSTransaction.TransactionEvents.TransactionResult.TransactionSuccessful;

class KDNativeModule(reactContext: ReactApplicationContext) : ReactContextBaseJavaModule(reactContext) {

    companion object {
      const val CARD_TYPE_VISA = "0"
      const val CARD_TYPE_MASTERCARD = "1"
      const val CARD_TYPE_AMEX = "2"
      const val CARD_TYPE_JCB = "3"
      const val CARD_TYPE_DISCOVER = "23"

      const val TRX_STATUS_APPROVED = "100"
      const val TRX_STATUS_REVERSED = "101"
      const val TRX_STATUS_VOIDED = "102"
      const val TRX_STATUS_PENDING_SIGNATURE = "103"
      const val TRX_STATUS_SETTLED = "104"
      const val TRX_STATUS_PENDING_TC = "105"
      const val TRX_STATUS_REFUNDED = "106"
  }

   private val eventEmitter: DeviceEventManagerModule.RCTDeviceEventEmitter by lazy {
        reactApplicationContext.getJSModule(DeviceEventManagerModule.RCTDeviceEventEmitter::class.java)
    }

    override fun getName(): String {
        return "KDNativeModule"
    }

    @ReactMethod
    fun myNativeFunction(data: ReadableMap, callback: Callback) {
        // Extract data from ReadableMap
        val value1 = data.getString("key1")
        val value2 = data.getInt("key2")
        
        // Create a WritableMap to return the result
        val result: WritableMap = Arguments.createMap()
        result.putString("resultKey1", "Processed: $value1")
        result.putInt("resultKey2", value2 * 2)
        
        // Invoke the callback with the result
        callback.invoke(result)
    }

    @ReactMethod
    fun isRunningOnSeparateDisplay(promise: Promise) {
        val activity = currentActivity
        if (activity == null) {
            promise.reject("ACTIVITY_NULL", "Activity is null")
            return
        }

        /* val displayManager = activity.getSystemService(Context.DISPLAY_SERVICE) as DisplayManager
        val displays = displayManager.displays

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            val currentDisplay = activity.display
            val isOnSeparateDisplay = displays.any { it != currentDisplay && it.state == Display.STATE_ON }
            promise.resolve(isOnSeparateDisplay)
        } else {
            // For older Android versions, we'll consider it's on a separate display if there's more than one display
            promise.resolve(displays.size > 1)
        } */
    }

    /* private void writeLog(String msg) {
         val now = Date()
        val formattedDate = SimpleDateFormat("MM-dd HH:mm:ss.SS", Locale.getDefault()).format(now)
        val logMessage = "$formattedDate\n$msg\n\n"
        
        // Send an event to JavaScript using the saved eventEmitter
        eventEmitter.emit("LogMessage", logMessage)
    }

    private void clearLogs() {
        this.runOnUiThread(() -> {
            tvLogArea.setText("");
            tvLogArea.scrollTo(0,0);
        });
    }

  fun initFasstapMPOSSDK() {
    val context = reactContext.applicationContext

    val config = SSMPOSSDKConfiguration.Builder.create()
    .setAttestationHost(BuildConfig.ATTESTATION_HOST)
    .setAttestationHostReadTimeout(10000L)
    .setAttestationRefreshInterval(300000L)
    .setAttestationStrictHttp(true)
    .setAttestationConnectionTimeout(30000L)
    .setLibGooglePlayProjNum(BuildConfig.PROJECT_NUM)
    .setLibAccessKey(BuildConfig.ACCESS_KEY)
    .setLibSecretKey(BuildConfig.SECRET_KEY)
    .setUniqueID(BuildConfig.UNIQUE_ID)
    .setDeveloperID(BuildConfig.DEVELOPER_ID)
    .setEnvironment(if (BuildConfig.FLAVOR_ENV === "PROD") Environment.PROD else Environment.UAT)
    .build()

    // SDK initialization require activity context
    val activity = currentActivity ?: throw Exception("Activity is null")
    SSMPOSSDK.init(context, config);

    writeLog("SDK Version: " + SSMPOSSDK.getInstance().getSdkVersion());
    writeLog("COTS ID: " + SSMPOSSDK.getInstance().getCotsId());

    if (!SSMPOSSDK.hasRequiredPermission(getApplicationContext())) {
        SSMPOSSDK.requestPermissionIfRequired(activity, 1000);
    }
  }

  private void startTrx() {

        if (!isNfcEnabled(this))
        {
            return;
        }

        if (SSMPOSSDK.requestPermissionIfRequired(MainActivity.this, 10009))
        {
            new Thread() {
                @Override
                public void run() {
                    startEMVProcessing();
                }
            }.start();
        }
        else
        {
        }
    }

    public boolean isNfcEnabled(Context context) {
        NfcAdapter adapter = NfcAdapter.getDefaultAdapter(context);
        if (adapter != null) {
            if (adapter.isEnabled())
            {
                return true;
            }
            else
            {
                runOnUiThread(() -> new AlertDialog.Builder(MainActivity.this)
                        .setMessage(R.string.ALERT_NFC_NOT_ENABLE)
                        .setPositiveButton(R.string.ALERT_BTN_OK, new DialogInterface.OnClickListener() {
                            @Override
                            public void onClick(DialogInterface dialog, int which) {
                                dialog.dismiss();
                            }
                        })
                        .setNegativeButton(R.string.BTN_SETTINGS, new DialogInterface.OnClickListener() {
                            @Override
                            public void onClick(DialogInterface dialog, int which) {
                                Intent intent = new Intent(Settings.ACTION_NFC_SETTINGS);
                                if (intent.resolveActivity(getPackageManager()) != null) {
                                    startActivity(intent);
                                    return;
                                }
                                intent = new Intent(Settings.ACTION_SETTINGS);
                                if (intent.resolveActivity(getPackageManager()) != null) {
                                    startActivity(intent);
                                } else {
                                    new AlertDialog.Builder(MainActivity.this)
                                            .setMessage(R.string.ALERT_NOT_SUPPORTED_MSG)
                                            .setCancelable(true)
                                            .setPositiveButton(R.string.ALERT_BTN_OK, new DialogInterface.OnClickListener() {
                                                @Override
                                                public void onClick(DialogInterface dialog, int which) {
                                                    dialog.dismiss();
                                                }
                                            })
                                            .create()
                                            .show();
                                }
                            }
                        })
                        .setCancelable(false)
                        .create()
                        .show()
                );
                return false;
            }
        }

        // NFC not supported
        btnStartTrx.setEnabled(false);
        return false;
    }

    private void refreshToken()
    {
        writeLog("refreshToken()");
        SSMPOSSDK.getInstance().getSSMPOSSDKConfiguration().uniqueID = edtUserID.getText().toString();
        SSMPOSSDK.getInstance().getSSMPOSSDKConfiguration().developerID = edtDeveloperID.getText().toString();
        SSMPOSSDK.getInstance().getTransaction().refreshToken(this, new MPOSTransaction.TransactionEvents() {
            @Override
            public void onTransactionResult(int result, MPOSTransactionOutcome transactionOutcome) {
                writeLog("onTransactionResult :: " + result);

                if(result == TransactionSuccessful) {
                    btnStartTrx.setEnabled(true);
                    btnVoidTrx.setEnabled(false);
                    btnGetTransactionStatus.setEnabled(false);
                    btnSettlement.setEnabled(true);
                } else {
                    if(transactionOutcome != null) {
                        writeLog(transactionOutcome.getStatusCode() + " - " + transactionOutcome.getStatusMessage());
                    }
                }
            }

            @Override
            public void onTransactionUIEvent(int event) {
                writeLog("onTransactionUIEvent :: " + event);
            }
        });
    }

    private void startEMVProcessing()
    {
        if (edtAmtAuth.getText().toString() != null && Double.parseDouble(edtAmtAuth.getText().toString()) <= 0)
        {
            writeLog("Amount cannot be zero!");
            return;
        }

        // run aync task as blocking.
        this.runOnUiThread(() -> {
            clearLogs();
            writeLog("Amount, Authorised: " + edtAmtAuth.getText().toString());
        });

        try {
            _transactionOutcome = null;

            MPOSTransactionParams transactionalParams = MPOSTransactionParams.Builder.create()
                    .setReferenceNumber(edtRefNo.getText().toString())
                    .setAmount(edtAmtAuth.getText().toString())
                    .build();

            SSMPOSSDK.getInstance().getTransaction().startTransaction(this, transactionalParams, new MPOSTransaction.TransactionEvents() {
                @Override
                public void onTransactionResult(int result, MPOSTransactionOutcome transactionOutcome) {
                    _transactionOutcome = transactionOutcome;
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            writeLog("onTransactionResult :: " + result);

                            if(result == TransactionSuccessful)
                            {
                                edtTrxID.setText(transactionOutcome.getTransactionID());
                                btnVoidTrx.setEnabled(true);
                                btnRefundTrx.setEnabled(true);
                                btnGetTransactionStatus.setEnabled(true);
                                btnUploadSignature.setEnabled(false);

                                String outcome = "Transaction ID :: " + transactionOutcome.getTransactionID() + "\n";
                                outcome += "Reference No :: " + transactionOutcome.getReferenceNo() + "\n";
                                outcome += "Approval code :: " + transactionOutcome.getApprovalCode() + "\n";
                                outcome += "Card number :: " + transactionOutcome.getCardNo() + "\n";
                                outcome += "Cardholder name :: " + transactionOutcome.getCardHolderName() + "\n";
                                outcome += "Acquirer ID :: " + transactionOutcome.getAcquirerID() + "\n";
                                outcome += "Contactless CVM Type :: " + transactionOutcome.getContactlessCVMType() + "\n";
                                outcome += "RRN :: " + transactionOutcome.getRrefNo()+ "\n";
                                outcome += "Trace No :: " + transactionOutcome.getTraceNo()+ "\n";
                                outcome += "Transaction Date Time UTC :: " + transactionOutcome.getTransactionDateTime();
                                writeLog(outcome);

                                if(CARD_TYPE_VISA.equals(transactionOutcome.getCardType()))
                                {
                                    animateVisaSensoryBranding();
                                }
                                else if(CARD_TYPE_MASTERCARD.equals(transactionOutcome.getCardType()))
                                {
                                    animateMastercardSensoryTransaction();
                                }
                                else if(CARD_TYPE_AMEX.equals(transactionOutcome.getCardType()))
                                {
                                    animateAmexSensoryTransaction();
                                }
                                else if(CARD_TYPE_JCB.equals(transactionOutcome.getCardType()))
                                {
                                    animateJCBSensoryTransaction();
                                }
                                else if(CARD_TYPE_DISCOVER.equals(transactionOutcome.getCardType()))
                                {
                                    animateDiscoverSensoryTransaction();
                                }
                            }
                            else if (result == TransactionFailed)
                            {
                                if(transactionOutcome != null)
                                {
                                    String outcome = transactionOutcome.getStatusCode() + " - " + transactionOutcome.getStatusMessage();
                                    if (transactionOutcome.getTransactionID() != null && transactionOutcome.getTransactionID().length() > 0)
                                    {
                                        outcome += "\nTransaction ID :: " + transactionOutcome.getTransactionID() + "\n";
                                        outcome += "Reference No :: " + transactionOutcome.getReferenceNo() + "\n";
                                        outcome += "Approval code :: " + transactionOutcome.getApprovalCode() + "\n";
                                        outcome += "Card number :: " + transactionOutcome.getCardNo() + "\n";
                                        outcome += "Cardholder name :: " + transactionOutcome.getCardHolderName() + "\n";
                                        outcome += "Acquirer ID :: " + transactionOutcome.getAcquirerID() + "\n";
                                        outcome += "RRN :: " + transactionOutcome.getRrefNo() + "\n";
                                        outcome += "Trace No :: " + transactionOutcome.getTraceNo() + "\n";
                                        outcome += "Transaction Date Time UTC :: " + transactionOutcome.getTransactionDateTime();
                                    }
                                    writeLog(outcome);
                                }
                                else
                                {
                                    writeLog("Error ::" + result);
                                }
                            }
                        }
                    });
                }

                @Override
                public void onTransactionUIEvent(int event) {
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            if (event == TransactionUIEvent.CardReadOk)
                            {
                                // you may customize card reads OK sound & vibration, below is some example
                                ToneGenerator toneGenerator = new ToneGenerator(AudioManager.STREAM_MUSIC, ToneGenerator.MAX_VOLUME);
                                toneGenerator.startTone(ToneGenerator.TONE_DTMF_P, 500);

                                Vibrator v = (Vibrator) MainActivity.this.getSystemService(Context.VIBRATOR_SERVICE);
                                if (v.hasVibrator())
                                {
                                    v.vibrate(VibrationEffect.createOneShot(200, VibrationEffect.DEFAULT_AMPLITUDE));
                                }
                                writeLog("Card read completed");
                            }
                            else if (event == TransactionUIEvent.RequestSignature)
                            {
                                writeLog("Signature is required");
                                btnUploadSignature.setEnabled(true);
                            }
                            else
                            {
                                switch (event)
                                {
                                    case TransactionUIEvent.PresentCard:
                                    {
                                        writeLog("Present your card");
                                    }
                                    break;
                                    case TransactionUIEvent.Authorising:
                                    {
                                        writeLog("Authorising...");
                                    }
                                    break;
                                    case TransactionUIEvent.CardPresented:
                                    {
                                        writeLog("Card detected");
                                    }
                                    break;
                                    case TransactionUIEvent.CardReadError:
                                    {
                                        writeLog("Card read failed");
                                    }
                                    case TransactionUIEvent.CardReadRetry:
                                    {
                                        writeLog("Card read retry");
                                    }
                                    break;
                                    default:
                                        writeLog("onTransactionUIEvent :: " + event);
                                        break;
                                }
                            }
                        }
                    });
                }
            });
        } catch (Exception e) {
            Log.e(TAG, e.getMessage(), e);
        }
    }

    private void uploadSignature()
    {
        writeLog("uploadSignature()");

        String base64SignatureString = ""; // your signature image base64 string

        try {
            MPOSTransactionParams transactionalParams = MPOSTransactionParams.Builder.create()
                    .setSignature(base64SignatureString)
                    .build();

            SSMPOSSDK.getInstance().getTransaction().uploadSignature(transactionalParams);

        } catch (Exception e) {
            Log.e(TAG, e.getMessage(), e);
        }
    }

    private void voidTransaction()
    {
        writeLog("voidTransaction()");
        try {
            MPOSTransactionParams transactionalParams = MPOSTransactionParams.Builder.create()
                    .setMPOSTransactionID(edtTrxID.getText().toString())
                    .build();

            SSMPOSSDK.getInstance().getTransaction().voidTransaction(this, transactionalParams, new MPOSTransaction.TransactionEvents() {
                @Override
                public void onTransactionResult(int result, MPOSTransactionOutcome transactionOutcome) {
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            writeLog("onTransactionResult :: " + result);
                            if(result == TransactionSuccessful) {
                                btnVoidTrx.setEnabled(false);
                                if (transactionOutcome != null && transactionOutcome.getTransactionID() != null && transactionOutcome.getTransactionID().length() > 0)
                                {
                                    String outcome = "Status :: " + transactionOutcome.getStatusCode() + " - " + (mapStatusCode(transactionOutcome.getStatusCode()).length()>0?mapStatusCode(transactionOutcome.getStatusCode()):transactionOutcome.getStatusMessage()) + "\n";
                                    outcome += "Transaction ID :: " + transactionOutcome.getTransactionID() + "\n";
                                    outcome += "Reference no :: " + transactionOutcome.getReferenceNo() + "\n";
                                    outcome += "Approval code :: " + transactionOutcome.getApprovalCode() + "\n";
                                    outcome += "Invoice no :: " + transactionOutcome.getInvoiceNo() + "\n";
                                    outcome += "AID :: " + transactionOutcome.getAid() + "\n";
                                    outcome += "Card type :: " + transactionOutcome.getCardType() + "\n";
                                    outcome += "Application label :: " + transactionOutcome.getApplicationLabel() + "\n";
                                    outcome += "Card number :: " + transactionOutcome.getCardNo() + "\n";
                                    outcome += "Cardholder name :: " + transactionOutcome.getCardHolderName()+ "\n";
                                    outcome += "RRN :: " + transactionOutcome.getRrefNo() + "\n";
                                    outcome += "Trace No :: " + transactionOutcome.getTraceNo()+ "\n";
                                    outcome += "Transaction Date Time UTC :: " + transactionOutcome.getTransactionDateTime();
                                    writeLog(outcome);
                                }
                            }
                            else {
                                if (transactionOutcome != null) {
                                    writeLog(transactionOutcome.getStatusCode() + " - " + transactionOutcome.getStatusMessage());
                                }
                            }
                        }
                    });
                }

                @Override
                public void onTransactionUIEvent(int event) {
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            writeLog("onTransactionUIEvent :: " + event);
                        }
                    });
                }
            });
        } catch (Exception e) {
            Log.e(TAG, e.getMessage(), e);
        }
    }

    private void refundTransaction()
    {
        writeLog("refundTransaction()");
        try {
            MPOSTransactionParams transactionalParams = MPOSTransactionParams.Builder.create()
                    .setMPOSTransactionID(edtTrxID.getText().toString())
                    .setAmount(edtAmtAuth.getText().toString())
                    .build();

            SSMPOSSDK.getInstance().getTransaction().refundTransaction(this, transactionalParams, new MPOSTransaction.TransactionEvents() {
                @Override
                public void onTransactionResult(int result, MPOSTransactionOutcome transactionOutcome) {
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            writeLog("onTransactionResult :: " + result);
                            if(result == TransactionSuccessful) {
                                btnRefundTrx.setEnabled(false);
                                if (transactionOutcome != null && transactionOutcome.getTransactionID() != null && transactionOutcome.getTransactionID().length() > 0)
                                {
                                    String outcome = "Status :: " + transactionOutcome.getStatusCode() + " - " + (mapStatusCode(transactionOutcome.getStatusCode()).length()>0?mapStatusCode(transactionOutcome.getStatusCode()):transactionOutcome.getStatusMessage()) + "\n";
                                    outcome += "Transaction ID :: " + transactionOutcome.getTransactionID() + "\n";
                                    outcome += "Reference no :: " + transactionOutcome.getReferenceNo() + "\n";
                                    outcome += "Approval code :: " + transactionOutcome.getApprovalCode() + "\n";
                                    outcome += "Invoice no :: " + transactionOutcome.getInvoiceNo() + "\n";
                                    outcome += "AID :: " + transactionOutcome.getAid() + "\n";
                                    outcome += "Card type :: " + transactionOutcome.getCardType() + "\n";
                                    outcome += "Application label :: " + transactionOutcome.getApplicationLabel() + "\n";
                                    outcome += "Card number :: " + transactionOutcome.getCardNo() + "\n";
                                    outcome += "Cardholder name :: " + transactionOutcome.getCardHolderName() + "\n";
                                    outcome += "RRN :: " + transactionOutcome.getRrefNo() + "\n";
                                    outcome += "Trace No :: " + transactionOutcome.getTraceNo() + "\n";
                                    outcome += "Transaction Date Time UTC :: " + transactionOutcome.getTransactionDateTime();
                                    writeLog(outcome);
                                }
                            }
                            else {
                                if (transactionOutcome != null) {
                                    writeLog(transactionOutcome.getStatusCode() + " - " + transactionOutcome.getStatusMessage());
                                }
                            }
                        }
                    });
                }

                @Override
                public void onTransactionUIEvent(int event) {
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            writeLog("onTransactionUIEvent :: " + event);
                        }
                    });
                }
            });
        } catch (Exception e) {
            Log.e(TAG, e.getMessage(), e);
        }
    }

    private void refundTransactionWithCardPresented()
    {
        writeLog("refundTransactionWithCardPresented()");

        if (edtAmtAuth.getText().toString() != null && Double.parseDouble(edtAmtAuth.getText().toString()) <= 0)
        {
            writeLog("Amount cannot be zero!");
            return;
        }

        // run aync task as blocking.
        this.runOnUiThread(() -> {
            clearLogs();
            writeLog("Amount, Authorised: " + edtAmtAuth.getText().toString());
        });

        try {
            _transactionOutcome = null;

            MPOSTransactionParams transactionalParams = MPOSTransactionParams.Builder.create()
                    .setReferenceNumber(edtRefNo.getText().toString())
                    .setAmount(edtAmtAuth.getText().toString())
                    .setCardRequiredForRefund(true)
                    .build();

            if (transactionalParams.isCardRequiredForRefund())
            {
            }

            SSMPOSSDK.getInstance().getTransaction().refundTransaction(this, transactionalParams, new MPOSTransaction.TransactionEvents() {
                @Override
                public void onTransactionResult(int result, MPOSTransactionOutcome transactionOutcome) {
                    _transactionOutcome = transactionOutcome;
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            writeLog("onTransactionResult :: " + result);

                            if(result == TransactionSuccessful)
                            {
                                edtTrxID.setText(transactionOutcome.getTransactionID());
                                btnVoidTrx.setEnabled(true);
                                btnGetTransactionStatus.setEnabled(true);

                                String outcome = "Transaction ID :: " + transactionOutcome.getTransactionID() + "\n";
                                outcome += "Approval code :: " + transactionOutcome.getApprovalCode() + "\n";
                                outcome += "Card number :: " + transactionOutcome.getCardNo() + "\n";
                                outcome += "Cardholder name :: " + transactionOutcome.getCardHolderName() + "\n";
                                outcome += "RRN :: " + transactionOutcome.getRrefNo();
                                writeLog(outcome);

                                if(CARD_TYPE_VISA.equals(transactionOutcome.getCardType()))
                                {
                                    animateVisaSensoryBranding();
                                }
                                else if(CARD_TYPE_MASTERCARD.equals(transactionOutcome.getCardType()))
                                {
                                    animateMastercardSensoryTransaction();
                                }
                                else if(CARD_TYPE_AMEX.equals(transactionOutcome.getCardType()))
                                {
                                    animateAmexSensoryTransaction();
                                }
                                else if(CARD_TYPE_JCB.equals(transactionOutcome.getCardType()))
                                {
                                    animateJCBSensoryTransaction();
                                }
                            }
                            else
                            {
                                if(transactionOutcome != null)
                                {
                                    writeLog(transactionOutcome.getStatusCode() + " - " + transactionOutcome.getStatusMessage());
                                }
                                else
                                {
                                    writeLog("Error ::" + result);
                                }
                            }
                        }
                    });
                }

                @Override
                public void onTransactionUIEvent(int event) {
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            if (event == TransactionUIEvent.CardReadOk)
                            {
                                // you may customize card reads OK sound & vibration, below is some example
                                ToneGenerator toneGenerator = new ToneGenerator(AudioManager.STREAM_MUSIC, ToneGenerator.MAX_VOLUME);
                                toneGenerator.startTone(ToneGenerator.TONE_DTMF_P, 500);

                                Vibrator v = (Vibrator) MainActivity.this.getSystemService(Context.VIBRATOR_SERVICE);
                                if (v.hasVibrator())
                                {
                                    v.vibrate(VibrationEffect.createOneShot(200, VibrationEffect.DEFAULT_AMPLITUDE));
                                }
                            }
                            writeLog("onTransactionUIEvent :: " + event);
                        }
                    });
                }
            });
        } catch (Exception e) {
            Log.e(TAG, e.getMessage(), e);
        }
    }

    private void getTransactionStatus()
    {
        writeLog("getTransactionStatus()");
        try {
            String trxID = edtTrxID.getText().toString();
            String referenceNo = edtRefNo.getText().toString();

            MPOSTransactionParams transactionalParams = MPOSTransactionParams.Builder.create().build();

            if (trxID.length() > 0)
            {
                transactionalParams = MPOSTransactionParams.Builder.create()
                        .setMPOSTransactionID(edtTrxID.getText().toString())
                        .build();
            }
            else if (referenceNo.length() > 0)
            {
                transactionalParams = MPOSTransactionParams.Builder.create()
                        .setReferenceNumber(edtRefNo.getText().toString())
                        .build();
            }

            SSMPOSSDK.getInstance().getTransaction().getTransactionStatus(this, transactionalParams, new MPOSTransaction.TransactionEvents() {
                @Override
                public void onTransactionResult(int result, MPOSTransactionOutcome transactionOutcome) {
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            if(result == TransactionSuccessful)
                            {
                                if (transactionOutcome.getStatusCode().equals(TRX_STATUS_APPROVED))
                                {
                                    btnVoidTrx.setEnabled(true);
                                }
                                else if (transactionOutcome.getStatusCode().equals(TRX_STATUS_SETTLED))
                                {
                                    btnRefundTrx.setEnabled(true);
                                }
                                String outcome = "Status :: " + transactionOutcome.getStatusCode() + " - " + (mapStatusCode(transactionOutcome.getStatusCode()).length()>0?mapStatusCode(transactionOutcome.getStatusCode()):transactionOutcome.getStatusMessage()) + "\n";
                                outcome += "Reference no :: " + transactionOutcome.getReferenceNo() + "\n";
                                outcome += "Amount auth :: " + transactionOutcome.getAmountAuthorized() + "\n";
                                outcome += "Transaction ID :: " + transactionOutcome.getTransactionID() + "\n";
                                outcome += "Transaction date :: " + transactionOutcome.getTransactionDate() + "\n";
                                outcome += "Batch no :: " + transactionOutcome.getBatchNo() + "\n";
                                outcome += "Approval code :: " + transactionOutcome.getApprovalCode() + "\n";
                                outcome += "Invoice no :: " + transactionOutcome.getInvoiceNo() + "\n";
                                outcome += "AID :: " + transactionOutcome.getAid() + "\n";
                                outcome += "Card type :: " + transactionOutcome.getCardType() + "\n";
                                outcome += "Application label :: " + transactionOutcome.getApplicationLabel() + "\n";
                                outcome += "Card number :: " + transactionOutcome.getCardNo() + "\n";
                                outcome += "Cardholder name :: " + transactionOutcome.getCardHolderName()+ "\n";
                                outcome += "Trace no :: " + transactionOutcome.getTraceNo()+ "\n";
                                outcome += "RRN :: " + transactionOutcome.getRrefNo() + "\n";
                                outcome += "Transaction Date Time UTC :: " + transactionOutcome.getTransactionDateTime();

                                writeLog(outcome);
                            }
                            else
                            {
                                if(transactionOutcome != null)
                                {
                                    writeLog(transactionOutcome.getStatusCode() + " - " + transactionOutcome.getStatusMessage());
                                }
                                else
                                {
                                    writeLog("Error ::" + result);
                                }
                            }
                        }
                    });
                }

                @Override
                public void onTransactionUIEvent(int event) {
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            writeLog("onTransactionUIEvent :: " + event);
                        }
                    });
                }
            });
        } catch (Exception e) {
            Log.e(TAG, e.getMessage(), e);
        }
    }

    private void performSettlement()
    {
        writeLog("performSettlement()");
        try {
            MPOSTransactionParams transactionalParams = MPOSTransactionParams.Builder.create()
                    .build();

            SSMPOSSDK.getInstance().getTransaction().performSettlement(this, transactionalParams, new MPOSTransaction.TransactionEvents() {
                @Override
                public void onTransactionResult(int result, MPOSTransactionOutcome transactionOutcome) {
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            writeLog("onTransactionResult :: " + result);
                            if(result != TransactionSuccessful && transactionOutcome != null) {
                                writeLog(transactionOutcome.getStatusCode() + " - " + transactionOutcome.getStatusMessage());
                            }
                        }
                    });
                }

                @Override
                public void onTransactionUIEvent(int event) {
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            writeLog("onTransactionUIEvent :: " + event);
                        }
                    });
                }
            });
        } catch (Exception e) {
            Log.e(TAG, e.getMessage(), e);
        }
    }
    */
}