import React, {
  Component,
  useReducer,
  useState,
  useEffect,
  useRef,
  useCallback,
} from 'react';
import {
  StyleSheet,
  ScrollView,
  Image,
  View,
  Text,
  Alert,
  TouchableOpacity,
  TextInput,
  Dimensions,
  FlatList,
  Modal,
  PermissionsAndroid,
  Platform,
  useWindowDimensions,
  ActivityIndicator,
  KeyboardAvoidingView,
} from 'react-native';
import Colors from '../constant/Colors';
import SideBar from './SideBar';
import * as User from '../util/User';

import Icon from 'react-native-vector-icons/Feather';
// import TopBar from './TopBar';
import Icon1 from 'react-native-vector-icons/FontAwesome';
import Icon2 from 'react-native-vector-icons/EvilIcons';
import Icon3 from 'react-native-vector-icons/Foundation';
import Icon4 from 'react-native-vector-icons/FontAwesome5';
import DropDownPicker from 'react-native-dropdown-picker';
// import { ceil } from 'react-native-reanimated';
// import CheckBox from '@react-native-community/checkbox';
import DateTimePicker from 'react-native-modal-datetime-picker';
import moment from 'moment';
import Close from 'react-native-vector-icons/AntDesign';
import ApiClient from '../util/ApiClient';
import API from '../constant/API';
import AsyncStorage from '@react-native-async-storage/async-storage';
import Styles from '../constant/Styles';
// import DocumentPicker from "react-native-document-picker";
import Ionicons from 'react-native-vector-icons/Ionicons';
// import RNFetchBlob from "rn-fetch-blob";
// import { isTablet } from "react-native-device-detection";
import 'react-native-get-random-values';
import {customAlphabet} from 'nanoid';
import {
  STOCK_TAKE_STATUS,
  STOCK_TAKE_STATUS_PARSED,
  EMAIL_REPORT_TYPE,
  STOCK_TRANSFER_STATUS,
} from '../constant/common';
import {CommonStore} from '../store/commonStore';
import {MerchantStore} from '../store/merchantStore';
import {UserStore} from '../store/userStore';
import {
  checkIsAllowPromotionVoucherToApply,
  convertArrayToCSV,
  generateEmailReport,
  sliceUnicodeStringV2WithDots,
} from '../util/common';
// import {
//   KeyboardAwareFlatList,
//   KeyboardAwareScrollView,
// } from "react-native-keyboard-aware-scroll-view";
// import RNPickerSelect from "react-native-picker-select";
// import { useKeyboard } from '../hooks';
import AntDesign from 'react-native-vector-icons/AntDesign';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import XLSX from 'xlsx';
import {v4 as uuidv4} from 'uuid';
// import GCalendar from '../assets/svg/GCalendar';
import GCalendar from '../assets/svg/GCalendar.svg';
// import GCalendarGrey from '../assets/svg/GCalendarGrey';
import GCalendarGrey from '../assets/svg/GCalendarGrey.svg';
import {Picker} from 'react-native';
// import {CSVLink} from 'react-csv';
// import Select from 'react-select';
import {OutletStore} from '../store/outletStore';
// import MultiSelect from 'react-multiple-select-dropdown-lite';
// import '../constant/styles.css';
import DatePicker from 'react-native-modal-datetime-picker';
import personicon from '../assets/image/default-profile.png';
import headerLogo from '../assets/image/logo.png';
// import DatePicker from 'react-datepicker';
// import 'react-datepicker/dist/react-datepicker.css';
// import '../constant/datePicker.css';
import APILocal from '../util/apiLocalReplacers';
// import {isMobile} from '../util/common';
import Ionicon from 'react-native-vector-icons/Ionicons';
// import {Html5QrcodeScanner} from 'html5-qrcode';
import {Camera, CameraType} from 'react-native-camera-kit';

const alphabet = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ';
const nanoid = customAlphabet(alphabet, 12);

var isLoadingLocal = false;

// 2024-05-24 - supplier context will be replaced by rack context

const RackStockAdjustment = props => {
  //port til may 11 2023 changes
  const {navigation} = props;

  const {width: windowWidth, height: windowHeight} = useWindowDimensions();

  //   const [keyboardHeight] = useKeyboard();

  const [selectedOutletSupplyItem, setSelectedOutletSupplyItem] = useState({});

  const [stockTake, setStockTake] = useState(true);
  const [addStockTake, setAddStockTake] = useState(false);
  const [addPurchase, setAddPurchase] = useState(false);
  const [editPurchase, setEditPurchase] = useState(false);
  const [addStockTransfer, setAddStockTransfer] = useState(false);
  const [stockList, setStockList] = useState([]);
  const [stockTransferList, setStockTransferList] = useState([]);
  const [stockTakeList, setStockTakeList] = useState([]);
  const [orderList, setOrderList] = useState([]);
  const [itemsToOrder, setItemsToOrder] = useState([{}, {}, {}]);
  const [itemsToOrder2, setItemsToOrder2] = useState([{}, {}, {}]);
  const [addStockTransferList, setAddStockTransferList] = useState([
    {},
    {},
    {},
  ]);
  const [addUnCountedStockTakeList, setAddUnCountedStockTakeList] = useState([
    {},
  ]);
  const [productList, setProductList] = useState([]);
  const [isSelected, setIsSelected] = useState(false);
  const [isSelected2, setIsSelected2] = useState(false);
  const [isSelected3, setIsSelected3] = useState(true);
  const [isSelected4, setIsSelected4] = useState(false);
  const [isDateTimePickerVisible, setIsDateTimePickerVisible] = useState(false);
  const [date, setDate] = useState('');
  const [completedDate, setCompletedDate] = useState('');
  const [visible, setVisible] = useState(false);
  const [Email, setEmail] = useState(null);
  const [modal, setModal] = useState(false);
  // const [// outletId, set// outletId] = useState(1);
  const [outletId, setOutletId] = useState(User.getOutletId());
  const [switchMerchant, setSwitchMerchant] = useState(false);
  const [search, setSearch] = useState('');
  const [search2, setSearch2] = useState('');
  const [search3, setSearch3] = useState('');
  const [ideal, setIdeal] = useState('');
  const [minimum, setMinimum] = useState('');
  const [itemId, setItemId] = useState('');
  const [choose, setChoose] = useState(null);
  const [addCountedStockTakeList, setAddCountedStockTakeList] = useState([
    {
      name: '100 plus',
      sku: 'soda',
      cost: 2,
      expectedQty: 50,
      countedQty: 0,
    },
    {
      name: 'Coke',
      sku: 'soda',
      cost: 2,
      expectedQty: 50,
      countedQty: 0,
    },
    {
      name: 'Beer',
      sku: 'soda',
      cost: 10,
      expectedQty: 20,
      countedQty: 0,
    },
  ]);

  const [loading, setLoading] = useState(false);
  const [remark, setRemark] = useState('');
  const [remark2, setRemark2] = useState('');
  const [addStock, setAddStock] = useState(false);

  //////////////////////////////////////////////////////////////////////

  const [startDate, setStartDate] = useState(Date.now());

  const [poId, setPoId] = useState('');
  const [editMode, setEditMode] = useState(false);

  const [exportEmail, setExportEmail] = useState('');
  const [exportModal, setExportModal] = useState(false);
  const [importModal, setImportModal] = useState(false);

  const [isLoadingExcel, setIsLoadingExcel] = useState(false);
  const [isLoadingCsv, setIsLoadingCsv] = useState(false);

  const [remarkModal, setRemarkModal] = useState(false);

  const [targetOutletDropdownList, setTargetOutletDropdownList] = useState([]);
  const [selectedTargetOutletId, setSelectedTargetOutletId] = useState('');

  // const [supplierDropdownList, setSupplierDropdownList] = useState([{ label: 'All', value: 'ALL' }]);
  const [supplierDropdownList, setSupplierDropdownList] = useState([]);

  const [selectedSupplierId, setSelectedSupplierId] = useState('');
  const [productName, setProductName] = useState('');
  const [productSku, setProductSku] = useState('');
  const [productQuantity, setProductQuantity] = useState('');

  // store the scanned Rack.items (1 of it)
  const [scannedRackItem, setScannedRackItem] = useState(null);

  const [selectedRackId, setSelectedRackId] = useState('');
  const [selectedRack, setSelectedRack] = useState(null);
  const [rackDropdownList, setRackDropdownList] = useState([]);
  const [isButtonDisabled, setIsButtonDisabled] = useState(true);
  const [toShowInsertQuantitySection, setToShowInsertQuantitySection] =
    useState(false);
  const [poStatus, setPoStatus] = useState(STOCK_TAKE_STATUS.CREATED);

  const [outletSupplyItemDropdownList, setOutletSupplyItemDropdownList] =
    useState([]);
  const [expandBarSelection, setExpandBarSelection] = useState(
    props.expandBarSelection === undefined ? false : props.expandBarSelection,
  );
  const [expandLineSelection, setExpandLineSelection] = useState(false);

  const [poItems, setPoItems] = useState([
    {
      outletSupplyItemId: '',
      name: '',
      sku: '',
      skuMerchant: '',
      unit: '',
      quantity: 0,
      countedQuantity: '0',
      diffQuantity: 0,
      diffCost: 0,
      price: 0,
      totalPrice: 0,
      remarks: '',
    },
  ]);

  const [selectedPoItemIndex, setSelectedPoItemIndex] = useState(0);
  const [scannedBarcode, setScannedBarcode] = useState(null);
  const [diffQuantityTotal, setDiffQuantityTotal] = useState(0);
  const [diffCostTotal, setDiffCostTotal] = useState(0);
  const [rackName, setRackName] = useState(''); // State to hold the scanned rack name
  const [outletSupplyItems, setOutletSupplyItems] = useState([]);
  const [showScannerRack, setShowScannerRack] = useState(false);
  const [showScannerProduct, setShowScannerProduct] = useState(false);
  const [showScanner, setShowScanner] = useState(false);
  const [showScannerItem, setShowScannerItem] = useState(false);
  const [cartItemBybc, setCartItemBybc] = useState([]);
  const [sLable, setSLabel] = useState('');
  ////////////////////////////////////////////////////////////////

  // 2024-05-24 - stock take helper changes

  // get Rack by qr code, if not found will return null
  const getRackByQrCode = qrCode => {
    const foundRack = racks.find(rack => rack.qr === qrCode);

    return foundRack || null;
  };

  // set the current active rack (and the items that inside it, to display on ui)
  // can pass null, if needed to reset to no-rack-chosen state
  const setCurrentActiveRack = (rack, overrideList = false) => {
    if (rack) {
      setSelectedRack(rack);
      setSelectedRackId(rack.uniqueId);
      console.log('Rack set in setCurrentActiveRack:', rack);
      console.log('Selected Rack ID in setCurrentActiveRack:', rack.uniqueId);

      if (overrideList) {
        setPoItems(
          Object.entries(rack.items).map(([key, value]) => {
            let foundOutletItem = outletItems.find(
              findItem => findItem.uniqueId === value.id,
            );

            let foundOutletItemPrice = foundOutletItem
              ? foundOutletItem.price
              : 0;

            return {
              outletSupplyItemId: value.id,
              name: value.n,
              sku: value.sku,
              skuMerchant: value.skuM,
              unit: '',
              quantity: value.sc,
              countedQuantity: value.sc,
              diffQuantity: 0,
              diffCost: 0,
              price: foundOutletItemPrice,
              totalPrice: foundOutletItemPrice * value.sc,
              remarks: '',
            };
          }),
        );
      }
    } else {
      setSelectedRack(null);
      setSelectedRackId('NONE');
      console.log('No rack set');
      if (overrideList) {
        setPoItems([]);
      }
    }
  };
  // get rack item (Rack.items[i]), by barcode (will return null if no rack item's barcode matched)
  const getRackItemByBarcode = (barcode, rack) => {
    // const foundRackItem = rack.items.find(rackItem => rackItem.bc === barcode);

    const foundOutletItem = outletItems.find(item => item.bc === barcode);

    if (foundOutletItem) {
      const foundRackItem = rack.items[foundOutletItem.sku];

      return foundRackItem;
    } else {
      return null;
    }
  };

  // insert stock count into the current stock item list (for matched item)
  // will return true if operation done & success
  // will return false if operation failed (ex: stock count not enough)
  const insertStockCountIntoItemList = (rackItem, stockCount, poItems) => {
    // note: poItems[i].outletSupplyItemId = OutletItem.uniqueId

    const matchedPoItemIndex = poItems.findIndex(
      item => item.sku === rackItem.sku,
    );
    const foundOutletItem = outletItems.find(item => item.sku === rackItem.sku);

    if (foundOutletItem) {
      if (matchedPoItemIndex >= 0) {
        // means matched and existed

        const value = stockCount.length > 0 ? parseFloat(stockCount) : 0;

        const stockCountStore = foundOutletItem.stockCount
          ? foundOutletItem.stockCount
          : 0;
        const stockCountOtherRacks = racks.reduce((accum, rack) => {
          if (rack.uniqueId !== selectedRackId) {
            // can proceed

            const rackItemsArr = Object.entries(rack.items).map(
              ([key, value]) => {
                return {
                  ...value,
                };
              },
            );

            const matchedCount = rackItemsArr.reduce(
              (matchedCount, findRackItem) => {
                if (findRackItem.sku === rackItem.sku) {
                  return matchedCount + findRackItem.sc;
                } else {
                  return matchedCount;
                }
              },
              0,
            );

            return accum + matchedCount;
          } else {
            return accum;
          }
        }, 0);
        const stockCountInTransfer = stockTransfersRack
          .filter(
            stockTransfer =>
              stockTransfer.status === STOCK_TRANSFER_STATUS.CREATED ||
              stockTransfer.status === STOCK_TRANSFER_STATUS.ORDERED ||
              stockTransfer.status === STOCK_TRANSFER_STATUS.PARTIALLY_RECEIVED,
          )
          .reduce((accum, stockTransfer) => {
            if (!Array.isArray(stockTransfer.stItems)) {
              console.log('stItems is not an array:', stockTransfer.stItems);
              return accum;
            }
            const matchedCount = stockTransfer.stItems.reduce(
              (matchedCount, stItem) => {
                if (stItem.sku === rackItem.sku) {
                  if (typeof stItem.transferQuantity !== 'number') {
                    console.log(
                      'transferQuantity is not a number:',
                      stItem.transferQuantity,
                    );
                    return matchedCount;
                  }
                  return matchedCount + stItem.transferQuantity;
                } else {
                  return matchedCount;
                }
              },
              0,
            );

            return accum + matchedCount;
          }, 0);

        console.log('stockCountInTransfer:', stockCountInTransfer);

        if (
          value + stockCountOtherRacks + stockCountInTransfer <=
          stockCountStore
        ) {
          // means inputed amount + other rack's amount <= amount in current store, can proceed

          setPoItems(
            poItems.map((poItem, i) =>
              i === matchedPoItemIndex
                ? {
                    ...poItem,
                    countedQuantity: value,
                    diffQuantity: value - poItem.quantity,
                    diffCost: (value - poItem.quantity) * poItem.price,
                    totalPrice:
                      value * poItem.price < 0 ? 0 : value * poItem.price, // total price should be positive
                    updated: true,
                  }
                : poItem,
            ),
          );

          return true;
        } else {
          return false;
        }
      } else {
        return false;
      }
    } else {
      return false;
    }
  };

  ////////////////////////////////////////////////////////////////

  // function end
  let html5QrcodeScannerRef = null;
  // let html5QrcodeScannerRef2 = null

  const handleIconPress = () => {
    setShowScanner(prev => !prev); // Toggle showScanner state
  };
  const handleIconPressRack = () => {
    setShowScannerRack(prev => !prev); // Toggle showScannerRack state
  };

  const handleIconPressProduct = () => {
    setShowScannerProduct(prev => !prev); // Toggle showScannerProduct state
  };

  const qrReaderCallback = useCallback(async () => {
    /*
      if (node !== null && showScannerRack) {
        // do stuff here

        console.log('reader');
        console.log(document.getElementById('reader'));

        let html5QrcodeScannerRef = new Html5QrcodeScanner(
          node.id,
          {fps: 10, qrbox: {width: 250, height: 250}},
          /* verbose=  false,
        );
        html5QrcodeScannerRef.render(onScanSuccess, onScanFailure);
      }*/

    if (qrReaderCallback && qrReaderCallback.current) {
      const photo = await qrReaderCallback.current.capture();
      console.log('Photo taken:', photo.uri);
    }
  }, [showScannerRack, showScannerProduct]);

  const onScanSuccess = async (decodedText, decodedResult) => {
    // handle the scanned code as you like, for example:
    console.log(`Code matched = ${decodedText}`, decodedResult);
    console.log(global.cameraScanState);
    if (global.cameraScanState === 'reader') {
      setShowScannerRack(false);

      try {
        const rack = await getRackByQrCode(decodedText);
        if (rack) {
          console.log('Rack found:', rack);
          setCurrentActiveRack(rack, true);
          setRackName(rack.rackName); // Assuming 'name' is the property for rack's name
        } else {
          alert('This QR code does not belong to any rack.');
        }
      } catch (error) {
        console.error('Error fetching rack:', error);
        alert('Error fetching rack. Please try again.');
      }
    } else {
      setScannedBarcode(decodedText);
    }

    console.log(global.cameraScanState);
  };

  const onScanFailure = error => {
    // handle scan failure, usually better to ignore and keep scanning.
    // for example:
    console.warn(`Code scan error = ${error}`);
  };
  const handleQuantityChange = text => {
    // Update the productQuantity state with the new value
    setProductQuantity(text);
  };

  const handleInsertStockCount = useCallback(
    foundRackItem => {
      if (foundRackItem && productQuantity !== '' && !isNaN(productQuantity)) {
        const success = insertStockCountIntoItemList(
          foundRackItem,
          productQuantity,
          poItems,
        );
        if (success) {
          alert('Success, Quantity updated successfully.');
          CommonStore.update(s => {
            s.selectedStockTakeEdit = null;
          });
          setStockTake(false);
          setAddStockTake(true);
          setToShowInsertQuantitySection(false);
          // setAddStock(false);

          console.log('Stock count inserted successfully.');
        } else {
          console.log('Failed to insert stock count.');
        }
      } else {
        alert(
          'Error, Please select a rack and enter a valid number for quantity.',
        );
      }
    },
    [selectedRack, productQuantity, poItems],
  );

  useEffect(() => {
    if (selectedRack && scannedBarcode) {
      console.log('Selected rack:', selectedRack);
      let product = getRackItemByBarcode(scannedBarcode, selectedRack);

      if (product) {
        console.log('Product found:', product);
        setProductName(product.n);
        setProductSku(product.skuM);
        setProductQuantity(product.sc);
        setScannedRackItem(product);
        setShowScannerProduct(false);
        setShowScannerRack(false);
        setStockTake(false);
        // setAddStockTake(false); // temp fixes
        setAddStock(false);
        setToShowInsertQuantitySection(true); // Only set this if product is found
      } else {
        alert('Product not found in the rack');
        // Handle the case where the product is not found
        setScannedRackItem(null);
      }
      // Reset scannedBarcode after processing
      setScannedBarcode(null);
    }
  }, [selectedRack, scannedBarcode]);

  const racks = CommonStore.useState(s => s.racks);

  const allOutletsSupplyItems = CommonStore.useState(
    s => s.allOutletsSupplyItems,
  );
  const allOutletsSupplyItemsDict = CommonStore.useState(
    s => s.allOutletsSupplyItemsDict,
  );

  const suppliersProduct = CommonStore.useState(s => s.suppliersProduct);
  const allOutlets = MerchantStore.useState(s => s.allOutlets);
  const merchantId = UserStore.useState(s => s.merchantId);

  const stockTakesRack = CommonStore.useState(s => s.stockTakesRack);
  const stockTransfersRack = CommonStore.useState(s => s.stockTransfersRack);

  const outletItems = OutletStore.useState(s => s.outletItems);

  const isLoading = CommonStore.useState(s => s.isLoading);

  const userName = UserStore.useState(s => s.name);
  const userId = UserStore.useState(s => s.firebaseUid);
  const merchantName = MerchantStore.useState(s => s.name);

  const selectedRackStockTakeEdit = CommonStore.useState(
    s => s.selectedRackStockTakeEdit,
  );

  const currOutletId = MerchantStore.useState(s => s.currOutletId);

  const outletSelectDropdownView = CommonStore.useState(
    s => s.outletSelectDropdownView,
  );

  const [rev_date, setRev_date] = useState(
    moment().subtract(6, 'days').startOf('day'),
  );
  const [isDatePickerVisible, setDatePickerVisibility] = useState(false);

  const [rev_date1, setRev_date1] = useState(
    moment().endOf(Date.now()).endOf('day'),
  );
  const [isDatePickerVisible1, setDatePickerVisibility1] = useState(false);

  useEffect(() => {
    if (
      currOutletId !== '' &&
      Array.isArray(allOutlets) &&
      allOutlets.length > 0 &&
      Array.isArray(stockTakesRack) &&
      stockTakesRack.length > 0
    ) {
      var stockTakesProductTemp = [];
      for (var i = 0; i < stockTakesRack.length; i++) {
        if (
          moment(rev_date).isSameOrBefore(stockTakesRack[i].createdAt) &&
          moment(rev_date1).isAfter(stockTakesRack[i].createdAt)
        ) {
          stockTakesProductTemp.push(stockTakesRack[i]);
        }
      }
      stockTakesProductTemp.sort((a, b) => b.orderDate - a.orderDate);
      setStockTakeList(stockTakesProductTemp);
    }
  }, [currOutletId, rev_date, rev_date1, stockTakesRack]);

  useEffect(() => {
    if (selectedRackStockTakeEdit) {
      // insert info

      setPoId(selectedRackStockTakeEdit.stId);
      setPoStatus(selectedRackStockTakeEdit.status);
      setSelectedTargetOutletId(selectedRackStockTakeEdit.outletId);
      // setSelectedSupplierId(selectedRackStockTakeEdit.supplierId);
      setSelectedSupplierId('ALL');

      const foundRack = racks.find(
        rack => rack.uniqueId === selectedRackStockTakeEdit.rackId,
      );
      setSelectedRackId(foundRack ? foundRack.uniqueId : 'NONE');
      setSelectedRack(foundRack ? foundRack : null);

      setDate(selectedRackStockTakeEdit.createdAt);
      setCompletedDate(selectedRackStockTakeEdit.completedDate);
      setRemark(selectedRackStockTakeEdit.remarks);

      console.log('1');

      if (selectedRackStockTakeEdit.stItems) {
        // setPoItems(selectedRackStockTakeEdit.stItems);

        setPoItems(
          selectedRackStockTakeEdit.stItems.map(item => ({
            ...item,
            countedQuantity:
              typeof item.countedQuantity === 'number'
                ? item.countedQuantity.toFixed(0)
                : item.countedQuantity,
            updated: false,
          })),
        );
      }
    } else {
      // designed to always mounted, thus need clear manually...

      // setPoId(nanoid());
      setPoId(
        `STA${((stockTakesRack?.length || 0) + 1).toString().padStart(4, '0')}`,
      );
      setPoStatus(STOCK_TAKE_STATUS.CREATED);
      setSelectedTargetOutletId(currOutletId);
      //setSelectedTargetOutletId(allOutlets.find(allOutlet => allOutlet.uniqueId === currOutletId))
      // suppliersProduct.length > 0 &&
      //   setSelectedSupplierId(suppliersProduct[0].uniqueId);
      setSelectedSupplierId('ALL');
      setSelectedRackId('NONE');
      setSelectedRack(null);
      setDate(Date.now());
      setRemark('');

      console.log('2');

      // if (outletSupplyItems.length > 0 && Object.keys(allOutletsSupplyItemsDict).length > 0) {
      // if (outletSupplyItems.length > 0) {
      //   setPoItems([
      //     {
      //       outletSupplyItemId: outletSupplyItems[0].uniqueId,
      //       name: outletSupplyItems[0].name,
      //       sku: outletSupplyItems[0].sku,
      //       quantity: outletSupplyItems[0].quantity,
      //       transferQuantity: 0,
      //       price: outletSupplyItems[0].price,
      //       totalPrice: 0,
      //     }
      //   ]);
      // }
      // else {
      //   setPoItems([
      //     {
      //       outletSupplyItemId: '',
      //       name: '',
      //       sku: '',
      //       quantity: 0,
      //       transferQuantity: 0,
      //       price: 0,
      //       totalPrice: 0,
      //     }
      //   ]);
      // }
    }
  }, [selectedRackStockTakeEdit, addStockTake]);

  useEffect(() => {
    setPoItems([
      {
        outletSupplyItemId: '',
        name: '',
        sku: '',
        skuMerchant: '',
        unit: '',
        quantity: 0,
        countedQuantity: 0,
        diffQuantity: 0,
        diffCost: 0,
        price: 0,
        totalPrice: 0,
        remarks: '',
      },
    ]);
  }, [selectedTargetOutletId]);

  // useEffect(() => {
  //   var outletSupplyItemsTemp = outletItems.filter((outletSupplyItem) => {
  //     if (
  //       outletSupplyItem.outletId === selectedTargetOutletId &&
  //       (outletSupplyItem.supplierId === selectedSupplierId ||
  //         selectedSupplierId === "ALL")
  //     ) {
  //       return true;
  //     } else {
  //       return false;
  //     }
  //     //return true;
  //   });

  //   setOutletSupplyItems(outletSupplyItemsTemp);
  // }, [
  //   // allOutletsSupplyItems,
  //   outletItems,
  //   selectedTargetOutletId,
  //   selectedSupplierId,
  // ]);

  useEffect(() => {
    var outletSupplyItemsTemp = [];

    if (selectedRack) {
      const rackItemSkuList = selectedRack.itemSkuList;

      outletSupplyItemsTemp = outletItems.filter(outletSupplyItem => {
        if (
          outletSupplyItem.outletId === selectedTargetOutletId &&
          rackItemSkuList.includes(outletSupplyItem.sku)
        ) {
          return true;
        } else {
          return false;
        }
        //return true;
      });
    }

    setOutletSupplyItems(outletSupplyItemsTemp);
  }, [
    // allOutletsSupplyItems,

    // racks,

    outletItems,
    selectedTargetOutletId,
    // selectedSupplierId,
    selectedRackId,
    selectedRack,
  ]);

  useEffect(() => {
    const outletDropdownListTemp = allOutlets.map(outlet => ({
      label: outlet.name,
      value: outlet.uniqueId,
    }));

    setTargetOutletDropdownList(outletDropdownListTemp);

    if (
      selectedTargetOutletId === '' &&
      Array.isArray(allOutlets) &&
      allOutlets.length > 0
    ) {
      setSelectedTargetOutletId(currOutletId);
      //setSelectedTargetOutletId(allOutlets.find(allOutlet => allOutlet.uniqueId === currOutletId ))
    }
  }, [allOutlets]);

  //  useEffect(() => {
  //    setSupplierDropdownList(
  //     suppliersProduct.map((supplier) => ({
  //       label: supplier.name,
  //       value: supplier.uniqueId,
  //     }))
  //   );

  //   if (selectedSupplierId === "" && suppliersProduct.length > 0) {
  // setSelectedSupplierId(suppliersProduct[0].uniqueId);
  //     setSelectedSupplierId('ALL');
  //   }
  // console.log('1');
  // }, [suppliersProduct]);

  // useEffect(() => {
  //   setRackDropdownList(
  //     racks.map((rack) => ({
  //       label: rack.rackName,
  //      value: rack.uniqueId,
  //     }))
  //   );

  // if (selectedRackId === "" && racks.length > 0) {
  //   // setSelectedRackId(racks[0].uniqueId);
  //   setSelectedRackId('ALL');
  // }
  // console.log('1');
  // }, [racks]);

  useEffect(() => {
    setOutletSupplyItemDropdownList(
      outletSupplyItems.map(outletSupplyItem => {
        // if (selectedSupplierId === supplyItem.supplierId) {
        //   return { label: supplyItem.name, value: supplyItem.uniqueId };
        // }

        return {
          label: outletSupplyItem.name,
          value: outletSupplyItem.uniqueId,
        };
      }),
    );

    var poItemsTemp = [];

    if (
      Array.isArray(outletSupplyItems) &&
      outletSupplyItems.length > 0
      // &&
      // Object.keys(allOutletsSupplyItemsDict).length > 0
    ) {
      for (var i = 0; i < outletSupplyItems.length; i++) {
        // const quantity = allOutletsSupplyItemsDict[poItemsTemp[i].outletSupplyItemId] ? allOutletsSupplyItemsDict[poItemsTemp[i].outletSupplyItemId].quantity : 0;
        // const price = allOutletsSupplyItemsDict[poItemsTemp[i].outletSupplyItemId] ? allOutletsSupplyItemsDict[poItemsTemp[i].outletSupplyItemId].price : 0;

        const prevRecord = poItems.find(
          poItem => poItem.outletSupplyItemId === outletSupplyItems[i].uniqueId,
        );

        var record = {};

        if (prevRecord) {
          let stockCountToUse = outletSupplyItems[i].stockCount;
          if (prevRecord.sc !== undefined) {
            stockCountToUse = parseFloat(prevRecord.sc);
          }

          record = {
            ...prevRecord, // inherit current user edited changes
            quantity: stockCountToUse || 0, // check if the supply item sku for this outlet existed | might changed in real time
            price: outletSupplyItems[i].price, // might changed in real time
            diffQuantity:
              (prevRecord.countedQuantity ? prevRecord.countedQuantity : 0) -
              (stockCountToUse || 0),
            diffCost:
              ((prevRecord.countedQuantity ? prevRecord.countedQuantity : 0) -
                (stockCountToUse || 0)) *
              outletSupplyItems[i].price,
            totalPrice:
              (prevRecord.countedQuantity ? prevRecord.countedQuantity : 0) *
              outletSupplyItems[i].price, // need check in future, whether used stock quantity or counted quantity
            // remarks: outletSupplyItems[i].remark2,
          };
        } else {
          record = {
            outletSupplyItemId: outletSupplyItems[i].uniqueId,
            name: outletSupplyItems[i].name,
            unit: '',
            sku: outletSupplyItems[i].sku,
            skuMerchant: outletSupplyItems[i].skuMerchant,
            quantity: outletSupplyItems[i].stockCount || 0, // check if the supply item sku for this outlet existed | might changed in real time
            countedQuantity: outletSupplyItems[i].stockCount || 0,
            diffQuantity: 0,
            diffCost: 0,
            price: outletSupplyItems[i].price, // might changed in real time
            totalPrice:
              (outletSupplyItems[i].stockCount || 0) *
              outletSupplyItems[i].price, // need check in future, whether used stock quantity or counted quantity
            remarks: '',
          };
        }

        poItemsTemp.push(record);
      }
      // console.log('poItemsTemp: ' + poItemsTemp);
      // console.log(
      //   ' OutletSupplyItemDropdownList: ' + outletSupplyItemDropdownList,
      // );
    } else {
      poItemsTemp = [
        {
          outletSupplyItemId: '',
          name: '',
          sku: '',
          skuMerchant: '',
          unit: '',
          quantity: 0,
          countedQuantity: 0,
          diffQuantity: 0,
          diffCost: 0,
          price: 0,
          totalPrice: 0,
          remarks: '',
        },
      ];
    }

    setPoItems(poItemsTemp);

    // if (outletSupplyItems.length > 0 &&
    //   poItems.length === 1 &&
    //   poItems[0].outletSupplyItemId === '') {
    //   setPoItems([
    //     {
    //       outletSupplyItemId: outletSupplyItems[0].uniqueId,
    //       name: outletSupplyItems[0].name,
    //       sku: outletSupplyItems[0].sku,
    //       quantity: outletSupplyItems[0].quantity,
    //       countedQuantity: outletSupplyItems[0].quantity,
    //       diffQuantity: 0,
    //       diffCost: 0,
    //       price: outletSupplyItems[0].price,
    //       totalPrice: 0,
    //     }
    //   ]);
    // }
    // else if (poItems[0].outletSupplyItemId !== '' &&
    //   Object.keys(allOutletsSupplyItemsDict).length > 0) {
    //   var poItemsTemp = [
    //     ...poItems,
    //   ];

    //   for (var i = 0; i < poItemsTemp.length; i++) {
    //     const quantity = allOutletsSupplyItemsDict[poItemsTemp[i].outletSupplyItemId] ? allOutletsSupplyItemsDict[poItemsTemp[i].outletSupplyItemId].quantity : 0;
    //     const price = allOutletsSupplyItemsDict[poItemsTemp[i].outletSupplyItemId] ? allOutletsSupplyItemsDict[poItemsTemp[i].outletSupplyItemId].price : 0;

    //     poItemsTemp[i] = {
    //       ...poItemsTemp[i],
    //       quantity: quantity, // check if the supply item sku for this outlet existed | might changed in real time
    //       price: price, // might changed in real time
    //       diffQuantity: poItemsTemp[i].countedQuantity - quantity,
    //       diffCost: (poItemsTemp[i].countedQuantity - quantity) * price,
    //       totalPrice: quantity * price, // need check in future, whether used stock quantity or counted quantity
    //     };
    //   }

    //   setPoItems(poItemsTemp);
    // }
  }, [
    outletSupplyItems,
    // allOutletsSupplyItemsDict
    // outletItems,
  ]);

  useEffect(() => {
    setDiffQuantityTotal(
      poItems.reduce(
        (accum, poItem) =>
          accum + (poItem.diffQuantity ? poItem.diffQuantity : 0),
        0,
      ),
    );
    setDiffCostTotal(
      poItems.reduce(
        (accum, poItem) => accum + (poItem.diffCost ? poItem.diffCost : 0),
        0,
      ),
    );
  }, [poItems]);

  useEffect(() => {
    requestStoragePermission();

    setPoId(nanoid());
    // setPoId(`STA${(stockTakes.length + 1).toString().padStart(4, '0')}`);
  }, []);

  useEffect(() => {
    if (
      selectedRackStockTakeEdit === null &&
      Array.isArray(stockTakesRack) &&
      stockTakesRack.length > 0
    ) {
      // setPoId(`ST${moment().format('MMM').toUpperCase() + moment().format('YY') + (stockTransfers.length + 1).toString().padStart(4, '0')}`);
      setPoId(`STA${(stockTakesRack.length + 1).toString().padStart(4, '0')}`);
    }
  }, [stockTakesRack]);

  ////////////////////////////////////////////////////////////////

  const setState = () => {};

  // navigation.dangerouslyGetParent().setOptions({
  //   tabBarVisible: false
  // });

  const currOutletShiftStatus = OutletStore.useState(
    s => s.currOutletShiftStatus,
  );

  // const [outletDropdownList, setOutletDropdownList] = useState([]);
  // const [selectedOutletList, setSelectedOutletList] = useState([]); // multi-outlets

  const [openSS, setOpenSS] = useState(false);
  const [openDS, setOpenDS] = useState(false);
  const [openP, setOpenP] = useState(false);

  // var outletNames = [];

  // for (var i = 0; i < allOutlets.length; i++) {
  //   for (var j = 0; j < selectedOutletList.length; j++) {
  //     if (selectedOutletList.includes(allOutlets[i].uniqueId)) {
  //       outletNames.push(allOutlets[i].name);
  //       break;
  //     }
  //   }
  // }

  // useEffect(() => {
  //   setOutletDropdownList(
  //     allOutlets.map((item) => {
  //       return { label: item.name, value: item.uniqueId };
  //     })
  //   );
  // }, [allOutlets]);

  var targetOutletDropdownListTemp = allOutlets.map(outlet => ({
    label: sliceUnicodeStringV2WithDots(outlet.name, 20),
    value: outlet.uniqueId,
  }));

  navigation.setOptions({
    headerBackVisible: false,
    headerLeft: () => (
      <TouchableOpacity
        style={{}}
        onPress={() => {
          props.navigation.goBack();
        }}>
        <View
          style={{
            marginRight: 10,
            display: 'flex',
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'flex-start',
            marginTop: 10,
            // opacity: 0.8,
          }}>
          <Ionicons
            name="chevron-back"
            size={26}
            color={Colors.whiteColor}
            style={{}}
          />

          <Text
            style={{
              color: Colors.whiteColor,
              fontSize: 16,
              textAlign: 'center',
              fontFamily: 'NunitoSans-SemiBold',
              // lineHeight: 22,
              marginTop: -3,
            }}>
            Back
          </Text>
        </View>
      </TouchableOpacity>
    ),
    headerTitle: () => (
      <View
        style={{
          justifyContent: 'center',
          alignItems: 'center',
          bottom: -2,
        }}>
        <Text
          style={{
            fontSize: 22,
            // lineHeight: 25,
            textAlign: 'center',
            fontFamily: 'NunitoSans-Bold',
            color: Colors.whiteColor,
            // opacity: 0.8,
            width: windowWidth * 0.55,
          }}
          numberOfLines={1}>
          Rack Stock Adjustment
        </Text>
      </View>
    ),
    headerRight: () => (
      <View
        style={{
          flexDirection: 'row',
          justifyContent: 'center',
          alignItems: 'center',
        }}>
        <TouchableOpacity
          onPress={() => {
            props.navigation.navigate('Profile');
          }}>
          <Image
            style={{
              width: 32,
              height: 32,
              marginTop: 8,
              marginRight: 10,
            }}
            source={require('../assets/image/drawer.png')}
          />
        </TouchableOpacity>
      </View>
    ),
  });

  // componentDidMount = () => {
  //   // setInterval(() => {
  //   //   getStockOrder()
  //   //   getStockTransfer()
  //   //   getLowStock()
  //   // }, 1000);
  //   getStockOrder()

  // }

  // async componentWillMount = () => {
  //   await requestStoragePermission()
  // }

  const requestStoragePermission = async () => {
    try {
      const granted = await PermissionsAndroid.request(
        PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE,
        {
          title: 'KooDoo Merchant Storage Permission',
          message: 'KooDoo Merchant App needs access to your storage ',
          buttonNegative: 'Cancel',
          buttonPositive: 'OK',
        },
      );
      if (granted === PermissionsAndroid.RESULTS.GRANTED) {
        console.log('Storage permission granted');
      } else {
        console.log('Storage permission denied');
      }
    } catch (err) {
      console.warn(err);
    }
  };

  const convertTemplateToExcelFormat = () => {
    var excelTemplate = [];

    for (var i = 0; i < poItems.length; i++) {
      var excelColumn = {
        'Product Name': poItems[i].name,
        SKU: poItems[i].skuMerchant,
        Unit: poItems[i].unit,
        'Cost (RM)': poItems[i].price,
        'In Stock': poItems[i].quantity,
        Counted: poItems[i].countedQuantity ? poItems[i].countedQuantity : 0,
        'Balance (Qty)': poItems[i].diffQuantity,
        'Cost Difference (RM)': poItems[i].diffCost,
      };
      excelTemplate.push(excelColumn);
    }

    console.log('excelTemplate');
    console.log(excelTemplate);

    return excelTemplate;
  };

  const importSelectFile = async () => {
    //   todo
    // try {
    //   const res = await DocumentPicker.pick({
    //     type: [DocumentPicker.types.xlsx],
    //   });
    //   console.log(res);
    // } catch (err) {
    //   if (DocumentPicker.isCancel(err)) {
    //   } else {
    //     throw err;
    //   }
    // }
  };

  //error show readAsArrayBuffer not implemented
  const importTemplate = file => {
    const promise = new Promise((resolve, reject) => {
      const fileReader = new FileReader();
      fileReader.readAsArrayBuffer(file);

      fileReader.onload = e => {
        const bufferArray = e.target.result;

        const wb = XLSX.read(bufferArray, {type: 'buffer'});

        const wsname = wb.SheetNames[0];

        const ws = wb.Sheets[wsname];

        const data = XLSX.utils.sheet_to_json(ws);

        resolve(data);
      };

      fileReader.onerror = error => {
        reject(error);
      };
    });

    promise.then(d => {
      console.log(d);
    });
  };

  const convertDataToExcelFormat = () => {
    var excelData = [];

    for (var i = 0; i < stockTakeList.length; i++) {
      for (var j = 0; j < stockTakeList[i].stItems.length; j++) {
        var excelRow = {
          'Product Name': stockTakeList[i].stItems[j].name,
          Instock: stockTakeList[i].stItems[j].quantity
            ? stockTakeList[i].stItems[j].quantity.toFixed(0)
            : '0',
          Counted: stockTakeList[i].stItems[j].countedQuantity
            ? stockTakeList[i].stItems[j].countedQuantity
            : '0',
          Balance: stockTakeList[i].stItems[j].diffQuantity
            ? stockTakeList[i].stItems[j].diffQuantity.toFixed(0)
            : '0',

          // 'Product Name': stockTakeList[i].stItems
          //   .map((item) => `${item.name}`)
          //   .join(','),
          // Instock: stockTakeList[i].stItems.map((item) => item.quantity || 0).join(','),
          // Counted: stockTakeList[i].stItems
          //   .map((item) => item.countedQuantity)
          //   .join(','),
          // Balance: stockTakeList[i].stItems
          //   .map((item) => item.diffQuantity)
          //   .join(','),

          'Stock Adjustment ID': stockTakeList[i].stId,
          'Created Date': moment(stockTakeList[i].startDate).format(
            'DD/MM/YYYY',
          ),
          'Completed Date': moment(stockTakeList[i].completedDate).format(
            'DD/MM/YYYY',
          ),
          Description: stockTakeList[i].remark,
          Store: stockTakeList[i].outletName,
          Supplier: stockTakeList[i].supplierName,
          Status: stockTakeList[i].status,
        };

        excelData.push(excelRow);
      }
    }

    console.log('excelData');
    console.log(excelData);

    return excelData;
  };
  const handleExportExcel = () => {
    const excelData = convertDataToExcelFormat();

    var ws = XLSX.utils.json_to_sheet(excelData);
    var wb = XLSX.utils.book_new();

    XLSX.utils.book_append_sheet(wb, ws, 'KooDoo Transfer Report');
    const wbout = XLSX.write(wb, {type: 'binary', bookType: 'xlsx'});
    RNFS.writeFile(
      `${
        Platform.OS === 'ios'
          ? RNFS.DocumentDirectoryPath
          : RNFS.DownloadDirectoryPath
      }/KooDoo-Transfer-Report-${moment().format('YYYY-MM-DD-HH-mm-ss')}.xlsx`,
      wbout,
      'ascii',
    )
      .then(success => {
        Alert.alert(
          'Success',
          `Exported to ${
            Platform.OS === 'ios'
              ? RNFS.DocumentDirectoryPath
              : RNFS.DownloadDirectoryPath
          }/KooDoo-Transfer-Report-${moment().format(
            'YYYY-MM-DD-HH-mm-ss',
          )}.xlsx`,
          [
            {
              text: 'OK',
              onPress: () => {
                CommonStore.update(s => {
                  s.isLoading = false;
                });
                setIsLoadingLocalExcel(false);
                setExportModal(false);

                logEventAnalytics({
                  eventName:
                    ANALYTICS.MODULE_COMPOSITE_STOCK_TRANSFER_DOWNLOAD_MODAL_DOWNLOAD_AS_C_EXCEL_SUCCESS_ALERT,
                  eventNameParsed:
                    ANALYTICS_PARSED.MODULE_COMPOSITE_STOCK_TRANSFER_DOWNLOAD_MODAL_DOWNLOAD_AS_C_EXCEL_SUCCESS_ALERT,
                });
              },
            },
          ],
          {cancelable: false},
        );
        console.log('Success');
      })
      .catch(e => {
        console.log('Error', e);
      });
  };
  // function here
  /*   const showDateTimePicker = () => {
      setState({ isDateTimePickerVisible: true });
    }; */

  const hideDateTimePicker = () => {
    setState({isDateTimePickerVisible: false});
  };

  const handleDatePicked = date => {
    setState({date: date.toString()});
  };

  const renderStockItem = ({item}) =>
    item.items.map(result => {
      return (
        <View
          style={{
            backgroundColor: '#FFFFFF',
            flexDirection: 'row',
            paddingVertical: 20,
            paddingHorizontal: 20,
            borderBottomWidth: StyleSheet.hairlineWidth,
            borderBottomColor: '#C4C4C4',
          }}>
          <Text style={{width: '13%', color: Colors.primaryColor}}>
            {result.name}
          </Text>
          <Text style={{width: '12%'}}>{item.type}</Text>
          <Text
            style={{
              width: '6%',
              color:
                result.itemInventory.quantity < result.itemInventory.ideal_qty
                  ? '#F51B1B'
                  : Colors.blackColor,
            }}>
            {result.itemInventory.quantity}
          </Text>
          <View
            style={{
              width: '12%',
              alignItems: 'center',
            }}>
            <TextInput
              editable={!loading}
              underlineColorAndroid={Colors.whiteColor}
              clearButtonMode="while-editing"
              style={styles.textInput2}
              placeholder={result.itemInventory.ideal_qty.toString()}
              placeholderTextColor={Platform.select({ios: '#a9a9a9'})}
              onChangeText={text => {
                setState({
                  ideal: text.trim(),
                  itemId: result.itemInventory.itemId,
                });
              }}
              value={email}
            />
          </View>
          <View
            style={{
              width: '12%',
              alignItems: 'center',
              marginLeft: 30,
            }}>
            <TextInput
              editable={!loading}
              underlineColorAndroid={Colors.whiteColor}
              clearButtonMode="while-editing"
              style={styles.textInput2}
              placeholder={result.itemInventory.minimum_qty.toString()}
              placeholderTextColor={Platform.select({ios: '#a9a9a9'})}
              onChangeText={text => {
                setState({
                  minimum: text.trim(),
                  itemId: result.itemInventory.itemId,
                });
              }}
              value={email}
            />
          </View>
          <Text style={{width: '10%', marginLeft: 40}}>
            {result.itemInventory.unit === null ? 0 : result.itemInventory.unit}
          </Text>
          <Text style={{width: '11%'}}>15/10/2020</Text>
          <View style={{width: '15%'}}>
            {result.supplyProducts.map(elements => {
              return (
                <Text>{elements === null ? ' ' : elements.supplier.name}</Text>
              );
            })}
          </View>
        </View>
      );
    });

  const renderStockTakeItem = ({item}) => (
    <TouchableOpacity
      style={{
        backgroundColor: '#ffffff',
        flexDirection: 'row',
        paddingVertical: 20,
        paddingLeft: 8,
        borderBottomWidth: StyleSheet.hairlineWidth,
        borderBottomColor: '#c4c4c4',
        alignItems: 'center',
        width: '100%',
      }}
      onPress={() => {
        CommonStore.update(s => {
          s.selectedRackStockTakeEdit = item;
        });

        setStockTake(false);
        setAddStockTake(true);
      }}>
      <View style={{flexDirection: 'row', width: '100%', alignItems: 'center'}}>
        {/* <View style={{ flexDirection: 'row' }}> */}
        <View style={{width: '15%', marginRight: 2}}>
          <Text
            style={{
              color: Colors.primaryColor,
              fontSize: switchMerchant ? 10 : 14,
              fontFamily: 'NunitoSans-Regular',
            }}>
            {item.stId}
          </Text>
        </View>
        <View style={{width: '29%', marginRight: 2}}>
          <Text
            style={{
              fontSize: switchMerchant ? 10 : 14,
              fontFamily: 'NunitoSans-Regular',
            }}>
            {`${moment(item.createdAt).format('DD MMM YYYY')}/\n${
              item.status !== STOCK_TAKE_STATUS.COMPLETED
                ? 'Ongoing'
                : moment(item.completedDate).format('DD MMM YYYY')
            }`}
          </Text>
          {/* <Text style={{ fontSize: 13, }}>
            {moment(item.startDate).format('HH:mmA')}
          </Text> */}
        </View>

        {/* <View style={{ width: "12%", marginHorizontal: 2 }}>
          <Text
            style={{
              fontSize: switchMerchant ? 10 : 14,
              fontFamily: "NunitoSans-Regular",
            }}
          >
            {item.status !== STOCK_TAKE_STATUS.COMPLETED
              ? "Ongoing"
              : moment(item.completedDate).format("DD MMM YYYY")}
          </Text>
          {/* <Text style={{ fontSize: 13, }}>
            {moment(item.updatedAt).format('HH:mmA')}
          </Text> *
      </View> */}

        {/* <Text
          style={{
            width: "12%",
            marginHorizontal: 2,
            fontSize: switchMerchant ? 10 : 14,
            fontFamily: "NunitoSans-Regular",
          }}
          numberOfLines={2}
        >
          {item.staffName ? item.staffName : "N/A"}
        </Text> */}

        <Text
          style={{
            width: '25%',
            marginHorizontal: 2,
            fontSize: switchMerchant ? 10 : 14,
            fontFamily: 'NunitoSans-Regular',
          }}>
          {item.outletName}
        </Text>

        {/* <Text
          style={{
            width: "17%",
            marginHorizontal: 2,
            fontSize: switchMerchant ? 10 : 14,
            fontFamily: "NunitoSans-Regular",
          }}
        >
          {item.supplierName}
        </Text> */}
        <View style={{width: '25%', alignItems: 'baseline', marginLeft: 2}}>
          <View
            style={
              switchMerchant
                ? {
                    alignItems: 'center',
                    backgroundColor: item.status
                      ? Colors.primaryColor
                      : '#fc0000',
                    borderRadius: 10,
                    padding: 10,
                    paddingHorizontal: 10,
                    width: 80,
                  }
                : {
                    alignItems: 'center',
                    backgroundColor: item.status
                      ? Colors.primaryColor
                      : '#fc0000',
                    borderRadius: 10,
                    padding: 10,
                    width: 80,
                    // paddingHorizontal: 10,
                    // width: Dimensions.get("screen").width <= 1024 ? 125 : 140,
                  }
            }>
            <Text
              style={{
                color: item.status ? Colors.whiteColor : Colors.whiteColor,
                fontWeight: '500',
                fontSize: switchMerchant ? 10 : 14,
                fontFamily: 'NunitoSans-Regular',
              }}>
              {STOCK_TAKE_STATUS_PARSED[item.status]}
              {/* Partially Received */}
            </Text>
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );

  const renderOrderItem = ({item}) =>
    item.stockOrderProducts.map((elements, index) => {
      return (
        <TouchableOpacity
          onPress={() => {
            setState({
              lowStockAlert: false,
              purchaseOrder: false,
              stockTransfer: false,
              stockTake: false,
              addPurchase: false,
              editPurchase: true,
              addStockTransfer: false,
              addStockTake: false,
            });
          }}>
          <View
            style={{
              backgroundColor: '#ffffff',
              flexDirection: 'row',
              paddingVertical: 20,
              paddingHorizontal: 20,
              borderBottomWidth: StyleSheet.hairlineWidth,
              borderBottomColor: '#c4c4c4',
            }}>
            <Text style={{width: '11%', color: Colors.primaryColor}}>
              PO{item.id}
            </Text>
            <Text style={{width: '13%'}}>
              {moment(item.createdAt).format('DD/MM/YYYY')}
            </Text>
            <Text style={{width: '15%'}}>
              {moment(item.eta).format('DD/MM/YYYY')}
            </Text>
            <Text style={{width: '15%'}}>{item.outlet.name}</Text>
            <Text style={{width: '17%'}}>
              {item.supplier === null ? '' : item.supplier.name}
            </Text>
            <Text style={{width: '15%'}}>RM{elements.amount}</Text>
            <View
              style={{
                width: '12%',
                alignItems: 'center',
                backgroundColor:
                  item.status == 0
                    ? '#dedede'
                    : item.status == 1
                    ? '#969696'
                    : item.status == 2
                    ? Colors.secondaryColor
                    : Colors.primaryColor,
                borderRadius: 10,
              }}>
              <Text
                style={{
                  color:
                    item.status == 0
                      ? Colors.blackColor
                      : item.status == 1
                      ? Colors.whiteColor
                      : item.status == 2
                      ? Colors.blackColor
                      : Colors.whiteColor,
                  borderRadius: 10,
                }}>
                {item.status == 0
                  ? 'Fail'
                  : item.status == 1
                  ? 'In Progress'
                  : item.status == 2
                  ? 'Arrived'
                  : 'Completed'}
              </Text>
            </View>
          </View>
        </TouchableOpacity>
      );
    });

  const renderItemsToOrder = ({item}) => (
    <View
      style={{
        backgroundColor: '#ffffff',
        flexDirection: 'row',
        paddingVertical: 20,
        paddingHorizontal: 20,
        borderBottomWidth: StyleSheet.hairlineWidth,
        borderBottomColor: '#c4c4c4',
      }}>
      <View style={{width: '8%'}}>
        <input
          defaultValue={isSelected}
          onChange={isSelected => setState({isSelected})}
          style={
            {
              // alignSelf: "center",
              // borderRadius: 15,
              // paddingBottom: 3,
            }
          }
          type={'checkbox'}
          //   checked={allOutlets.length === selectedOutletList.length}
        />
        {/* <CheckBox
          value={isSelected}
          onValueChange={(isSelected) => setState({ isSelected })}
        /> */}
      </View>
      <Text style={{width: '14%', color: '#8f8f8f'}}>Chicken patty</Text>
      <Text style={{width: '16%', color: '#8f8f8f'}}>meat</Text>
      <Text style={{width: '14%', color: '#8f8f8f'}}>50</Text>
      <Text style={{width: '16%', color: '#8f8f8f'}}>50</Text>
      <Text style={{width: '18%', color: '#8f8f8f'}}>RM6.00</Text>
      <Text style={{width: '16%', color: '#8f8f8f'}}>RM300.00</Text>
    </View>
  );

  const renderAddCountedStockTake = ({item, index}) => (
    <View
      style={{
        backgroundColor: '#ffffff',
        flexDirection: 'row',
        paddingVertical: 20,
        paddingHorizontal: 10,
        borderBottomWidth: StyleSheet.hairlineWidth,
        borderBottomColor: '#c4c4c4',
        backgroundColor:
          item.quantity > item.countedQuantity
            ? '#ffe8e8'
            : item.quantity == item.countedQuantity
            ? Colors.fieldtBgColor
            : Colors.lightPrimary,
        //alignContent: 'center',
        alignItems: 'center',
        //justifyContent: 'center',
        //backgroundColor: 'blue',
      }}>
      <Text
        style={{
          width: '35%',
          color: '#949494',
          fontSize: switchMerchant ? 10 : 14,
          fontFamily: 'NunitoSans-Regular',
        }}>
        {item.name}
      </Text>

      <Text
        style={{
          width: '19.7%',
          color: '#949494',

          fontFamily: 'NunitoSans-Regular',
          fontSize: switchMerchant ? 10 : 14,
        }}>
        {item.skuMerchant ? item.skuMerchant : ''}
      </Text>
      {/* <Text
        style={{
          width: "11%",
          color: "#949494",
          marginHorizontal: 2,
          fontFamily: "NunitoSans-Regular",
          fontSize: switchMerchant ? 10 : 14,
        }}
      >
        RM{item.price.toFixed(2).replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1,")}
      </Text> */}
      {/* <Text
        style={{
          width: "12%",
          color: "#949494",
          marginHorizontal: 2,
          fontFamily: "NunitoSans-Regular",
          fontSize: switchMerchant ? 10 : 14,
        }}
      >
        {item.unit}
      </Text> */}
      <Text
        style={{
          width: '15.5%',
          color: '#949494',
          marginHorizontal: 2,
          fontFamily: 'NunitoSans-Regular',
          fontSize: switchMerchant ? 10 : 14,
        }}>
        {item.quantity}
      </Text>

      <View
        style={{
          width: '18%',
          marginHorizontal: 2,
          // backgroundColor: Colors.whiteColor,
          // borderRadius: 10,
          // padding: 10,
          // shadowColor: "#000",
          // shadowOffset: {
          //   width: 0,
          //   height: 2,
          // },
          // shadowOpacity: 0.22,
          // shadowRadius: 3.22,
          // elevation: 1,
        }}>
        <TextInput
          editable={
            !selectedRackStockTakeEdit ||
            (selectedRackStockTakeEdit &&
              selectedRackStockTakeEdit.status !== STOCK_TAKE_STATUS.COMPLETED)
              ? true
              : false
          }
          style={{
            backgroundColor: Colors.fieldtBgColor,
            borderRadius: 5,
            marginVertical: 5,
            marginRight: 5,
            borderWidth: 1,
            borderColor: '#E5E5E5',
            paddingLeft: 10,
            color:
              item.quantity > parseFloat(item.countedQuantity)
                ? '#e02424'
                : Colors.blackColor,
            marginTop: Platform.OS === 'ios' ? 0 : -15,
            marginBottom: Platform.OS === 'ios' ? 0 : -15,
            width: switchMerchant ? 55 : 60,
            padding: 5,
            fontFamily: 'NunitoSans-Regular',
            fontSize: switchMerchant ? 10 : 14,
          }}
          // placeholder="0"
          value={item.countedQuantity === '' ? '' : item.countedQuantity}
          onChangeText={text => {
            // setState({ itemName: text });

            const value = text.length > 0 ? parseFloat(text) : 0;

            setPoItems(
              poItems.map((poItem, i) =>
                i === index
                  ? {
                      ...poItem,
                      countedQuantity: value,
                      diffQuantity: value - poItem.quantity,
                      diffCost: (value - poItem.quantity) * poItem.price,
                      totalPrice:
                        value * poItem.price < 0 ? 0 : value * poItem.price, // total price should be positive
                    }
                  : poItem,
              ),
            );
          }}
          keyboardType={'decimal-pad'}
          placeholderTextColor={Platform.select({ios: '#a9a9a9'})}
        />
      </View>

      {/* <Text
        style={{
          width: "13%",
          color: "#949494",
          marginHorizontal: 2,
          fontFamily: "NunitoSans-Regular",
          fontSize: switchMerchant ? 10 : 14,
          padding: 0,
        }}
      >
        {item.diffQuantity.toFixed(2).replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1,")}
      </Text> */}

      {/* <Text
        style={{
          width: "14%",
          color: "#949494",
          marginLeft: 2,
          fontFamily: "NunitoSans-Regular",
          fontSize: switchMerchant ? 10 : 14,
          padding: 0,
        }}
      >
        {item.diffCost.toFixed(2).replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1,")}
      </Text> */}

      <TouchableOpacity
        style={{
          left: 10,
        }}
        onPress={() => {
          setSelectedPoItemIndex(index);
          setRemarkModal(true);
        }}>
        {/* <Icon3 name="clipboard-pencil" size={20} color="#000" /> */}
        <Icon
          name="clipboard"
          size={switchMerchant ? 15 : 20}
          color={Colors.secondaryColor}
        />
      </TouchableOpacity>
    </View>
  );

  const email = () => {
    var body = {
      stockTransferId: 1,
      email: Email,
    };
    // ApiClient.POST(API.emailStockTransfer, body, false).then((result) => {

    //   if (result == true) {

    //     window.confirm(
    //       'Success',
    //       'The email had sent',
    //       [
    //         { text: "OK", onPress: () => { } }
    //       ],
    //       { cancelable: false },
    //     );
    //   }
    // });
  };

  const CheckStockOrder = () => {
    var body = {
      stockOrderId: 1,
      counted: [
        {
          id: 1,
          soId: 2,
          itemId: 4,
          name: 'food',
          quantity: -118,
          amount: 2,
        },
      ],
    };
    // ApiClient.POST(API.checkStockOrder, body, false).then((result) => {

    //   if (result !== null) {

    //     window.confirm(
    //       'Success',
    //       'The quantity keeped',
    //       [
    //         { text: "OK", onPress: () => { } }
    //       ],
    //       { cancelable: false },
    //     );
    //   }
    // });
  };

  const CheckStockOrderDelete = () => {
    var body = {
      stockOrderId: 1,
      counted: [
        {
          id: 1,
          soId: 2,
          itemId: 4,
          name: 'food',
          quantity: -118,
          amount: 2,
        },
      ],
    };
    // ApiClient.POST(API.checkStockOrderDelete, body, false).then((result) => {

    //   if (result !== null) {

    //     window.confirm(
    //       'Success',
    //       'the quantity reduce to 0',
    //       [
    //         { text: "OK", onPress: () => { } }
    //       ],
    //       { cancelable: false },
    //     );
    //   }
    // });
  };

  //   const createStockOrder = () => {
  //     var body = {
  //       supplierId: choice7,
  //       outletId: choice8,
  //       eta: moment(date).format("YYYY-MM-DD" + " " + "hh:mm:ss"),
  //       items: [
  //         {
  //           id: 5,
  //           sku: "papaya",
  //           quantity: 50,
  //         },
  //         {
  //           id: 6,
  //           sku: "mango",
  //           quantity: 20,
  //         },
  //       ],
  //       remarks: "Order Stock",
  //     };
  //     // ApiClient.POST(API.createStockOrder, body).then((result) => {
  //     //   if (result !== null) {

  //     //     window.confirm(
  //     //       'Success',
  //     //       'The order has created',
  //     //       [
  //     //         {text: "OK", onPress: () => { } }
  //     //       ],
  //     //       {cancelable: false },
  //     //     );
  //     //   }
  //     // });
  //   };

  //   const createStockTransfer = () => {
  //     var body = {
  //       fromOutlet: choice1,
  //       toOutlet: choice3,
  //       email: Email,
  //       items: [
  //         {
  //           id: 11,
  //           quantity: 20,
  //         },
  //         {
  //           id: 12,
  //           quantity: 30,
  //         },
  //       ],
  //       remarks: "Stock Transfer",
  //     };
  //     // ApiClient.POST(API.createStockTransfer, body).then((result) => {

  //     //   if (result.success !== null) {

  //     //     window.confirm(
  //     //       'Success',
  //     //       'The Stock Transfer has created',
  //     //       [
  //     //         {text: "OK", onPress: () => { } }
  //     //       ],
  //     //       {cancelable: false },
  //     //     );
  //     //   }
  //     // });
  //   };

  const editStockOrder = () => {
    var body = {
      productId: 2,
      supplierId: 3,
      remarks: 'meat',
      eta: '10-1-2020 01:46:22',
      itemId: 11,
      name: 'chicken',
      quantity: 100,
    };
    // ApiClient.POST(API.editStockOrder, body).then((result) => {

    //   if (result.success !== null) {

    //     window.confirm(
    //       'Success',
    //       'The Stock Order has edited',
    //       [
    //         { text: "OK", onPress: () => { } }
    //       ],
    //       { cancelable: false },
    //     );
    //   }
    // });
  };

  const editStockTransfer = () => {
    var body = {
      stProduct: 1,
      toOutlet: 2,
      email: Email,
      itemId: 61,
      quantity: 120,
      remarks: 'Stock Transfer',
    };
    // ApiClient.POST(API.editStockTransfer, body).then((result) => {

    //   if (result.success !== null) {

    //     window.confirm(
    //       'Success',
    //       'The Stock Transfer has edited',
    //       [
    //         { text: "OK", onPress: () => { } }
    //       ],
    //       { cancelable: false },
    //     );
    //   }
    // });
  };

  const settingLowStock = () => {
    var body = {
      itemId: itemId,
      ideal: ideal,
      minimum: minimum,
    };
    // ApiClient.POST(API.settingLowStock, body).then((result) => {

    //   if (result.success !== null) {

    //     window.confirm(
    //       'Success',
    //       'The low stock has been setted',
    //       [
    //         { text: "OK", onPress: () => { } }
    //       ],
    //       { cancelable: false },
    //     );
    //   }
    // });
  };
  const exportTemplate = () => {
    const excelTemplate = convertTemplateToExcelFormat();

    var excelFile = `${
      Platform.OS === 'ios'
        ? RNFS.DocumentDirectoryPath
        : RNFS.DownloadDirectoryPath
    }/koodoo-Inventory-Stock-Transfer${moment().format(
      'YYYY-MM-DD-HH-mm-ss',
    )}.xlsx`;
    var excelWorkSheet = XLSX.utils.json_to_sheet(excelTemplate);
    var excelWorkBook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(
      excelWorkBook,
      excelWorkSheet,
      'Stock Transfer Template',
    );

    const workBookData = XLSX.write(excelWorkBook, {
      type: 'binary',
      bookType: 'xlsx',
    });

    RNFS.writeFile(excelFile, workBookData, 'ascii')
      .then(success => {
        // console.log(`wrote file ${excelFile}`);

        Alert.alert(
          'Success',
          `Send to ${excelFile}`,
          [{text: 'OK', onPress: () => {}}],
          {cancelable: false},
        );
      })
      .catch(err => {
        // console.log(err.message);
      });
  };

  const downloadCsv = () => {
    if (stockTransfersProduct) {
      const csvData = convertArrayToCSV(stockTransfersProduct);

      const pathToWrite = `${
        RNFetchBlob.fs.dirs.DownloadDir
      }/koodoo-report-stock-transfer-${moment().format(
        'YYYY-MM-DD-HH-mm-ss',
      )}.csv`;
      // console.log("PATH", pathToWrite)
      RNFetchBlob.fs
        .writeFile(pathToWrite, csvData, 'utf8')
        .then(() => {
          // console.log(`wrote file ${pathToWrite}`);
          // wrote file /storage/emulated/0/Download/data.csv
          Alert.alert(
            'Success',
            `Send to ${pathToWrite}`,
            [{text: 'OK', onPress: () => {}}],
            {cancelable: false},
          );
        })
        .catch(error => console.error(error));
    }

    // var body = {
    //   data: stockTransferList
    // }
    // ApiClient.POST(API.exportDataCSV, body, false).then((result) => {
    //   // console.log("RESULT", result)
    //   if (result !== null) {
    //     const pathToWrite = `${RNFetchBlob.fs.dirs.DownloadDir}/StockTransferData.csv`;
    //     // console.log("PATH", pathToWrite)
    //     RNFetchBlob.fs
    //       .writeFile(pathToWrite, result, 'utf8')
    //       .then(() => {
    //         // console.log(`wrote file ${pathToWrite}`);
    //         // wrote file /storage/emulated/0/Download/data.csv
    //         Alert.alert(
    //           'Success',
    //           'The data had exported',
    //           [{ text: 'OK', onPress: () => { } }],
    //           { cancelable: false },
    //         );
    //       })
    //       .catch(error => console.error(error));
    //   }
    // });
  };

  const createStockTake = (stockTakeStatus = STOCK_TAKE_STATUS.CREATED) => {
    if (
      allOutlets.find(outlet => outlet.uniqueId === selectedTargetOutletId) &&
      selectedRack &&
      selectedRack.uniqueId &&
      Array.isArray(poItems) &&
      poItems.length > 0
      // &&
      // (suppliersProduct.find(
      //   (supplier) => supplier.uniqueId === selectedSupplierId
      // ) ||
      //   selectedSupplierId === "ALL")
    ) {
      const poItemsParsed = poItems.map(item => ({
        ...item,
        countedQuantity: parseFloat(item.countedQuantity),
      }));

      if (selectedRackStockTakeEdit === null) {
        var body = {
          stId: poId,
          stItems: poItemsParsed,
          diffQuantityTotal: +parseFloat(diffQuantityTotal).toFixed(2),
          diffCostTotal: +parseFloat(diffCostTotal).toFixed(2),
          completedDate: moment().valueOf(), // will change to editable in future

          status: stockTakeStatus,

          outletId: selectedTargetOutletId,
          outletName: allOutlets.find(
            outlet => outlet.uniqueId === selectedTargetOutletId,
          ).name,

          supplierId: selectedSupplierId,
          supplierName: '',

          startDate: startDate,

          merchantId: merchantId,
          remarks: remark,

          staffName: userName,
          staffId: userId,

          rackId: selectedRackId,
          rackName: selectedRack ? selectedRack.rackName : '',
          rackItems: Object.entries(selectedRack.items).map(([key, value]) => {
            return {
              ...value,
            };
          }),
        };

        console.log('checking', body);

        APILocal.createStockTakeRack({body: body}).then(result => {
          // ApiClient.POST(API.createStockTakeProduct, body).then((result) => {
          console.log('result', result);

          if (result && result.status === 'success') {
            CommonStore.update(s => {
              s.isLoading = false;
            });
            isLoadingLocal = false;
            if (
              window.confirm('Success\n\nStock adjustment has been saved') ==
              true
            ) {
              CommonStore.update(s => {
                s.selectedRackStockTakeEdit = null;
              });

              setStockTake(true);
              setAddStockTake(false);
              setAddStock(false);
            } else {
              console.log('You canceled!');

              CommonStore.update(s => {
                s.isLoading = false;
              });
              isLoadingLocal = false;
            }
          } else {
            if (
              window.confirm('Error\n\nFailed to create stock adjustment') ==
              true
            ) {
              CommonStore.update(s => {
                s.selectedRackStockTakeEdit = null;
              });

              CommonStore.update(s => {
                s.isLoading = false;
              });
              isLoadingLocal = false;

              setStockTake(true);
              setAddStockTake(false);
              setAddStock(false);
            } else {
              console.log('You canceled!');

              CommonStore.update(s => {
                s.isLoading = false;
              });
              isLoadingLocal = false;
            }
          }
        });
      } else {
        var body = {
          stId: poId,
          stItems: poItemsParsed,
          diffQuantityTotal: +parseFloat(diffQuantityTotal).toFixed(2),
          diffCostTotal: +parseFloat(diffCostTotal).toFixed(2),
          completedDate: moment().valueOf(), // will change to editable in future

          status: stockTakeStatus,

          outletId: selectedTargetOutletId,
          outletName: allOutlets.find(
            outlet => outlet.uniqueId === selectedTargetOutletId,
          ).name,

          supplierId: selectedSupplierId,
          supplierName: '',

          startDate: startDate,

          merchantId: merchantId,
          remarks: remark,

          rackId: selectedRackId,
          rackName: selectedRack ? selectedRack.rackName : '',
          rackItems: Object.entries(selectedRack.items).map(([key, value]) => {
            return {
              ...value,
            };
          }),

          stockTakeId: selectedRackStockTakeEdit.uniqueId,
        };
        console.log('checking', body);
        console.log(body);

        APILocal.updateStockTakeRack({body: body}).then(result => {
          // ApiClient.POST(API.updateStockTakeProduct, body).then((result) => {
          console.log('result', result);

          if (result && result.status === 'success') {
            CommonStore.update(s => {
              s.isLoading = false;
            });
            isLoadingLocal = false;
            if (
              window.confirm(
                'Success\n\nStock adjustment has been completed',
              ) == true
            ) {
              CommonStore.update(s => {
                s.selectedRackStockTakeEdit = null;
              });

              setStockTake(true);
              setAddStockTake(false);
              setAddStock(false);
            } else {
              console.log('You canceled!');

              CommonStore.update(s => {
                s.isLoading = false;
              });
              isLoadingLocal = false;
            }
          } else {
            if (
              window.confirm('Error\n\nFailed to update stock adjustment') ==
              true
            ) {
              CommonStore.update(s => {
                s.selectedRackStockTakeEdit = null;
              });

              CommonStore.update(s => {
                s.isLoading = false;
              });
              isLoadingLocal = false;

              setStockTake(true);
              setAddStockTake(false);
              setAddStock(false);
            } else {
              console.log('You canceled!');

              CommonStore.update(s => {
                s.isLoading = false;
              });
              isLoadingLocal = false;
            }
          }
        });
      }
    } else {
      if (
        window.confirm('Info\n\nItems are empty, or rack is not chosen.') ==
        true
      ) {
        // console.log("You pressed OK!");
      } else {
        // console.log("You canceled!");
      }
    }
  };

  const barFilterPressed = barFilterTapped => {
    setExpandBarSelection(true);
  };

  const lineFilterPressed = lineFilterTapped => {
    setExpandLineSelection(true);
  };

  const flatListRef = useRef();

  const ScrollToTop = () => {
    flatListRef.current.scrollToOffset({animated: true, offset: 0});
  };

  const ScrollToBottom = () => {
    flatListRef.current.scrollToOffset({animated: true, offset: 100});
  };

  const [isSidebarOpen, setIsSidebarOpen] = useState(true);

  const toggleSidebar = () => {
    setIsSidebarOpen(!isSidebarOpen);
  };

  ////////////////////////////////////////////////////////////////

  // function end

  return (
    <View>
      {/*isMobile() && <TopBar navigation={navigation} />*/}

      <View
        style={[styles.container, {height: windowHeight, width: windowWidth}]}>
        {/* <View
          style={[
            styles.sidebar,

          ]}
        >
          <SideBar navigation={navigation} selectedTab={0} isSidebarOpen={isSidebarOpen} toggleSidebar={toggleSidebar} />
        </View> */}
        {/*  )} */}
        <View style={{height: windowHeight, width: windowWidth}}>
          <ScrollView
            showsVerticalScrollIndicator={false}
            style={{}}
            contentContainerStyle={{
              backgroundColor: Colors.highlightColor,
            }}>
            <View style={{width: windowWidth}}>
              <TouchableOpacity
                style={{
                  flexDirection: 'row',
                  alignContent: 'center',
                  alignItems: 'center',
                  marginVertical: 5,
                  marginLeft: 11,
                }}
                onPress={() => props.navigation.goBack()}>
                <MaterialCommunityIcons
                  name="chevron-left"
                  size={switchMerchant ? 20 : 20}
                  color={Colors.primaryColor}
                />
                <Text
                  style={{
                    fontFamily: 'NunitoSans-Bold',
                    fontSize: switchMerchant ? 14 : 16,
                    color: Colors.primaryColor,
                  }}>
                  Back
                </Text>
              </TouchableOpacity>
            </View>

            {/* <ScrollView horizontal={true} showsHorizontalScrollIndicator={true}> */}
            <View style={styles.content}>
              <Modal
                supportedOrientations={['portrait', 'landscape']}
                style={
                  {
                    // flex: 1
                  }
                }
                visible={exportModal}
                transparent={true}
                animationType={'fade'}>
                <View
                  style={{
                    flex: 1,
                    backgroundColor: Colors.modalBgColor,
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}>
                  <View
                    style={{
                      width: Dimensions.get('screen').width * 0.75,
                      backgroundColor: Colors.whiteColor,
                      borderRadius: 12,
                      padding: Dimensions.get('screen').width * 0.05,
                      alignItems: 'center',
                    }}>
                    <TouchableOpacity
                      disabled={isLoading}
                      style={{
                        position: 'absolute',
                        right: Dimensions.get('screen').width * 0.015,
                        top: Dimensions.get('screen').width * 0.01,

                        elevation: 1000,
                        zIndex: 1000,
                      }}
                      onPress={() => {
                        setExportModal(false);
                      }}>
                      <AntDesign
                        name="closecircle"
                        size={switchMerchant ? 15 : 25}
                        color={Colors.fieldtTxtColor}
                      />
                    </TouchableOpacity>
                    <View
                      style={{
                        alignItems: 'center',
                        top: '20%',
                        position: 'absolute',
                      }}>
                      <Text
                        style={{
                          fontFamily: 'NunitoSans-Bold',
                          textAlign: 'center',
                          fontSize: switchMerchant ? 18 : 24,
                        }}>
                        Download Report
                      </Text>
                    </View>
                    <View style={{top: switchMerchant ? '14%' : '10%'}}>
                      <View
                        style={{
                          alignItems: 'center',
                          justifyContent: 'center',
                          //top: '10%',
                          flexDirection: 'row',
                          //   borderWidth:1,
                          marginTop: 30,
                        }}>
                        <TouchableOpacity
                          disabled={isLoading}
                          style={{
                            justifyContent: 'center',
                            flexDirection: 'row',
                            borderWidth: 1,
                            borderColor: Colors.primaryColor,
                            backgroundColor: '#0A1F44',
                            borderRadius: 5,
                            width: switchMerchant ? 100 : 100,
                            paddingHorizontal: 10,
                            height: switchMerchant ? 35 : 40,
                            alignItems: 'center',
                            shadowOffset: {
                              width: 0,
                              height: 2,
                            },
                            shadowOpacity: 0.22,
                            shadowRadius: 3.22,
                            elevation: 1,
                            zIndex: -1,
                            marginRight: 15,
                          }}
                          onPress={() => {
                            if (
                              convertDataToExcelFormat() &&
                              convertDataToExcelFormat().length > 0
                            ) {
                              handleExportExcel();
                            } else {
                              Alert.alert('Info, Empty data to export.');

                              CommonStore.update(s => {
                                s.isLoading = false;
                              });
                              setIsLoadingExcel(false);
                            }
                          }}>
                          {isLoadingExcel ? (
                            <ActivityIndicator
                              size={'small'}
                              color={Colors.whiteColor}
                            />
                          ) : (
                            <Text
                              style={{
                                color: Colors.whiteColor,
                                //marginLeft: 5,
                                fontSize: switchMerchant ? 10 : 16,
                                fontFamily: 'NunitoSans-Bold',
                              }}>
                              EXCEL
                            </Text>
                          )}
                        </TouchableOpacity>

                        <View>
                          {/*  <CSVLink
                            style={{
                              justifyContent: 'center',
                              alignContent: 'center',
                              alignItems: 'center',
                              display: 'inline-block',
                              flexDirection: 'row',
                              textDecoration: 'none',
                              borderWidth: 1,
                              borderColor: Colors.primaryColor,
                              backgroundColor: '#0A1F44',
                              borderRadius: 5,
                              width: 100,
                              paddingHorizontal: 10,
                              height: 40,
                              alignItems: 'center',
                              shadowOffset: {
                                width: 0,
                                height: 2,
                              },
                              shadowOpacity: 0.22,
                              shadowRadius: 3.22,
                              elevation: 1,
                              zIndex: -1,
                            }}
                            onClick={() => {
                              setExportModal(false);
                            }}
                            data={convertDataToExcelFormat()}
                            filename="KooDoo Stock Adjustment Report.csv">
                            <View
                              style={{
                                width: '100%',
                                height: '100%',
                                alignContent: 'center',
                                alignItems: 'center',
                                alignSelf: 'center',
                                justifyContent: 'center',
                              }}>
                              <Text
                                style={{
                                  color: Colors.whiteColor,
                                  //marginLeft: 5,
                                  fontSize: 16,
                                  fontFamily: 'NunitoSans-Bold',
                                }}>
                                CSV
                              </Text>
                            </View>
                          </CSVLink> */}
                        </View>
                      </View>
                    </View>
                  </View>
                </View>
              </Modal>

              {toShowInsertQuantitySection && (
                <Modal
                  animationType="slide"
                  transparent={true}
                  visible={toShowInsertQuantitySection}
                  onRequestClose={() => {
                    setToShowInsertQuantitySection(false);
                  }}>
                  <View
                    style={{
                      flex: 1,
                      justifyContent: 'center',
                      alignItems: 'center',
                      backgroundColor: 'rgba(0, 0, 0, 0.5)',
                    }}></View>
                  <View
                    style={{
                      backgroundColor: Colors.whiteColor,
                      width: windowWidth,
                      height: windowHeight * 0.915,

                      alignSelf: 'center',
                      shadowOpacity: 0,
                      shadowColor: '#000',
                      shadowOffset: {width: 0, height: 2},
                      shadowOpacity: 0.22,
                      shadowRadius: 3.22,
                      elevation: 3,
                      borderRadius: 5,
                    }}>
                    <View style={{marginTop: 20}}>
                      <Text
                        style={{
                          fontSize: 20,
                          fontWeight: 'bold',
                          textAlign: 'center',
                        }}>
                        {' '}
                        Please Fill In The Quantity
                      </Text>
                    </View>

                    <View
                      style={{
                        flexDirection: 'row',
                        // width: "47%",
                        alignItems: 'center',
                        marginTop: 60,
                        marginLeft: 10,
                      }}>
                      <Text
                        style={{
                          fontFamily: 'NunitoSans-Bold',
                          fontSize: switchMerchant ? 10 : 14,
                          width: windowWidth * 0.3,
                        }}>
                        Product Name:
                      </Text>
                      <TextInput
                        editable={false}
                        value={productName}
                        style={{
                          backgroundColor: Colors.whiteColor,
                          width: 200,
                          height: 40,
                          borderRadius: 5,
                          borderWidth: 1,
                          borderColor: '#E5E5E5',
                          flexDirection: 'row',
                          paddingHorizontal: 3,
                        }}
                      />
                    </View>
                    <View
                      style={{
                        flexDirection: 'row',
                        // width: "47%",
                        alignItems: 'center',
                        marginTop: 65,
                        marginLeft: 10,
                      }}>
                      <Text
                        style={{
                          fontFamily: 'NunitoSans-Bold',
                          fontSize: switchMerchant ? 10 : 14,
                          width: windowWidth * 0.3,
                        }}>
                        SKU:
                      </Text>
                      <TextInput
                        editable={false}
                        value={productSku}
                        style={{
                          backgroundColor: Colors.whiteColor,
                          width: 200,
                          height: 40,
                          borderRadius: 5,
                          borderWidth: 1,
                          borderColor: '#E5E5E5',
                          flexDirection: 'row',
                          paddingHorizontal: 3,
                        }}
                      />
                    </View>
                    <View
                      style={{
                        flexDirection: 'row',
                        // width: "47%",
                        alignItems: 'center',
                        marginTop: 65,
                        marginLeft: 10,
                      }}>
                      <Text
                        style={{
                          fontFamily: 'NunitoSans-Bold',
                          fontSize: switchMerchant ? 10 : 14,
                          width: windowWidth * 0.3,
                        }}>
                        Quantity:
                      </Text>
                      <TextInput
                        editable={true}
                        value={productQuantity}
                        onChangeText={handleQuantityChange}
                        style={{
                          backgroundColor: Colors.fieldtBgColor,
                          width: 200,
                          height: 40,
                          borderRadius: 5,
                          borderWidth: 1,
                          borderColor: '#E5E5E5',
                          flexDirection: 'row',
                          paddingHorizontal: 3,
                        }}
                      />
                    </View>
                    <View
                      style={{
                        alignItems: 'center',
                      }}>
                      <TouchableOpacity
                        style={{
                          justifyContent: 'center',
                          marginTop: 65,
                          borderWidth: 1,
                          borderColor: Colors.primaryColor,
                          backgroundColor: '#0A1F44',
                          borderRadius: 5,
                          width: switchMerchant ? 120 : 170,
                          paddingHorizontal: 10,
                          height: switchMerchant ? 35 : 40,
                          alignItems: 'center',
                          shadowOffset: {
                            width: 0,
                            height: 2,
                          },
                          shadowOpacity: 0.22,
                          shadowRadius: 3.22,
                          elevation: 1,
                          zIndex: -1,
                        }}
                        onPress={() => {
                          handleInsertStockCount(scannedRackItem);
                        }}>
                        <Text
                          style={{
                            color: Colors.whiteColor,
                            //marginLeft: 5,
                            fontSize: switchMerchant ? 10 : 16,
                            fontFamily: 'NunitoSans-Bold',
                          }}>
                          CONTINUE
                        </Text>
                      </TouchableOpacity>
                    </View>
                  </View>
                </Modal>
              )}
              <Modal
                supportedOrientations={['portrait', 'landscape']}
                style={
                  {
                    // flex: 1
                  }
                }
                visible={importModal}
                transparent={true}
                animationType={'fade'}>
                <View style={styles.modalContainer}>
                  <View style={[styles.modalViewImport]}>
                    <TouchableOpacity
                      style={styles.closeButton}
                      onPress={() => {
                        // setState({ changeTable: false });
                        setImportModal(false);
                      }}>
                      <AntDesign
                        name="closecircle"
                        size={25}
                        color={Colors.fieldtTxtColor}
                      />
                    </TouchableOpacity>
                    <View style={{padding: 10, margin: 30}}>
                      <View
                        style={[
                          styles.modalTitle1,
                          {justifyContent: 'center', alignItems: 'center'},
                        ]}>
                        <Text
                          style={[
                            styles.modalTitleText1,
                            {fontSize: 16, fontWeight: '500'},
                          ]}>
                          Imported List
                        </Text>
                      </View>
                      {/* <View style={{
                  heigth: 70,
                  marginVertical: 10,
                  borderWidth: 1,
                  borderColor: '#E5E5E5',
                  height: '80%'
                }}>
                <Table borderStyle={{ borderWidth: 1 }}>
                  <Row data={TableData.tableHead} flexArr={[1, 2, 1, 1]} style={{}}/>
                  <TableWrapper style={{}}>
                  <Col data={TableData.tableTitle} style={{flex: 1}} heightArr={[28, 28, 28, 28]} textStyle={{}}/>
                  <Rows data={TableData.tableData} flexArr={[1, 2, 1, 1]} style={{height: 28}} textStyle={{textAlign: 'center'}}/>
                  </TableWrapper>
                </Table>
                </View> */}
                      <View
                        style={{
                          flexDirection: 'row',
                          justifyContent: 'space-between',
                          alignItems: 'center',
                        }}>
                        <View
                          style={{
                            backgroundColor: Colors.primaryColor,
                            width: 150,
                            height: 40,
                            marginVertical: 15,
                            borderRadius: 5,
                            alignSelf: 'center',
                          }}>
                          <TouchableOpacity
                            onPress={() => {
                              importSelectFile();
                            }}>
                            <Text
                              style={{
                                color: Colors.whiteColor,
                                alignSelf: 'center',
                                marginVertical: 10,
                              }}>
                              IMPORT
                            </Text>
                          </TouchableOpacity>
                        </View>
                        <View style={{flexDirection: 'row'}}>
                          <View
                            style={{
                              backgroundColor: Colors.whiteColor,
                              width: 150,
                              height: 40,
                              marginVertical: 15,
                              borderRadius: 5,
                              alignSelf: 'center',
                            }}>
                            <TouchableOpacity
                              onPress={() => {
                                //   todo
                                // setImportTemplate(false);
                              }}>
                              <Text
                                style={{
                                  color: Colors.primaryColor,
                                  alignSelf: 'center',
                                  marginVertical: 10,
                                }}>
                                CANCEL
                              </Text>
                            </TouchableOpacity>
                          </View>
                          <View
                            style={{
                              backgroundColor: Colors.primaryColor,
                              width: 150,
                              height: 40,
                              marginVertical: 15,
                              borderRadius: 5,
                              alignSelf: 'center',
                            }}>
                            <TouchableOpacity onPress={() => {}}>
                              <Text
                                style={{
                                  color: Colors.whiteColor,
                                  alignSelf: 'center',
                                  marginVertical: 10,
                                }}>
                                SAVE
                              </Text>
                            </TouchableOpacity>
                          </View>
                        </View>
                      </View>
                    </View>
                  </View>
                </View>
              </Modal>

              <Modal
                supportedOrientations={['portrait', 'landscape']}
                style={
                  {
                    // flex: 1
                  }
                }
                visible={remarkModal}
                transparent={true}
                animationType={'fade'}>
                <View style={styles.modalContainer}>
                  <View
                    style={[
                      styles.modalView,
                      {
                        height: Dimensions.get('screen').width * 0.3,
                      },
                    ]}>
                    <TouchableOpacity
                      style={styles.closeButton}
                      onPress={() => {
                        setRemarkModal(false);
                      }}>
                      <AntDesign
                        name="closecircle"
                        size={switchMerchant ? 15 : 25}
                        color={Colors.fieldtTxtColor}
                      />
                    </TouchableOpacity>
                    <View
                      style={
                        (styles.modalTitle, {top: '20%', position: 'absolute'})
                      }>
                      <Text style={styles.modalTitleText}>Remarks</Text>
                    </View>
                    <View style={{top: '10%'}}>
                      <View
                        style={{
                          justifyContent: 'center',
                          alignItems: 'center',
                        }}>
                        <TextInput
                          // underlineColorAndroid={Colors.fieldtBgColor}
                          style={{
                            marginTop: '2%',
                            padding: 5,
                            backgroundColor: Colors.fieldtBgColor,
                            width: Dimensions.get('screen').width * 0.3,
                            height: Dimensions.get('screen').height * 0.22,
                            borderRadius: 5,
                            borderWidth: 1,
                            borderColor: '#E5E5E5',
                            marginVertical: 5,
                            paddingTop: Platform.OS == 'ios' ? 10 : 0,
                            paddingLeft: 10,
                            fontFamily: 'NunitoSans-Regular',
                            fontSize: switchMerchant ? 10 : 14,
                            textAlignVertical: 'top',
                            paddingTop: switchMerchant ? 5 : '2%',
                          }}
                          placeholder="Remarks"
                          placeholderStyle={{
                            fontFamily: 'NunitoSans-Regular',
                            fontSize: switchMerchant ? 10 : 14,
                          }}
                          placeholderTextColor={Platform.select({
                            ios: '#a9a9a9',
                          })}
                          onChangeText={text => {
                            // setRemark2(text);

                            setPoItems(
                              poItems.map((poItem, i) =>
                                i === selectedPoItemIndex
                                  ? {
                                      ...poItem,
                                      remarks: text,
                                    }
                                  : poItem,
                              ),
                            );
                          }}
                          value={poItems[selectedPoItemIndex].remarks}
                          multiline={true}
                        />
                      </View>
                      <View
                        style={{
                          alignItems: 'center',
                          // top: '7%',
                          //marginTop: 10,
                          marginTop: 20,
                        }}>
                        <TouchableOpacity
                          style={{
                            justifyContent: 'center',
                            flexDirection: 'row',
                            borderWidth: 1,
                            borderColor: Colors.primaryColor,
                            backgroundColor: '#0A1F44',
                            borderRadius: 5,
                            width: 100,
                            paddingHorizontal: 10,
                            height: switchMerchant ? 35 : 40,
                            alignItems: 'center',
                            shadowOffset: {
                              width: 0,
                              height: 2,
                            },
                            shadowOpacity: 0.22,
                            shadowRadius: 3.22,
                            elevation: 1,
                            zIndex: -1,
                          }}
                          //onPress={() => { setRemarkModal(false) }}>
                          onPress={() => {
                            setRemarkModal(false);
                          }}>
                          <Text
                            style={{
                              color: Colors.whiteColor,
                              //marginLeft: 5,
                              fontSize: switchMerchant ? 10 : 16,
                              fontFamily: 'NunitoSans-Bold',
                            }}>
                            DONE
                          </Text>
                        </TouchableOpacity>
                      </View>
                    </View>
                  </View>
                </View>
              </Modal>

              {stockTake ? (
                <View style={{}}>
                  <View
                    style={{
                      width: windowWidth,
                      paddingHorizontal: 15,
                      alignSelf: 'center',
                    }}>
                    <View
                      style={{
                        width: windowWidth * 0.9,
                        marginLeft: 15,
                        alignSelf: 'center',
                      }}>
                      <View
                        style={{alignItems: 'center', flexDirection: 'row'}}>
                        <Text
                          style={{
                            fontSize: switchMerchant ? 20 : 26,
                            fontFamily: 'NunitoSans-Bold',
                          }}>
                          {stockTakeList.length} Stock Adjustment
                        </Text>
                      </View>
                      <View
                        style={{
                          flexDirection: 'row',
                          justifyContent: 'space-between',
                          marginVertical: 10,
                        }}>
                        <View style={{alignItem: 'center'}}>
                          <TouchableOpacity
                            style={{
                              justifyContent: 'center',
                              flexDirection: 'row',
                              borderWidth: 1,
                              borderColor: Colors.primaryColor,
                              backgroundColor: '#0A1F44',
                              borderRadius: 5,
                              //width: 160,
                              paddingHorizontal: 10,
                              width: switchMerchant ? 200 : 250,
                              height: switchMerchant ? 35 : 40,
                              alignItems: 'center',
                              shadowOffset: {
                                width: 0,
                                height: 2,
                              },
                              shadowOpacity: 0.22,
                              shadowRadius: 3.22,
                              elevation: 1,
                              zIndex: -1,
                            }}
                            onPress={() => {
                              setExportModal(true);
                            }}>
                            <View
                              style={{
                                flexDirection: 'row',
                                alignItems: 'center',
                              }}>
                              <Icon
                                name="download"
                                size={switchMerchant ? 10 : 20}
                                color={Colors.whiteColor}
                              />
                              <Text
                                style={{
                                  color: Colors.whiteColor,
                                  marginLeft: 5,
                                  fontSize: switchMerchant ? 10 : 16,
                                  fontFamily: 'NunitoSans-Bold',
                                }}>
                                DOWNLOAD
                              </Text>
                            </View>
                          </TouchableOpacity>
                        </View>
                      </View>

                      <View style={{flexDirection: 'row'}}>
                        <TouchableOpacity
                          style={{
                            justifyContent: 'center',
                            flexDirection: 'row',
                            borderWidth: 1,
                            borderColor: Colors.primaryColor,
                            backgroundColor: '#0A1F44',
                            borderRadius: 5,
                            paddingHorizontal: switchMerchant ? 5 : 10,
                            width: switchMerchant ? 200 : 250,
                            height: switchMerchant ? 35 : 40,
                            alignItems: 'center',
                            shadowOffset: {
                              width: 0,
                              height: 2,
                            },
                            shadowOpacity: 0.22,
                            shadowRadius: 3.22,
                            elevation: 1,
                            zIndex: -1,
                            marginBottom: 15,
                          }}
                          onPress={() => {
                            CommonStore.update(s => {
                              s.selectedRackStockTakeEdit = null;
                            });
                            setStockTake(false);
                            setAddStockTake(true);
                            setAddStock(true);
                            console.log(currOutletId);
                          }}>
                          <View
                            style={{
                              flexDirection: 'row',
                              alignItems: 'center',
                            }}>
                            <AntDesign
                              name="pluscircle"
                              size={switchMerchant ? 10 : 20}
                              color={Colors.whiteColor}
                            />
                            <Text
                              style={{
                                color: Colors.whiteColor,
                                marginLeft: 5,
                                fontSize: switchMerchant ? 10 : 16,
                                fontFamily: 'NunitoSans-Bold',
                              }}>
                              STOCK ADJUSTMENT
                            </Text>
                          </View>
                        </TouchableOpacity>
                      </View>

                      <View style={{flexDirection: 'row'}}>
                        <View style={[{height: switchMerchant ? 35 : 40}]}>
                          <View
                            style={{
                              width: switchMerchant ? 200 : 250,
                              height: switchMerchant ? 35 : 40,
                              backgroundColor: 'white',
                              borderRadius: 5,
                              // marginLeft: '53%',
                              flexDirection: 'row',
                              alignContent: 'center',
                              alignItems: 'center',
                              alignSelf: 'flex-end',
                              shadowColor: '#000',
                              shadowOffset: {
                                width: 0,
                                height: 2,
                              },
                              shadowOpacity: 0.22,
                              shadowRadius: 3.22,
                              elevation: 3,
                              borderWidth: 1,
                              borderColor: '#E5E5E5',
                            }}>
                            <Icon
                              name="search"
                              size={switchMerchant ? 13 : 18}
                              color={Colors.primaryColor}
                              style={{marginLeft: 15}}
                            />
                            <TextInput
                              editable={!loading}
                              // underlineColorAndroid={Colors.primaryColor}
                              style={{
                                width: switchMerchant ? 180 : 220,
                                fontSize: 15,
                                fontFamily: 'NunitoSans-Regular',
                                padding: 0,
                                paddingLeft: 5,
                                fontSize: switchMerchant ? 10 : 16,
                                height: 35,
                              }}
                              clearButtonMode="while-editing"
                              placeholder=" Search"
                              placeholderTextColor={Platform.select({
                                ios: '#a9a9a9',
                              })}
                              onChangeText={text => {
                                setSearch(text);
                              }}
                              value={search}
                            />
                          </View>
                        </View>
                      </View>

                      <View
                        style={{
                          flexDirection: 'row',
                          width: windowWidth,
                          alignSelf: 'center',
                          marginLeft: 42,
                        }}>
                        <View
                          style={[
                            {
                              //marginRight: Platform.OS === 'ios' ? 0 : 10,
                              // paddingLeft: 15,
                              paddingHorizontal: 15,
                              flexDirection: 'row',
                              alignItems: 'center',
                              borderRadius: 5,
                              paddingVertical: 10,
                              justifyContent: 'center',
                              backgroundColor: Colors.whiteColor,
                              shadowOpacity: 0,
                              shadowColor: '#000',
                              shadowOffset: {
                                width: 0,
                                height: 2,
                              },
                              shadowOpacity: 0.22,
                              shadowRadius: 3.22,
                              elevation: 1,
                              marginTop: 10,
                            },
                          ]}>
                          <View
                            style={{alignSelf: 'center', marginRight: 5}}
                            onPress={() => {
                              setState({
                                pickerMode: 'date',
                                showDateTimePicker: true,
                              });
                            }}>
                            <GCalendar
                              width={switchMerchant ? 15 : 20}
                              height={switchMerchant ? 15 : 20}
                            />
                          </View>

                          <TouchableOpacity
                            onPress={() => setDatePickerVisibility(true)}>
                            <DatePicker
                              isVisible={isDatePickerVisible}
                              mode="date"
                              onConfirm={data => {
                                setRev_date(moment(data));
                                setDatePickerVisibility(false);
                              }}
                              onCancel={() => setDatePickerVisibility(false)}
                              date={rev_date.toDate()}
                              maximumDate={new Date()} // This is an example, you can replace with your rev_date1
                            />
                            <Text
                              style={
                                switchMerchant
                                  ? {
                                      fontSize: 10,
                                      fontFamily: 'NunitoSans-Regular',
                                      marginHorizontal: 8,
                                    }
                                  : {
                                      fontFamily: 'NunitoSans-Regular',
                                      marginHorizontal: 8,
                                    }
                              }>
                              {moment(rev_date)
                                .format('DD/MM/YYYY')
                                ?.toString()}
                            </Text>
                          </TouchableOpacity>
                          <Text
                            style={
                              switchMerchant
                                ? {
                                    fontSize: 10,
                                    fontFamily: 'NunitoSans-Regular',
                                    marginHorizontal: 4,
                                  }
                                : {
                                    fontFamily: 'NunitoSans-Regular',
                                    marginHorizontal: 4,
                                  }
                            }>
                            -
                          </Text>
                          <TouchableOpacity
                            onPress={() => setDatePickerVisibility1(true)}>
                            <DatePicker
                              isVisible={isDatePickerVisible1}
                              mode="date"
                              onConfirm={data => {
                                setRev_date1(moment(data));
                                setDatePickerVisibility1(false);
                              }}
                              onCancel={() => setDatePickerVisibility1(false)}
                              date={rev_date1.toDate()}
                              maximumDate={new Date()} // This is an example, you can replace with your rev_date1
                            />
                            <Text
                              style={
                                switchMerchant
                                  ? {
                                      fontSize: 10,
                                      fontFamily: 'NunitoSans-Regular',
                                      marginHorizontal: 8,
                                    }
                                  : {
                                      fontFamily: 'NunitoSans-Regular',
                                      marginHorizontal: 8,
                                    }
                              }>
                              {moment(rev_date1)
                                .format('DD/MM/YYYY')
                                ?.toString()}
                            </Text>
                          </TouchableOpacity>
                        </View>
                      </View>
                    </View>
                  </View>

                  <View
                    style={{
                      backgroundColor: Colors.whiteColor,
                      width: windowWidth * 0.9,
                      height: windowHeight * 0.72,
                      marginTop: 10,
                      marginLeft: 15,
                      marginBottom: 30,
                      alignSelf: 'center',
                      borderRadius: 5,
                      shadowOpacity: 0,
                      shadowColor: '#000',
                      shadowOffset: {
                        width: 0,
                        height: 2,
                      },
                      shadowOpacity: 0.22,
                      shadowRadius: 3.22,
                      elevation: 3,
                      zIndex: -1,
                    }}>
                    <View
                      style={{
                        borderTopLeftRadius: 10,
                        borderTopRightRadius: 10,
                        backgroundColor: '#ffffff',
                        flexDirection: 'row',
                        paddingVertical: 20,
                        paddingHorizontal: 15,
                        //marginTop: 10,
                        borderBottomWidth: StyleSheet.hairlineWidth,
                      }}>
                      <Text
                        style={{
                          fontSize: switchMerchant ? 10 : 14,
                          width: '15%',
                          alignSelf: 'center',
                          fontFamily: 'NunitoSans-Bold',
                          marginRight: 2,
                        }}>
                        ID
                      </Text>
                      <Text
                        style={{
                          fontSize: switchMerchant ? 10 : 14,
                          width: '30%',
                          alignSelf: 'center',
                          fontFamily: 'NunitoSans-Bold',
                          marginRight: 2,
                        }}>
                        {'Created Date/\nCompleted Date'}
                      </Text>
                      {/* <Text
                        style={{
                          fontSize: switchMerchant ? 10 : 14,
                          width: "12%",
                          alignSelf: "center",
                          fontFamily: "NunitoSans-Bold",
                          marginHorizontal: 2,
                        }}
                      >
                        Completed Date
                      </Text> */}
                      {/* <Text
                            style={{
                              fontSize: switchMerchant ? 10 : 14,
                              width: "12%",
                              alignSelf: "center",
                              fontFamily: "NunitoSans-Bold",
                              marginHorizontal: 2,
                            }}
                          >
                            Staff
                          </Text> */}
                      <Text
                        style={{
                          fontSize: switchMerchant ? 10 : 14,
                          width: '28.5%',
                          alignSelf: 'center',
                          fontFamily: 'NunitoSans-Bold',
                          marginHorizontal: 2,
                        }}>
                        Store
                      </Text>
                      {/* <Text
                            style={{
                              fontSize: switchMerchant ? 10 : 14,
                              width: "17%",
                              alignSelf: "center",
                              fontFamily: "NunitoSans-Bold",
                              marginHorizontal: 2,
                            }}
                          >
                            Supplier
                          </Text> */}
                      <Text
                        style={{
                          fontSize: switchMerchant ? 10 : 14,
                          width: '25%',
                          alignSelf: 'center',
                          fontFamily: 'NunitoSans-Bold',
                          marginLeft: 2,
                        }}>
                        Status
                      </Text>
                    </View>
                    <FlatList
                      nestedScrollEnabled={true}
                      showsVerticalScrollIndicator={false}
                      data={stockTakeList.filter(item => {
                        if (search !== '') {
                          const searchLowerCase = search.toLowerCase();

                          return (
                            item.outletName
                              .toLowerCase()
                              .includes(searchLowerCase) ||
                            item.supplierName
                              .toLowerCase()
                              .includes(searchLowerCase)
                          );
                        } else {
                          return true;
                        }
                      })}
                      extraData={stockTakeList.filter(item => {
                        if (search !== '') {
                          const searchLowerCase = search.toLowerCase();

                          return (
                            item.outletName
                              .toLowerCase()
                              .includes(searchLowerCase) ||
                            item.supplierName
                              .toLowerCase()
                              .includes(searchLowerCase)
                          );
                        } else {
                          return true;
                        }
                      })}
                      renderItem={renderStockTakeItem}
                      keyExtractor={(item, index) => String(index)}
                      contentContainerStyle={{
                        paddingRight: 5,
                        paddingBottom: switchMerchant ? 0 : 80,
                      }}
                    />
                  </View>
                </View>
              ) : null}

              {addStockTake ? (
                <View
                  style={{
                    backgroundColor: Colors.whiteColor,
                    width: windowWidth * 0.95,
                    height: windowHeight,
                    marginTop: switchMerchant ? 5 : 5,
                    alignSelf: 'center',
                    shadowOpacity: 0,
                    shadowColor: '#000',
                    shadowOffset: {
                      width: 0,
                      height: 2,
                    },
                    shadowOpacity: 0.22,
                    shadowRadius: 3.22,
                    elevation: 3,
                    borderRadius: 5,
                  }}>
                  <ScrollView
                    showsVerticalScrollIndicator={false}
                    style={{
                      paddingTop: 30,
                      padding: 10,
                    }}
                    contentContainerStyle={{}}>
                    <View
                      style={{
                        borderBottomWidth: StyleSheet.hairlineWidth,
                        padding: 10,
                        paddingTop: 20,
                      }}>
                      <View style={{}}>
                        <View
                          style={{
                            flexDirection: 'row',
                            justifyContent: 'center',
                            marginBottom: 20,
                          }}>
                          <View style={{flexDirection: 'column'}}>
                            <Text
                              style={{
                                marginTop: 0,
                                fontSize: switchMerchant ? 20 : 26,
                                fontWeight: 'bold',
                                textAlign: 'center',
                              }}>
                              Stock Adjustment
                            </Text>
                            <Text
                              style={{
                                fontSize: switchMerchant ? 10 : 16,
                                color: '#adadad',
                                textAlign: 'center',
                              }}>
                              Fill in the stock adjustment information
                            </Text>
                          </View>
                        </View>

                        {selectedRackStockTakeEdit === null ||
                        selectedRackStockTakeEdit.status !==
                          STOCK_TAKE_STATUS.COMPLETED ? (
                          <View style={{flexDirection: 'column'}}>
                            <View style={{}}>
                              <TouchableOpacity
                                style={{
                                  marginBottom: 10,
                                  marginTop: 20,
                                  justifyContent: 'center',
                                  flexDirection: 'row',
                                  borderWidth: 1,
                                  borderColor: Colors.primaryColor,
                                  backgroundColor: '#0A1F44',
                                  borderRadius: 5,
                                  width: switchMerchant ? 120 : 170,
                                  paddingHorizontal: 10,
                                  height: switchMerchant ? 35 : 40,
                                  alignItems: 'center',
                                  shadowOffset: {
                                    width: 0,
                                    height: 2,
                                  },
                                  shadowOpacity: 0.22,
                                  shadowRadius: 3.22,
                                  elevation: 1,
                                  zIndex: -1,
                                  marginRight: 10,
                                }}
                                onPress={() => {
                                  if (!isLoading && !isLoadingLocal) {
                                    CommonStore.update(s => {
                                      s.isLoading = true;
                                    });

                                    isLoadingLocal = true;

                                    createStockTake();
                                  }
                                }}>
                                <Text
                                  style={{
                                    color: Colors.whiteColor,
                                    //marginLeft: 5,
                                    fontSize: switchMerchant ? 10 : 16,
                                    fontFamily: 'NunitoSans-Bold',
                                  }}>
                                  SAVE
                                </Text>
                              </TouchableOpacity>
                            </View>
                            {!addStock && selectedRackStockTakeEdit ? (
                              <View style={{}}>
                                <TouchableOpacity
                                  style={{
                                    justifyContent: 'center',
                                    flexDirection: 'row',
                                    borderWidth: 1,
                                    borderColor: Colors.primaryColor,
                                    backgroundColor: '#0A1F44',
                                    borderRadius: 5,
                                    width: switchMerchant ? 120 : 170,
                                    paddingHorizontal: 10,
                                    height: switchMerchant ? 35 : 40,
                                    alignItems: 'center',
                                    shadowOffset: {
                                      width: 0,
                                      height: 2,
                                    },
                                    shadowOpacity: 0.22,
                                    shadowRadius: 3.22,
                                    elevation: 1,
                                    zIndex: -1,
                                    marginBottom: 10,
                                  }}
                                  onPress={() => {
                                    if (!isLoading && !isLoadingLocal) {
                                      CommonStore.update(s => {
                                        s.isLoading = true;
                                      });

                                      isLoadingLocal = true;

                                      createStockTake(
                                        STOCK_TAKE_STATUS.COMPLETED,
                                      );
                                    }
                                  }}>
                                  <Text
                                    style={{
                                      color: Colors.whiteColor,
                                      //marginLeft: 5,
                                      fontSize: switchMerchant ? 10 : 16,
                                      fontFamily: 'NunitoSans-Bold',
                                    }}>
                                    {!isLoading ? 'COMPLETE' : 'LOADING'}
                                  </Text>
                                </TouchableOpacity>
                              </View>
                            ) : null}
                            <View style={{}}>
                              <TouchableOpacity
                                style={{
                                  justifyContent: 'center',
                                  flexDirection: 'row',
                                  borderWidth: 1,
                                  borderColor: Colors.primaryColor,
                                  backgroundColor: '#0A1F44',
                                  borderRadius: 5,
                                  width: switchMerchant ? 120 : 170,
                                  paddingHorizontal: 10,
                                  height: switchMerchant ? 35 : 40,
                                  alignItems: 'center',
                                  shadowOffset: {
                                    width: 0,
                                    height: 2,
                                  },
                                  shadowOpacity: 0.22,
                                  shadowRadius: 3.22,
                                  elevation: 1,
                                  zIndex: -1,
                                  marginRight: 10,
                                  marginBottom: 10,
                                }}
                                onPress={() => {
                                  handleIconPressRack();
                                  setShowScannerProduct(false);
                                  global.cameraScanState = 'reader';
                                }}>
                                <MaterialCommunityIcons
                                  name={
                                    showScannerRack ? 'close' : 'qrcode-scan'
                                  }
                                  size={18}
                                  style={{color: Colors.whiteColor}}
                                />
                                <Text
                                  style={{
                                    color: Colors.whiteColor,
                                    marginLeft: 7,
                                    fontSize: switchMerchant ? 10 : 16,
                                    fontFamily: 'NunitoSans-Bold',
                                  }}>
                                  RACK
                                </Text>
                              </TouchableOpacity>
                              {/** <View
                                style={{
                                  display: showScannerRack ? 'flex' : 'none',
                                }}
                                ref={qrReaderCallback}
                                id="reader"
                                nativeID="reader"
                                width="50px">
                                
                              </View>*/}
                              {showScannerRack ? (
                                <Camera
                                  ref={qrReaderCallback}
                                  onReadCode={() => setShowScannerRack(false)}
                                  cameraType={CameraType.Back}
                                  scanBarcode={true}
                                  flashMode="auto"
                                />
                              ) : (
                                <></>
                              )}
                            </View>
                            <View style={{}}>
                              <TouchableOpacity
                                style={{
                                  justifyContent: 'center',
                                  flexDirection: 'row',
                                  borderWidth: 1,
                                  borderColor: Colors.primaryColor,
                                  backgroundColor: '#0A1F44',
                                  borderRadius: 5,
                                  width: switchMerchant ? 120 : 170,
                                  paddingHorizontal: 10,
                                  height: switchMerchant ? 35 : 40,
                                  alignItems: 'center',
                                  shadowOffset: {
                                    width: 0,
                                    height: 2,
                                  },
                                  shadowOpacity: 0.22,
                                  shadowRadius: 3.22,
                                  elevation: 1,
                                  zIndex: -1,
                                  marginRight: 10,
                                }}
                                onPress={() => {
                                  handleIconPressProduct();
                                  setShowScannerRack(false);
                                  global.cameraScanState = 'readerProduct';
                                }}>
                                <MaterialCommunityIcons
                                  name={
                                    showScannerProduct ? 'close' : 'qrcode-scan'
                                  }
                                  size={18}
                                  style={{color: Colors.whiteColor}}
                                />

                                <Text
                                  style={{
                                    color: Colors.whiteColor,
                                    marginLeft: 7,
                                    fontSize: switchMerchant ? 10 : 16,
                                    fontFamily: 'NunitoSans-Bold',
                                  }}>
                                  PRODUCT
                                </Text>
                              </TouchableOpacity>
                              {/*    <View
                                style={{
                                  display: showScannerProduct ? 'flex' : 'none',
                                }}
                                ref={qrReaderCallback}
                                id="readerProduct"
                                nativeID="readerProduct"
                                width="600px"></View>
                            */}
                            </View>
                            {showScannerProduct ? (
                              <Camera
                                ref={qrReaderCallback}
                                scanBarcode={true}
                                onReadCode={() => setShowScannerRack(false)}
                                cameraType={CameraType.Back}
                                flashMode="auto"
                              />
                            ) : (
                              <></>
                            )}
                            {/*    {(!addStock && selectedRackStockTakeEdit)? (
                                  <View style={{}}>
                                    <TouchableOpacity
                                      style={{
                                        justifyContent: "center",
                                        flexDirection: "row",
                                        borderWidth: 1,
                                        borderColor: Colors.tabRed,
                                        backgroundColor: Colors.tabRed,
                                        borderRadius: 5,
                                        width: switchMerchant ? 120 : 170,
                                        paddingHorizontal: 5,
                                        height: switchMerchant ? 35 : 40,
                                        alignItems: "center",
                                        shadowOffset: {
                                          width: 0,
                                          height: 2,
                                        },
                                        shadowOpacity: 0.22,
                                        shadowRadius: 3.22,
                                        elevation: 1,
                                        zIndex: -1,
                                      }}
                                      onPress={() => {
                                        var body = {
                                          stId: selectedRackStockTakeEdit.uniqueId,
                                        };

                                        ApiClient.POST(
                                          API.deleteStockTakeRack,
                                          body
                                        ).then((result) => {
                                          console.log("result", result);

                                          if (
                                            result &&
                                            result.status === "success"
                                          ) {
                                            if (
                                              window.confirm(
                                                "Success. The stock adjustment has been canceled"
                                              ) == true
                                            ) {
                                              CommonStore.update((s) => {
                                                s.selectedRackStockTakeEdit = null;
                                              });

                                              setStockTake(true);
                                              setAddStockTake(false);
                                              setAddStock(false);
                                            } else {
                                              console.log("You canceled!");
                                            }
                                          } else {
                                            if (
                                              window.confirm(
                                                "Error. Failed to cancel Stock adjustment"
                                              ) == true
                                            ) {
                                              // console.log("You pressed ok!");
                                            } else {
                                              // console.log("You canceled!");
                                            }
                                          }
                                        });
                                      }}
                                    >
                                      <Text
                                        style={{
                                          color: Colors.whiteColor,
                                          //marginLeft: 5,
                                          fontSize: switchMerchant ? 10 : 16,
                                          fontFamily: "NunitoSans-Bold",
                                        }}
                                      >
                                        CANCEL
                                      </Text>
                                    </TouchableOpacity>
                                  </View>
                                ) : (
                                  <></>
                                )} */}
                          </View>
                        ) : (
                          <></>
                        )}
                      </View>

                      <View
                        style={{
                          // flexDirection: "row",
                          marginTop: 25,
                          // justifyContent: "space-between",
                          width: '96%',
                          alignSelf: 'center',
                        }}>
                        <View
                          style={{
                            flexDirection: 'row',
                            // width: switchMerchant ? "53%" : "51%",
                            alignItems: 'center',
                          }}>
                          <Text
                            style={{
                              fontFamily: 'NunitoSans-Bold',
                              fontSize: switchMerchant ? 10 : 14,
                              width: windowWidth * 0.2,
                            }}>
                            ID
                          </Text>
                          <View
                            style={{
                              // width: "50%",
                              justifyContent: 'space-between',
                              flexDirection: 'row',
                              alignItems: 'center',
                            }}>
                            {editMode ? (
                              <TextInput
                                editable={true}
                                style={{
                                  backgroundColor: Colors.fieldtBgColor,
                                  width: switchMerchant ? '85%' : 200,
                                  height: 35,
                                  borderRadius: 5,
                                  padding: 5,
                                  //marginVertical: 5,
                                  borderWidth: 1,
                                  borderColor: '#E5E5E5',
                                  paddingLeft: 10,
                                  fontFamily: 'NunitoSans-Regular',
                                  fontSize: switchMerchant ? 10 : 14,
                                }}
                                placeholder={'Max 12 Character'}
                                placeholderStyle={{
                                  fontFamily: 'NunitoSans-Regular',
                                  fontSize: switchMerchant ? 10 : 14,
                                }}
                                placeholderTextColor={Platform.select({
                                  ios: '#a9a9a9',
                                })}
                                maxLength={12}
                                onChangeText={text => {
                                  setPoId(text);
                                }}
                              />
                            ) : (
                              <View
                                style={{
                                  borderRadius: 5,
                                  borderWidth: 1,
                                  borderColor: '#E5E5E5',
                                  height: 35,
                                  width: switchMerchant ? '85%' : 200,
                                  justifyContent: 'center',
                                  paddingHorizontal: 10,
                                  backgroundColor: Colors.fieldtBgColor,
                                }}>
                                <Text
                                  style={{
                                    fontFamily: 'NunitoSans-Regular',
                                    fontSize: switchMerchant ? 10 : 14,
                                  }}>
                                  {`${poId}`}
                                </Text>
                              </View>
                            )}
                            {!selectedRackStockTakeEdit ? (
                              <View style={{marginLeft: 10}}>
                                <TouchableOpacity
                                  onPress={() => {
                                    setEditMode(!editMode);
                                  }}>
                                  {/* <MaterialCommunityIcons name="pencil" size={20} color={Colors.primaryColor} /> */}
                                  <Icon
                                    name="edit"
                                    size={switchMerchant ? 15 : 20}
                                    color={Colors.primaryColor}
                                  />
                                </TouchableOpacity>
                              </View>
                            ) : (
                              <></>
                            )}
                          </View>
                        </View>

                        <View
                          style={{
                            flexDirection: 'row',
                            // width: "47%",
                            alignItems: 'center',
                            marginTop: 10,
                          }}>
                          <Text
                            style={{
                              fontFamily: 'NunitoSans-Bold',
                              fontSize: switchMerchant ? 10 : 14,
                              width: windowWidth * 0.2,
                            }}>
                            Target Rack
                          </Text>

                          <View
                            style={{
                              width: switchMerchant ? '60%' : 220,
                              height: 30,
                              backgroundColor: Colors.whiteColor,
                              borderRadius: 10,
                              justifyContent: 'center',
                              alignSelf: 'center',
                              zIndex: 5,
                            }}>
                            {/* {(supplierDropdownList.find(
                                  (item) => item.value === selectedSupplierId
                                ) ||
                                  selectedSupplierId === "ALL") && ( */}
                            <View style={{width: 220}}>
                              <TextInput
                                editable={false}
                                value={rackName}
                                style={{
                                  backgroundColor: Colors.fieldtBgColor,
                                  width: 200,
                                  height: 35,
                                  borderRadius: 5,
                                  borderWidth: 1,
                                  borderColor: '#E5E5E5',
                                  flexDirection: 'row',
                                  padding: 5,
                                  paddingLeft: 10,
                                  fontFamily: 'NunitoSans-Regular',
                                  fontSize: switchMerchant ? 10 : 14,
                                }}
                              />
                            </View>
                            {/* )} */}
                          </View>
                        </View>
                      </View>

                      <View
                        style={{
                          // flexDirection: "row",
                          marginTop: 10,
                          // justifyContent: "space-between",
                          width: '96%',
                          alignSelf: 'center',
                          zIndex: -1,
                        }}>
                        <View
                          style={{
                            flexDirection: 'row',
                            // width: switchMerchant ? "53%" : "51%",
                            alignItems: 'center',
                          }}>
                          <Text
                            style={{
                              fontFamily: 'NunitoSans-Bold',
                              fontSize: switchMerchant ? 10 : 14,
                              width: windowWidth * 0.2,
                            }}>
                            Created Date
                          </Text>
                          <View
                            style={{
                              flexDirection: 'row',
                              // width: switchMerchant ? "50%" : "71%",
                              marginTop: 0,
                            }}>
                            <View
                              style={{
                                flexDirection: 'row',
                                backgroundColor: Colors.fieldtBgColor,
                                width: switchMerchant ? '85%' : 200,
                                height: 35,
                                borderRadius: 5,
                                padding: 5,
                                //marginVertical: 5,
                                borderWidth: 1,
                                borderColor: '#E5E5E5',
                                paddingLeft: 10,
                                alignItems: 'center',
                                //justifyContent: 'space-between'
                              }}>
                              <GCalendarGrey
                                height={switchMerchant ? 15 : 20}
                                width={switchMerchant ? 15 : 20}
                              />
                              <Text
                                style={{
                                  fontFamily: 'NunitoSans-Regular',
                                  fontSize: switchMerchant ? 10 : 14,
                                  marginLeft: 15,
                                }}>
                                {moment(date).format('DD MMM YYYY')}
                                {/* {moment(startDate).format('HH:mm')} */}
                              </Text>
                              {/* <Text style={{ color: 'black', marginLeft: 10, fontSize: 16 }}>{moment(startDate).format('HH:mm')}</Text> */}
                            </View>
                          </View>
                        </View>
                        <View
                          style={{
                            flexDirection: 'row',
                            // width: "47%",
                            alignItems: 'center',
                            marginTop: 10,
                          }}>
                          <Text
                            style={{
                              fontFamily: 'NunitoSans-Bold',
                              fontSize: switchMerchant ? 10 : 14,
                              width: windowWidth * 0.2,
                            }}>
                            Completed Date
                          </Text>
                          <View style={{flexDirection: 'row'}}>
                            <Text
                              style={{
                                fontFamily: 'NunitoSans-Regular',
                                fontSize: switchMerchant ? 10 : 14,
                              }}>
                              {selectedRackStockTakeEdit === null ||
                              selectedRackStockTakeEdit.status !==
                                STOCK_TAKE_STATUS.COMPLETED
                                ? 'Ongoing'
                                : moment(completedDate).format('DD MMM YYYY')}
                            </Text>
                          </View>
                        </View>
                      </View>

                      <View
                        style={{
                          // flexDirection: "row",
                          // justifyContent: "space-between",
                          marginTop: 10,
                          marginBottom: 20,
                          zIndex: -1,
                          width: '96%',
                          alignSelf: 'center',
                          alignItem: 'center',
                        }}>
                        <View
                          style={{
                            // width: switchMerchant ? "53%" : "51%",
                            justifyContent: 'center',
                          }}>
                          <View style={{flexDirection: 'row'}}>
                            <View
                              style={{
                                flexDirection: 'row',
                                alignItems: 'center',
                              }}>
                              <Text
                                style={{
                                  fontFamily: 'NunitoSans-Bold',
                                  fontSize: switchMerchant ? 10 : 14,
                                  width: windowWidth * 0.2,
                                }}>
                                Total Cost Difference:
                              </Text>
                              <Text
                                style={{
                                  color:
                                    diffQuantityTotal < 0
                                      ? '#f71616'
                                      : diffQuantityTotal > 0
                                      ? Colors.primaryColor
                                      : Colors.blackColor,
                                  fontFamily: 'NunitoSans-Regular',
                                  fontSize: switchMerchant ? 10 : 14,
                                  // width: "38%",
                                }}>
                                RM
                                {diffCostTotal
                                  .toFixed(2)
                                  .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}
                              </Text>
                            </View>
                          </View>
                        </View>
                        <View
                          style={{
                            flexDirection: 'row',
                            // width: "47%",
                            alignItems: 'center',
                            marginTop: 10,
                          }}>
                          <Text
                            style={{
                              fontFamily: 'NunitoSans-Bold',
                              fontSize: switchMerchant ? 10 : 14,
                              width: windowWidth * 0.2,
                            }}>
                            Current Status
                          </Text>
                          <View
                            style={{
                              width: switchMerchant ? '60%' : 200,
                              alignItems: 'center',
                              backgroundColor: Colors.secondaryColor,
                              borderRadius: 10,
                              paddingVertical: 10,
                            }}>
                            <Text
                              style={{
                                fontFamily: 'NunitoSans-Regular',
                                fontSize: switchMerchant ? 10 : 14,
                              }}>
                              {selectedRackStockTakeEdit === null ||
                              selectedRackStockTakeEdit.status !==
                                STOCK_TAKE_STATUS.COMPLETED
                                ? 'Created'
                                : 'Completed'}
                            </Text>
                          </View>
                        </View>

                        <View
                          style={{
                            backgroundColor: '#ffffff',
                            flexDirection: 'row',
                            paddingVertical: 20,
                            paddingHorizontal: 7,
                            marginTop: 10,
                            borderBottomWidth: StyleSheet.hairlineWidth,
                            //backgroundColor: 'red'
                          }}>
                          <Text
                            style={{
                              fontFamily: 'NunitoSans-Bold',
                              fontSize: switchMerchant ? 10 : 14,
                              width: '34%',
                              alignSelf: 'center',
                              marginRight: 5,
                            }}>
                            Product Name
                          </Text>
                          <Text
                            style={{
                              fontFamily: 'NunitoSans-Bold',
                              fontSize: switchMerchant ? 10 : 14,
                              width: '18%',
                              alignSelf: 'center',
                            }}>
                            SKU
                          </Text>
                          {/* <Text
                            style={{
                              fontFamily: "NunitoSans-Bold",
                              fontSize: switchMerchant ? 10 : 14,
                              width: "11%",
                              alignSelf: "center",
                              marginHorizontal: 2,
                            }}
                          >
                            Cost
                          </Text> */}
                          {/* <Text
                            style={{
                              fontFamily: "NunitoSans-Bold",
                              fontSize: switchMerchant ? 10 : 14,
                              width: "12%",
                              alignSelf: "center",
                              marginHorizontal: 2,
                            }}
                          >
                            Unit
                          </Text> */}
                          <Text
                            style={{
                              fontFamily: 'NunitoSans-Bold',
                              fontSize: switchMerchant ? 10 : 14,
                              width: '17%',
                              alignSelf: 'center',
                              marginHorizontal: 2,
                            }}>
                            In Stock
                          </Text>
                          <Text
                            style={{
                              fontFamily: 'NunitoSans-Bold',
                              fontSize: switchMerchant ? 10 : 14,
                              width: '20%',
                              alignSelf: 'center',
                              marginHorizontal: 2,
                            }}>
                            Counted
                          </Text>
                          {/* <Text
                            style={{
                              fontFamily: "NunitoSans-Bold",
                              fontSize: switchMerchant ? 10 : 14,
                              width: "13%",
                              alignSelf: "center",
                              marginHorizontal: 2,
                            }}
                          >
                            {`Balance\n(Qty)`}
                          </Text>
                          <Text
                            style={{
                              fontFamily: "NunitoSans-Bold",
                              fontSize: switchMerchant ? 10 : 14,
                              width: "14%",
                              alignSelf: "center",
                              marginLeft: 2,
                            }}
                          >
                            {/* Balance(RM) *
                            {`Cost Difference\n(RM)`}
                          </Text> */}
                          {/* <Text style={{ width: '12%', alignSelf: 'center', marginLeft: 2 }}>
                    Remarks
                  </Text> */}
                          {/* <Text style={{ width: '12%', alignSelf: 'center' }}>Action</Text> */}
                        </View>

                        {poItems.length > 0 ? (
                          <FlatList
                            nestedScrollEnabled={true}
                            showsVerticalScrollIndicator={false}
                            data={poItems}
                            extraData={poItems}
                            renderItem={renderAddCountedStockTake}
                            keyExtractor={(item, index) => String(index)}
                          />
                        ) : (
                          <View
                            style={{
                              alignItems: 'center',
                              justifyContent: 'center',
                              height: '17.5%',
                            }}>
                            <Text
                              style={{
                                color: Colors.descriptionColor,
                                fontFamily: 'NunitoSans-Regular',
                                fontSize: switchMerchant ? 10 : 14,
                              }}>
                              N/A
                            </Text>
                          </View>
                        )}

                        {poItems.length > 0 ? (
                          <View
                            style={{
                              marginTop: 30,
                              width: '99.6%',
                              marginLeft: '0.2%',
                            }}>
                            <View>
                              <Text
                                style={{
                                  fontFamily: 'NunitoSans-Bold',
                                  fontSize: switchMerchant ? 10 : 14,
                                }}>
                                Stock Adjustment Summary
                              </Text>
                            </View>

                            <View style={{marginTop: 10}}>
                              <TextInput
                                underlineColorAndroid={Colors.fieldtBgColor}
                                style={{
                                  padding: 5,
                                  backgroundColor: Colors.fieldtBgColor,
                                  height: 120,
                                  borderRadius: 5,
                                  borderWidth: 1,
                                  borderColor: '#E5E5E5',
                                  marginVertical: 5,
                                  paddingTop: Platform.OS == 'ios' ? 10 : 10,
                                  paddingLeft: 10,
                                  textAlignVertical: 'top',
                                  fontFamily: 'NunitoSans-Regular',
                                  fontSize: switchMerchant ? 10 : 14,
                                }}
                                // placeholder="Counted Items description here"
                                placeholder="Remarks"
                                placeholderStyle={{
                                  fontFamily: 'NunitoSans-Regular',
                                  fontSize: switchMerchant ? 10 : 14,
                                }}
                                placeholderTextColor={Platform.select({
                                  ios: '#a9a9a9',
                                })}
                                onChangeText={text => {
                                  // setState({ remark: text });
                                  setRemark(text);
                                }}
                                value={remark}
                                multiline={true}
                              />
                            </View>
                          </View>
                        ) : (
                          <View
                            style={{
                              alignItems: 'center',
                              justifyContent: 'center',
                              height: '17.5%',
                            }}>
                            <Text
                              style={{
                                color: Colors.descriptionColor,
                                fontFamily: 'NunitoSans-Regular',
                                fontSize: switchMerchant ? 10 : 14,
                              }}>
                              N/A
                            </Text>
                          </View>
                        )}

                        {!selectedRackStockTakeEdit ||
                        (selectedRackStockTakeEdit &&
                          selectedRackStockTakeEdit.status !==
                            STOCK_TAKE_STATUS.COMPLETED) ? (
                          <View
                            style={{
                              flexDirection: 'row',
                              alignSelf: 'center',
                              justifyContent: 'space-evenly',
                              marginTop: 20,
                            }}>
                            <Modal
                              supportedOrientations={['portrait', 'landscape']}
                              style={{flex: 1}}
                              visible={modal}
                              transparent={true}
                              animationType="slide">
                              <View
                                style={{
                                  backgroundColor: 'rgba(0,0,0,0.5)',
                                  flex: 1,
                                  justifyContent: 'center',
                                  alignItems: 'center',
                                  minHeight: Dimensions.get('window').height,
                                }}>
                                <View style={styles.confirmBox}>
                                  <TouchableOpacity
                                    onPress={() => {
                                      setState({modal: false});
                                    }}>
                                    <View
                                      style={{
                                        alignSelf: 'flex-end',
                                        padding: 16,
                                      }}>
                                      {/* <Close name="closecircle" size={25} /> */}
                                      <AntDesign
                                        name="closecircle"
                                        size={25}
                                        color={Colors.fieldtTxtColor}
                                      />
                                    </View>
                                  </TouchableOpacity>
                                  <View>
                                    <Text
                                      style={{
                                        textAlign: 'center',
                                        fontWeight: '700',
                                        fontSize: 28,
                                      }}>
                                      Uncounted Items
                                    </Text>
                                  </View>
                                  <View style={{marginTop: 20}}>
                                    <Text
                                      style={{
                                        textAlign: 'center',
                                        color: Colors.descriptionColor,
                                        fontSize: 25,
                                      }}>
                                      Select the action for uncounted items
                                    </Text>
                                  </View>
                                  <View
                                    style={{
                                      backgroundColor: 'white',
                                      marginLeft: 55,
                                      flexDirection: 'row',
                                      marginTop: 30,
                                    }}>
                                    <input
                                      defaultValue={isSelected3}
                                      onChange={isSelected3 =>
                                        setState({
                                          isSelected3,
                                          isSelected4: false,
                                          choose: 1,
                                        })
                                      }
                                      style={
                                        {
                                          // alignSelf: "center",
                                          // borderRadius: 15,
                                          // paddingBottom: 3,
                                        }
                                      }
                                      type={'checkbox'}
                                      //   checked={allOutlets.length === selectedOutletList.length}
                                    />
                                    {/* <CheckBox
                                  value={isSelected3}
                                  onValueChange={(isSelected3) =>
                                    setState({
                                      isSelected3,
                                      isSelected4: false,
                                      choose: 1,
                                    })
                                  }
                                /> */}
                                    <Text
                                      style={{
                                        marginLeft: 10,
                                        color: Colors.primaryColor,
                                        fontSize: 18,
                                      }}>
                                      Keep the expected quantity
                                    </Text>
                                  </View>
                                  <View
                                    style={{
                                      backgroundColor: 'white',
                                      marginLeft: 55,
                                      flexDirection: 'row',
                                      marginTop: 20,
                                    }}>
                                    <input
                                      defaultValue={isSelected4}
                                      onChange={isSelected4 =>
                                        setState({
                                          isSelected4,
                                          isSelected3: false,
                                          choose: 2,
                                        })
                                      }
                                      style={
                                        {
                                          // alignSelf: "center",
                                          // borderRadius: 15,
                                          // paddingBottom: 3,
                                        }
                                      }
                                      type={'checkbox'}
                                      //   checked={allOutlets.length === selectedOutletList.length}
                                    />
                                    {/* <CheckBox
                                  value={isSelected4}
                                  onValueChange={(isSelected4) =>
                                    setState({
                                      isSelected4,
                                      isSelected3: false,
                                      choose: 2,
                                    })
                                  }
                                /> */}
                                    <Text
                                      style={{marginLeft: 10, fontSize: 18}}>
                                      Reduce quantity to 0
                                    </Text>
                                  </View>
                                  <View
                                    style={{
                                      flexDirection: 'row',
                                      alignSelf: 'center',
                                      marginTop: 20,
                                      justifyContent: 'center',
                                      alignItems: 'center',
                                      width: '50%',
                                      alignContent: 'center',
                                      zIndex: 6000,
                                    }}></View>
                                  <View
                                    style={{
                                      alignSelf: 'center',
                                      marginTop: 20,
                                      justifyContent: 'center',
                                      alignItems: 'center',
                                      width: 250,
                                      height: 40,
                                      alignContent: 'center',
                                      flexDirection: 'row',
                                      marginTop: 40,
                                    }}>
                                    <TouchableOpacity
                                      onPress={() => {
                                        if (choose == 1) {
                                          CheckStockOrder();
                                        } else if (choose == 2) {
                                          CheckStockOrderDelete();
                                        }
                                        setState({modal: false});
                                      }}
                                      style={{
                                        backgroundColor: Colors.fieldtBgColor,
                                        width: '60%',
                                        justifyContent: 'center',
                                        alignItems: 'center',
                                        alignContent: 'center',
                                        borderRadius: 10,
                                        height: 60,
                                      }}>
                                      <Text
                                        style={{
                                          fontSize: 28,
                                          color: Colors.primaryColor,
                                        }}>
                                        Continue
                                      </Text>
                                    </TouchableOpacity>
                                    <TouchableOpacity
                                      onPress={() => {
                                        setState({modal: false});
                                      }}
                                      style={{
                                        backgroundColor: Colors.fieldtBgColor,
                                        width: '60%',
                                        justifyContent: 'center',
                                        alignItems: 'center',
                                        alignContent: 'center',
                                        borderRadius: 10,
                                        height: 60,
                                        marginLeft: 30,
                                      }}>
                                      <Text
                                        style={{
                                          fontSize: 28,
                                          color: '#cccccc',
                                        }}>
                                        Cancel
                                      </Text>
                                    </TouchableOpacity>
                                  </View>
                                </View>
                              </View>
                            </Modal>
                          </View>
                        ) : (
                          <></>
                        )}
                      </View>
                    </View>
                  </ScrollView>
                </View>
              ) : null}
            </View>

            {/* </ScrollView> */}
          </ScrollView>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.highlightColor,
    flexDirection: 'row',
  },
  headerLogo: {
    width: 112,
    height: 25,
    marginLeft: 10,
  },
  list: {
    paddingVertical: 20,
    paddingHorizontal: 30,
    flexDirection: 'row',
    alignItems: 'center',
  },
  listItem: {
    marginLeft: 20,
    color: Colors.descriptionColor,
    fontSize: 16,
  },
  sidebar: {
    width: Dimensions.get('screen').width * Styles.sideBarWidth,
    // shadowColor: "#000",
    // shadowOffset: {
    //   width: 0,
    //   height: 8,
    // },
    // shadowOpacity: 0.44,
    // shadowRadius: 10.32,

    // elevation: 16,
  },
  content: {
    width: '100%',
    alignItems: 'center',
  },
  submitText: {
    height:
      Platform.OS == 'ios'
        ? Dimensions.get('screen').height * 0.06
        : Dimensions.get('screen').height * 0.05,
    paddingVertical: 5,
    paddingHorizontal: 20,
    flexDirection: 'row',
    color: '#4cd964',
    textAlign: 'center',
    borderRadius: 10,
    borderWidth: 1,
    borderColor: Colors.primaryColor,
    justifyContent: 'center',
    alignContent: 'center',
    alignItems: 'center',
    marginRight: 10,
  },
  submitText1: {
    paddingVertical: 10,
    paddingHorizontal: 20,
    flexDirection: 'row',
    color: '#4cd964',
    textAlign: 'center',
    borderRadius: 10,
    borderWidth: 1,
    borderColor: Colors.primaryColor,
    alignSelf: 'flex-end',
    marginRight: 20,
    // marginTop: 15
    height: 40,
    width: 195,
  },
  submitText2: {
    paddingVertical: 10,
    paddingHorizontal: 20,
    flexDirection: 'row',
    color: '#4cd964',
    textAlign: 'center',
    alignSelf: 'flex-end',
    marginRight: 20,
    marginTop: 15,
  },
  /* textInput: {
          width: 300,
        height: '10%',
        padding: 20,
        backgroundColor: Colors.fieldtBgColor,
        borderRadius: 5,
        borderRadius: 5,
        paddingTop: 20,
  }, */

  textInput: {
    width: 200,
    height: 40,
    // padding:5,
    backgroundColor: Colors.whiteColor,
    borderRadius: 0,
    marginRight: '35%',
    flexDirection: 'row',
    alignContent: 'center',
    alignItems: 'flex-end',

    shadowOffset:
      Platform.OS == 'ios'
        ? {
            width: 0,
            height: 0,
          }
        : {
            width: 0,
            height: 7,
          },
    shadowOpacity: Platform.OS == 'ios' ? 0 : 0.43,
    shadowRadius: Platform.OS == 'ios' ? 0 : 0.51,
    elevation: 15,
  },
  searchIcon: {
    backgroundColor: Colors.whiteColor,
    height: 40,
    padding: 10,
    shadowOffset:
      Platform.OS == 'ios'
        ? {
            width: 0,
            height: 0,
          }
        : {
            width: 0,
            height: 7,
          },
    shadowOpacity: Platform.OS == 'ios' ? 0 : 0.43,
    shadowRadius: Platform.OS == 'ios' ? 0 : 9.51,

    elevation: 15,
  },
  textInput1: {
    width: 160,
    padding: 5,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 5,
    borderRadius: 5,
    paddingTop: 5,
  },
  textInput2: {
    width: 100,
    padding: 5,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 5,
    borderRadius: 5,
    paddingTop: 5,
    textAlign: 'center',
  },
  confirmBox: {
    width: 450,
    height: 450,
    borderRadius: 12,
    backgroundColor: Colors.whiteColor,
  },
  textFieldInput: {
    height: 120,
    //width: 850,
    paddingHorizontal: 10,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 5,
    borderRadius: 5,
  },
  RemarkInput: {
    height: 110,
    width: 300,
    paddingHorizontal: 10,
    marginVertical: 10,
    //backgroundColor: Colors.fieldtBgColor,
    borderRadius: 5,
  },
  circleIcon: {
    width: 30,
    height: 30,
    // resizeMode: 'contain',
    marginRight: 10,
    alignSelf: 'center',
  },
  headerLeftStyle: {
    width: Dimensions.get('screen').width * 0.17,
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    flex: 1,
    backgroundColor: Colors.modalBgColor,
    alignItems: 'center',
    justifyContent: 'center',
  },
  modalView: {
    height: Dimensions.get('screen').width * 0.25,
    width: Dimensions.get('screen').width * 0.35,
    backgroundColor: Colors.whiteColor,
    borderRadius: 12,
    padding: Dimensions.get('screen').width * 0.03,
    alignItems: 'center',
    justifyContent: 'center',
  },
  modalView1: {
    //height: Dimensions.get('screen').width * 0.2,
    width: Dimensions.get('screen').width * 0.3,
    backgroundColor: Colors.whiteColor,
    borderRadius: 12,
    padding: Dimensions.get('screen').width * 0.03,
    alignItems: 'center',
    justifyContent: 'center',
  },
  modalViewImport: {
    height: Dimensions.get('screen').width * 0.6,
    width: Dimensions.get('screen').width * 0.6,
    backgroundColor: Colors.whiteColor,
    borderRadius: Dimensions.get('screen').width * 0.03,
    padding: 20,
    paddingTop: 25,
    //paddingBottom: 30,
  },
  closeButton: {
    position: 'absolute',
    right: Dimensions.get('screen').width * 0.02,
    top: Dimensions.get('screen').width * 0.02,

    elevation: 1000,
    zIndex: 1000,
  },
  modalTitle: {
    alignItems: 'center',
    //top: '20%',
    //position: 'absolute',
  },
  modalBody: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  modalTitleText: {
    fontFamily: 'NunitoSans-Bold',
    textAlign: 'center',
    fontSize: 20,
  },
  modalDescText: {
    fontFamily: 'NunitoSans-SemiBold',
    fontSize: 18,
    color: Colors.fieldtTxtColor,
  },
  modalBodyText: {
    flex: 1,
    fontFamily: 'NunitoSans-SemiBold',
    fontSize: 25,
    width: '20%',
  },
  modalSaveButton: {
    width: Dimensions.get('screen').width * 0.15,
    backgroundColor: Colors.fieldtBgColor,
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 8,

    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 3.22,
    elevation: 1,

    marginVertical: 10,
  },
});
export default RackStockAdjustment;
