import { Text } from "react-native-fast-text";
import React, { Component, useReducer, useState, useEffect, useRef, useCallback, useMemo } from 'react';
import {
  StyleSheet,
  Image,
  View,
  Dimensions,
  Alert,
  TouchableOpacity,
  Button,
  Modal as ModalComponent,
  TextInput,
  Animated,
  Easing,
  Picker,
  ActivityIndicator,
  Platform,
  KeyboardAvoidingView,
  useWindowDimensions,
  InteractionManager,
} from 'react-native';
import { ModalView as ModalViewComponent } from 'react-native-multiple-modals';
import Colors from '../constant/Colors';
import SideBar from './SideBar';
import * as User from '../util/User';
import API from '../constant/API';
import ApiClient from '../util/ApiClient';
import 'react-native-get-random-values';
import { v4 as uuidv4 } from 'uuid';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import Feather from 'react-native-vector-icons/Feather';
import IIcon from 'react-native-vector-icons/AntDesign';
import FIcon from 'react-native-vector-icons/Fontisto';
import Entypo from 'react-native-vector-icons/Entypo';
import FontAwesome from 'react-native-vector-icons/FontAwesome';
import Ionicon from 'react-native-vector-icons/Ionicons';
import DropDownPicker from 'react-native-dropdown-picker';
import Styles from '../constant/Styles';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { FlingGestureHandler, ScrollView, FlatList } from 'react-native-gesture-handler';
import Loading from '../assets/svg/loading.svg';
import KitchenOrder from './components/KitchenOrder';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import AntDesign from 'react-native-vector-icons/AntDesign';
import {
  getTransformForModalInsideNavigation,
  getTransformForScreenInsideNavigation,
  isTablet,
  logToFile
} from '../util/common';
import { OutletStore } from '../store/outletStore';
import moment from 'moment';
import {
  ORDER_TYPE,
  TICK_LIST_TYPE,
  USER_ORDER_STATUS,
  COURIER_INFO_DICT,
  EXPAND_TAB_TYPE,
  PRODUCT_PRICE_TYPE,
  UNIT_TYPE_SHORT,
  UNIT_TYPE,
  PRIVILEGES_NAME,
  ROLE_TYPE,
  KD_ITEM_STATUS,
  KD_PRINT_EVENT_TYPE,
} from '../constant/common';
import { UserStore } from '../store/userStore';
import { MerchantStore } from '../store/merchantStore';
import { PageStore } from '../store/pageStore';
import { CommonStore } from '../store/commonStore';
// import { Checkbox } from 'react-native-paper';
import { convertCartItemForKitchenSummary } from '../util/common';
import CheckBox from '@react-native-community/checkbox';
import { useNetInfo } from "@react-native-community/netinfo";
import APILocal from '../util/apiLocalReplacers';
import { useFocusEffect } from '@react-navigation/native';
import UserIdleWrapper from '../components/userIdleWrapper';
import { calcPrintTotalForKdIndividual, calcPrintTotalForKdIndividualCancelled, isSunmiDevice, printDocketCancelled, printDocketForKDCancelled, printKDSummaryCategoryWrapper, printUserOrder } from "../util/printer";
// import { storageMMKV } from '../util/storageMMKV';
import firestore from '@react-native-firebase/firestore';
import { Collections } from '../constant/firebase';
import { PRINTER_USAGE_TYPE } from "../constant/printer";

const AnimatedIcon = Animated.createAnimatedComponent(IIcon);
const ModalView = Platform.OS === 'android' ? ModalComponent : ModalViewComponent;

const KitchenScreen = React.memo((props) => {
  const { navigation } = props;

  ///////////////////////////////////////////////////////////

  const [isMounted, setIsMounted] = useState(true);

  useFocusEffect(
    useCallback(() => {
      setIsMounted(true);
      return () => {
        setIsMounted(false);
      };
    }, [])
  );

  ///////////////////////////////////////////////////////////

  const netInfo = useNetInfo();

  const { width: windowWidth, height: windowHeight } = useWindowDimensions();

  const [viewOptions, setViewOptions] = useState(true);
  const [category, setCategory] = useState([]);
  const [orderItems, setOrderItems] = useState([]);
  const [result, setResult] = useState([]);
  const [orders, setOrders] = useState([]);
  // const [scroll, setScroll] = useState(false);
  const [deliveryScreen, setDeliveryScreen] = useState(false);
  const [stockMissingDesc, setStockMissingDesc] = useState(null);
  const [expandView, setExpandView] = useState(false);
  const [orderId, setOrderId] = useState(null);
  const [showSummary, setShowSummary] = useState(false);
  const [refresh, setRefresh] = useState(false);
  const [test, setTest] = useState(null);
  const [seconds, setSeconds] = useState(10);

  const [summary, setSummary] = useState([]);
  const [summaryCancelled, setSummaryCancelled] = useState([]);
  const [summaryDelivered, setSummaryDelivered] = useState([]);

  // const [selectedSummaryCheckDict, setSelectedSummaryCheckDict] = useState({});
  // const [selectedsummaryCancelledCheckDict, setSelectedsummaryCancelledCheckDict] = useState({});
  // const [selectedSummaryDeliveredCheckDict, setSelectedSummaryDeliveredCheckDict] = useState({});

  const [list, setList] = useState([]);
  const [search, setSearch] = useState('');
  const [categories, setCategories] = useState([]);
  const [selectedCategoriesIds, setSelectedCategoriesIds] = useState([]);
  const [availableCategories, setAvailableCategories] = useState([]);
  const [orderItemList, setOrderItemList] = useState([]);
  const [selectedCat, setSelectedCat] = useState([]);
  const [footerOpen, setFooterOpen] = useState([]);
  const [removeFilter, setRemoveFilter] = useState('');
  const [scroll, setScroll] = useState(true);
  const [allKitchen, setAllKitchen] = useState([]);

  const [item, setItem] = useState({});
  const [checkSelectedItem, setCheckSelectedItem] = useState([]);
  const [controller, setController] = useState({});

  const [sortingType, setSortingType] = useState(0);

  const [refreshRate, setRefreshRate] = useState(new Date());

  const setState = () => { };

  const [userOrdersActiveFirstPhase, setUserOrdersActiveFirstPhase] = useState([]);
  const [userOrdersActive, setUserOrdersActive] = useState([]);
  const [userOrdersPrepared, setUserOrdersPrepared] = useState([]);

  const [showUndoUserOrderModal, setShowUndoUserOrderModal] = useState(false);
  const [selectedUserOrderUndo, setSelectedUserOrderUndo] = useState(null);
  const [userOrderUndoRemarks, setUserOrderUndoRemarks] = useState('');

  const [isAscending, setIsAscending] = useState(true);

  const [selectedOrderItemList, setSelectedOrderItemList] = useState([]);
  const [selectedOrderItemCancelledList, setSelectedOrderItemCancelledList] =
    useState([]);
  const [selectedOrderItemDeliveredList, setSelectedOrderItemDeliveredList] =
    useState([]);

  // const userOrders = OutletStore.useState((s) => s.userOrders
  //   .filter(order => {
  //     return !(
  //       order.orderStatus === USER_ORDER_STATUS.ORDER_COMPLETED
  //       ||
  //       (order.paymentDetails && order.paymentDetails.channel)
  //     );
  //   })
  // );

  const [userOrders, setUserOrders] = useState([]);

  const userOrdersRaw = OutletStore.useState(s => s.userOrders);

  const outletTablesDict = OutletStore.useState((s) => s.outletTablesDict);
  const [switchMerchant, setSwitchMerchant] = useState(isTablet() ? false : true);

  const userOrdersExpandedDict = OutletStore.useState(
    (s) => s.userOrdersExpandedDict,
  );
  // const kitchenScrollItem = PageStore.useState(s => s.kitchenScrollItem);

  const userName = UserStore.useState((s) => s.name);
  const firebaseUid = UserStore.useState((s) => s.firebaseUid);
  const merchantName = MerchantStore.useState((s) => s.name);

  const outletSelectDropdownView = CommonStore.useState(
    (s) => s.outletSelectDropdownView,
  );

  const isLoading = CommonStore.useState((s) => s.isLoading);

  const kitchenOrderRef = useRef(null);

  //////////////////////////////////////////////////////////////

  const currOutletId = MerchantStore.useState((s) => s.currOutletId);
  const allOutlets = MerchantStore.useState((s) => s.allOutlets);

  const currOutlet = MerchantStore.useState((s) => s.currOutlet);

  //////////////////////////////////////////////////////////////

  const summaryCheckDict = CommonStore.useState((s) => s.summaryCheckDict);
  const summaryCancelledCheckDict = CommonStore.useState(
    (s) => s.summaryCancelledCheckDict,
  );
  const summaryDeliveredCheckDict = CommonStore.useState(
    (s) => s.summaryDeliveredCheckDict,
  );

  const isAlphaUser = UserStore.useState((s) => s.isAlphaUser);
  const expandTab = CommonStore.useState((s) => s.expandTab);
  const currPageStack = CommonStore.useState((s) => s.currPageStack);

  const [selectedPrinterAreaList, setSelectedPrinterAreaList] = useState([]);
  const [printerAreaDropdownList, setPrinterAreaDropdownList] = useState([]);

  const outletPrinters = OutletStore.useState((s) => s.outletPrinters.concat(s.sunmiPrinters).concat(s.iminPrinters).concat(s.blePrinters));

  const privileges_state = UserStore.useState((s) => s.privileges);

  const [privileges, setPrivileges] = useState([]);
  const role = UserStore.useState((s) => s.role);
  const pinNo = UserStore.useState(s => s.pinNo);

  // Category
  const [outletCategoryDropdownList, setOutletCategoryDropdownList] = useState([]);
  // const [selectedOutletCategoryList, setSelectedOutletCategoryList] = useState([]);
  const selectedOutletCategoryList = CommonStore.useState(s => s.selectedOutletCategoryList);
  const outletCategories = OutletStore.useState((s) => s.outletCategories);

  useEffect(() => {
    const useEffectCallback = async () => {
      // admin full access

      // const enteredPinNo = await AsyncStorage.getItem('enteredPinNo');
      // const enteredPinNo = storageMMKV.getString('enteredPinNo');
      const enteredPinNo = await global.watermelonDBDatabase.localStorage.get('enteredPinNo');

      if (role === ROLE_TYPE.ADMIN && pinNo === enteredPinNo) {
        setPrivileges([
          "EMPLOYEES",
          "OPERATION",
          "PRODUCT",
          "INVENTORY",
          "INVENTORY_COMPOSITE",
          "DOCKET",
          "VOUCHER",
          "PROMOTION",
          "CRM",
          "LOYALTY",
          "TRANSACTIONS",
          "REPORT",
          "RESERVATIONS",

          // for action
          'REFUND_ORDER',

          'SETTINGS',

          'QUEUE',

          'OPEN_CASH_DRAWER',

          'KDS',

          'UPSELLING',

          // for Kitchen

          'REJECT_ITEM',
          'CANCEL_ORDER',
          //'REFUND_tORDER',

          'MANUAL_DISCOUNT',
        ]);
      } else {
        setPrivileges(privileges_state || []);
      }
    };

    useEffectCallback();
  }, [role, privileges_state, pinNo]);

  //////////////////////////////////////////////////////////////

  // 2023-11-01 - Set orders data for flatlist

  // useEffect(() => {
  //   if (isMounted) {
  //     console.log('userOrdersRaw');
  //     // console.log(userOrdersRaw);

  //     const userOrdersTemp = userOrdersRaw.filter(order => {
  //       console.log(order.orderId);
  //       console.log(order.uniqueId);

  //       return !(
  //         order.orderStatus === USER_ORDER_STATUS.ORDER_COMPLETED
  //         // ||
  //         // (order.paymentDetails && order.paymentDetails.channel)
  //       );
  //     });

  //     console.log('userOrdersTemp');
  //     console.log(userOrdersTemp);

  //     setUserOrders(userOrdersTemp);
  //   }
  // }, [userOrdersRaw, isMounted]);

  //////////////////////////////////////////////////////////////

  console.log('summaryCheckDict');
  console.log(summaryCheckDict);

  //////////////////////////////////////////////////////////////

  useEffect(() => {
    var uniquePrinterAreaNameList = [];
    var uniquePrinterAreaList = [];

    for (var i = 0; i < outletPrinters.length; i++) {
      var name = '';

      if (outletPrinters[i].area) {
        name = outletPrinters[i].area;
      } else if (outletPrinters[i].name) {
        name = outletPrinters[i].name;
      }

      if (name && !uniquePrinterAreaNameList.includes(name)) {
        uniquePrinterAreaNameList.push(name);
        uniquePrinterAreaList.push(outletPrinters[i]);
      }
    }

    uniquePrinterAreaNameList = [...new Set(uniquePrinterAreaNameList)];
    // uniquePrinterAreaList = [...new Set(uniquePrinterAreaList)];

    const printerAreaDropdownListTemp = uniquePrinterAreaList.map((item) => ({
      label: item.area || item.name,
      // value: item.uniqueId,
      value: item.area || item.name,
    }));

    setPrinterAreaDropdownList(printerAreaDropdownListTemp);

    if (printerAreaDropdownListTemp.length > 0) {
      console.log('help select');

      // setSelectedPrinterAreaList([printerAreaDropdownListTemp[0].value]);
      setSelectedPrinterAreaList(printerAreaDropdownListTemp.map(printerArea => printerArea.value));
    } else {
      console.log('make blank');

      setSelectedPrinterAreaList([]);
    }
  }, [outletPrinters]);

  useEffect(() => {
    setOutletCategoryDropdownList(
      outletCategories.map(({ name, uniqueId }) => ({
        label: name,
        value: uniqueId,
      }))
    );

    if (!selectedOutletCategoryList && outletCategories.length > 0) {
      // setSelectedOutletCategoryList(null);
      CommonStore.update((s) => {
        s.selectedOutletCategoryList = [];
      });
    }
  }, [outletCategories]);

  useEffect(() => {
    const fetchKdsCatList = async () => {
      try {
        const outletRef = firestore().collection(Collections.Outlet).doc(currOutletId);
        const outletDoc = await outletRef.get();

        if (outletDoc.exists) {
          const data = outletDoc.data();

          console.log('check firebase kds cat list:', data.kdsCatList);
          console.log('--stop---')
          const filteredKdsCatList = (data.kdsCatList ? data.kdsCatList : []).filter(cat =>
            outletCategories.some(category => category.uniqueId === cat)
          );

          CommonStore.update((s) => {
            s.selectedOutletCategoryList = filteredKdsCatList || []
          });
        } else {
          console.log('outlet not found');
        }
      } catch (error) {
        console.error('Error fetching kdsCatList:', error);
      }
    };

    fetchKdsCatList();
  }, [currOutletId, outletCategories]);

  //////////////////////////////////////////////////////////////

  useEffect(() => {
    setTimeout(() => {
      setRefreshRate(new Date());
      //// console.log('Testing 123')
    }, 30000);
  }, [refreshRate]);

  useEffect(() => {
    if (
      isMounted
      // true
    ) {
      // var tempUserOrdersActive = userOrders.filter(order => {
      //     var isValid = false;

      //     if (order.orderType === ORDER_TYPE.DELIVERY || order.orderType === ORDER_TYPE.PICKUP) {

      //         if (order.orderStatus !== USER_ORDER_STATUS.ORDER_PREPARED &&
      //             order.orderStatus !== USER_ORDER_STATUS.ORDER_RECEIVED &&
      //             order.orderStatus !== USER_ORDER_STATUS.ORDER_DELIVERED) {
      //             isValid = true;
      //         }
      //     }
      //     else {
      //         if (order.orderStatus !== USER_ORDER_STATUS.ORDER_DELIVERED) {
      //             isValid = true;
      //         }
      //     }

      //     return isValid;
      // });

      InteractionManager.runAfterInteractions(() => {
        var tempUserOrdersActive = [];
        var tempUserOrdersPrepared = [];

        for (var i = 0; i < userOrders.length; i++) {
          const order = userOrders[i];

          var toSkip = false;

          if (
            (
              // userOrders[i].orderStatus === USER_ORDER_STATUS.ORDER_RECEIVED ||

              // userOrders[i].orderStatus === USER_ORDER_STATUS.ORDER_CANCELLED_BY_MERCHANT ||
              // userOrders[i].orderStatus === USER_ORDER_STATUS.ORDER_CANCELLED_BY_USER ||
              (userOrders[i].orderStatus ===
                USER_ORDER_STATUS.ORDER_SENDER_REJECTED ||
                userOrders[i].orderStatus ===
                USER_ORDER_STATUS.ORDER_SENDER_CANCELED || userOrders[i].orderStatus ===
                USER_ORDER_STATUS.ORDER_SENDER_EXPIRED))
            // &&
            // (order.orderType === ORDER_TYPE.DELIVERY ||
            //   order.orderType === ORDER_TYPE.PICKUP)
          ) {
            toSkip = true;
          }
          else if (currOutlet.dineInRequiredAuthorization && order.orderType === ORDER_TYPE.DINEIN &&
            order.orderStatus === USER_ORDER_STATUS.ORDER_RECEIVED) {
            toSkip = true;
          }

          if (!toSkip) {
            var isValid = false;

            if (
              order.orderType === ORDER_TYPE.DELIVERY ||
              order.orderType === ORDER_TYPE.PICKUP
            ) {
              if (
                order.orderStatus !== USER_ORDER_STATUS.ORDER_PREPARED &&
                // order.orderStatus !== USER_ORDER_STATUS.ORDER_RECEIVED &&
                order.orderStatus !== USER_ORDER_STATUS.ORDER_DELIVERED &&
                order.orderStatus !== USER_ORDER_STATUS.ORDER_REJECTED_BY_MERCHANT
              ) {
                isValid = true;
              }
            } else if (
              order.orderStatus !== USER_ORDER_STATUS.ORDER_DELIVERED &&
              order.orderStatus !== USER_ORDER_STATUS.ORDER_REJECTED_BY_MERCHANT
            ) {
              isValid = true;
            }

            if (isValid) {
              tempUserOrdersActive.push(order);
            } else {
              // if (order.paymentDetails === null) {
              //     tempUserOrdersPrepared.push(order);
              // }

              tempUserOrdersPrepared.push(order);
            }
          }
        }

        //setAllRecords(allRecordsTemp.sort((a, b) => b.updatedAt - a.updatedAt));
        //setAllKitchen(tempUserOrdersActive.sort((a, b) => b.isPrioritizedOrder - a.isPrioritizedOrder));
        // setUserOrdersActive(tempUserOrdersActive.sort((a, b) => b.isPrioritizedOrder - a.isPrioritizedOrder));
        //setUserOrdersActive(tempUserOrdersActive.slice(0).sort((a, b) => (moment().valueOf() - b.estimatedPreparedDate) - (moment().valueOf() - a.estimatedPreparedDate)));

        //// console.log(orderId)

        var tempSummaryDict = {};
        var tempSummaryCancelledDict = {};
        var tempSummaryDeliveredDict = {};

        for (var i = 0; i < tempUserOrdersActive.length; i++) {
          for (var j = 0; j < tempUserOrdersActive[i].cartItems.length; j++) {
            const tempSummaryItem = convertCartItemForKitchenSummary(
              tempUserOrdersActive[i].cartItems[j],
              tempUserOrdersActive[i],
            );

            if (tempUserOrdersActive[i].cartItems[j].deliveredAt === null) {
              if (tempSummaryDict[tempSummaryItem.key]) {
                tempSummaryDict[tempSummaryItem.key].push(tempSummaryItem.value);
              } else {
                tempSummaryDict[tempSummaryItem.key] = [tempSummaryItem.value];
              }
            } else if (tempSummaryDeliveredDict[tempSummaryItem.key]) {
              tempSummaryDeliveredDict[tempSummaryItem.key].push(
                tempSummaryItem.value,
              );
            } else {
              tempSummaryDeliveredDict[tempSummaryItem.key] = [
                tempSummaryItem.value,
              ];
            }
          }

          if (tempUserOrdersActive[i].cartItemsCancelled) {
            for (
              var j = 0;
              j < tempUserOrdersActive[i].cartItemsCancelled.length;
              j++
            ) {
              const tempSummaryItem = convertCartItemForKitchenSummary(
                tempUserOrdersActive[i].cartItemsCancelled[j],
                tempUserOrdersActive[i],
              );

              if (tempSummaryCancelledDict[tempSummaryItem.key]) {
                tempSummaryCancelledDict[tempSummaryItem.key].push(
                  tempSummaryItem.value,
                );
              } else {
                tempSummaryCancelledDict[tempSummaryItem.key] = [
                  tempSummaryItem.value,
                ];
              }
            }
          }
        }

        setSummary(
          Object.entries(tempSummaryDict)
            .map(([key, cartItemList]) => {
              var earliestDate = Date.now();

              var quantityAccum = cartItemList.reduce(
                (accum, cartItem) => accum + cartItem.quantity,
                0,
              );

              cartItemList.map(item => {
                // console.log('=====================');
                // console.log(`userOrderId: ${item.userOrderId}`);
                // console.log(`cartItemDate: ${item.cartItemDate}`);
                // console.log('=====================');
              })

              var addOnParsedDictAccum = {};
              for (var i = 0; i < cartItemList.length; i++) {
                if (moment(cartItemList[i].cartItemDate).isBefore(earliestDate)) {
                  earliestDate = cartItemList[i].cartItemDate;
                }

                for (var j = 0; j < cartItemList[i].addOnParsedList.length; j++) {
                  if (
                    addOnParsedDictAccum[cartItemList[i].addOnParsedList[j].name]
                  ) {
                    addOnParsedDictAccum[cartItemList[i].addOnParsedList[j].name] +=
                      cartItemList[i].addOnParsedList[j].quantity
                        ? cartItemList[i].addOnParsedList[j].quantity
                        : 0;
                  } else {
                    addOnParsedDictAccum[cartItemList[i].addOnParsedList[j].name] =
                      cartItemList[i].addOnParsedList[j].quantity
                        ? cartItemList[i].addOnParsedList[j].quantity
                        : 0;
                  }
                }
              }

              var addOnParsedListAccum = Object.entries(addOnParsedDictAccum)
                .map(([key, value]) => ({
                  name: key,
                  quantity: value,
                }))
                .sort((b, a) => (a.name > b.name ? 1 : -1));

              return {
                key,
                name: cartItemList[0].name,
                cartItemList,

                quantityAccum,
                addOnParsedListAccum,

                earliestDate,

                priceType: cartItemList[0].priceType ? cartItemList[0].priceType : PRODUCT_PRICE_TYPE.FIXED,
                unitType: cartItemList[0].unitType ? cartItemList[0].unitType : UNIT_TYPE.GRAM,
              };
            })
            // .sort((b, a) => (a.name > b.name ? 1 : -1)),
            .sort((a, b) => a.earliestDate - b.earliestDate),
        );

        setSummaryDelivered(
          Object.entries(tempSummaryDeliveredDict)
            .map(([key, cartItemList]) => {
              var earliestDate = Date.now();

              var quantityAccum = cartItemList.reduce(
                (accum, cartItem) => accum + cartItem.quantity,
                0,
              );

              var addOnParsedDictAccum = {};
              for (var i = 0; i < cartItemList.length; i++) {
                if (moment(cartItemList[i].cartItemDate).isBefore(earliestDate)) {
                  earliestDate = cartItemList[i].cartItemDate;
                }

                for (var j = 0; j < cartItemList[i].addOnParsedList.length; j++) {
                  if (
                    addOnParsedDictAccum[cartItemList[i].addOnParsedList[j].name]
                  ) {
                    addOnParsedDictAccum[cartItemList[i].addOnParsedList[j].name] +=
                      cartItemList[i].addOnParsedList[j].quantity
                        ? cartItemList[i].addOnParsedList[j].quantity
                        : 0;
                  } else {
                    addOnParsedDictAccum[cartItemList[i].addOnParsedList[j].name] =
                      cartItemList[i].addOnParsedList[j].quantity
                        ? cartItemList[i].addOnParsedList[j].quantity
                        : 0;
                  }
                }
              }

              var addOnParsedListAccum = Object.entries(addOnParsedDictAccum)
                .map(([key, value]) => ({
                  name: key,
                  quantity: value,
                }))
                .sort((b, a) => (a.name > b.name ? 1 : -1));

              return {
                key,
                name: cartItemList[0].name,
                cartItemList,

                quantityAccum,
                addOnParsedListAccum,

                earliestDate, // 2022-12-20 - missing field to add

                priceType: cartItemList[0].priceType ? cartItemList[0].priceType : PRODUCT_PRICE_TYPE.FIXED,
                unitType: cartItemList[0].unitType ? cartItemList[0].unitType : UNIT_TYPE.GRAM,
              };
            })
            // .sort((b, a) => (a.name > b.name ? 1 : -1)),
            .sort((a, b) => a.earliestDate - b.earliestDate),
        );

        setSummaryCancelled(
          Object.entries(tempSummaryCancelledDict)
            .map(([key, cartItemList]) => {
              var earliestDate = Date.now();

              var quantityAccum = cartItemList.reduce(
                (accum, cartItem) => accum + cartItem.quantity,
                0,
              );

              var addOnParsedDictAccum = {};
              for (var i = 0; i < cartItemList.length; i++) {
                if (moment(cartItemList[i].cartItemDate).isBefore(earliestDate)) {
                  earliestDate = cartItemList[i].cartItemDate;
                }

                for (var j = 0; j < cartItemList[i].addOnParsedList.length; j++) {
                  if (
                    addOnParsedDictAccum[cartItemList[i].addOnParsedList[j].name]
                  ) {
                    addOnParsedDictAccum[cartItemList[i].addOnParsedList[j].name] +=
                      cartItemList[i].addOnParsedList[j].quantity
                        ? cartItemList[i].addOnParsedList[j].quantity
                        : 0;
                  } else {
                    addOnParsedDictAccum[cartItemList[i].addOnParsedList[j].name] =
                      cartItemList[i].addOnParsedList[j].quantity
                        ? cartItemList[i].addOnParsedList[j].quantity
                        : 0;
                  }
                }
              }

              var addOnParsedListAccum = Object.entries(addOnParsedDictAccum)
                .map(([key, value]) => ({
                  name: key,
                  quantity: value,
                }))
                .sort((b, a) => (a.name > b.name ? 1 : -1));

              return {
                key,
                name: cartItemList[0].name,
                cartItemList,

                quantityAccum,
                addOnParsedListAccum,

                earliestDate,

                priceType: cartItemList[0].priceType ? cartItemList[0].priceType : PRODUCT_PRICE_TYPE.FIXED,
                unitType: cartItemList[0].unitType ? cartItemList[0].unitType : UNIT_TYPE.GRAM,
              };
            })
            // .sort((b, a) => (a.name > b.name ? 1 : -1)),
            .sort((a, b) => a.earliestDate - b.earliestDate),
        );

        // tempUserOrdersActive.sort(
        //   (a, b) =>
        //     moment().valueOf() -
        //     b.createdAt -
        //     (moment().valueOf() - a.createdAt),
        // );

        setUserOrdersActiveFirstPhase(
          tempUserOrdersActive.sort(
            (a, b) => b.isPrioritizedOrder - a.isPrioritizedOrder,
          ),
        );

        setUserOrdersPrepared(
          tempUserOrdersPrepared.sort((a, b) => b.updatedAt - a.updatedAt),
        );

        console.log('tempUserOrdersActive');
        console.log(tempUserOrdersActive);
        console.log('tempUserOrdersPrepared');
        console.log(tempUserOrdersPrepared);

        //setUserOrdersActive(tempUserOrdersActive);
      });
    }
  }, [userOrders, isMounted]);

  useEffect(() => {
    InteractionManager.runAfterInteractions(() => {
      // setSortingType(param.value);

      // var tempUserOrdersActive = userOrders.filter(order => order.orderStatus !== USER_ORDER_STATUS.ORDER_DELIVERED && order.orderStatus !== USER_ORDER_STATUS.ORDER_PREPARED);
      var tempUserOrdersActive = userOrdersActiveFirstPhase.filter((order) => {
        // var isValid = false;
        // //setUserOrdersActive(tempUserOrdersActive.slice(0).sort((a, b) => b.isPrioritizedOrder - a.isPrioritizedOrder));

        // if (
        //   order.orderType === ORDER_TYPE.DELIVERY ||
        //   order.orderType === ORDER_TYPE.PICKUP
        // ) {
        //   if (order.orderStatus !== USER_ORDER_STATUS.ORDER_RECEIVED) {
        //     isValid = true;
        //   }
        // } else {
        //   isValid = true;
        // }

        // return isValid;

        var toSkip = false;

        if (
          (
            // order.orderStatus === USER_ORDER_STATUS.ORDER_RECEIVED ||

            // userOrders[i].orderStatus === USER_ORDER_STATUS.ORDER_CANCELLED_BY_MERCHANT ||
            // userOrders[i].orderStatus === USER_ORDER_STATUS.ORDER_CANCELLED_BY_USER ||
            (order.orderStatus === USER_ORDER_STATUS.ORDER_SENDER_REJECTED ||
              order.orderStatus === USER_ORDER_STATUS.ORDER_SENDER_CANCELED || order.orderStatus === USER_ORDER_STATUS.ORDER_SENDER_EXPIRED))
          //   &&
          // (order.orderType === ORDER_TYPE.DELIVERY ||
          //   order.orderType === ORDER_TYPE.PICKUP)
        ) {
          toSkip = true;
        }
        else if (currOutlet.dineInRequiredAuthorization && order.orderType === ORDER_TYPE.DINEIN &&
          order.orderStatus === USER_ORDER_STATUS.ORDER_RECEIVED) {
          toSkip = true;
        }

        if (!toSkip) {
          var isValid = false;

          if (
            order.orderType === ORDER_TYPE.DELIVERY ||
            order.orderType === ORDER_TYPE.PICKUP
          ) {
            if (
              order.orderStatus !== USER_ORDER_STATUS.ORDER_PREPARED &&
              // order.orderStatus !== USER_ORDER_STATUS.ORDER_RECEIVED &&
              order.orderStatus !== USER_ORDER_STATUS.ORDER_DELIVERED &&
              order.orderStatus !== USER_ORDER_STATUS.ORDER_REJECTED_BY_MERCHANT
            ) {
              isValid = true;
            }
          } else if (
            order.orderStatus !== USER_ORDER_STATUS.ORDER_DELIVERED &&
            order.orderStatus !== USER_ORDER_STATUS.ORDER_REJECTED_BY_MERCHANT
          ) {
            isValid = true;
          }

          ////////////////////////////////////////

          // 2022-11-24 - Check for printer area list

          // var isPrinterValid = false;

          // for (var i = 0; i < selectedPrinterAreaList.length; i++) {
          //   const printerArea = selectedPrinterAreaList[i];

          //   order.cartItems.map(cartItem => {
          //     if (cartItem.printerAreaList && cartItem.printerAreaList.includes(printerArea)) {
          //       isPrinterValid = true;
          //     }

          //     if (
          //       global.allOutletsCategoriesDict[cartItem.categoryId] &&
          //       global.allOutletsCategoriesDict[cartItem.categoryId].printerAreaList &&
          //       global.allOutletsCategoriesDict[cartItem.categoryId].printerAreaList.includes(
          //         printerArea,
          //       )
          //     ) {
          //       // add, if the product level printer area list not included, and the product level printer area list is empty

          //       isPrinterValid = true;
          //     }
          //   });

          //   if (order.cartItemsCancelled && !isPrinterValid) {
          //     order.cartItemsCancelled.map(cartItem => {
          //       if (cartItem.printerAreaList && cartItem.printerAreaList.includes(printerArea)) {
          //         isPrinterValid = true;
          //       }

          //       if (
          //         global.allOutletsCategoriesDict[cartItem.categoryId] &&
          //         global.allOutletsCategoriesDict[cartItem.categoryId].printerAreaList &&
          //         global.allOutletsCategoriesDict[cartItem.categoryId].printerAreaList.includes(
          //           printerArea,
          //         )
          //       ) {
          //         // add, if the product level printer area list not included, and the product level printer area list is empty

          //         isPrinterValid = true;
          //       }
          //     });
          //   }

          //   if (!isPrinterValid) {
          //     break;
          //   }
          // }

          // if (selectedPrinterAreaList.length <= 0) {
          //   isPrinterValid = true;
          // }

          // 2022-11-24 - Check for category list

          var isCategoryValid = false;

          for (var i = 0; i < selectedOutletCategoryList.length; i++) { // Change to selectedCategoryList

            if (!isCategoryValid) {
              const selectedCategory = selectedOutletCategoryList[i];

              order.cartItems.map(cartItem => {
                // Check if the cart item's category matches the selected category
                if (cartItem.categoryId === selectedCategory) {
                  isCategoryValid = true; // Set to true if a match is found
                }

                // Check if the category in the global dictionary has the selected category
                if (
                  global.allOutletsCategoriesDict[cartItem.categoryId] &&
                  global.allOutletsCategoriesDict[cartItem.categoryId].uniqueId === selectedCategory
                ) {
                  isCategoryValid = true; // Set to true if a match is found
                }
              });

              if (order.cartItemsCancelled && !isCategoryValid) {
                order.cartItemsCancelled.map(cartItem => {
                  // Check if the cart item's category matches the selected category
                  if (cartItem.categoryId === selectedCategory) {
                    isCategoryValid = true; // Set to true if a match is found
                  }

                  // Check if the category in the global dictionary has the selected category
                  if (
                    global.allOutletsCategoriesDict[cartItem.categoryId] &&
                    global.allOutletsCategoriesDict[cartItem.categoryId].uniqueId === selectedCategory
                  ) {
                    isCategoryValid = true; // Set to true if a match is found
                  }
                });
              }
            }
          }

          if (selectedOutletCategoryList.length <= 0) {
            isCategoryValid = true;
          }

          ////////////////////////////////////////

          // return isValid && isPrinterValid;
          return isValid && isCategoryValid;
        }
      });

      tempUserOrdersActive = tempUserOrdersActive.slice(0);

      // setUserOrdersActive(tempUserOrdersActive);
      // if (param.value == 0){ //default show prioritize order first
      //     setUserOrdersActive(tempUserOrdersActive.slice(0).sort((a, b) => b.isPrioritizedOrder - a.isPrioritizedOrder));
      // }
      if (sortingType == 0) {
        tempUserOrdersActive.sort(
          (a, b) =>
            moment().valueOf() -
            b.createdAt -
            (moment().valueOf() - a.createdAt),
        );
      }
      if (sortingType == 1) {
        //Prioritize
        tempUserOrdersActive.sort(
          (a, b) => b.isPrioritizedOrder - a.isPrioritizedOrder,
        );
      }
      if (sortingType == 2) {
        //orderid
        tempUserOrdersActive.sort((a, b) => b.orderId.localeCompare(a.orderId));
      }
      if (sortingType == 3) {
        //date time
        tempUserOrdersActive.sort((a, b) => b.orderDate - a.orderDate);
      }
      if (sortingType == 4) {
        //Name
        tempUserOrdersActive.sort((a, b) => b.userName.localeCompare(a.userName));
      }
      if (sortingType == 5) {
        //Waiting Time
        // tempUserOrdersActive.sort(
        //   (a, b) =>
        //     moment().valueOf() -
        //     b.estimatedPreparedDate -
        //     (moment().valueOf() - a.estimatedPreparedDate),
        // );
        tempUserOrdersActive.sort(
          (a, b) =>
            moment().valueOf() -
            b.createdAt -
            (moment().valueOf() - a.createdAt),
        );
      }
      if (sortingType == 6) {
        //Payment Method
        tempUserOrdersActive.sort((a, b) =>
          (b.orderStatus ? b.orderStatus : '').localeCompare(
            a.orderStatus ? a.orderStatus : '',
          ),
        );
      }
      if (sortingType == 7) {
        //Total
        tempUserOrdersActive.sort((a, b) => b.finalPrice - a.finalPrice);
      }
      if (sortingType == 8) {
        //Prioritize
        tempUserOrdersActive.sort(
          (a, b) => b.isPrioritizedOrder - a.isPrioritizedOrder,
        );
      }

      if (!isAscending) {
        tempUserOrdersActive.reverse();
      }

      if (search.length > 0) {
        tempUserOrdersActive = tempUserOrdersActive.filter((order) => {
          var isFound = false;

          if (
            order.orderId &&
            order.orderId.toLowerCase().includes(search.toLowerCase())
          ) {
            isFound = true;
          }

          if (
            order.tableCode &&
            order.tableCode.toLowerCase().includes(search.toLowerCase())
          ) {
            isFound = true;
          }

          var orderEstimatedTime =
            // (moment().valueOf() - order.estimatedPreparedDate) / (1000 * 60);
            (moment().valueOf() - order.createdAt) / (1000 * 60);
          orderEstimatedTime = `${orderEstimatedTime < 0 ? 0 : orderEstimatedTime.toFixed(0)
            }mins`;
          if (orderEstimatedTime.toLowerCase().includes(search.toLowerCase())) {
            isFound = true;
          }

          for (var i = 0; i < order.cartItems.length; i++) {
            if (
              order.cartItems[i].name.toLowerCase().includes(search.toLowerCase())
            ) {
              isFound = true;
              break;
            }
          }

          if (order.cartItemsCancelled) {
            for (var i = 0; i < order.cartItemsCancelled.length; i++) {
              if (
                order.cartItemsCancelled[i].name
                  .toLowerCase()
                  .includes(search.toLowerCase())
              ) {
                isFound = true;
                break;
              }
            }
          }

          return isFound;
        });
      }

      setUserOrdersActive(tempUserOrdersActive);

      console.log('tempUserOrdersActive');
      console.log(tempUserOrdersActive);
    });
  }, [sortingType, isAscending, search,
    // selectedPrinterAreaList,
    selectedOutletCategoryList,
    currOutlet, userOrdersActiveFirstPhase]);

  useEffect(() => {
    InteractionManager.runAfterInteractions(() => {
      var tempSelectedOrderItemList = [];
      var tempSelectedOrderItemCancelledList = [];
      var tempSelectedOrderItemDeliveredList = [];

      if (selectedUserOrderUndo && selectedUserOrderUndo.orderType) {
        if (selectedUserOrderUndo && selectedUserOrderUndo.uniqueId) {
          for (var k = 0; k < selectedUserOrderUndo.cartItems.length; k++) {
            if (selectedUserOrderUndo.cartItems[k].deliveredAt === null) {
              tempSelectedOrderItemList.push(selectedUserOrderUndo.cartItems[k]);
            } else {
              tempSelectedOrderItemDeliveredList.push(
                selectedUserOrderUndo.cartItems[k],
              );
            }
          }

          if (selectedUserOrderUndo.cartItemsCancelled) {
            for (
              var k = 0;
              k < selectedUserOrderUndo.cartItemsCancelled.length;
              k++
            ) {
              tempSelectedOrderItemCancelledList.push(
                selectedUserOrderUndo.cartItemsCancelled[k],
              );
            }
          }
        }
      }

      setSelectedOrderItemList(tempSelectedOrderItemList);
      setSelectedOrderItemCancelledList(tempSelectedOrderItemCancelledList);
      setSelectedOrderItemDeliveredList(tempSelectedOrderItemDeliveredList);
    });
  }, [selectedUserOrderUndo]);

  // constructor({ navigation, props }) {
  //     super(props)
  //     state = {

  //     }
  //     controller;

  // }

  // _unsubscribe = navigation.addListener('focus', () => {
  //     controller.close()
  // });

  // navigation.dangerouslyGetParent().setOptions({
  //   tabBarVisible: false,
  // });

  navigation.setOptions({
    headerLeft: () => (
      <TouchableOpacity
        onPress={() => {
          if (isAlphaUser || true) {
            navigation.navigate('MenuOrderingScreen');

            CommonStore.update((s) => {
              s.currPage = 'MenuOrderingScreen';
              s.currPageStack = [...currPageStack, 'MenuOrderingScreen'];
            });
          }
          else {
            navigation.navigate('Table');

            CommonStore.update((s) => {
              s.currPage = 'Table';
              s.currPageStack = [...currPageStack, 'Table'];
            });
          }
          if (expandTab !== EXPAND_TAB_TYPE.OPERATION) {
            CommonStore.update((s) => {
              s.expandTab = EXPAND_TAB_TYPE.OPERATION;
            });
          }
        }} style={styles.headerLeftStyle}>
        <Image
          style={[{
            width: 124,
            height: 26,
          }, switchMerchant ? {
            transform: [
              { scaleX: 0.7 },
              { scaleY: 0.7 }
            ],
          } : {}]}
          resizeMode="contain"
          source={require('../assets/image/logo.png')}
        />
      </TouchableOpacity>
    ),
    headerTitle: () => (
      <View
        style={[
          {
            // justifyContent: Platform.OS === 'android' ? 'center' : 'flex-start',
            // alignItems: Platform.OS === 'android' ? 'center' : 'flex-start',
            // marginRight: Platform.OS === 'ios' ? "27%" : 0,
            // bottom: switchMerchant ? '2%' : 0,
            ...global.getHeaderTitleStyle(),
          },
          // windowWidth >= 768 && switchMerchant
          //   ? { right: windowWidth * 0.1 }
          //   : {},
          // windowWidth <= 768
          //   ? { right: 20 }
          //   : {},
        ]}>
        <Text
          style={{
            fontSize: switchMerchant ? 20 : 24,
            // lineHeight: 25,
            textAlign: 'left',
            alignItems: 'flex-start',
            justifyContent: 'flex-start',
            fontFamily: 'NunitoSans-Bold',
            color: Colors.whiteColor,
            opacity: 1,
            paddingLeft: Platform.OS === 'ios' ? "1%" : 0,
          }}>
          Kitchen
        </Text>
      </View>
    ),
    headerRight: () => (
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
        }}>
        {outletSelectDropdownView()}
        <View
          style={{
            backgroundColor: 'white',
            width: 0.5,
            height: windowHeight * 0.025,
            opacity: 0.8,
            marginHorizontal: 15,
            bottom: -1,
          }} />
        <TouchableOpacity
          onPress={() => {
            if (global.currUserRole === 'admin') {
              navigation.navigate('Setting');
            }
          }}
          style={{ flexDirection: 'row', alignItems: 'center' }}>
          <Text
            style={[{
              fontFamily: 'NunitoSans-SemiBold',
              fontSize: switchMerchant ? 10 : 16,
              color: Colors.secondaryColor,
              marginRight: 15,
            }, switchMerchant ? { width: windowWidth / 8 } : {}]}
            numberOfLines={switchMerchant ? 1 : 1}
          >
            {userName}
          </Text>
          <View
            style={{
              marginRight: 30,
              width: windowHeight * 0.05,
              height: windowHeight * 0.05,
              borderRadius: windowHeight * 0.05 * 0.5,
              alignItems: 'center',
              justifyContent: 'center',
              backgroundColor: 'white',
            }}>
            <Image
              style={{
                width: windowHeight * 0.035,
                height: windowHeight * 0.035,
                alignSelf: 'center',
              }}
              source={require('../assets/image/profile-pic.jpg')}
            />
          </View>
        </TouchableOpacity>
      </View>
    ),
  });

  // const sortingOrders = (param) => {
  //     // setSortingType(param.value);

  //     // var tempUserOrdersActive = userOrders.filter(order => order.orderStatus !== USER_ORDER_STATUS.ORDER_DELIVERED && order.orderStatus !== USER_ORDER_STATUS.ORDER_PREPARED);
  //     var tempUserOrdersActive = userOrders.filter(order => {
  //         var isValid = false;
  //         //setUserOrdersActive(tempUserOrdersActive.slice(0).sort((a, b) => b.isPrioritizedOrder - a.isPrioritizedOrder));

  //         if (order.orderType === ORDER_TYPE.DELIVERY || order.orderType === ORDER_TYPE.PICKUP) {
  //             if (order.orderStatus !== USER_ORDER_STATUS.ORDER_RECEIVED) {
  //                 isValid = true;
  //             }
  //         }
  //         else {
  //             isValid = true;
  //         }

  //         return isValid;
  //     });

  //     // setUserOrdersActive(tempUserOrdersActive);
  //     // if (param.value == 0){ //default show prioritize order first
  //     //     setUserOrdersActive(tempUserOrdersActive.slice(0).sort((a, b) => b.isPrioritizedOrder - a.isPrioritizedOrder));
  //     // }
  //     if (param.value == 1) { //Prioritize
  //         tempUserOrdersActive.slice(0).sort((a, b) => b.isPrioritizedOrder - a.isPrioritizedOrder);
  //     }
  //     if (param.value == 2) { //orderid
  //         tempUserOrdersActive.slice(0).sort((a, b) => b.orderId.localeCompare(a.orderId));
  //     }
  //     if (param.value == 3) { //date time
  //         tempUserOrdersActive.slice(0).sort((a, b) => b.orderDate - a.orderDate);
  //     }
  //     if (param.value == 4) { //Name
  //         tempUserOrdersActive.slice(0).sort((a, b) => b.userName.localeCompare(a.userName));
  //     }
  //     if (param.value == 5) { //Waiting Time
  //         tempUserOrdersActive.slice(0).sort((a, b) => (moment().valueOf() - b.estimatedPreparedDate) - (moment().valueOf() - a.estimatedPreparedDate));
  //     }
  //     if (param.value == 6) { //Payment Method
  //         tempUserOrdersActive.slice(0).sort((a, b) => b.orderStatus.localeCompare(a.orderStatus));
  //     }
  //     if (param.value == 7) { //Total
  //         tempUserOrdersActive.slice(0).sort((a, b) => b.finalPrice - a.finalPrice);
  //     }
  //     if (param.value == 8) { //Prioritize
  //         tempUserOrdersActive.slice(0).sort((a, b) => b.isPrioritizedOrder - a.isPrioritizedOrder);
  //     }

  //     setUserOrdersActive(tempUserOrdersActive);
  // }

  // componentDidMount = () => {
  //     const { navigation } = props;

  //     ApiClient.GET(API.getLeftItem + User.getOutletId()).then((result) => {  //leftItem?outletId=1
  //         const filteredOrders = result.filter(orders => orders.status === 1);
  //         // Extract categories
  //         const tmpCategories = {};
  //         const holder = {};
  //         const obj = [];
  //         for (const order of filteredOrders) { // saving category and "Active " to new object
  //             for (const orderItem of order.orderItems) {

  //                 if (!orderItem.item) {
  //                     continue
  //                 }

  //                 const itemName = orderItem.name;
  //                 if (orderItem.item.category == null) {
  //                     continue
  //                 }
  //                 const category = orderItem.item.category;
  //                 const categoryId = category.name;
  //                 if (holder[itemName]) {
  //                     holder[itemName] = holder[itemName] + orderItem.quantity
  //                 } else {
  //                     holder[itemName] = orderItem.quantity
  //                 }
  //                 if (!tmpCategories[categoryId]) {
  //                     tmpCategories[categoryId] = {
  //                         value: categoryId,
  //                         label: category.name,
  //                         active: false
  //                     };

  //                 }
  //                 if (!orderItem.orderItemAddOns) {
  //                     continue
  //                 }

  //             }
  //         }
  //         for (var props in holder) {
  //             obj.push({ name: props, quantity: holder[props] })
  //         }

  //         const categories = Object.values(tmpCategories);

  //         setState({
  //             orders: filteredOrders,
  //             summary: obj,
  //             categories
  //         })

  //     });
  //     setInterval(() => {
  //         getOrder();
  //     }, 10000);
  //     getOrder();
  // }

  // componentWillUnmount = () => {
  //     _unsubscribe();
  // }

  const getOrder = () => {
    // get Order
    ApiClient.GET(API.getLeftItem + User.getOutletId()).then((result) => {
      //leftItem?outletId=1
      const filteredOrders = result.filter((orders) => orders.status === 1);
      // Extract categories
      const tmpCategories = {};
      const holder = {};
      const obj = [];
      for (const order of filteredOrders) {
        // saving category and "Active " to new object
        for (const orderItem of order.orderItems) {
          if (!orderItem.item) {
            continue;
          }
          const itemName = orderItem.name;
          const { category } = orderItem.item;
          const categoryId = category.name;
          if (holder[itemName]) {
            holder[itemName] = holder[itemName] + orderItem.quantity;
          } else {
            holder[itemName] = orderItem.quantity;
          }
          if (!tmpCategories[categoryId]) {
            tmpCategories[categoryId] = {
              value: categoryId,
              label: category.name,
              active: false,
            };
          }
          if (!orderItem.orderItemAddOns) {
            continue;
          }
        }
      }
      for (var props in holder) {
        obj.push({ name: props, quantity: holder[props] });
      }

      const categories = Object.values(tmpCategories);

      setState({
        orders: filteredOrders,
        summary: obj,
        // categories
      });
    });
  };

  const checkSameItems = (itemName) => {
    const orders = orders;
    const itemSelected =
      checkSelectedItem != undefined ? checkSelectedItem : [];
    const isAlreadySelected = itemSelected.includes(itemName);
    // console.log(itemName);
    if (isAlreadySelected) {
      itemSelected.splice(itemName, 1);
    } else {
      itemSelected.push(itemName);
    }
    setState({ checkSelectedItem: itemSelected });

    setState({ orders });
  };

  const isSameItemCheck = (itemName) => {
    if (checkSelectedItem != undefined) {
      if (checkSelectedItem.includes(itemName)) {
        return true;
      }
    }
    return false;
  };

  const setCategoriesFunc = () => {
    const categories = categories;
    const filteredCat = categories.filter((o) => o.active === true);
    var temp = [];
    filteredCat.map((i) => {
      temp.push(i.value);
    });
    //// console.log("temp", temp)
    setState({ selectedCat: temp });
    //selectedCat = temp
  };

  const getOrderItemStatus = (param) => {
    var flag = false;
    ApiClient.GET(API.getCheckItem + param).then((result) => {
      flag = result.isCheck;
    });

    return flag;
  };

  // const checkOrderItem = (item, orderItem, cb) => {
  //     // var body = {
  //     //     itemId: item.itemId,
  //     //     cartItemDate: item.cartItemDate,
  //     //     orderId: orderItem.uniqueId,
  //     // };

  //     // if (item.isChecked) {
  //     //     ApiClient.POST(API.orderDeliverUndo, body).then(result => {

  //     //     });
  //     // }
  //     // else {
  //     //     ApiClient.POST(API.orderDeliver, body).then(result => {

  //     //     });
  //     // }

  //     var body = {
  //         orderItemList: [
  //             {
  //                 itemId: item.itemId,
  //                 cartItemDate: item.cartItemDate,
  //             }
  //         ],
  //         orderId: orderItem.uniqueId,
  //     };

  //     ApiClient.POST(API.cancelUserOrderItem, body).then(result => {
  //         if (result && result.status === 'success') {
  //             // Alert.alert('Success', 'Cancel successfully');
  //         }
  //         else {
  //             Alert.alert('Error', 'Failed to cancel');
  //         }

  //         cb && cb();
  //     }).catch(err => {
  //         Alert.alert('Error', 'Something went wrong');

  //         cb && cb();
  //     });
  // };

  const getOrderItem = (orderId) => {
    ApiClient.GET(API.getCurrentOrderItem + orderId).then((result) => {
      result.orderItems.map((elements) => {
        return elements.options;
      });
    });
  };

  const completeOrder = (param, cb = () => { }, orderParam) => {
    var body = {
      orderId: param,
      // itemDone: true

      notifyWaiter: true,
      outlet: currOutlet,

      orderIdHuman: `#${(orderParam.orderType === ORDER_TYPE.DINEIN ? '' : 'T') + orderParam.orderId}`,
    };

    (
      // netInfo.isInternetReachable && netInfo.isConnected
      //   ?
      //   ApiClient.POST(API.orderDeliverAll, body)
      //   :
      (APILocal.orderDeliverAll({ body, uid: firebaseUid }))
    )
      .then((result) => {
        // setState({ refresh: true })
        if (result) {
          // getOrder();
          // Alert.alert(
          //   'Success',
          //   'Order Complete',
          //   [
          //     {
          //       text: 'OK',
          //       onPress: () => {
          //         setState({});
          //       },
          //     },
          //   ],
          //   { cancelable: false },
          // );
        }

        cb && cb();
      });
  };

  // for bottom list
  const undoUserOrder = (order) => {
    var body = {
      orderItemList: order.cartItems.map((cartItem) => ({
        itemId: cartItem.itemId,
        cartItemDate: cartItem.cartItemDate,
      })),
      orderId: order.uniqueId,
      remarks: userOrderUndoRemarks,

      outlet: currOutlet,

      orderIdHuman: `#${(order.orderType === ORDER_TYPE.DINEIN ? '' : 'T') + order.orderId}`,
    };

    // console.log(body);

    CommonStore.update((s) => {
      s.isLoading = true;
    });

    // ApiClient.POST(API.orderDeliverUndoMultiple, body)
    APILocal.orderDeliverUndoMultiple({ body, uid: firebaseUid })
      .then((result) => {
        if (result && result.status === 'success') {
          // Alert.alert('Success', 'Undo successfully');

          Alert.alert('Success', 'Order has been undone');

          setShowUndoUserOrderModal(false);
        } else {
          Alert.alert('Error', 'Failed to undo');
        }

        setTimeout(() => {
          CommonStore.update((s) => {
            s.isLoading = false;
          });
        }, 0);
      })
      .catch((err) => {
        console.error(err);
        Alert.alert('Error', 'Something went wrong');

        setTimeout(() => {
          CommonStore.update((s) => {
            s.isLoading = false;
          });
        }, 0);
      });
  };

  const openFooter = (itemId) => {
    var tempList = footerOpen;
    if (tempList.includes(itemId)) {
      tempList.splice(tempList.indexOf(itemId));
    } else {
      tempList.push(itemId);
    }
    setState({ footerOpen: tempList });
  };

  const renderFooter = ({ item, index }) => (
    <View
      style={[{
        alignSelf: 'flex-end',
        height: switchMerchant
          ? windowHeight * 0.12
          : windowHeight * 0.11,
      },
      !switchMerchant && windowWidth < 1000 ?
        {
          height: windowHeight * 0.13,
        }
        : {}
      ]}>
      {/* {footerOpen.includes(item.id) == true ?
            <View style={{ position: 'relative' }}> 
                <KitchenOrder
                    viewOptions={true}
                    checkOrderItem={checkOrderItem.bind(this)}
                    checkSelectedItem={checkSelectedItem}
                    completeOrder={completeOrder.bind(this)}
                    setScroll={scroll => {
                        setState({ scroll })
                    }}
                    search={search}
                    scroll={scroll}
                    order={item} selectedCat={selectedCat}
                    onTimesUp={() => {
                        completeOrder(item.id);
                    }}
                />
                </View>
                : null} */}
      <TouchableOpacity
        onPress={() => {
          // PageStore.update(s => {
          //     s.kitchenScrollItem = item;
          // });

          // if (viewOptions) {
          //     kitchenOrderRef && kitchenOrderRef.current && kitchenOrderRef.current.scrollToIndex({
          //         index: index,
          //     });
          // }
          // else {
          //     kitchenOrderRef && kitchenOrderRef.current && kitchenOrderRef.current.scrollToItem({
          //         item: index,
          //     });
          // }

          // var toExpand = undefined;
          // if (userOrdersExpandedDict[item.uniqueId] === undefined) {
          //     toExpand = true;
          // }

          // OutletStore.update(s => {
          //     s.userOrdersExpandedDict = {
          //         ...userOrdersExpandedDict,
          //         [item.uniqueId]: toExpand,
          //     };
          // });
          // console.log(item);
          setSelectedUserOrderUndo(item);
          setShowUndoUserOrderModal(true);
          setUserOrderUndoRemarks(item.remarks);
        }}>
        <View
          style={{
            backgroundColor:
              // (moment().valueOf() - item.estimatedPreparedDate) / (1000 * 60) >=
              //   20
              //   ? Colors.tabRed
              //   : (moment().valueOf() - item.estimatedPreparedDate) /
              //     (1000 * 60) <
              //     20 &&
              //     (moment().valueOf() - item.estimatedPreparedDate) /
              //     (1000 * 60) >=
              //     15
              //     ? Colors.tabYellow
              //     : Colors.tabGrey,
              (moment().valueOf() - item.createdAt) / (1000 * 60) >=
                20
                ? Colors.tabRed
                : (moment().valueOf() - item.createdAt) /
                  (1000 * 60) <
                  20 &&
                  (moment().valueOf() - item.createdAt) /
                  (1000 * 60) >=
                  15
                  ? Colors.tabYellow
                  : Colors.tabGrey,
            padding: 3,
            marginHorizontal: 10,
            marginVertical: windowWidth <= 1133 ? 8 : 13,
            marginLeft: index === 0 ? 30 : 0,
            marginTop: windowWidth < 670 ? -15 : 0,
            // width: Platform.OS == 'ios' ? windowWidth * 0.1 : windowWidth * 0.08,
            minWidth: switchMerchant ? 60 : 90,
            height: windowHeight * 0.06,
            borderRadius: 5,
            borderWidth: 1,
            borderColor: '#bbb',
            // placeholderTextColor={Platform.select({ios: '#a9a9a9'})}
            //borderColor: ((moment().valueOf() - item.estimatedPreparedDate) / (1000 * 60)) >= 20 ? Colors.tabRed : (((moment().valueOf() - item.estimatedPreparedDate) / (1000 * 60)) < 20 && ((moment().valueOf() - item.estimatedPreparedDate) / (1000 * 60)) >= 15) ? Colors.tabYellow : Colors.tabGrey,
            flexDirection: 'row',
          }}>
          {/* {item.isPrioritizedOrder === true ?
                        <View style={{ width: 80, backgroundColor: "#e08300", height: 18, position: "absolute", zIndex: 6000, top: -10, right: 8, borderRadius: 5 }}>
                            <View style={{ flexDirection: "row", justifyContent: 'center', width: "80%" }}>
                                <MaterialCommunityIcons
                                    name="message-alert-outline"
                                    size={17}
                                    //color={Colors.whiteColor}
                                    borderColor={((moment().valueOf() - item.estimatedPreparedDate) / (1000 * 60)) >= 20 ? Colors.tabRed : (((moment().valueOf() - item.estimatedPreparedDate) / (1000 * 60)) < 20 && ((moment().valueOf() - item.estimatedPreparedDate) / (1000 * 60)) >= 15) ? Colors.tabYellow : Colors.tabGrey}
                                    style={{ marginLeft: 15, marginRight: 3 }}
                                />
                                <Text style={{ color: Colors.whiteColor, fontSize: 8, alignSelf: "center" }}>Prioritize Order</Text>
                            </View>
                        </View>
                        : null} */}
          {item.orderStatus === USER_ORDER_STATUS.ORDER_DELIVERED ||
            item.orderStatus === USER_ORDER_STATUS.ORDER_PREPARED ? (
            <View
              style={{
                width: switchMerchant ? 57 : 80,
                backgroundColor: Colors.primaryColor,
                height: windowWidth <= 1133 ? 18 : 20,
                position: 'absolute',
                zIndex: 6000,
                top: switchMerchant
                  ? windowHeight * -0.03
                  : windowWidth * -0.01,
                right: switchMerchant ? 0 : 8,
                borderRadius: 5,
              }}>
              <View
                style={{
                  flexDirection: 'row',
                  justifyContent: 'center',
                  width: '80%',
                  alignItems: 'center',
                  top: 2.5,
                }}>
                <Entypo
                  name="check"
                  size={switchMerchant ? 10 : 13}
                  color={Colors.whiteColor}
                  // borderColor={
                  //   (moment().valueOf() - item.estimatedPreparedDate) /
                  //     (1000 * 60) >=
                  //     20
                  //     ? Colors.tabRed
                  //     : (moment().valueOf() - item.estimatedPreparedDate) /
                  //       (1000 * 60) <
                  //       20 &&
                  //       (moment().valueOf() - item.estimatedPreparedDate) /
                  //       (1000 * 60) >=
                  //       15
                  //       ? Colors.tabYellow
                  //       : Colors.tabGrey
                  // }
                  borderColor={
                    (moment().valueOf() - item.createdAt) /
                      (1000 * 60) >=
                      20
                      ? Colors.tabRed
                      : (moment().valueOf() - item.createdAt) /
                        (1000 * 60) <
                        20 &&
                        (moment().valueOf() - item.createdAt) /
                        (1000 * 60) >=
                        15
                        ? Colors.tabYellow
                        : Colors.tabGrey
                  }
                  style={
                    switchMerchant
                      ? {
                        marginLeft: windowWidth * 0.015,
                        marginRight: windowWidth * 0.005,
                      }
                      : {
                        marginLeft: 15,
                        marginRight: 3,
                      }
                  }
                />
                <Text
                  style={{
                    color: Colors.whiteColor,
                    fontSize: switchMerchant ? 8 : 10,
                    alignSelf: 'center',
                  }}>
                  Served
                </Text>
              </View>
            </View>
          ) : null}
          {item.orderStatus === USER_ORDER_STATUS.ORDER_REJECTED_BY_MERCHANT ? (
            <View
              style={{
                width: switchMerchant ? 57 : 80,
                backgroundColor: '#e08300',
                height: windowWidth <= 1133 ? 18 : 20,
                position: 'absolute',
                zIndex: 6000,
                top: switchMerchant
                  ? windowHeight * -0.03
                  : windowWidth * -0.01,
                right: switchMerchant ? 0 : 8,
                borderRadius: 5,
              }}>
              <View
                style={{
                  flexDirection: 'row',
                  justifyContent: 'center',
                  width: '80%',
                  alignItems: 'center',
                  top: switchMerchant ? 2 : 1,
                }}>
                <Entypo
                  name="cross"
                  size={switchMerchant ? 12 : 17}
                  color={Colors.whiteColor}
                  // borderColor={
                  //   (moment().valueOf() - item.estimatedPreparedDate) /
                  //     (1000 * 60) >=
                  //     20
                  //     ? Colors.tabRed
                  //     : (moment().valueOf() - item.estimatedPreparedDate) /
                  //       (1000 * 60) <
                  //       20 &&
                  //       (moment().valueOf() - item.estimatedPreparedDate) /
                  //       (1000 * 60) >=
                  //       15
                  //       ? Colors.tabYellow
                  //       : Colors.tabGrey
                  // }
                  borderColor={
                    (moment().valueOf() - item.createdAt) /
                      (1000 * 60) >=
                      20
                      ? Colors.tabRed
                      : (moment().valueOf() - item.createdAt) /
                        (1000 * 60) <
                        20 &&
                        (moment().valueOf() - item.createdAt) /
                        (1000 * 60) >=
                        15
                        ? Colors.tabYellow
                        : Colors.tabGrey
                  }
                  style={
                    switchMerchant
                      ? {
                        marginLeft: windowWidth * 0.015,
                        marginRight: windowWidth * 0.005,
                      }
                      : {
                        marginLeft: 15,
                        marginRight: 3,
                      }
                  }
                />
                <Text
                  style={
                    switchMerchant
                      ? {
                        color: Colors.whiteColor,
                        fontSize: 8,
                        alignSelf: 'center',
                        bottom: 0.5,
                        right: 1.5,
                      }
                      : {
                        color: Colors.whiteColor,
                        fontSize: 10,
                        alignSelf: 'center',
                        bottom: 0.5,
                      }
                  }>
                  Cancelled
                </Text>
              </View>
            </View>
          ) : null}
          <View style={{ justifyContent: 'center' }}>
            {/* <Icon name={item.customTable === "TAKE AWAY" ? "shopping-outline" : "silverware-variant"} size={30} color={((moment().valueOf() - item.estimatedPreparedDate) / (1000 * 60)) >= 20 ? Colors.tabRed : (((moment().valueOf() - item.estimatedPreparedDate) / (1000 * 60)) < 20 && ((moment().valueOf() - item.estimatedPreparedDate) / (1000 * 60)) >= 15) ? Colors.tabYellow : Colors.tabGrey} /> */}
            {item.courierId ? (
              <Image
                style={[
                  {
                    width: 25,
                    height: 25,
                  },
                  switchMerchant
                    ? {
                      width: 15,
                      height: 15,
                    }
                    : {},
                ]}
                source={COURIER_INFO_DICT[item.courierCode].img}
              />
            ) : item.orderType === ORDER_TYPE.DINEIN ? (
              <Image
                style={[
                  {
                    width: 25,
                    height: 25,
                    marginLeft: 0,
                  },
                  !isTablet()
                    ? {
                      width: 15,
                      height: 15,
                    }
                    : {},
                ]}
                resizeMode="contain"
                source={require('../assets/image/dineinWhite1.png')}
              />
            ) : (
              <Image
                style={[
                  {
                    width: 25,
                    height: 25,
                    //marginLeft: 0,
                    //left: 30,
                  },
                  switchMerchant
                    ? {
                      width: 15,
                      height: 15,
                      // top: windowHeight * 0.009,
                    }
                    : {},
                ]}
                resizeMode="contain"
                source={require('../assets/image/takeawayWhite.png')}
              />
            )}

            {/* <Image
              style={{
                width: switchMerchant ? 15 : 25,
                height: switchMerchant ? 15 : 25,
              }}
              resizeMode="contain"
              source={
                item.orderType !== ORDER_TYPE.DINEIN &&
                (moment().valueOf() - item.estimatedPreparedDate) /
                  (1000 * 60) >=
                  20
                  ? require('../assets/image/takeawayWhite1.png')
                  : item.orderType !== ORDER_TYPE.DINEIN &&
                    (moment().valueOf() - item.estimatedPreparedDate) /
                      (1000 * 60) <
                      20 &&
                    (moment().valueOf() - item.estimatedPreparedDate) /
                      (1000 * 60) >=
                      15
                  ? require('../assets/image/takeawayWhite1.png')
                  : item.orderType !== ORDER_TYPE.DINEIN &&
                    (moment().valueOf() - item.estimatedPreparedDate) /
                      (1000 * 60) <
                      15
                  ? require('../assets/image/takeawayWhite1.png')
                  : item.orderType !== ORDER_TYPE.DINEIN &&
                    (moment().valueOf() - item.estimatedPreparedDate) /
                      (1000 * 60) >=
                      20
                  ? require('../assets/image/dineinWhite1.png')
                  : item.orderType !== ORDER_TYPE.DINEIN &&
                    (moment().valueOf() - item.estimatedPreparedDate) /
                      (1000 * 60) <
                      20 &&
                    (moment().valueOf() - item.estimatedPreparedDate) /
                      (1000 * 60) >=
                      15
                  ? require('../assets/image/dineinWhite1.png')
                  : require('../assets/image/dineinWhite1.png')
              }
            /> */}
          </View>
          <View
            style={{
              borderWidth: StyleSheet.hairlineWidth,
              borderColor: '#ebebeb',
              marginLeft: 5,
            }} />
          <View
            style={{
              justifyContent: 'center',
              //justifyContent: 'center',
              alignItems: 'center',
              backgroundColor: 'white',
              // borderWidth: 1,
            }}>
            <Text
              style={[
                {
                  fontFamily: 'NunitoSans-Bold',
                  fontSize: windowWidth <= 1133 ? 12 : windowWidth < 670 ? 14 : 17,
                  lineHeight: windowWidth <= 1133 ? 19 : 26,
                  fontWeight: '500',
                  paddingLeft: 3,
                  // color:
                  //   (moment().valueOf() - item.estimatedPreparedDate) /
                  //     (1000 * 60) >=
                  //     20
                  //     ? Colors.blackColor
                  //     : (moment().valueOf() - item.estimatedPreparedDate) /
                  //       (1000 * 60) <
                  //       20 &&
                  //       (moment().valueOf() - item.estimatedPreparedDate) /
                  //       (1000 * 60) >=
                  //       15
                  //       ? Colors.blackColor
                  //       : Colors.blackColor,
                  color:
                    (moment().valueOf() - item.createdAt) /
                      (1000 * 60) >=
                      20
                      ? Colors.blackColor
                      : (moment().valueOf() - item.createdAt) /
                        (1000 * 60) <
                        20 &&
                        (moment().valueOf() - item.createdAt) /
                        (1000 * 60) >=
                        15
                        ? Colors.blackColor
                        : Colors.blackColor,
                  //marginTop: windowWidth < 670 ? 5 : 0,
                },
                switchMerchant
                  ? {
                    fontSize: 10,
                    // borderWidth: 1,
                    // alignSelf: 'flex-start',
                    position: 'relative',
                    // top: '50%',
                    lineHeight:
                      windowWidth <= 1133 ? 16 : 16,
                  }
                  : {},
              ]}>
              {/* {item.table != null ? item.table.code : "#T" + item.id} */}
              {/* {outletTablesDict[item.tableId] ? outletTablesDict[item.tableId].code : ('#T' + item.orderId)} */}
              {outletTablesDict[item.tableId]
                ? `${outletTablesDict[item.tableId].code} `
                : ''}
              {item.orderType !== ORDER_TYPE.DINEIN
                ? item.orderType === ORDER_TYPE.DELIVERY
                  ? '#D'
                  : '#T'
                : '#'}
              {item.orderId ? item.orderId : ''}
            </Text>
            {switchMerchant ? (
              <></>
            ) : (
              <Text
                style={[
                  {
                    fontFamily: 'NunitoSans-Bold',
                    color:
                      // (moment().valueOf() - item.estimatedPreparedDate) /
                      //   (1000 * 60) >=
                      //   20
                      //   ? Colors.blackColor
                      //   : (moment().valueOf() - item.estimatedPreparedDate) /
                      //     (1000 * 60) <
                      //     20 &&
                      //     (moment().valueOf() - item.estimatedPreparedDate) /
                      //     (1000 * 60) >=
                      //     15
                      //     ? Colors.blackColor
                      //     : Colors.blackColor,
                      (moment().valueOf() - item.createdAt) /
                        (1000 * 60) >=
                        20
                        ? Colors.blackColor
                        : (moment().valueOf() - item.createdAt) /
                          (1000 * 60) <
                          20 &&
                          (moment().valueOf() - item.createdAt) /
                          (1000 * 60) >=
                          15
                          ? Colors.blackColor
                          : Colors.blackColor,
                    lineHeight:
                      windowWidth <= 1133 ? 10 : 14,
                    fontSize: 10,
                    top: switchMerchant ? -2 : 0,
                  },
                  switchMerchant
                    ? {
                      position: 'relative',
                      bottom: 1,
                    }
                    : {
                      bottom: windowWidth < 670 ? 10 : 2,
                      textAlignVertical: 'center',
                      // borderWidth: 1
                    },
                ]}>
                {/* {(moment().valueOf() - item.estimatedPreparedDate) /
                  (1000 * 60) <
                  0
                  ? 0
                  : (moment().valueOf() - item.estimatedPreparedDate) /
                    (1000 * 60) >
                    120
                    ? 'OverTime'
                    : `${(
                      (moment().valueOf() - item.estimatedPreparedDate) /
                      (1000 * 60)
                    ).toFixed(0)}mins`} */}
                {(moment().valueOf() - item.createdAt) /
                  (1000 * 60) <
                  0
                  ? 0
                  : (moment().valueOf() - item.createdAt) /
                    (1000 * 60) >
                    120
                    ? 'OverTime'
                    : `${(
                      (moment().valueOf() - item.createdAt) /
                      (1000 * 60)
                    ).toFixed(0)}mins`}
              </Text>
            )}
          </View>
        </View>
      </TouchableOpacity>
    </View>
  );

  const renderFooterExtend = ({ item, index }) => (
    <View style={{ alignSelf: 'flex-end' }}>
      {footerOpen.includes(item.uniqueId) == true ? (
        <View style={{ position: 'relative' }}>
          <KitchenOrder
            viewOptions
            // checkOrderItem={checkOrderItem.bind(this)}
            checkSelectedItem={checkSelectedItem}
            completeOrder={completeOrder.bind(this)}
            setScroll={(scroll) => {
              setState({ scroll });
            }}
            search={search}
            scroll={scroll}
            order={item}
            selectedCat={selectedCat}
            onTimesUp={() => {
              completeOrder(item.uniqueId, () => { }, item);
            }}
            refreshRate={refreshRate}
          />
        </View>
      ) : null}
      <TouchableOpacity
        onPress={() => {
          // openFooter(item.id);
          // setState({ item });
          // item.expand == true ? item.expand = false : (item.expand == null || item.expand == false) ? item.expand = true : null

          // openFooter(item.uniqueId);
          // setItem(item);

          PageStore.update((s) => {
            s.kitchenScrollItem = item;
          });

          if (viewOptions) {
            kitchenOrderRef &&
              kitchenOrderRef.current &&
              kitchenOrderRef.current.scrollToIndex({
                index,
              });
          } else {
            kitchenOrderRef &&
              kitchenOrderRef.current &&
              kitchenOrderRef.current.scrollToItem({
                item: index,
              });
          }

          var toExpand = undefined;
          if (userOrdersExpandedDict[item.uniqueId] === undefined) {
            //toExpand = true;
          }

          OutletStore.update((s) => {
            s.userOrdersExpandedDict = {
              ...userOrdersExpandedDict,
              //[item.uniqueId]: toExpand,
            };
          });
        }}>
        {/*<View style={{ backgroundColor: Colors.whiteColor, padding: 3, marginHorizontal: 10, marginVertical: 13, marginLeft: index === 0 ? 30 : 0, width: Platform.OS == 'ios' ? windowWidth * 0.1 : windowWidth * 0.07, height: windowHeight * 0.06, borderRadius: 5, borderWidth: 1, borderColor: ((moment().valueOf() - item.estimatedPreparedDate) / (1000 * 60)) >= 20 ? Colors.tabRed : (((moment().valueOf() - item.estimatedPreparedDate) / (1000 * 60)) < 20 && ((moment().valueOf() - item.estimatedPreparedDate) / (1000 * 60)) >= 15) ? Colors.tabYellow : Colors.tabGrey, flexDirection: "row" }}>
                     {item.prioritizeOrder === true ?
                        <View style={{ width: 80, backgroundColor: "#e08300", height: 18, position: "absolute", zIndex: 6000, top: -10, right: 8, borderRadius: 5 }}>
                            <View style={{ flexDirection: "row", justifyContent: 'center', width: "80%" }}>
                                <MaterialCommunityIcons
                                    name="message-alert-outline"
                                    size={15}
                                    color={Colors.whiteColor}
                                    style={{ marginLeft: 15, marginRight: 3 }}
                                />
                                <Text style={{ color: Colors.whiteColor, fontSize: 8, alignSelf: "center" }}>Prioritize Order</Text>
                            </View>
                        </View>
                        : null}
                    <View style={{ justifyContent: "center" }}> */}
        {/* <Icon name={item.customTable === "TAKE AWAY" ? "shopping-outline" : "silverware-variant"} size={30} color={((moment().valueOf() - item.estimatedPreparedDate) / (1000 * 60)) >= 20 ? Colors.tabRed : (((moment().valueOf() - item.estimatedPreparedDate) / (1000 * 60)) < 20 && ((moment().valueOf() - item.estimatedPreparedDate) / (1000 * 60)) >= 15) ? Colors.tabYellow : Colors.tabGrey} /> */}
        {/* <Image
                            style={{
                                width: 25,
                                height: 25,
                            }}
                            resizeMode="contain"
                            source={
                                item.customTable === "TAKE AWAY" && ((moment().valueOf() - item.estimatedPreparedDate) / (1000 * 60)) >= 20 ? require('../assets/image/takeawayRed.png')
                                    : item.customTable === "TAKE AWAY" && (((moment().valueOf() - item.estimatedPreparedDate) / (1000 * 60)) < 20 && ((moment().valueOf() - item.estimatedPreparedDate) / (1000 * 60)) >= 15) ? require('../assets/image/takeawayYellow.png')
                                        : item.customTable === "TAKE AWAY" && (((moment().valueOf() - item.estimatedPreparedDate) / (1000 * 60)) < 15) ? require('../assets/image/TakeawayBlack.png')
                                            : item.customTable !== "TAKE AWAY" && ((moment().valueOf() - item.estimatedPreparedDate) / (1000 * 60)) >= 20 ? require('../assets/image/dineinRed1.png')
                                                : item.customTable !== "TAKE AWAY" && (((moment().valueOf() - item.estimatedPreparedDate) / (1000 * 60)) < 20 && ((moment().valueOf() - item.estimatedPreparedDate) / (1000 * 60)) >= 15) ? require('../assets/image/dineinYellow1.png')
                                                    : require('../assets/image/dineinGrey.png')
                            }
                        />
                    </View>
                    <View style={{ borderWidth: StyleSheet.hairlineWidth, borderColor: "#ebebeb", marginLeft: 5 }}></View>
                    <View style={{ justifyContent: "center", justifyContent: "center", alignItems: "center" }}>
                        <Text style={{ fontFamily: 'NunitoSans-Bold', fontSize: 17, lineHeight: 26, fontWeight: "500" }}>{item.table != null ? item.table.code : "#T" + item.id}</Text>
                        <Text style={{ fontFamily: 'NunitoSans-Bold', color: Colors.tabGrey, lineHeight: 14, fontSize: 10 }}>{((moment().valueOf() - item.estimatedPreparedDate) / (1000 * 60))}mins</Text>
                    </View>
                </View> */}
      </TouchableOpacity>
    </View>
  );

  const renderSummary = ({ item, index }) => {
    return (
      <View>
        <View
          style={{
            // height: 70,
            flexDirection: 'row',
            alignItems: 'center',
            borderTopWidth: index > 0 ? StyleSheet.hairlineWidth : null,
            padding: 15,
            backgroundColor: 'white',


          }}>
          {/* disabled first */}
          {/* <View style={{ flex: 1 }}>
                    <TouchableOpacity onPress={() => {
                        checkSameItems(item.name), setState({ isSelectedCheck: true }),
                            checkSelectedItem != undefined && checkSelectedItem.length == 0 ? setState({ checkSelectedItem: undefined }) : null,
                            // console.log("checkSelectedItem", checkSelectedItem)
                    }}>
                        <View style={{ borderRadius: 5, backgroundColor: isSameItemCheck(item.name) ? Colors.primaryColor : Colors.fieldtBgColor, marginRight: 20, alignItems: 'center', justifyContent: 'center' }}>
                            <Icon name="check" size={20} color={isSameItemCheck(item.name) ? Colors.whiteColor : Colors.fieldtTxtColor} style={{ margin: 2 }} />
                        </View>
                    </TouchableOpacity>
                </View> */}
          <View style={{ flex: 3 }}>
            <Text
              style={[
                {
                  fontFamily: 'NunitoSans-Bold',
                  color: Colors.blackColor,
                  opacity: 0.8,
                },
                switchMerchant ? { fontSize: 10 } : {},
              ]}>
              {item.name}{item.priceType === PRODUCT_PRICE_TYPE.UNIT ? ` (${UNIT_TYPE_SHORT[item.unitType]})` : ''}
            </Text>
          </View>
          <View style={{ flex: 1, alignItems: 'center' }}>
            <Text
              style={[
                {
                  fontFamily: 'NunitoSans-Bold',
                  color: Colors.blackColor,
                  opacity: 0.8,
                },
                switchMerchant ? { fontSize: 10 } : {},
              ]}>
              x{item.quantityAccum}
            </Text>
          </View>
          <View style={{}}>
            <CheckBox
              style={{
                ...(Platform.OS === 'ios' && {
                  width: switchMerchant ? 10 : 16,
                  height: switchMerchant ? 10 : 16,
                }),
              }}
              value={
                summaryCheckDict[item.key] !== false &&
                summaryCheckDict[item.key] !== undefined
              }
              onValueChange={(value) => {
                if (summaryCheckDict[item.key]) {
                  uncheckOrderItemSummary(item);
                } else {
                  checkOrderItemSummary(item);
                }
              }}
            />
          </View>
        </View>

        <View>
          {item.addOnParsedListAccum.map((addOn, addOnIndex) => {
            return (
              <View
                style={{
                  // height: 70,
                  flexDirection: 'row',
                  alignItems: 'center',
                  // borderBottomWidth: item.length === index + 1 ? null : StyleSheet.hairlineWidth,
                  padding: 15,
                  // paddingTop: 0,
                  // paddingBottom: 0,
                  paddingVertical: 0,
                  //marginBottom: -10,
                  ...(addOnIndex === 0 && {
                    marginTop: -10,
                  }),
                  ...(addOnIndex === item.addOnParsedListAccum.length - 1 && {
                    paddingBottom: 20,
                  }),
                  backgroundColor: 'white',
                }}>
                {/* disabled first */}
                {/* <View style={{ flex: 1 }}>
                                    <TouchableOpacity onPress={() => {
                                        checkSameItems(item.name), setState({ isSelectedCheck: true }),
                                            checkSelectedItem != undefined && checkSelectedItem.length == 0 ? setState({ checkSelectedItem: undefined }) : null,
                                            // console.log("checkSelectedItem", checkSelectedItem)
                                    }}>
                                        <View style={{ borderRadius: 5, backgroundColor: isSameItemCheck(item.name) ? Colors.primaryColor : Colors.fieldtBgColor, marginRight: 20, alignItems: 'center', justifyContent: 'center' }}>
                                            <Icon name="check" size={20} color={isSameItemCheck(item.name) ? Colors.whiteColor : Colors.fieldtTxtColor} style={{ margin: 2 }} />
                                        </View>
                                    </TouchableOpacity>
                                </View> */}
                <View style={{ flex: 3 }}>
                  <Text
                    style={[
                      {
                        fontFamily: 'NunitoSans-Regular',
                        color: Colors.fieldtTxtColor,
                      },
                      switchMerchant ? { fontSize: 8 } : {},
                    ]}>
                    {addOn.name}
                  </Text>
                </View>
                <View
                  style={{
                    flex: 1,
                    alignItems: 'center',
                    opacity: addOn.quantity > 0 ? 1 : 0,
                    left: Platform.OS == 'ios' ? 15 : 0,
                  }}>
                  <Text
                    style={[
                      {
                        fontFamily: 'NunitoSans-Regular',
                        color: Colors.fieldtTxtColor,
                      },
                      switchMerchant ? { fontSize: 8 } : {},
                    ]}>
                    x{addOn.quantity}
                  </Text>
                </View>
                <View
                  style={{
                    opacity: 0,
                  }}>
                  <CheckBox value />
                </View>
              </View>
            );
          })}
        </View>
      </View>
    );
  };

  const renderSummaryDelivered = ({ item, index }) => {
    return (
      <View>
        <View
          style={{
            // height: 70,
            flexDirection: 'row',
            alignItems: 'center',
            //borderTopWidth: index > 0 ? StyleSheet.hairlineWidth : null,
            borderTopWidth: StyleSheet.hairlineWidth,
            padding: 15,
            backgroundColor: 'white',
          }}>
          {/* disabled first */}
          {/* <View style={{ flex: 1 }}>
                    <TouchableOpacity onPress={() => {
                        checkSameItems(item.name), setState({ isSelectedCheck: true }),
                            checkSelectedItem != undefined && checkSelectedItem.length == 0 ? setState({ checkSelectedItem: undefined }) : null,
                            // console.log("checkSelectedItem", checkSelectedItem)
                    }}>
                        <View style={{ borderRadius: 5, backgroundColor: isSameItemCheck(item.name) ? Colors.primaryColor : Colors.fieldtBgColor, marginRight: 20, alignItems: 'center', justifyContent: 'center' }}>
                            <Icon name="check" size={20} color={isSameItemCheck(item.name) ? Colors.whiteColor : Colors.fieldtTxtColor} style={{ margin: 2 }} />
                        </View>
                    </TouchableOpacity>
                </View> */}
          <View style={{ flex: 3 }}>
            <Text
              style={[
                {
                  fontFamily: 'NunitoSans-Bold',
                  color: Colors.primaryColor,
                  opacity: 0.8,
                  textDecorationLine: 'line-through',
                },
                switchMerchant ? { fontSize: 10 } : {},
              ]}>
              {item.name}{item.priceType === PRODUCT_PRICE_TYPE.UNIT ? ` (${UNIT_TYPE_SHORT[item.unitType]})` : ''}
            </Text>
          </View>
          <View style={{ flex: 1, alignItems: 'center' }}>
            <Text
              style={[
                {
                  fontFamily: 'NunitoSans-Bold',
                  color: Colors.primaryColor,
                  opacity: 0.8,
                  textDecorationLine: 'line-through',
                },
                switchMerchant ? { fontSize: 10 } : {},
              ]}>
              x{item.quantityAccum}
            </Text>
          </View>
          <View>
            <CheckBox
              style={{
                ...(Platform.OS === 'ios' && {
                  width: switchMerchant ? 10 : 16,
                  height: switchMerchant ? 10 : 16,
                }),
              }}
              value={
                summaryDeliveredCheckDict[item.key] !== false &&
                summaryDeliveredCheckDict[item.key] !== undefined
              }
              onValueChange={(value) => {
                if (summaryDeliveredCheckDict[item.key]) {
                  uncheckOrderItemDeliveredSummary(item);
                } else {
                  checkOrderItemDeliveredSummary(item);
                }
              }}
            />
          </View>
        </View>

        <View>
          {item.addOnParsedListAccum.map((addOn, addOnIndex) => {
            return (
              <View
                style={{
                  // height: 70,
                  flexDirection: 'row',
                  alignItems: 'center',
                  // borderBottomWidth: item.length === index + 1 ? null : StyleSheet.hairlineWidth,
                  padding: 15,
                  // paddingTop: 0,
                  // paddingBottom: 0,
                  paddingVertical: 0,
                  //marginBottom: -10,
                  ...(addOnIndex === 0 && {
                    marginTop: -10,
                  }),
                  ...(addOnIndex === item.addOnParsedListAccum.length - 1 && {
                    paddingBottom: 20,
                  }),
                  backgroundColor: 'white',
                }}>
                {/* disabled first */}
                {/* <View style={{ flex: 1 }}>
                                    <TouchableOpacity onPress={() => {
                                        checkSameItems(item.name), setState({ isSelectedCheck: true }),
                                            checkSelectedItem != undefined && checkSelectedItem.length == 0 ? setState({ checkSelectedItem: undefined }) : null,
                                            // console.log("checkSelectedItem", checkSelectedItem)
                                    }}>
                                        <View style={{ borderRadius: 5, backgroundColor: isSameItemCheck(item.name) ? Colors.primaryColor : Colors.fieldtBgColor, marginRight: 20, alignItems: 'center', justifyContent: 'center' }}>
                                            <Icon name="check" size={20} color={isSameItemCheck(item.name) ? Colors.whiteColor : Colors.fieldtTxtColor} style={{ margin: 2 }} />
                                        </View>
                                    </TouchableOpacity>
                                </View> */}
                <View
                  style={{
                    flex: 3,
                    //height: (windowHeight)*0.07
                  }}>
                  <Text
                    style={[
                      {
                        fontFamily: 'NunitoSans-Regular',
                        color: Colors.primaryColor,
                        textDecorationLine: 'line-through',
                      },
                      switchMerchant ? { fontSize: 8 } : {},
                    ]}>
                    {addOn.name}
                  </Text>
                </View>
                <View
                  style={{
                    flex: 1,
                    alignItems: 'center',
                    opacity: addOn.quantity > 0 ? 1 : 0,
                    left: Platform.OS == 'ios' ? 15 : 0,
                    //height: (windowHeight)*0.07
                  }}>
                  <Text
                    style={[
                      {
                        fontFamily: 'NunitoSans-Regular',
                        color: Colors.fieldtTxtColor,
                      },
                      switchMerchant ? { fontSize: 8 } : {},
                    ]}>
                    x{addOn.quantity}
                  </Text>
                </View>
                <View
                  style={{
                    opacity: 0,
                  }}>
                  <CheckBox value />
                </View>
              </View>
            );
          })}
        </View>
      </View>
    );
  };

  const renderSummaryCancelled = ({ item, index }) => {
    return (
      <View>
        <View
          style={{
            // height: 70,
            flexDirection: 'row',
            alignItems: 'center',
            //borderTopWidth: index > 0 ? StyleSheet.hairlineWidth : null,
            borderTopWidth: StyleSheet.hairlineWidth,
            padding: 15,
            backgroundColor: 'white',
          }}>
          {/* disabled first */}
          {/* <View style={{ flex: 1 }}>
                    <TouchableOpacity onPress={() => {
                        checkSameItems(item.name), setState({ isSelectedCheck: true }),
                            checkSelectedItem != undefined && checkSelectedItem.length == 0 ? setState({ checkSelectedItem: undefined }) : null,
                            // console.log("checkSelectedItem", checkSelectedItem)
                    }}>
                        <View style={{ borderRadius: 5, backgroundColor: isSameItemCheck(item.name) ? Colors.primaryColor : Colors.fieldtBgColor, marginRight: 20, alignItems: 'center', justifyContent: 'center' }}>
                            <Icon name="check" size={20} color={isSameItemCheck(item.name) ? Colors.whiteColor : Colors.fieldtTxtColor} style={{ margin: 2 }} />
                        </View>
                    </TouchableOpacity>
                </View> */}
          <View style={{ flex: 3 }}>
            <Text
              style={[
                {
                  fontFamily: 'NunitoSans-Bold',
                  color: Colors.tabRed,
                  opacity: 0.8,
                  textDecorationLine: 'line-through',
                },
                switchMerchant ? { fontSize: 10 } : {},
              ]}>
              {item.name}{item.priceType === PRODUCT_PRICE_TYPE.UNIT ? ` (${UNIT_TYPE_SHORT[item.unitType]})` : ''}
            </Text>
          </View>
          <View style={{ flex: 1, alignItems: 'center' }}>
            <Text
              style={[
                {
                  fontFamily: 'NunitoSans-Bold',
                  color: Colors.blackColor,
                  opacity: 0.8,
                },
                switchMerchant ? { fontSize: 10 } : {},
              ]}>
              x{item.quantityAccum}
            </Text>
          </View>
          <View>
            <CheckBox
              style={{
                ...(Platform.OS === 'ios' && {
                  width: switchMerchant ? 10 : 16,
                  height: switchMerchant ? 10 : 16,
                }),
              }}
              value={
                summaryCancelledCheckDict[item.key] !== false &&
                summaryCancelledCheckDict[item.key] !== undefined
              }
              onValueChange={(value) => {
                if (summaryCancelledCheckDict[item.key]) {
                  uncheckOrderItemCancelledSummary(item);
                } else {
                  checkOrderItemCancelledSummary(item);
                }
              }}
            />
          </View>
        </View>

        <View>
          {item.addOnParsedListAccum.map((addOn, addOnIndex) => {
            return (
              <View
                style={{
                  // height: 70,
                  flexDirection: 'row',
                  alignItems: 'center',
                  // borderBottomWidth: item.length === index + 1 ? null : StyleSheet.hairlineWidth,
                  padding: 15,
                  // paddingTop: 0,
                  // paddingBottom: 0,
                  paddingVertical: 0,
                  marginBottom: -10,
                  ...(addOnIndex === 0 && {
                    marginTop: -10,
                  }),
                  ...(addOnIndex === item.addOnParsedListAccum.length - 1 && {
                    paddingBottom: 20,
                  }),
                  backgroundColor: 'white',
                }}>
                {/* disabled first */}
                {/* <View style={{ flex: 1 }}>
                                    <TouchableOpacity onPress={() => {
                                        checkSameItems(item.name), setState({ isSelectedCheck: true }),
                                            checkSelectedItem != undefined && checkSelectedItem.length == 0 ? setState({ checkSelectedItem: undefined }) : null,
                                            // console.log("checkSelectedItem", checkSelectedItem)
                                    }}>
                                        <View style={{ borderRadius: 5, backgroundColor: isSameItemCheck(item.name) ? Colors.primaryColor : Colors.fieldtBgColor, marginRight: 20, alignItems: 'center', justifyContent: 'center' }}>
                                            <Icon name="check" size={20} color={isSameItemCheck(item.name) ? Colors.whiteColor : Colors.fieldtTxtColor} style={{ margin: 2 }} />
                                        </View>
                                    </TouchableOpacity>
                                </View> */}
                <View
                  style={{
                    flex: 3,
                    height: windowHeight * 0.07,
                  }}>
                  <Text
                    style={[
                      {
                        fontFamily: 'NunitoSans-Regular',
                        color: Colors.tabRed,
                        textDecorationLine: 'line-through',
                      },
                      switchMerchant ? { fontSize: 8 } : {},
                    ]}>
                    {addOn.name}
                  </Text>
                </View>
                <View
                  style={{
                    flex: 1,
                    alignItems: 'center',
                    opacity: addOn.quantity > 0 ? 1 : 0,
                    left: Platform.OS == 'ios' ? 15 : 0,
                    height: windowHeight * 0.07,
                  }}>
                  <Text
                    style={[
                      {
                        fontFamily: 'NunitoSans-Regular',
                        color: Colors.fieldtTxtColor,
                      },
                      switchMerchant ? { fontSize: 8 } : {},
                    ]}>
                    x{addOn.quantity}
                  </Text>
                </View>
                <View
                  style={{
                    opacity: 0,
                  }}>
                  <CheckBox value />
                </View>
              </View>
            );
          })}
        </View>
      </View>
    );
  };

  /////////////////////////////////////////////////////////

  // reject/undo functions

  const cancelUserOrder = () => {
    var orderItemList = Object.entries(summaryCheckDict)
      .map(([key, value]) => value)
      .filter(
        (summaryItem) => summaryItem !== false && summaryItem !== undefined,
      )
      .reduce(
        (cartItemListAccum, summaryItem) =>
          cartItemListAccum.concat(summaryItem.cartItemList),
        [],
      );

    var orderIdList = [...new Set(orderItemList.map((o) => o.userOrderId))];

    var body = {
      orderItemList,
      orderIdList,

      userOrdersActive,

      outlet: currOutlet,

      tableIdList: [],
    };

    // console.log(body);

    CommonStore.update((s) => {
      s.isLoading = true;
    });

    // ApiClient.POST(API.cancelUserOrderItemSummary, body)
    APILocal.cancelUserOrderItemSummary({ body, uid: firebaseUid })
      .then(async (result) => {
        if (result && result.status === 'success') {
          //////////////////////////////////////////////////////////

          try {
            for (let oiIndex = 0; oiIndex < result.orderDataList.length; oiIndex++) {
              // let order = userOrdersRaw.find(orderFind => orderFind.uniqueId === orderIdList[oiIndex]);
              let order = result.orderDataList[oiIndex];

              if (order) {
                logToFile('kitchen - printUserOrder - KITCHEN_DOCKET cancelled');

                if (global.currOutlet.cancelRejectPrintOs) {
                  await printUserOrder(
                    {
                      orderData: order,
                    },
                    false,
                    [PRINTER_USAGE_TYPE.ORDER_SUMMARY],
                    false,
                    false,
                    false,
                    { isInternetReachable: true, isConnected: true },
                    true, // for isPrioritized
                  );
                }

                if (global.outletKdEventTypes.includes(KD_PRINT_EVENT_TYPE.REJECT)) {
                  await printUserOrder(
                    {
                      // orderId: orderIdList[i],
                      orderData: order,
                      // receiptNote: currOutlet.receiptNote || '',
                    },
                    false,
                    [PRINTER_USAGE_TYPE.KITCHEN_DOCKET],
                    false,
                    false,
                    false,
                    netInfoData = { isInternetReachable: netInfo.isInternetReachable, isConnected: netInfo.isConnected },
                    true,
                    [KD_ITEM_STATUS.PENDING, KD_ITEM_STATUS.DELIVERED],
                    // cancelUser,
                  );

                  await printKDSummaryCategoryWrapper(
                    {
                      // orderId: orderIdList[i],
                      orderData: order,
                    },
                    [KD_ITEM_STATUS.PENDING, KD_ITEM_STATUS.DELIVERED],
                    // cancelUser,
                  );

                  const printerIpCountDict = await calcPrintTotalForKdIndividualCancelled({
                    userOrder: order,
                  });
                  const printerTaskId = uuidv4();
                  global.printingTaskIdDict[printerTaskId] = {};

                  if (order && order.cartItemsCancelled && order.cartItemsCancelled.length > 0) {
                    for (let bdIndex = 0; bdIndex < order.cartItemsCancelled.length; bdIndex++) {
                      if (!order.cartItemsCancelled[bdIndex].isDocket) {
                        await printDocketForKDCancelled(
                          {
                            userOrder: order,
                            cartItem: order.cartItemsCancelled[bdIndex],
                            printerIpCountDict: printerIpCountDict,
                            printerTaskId: printerTaskId,
                          },
                          // true,
                          [PRINTER_USAGE_TYPE.KITCHEN_DOCKET],
                          // selectedPaymentMethodType === OFFLINE_PAYMENT_METHOD_TYPE.CASH,
                          [KD_ITEM_STATUS.PENDING, KD_ITEM_STATUS.DELIVERED],
                          // cancelUser,
                        );
                      }
                    }

                    for (let index = 0; index < order.cartItemsCancelled.length; index++) {
                      if (order.cartItemsCancelled[index].isDocket) {
                        await printDocketCancelled(
                          {
                            userOrder: order,
                            cartItem: order.cartItemsCancelled[index],
                          },
                          // true,
                          [PRINTER_USAGE_TYPE.RECEIPT],
                          [KD_ITEM_STATUS.PENDING, KD_ITEM_STATUS.DELIVERED],
                          // cancelUser,
                        );
                      }
                    }
                  }
                }
              }
            }
          }
          catch (ex) {
            console.error(ex);

            logToFile(ex);
          }

          //////////////////////////////////////////////////////////

          Alert.alert('Success', 'Order has been cancelled.\n\nNote: For online-paid cart item, reject will be no effect.');
        } else {
          Alert.alert('Error', 'Failed to cancel');
        }

        setTimeout(() => {
          CommonStore.update((s) => {
            s.isLoading = false;
          });
        }, 0);
      })
      .catch((err) => {
        console.error(err);
        Alert.alert('Error', 'Something went wrong');

        setTimeout(() => {
          CommonStore.update((s) => {
            s.isLoading = false;
          });
        }, 0);
      });

    CommonStore.update((s) => {
      s.summaryCheckDict = {};
      s.summaryCancelledCheckDict = {};
      s.summaryDeliveredCheckDict = {};
    });
  };

  const undoUserOrderCancelled = () => {
    var orderItemList = Object.entries(summaryCancelledCheckDict)
      .map(([key, value]) => value)
      .filter(
        (summaryItem) => summaryItem !== false && summaryItem !== undefined,
      )
      .reduce(
        (cartItemListAccum, summaryItem) =>
          cartItemListAccum.concat(summaryItem.cartItemList),
        [],
      );

    var orderIdList = [...new Set(orderItemList.map((o) => o.userOrderId))];

    var body = {
      orderItemList,
      orderIdList,

      userOrdersActive,

      outlet: currOutlet,
    };

    // console.log(body);

    CommonStore.update((s) => {
      s.isLoading = true;
    });

    // ApiClient.POST(API.undoUserOrderItemCancelledSummary, body)
    APILocal.undoUserOrderItemCancelledSummary({ body, uid: firebaseUid })
      .then((result) => {
        if (result && result.status === 'success') {
          Alert.alert('Success', 'Order has been undone');
        } else {
          Alert.alert('Error', 'Failed to undo');
        }

        setTimeout(() => {
          CommonStore.update((s) => {
            s.isLoading = false;
          });
        }, 0);
      })
      .catch((err) => {
        console.error(err);
        Alert.alert('Error', 'Something went wrong');

        setTimeout(() => {
          CommonStore.update((s) => {
            s.isLoading = false;
          });
        }, 0);
      });

    CommonStore.update((s) => {
      s.summaryCheckDict = {};
      s.summaryCancelledCheckDict = {};
      s.summaryDeliveredCheckDict = {};
    });
  };

  const deliverUserOrder = () => {
    var orderItemList = Object.entries(summaryCheckDict)
      .map(([key, value]) => value)
      .filter(
        (summaryItem) => summaryItem !== false && summaryItem !== undefined,
      )
      .reduce(
        (cartItemListAccum, summaryItem) =>
          cartItemListAccum.concat(summaryItem.cartItemList),
        [],
      );

    var orderIdList = [...new Set(orderItemList.map((o) => o.userOrderId))];

    var body = {
      orderItemList,
      orderIdList,

      userOrdersActive,

      outlet: currOutlet,
    };

    // console.log(body);

    CommonStore.update((s) => {
      s.isLoading = true;
    });

    (
      // netInfo.isInternetReachable && netInfo.isConnected
      //   ?
      //   ApiClient.POST(API.orderDeliverMultipleSummary, body)
      //   :
      (APILocal.orderDeliverMultipleSummary({ body, uid: firebaseUid }))
    )
      .then((result) => {
        if (result && result.status === 'success') {
          Alert.alert('Success', 'Order has been delivered');
        } else {
          Alert.alert('Error', 'Failed to deliver');
        }

        setTimeout(() => {
          CommonStore.update((s) => {
            s.isLoading = false;
          });
        }, 0);
      })
      .catch((err) => {
        console.error(err);
        Alert.alert('Error', 'Something went wrong');

        setTimeout(() => {
          CommonStore.update((s) => {
            s.isLoading = false;
          });
        }, 0);
      });

    CommonStore.update((s) => {
      s.summaryCheckDict = {};
      s.summaryCancelledCheckDict = {};
      s.summaryDeliveredCheckDict = {};
    });
  };

  const deliverUserOrderUndo = () => {
    var orderItemList = Object.entries(summaryDeliveredCheckDict)
      .map(([key, value]) => value)
      .filter(
        (summaryItem) => summaryItem !== false && summaryItem !== undefined,
      )
      .reduce(
        (cartItemListAccum, summaryItem) =>
          cartItemListAccum.concat(summaryItem.cartItemList),
        [],
      );

    var orderIdList = [...new Set(orderItemList.map((o) => o.userOrderId))];

    var body = {
      orderItemList,
      orderIdList,

      userOrdersActive,

      outlet: currOutlet,
    };

    // console.log(body);

    CommonStore.update((s) => {
      s.isLoading = true;
    });

    (
      // netInfo.isInternetReachable && netInfo.isConnected
      //   ?
      //   ApiClient.POST(API.orderDeliverUndoMultipleSummary, body)
      //   :
      (APILocal.orderDeliverUndoMultipleSummary({ body, uid: firebaseUid }))
    )
      .then((result) => {
        if (result && result.status === 'success') {
          Alert.alert('Success', 'Order has been undone');
        } else {
          Alert.alert('Error', 'Failed to undo');
        }

        setTimeout(() => {
          CommonStore.update((s) => {
            s.isLoading = false;
          });
        }, 0);
      })
      .catch((err) => {
        console.error(err);
        Alert.alert('Error', 'Something went wrong');

        setTimeout(() => {
          CommonStore.update((s) => {
            s.isLoading = false;
          });
        }, 0);
      });

    CommonStore.update((s) => {
      s.summaryCheckDict = {};
      s.summaryCancelledCheckDict = {};
      s.summaryDeliveredCheckDict = {};
    });
  };

  const checkOrderItemSummary = (summaryItem) => {
    // console.log('check!');

    var summaryCheckDictNew = {
      ...summaryCheckDict,
      [summaryItem.key]: summaryItem,
    };

    CommonStore.update((s) => {
      s.summaryCancelledCheckDict = {};
      s.summaryDeliveredCheckDict = {};

      s.summaryCheckDict = summaryCheckDictNew;
    });
  };

  const uncheckOrderItemSummary = (summaryItem) => {
    // console.log('uncheck!');

    // var body = {
    //   itemId: item.itemId,
    //   cartItemDate: item.cartItemDate,
    //   orderId: orderItem.uniqueId,
    // };

    // ApiClient.POST(API.orderDeliverUndo, body).then(result => {
    // }).catch(err => Alert('Error', 'Something went wrong'));

    var summaryCheckDictNew = {
      ...summaryCheckDict,
      [summaryItem.key]: false,
    };

    CommonStore.update((s) => {
      s.summaryCheckDict = summaryCheckDictNew;
    });
  };

  const checkOrderItemCancelledSummary = (summaryItem) => {
    // console.log('check!');

    CommonStore.update((s) => {
      s.summaryCheckDict = {};
      s.summaryDeliveredCheckDict = {};

      var summaryCancelledCheckDictNew = {
        ...summaryCancelledCheckDict,
        [summaryItem.key]: summaryItem,
      };

      s.summaryCancelledCheckDict = summaryCancelledCheckDictNew;
    });
  };

  const uncheckOrderItemCancelledSummary = (summaryItem) => {
    // console.log('uncheck!');

    var summaryCancelledCheckDictNew = {
      ...summaryCancelledCheckDict,
      [summaryItem.key]: false,
    };

    CommonStore.update((s) => {
      s.summaryCancelledCheckDict = summaryCancelledCheckDictNew;
    });
  };

  const checkOrderItemDeliveredSummary = (summaryItem) => {
    // console.log('check!');

    CommonStore.update((s) => {
      s.summaryCheckDict = {};
      s.summaryCancelledCheckDict = {};

      var summaryDeliveredCheckDictNew = {
        ...summaryDeliveredCheckDict,
        [summaryItem.key]: summaryItem,
      };

      s.summaryDeliveredCheckDict = summaryDeliveredCheckDictNew;
    });
  };

  const uncheckOrderItemDeliveredSummary = (summaryItem) => {
    // console.log('uncheck!');

    var summaryDeliveredCheckDictNew = {
      ...summaryDeliveredCheckDict,
      [summaryItem.key]: false,
    };

    CommonStore.update((s) => {
      s.summaryDeliveredCheckDict = summaryDeliveredCheckDictNew;
    });
  };

  // const selectedCategories = categories.filter(o => selectedCategoriesIds.includes(o.value))

  //////////////////////////////////////////////

  // use render cycle to update, state will delay

  var currTickListType = TICK_LIST_TYPE.UNTICK;
  if (Object.values(summaryCheckDict).find((item) => item && item.key)) {
    currTickListType = TICK_LIST_TYPE.NORMAL;
  } else if (
    Object.values(summaryDeliveredCheckDict).find((item) => item && item.key)
  ) {
    currTickListType = TICK_LIST_TYPE.DELIVERED;
  } else if (
    Object.values(summaryCancelledCheckDict).find((item) => item && item.key)
  ) {
    currTickListType = TICK_LIST_TYPE.CANCELLED;
  }

  // console.log('currTickListType');
  // console.log(currTickListType);

  //////////////////////////////////////////////

  const renderKitchenOrder = ({ item }) => {
    return (
      <KitchenOrder
        viewOptions={viewOptions}
        // checkOrderItem={checkOrderItem.bind(this)}
        checkSelectedItem={checkSelectedItem}
        completeOrder={completeOrder.bind(this)}
        setScroll={(scroll) => {
          setState({ scroll });
        }}
        search={search}
        scroll={scroll}
        order={item}
        selectedCat={selectedCat}
        onTimesUp={() => {
          completeOrder(item.uniqueId, () => { }, item);
        }}
        refreshRate={refreshRate}
      />
    );
  };


  const sortedUserOrdersActive = useMemo(() => {
    if (sortingType === 1) {
      return [...userOrdersActive].sort((a, b) => b.isPrioritizedOrder - a.isPrioritizedOrder);
    }
    return userOrdersActive;
  }, [userOrdersActive, sortingType]);

  const sortedUserOrdersActive1 = useMemo(() => {
    if (sortingType === 0) {
      return [...userOrdersActive].sort((a, b) => a.isPrioritizedOrder - b.isPrioritizedOrder);
    }
    return userOrdersActive;
  }, [userOrdersActive, sortingType]);


  const deliverButtonStyle = useMemo(() => [
    switchMerchant
      ? {
        width:
          currTickListType === TICK_LIST_TYPE.NORMAL
            ? '30%'
            : '45%',
        height: switchMerchant
          ? windowHeight * 0.05
          : windowHeight * 0.04,

        alignItems: 'center',
        justifyContent: 'center',

        backgroundColor:
          currTickListType === TICK_LIST_TYPE.NORMAL
            ? Colors.primaryColor
            : Colors.tabRed,
        borderRadius: 8,

        shadowColor: '#000',
        shadowOffset: {
          width: 0,
          height: 1,
        },
        shadowOpacity: 0.22,
        shadowRadius: 2.22,
        elevation: 3,
        top: '1%',
      }
      : {
        width:
          currTickListType === TICK_LIST_TYPE.NORMAL
            ? '30%'
            : '45%',
        height: switchMerchant
          ? windowHeight * 0.05
          : windowHeight * 0.04,

        alignItems: 'center',
        justifyContent: 'center',

        backgroundColor:
          currTickListType === TICK_LIST_TYPE.NORMAL
            ? Colors.primaryColor
            : Colors.tabRed,
        borderRadius: 8,

        shadowColor: '#000',
        shadowOffset: {
          width: 0,
          height: 1,
        },
        shadowOpacity: 0.22,
        shadowRadius: 2.22,
        elevation: 3,
      }
  ], [switchMerchant, currTickListType, windowHeight]);

  const deliverButtonTextStyle = useMemo(() =>
    switchMerchant
      ? {
        color: Colors.whiteColor,
        fontFamily: 'NunitoSans-SemiBold',
        fontSize: 10,
        bottom: 1,
      }
      : {
        color: Colors.whiteColor,
        fontFamily: 'NunitoSans-SemiBold',
        fontSize: 14,
      }
    , [switchMerchant]);

  const rejectButtonStyle = useMemo(() => [
    switchMerchant
      ? {
        width:
          currTickListType === TICK_LIST_TYPE.NORMAL
            ? '30%'
            : '45%',
        height: switchMerchant
          ? windowHeight * 0.05
          : windowHeight * 0.04,

        alignItems: 'center',
        justifyContent: 'center',

        backgroundColor:
          currTickListType === TICK_LIST_TYPE.NORMAL
            ? Colors.tabRed
            : Colors.primaryColor,
        borderRadius: 8,

        shadowColor: '#000',
        shadowOffset: {
          width: 0,
          height: 1,
        },
        shadowOpacity: 0.22,
        shadowRadius: 2.22,
        elevation: 3,
        top: '1%',
      }
      : {
        width:
          currTickListType === TICK_LIST_TYPE.NORMAL
            ? '30%'
            : '45%',
        height: switchMerchant
          ? windowHeight * 0.05
          : windowHeight * 0.04,

        alignItems: 'center',
        justifyContent: 'center',

        backgroundColor:
          currTickListType === TICK_LIST_TYPE.NORMAL
            ? Colors.tabRed
            : Colors.primaryColor,
        borderRadius: 8,

        shadowColor: '#000',
        shadowOffset: {
          width: 0,
          height: 1,
        },
        shadowOpacity: 0.22,
        shadowRadius: 2.22,
        elevation: 3,
      }
  ], [switchMerchant, currTickListType, windowHeight]);

  const UndoUserOrderModal = React.memo(function UndoUserOrderModal() {
    return (
      <ModalView
        supportedOrientations={['landscape', 'portrait']}
        style={
          {
            // flex: 1
          }
        }
        visible={showUndoUserOrderModal}
        transparent
        animationType={'slide'}>
        <KeyboardAvoidingView
          behavior={'padding'}
          style={styles.modalContainer}>
          <View
            style={[
              styles.modalView,
              {
                height: windowHeight * 0.7,

                ...getTransformForModalInsideNavigation(),
              },
            ]}>
            <TouchableOpacity
              style={styles.closeButton}
              onPress={() => {
                setShowUndoUserOrderModal(false);
              }}>
              {switchMerchant ? (
                <AntDesign
                  name="closecircle"
                  size={16}
                  color={Colors.fieldtTxtColor}
                />
              ) : (
                <AntDesign
                  name="closecircle"
                  size={25}
                  color={Colors.fieldtTxtColor}
                />
              )}
            </TouchableOpacity>
            <View
              style={[
                styles.modalTitle,
                {
                  // marginBottom: 15,
                },
                switchMerchant
                  ? {
                    top: '8%',
                  }
                  : {},
              ]}>
              <Text
                style={[
                  styles.modalTitleText,
                  switchMerchant
                    ? {
                      fontSize: 12,
                    }
                    : {},
                ]}>
                {`Undo Order #${selectedUserOrderUndo &&
                  selectedUserOrderUndo.orderType !== ORDER_TYPE.DINEIN
                  ? 'T'
                  : ''
                  }${selectedUserOrderUndo ? selectedUserOrderUndo.orderId : ''}`}
              </Text>
            </View>
            <View
              style={[
                { top: '5%', alignSelf: 'flex-start', width: '100%' },
                switchMerchant
                  ? {
                    top: '8%',
                    // height: windowHeight * 0.4,
                    //   borderWidth: 1,
                  }
                  : {},
              ]}>
              <View
                style={
                  {
                    // marginBottom: 20,
                  }
                } />

              <ScrollView
                style={{
                  // paddingBottom: 100,
                  // marginBottom: 50,
                  marginBottom: 0,
                  paddingBottom: 0,
                  // backgroundColor: 'purple',
                  // borderWidth: 1,

                  height: switchMerchant
                    ? windowHeight * 0.2
                    : windowHeight * 0.3,
                  // marginVertical: 55,
                }}
                contentContainerStyle={{
                  paddingVertical: 15,
                }}
                nestedScrollEnabled
                scrollEnabled>
                <View
                  style={{
                    // paddingBottom: 100,
                    // marginBottom: 50,
                    marginBottom: 0,
                    paddingBottom: 0,
                    // backgroundColor: 'purple',
                  }}
                // nestedScrollEnabled={true}
                // scrollEnabled={props.viewOptions == true ? true : props.scroll}
                >
                  {
                    // order.cartItems.map((orderItem, index) => {
                    selectedOrderItemList.map((orderItem, index) => {
                      return (
                        <View
                          style={{
                            // borderBottomWidth: index + 1 === order.cartItems.length ? null : StyleSheet.hairlineWidth,
                            padding: 15,
                            borderWidth: 1,
                            paddingHorizontal: 5,
                            // backgroundColor: 'green',
                          }}>
                          <View
                            style={{
                              flexDirection: 'row',
                              alignItems: 'center',
                              width: '100%',
                              // backgroundColor: 'yellow',
                            }}>
                            {/* <View style={{ flexDirection: 'row', alignItems: 'center' }}></View> */}
                            <View
                              style={{
                                width: '15%',
                                height: '100%',
                                marginRight: 5,
                                // backgroundColor: 'blue',
                              }}>
                              {/* <TouchableOpacity onPress={() => {
                                                      checkOrderItemFunc(orderItem, order);
                                                  }}>
                                                      <View style={{
                                                          borderRadius: 5,
                                                          backgroundColor: orderItem.isChecked == true ? Colors.primaryColor : Colors.fieldtBgColor,
                                                          marginRight: 2,
                                                          marginLeft: 2,
                                                          alignItems: 'center',
                                                          justifyContent: 'center',
                                                          width: 34,
                                                          height: 34
                                                      }}>
                                                          <Icon name="check" size={20} color={orderItem.isChecked == true ? Colors.whiteColor : Colors.fieldtTxtColor} style={{ margin: 2 }} />
                                                      </View>
                                                  </TouchableOpacity> */}

                              <CheckBox
                                style={{
                                  ...(Platform.OS === 'ios' && {
                                    width: 16,
                                    height: 16,
                                  }),
                                  opacity: 0,
                                }}
                              // value={selectedOrderItemCheckDict[orderItem.itemId + orderItem.cartItemDate.toString()] !== false && selectedOrderItemCheckDict[orderItem.itemId + orderItem.cartItemDate.toString()] !== undefined} onValueChange={(value) => {
                              //     if (selectedOrderItemCheckDict[orderItem.itemId + orderItem.cartItemDate.toString()]) {
                              //         uncheckOrderItem(orderItem, order);
                              //     }
                              //     else {
                              //         checkOrderItem(orderItem, order);
                              //     }
                              // }}
                              />
                            </View>

                            <View
                              style={{ flexDirection: 'column', width: '65%' }}>
                              <View style={{}}>
                                <Text
                                  style={[
                                    {
                                      fontFamily: 'NunitoSans-Bold',
                                      fontSize: 14,
                                    },
                                    switchMerchant
                                      ? {
                                        fontSize: 7,
                                      }
                                      : {},
                                  ]}>
                                  {orderItem.name}{orderItem.priceType === PRODUCT_PRICE_TYPE.UNIT ? ` (${UNIT_TYPE_SHORT[orderItem.unitType]})` : ''}
                                </Text>
                              </View>
                              {orderItem.remarks &&
                                orderItem.remarks.length > 0 ? (
                                <View style={{}}>
                                  <Text
                                    style={[
                                      {
                                        fontFamily: 'NunitoSans-SemiBold',
                                        fontSize: 10,
                                      },
                                      switchMerchant
                                        ? {
                                          fontSize: 7,
                                        }
                                        : {},
                                    ]}>
                                    {orderItem.remarks}
                                  </Text>
                                </View>
                              ) : (
                                <></>
                              )}
                            </View>

                            <View style={{ width: '20%', alignItems: 'center' }}>
                              <Text
                                style={[
                                  {
                                    fontFamily: 'NunitoSans-Bold',
                                    color: Colors.fieldtTxtColor,
                                  },
                                  switchMerchant
                                    ? {
                                      fontSize: 7,
                                    }
                                    : {},
                                ]}>
                                x{orderItem.quantity}
                              </Text>
                            </View>
                          </View>

                          <View
                            style={{
                              width: '100%',
                              alignItems: 'flex-start',
                              //paddingHorizontal: 20,
                              marginTop: 10,
                              // backgroundColor: 'red',
                            }}>
                            {orderItem.addOns.map((o) => {
                              return (
                                <View
                                  style={{ flexDirection: 'row', marginLeft: 5 }}>
                                  {/* <Text style={{ fontFamily: "NunitoSans-Regular", color: Colors.fieldtTxtColor }}>x{o.quantity}</Text> */}
                                  <View style={{ width: '15%' }} />
                                  <Text
                                    style={{
                                      fontFamily: 'NunitoSans-Regular',
                                      color: Colors.fieldtTxtColor,
                                      width: '30%',
                                    }}>
                                    {o.name}:
                                  </Text>
                                  {(o.choiceNames || []).map((choice) => {
                                    return (
                                      <Text
                                        style={[
                                          {
                                            marginLeft: 10,
                                            flex: 3,
                                            fontFamily: 'NunitoSans-Regular',
                                            color: Colors.fieldtTxtColor,
                                          },
                                          switchMerchant
                                            ? {
                                              fontSize: 8,
                                            }
                                            : {},
                                        ]}>
                                        {choice}
                                      </Text>
                                    );
                                  })}
                                </View>
                              );
                            })}
                          </View>
                        </View>
                      );
                    })
                  }

                  {/* {countdown > -1 &&
                              <Animated.View style={{ width: 100, height: 100, alignSelf: 'center', transform: [{ rotate: spin }], alignItems: 'center', justifyContent: 'center' }}>
                                  <Icon name={"reload"} size={70} color={Colors.primaryColor} />
                              </Animated.View>} */}
                </View>

                {true ? (
                  <>
                    {/* <View style={{
                                              height: 1.5,
                                              width: '100%',
                                              backgroundColor: 'black',
                                              // marginTop: 8,
                                              // marginBottom: 4,
                                              opacity: 0.1,
                                          }}>

                                          </View> */}

                    <View
                      style={[
                        {
                          // height: 1.5,
                          width: '100%',
                          // backgroundColor: 'black',
                          // marginTop: 8,
                          // marginBottom: 4,
                          // opacity: 0.1,
                          alignItems: 'center',
                          marginTop: 15,
                          marginBottom: 15,
                        },
                        switchMerchant
                          ? {
                            marginTop: '1%',
                            //   borderWidth: 1,
                            marginBottom: '2%',
                          }
                          : {},
                      ]}>
                      <Text
                        style={[
                          {
                            fontFamily: 'NunitoSans-SemiBold',
                            fontSize: 16,
                            color: Colors.blackColor,
                          },
                          switchMerchant
                            ? {
                              fontSize: 10,
                            }
                            : {},
                        ]}>
                        {'Delivered'}
                      </Text>
                    </View>

                    <View
                      style={
                        {
                          // paddingBottom: 50,
                          // backgroundColor: 'red'
                        }
                      }
                    // nestedScrollEnabled={true} scrollEnabled={props.viewOptions == true ? true : props.scroll}
                    >
                      {
                        // order.cartItems.map((orderItem, index) => {
                        selectedOrderItemDeliveredList.map(
                          (orderItem, index) => {
                            return (
                              <View
                                style={{
                                  // borderBottomWidth: index + 1 === order.cartItems.length ? null : StyleSheet.hairlineWidth,
                                  padding: 15,
                                  paddingTop: index === 0 ? 0 : 15,
                                  paddingHorizontal: 5,
                                }}>
                                <View
                                  style={{
                                    flexDirection: 'row',
                                    alignItems: 'center',
                                    width: '100%',
                                  }}>
                                  {/* <View style={{ flexDirection: 'row', alignItems: 'center' }}></View> */}
                                  <View
                                    style={{
                                      width: '15%',
                                      height: '100%',
                                      marginRight: 5,
                                    }}>
                                    {/* <TouchableOpacity onPress={() => {
                                                      checkOrderItemFunc(orderItem, order);
                                                  }}>
                                                      <View style={{
                                                          borderRadius: 5,
                                                          backgroundColor: orderItem.isChecked == true ? Colors.primaryColor : Colors.fieldtBgColor,
                                                          marginRight: 2,
                                                          marginLeft: 2,
                                                          alignItems: 'center',
                                                          justifyContent: 'center',
                                                          width: 34,
                                                          height: 34
                                                      }}>
                                                          <Icon name="check" size={20} color={orderItem.isChecked == true ? Colors.whiteColor : Colors.fieldtTxtColor} style={{ margin: 2 }} />
                                                      </View>
                                                  </TouchableOpacity> */}

                                    <CheckBox
                                      style={{
                                        ...(Platform.OS === 'ios' && {
                                          width: 16,
                                          height: 16,
                                        }),
                                        opacity: 0,
                                      }}
                                    // value={selectedOrderItemDeliveredCheckDict[orderItem.itemId + orderItem.cartItemDate.toString()] !== false && selectedOrderItemDeliveredCheckDict[orderItem.itemId + orderItem.cartItemDate.toString()] !== undefined} onValueChange={(value) => {
                                    //     if (selectedOrderItemDeliveredCheckDict[orderItem.itemId + orderItem.cartItemDate.toString()]) {
                                    //         uncheckOrderItemDelivered(orderItem, order);
                                    //     }
                                    //     else {
                                    //         checkOrderItemDelivered(orderItem, order);
                                    //     }
                                    // }}
                                    />
                                  </View>

                                  <View
                                    style={{
                                      flexDirection: 'column',
                                      width: '65%',
                                    }}>
                                    <View style={{}}>
                                      <Text
                                        style={[
                                          {
                                            fontFamily: 'NunitoSans-Bold',
                                            fontSize: 14,
                                            textDecorationLine: 'line-through',
                                            color: Colors.primaryColor,
                                          },
                                          switchMerchant
                                            ? {
                                              fontSize: 8,
                                            }
                                            : {},
                                        ]}>
                                        {orderItem.name}{orderItem.priceType === PRODUCT_PRICE_TYPE.UNIT ? ` (${UNIT_TYPE_SHORT[orderItem.unitType]})` : ''}
                                      </Text>
                                    </View>
                                    {orderItem.remarks &&
                                      orderItem.remarks.length > 0 ? (
                                      <View style={{}}>
                                        <Text
                                          style={[
                                            {
                                              fontFamily: 'NunitoSans-SemiBold',
                                              fontSize: 10,
                                              textDecorationLine:
                                                'line-through',
                                              color: Colors.primaryColor,
                                            },
                                            switchMerchant
                                              ? {
                                                fontSize: 8,
                                              }
                                              : {},
                                          ]}>
                                          {orderItem.remarks}
                                        </Text>
                                      </View>
                                    ) : (
                                      <></>
                                    )}
                                  </View>

                                  <View
                                    style={{
                                      width: '20%',
                                      alignItems: 'center',
                                    }}>
                                    <Text
                                      style={[
                                        {
                                          fontFamily: 'NunitoSans-Bold',
                                          color: Colors.fieldtTxtColor,
                                        },
                                        switchMerchant
                                          ? {
                                            fontSize: 8,
                                          }
                                          : {},
                                      ]}>
                                      x{orderItem.quantity}
                                    </Text>
                                  </View>
                                </View>
                                <View
                                  style={{
                                    width: '100%',
                                    alignItems: 'flex-start',
                                    //paddingHorizontal: 20,
                                    marginTop: 10,
                                  }}>
                                  {orderItem.addOns.map((o) => {
                                    return (
                                      <View
                                        style={{
                                          flexDirection: 'row',
                                          marginLeft: 5,
                                        }}>
                                        {/* <Text style={{ fontFamily: "NunitoSans-Regular", color: Colors.fieldtTxtColor }}>x{o.quantity}</Text> */}
                                        <View style={{ width: '15%' }} />
                                        <Text
                                          style={[
                                            {
                                              fontFamily: 'NunitoSans-Regular',
                                              color: Colors.fieldtTxtColor,
                                              textDecorationLine:
                                                'line-through',
                                              color: Colors.primaryColor,
                                              width: '30%',
                                            },
                                            switchMerchant
                                              ? {
                                                fontSize: 8,
                                              }
                                              : {},
                                          ]}>
                                          {o.name}:
                                        </Text>
                                        {(o.choiceNames || []).map((choice) => {
                                          return (
                                            <Text
                                              style={[
                                                {
                                                  marginLeft: 10,
                                                  flex: 3,
                                                  fontFamily:
                                                    'NunitoSans-Regular',
                                                  color: Colors.fieldtTxtColor,
                                                  textDecorationLine:
                                                    'line-through',
                                                  color: Colors.primaryColor,
                                                },
                                                switchMerchant
                                                  ? {
                                                    fontSize: 8,
                                                  }
                                                  : {},
                                              ]}>
                                              {choice}
                                            </Text>
                                          );
                                        })}
                                      </View>
                                    );
                                  })}
                                </View>
                              </View>
                            );
                          },
                        )
                      }

                      {/* {countdown > -1 &&
                              <Animated.View style={{ width: 100, height: 100, alignSelf: 'center', transform: [{ rotate: spin }], alignItems: 'center', justifyContent: 'center' }}>
                                  <Icon name={"reload"} size={70} color={Colors.primaryColor} />
                              </Animated.View>} */}
                    </View>

                    {/* //////////////////////////////////////////////////////////////////////////////////////////// */}

                    <View
                      style={{
                        height: 1.5,
                        width: '100%',
                        backgroundColor: 'black',
                        // marginTop: 8,
                        // marginBottom: 4,
                        opacity: 0.1,
                      }} />

                    <View
                      style={[
                        {
                          // height: 1.5,
                          width: '100%',
                          // backgroundColor: 'black',
                          // marginTop: 8,
                          // marginBottom: 4,
                          // opacity: 0.1,
                          alignItems: 'center',
                          marginTop: 15,
                          marginBottom: 15,
                        },
                        switchMerchant
                          ? {
                            marginTop: '2%',
                            marginBottom: '2%',
                          }
                          : {},
                      ]}>
                      <Text
                        style={[
                          {
                            fontFamily: 'NunitoSans-SemiBold',
                            fontSize: 16,
                            color: Colors.blackColor,
                          },
                          switchMerchant
                            ? {
                              fontSize: 10,
                            }
                            : {},
                        ]}>
                        {'Cancelled'}
                      </Text>
                    </View>

                    <View
                      style={
                        {
                          // paddingBottom: 50,
                          // backgroundColor: 'red'
                        }
                      }
                    // nestedScrollEnabled={true} scrollEnabled={props.viewOptions == true ? true : props.scroll}
                    >
                      {
                        // order.cartItems.map((orderItem, index) => {
                        selectedOrderItemCancelledList.map(
                          (orderItem, index) => {
                            return (
                              <View
                                style={{
                                  // borderBottomWidth: index + 1 === order.cartItems.length ? null : StyleSheet.hairlineWidth,
                                  padding: 15,
                                  paddingTop: index === 0 ? 0 : 15,
                                  paddingHorizontal: 5,
                                }}>
                                <View
                                  style={{
                                    flexDirection: 'row',
                                    alignItems: 'center',
                                    width: '100%',
                                  }}>
                                  {/* <View style={{ flexDirection: 'row', alignItems: 'center' }}></View> */}
                                  <View
                                    style={{
                                      width: '15%',
                                      height: '100%',
                                      marginRight: 5,
                                    }}>
                                    {/* <TouchableOpacity onPress={() => {
                                                      checkOrderItemFunc(orderItem, order);
                                                  }}>
                                                      <View style={{
                                                          borderRadius: 5,
                                                          backgroundColor: orderItem.isChecked == true ? Colors.primaryColor : Colors.fieldtBgColor,
                                                          marginRight: 2,
                                                          marginLeft: 2,
                                                          alignItems: 'center',
                                                          justifyContent: 'center',
                                                          width: 34,
                                                          height: 34
                                                      }}>
                                                          <Icon name="check" size={20} color={orderItem.isChecked == true ? Colors.whiteColor : Colors.fieldtTxtColor} style={{ margin: 2 }} />
                                                      </View>
                                                  </TouchableOpacity> */}

                                    <CheckBox
                                      style={{
                                        ...(Platform.OS === 'ios' && {
                                          width: 16,
                                          height: 16,
                                        }),
                                        opacity: 0,
                                      }}
                                    // value={selectedOrderItemCancelledCheckDict[orderItem.itemId + orderItem.cartItemDate.toString()] !== false && selectedOrderItemCancelledCheckDict[orderItem.itemId + orderItem.cartItemDate.toString()] !== undefined} onValueChange={(value) => {
                                    //     if (selectedOrderItemCancelledCheckDict[orderItem.itemId + orderItem.cartItemDate.toString()]) {
                                    //         uncheckOrderItemCancelled(orderItem, order);
                                    //     }
                                    //     else {
                                    //         checkOrderItemCancelled(orderItem, order);
                                    //     }
                                    // }}
                                    />
                                  </View>

                                  <View
                                    style={{
                                      flexDirection: 'column',
                                      width: '65%',
                                    }}>
                                    <View style={{}}>
                                      <Text
                                        style={[
                                          {
                                            fontFamily: 'NunitoSans-Bold',
                                            fontSize: 14,
                                            textDecorationLine: 'line-through',
                                            color: Colors.tabRed,
                                          },
                                          switchMerchant
                                            ? {
                                              fontSize: 8,
                                            }
                                            : {},
                                        ]}>
                                        {orderItem.name}{orderItem.priceType === PRODUCT_PRICE_TYPE.UNIT ? ` (${UNIT_TYPE_SHORT[orderItem.unitType]})` : ''}
                                      </Text>
                                    </View>
                                    {orderItem.remarks &&
                                      orderItem.remarks.length > 0 ? (
                                      <View style={{}}>
                                        <Text
                                          style={[
                                            {
                                              fontFamily: 'NunitoSans-SemiBold',
                                              fontSize: 10,
                                              textDecorationLine:
                                                'line-through',
                                              color: Colors.tabRed,
                                            },
                                            switchMerchant
                                              ? {
                                                fontSize: 8,
                                              }
                                              : {},
                                          ]}>
                                          {orderItem.remarks}
                                        </Text>
                                      </View>
                                    ) : (
                                      <></>
                                    )}
                                  </View>

                                  <View
                                    style={{
                                      width: '20%',
                                      alignItems: 'center',
                                    }}>
                                    <Text
                                      style={[
                                        {
                                          fontFamily: 'NunitoSans-Bold',
                                          color: Colors.fieldtTxtColor,
                                        },
                                        switchMerchant
                                          ? {
                                            fontSize: 8,
                                          }
                                          : {},
                                      ]}>
                                      x{orderItem.quantity}
                                    </Text>
                                  </View>
                                </View>
                                <View
                                  style={{
                                    width: '100%',
                                    alignItems: 'flex-start',
                                    //paddingHorizontal: 20,
                                    marginTop: 10,
                                  }}>
                                  {orderItem.addOns.map((o) => {
                                    return (
                                      <View
                                        style={{
                                          flexDirection: 'row',
                                          marginLeft: 5,
                                        }}>
                                        {/* <Text style={{ fontFamily: "NunitoSans-Regular", color: Colors.fieldtTxtColor }}>x{o.quantity}</Text> */}
                                        <View style={{ width: '15%' }} />
                                        <Text
                                          style={[
                                            {
                                              fontFamily: 'NunitoSans-Regular',
                                              color: Colors.fieldtTxtColor,
                                              textDecorationLine:
                                                'line-through',
                                              color: Colors.tabRed,
                                              width: '30%',
                                            },
                                            switchMerchant
                                              ? {
                                                fontSize: 8,
                                              }
                                              : {},
                                          ]}>
                                          {o.name}:
                                        </Text>
                                        {(o.choiceNames || []).map((choice) => {
                                          return (
                                            <Text
                                              style={[
                                                {
                                                  marginLeft: 10,
                                                  flex: 3,
                                                  fontFamily:
                                                    'NunitoSans-Regular',
                                                  color: Colors.fieldtTxtColor,
                                                  textDecorationLine:
                                                    'line-through',
                                                  color: Colors.tabRed,
                                                },
                                                switchMerchant
                                                  ? {
                                                    fontSize: 8,
                                                  }
                                                  : {},
                                              ]}>
                                              {choice}
                                            </Text>
                                          );
                                        })}
                                      </View>
                                    );
                                  })}
                                </View>
                              </View>
                            );
                          },
                        )
                      }

                      {/* {countdown > -1 &&
                              <Animated.View style={{ width: 100, height: 100, alignSelf: 'center', transform: [{ rotate: spin }], alignItems: 'center', justifyContent: 'center' }}>
                                  <Icon name={"reload"} size={70} color={Colors.primaryColor} />
                              </Animated.View>} */}
                    </View>
                  </>
                ) : (
                  <></>
                )}
              </ScrollView>

              <Text
                style={[
                  {
                    marginTop: 15,
                    fontSize: 20,
                    fontFamily: 'NunitoSans-Bold',
                  },
                  switchMerchant
                    ? {
                      fontSize: 10,
                    }
                    : {},
                ]}>
                Remarks:
              </Text>
              <TextInput
                underlineColorAndroid={Colors.fieldtBgColor}
                style={[
                  styles.textInput9,
                  {
                    marginTop: 10,
                    shadowColor: '#000',
                    shadowOffset: {
                      width: 1,
                      height: 5,
                    },
                    shadowOpacity: 0.32,
                    shadowRadius: 3.22,
                  },
                  switchMerchant
                    ? {
                      fontSize: 8,
                      height: windowHeight * 0.08,
                    }
                    : {},
                ]}
                placeholderStyle={{ padding: 5 }}
                placeholder="Place your remarks here"
                onChangeText={(text) => {
                  setUserOrderUndoRemarks(text);
                }}
                defaultValue={userOrderUndoRemarks}
                textAlignVertical={'top'}
                multiline
                placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
              />

              {/* <Text style={{ fontSize: 20, fontFamily: 'NunitoSans-Bold', marginTop: 25 }}>Send As:</Text> */}

              <View
                style={{
                  alignItems: 'center',
                  justifyContent: 'center',
                  //top: '10%',
                  flexDirection: 'row',
                  marginTop: 25,
                  width: '100%',
                }}>
                <TouchableOpacity
                  style={[
                    {
                      justifyContent: 'center',
                      flexDirection: 'row',
                      borderWidth: 1,
                      borderColor: Colors.primaryColor,
                      backgroundColor: '#0A1F44',
                      borderRadius: 5,
                      width: 100,
                      paddingHorizontal: 10,
                      height: 40,
                      alignItems: 'center',
                      shadowOffset: {
                        width: 0,
                        height: 2,
                      },
                      shadowOpacity: 0.22,
                      shadowRadius: 3.22,
                      elevation: 1,
                      zIndex: -1,
                    },
                    switchMerchant
                      ? {
                        height: windowHeight * 0.08,
                      }
                      : {},
                  ]}
                  onPress={() => {
                    undoUserOrder(selectedUserOrderUndo);
                  }}>
                  {isLoading ? (
                    <>
                      <ActivityIndicator
                        color={Colors.whiteColor}
                        size={'small'}
                      />
                    </>
                  ) : (
                    <Text
                      style={[
                        {
                          color: Colors.whiteColor,
                          //marginLeft: 5,
                          fontSize: 16,
                          color: '#FFFFFF',
                          fontFamily: 'NunitoSans-Bold',
                        },
                        switchMerchant
                          ? {
                            fontSize: 10,
                          }
                          : {},
                      ]}>
                      UNDO
                    </Text>
                  )}
                </TouchableOpacity>

                <TouchableOpacity
                  style={[
                    {
                      justifyContent: 'center',
                      flexDirection: 'row',
                      borderWidth: 1,
                      borderColor: Colors.primaryColor,
                      backgroundColor: '#0A1F44',
                      borderRadius: 5,
                      width: 100,
                      paddingHorizontal: 10,
                      height: 40,
                      alignItems: 'center',
                      shadowOffset: {
                        width: 0,
                        height: 2,
                      },
                      shadowOpacity: 0.22,
                      shadowRadius: 3.22,
                      elevation: 1,
                      zIndex: -1,
                      marginLeft: 15,
                    },
                    switchMerchant
                      ? {
                        height: windowHeight * 0.08,
                      }
                      : {},
                  ]}
                  onPress={() => {
                    setShowUndoUserOrderModal(false);

                    setTimeout(() => {
                      CommonStore.update((s) => {
                        s.isLoading = false;
                      });
                    }, 0);
                  }}>
                  <Text
                    style={[
                      {
                        color: Colors.whiteColor,
                        //marginLeft: 5,
                        fontSize: 16,
                        color: '#FFFFFF',
                        fontFamily: 'NunitoSans-Bold',
                      },
                      switchMerchant
                        ? {
                          fontSize: 10,
                        }
                        : {},
                    ]}>
                    CANCEL
                  </Text>
                </TouchableOpacity>

                {/* <TouchableOpacity
                                  style={[styles.modalSaveButton, {
                                      zIndex: -1
                                  }]}
                                  onPress={() => { downloadPDF() }}>
                                  <Text style={[styles.modalDescText, { color: Colors.primaryColor }]}>PDF</Text>
                              </TouchableOpacity> */}
              </View>
            </View>
          </View>
        </KeyboardAvoidingView>
      </ModalView>
    );
  });

  const DeliveryScreenModal = React.memo(function DeliveryScreenModal() {
    return (
      <ModalView
        supportedOrientations={['landscape', 'portrait']}
        style={{
          backgroundColor: Colors.primaryColor,
          width: windowWidth,
          height: windowHeight,
        }}
        animationType="fade"
        transparent
        visible={deliveryScreen}
        onRequestClose={() => {
          Alert.alert(
            'Modal is now closing',
            [{ text: 'OK', onPress: () => { } }],
            { cancelable: false },
          );
        }}>
        <View
          style={{
            backgroundColor: 'rgba(0,0,0,0.5)',
            flex: 1,
            justifyContent: 'center',
            alignItems: 'center',
          }}>
          <View
            style={{
              width: windowWidth * 0.7,
              height: windowHeight * 0.75,
              backgroundColor: 'white',
              borderRadius: 12,
              alignItems: 'center',
              justifyContent: 'space-around',
              padding: 30,
            }}>
            <View style={{ alignItems: 'center' }}>
              <Text style={{ fontFamily: 'NunitoSans-Bold', fontSize: 26 }}>
                Confirm Shipment
              </Text>
            </View>
            <View style={{ alignItems: 'center', width: '100%' }}>
              <View
                style={{
                  flexDirection: 'row',
                  paddingVertical: 10,
                  borderBottomWidth: StyleSheet.hairlineWidth,
                  borderColor: Colors.fieldtTxtColor,
                }}>
                <View style={{ flex: 0.75 }} />
                <View style={{ flex: 3, alignItems: 'center' }}>
                  <Text style={{ fontFamily: 'NunitoSans-Bold', fontSize: 15 }}>
                    Product Name
                  </Text>
                </View>
                <View style={{ flex: 2, alignItems: 'center' }}>
                  <Text style={{ fontFamily: 'NunitoSans-Bold', fontSize: 15 }}>
                    SKU
                  </Text>
                </View>
                <View style={{ flex: 3, alignItems: 'center' }}>
                  <Text style={{ fontFamily: 'NunitoSans-Bold', fontSize: 15 }}>
                    Received qty
                  </Text>
                </View>
                <View style={{ flex: 3, alignItems: 'center' }}>
                  <Text style={{ fontFamily: 'NunitoSans-Bold', fontSize: 15 }}>
                    Cost (RM)
                  </Text>
                </View>
                <View style={{ flex: 3, alignItems: 'center' }}>
                  <Text style={{ fontFamily: 'NunitoSans-Bold', fontSize: 15 }}>
                    Subtotal (RM)
                  </Text>
                </View>
              </View>
              <View style={{ flexDirection: 'row', paddingVertical: 20 }}>
                <View
                  style={{
                    flex: 0.75,
                    alignItems: 'flex-end',
                    justifyContent: 'center',
                  }}>
                  <View
                    style={{
                      borderRadius: 5,
                      backgroundColor: Colors.fieldtBgColor,
                      alignItems: 'center',
                    }}>
                    <Icon
                      name="check"
                      size={20}
                      color={Colors.primaryColor}
                      style={{ margin: 2 }}
                    />
                  </View>
                </View>
                <View
                  style={{
                    flex: 3,
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}>
                  <Text style={{ fontFamily: 'NunitoSans-Regular' }}>
                    Product Name
                  </Text>
                </View>
                <View
                  style={{
                    flex: 2,
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}>
                  <Text style={{ fontFamily: 'NunitoSans-Regular' }}>SKU</Text>
                </View>
                <View
                  style={{
                    flex: 3,
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}>
                  <Text style={{ fontFamily: 'NunitoSans-Regular' }}>
                    Received qty
                  </Text>
                </View>
                <View
                  style={{
                    flex: 3,
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}>
                  <Text style={{ fontFamily: 'NunitoSans-Regular' }}>
                    Cost (RM)
                  </Text>
                </View>
                <View
                  style={{
                    flex: 3,
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}>
                  <Text style={{ fontFamily: 'NunitoSans-Regular' }}>
                    Subtotal (RM)
                  </Text>
                </View>
              </View>
            </View>
            <View style={{ width: '100%' }}>
              <View style={{ width: '100%' }}>
                <Text
                  style={{ fontFamily: 'NunitoSans-Bold', fontWeight: 'bold' }}>
                  Reason for stock missing (Optional)
                </Text>
              </View>
              <View
                style={{ paddingTop: 20, width: '100%', alignSelf: 'center' }}>
                <TextInput
                  underlineColorAndroid={Colors.fieldtBgColor}
                  style={{
                    backgroundColor: Colors.fieldtBgColor,
                    borderRadius: 8,
                    paddingHorizontal: 30,
                    fontSize: 14,
                    height: windowHeight * 0.1,
                  }}
                  textAlignVertical="top"
                  placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                  placeholder="Voucher description"
                  onChangeText={(text) => {
                    setState({ stockMissingDesc: text });
                  }}
                  value={stockMissingDesc}
                  multiline
                />
              </View>
            </View>
            <View
              style={{
                flexDirection: 'row',
                justifyContent: 'center',
                width: '100%',
                alignItems: 'center',
              }}>
              <TouchableOpacity
                onPress={() => {
                  setState({ deliveryScreen: false });
                }}
                style={{
                  backgroundColor: Colors.primaryColor,
                  width: windowWidth * 0.12,
                  height: windowHeight * 0.055,
                  alignItems: 'center',
                  justifyContent: 'center',
                  borderRadius: 15,
                  marginHorizontal: 20,
                }}>
                <Text
                  style={{
                    fontFamily: 'NunitoSans-Bold',
                    color: Colors.whiteColor,
                    fontSize: 18,
                  }}>
                  All Received
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                onPress={() => {
                  setState({ deliveryScreen: false });
                }}
                style={{
                  backgroundColor: Colors.fieldtBgColor,
                  width: windowWidth * 0.12,
                  height: windowHeight * 0.055,
                  alignItems: 'center',
                  justifyContent: 'center',
                  borderRadius: 15,
                  marginHorizontal: 20,
                }}>
                <Text
                  style={{
                    fontFamily: 'NunitoSans-Bold',
                    color: Colors.fieldtTxtColor,
                    fontSize: 18,
                  }}>
                  Received
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </ModalView>
    );
  });

  // 28 May - Component Listener
  // Kd Tech Team Whatsapp, 28 May, 3.46PM: Merchant will easily hit few thousand orders
  const MAX_REALTIME_DINE_IN_ORDERS = 100;
  const unsubscribeRef = useRef(null);

  useEffect(() => {
    if (!isMounted) {
      if (unsubscribeRef.current) {
        unsubscribeRef.current();
        unsubscribeRef.current = null;
      }

      setUserOrders([]);

      return;
    }

    const query = firestore()
      .collection(Collections.UserOrder)
      .where('outletId', '==', outletId)
      // .where('isReservationOrder', '==', false)
      .where('orderStatus', 'in', [
        USER_ORDER_STATUS.ORDER_RECEIVED,
        USER_ORDER_STATUS.ORDER_AUTHORIZED,
        USER_ORDER_STATUS.ORDER_REJECTED_BY_MERCHANT,
        USER_ORDER_STATUS.ORDER_PREPARING,
        USER_ORDER_STATUS.ORDER_PREPARED,
        USER_ORDER_STATUS.ORDER_DELIVERED,
      ])
      .where(
        'createdAt',
        '>=',
        !currOutlet.toggleOpenOrder
          ? moment().subtract(1, 'day').startOf('day').valueOf()
          : moment().subtract(currOutlet.openOrderDays ? currOutlet.openOrderDays : 30, 'day').startOf('day').valueOf(),
      )
      .orderBy('createdAt', 'desc')
      // .orderBy('updatedAt', 'desc')
      .limit(currOutlet.oLimitD ? currOutlet.oLimitD : 1000);

    // Store unsubscribe function for cleanup
    const unsubscribe = query.onSnapshot((snapshot) => {
      if (snapshot) {
        console.log('snapshot listened', snapshot.docs.length);
        
        const orders = [];
        snapshot.forEach(doc => {
          const data = doc.data();
          orders.push(data);
        });
        
        setUserOrders(orders);
      }
    });

    unsubscribeRef.current = unsubscribe;

    return () => {
      if (unsubscribeRef.current) {
        unsubscribeRef.current();
        unsubscribeRef.current = null;
      }
    };
  }, []);

  //////////////////////////////////////////////

  return (
    // <View style={styles.container}>
    //     <View style={styles.sidebar}>
    (<UserIdleWrapper disabled={!isMounted}>
      <View
        style={[
          styles.container,
          !isTablet()
            ? {
              transform: [{ scaleX: 1 }, { scaleY: 1 }],
            }
            : {},
          {
            ...getTransformForScreenInsideNavigation(),
          }
        ]}>
        {/* <View
          style={[
            styles.sidebar,
            !isTablet()
              ? {
                width: windowWidth * 0.08,
              }
              : {},
            switchMerchant
              ? {
                // width: '10%'
              }
              : {},
            {
              width: windowWidth * 0.08,
            }
          ]}>
          <SideBar
            navigation={props.navigation}
            selectedTab={1}
            expandOperation
          />
        </View> */}
        <View
          style={{
            flex: 1,
            backgroundColor: Colors.highlightColor,
            justifyContent: 'space-between',
          }}>
          <View style={styles.content}>
            <View
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'space-between',
              }}>
              <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                <View
                  style={
                    {
                      //padding: 10,
                      //marginBottom : '2%',
                    }
                  }>
                  <View
                    style={{
                      flexDirection: 'row',
                      // elevation: switchMerchant? 0 : 3,
                      borderRadius: 6,
                    }}>
                    <TouchableOpacity
                      style={{ marginRight: 10 }}
                      onPress={() => {
                        setViewOptions(true);
                      }}>
                      <View
                        style={
                          switchMerchant
                            ? {
                              backgroundColor: viewOptions
                                ? Colors.whiteColor
                                : Colors.fieldtBgColor,
                              elevation: viewOptions ? 5 : 0,
                              borderRadius: 6,
                              alignItems: 'center',
                              justifyContent: 'center',
                              height: 35,
                              width: 35,
                            }
                            : {
                              backgroundColor: viewOptions
                                ? Colors.whiteColor
                                : Colors.fieldtBgColor,
                              elevation: viewOptions ? 5 : 0,
                              borderRadius: 6,
                            }
                        }>
                        <Icon
                          name="view-column"
                          size={switchMerchant ? 25 : 37}
                          color={
                            viewOptions
                              ? Colors.primaryColor
                              : Colors.fieldtTxtColor
                          }
                          style={{ paddingHorizontal: 3, paddingVertical: 1 }}
                        />
                      </View>
                    </TouchableOpacity>
                    <TouchableOpacity
                      style={{ marginRight: 10 }}
                      onPress={() => {
                        setViewOptions(false);
                      }}>
                      <View
                        style={
                          switchMerchant
                            ? {
                              backgroundColor: !viewOptions
                                ? Colors.whiteColor
                                : Colors.fieldtBgColor,
                              elevation: !viewOptions ? 5 : 0,
                              borderRadius: 6,
                              alignItems: 'center',
                              justifyContent: 'center',
                              height: 35,
                              width: 35,
                            }
                            : {
                              backgroundColor: !viewOptions
                                ? Colors.whiteColor
                                : Colors.fieldtBgColor,
                              elevation: !viewOptions ? 5 : 0,
                              borderRadius: 6,
                            }
                        }>
                        <Icon
                          name="view-module"
                          size={switchMerchant ? 25 : 37}
                          color={
                            viewOptions
                              ? Colors.fieldtTxtColor
                              : Colors.primaryColor
                          }
                          style={{ paddingHorizontal: 3, paddingVertical: 1 }}
                        />
                      </View>
                    </TouchableOpacity>
                  </View>
                </View>
                {/* <DropDownPicker

                                controller={instance => setController(instance)}
                                containerStyle={{ height: 40 }}
                                items={[{ label: 'Default', value: 0 }, { label: 'Order ID', value: 1 }, { label: 'Date/Time', value: 2 }, { label: 'Name', value: 3 }, { label: 'Waiting Time', value: 4 }, { label: 'Payment Status', value: 5 }, { label: 'Total', value: 6 }]}
                                activeLabelStyle={{ color: Colors.blackColor, fontFamily: 'NunitoSans-Bold' }}
                                style={{ elevation: 3, }}
                                placeholder={"Sort By"}
                                placeholderStyle={{ color: Colors.blackColor, fontFamily: 'NunitoSans-Bold' }}
                                labelStyle={{ fontFamily: 'NunitoSans-Bold', color: Colors.blackColor, textAlign: 'left', width: '100%' }}
                                // multiple={true}
                                arrowStyle={{ fontWeight: 'bold', width: '50%' }}
                                arrowSize={23}
                                arrowColor={'black'}
                                multipleText={"Default"}
                                onChangeItem={selectedSort => {
                                    // setState({ sort: selectedSort });
                                    sortingOrders(selectedSort);
                                }}

                            /> */}

                <DropDownPicker
                  arrowColor={'black'}
                  arrowSize={switchMerchant ? 13 : 20}
                  arrowStyle={[
                    { fontWeight: 'bold' },
                    switchMerchant
                      ? {
                        // bottom: '0%',
                        height: '700%',
                        // borderWidth: 1
                      }
                      : {},
                  ]}
                  containerStyle={[
                    { height: 40 },
                    switchMerchant
                      ? {
                        height: 35,
                        // borderWidth: 1
                      }
                      : {},
                  ]}
                  style={[
                    {
                      width: 210,
                      paddingVertical: 0,
                      backgroundColor: Colors.fieldtBgColor,
                      borderRadius: 10,
                      //marginTop: '2%',
                      shadowOffset: {
                        width: 0,
                        height: 2,
                      },
                      shadowOpacity: 0.22,
                      shadowRadius: 3.22,
                      elevation: 2,

                      ...(switchMerchant && windowWidth < 775 && windowHeight < 412 && {
                        width: 150,
                      })
                    },
                    switchMerchant
                      ? {
                        // height: '1%'
                      }
                      : {},
                  ]}
                  placeholderStyle={{ color: Colors.fieldtTxtColor }}
                  items={[
                    { label: 'Prioritize Order', value: 1 },
                    { label: 'Order ID', value: 2 },
                    { label: 'Date/Time', value: 3 },
                    { label: 'Waiting Time', value: 4 },
                    //{ label: 'Payment Status', value: 5 },
                    //{ label: 'Total', value: 6 },
                  ]}
                  itemStyle={{ justifyContent: 'flex-start', zIndex: 2 }}
                  placeholder={'Sort By'}
                  onChangeItem={(selectedSort) => {
                    setSortingType(selectedSort.value);
                  }}
                  defaultValue={1}
                  dropDownMaxHeight={350}
                  dropDownStyle={{
                    width: 210,
                    height: 120,
                    backgroundColor: Colors.fieldtBgColor,
                    borderRadius: 10,
                    borderWidth: 1,
                    textAlign: 'left',
                    zIndex: 2,

                    ...(switchMerchant && windowWidth < 775 && windowHeight < 412 && {
                      width: 150,
                    })
                  }}
                  globalTextStyle={{
                    fontFamily: 'NunitoSans-SemiBold',
                    fontSize: switchMerchant ? 10 : 16,
                    color: Colors.fontDark,
                    marginLeft: 5,
                  }}

                // containerStyle={{ height: 40 }}
                // arrowColor={'black'}
                // arrowSize={20}
                // arrowStyle={{ fontWeight: 'bold' }}
                // labelStyle={{ fontFamily: 'NunitoSans-Bold', color: Colors.blackColor, textAlign: 'left', width: '100%' }}
                // style={{ width: 200, paddingVertical: 0, backgroundColor: Colors.fieldtBgColor, borderRadius: 10, marginTop: '2%' }}
                // placeholderStyle={{ color: Colors.fieldtTxtColor }}
                // itemStyle={{ justifyContent: 'flex-start', marginLeft: 5 }}
                // items={[{ label: 'Prioritize Order', value: 1 }, { label: 'Order ID', value: 2 }, { label: 'Date/Time', value: 3 }, { label: 'Waiting Time', value: 4 }, { label: 'Payment Status', value: 5 }, { label: 'Total', value: 6 }]}
                // placeholder={"Sort By"}
                // defaultValue={1}
                // onChangeItem={selectedSort => {
                //     // setState({ sort: selectedSort });
                //     // sortingOrders(selectedSort);

                //     setSortingType(selectedSort.value);
                // }}
                // dropDownMaxHeight={150}
                // dropDownStyle={{ width: 200, height: 150, backgroundColor: Colors.fieldtBgColor, borderRadius: 10, borderWidth: 1 }}
                />

                <View
                  style={{
                    //padding: 10,
                    //marginBottom: '2%',
                    marginLeft: 10,
                  }}>
                  <View
                    style={{
                      flexDirection: 'row',
                      backgroundColor: '#f2f2f4',
                      elevation: 3,
                      borderRadius: 6,
                    }}>
                    <TouchableOpacity
                      onPress={() => {
                        setIsAscending(!isAscending);
                      }}>
                      <View
                        style={
                          switchMerchant
                            ? {
                              backgroundColor: Colors.whiteColor,
                              elevation: 5,
                              borderRadius: 6,
                              alignItems: 'center',
                              justifyContent: 'center',
                              height: 35,
                              width: 35,
                            }
                            : {
                              backgroundColor: Colors.whiteColor,
                              elevation: 5,
                              borderRadius: 6,
                            }
                        }>
                        <FontAwesome
                          name={
                            isAscending ? 'sort-alpha-asc' : 'sort-alpha-desc'
                          }
                          size={switchMerchant ? 15 : 25}
                          // color={viewOptions ? Colors.primaryColor : Colors.fieldtTxtColor}
                          color={Colors.primaryColor}
                          style={{ paddingHorizontal: 10, paddingVertical: 6 }}
                        />
                      </View>
                    </TouchableOpacity>
                  </View>
                </View>

                {/* <View style={{ flexDirection: 'row', marginLeft: 10, flex: 1 }}>
                                <ScrollView style={{ flexDirection: 'row' }} horizontal={true}>
                                    {selectedCategoriesIds.map(category => {
                                        return (
                                            <TouchableOpacity
                                                style={{ padding: 6 }}
                                                onPress={() => {

                                                    category.active = !category.active;
                                                    setState({
                                                        categories: categories
                                                    })
                                                    setCategoriesFunc();
                                                    //// console.log("selectedCat", selectedCat)
                                                }}>
                                                <View style={{ flexDirection: 'row', elevation: 3, backgroundColor: category.active ? Colors.primaryColor : Colors.whiteColor, height: windowWidth * 0.03, alignItems: 'center', justifyContent: 'center', borderRadius: 9, marginHorizontal: 1 }}>
                                                    <Text style={{ fontFamily: 'NunitoSans-Bold', fontFamily: 'NunitoSans-Bold', color: category.active ? Colors.whiteColor : Colors.primaryColor, marginLeft: 15 }}>{category.label}</Text>
                                                    <TouchableOpacity
                                                        style={{ marginRight: 15 }}
                                                        onPress={() => {
                                                            if (category.active) { category.active = !category.active; }
                                                            setState({
                                                                categories: categories
                                                            })
                                                            setCategoriesFunc();
                                                            controller.selectItem([category.value])
                                                        }}
                                                    >
                                                        <AntDesign name={"close"} size={15} color={category.active ? Colors.whiteColor : Colors.primaryColor} />
                                                    </TouchableOpacity>
                                                </View>
                                            </TouchableOpacity>
                                        )
                                    })}
                                </ScrollView>
                            </View> */}

                {/* 2022-11-24 - Printer selection filter */}

                {/* {
                  (
                    selectedPrinterAreaList.every((val) =>
                      printerAreaDropdownList
                        .map((area) => area.value)
                        .includes(val)
                    )
                    ||
                    selectedPrinterAreaList.length === 0
                  )
                    ?
                    <DropDownPicker
                      arrowColor={'black'}
                      arrowSize={switchMerchant ? 13 : 20}
                      arrowStyle={[
                        { fontWeight: 'bold' },
                        switchMerchant
                          ? {
                            // bottom: '0%',
                            height: '700%',
                            // borderWidth: 1
                          }
                          : {},
                      ]}
                      containerStyle={[
                        { height: 40 },
                        switchMerchant
                          ? {
                            height: 35,
                            // borderWidth: 1
                          }
                          : {},
                      ]}
                      style={[
                        {
                          width: 210,
                          paddingVertical: 0,
                          backgroundColor: Colors.fieldtBgColor,
                          borderRadius: 10,
                          //marginTop: '2%',
                          shadowOffset: {
                            width: 0,
                            height: 2,
                          },
                          shadowOpacity: 0.22,
                          shadowRadius: 3.22,
                          elevation: 2,

                          marginLeft: 10,

                          ...(switchMerchant && windowWidth < 775 && windowHeight < 412 && {
                            width: 170,
                          })
                        },
                        switchMerchant
                          ? {
                            // height: '1%'
                          }
                          : {},
                      ]}
                      placeholderStyle={{ color: Colors.fieldtTxtColor }}
                      items={printerAreaDropdownList}
                      itemStyle={{ justifyContent: 'flex-start', zIndex: 2 }}
                      multipleText={'%d area(s) selected'}
                      customTickIcon={(press) => (
                        <Ionicon
                          name={'checkbox-outline'}
                          color={
                            press
                              ? Colors.fieldtBgColor
                              : Colors.primaryColor
                          }
                          size={25}
                        />
                      )}
                      onChangeItem={(items) => {
                        setSelectedPrinterAreaList(items);
                      }}
                      // defaultValue={1}
                      defaultValue={selectedPrinterAreaList}
                      multiple
                      dropDownMaxHeight={350}
                      dropDownStyle={{
                        width: 210,
                        height: 120,
                        backgroundColor: Colors.fieldtBgColor,
                        borderRadius: 10,
                        borderWidth: 1,
                        textAlign: 'left',
                        zIndex: 2,
                        marginLeft: 10,

                        ...(switchMerchant && windowWidth < 775 && windowHeight < 412 && {
                          width: 170
                        })
                      }}
                      globalTextStyle={{
                        fontFamily: 'NunitoSans-SemiBold',
                        fontSize: switchMerchant ? 10 : 16,
                        color: Colors.fontDark,
                        marginLeft: 5,
                      }}
                    />
                    :
                    <></>
                } */}

                {/* 2025-01-10 - Category filter */}

                {console.log('kds > selectedOutletCategoryList')}
                {console.log(selectedOutletCategoryList)}
                {console.log('kds > outletCategoryDropdownList.map(cat => cat.value)')}
                {console.log(outletCategoryDropdownList.map(cat => cat.value))}
                {console.log(selectedOutletCategoryList.every(val => {
                  return outletCategoryDropdownList.map(cat => cat.value)
                    .includes(val)
                }))}
                {
                  (
                    selectedOutletCategoryList.every(val => {
                      return outletCategoryDropdownList.map(cat => cat.value)
                        .includes(val)
                    })
                    ||
                    selectedOutletCategoryList.length === 0
                  )
                    ?
                    <DropDownPicker
                      arrowColor={'black'}
                      arrowSize={switchMerchant ? 13 : 20}
                      arrowStyle={[
                        { fontWeight: 'bold' },
                        switchMerchant
                          ? {
                            // bottom: '0%',
                            height: '700%',
                            // borderWidth: 1
                          }
                          : {},
                      ]}
                      containerStyle={[
                        { height: 40 },
                        switchMerchant
                          ? {
                            height: 35,
                            // borderWidth: 1
                          }
                          : {},
                      ]}
                      style={[
                        {
                          width: 250,
                          paddingVertical: 0,
                          backgroundColor: Colors.fieldtBgColor,
                          borderRadius: 10,
                          //marginTop: '2%',
                          shadowOffset: {
                            width: 0,
                            height: 2,
                          },
                          shadowOpacity: 0.22,
                          shadowRadius: 3.22,
                          elevation: 2,

                          marginLeft: 10,

                          ...(switchMerchant && windowWidth < 775 && windowHeight < 412 && {
                            width: 170,
                          })
                        },
                        switchMerchant
                          ? {
                            // height: '1%'
                          }
                          : {},
                      ]}
                      placeholder={'Please select a category'}
                      placeholderStyle={{ color: Colors.fieldtTxtColor }}
                      items={outletCategoryDropdownList}
                      itemStyle={{ justifyContent: 'flex-start', zIndex: 2 }}
                      multipleText={'%d categories selected'}
                      customTickIcon={(press) => (
                        <Ionicon
                          name={'checkbox-outline'}
                          color={
                            press
                              ? Colors.fieldtBgColor
                              : Colors.primaryColor
                          }
                          size={25}
                        />
                      )}
                      onChangeItem={(items) => {
                        // setSelectedOutletCategoryList(items);
                        CommonStore.update((s) => {
                          s.selectedOutletCategoryList = items;
                        });
                        APILocal.modifySelectedCatList(outletId, items);
                      }}
                      // defaultValue={1}
                      defaultValue={selectedOutletCategoryList}
                      multiple
                      dropDownMaxHeight={350}
                      dropDownStyle={{
                        width: 250,
                        height: 150,
                        backgroundColor: Colors.fieldtBgColor,
                        borderRadius: 10,
                        borderWidth: 1,
                        textAlign: 'left',
                        zIndex: 2,
                        marginLeft: 10,

                        ...(switchMerchant && windowWidth < 775 && windowHeight < 412 && {
                          width: 170
                        })
                      }}
                      searchable
                      globalTextStyle={{
                        fontFamily: 'NunitoSans-SemiBold',
                        fontSize: switchMerchant ? 10 : 16,
                        color: Colors.fontDark,
                        marginLeft: 5,
                      }}
                    />
                    :
                    <></>
                }
              </View>
              <View
                style={[
                  {
                    flexDirection: 'row',
                    //width: "25%",
                    //justifyContent: 'flex-end',
                    borderRadius: 3,
                    // borderWidth: 1,
                    alignItems: 'center',
                  },
                  switchMerchant
                    ? {
                      height: '60%',
                    }
                    : {},
                ]}>
                {/* <TouchableOpacity style={{ backgroundColor: Colors.whiteColor, elevation: 3, width: '60%', borderRadius: 3, height: 35, alignItems: 'center', flexDirection: 'row', paddingHorizontal: 5, borderColor: '#E5E5E5', borderWidth: 1 }}>
                                <FIcon name="search" size={15} color={Colors.primaryColor} />
                                <TextInput style={{ paddingLeft: 10, fontFamily: 'NunitoSans-Regular', borderRadius: 4, paddingBottom: 0, paddingTop: 0, width: '90%' }}
                                    onChangeText={text => setSearch(text)}
                                    placeholder="Search"
                                    placeholderTextColor={Colors.fieldtTxtColor}
                                />
                            </TouchableOpacity> */}

                <View
                  style={[
                    {
                      width: switchMerchant ? 200 : 250,
                      height: switchMerchant ? 35 : 40,
                      backgroundColor: 'white',
                      borderRadius: 5,
                      flexDirection: 'row',
                      alignContent: 'center',
                      alignItems: 'center',
                      shadowColor: '#000',
                      shadowOffset: {
                        width: 0,
                        height: 2,
                      },
                      shadowOpacity: 0.22,
                      shadowRadius: 3.22,
                      elevation: 3,
                      borderWidth: 1,
                      borderColor: '#E5E5E5',

                      ...(switchMerchant && windowWidth < 775 && windowHeight < 412 && {
                        width: 170,
                      })
                    },
                  ]}>
                  <Feather
                    name="search"
                    size={switchMerchant ? 13 : 18}
                    color={Colors.primaryColor}
                    style={{ marginLeft: switchMerchant ? 8 : 15 }}
                  />
                  <TextInput
                    // editable={!loading}
                    underlineColorAndroid={Colors.whiteColor}
                    style={
                      switchMerchant
                        ? {
                          width: 180,
                          fontSize: 10,
                          // borderWidth: 1,
                          fontFamily: 'NunitoSans-Regular',
                          height: '100%',
                          height: 45,
                        }
                        : {
                          width: 220,
                          fontSize: 15,
                          fontFamily: 'NunitoSans-Regular',
                          paddingLeft: 5,
                          height: 45,
                        }
                    }
                    clearButtonMode="while-editing"
                    placeholder=" Search"
                    placeholderTextColor={Platform.select({
                      ios: '#a9a9a9',
                    })}
                    onChangeText={(text) => {
                      setSearch(text);
                    }}
                    value={search}
                  />
                </View>
                <View
                  style={
                    switchMerchant
                      ? {
                        // borderWidth: 1,
                        // height: '150%',
                        alignContent: 'center',
                        justifyContent: 'center',
                        height: windowHeight * 0.1,
                        // borderWidth: 1
                      }
                      : {}
                  }>
                  <TouchableOpacity
                    onPress={() => {
                      setShowSummary(!showSummary);
                    }}
                    style={
                      switchMerchant
                        ? {
                          width: 35,
                          backgroundColor: Colors.whiteColor,
                          elevation: 3,
                          height: switchMerchant ? 35 : 40,
                          borderRadius: 7,
                          justifyContent: 'center',
                          alignItems: 'center',
                          marginLeft: 10,
                          paddingHorizontal: 6,
                          borderWidth: 1,
                          borderColor: '#E5E5E5',
                        }
                        : {
                          backgroundColor: Colors.whiteColor,
                          elevation: 3,
                          height: switchMerchant ? 35 : 40,
                          borderRadius: 7,
                          justifyContent: 'center',
                          alignItems: 'center',
                          marginLeft: 10,
                          paddingHorizontal: 6,
                          borderWidth: 1,
                          borderColor: '#E5E5E5',
                        }
                    }>
                    <Icon
                      name="clipboard-list-outline"
                      size={switchMerchant ? 20 : 26}
                      color={Colors.darkBgColor}
                    />
                  </TouchableOpacity>
                </View>
              </View>
            </View>

            {userOrdersActive.length == 0 ? (
              <View style={{ flexDirection: 'row', zIndex: -1 }}>
                <View
                  style={{
                    flex: 3,
                    paddingTop: viewOptions ? 0 : '2%',
                    height: switchMerchant
                      ? windowHeight * 0.54
                      : windowHeight >= 1133
                        ? windowHeight * 0.73
                        : windowHeight * 0.68,
                    justifyContent: 'center',
                    alignItems: 'center',
                  }}>
                  <Text
                    style={{
                      fontFamily: 'NunitoSans-Bold',
                      fontSize: 20,
                      color: Colors.fieldtTxtColor,
                    }}>
                    No pending orders
                  </Text>
                </View>
              </View>
            ) : (
              <View style={{ flexDirection: 'row', zIndex: -1 }}>
                <View
                  style={
                    switchMerchant
                      ? {
                        flex: 3,
                        paddingTop: viewOptions ? 0 : '2%',
                        height: switchMerchant
                          ? windowHeight * 0.54
                          : windowHeight >= 1133
                            ? windowHeight * 0.73
                            : windowHeight * 0.68,
                      }
                      : {
                        flex: 3,
                        paddingTop: viewOptions ? 0 : '2%',
                        height: switchMerchant
                          ? windowHeight * 0.54
                          : windowHeight >= 1133
                            ? windowHeight * 0.73
                            : windowHeight * 0.68,
                      }
                  }>
                  {viewOptions ? (
                    <FlatList
                      ref={kitchenOrderRef}
                      // contentContainerStyle={styles.flatlist}
                      showsHorizontalScrollIndicator={false}
                      horizontal
                      //nestedScrollEnabled={true}
                      numRows={2}
                      contentContainerStyle={[
                        styles.flatlist,
                        {
                          paddingVertical: 4,
                        },
                      ]}
                      data={sortedUserOrdersActive}
                      // .slice(0, viewOptions ? (showSummary ? 3 : 16) : (showSummary ? 16 : 6)) : userOrdersActive}
                      renderItem={renderKitchenOrder}
                      keyExtractor={(item) => item.uniqueId}
                      initialNumToRender={4}
                      maxToRenderPerBatch={1}
                      windowSize={3}
                      removeClippedSubviews={true}
                    />
                  ) : null}
                  {!viewOptions ? (
                    <FlatList
                      ref={kitchenOrderRef}
                      // contentContainerStyle={styles.flatlist}
                      showsHorizontalScrollIndicator={false}
                      //horizontal={true}
                      nestedScrollEnabled
                      numRows={2}
                      numColumns={3}
                      contentContainerStyle={[
                        styles.flatlistWrap,
                        {
                          // paddingVertical: 4,
                        },
                      ]}
                      data={sortedUserOrdersActive1}
                      // .slice(0, viewOptions ? (showSummary ? 3 : 16) : (showSummary ? 6 : 6)) : userOrdersActive}
                      renderItem={renderKitchenOrder}
                      keyExtractor={(item) => item.uniqueId}
                      initialNumToRender={4}
                      maxToRenderPerBatch={1}
                      windowSize={3}
                      removeClippedSubviews={true}
                    />
                  ) : null}
                </View>
                <View
                  style={{
                    flex: showSummary ? 1.34 : 0,
                    borderRadius: 10,
                  }}>
                  {!showSummary ? null : (
                    <>
                      <View
                        style={{
                          //marginHorizontal: 5,
                          marginTop: 10,
                          //padding: 5,
                          width: '100%',
                          height: switchMerchant
                            ? windowHeight * 0.53
                            : Platform.OS == 'android'
                              ? windowHeight >= 700
                                ? windowHeight * 0.63
                                : windowHeight * 0.6
                              : windowHeight * 0.62,
                          marginRight: 10,
                          shadowColor: '#000',
                          shadowOffset: {
                            width: 0,
                            height: 3,
                          },
                          shadowOpacity: 0.32,
                          shadowRadius: 6.22,
                          elevation: 3,
                        }}>
                        <View
                          style={{
                            backgroundColor: Colors.darkBgColor,
                            borderTopLeftRadius: 15,
                            borderTopRightRadius: 15,
                            height: switchMerchant
                              ? windowHeight * 0.12
                              : '14%',
                            borderWidth: 1,
                            borderColor: '#E5E5E5',
                          }}>
                          <View
                            style={{
                              alignItems: 'center',
                              justifyContent: 'center',
                              paddingHorizontal: 10,
                            }}>
                            <View
                              style={{
                                padding: 5,
                                borderBottomWidth: StyleSheet.hairlineWidth,
                                borderBottomColor: Colors.whiteColor,
                                alignSelf: 'stretch',
                                alignItems: 'center',
                              }}>
                              <Text
                                style={[
                                  {
                                    fontFamily: 'NunitoSans-Bold',
                                    paddingHorizontal: 13,
                                    color: Colors.whiteColor,
                                    fontSize: 20,
                                  },
                                  switchMerchant
                                    ? {
                                      fontSize: 10,
                                    }
                                    : {},
                                ]}>
                                Summary
                              </Text>
                            </View>
                            <View style={{ padding: 5 }}>
                              <Text
                                style={{
                                  fontFamily: 'NunitoSans-Bold',
                                  paddingHorizontal: 13,
                                  color: Colors.whiteColor,
                                  fontSize: switchMerchant ? 8 : 12,
                                }}>
                                List Of Items To Prepare
                              </Text>
                            </View>
                          </View>
                        </View>

                        <View
                          style={{
                            justifyContent: 'space-between',
                            flex: 1,
                            backgroundColor: '#E5E5E5',
                            borderWidth: 1,
                            borderColor: '#E5E5E5',
                          }}>
                          {isLoading ? (
                            <View
                              style={{
                                width: '100%',
                                height: '100%',
                                justifyContent: 'center',
                                alignItems: 'center',
                              }}>
                              <ActivityIndicator
                                size={'large'}
                                color={Colors.primaryColor}
                              />
                            </View>
                          ) : (
                            <ScrollView
                              showsVerticalScrollIndicator={false}
                              style={{ paddingBottom: 50 }}
                              nestedScrollEnabled>
                              <FlatList
                                // contentContainerStyle={styles.flatlist}
                                nestedScrollEnabled
                                data={
                                  search === ''
                                    ? summary
                                    : summary.filter((item) =>
                                      item.name
                                        .toLowerCase()
                                        .match(search.toLowerCase()),
                                    )
                                }
                                renderItem={renderSummary}
                                keyExtractor={(item) => item.key}
                              />

                              <FlatList
                                // contentContainerStyle={styles.flatlist}
                                nestedScrollEnabled
                                data={
                                  search === ''
                                    ? summaryDelivered
                                    : summaryDelivered.filter((item) =>
                                      item.name
                                        .toLowerCase()
                                        .match(search.toLowerCase()),
                                    )
                                }
                                renderItem={renderSummaryDelivered}
                                keyExtractor={(item) => item.key}
                              />

                              <FlatList
                                // contentContainerStyle={styles.flatlist}
                                nestedScrollEnabled
                                data={
                                  search === ''
                                    ? summaryCancelled
                                    : summaryCancelled.filter((item) =>
                                      item.name
                                        .toLowerCase()
                                        .match(search.toLowerCase()),
                                    )
                                }
                                renderItem={renderSummaryCancelled}
                                keyExtractor={(item) => item.key}
                              />
                            </ScrollView>
                          )}
                        </View>

                        {/* <View style={{ justifyContent: 'flex-end', flexDirection: 'row', position: 'absolute', top: 550, left: 130, zIndex: 2 }}>
                                            <TouchableOpacity
                                                style={{
                                                    width: 80,
                                                    backgroundColor: Colors.primaryColor,
                                                    height: 35,
                                                    alignItems: 'center',
                                                    justifyContent: 'center',
                                                    borderRadius: 8,
                                                }}
                                                onPress={() => { }}>
                                                <Text style={{ color: Colors.fieldtBgColor, textAlign: 'center' }}>Confrim</Text>
                                            </TouchableOpacity>                                            
                                        </View> */}
                        <View
                          style={{
                            borderBottomRightRadius: 15,
                            borderBottomLeftRadius: 15,
                            height: switchMerchant ? 35 : '9%',
                            backgroundColor: Colors.darkBgColor,
                            // backgroundColor: 'red'
                          }}
                        />
                      </View>
                    </>
                  )}

                  {currTickListType !== TICK_LIST_TYPE.UNTICK &&
                    showSummary &&
                    !isLoading ? (
                    <View
                      style={[{
                        bottom: switchMerchant
                          ? -windowHeight * 0.02
                          : windowHeight * 0.025,
                        // backgroundColor: 'red',
                        // backgroundColor: Colors.fieldtBgColor,
                        position: 'absolute',
                        width: '100%',
                        height: switchMerchant
                          ? windowHeight * 0.15
                          : windowHeight * 0.08,

                        flexDirection: 'row',
                        alignItems: 'center',

                        paddingBottom: 10,
                        paddingTop: 10,
                        paddingHorizontal: 15,
                      }, Platform.OS === 'ios' && !switchMerchant ? {
                        bottom: windowHeight * 0.035,
                      } : {}]}>
                      {currTickListType !== TICK_LIST_TYPE.CANCELLED ? (
                        <>
                          <TouchableOpacity
                            style={
                              deliverButtonStyle
                            }
                            onPress={() => {
                              if (currTickListType === TICK_LIST_TYPE.NORMAL) {
                                deliverUserOrder();
                              } else {
                                deliverUserOrderUndo();
                              }
                            }}>
                            <Text
                              style={
                                deliverButtonTextStyle
                              }>
                              {currTickListType === TICK_LIST_TYPE.NORMAL
                                ? 'DELIVER'
                                : 'UNDO'}
                            </Text>
                          </TouchableOpacity>

                          <View
                            style={{
                              width:
                                currTickListType === TICK_LIST_TYPE.NORMAL
                                  ? '5%'
                                  : '10%',
                            }} />
                        </>
                      ) : (
                        <></>
                      )}

                      {/* //////////////////////////////////////////////////////////////////// */}

                      {currTickListType !== TICK_LIST_TYPE.DELIVERED ? (
                        <>
                          {
                            (currOutlet && currOutlet.privileges &&
                              currOutlet.privileges.includes(PRIVILEGES_NAME.REJECT_ITEM))
                              && privileges && privileges.includes(PRIVILEGES_NAME.REJECT_ITEM) ?
                              <TouchableOpacity
                                style={
                                  rejectButtonStyle
                                }
                                onPress={() => {
                                  if (currTickListType === TICK_LIST_TYPE.NORMAL) {
                                    cancelUserOrder();
                                  } else {
                                    undoUserOrderCancelled();
                                  }
                                }}>
                                <Text
                                  style={{
                                    color: Colors.whiteColor,
                                    fontFamily: 'NunitoSans-SemiBold',
                                    fontSize: switchMerchant ? 10 : 14,
                                    bottom: switchMerchant
                                      ? Platform.OS === 'android'
                                        ? 1
                                        : 0
                                      : 0,
                                  }}>
                                  {currTickListType === TICK_LIST_TYPE.NORMAL
                                    ? 'REJECT'
                                    : 'UNDO'}
                                </Text>
                              </TouchableOpacity>
                              :
                              <>
                                <View
                                  style={
                                    switchMerchant
                                      ? [
                                        {
                                          width:
                                            currTickListType === TICK_LIST_TYPE.NORMAL
                                              ? '30%'
                                              : '45%',
                                          height: switchMerchant
                                            ? windowHeight * 0.05
                                            : windowHeight * 0.04,

                                          alignItems: 'center',
                                          justifyContent: 'center',

                                          top: '1%',
                                        },
                                      ]
                                      : [
                                        {
                                          width:
                                            currTickListType === TICK_LIST_TYPE.NORMAL
                                              ? '30%'
                                              : '45%',
                                          height: switchMerchant
                                            ? windowHeight * 0.05
                                            : windowHeight * 0.04,

                                          alignItems: 'center',
                                          justifyContent: 'center',
                                        },
                                      ]
                                  } />
                              </>}

                          <View
                            style={{
                              width:
                                currTickListType === TICK_LIST_TYPE.NORMAL
                                  ? '5%'
                                  : '10%',
                            }} />
                        </>
                      ) : (
                        <></>
                      )}

                      {/* //////////////////////////////////////////////////////////////////// */}

                      <TouchableOpacity
                        style={
                          switchMerchant
                            ? [
                              {
                                width:
                                  currTickListType === TICK_LIST_TYPE.NORMAL
                                    ? '30%'
                                    : '45%',
                                height: switchMerchant
                                  ? windowHeight * 0.05
                                  : windowHeight * 0.04,

                                alignItems: 'center',
                                justifyContent: 'center',

                                backgroundColor: Colors.lightGrey,
                                borderRadius: 8,

                                shadowColor: '#000',
                                shadowOffset: {
                                  width: 0,
                                  height: 1,
                                },
                                shadowOpacity: 0.22,
                                shadowRadius: 2.22,
                                elevation: 3,
                                top: '1%',
                              },
                            ]
                            : [
                              {
                                width:
                                  currTickListType === TICK_LIST_TYPE.NORMAL
                                    ? '30%'
                                    : '45%',
                                height: switchMerchant
                                  ? windowHeight * 0.05
                                  : windowHeight * 0.04,

                                alignItems: 'center',
                                justifyContent: 'center',

                                backgroundColor: Colors.lightGrey,
                                borderRadius: 8,

                                shadowColor: '#000',
                                shadowOffset: {
                                  width: 0,
                                  height: 1,
                                },
                                shadowOpacity: 0.22,
                                shadowRadius: 2.22,
                                elevation: 3,
                              },
                            ]
                        }
                        onPress={() => {
                          CommonStore.update((s) => {
                            s.summaryCheckDict = {};
                            s.summaryCancelledCheckDict = {};
                            s.summaryDeliveredCheckDict = {};
                          });
                        }}>
                        <Text
                          style={{
                            color: Colors.fontDark,
                            fontFamily: 'NunitoSans-SemiBold',
                            fontSize: switchMerchant ? 10 : 16,
                          }}>
                          CANCEL
                        </Text>
                      </TouchableOpacity>
                    </View>
                  ) : (
                    <></>
                  )}
                </View>
              </View>
            )}
            <FlatList
              style={{
                position: 'absolute',
                bottom: 0,
                backgroundColor: 'red',
              }}
              horizontal
              contentContainerStyle={{ flexDirection: 'row', paddingTop: 340 }}
              data={
                sortingType === 0
                  ? userOrdersActive.slice(0).sort((a, b) => {
                    return b.isPrioritizedOrder - a.isPrioritizedOrder;

                    //return moment(b.orderDate).add(b.totalPrepareTime, 'second').valueOf() - moment(a.orderDate).add(a.totalPrepareTime, 'second').valueOf();
                  })
                  : userOrdersActive
              }
              renderItem={renderFooterExtend}
              keyExtractor={(item) => item.uniqueId}
            />
          </View>

          <View
            style={{
              width: '100%',
              height: windowHeight * 0.03,
              backgroundColor: Colors.highlightColor,
              shadowColor: '#000',
              shadowOffset: {
                width: 0,
                height: 2,
              },
              shadowOpacity: 0.22,
              shadowRadius: 3.22,
              elevation: 3,

              // borderTopColor: 'black',
              borderWidth: 1,
              borderBottomWidth: 0,
              borderLeftWidth: 0,
              borderRightWidth: 0,
            }}
          />

          <FlatList
            style={
              switchMerchant
                ? {
                  position: 'absolute',
                  bottom: isSunmiDevice() ? '6%' : '0%',
                  width: '100%',
                  shadowColor: '#000',
                  shadowOffset: {
                    width: 0,
                    height: 2,
                  },
                  shadowOpacity: 0.22,
                  shadowRadius: 3.22,
                  // elevation: 3,
                  // borderWidth: 1,
                  height: '18%',
                }
                : {
                  position: 'absolute',
                  bottom: isSunmiDevice() ? '6%' : '0%',
                  width: '100%',
                  shadowColor: '#000',
                  shadowOffset: {
                    width: 0,
                    height: 2,
                  },
                  shadowOpacity: 0.22,
                  shadowRadius: 3.22,
                  // elevation: 3,
                  // borderWidth: 1,
                  height: windowHeight * 0.15,
                }
            }
            horizontal
            contentContainerStyle={{ flexDirection: 'row' }}
            // data={sortingType === 0 ? userOrdersActive.slice(0).sort((a, b) => {
            //     return b.isPrioritizedOrder - a.isPrioritizedOrder;

            //     //return moment(b.orderDate).add(b.totalPrepareTime, 'second').valueOf() - moment(a.orderDate).add(a.totalPrepareTime, 'second').valueOf();
            // }) : userOrdersActive}
            data={userOrdersPrepared.slice(0).sort((a, b) => {
              return a.updatedAt - b.updatedAt;

              //return moment(b.orderDate).add(b.totalPrepareTime, 'second').valueOf() - moment(a.orderDate).add(a.totalPrepareTime, 'second').valueOf();
            })}
            renderItem={renderFooter}
            keyExtractor={(item) => item.uniqueId}
          />
        </View>
        {<UndoUserOrderModal visible={showUndoUserOrderModal} />}
        {<DeliveryScreenModal visible={deliveryScreen} />}

      </View>
    </UserIdleWrapper>)
  );
});

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'black',
    flexDirection: 'row',
  },
  headerLogo: {
    width: 112,
    height: 25,
    marginLeft: 10,
  },
  list: {
    paddingVertical: 20,
    paddingHorizontal: 30,
    flexDirection: 'row',
    alignItems: 'center',
  },
  listItem: {
    marginLeft: 20,
    color: Colors.descriptionColor,
    fontSize: 16,
  },
  sidebar: {
    //width: windowWidth * Styles.sideBarWidth,
    // shadowColor: '#000',
    // shadowOffset: {
    //   width: 0,
    //   height: 8,
    // },
    // shadowOpacity: 0.44,
    // shadowRadius: 10.32,

    // elevation: 16,
  },
  flatlist: {
    flexDirection: 'row',
    //backgroundColor: 'red',
    //flexWrap: 'wrap',
  },
  flatlistWrap: {
    flexDirection: 'column',
    //flexWrap: 'wrap',
    //backgroundColor: 'red',
    //flex: 1
  },
  content: {
    paddingHorizontal: 16,
    paddingTop: 10,
  },
  circleIcon: {
    width: 30,
    height: 30,
    // resizeMode: 'contain',
    marginRight: 10,
    alignSelf: 'center',
  },
  headerLeftStyle: {
    width: Dimensions.get('screen').width * 0.17,
    justifyContent: 'center',
    alignItems: 'center',
  },

  modalContainer: {
    flex: 1,
    backgroundColor: Colors.modalBgColor,
    alignItems: 'center',
    justifyContent: 'center',
  },
  modalView: {
    height: Dimensions.get('screen').width * 0.3,
    width: Dimensions.get('screen').width * 0.4,
    backgroundColor: Colors.whiteColor,
    borderRadius: 12,
    padding: Dimensions.get('screen').width * 0.03,
    alignItems: 'center',
    justifyContent: 'center',
  },
  closeButton: {
    position: 'absolute',
    right: Dimensions.get('screen').width * 0.02,
    top: Dimensions.get('screen').width * 0.02,

    elevation: 1000,
    zIndex: 1000,
  },
  modalTitle: {
    alignItems: 'center',
    top: '10%',
    position: 'absolute',
  },
  modalBody: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  modalTitleText: {
    fontFamily: 'NunitoSans-Bold',
    textAlign: 'center',
    fontSize: 24,
  },
  modalDescText: {
    fontFamily: 'NunitoSans-SemiBold',
    fontSize: 18,
    color: Colors.fieldtTxtColor,
  },
  modalBodyText: {
    flex: 1,
    fontFamily: 'NunitoSans-SemiBold',
    fontSize: 25,
    width: '20%',
  },
  modalSaveButton: {
    width: 130,
    backgroundColor: Colors.fieldtBgColor,
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 8,

    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 3.22,
    elevation: 1,

    marginVertical: 10,
  },
  textInput9: {
    fontFamily: 'NunitoSans-Regular',
    // width: 430,
    height: 80,
    //flex: 1,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 5,
    paddingHorizontal: 15,
  },
});
export default KitchenScreen;
