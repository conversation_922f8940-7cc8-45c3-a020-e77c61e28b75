import Colors from '../constant/Colors';
import headerLogo from "../assets/image/logo.png";
//import { isMobile } from "../util/common";
import personicon from "../assets/image/default-profile.png";
import { UserStore } from '../store/userStore';
import { CommonStore } from '../store/commonStore';
import React, {
  useState,
  Component,
} from 'react';
import {
  StyleSheet,
  View,
  Text,
  Dimensions,
  TouchableOpacity,
  useWindowDimensions,
} from 'react-native';
import { set } from 'react-native-reanimated';
import { MerchantStore } from "../store/merchantStore";
import AsyncStorage from "@react-native-async-storage/async-storage";
import ApiClient from "../util/ApiClient";
import API from "../constant/API";
import { prefix } from "../constant/env";
import * as User from "../util/User";
import { useLinkTo, useRoute } from "@react-navigation/native";
import { Image } from 'react-native';

const TopBar = (props) => {

  const { navigation } = props;
  const linkTo = useLinkTo();

  const userName = UserStore.useState((s) => s.name);
  const [switchMerchant, setSwitchMerchant] = useState(false);
  const { width: windowWidth, height: windowHeight } = useWindowDimensions();

  const outletSelectDropdownView = CommonStore.useState(
    (s) => s.outletSelectDropdownView,
  );

  const currPage = CommonStore.useState(s => s.currPage);

  const [menu, setMenu] = useState(false);
  const role = UserStore.useState((s) => s.role);
  const currOutlet = MerchantStore.useState((s) => s.currOutlet);

  let maxCharacters;

  if (windowWidth >= 350 && windowWidth < 370) {
    maxCharacters = 11;
  } else if (windowWidth >= 370 && windowWidth < 385) {
    maxCharacters = 13;
  } else if (windowWidth >= 385 && windowWidth < 400) {
    maxCharacters = 15;
  } else if (windowWidth >= 400 && windowWidth < 418) {
    maxCharacters = 17;
  } else if (windowWidth >= 418 && windowWidth < 435) {
    maxCharacters = 19;
  } else {
    maxCharacters = 50;
  }

  let truncatedUserName;

  if (userName.length > maxCharacters) {
    let truncatedString = userName.substring(0, maxCharacters); // Get substring up to maxCharacters

    // Remove trailing spaces from the substring
    while (truncatedString.charAt(truncatedString.length - 1) === ' ') {
      truncatedString = truncatedString.slice(0, -1);
    }

    truncatedUserName = truncatedString + '...'; // Add ellipsis
  } else {
    truncatedUserName = userName;
  }

  const logOutButton = async () => {
    if (window.confirm("Logout: Do you want to logout?")) {
      UserStore.update((s) => {
        s.avatar = '';
        s.dob = null;
        s.email = '';
        s.gender = '';
        s.name = '';
        s.number = '';
        s.outletId = '';
        s.race = '';
        s.state = '';
        s.uniqueName = '';
        s.updatedAt = null;
        s.merchantId = '';
        s.role = '';
        s.refreshToken = '';
        s.firebaseUid = '';
        s.privileges = [];
      });

      MerchantStore.update((s) => {
        s.allOutlets = [];
        s.allOutletsDict = {};
        s.currOutletId = '';
        s.currOutlet = {
          uniqueId: '',
          privileges: [],
        };
      });

      await AsyncStorage.multiRemove([
        'accessToken',
        'userData',
        'refreshToken',

        'merchantLogoUrl',

        // 'lastActivity',

        'email',
        'password',

        'printerList',

        'supportCodeData',

        // 'isPrintingKDAndOS',
      ]);

      const merchantId = await AsyncStorage.getItem('merchantId');
      const currOutletId = await AsyncStorage.getItem('currOutletId');
      const tokenFcm = await AsyncStorage.getItem('tokenFcm');

      const body = {
        role: role,
        merchantId: merchantId,
        outletId: currOutlet.uniqueId,
        tokenFcm: tokenFcm,
      };

      ApiClient.POST(API.logoutUser, body).then((result) => {
        User.setlogin(false);
        User.setMerchantId(null);
        User.setUserData(null);
        User.setUserId(null);
        User.setRefreshToken(null);
        User.setOutletId(null);
        // User.getRefreshMainScreen();

        global.privileges = [];

        linkTo && linkTo(`${prefix}/login`);
      });
    }

    return;
  };

  /*return(
  navigation.setOptions({
      headerLeft: () => (
        <View
          // onPress={() => {
          //   if (isAlphaUser || true) {
          //     navigation.navigate('MenuOrderingScreen');

          //     CommonStore.update((s) => {
          //       s.currPage = 'MenuOrderingScreen';
          //       s.currPageStack = [...currPageStack, 'MenuOrderingScreen'];
          //     });
          //   }
          //   else {
          //     navigation.navigate('Table');

          //     CommonStore.update((s) => {
          //       s.currPage = 'Table';
          //       s.currPageStack = [...currPageStack, 'Table'];
          //     });
          //   }
          //   if (expandTab !== EXPAND_TAB_TYPE.OPERATION) {
          //     CommonStore.update((s) => {
          //       s.expandTab = EXPAND_TAB_TYPE.OPERATION;
          //     });
          //   }
          // }}
          style={[styles.headerLeftStyle, {
            width: windowWidth * 0.17,
              ...isMobile() && {
                display: 'none',
              },
          }]}>
          <img src={headerLogo} width={124} height={26} />
        </View>
      ),
      headerTitle: () => (
        <View
          style={[
            {
              justifyContent: 'center',
              alignItems: 'center',
              //marginRight: switchMerchant ? "27%" : 0,
              //bottom: switchMerchant ? '2%' : 0,
              //width: switchMerchant ? '100%' : switchMerchant ? "96%" : "55%",
              ...isMobile() && {
                display: 'none',
              },
            },
            windowWidth >= 768 && switchMerchant
              ? { right: windowWidth * 0.1 }
              : {},
            windowWidth <= 768
              ? { right: 20 }
              : {},
          ]}>
          <Text
            style={{
              fontSize: switchMerchant ? 20 : 24,
              // lineHeight: 25,
              textAlign: "center",
              fontFamily: "NunitoSans-Bold",
              color: Colors.whiteColor,
              opacity: 1,
            }}>
            Dashboard
          </Text>
        </View>
      ),
      headerRight: () => (

        <View
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: isMobile() ? '' : 'space-between',
            ...isMobile() && {
              //windowWidth: Dimensions.get('window').width,
              marginLeft: 5,
              width: windowWidth * 0.97,
              //position: 'absolute',
              //alignSelf: 'auto',
              //marginLeft: 0,
              //left: 0,
              //left: 0,
              //marginLeft: -30,
              //marginRight: 'auto',
              //marginLeft: 'auto',
            },
          }}>
          {outletSelectDropdownView && outletSelectDropdownView()}
          <View
            style={{
              backgroundColor: 'white',
              width: 0.5,
              height: windowHeight * 0.025,
              opacity: 0.8,
              marginHorizontal: 15,
              bottom: -1,
            }} />
          <TouchableOpacity
            onPress={() => {
              if (global.currUserRole === 'admin') {
                navigation.navigate('General Settings - KooDoo BackOffice');
              }
            }}
            style={{ flexDirection: 'row', alignItems: 'center' }}>
            {console.log('userName', userName)}
            <Text
              style={[{
                fontFamily: 'NunitoSans-SemiBold',
                fontSize: switchMerchant ? 10 : 16,
                color: Colors.secondaryColor,
                marginRight: 15,
              }, switchMerchant ? { width: windowWidth / 8 } : {}]}
              numberOfLines={switchMerchant ? 1 : 1}
              ellipsizeMode="tail"
            >
              {isMobile() ? truncatedUserName : userName}
            </Text>
            <View
              style={{
                marginRight: 30,
                width: windowHeight * 0.05,
                height: windowHeight * 0.05,
                borderRadius: windowHeight * 0.05 * 0.5,
                alignItems: 'center',
                justifyContent: 'center',
                backgroundColor: 'white',
                ...isMobile() && {
                  display: 'none',
                },
              }}>
              <img
                src={personicon}
                width={windowHeight * 0.035}
                height={windowHeight * 0.035}
              />
            </View>
          </TouchableOpacity>
       </View>
      ),
    })
  );*/

  return (
    <>
      <View style={{
        backgroundColor: Colors.darkBgColor,
        height: '8%',
        zIndex: 1000,
      }}>
        {menu ?
          <View style={{
            backgroundColor: 'white',
            width: 120,
            height: 75,
            borderRadius: 5,
            position: 'absolute',
            right: windowWidth * 0.07,
            top: windowHeight * 0.06,
            alignItems: "center",
            justifyContent: 'center',
          }}>
            <TouchableOpacity onPress={() => {
              props.navigation.navigate('RA Dashboard - KooDoo Assistant');
              setMenu(false);
              CommonStore.update((s) => {
                s.rackPage = false;
              })
            }}>
              <Text style={{
                fontFamily: "NunitoSans-Bold",
                fontSize: switchMerchant ? 14 : 16,
              }}>
                Home
              </Text>
            </TouchableOpacity>
            <View style={{ height: 2, backgroundColor: Colors.descriptionColor, marginVertical: 5, width: '100%' }} />
            <TouchableOpacity onPress={async () => {
              await AsyncStorage.clear();
              logOutButton();
            }}>
              <Text style={{
                fontFamily: "NunitoSans-Bold",
                fontSize: switchMerchant ? 14 : 16,
              }}>
                Logout
              </Text>
            </TouchableOpacity>
          </View>
          : null}
        <View style={{ marginTop: 'auto', marginBottom: 'auto', marginLeft: 5 }}>{/*marginLeft:5*/}
          <View style={{ flexDirection: 'row', justifyContent: 'space-between'  }}>
            <View
              // onPress={() => {
              //   if (isAlphaUser || true) {
              //     navigation.navigate('MenuOrderingScreen');

              //     CommonStore.update((s) => {
              //       s.currPage = 'MenuOrderingScreen';
              //       s.currPageStack = [...currPageStack, 'MenuOrderingScreen'];
              //     });
              //   }
              //   else {
              //     navigation.navigate('Table');

              //     CommonStore.update((s) => {
              //       s.currPage = 'Table';
              //       s.currPageStack = [...currPageStack, 'Table'];
              //     });
              //   }
              //   if (expandTab !== EXPAND_TAB_TYPE.OPERATION) {
              //     CommonStore.update((s) => {
              //       s.expandTab = EXPAND_TAB_TYPE.OPERATION;
              //     });
              //   }
              // }}
              style={[styles.headerLeftStyle, {
                width: windowWidth * 0.17,
                paddingTop: 'auto',
                paddingBottom: 'auto',


              }]}>
              <Image source={headerLogo} style={{ width: 124, height: 26 }} />
            </View>

            <View
              style={[
                {
                  justifyContent: 'center',
                  alignItems: 'center',

                },
                windowWidth >= 768 && switchMerchant
                  ? { right: windowWidth * 0.1 }
                  : {},
                windowWidth <= 768
                  ? { right: 20 }
                  : {},
              ]}>
              <Text
                style={{
                  fontSize: switchMerchant ? 20 : 24,
                  // lineHeight: 25,
                  textAlign: "center",
                  fontFamily: "NunitoSans-Bold",
                  color: Colors.whiteColor,
                  opacity: 1,
                }}>
                {currPage}
              </Text>
            </View>

            <View
              style={{
                alignSelf: 'flex-end',
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent:'space-between',
//                ...isMobile() && {
//                  //windowWidth: Dimensions.get('window').width,
//                  marginLeft: 5,
//                  alignSelf: 'flex-start',
//                  //width: windowWidth * 0.97,
//
//
//                  //position: 'absolute',
//                  //alignSelf: 'auto',
//                  //marginLeft: 0,
//                  //left: 0,
//                  //left: 0,
//                  //marginLeft: -30,
//                  //marginRight: 'auto',
//                  //marginLeft: 'auto',
//                },
              }}>
              {outletSelectDropdownView && outletSelectDropdownView()}
              <View
                style={{
                  backgroundColor: 'white',
                  width: 0.5,
                  height: windowHeight * 0.025,
                  opacity: 0.8,
                  marginHorizontal: 15,
                  bottom: -1,
                }} />
              <TouchableOpacity
                onPress={() => {
                  setMenu(!menu);
                }}
                style={{ flexDirection: 'row', alignItems: 'center' }}>
               
                <Text
                  style={[{
                    fontFamily: 'NunitoSans-SemiBold',
                    fontSize: switchMerchant ? 10 : 16,
                    color: Colors.secondaryColor,
                    marginRight: 15,
                  }, switchMerchant ? { width: windowWidth / 8 } : {}]}
                  numberOfLines={switchMerchant ? 1 : 1}
                  ellipsizeMode="tail"
                >
                  {userName}
                </Text>
                <View
                  style={{
                    marginRight: 30,
                    width: windowHeight * 0.05,
                    height: windowHeight * 0.05,
                    borderRadius: windowHeight * 0.05 * 0.5,
                    alignItems: 'center',
                    justifyContent: 'center',
                    backgroundColor: 'white',

                  }}>
                  <Image source={personicon} style={{ width: windowHeight * 0.035, height: windowHeight * 0.035 }} />

                </View>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </View>
    </>
  );
};

const styles = StyleSheet.create({
  headerLeftStyle: {
    width: Dimensions.get('screen').width * 0.17,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default TopBar;
