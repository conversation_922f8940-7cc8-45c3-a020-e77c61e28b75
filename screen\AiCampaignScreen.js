import { Text } from "react-native-fast-text";
import React, { Component, useReducer, useState, useEffect, useCallback} from 'react';
import {
  StyleSheet,
  Image,
  View,
  TouchableOpacity,
  Dimensions,
  Alert,
  Button,
  Modal,
  TextInput,
  KeyboardAvoidingView,
  ActivityIndicator,
  useWindowDimensions,
} from 'react-native';
import { ScrollView, FlatList } from "react-native-gesture-handler";
import { ModalView } from 'react-native-multiple-modals';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Platform } from 'react-native';
import Colors from '../constant/Colors';
import SideBar from './SideBar';
import Icon from 'react-native-vector-icons/Ionicons';
import EvilIcons from 'react-native-vector-icons/EvilIcons';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import Plus from 'react-native-vector-icons/Feather';
import FontAwesome from 'react-native-vector-icons/FontAwesome';
import Feather from 'react-native-vector-icons/Feather';
import PlusSvg from '../assets/svg/Plus.svg';
import DropDownPicker from 'react-native-dropdown-picker';
import Close from 'react-native-vector-icons/AntDesign';
import ApiClient from '../util/ApiClient';
import AntDesign from 'react-native-vector-icons/AntDesign';
import API from '../constant/API';
import * as User from '../util/User';
import * as Cart from '../util/Cart';
// import Dash from 'react-native-dash';
import moment from 'moment';
import Styles from '../constant/Styles';
import QRCode from 'react-native-qrcode-svg';
import EIcon from 'react-native-vector-icons/Entypo';
// import CurrencyInput, { formatValue } from 'react-currency-input-field';
import AIcon from 'react-native-vector-icons/AntDesign';
import OrderModal from './components/OrderModal';
// import Barcode from 'react-native-barcode-builder';
// // import molpay from 'molpay-mobile-xdk-reactnative-beta';
import {
  isTablet,
  parseValidIntegerText
} from '../util/common';
import { MerchantStore } from '../store/merchantStore';
import { OutletStore } from '../store/outletStore';
import { CommonStore } from '../store/commonStore';
import { UserStore } from '../store/userStore';
import { LoyaltyStore } from "../store/loyaltyStore";
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
// import Swipeable from 'react-native-gesture-handler/Swipeable';
import {
  DELAY_LONG_PRESS_TIME,
  MODE_ADD_CART,
  OFFLINE_BILL_TYPE,
  OFFLINE_PAYMENT_METHOD_DROPDOWN_LIST,
  OFFLINE_PAYMENT_METHOD_TYPE,
  ORDER_TYPE,
  USER_ORDER_PRIORITY,
  USER_ORDER_STATUS,
  EXPAND_TAB_TYPE,
  LOYALTY_CAMPAIGN_BATCH_TYPE,
} from '../constant/common';
import { useKeyboard } from '../hooks';
import RNPickerSelect from 'react-native-picker-select';
import {
  getObjectDiff,
  isObjectEqual,
  listenToCurrOutletIdChangesWaiter,
  naturalCompare,
  getTransformForModalInsideNavigation,
  getTransformForScreenInsideNavigation,
} from '../util/common';
import { qrUrl } from '../constant/env';
import { printUserOrder } from '../util/printer';
import { Collections } from '../constant/firebase';
import firestore from '@react-native-firebase/firestore';
// import CheckBox from 'react-native-check-box';
import AsyncImage from '../components/asyncImage';
import 'react-native-get-random-values';
import { v4 as uuidv4 } from 'uuid';
import { customAlphabet } from 'nanoid';
import CheckBox from '@react-native-community/checkbox';
// import { DIMENTIONS } from 'react-native-numeric-input';
import { color } from 'react-native-reanimated';
import DateTimePickerModal from 'react-native-modal-datetime-picker';
import GCalendar from '../assets/svg/GCalendar';
import FusionCharts from 'react-native-fusioncharts';
import Ionicon from 'react-native-vector-icons/Ionicons';

import {
  CHART_DATA,
  CHART_TYPE,
  FS_LIBRARY_PATH,
  CHART_Y_AXIS_DROPDOWN_LIST,
  CHART_FIELD_COMPARE_DROPDOWN_LIST,
  CHART_FIELD_NAME_DROPDOWN_LIST,
  CHART_FIELD_TYPE,
  CHART_FIELD_COMPARE_DICT,
  CHART_PERIOD,
} from '../constant/chart';
import {
  filterChartItems,
  getDataForChartDashboardTodaySales,
  getDataForSalesLineChart,
} from '../util/chart';
import { useFocusEffect } from '@react-navigation/native';
import UserIdleWrapper from '../components/userIdleWrapper';

import {
  EFFECTIVE_DAY_DROPDOWN_LIST,
  TARGET_USER_GROUP_DROPDOWN_LIST,
  PROMOTION_TYPE_VARIATION,
  PROMOTION_TYPE_VARIATION_DROPDOWN_LIST,
  EFFECTIVE_TYPE,
  CRM_SEGMENT_DROPDOWN_LIST,
} from '../constant/promotions';
import { CellContainer, autoScroll } from '@shopify/flash-list';
import { getWeeksInMonthWithOptions } from "date-fns/fp";
import { parse } from "date-fns";
import { LOYALTY_CAMPAIGN_TYPE } from "../constant/loyalty";
// const ModalView = Platform.OS === 'android' ? ModalComponent : ModalViewComponent;

const AiCampaignScreen = (props) => {
  const { navigation } = props;

  ///////////////////////////////////////////////////////////

  const [isMounted, setIsMounted] = useState(true);

  useFocusEffect(
    useCallback(() => {
      setIsMounted(true);
      return () => {
        setIsMounted(false);
      };
    }, [])
  );

  ///////////////////////////////////////////////////////////

  const { width: windowWidth, height: windowHeight } = useWindowDimensions();
  const [switchMerchant, setSwitchMerchant] = useState(isTablet() ? false : true);
  
  ///////////////////////////////////////////////////////////

  // Navigation bar
  const outletSelectDropdownView = CommonStore.useState(
    (s) => s.outletSelectDropdownView,
  );

  const userName = UserStore.useState((s) => s.name);

  const taggableVouchers = OutletStore.useState(s => s.taggableVouchers);

  const merchantId = UserStore.useState((s) => s.merchantId);
  const merchantName = MerchantStore.useState((s) => s.name);
  const merchantLogo = MerchantStore.useState((s) => s.logo);
  const currOutletId = MerchantStore.useState(s => s.currOutletId);
  const currOutlet = MerchantStore.useState((s) => s.currOutlet);
  const recommendedLoyalty = OutletStore.useState((s) => s.recommendedLoyalty);
  const selectedRecommendedLoyalty = OutletStore.useState((s) => s.selectedRecommendedLoyalty);
  
  const isAlphaUser = UserStore.useState((s) => s.isAlphaUser);
  const expandTab = CommonStore.useState((s) => s.expandTab);
  const currPageStack = CommonStore.useState((s) => s.currPageStack);
  const isLoading = CommonStore.useState((s) => s.isLoading);

  const taggableVoucherId = LoyaltyStore.useState((s) => s.taggableVoucherId);

  const loyaltyCampaignSendTime = LoyaltyStore.useState((s) => s.loyaltyCampaignSendTime);
  const loyaltyCampaignExpirationDays = LoyaltyStore.useState((s) => s.loyaltyCampaignExpirationDays);

  /////////////////////////////////////////////////
  
  // Toggle State for Selected Loyalty Representation
  const [selectedLoyalty, setSelectedLoyalty] = useState(null);

  // Delete Recommended Loyalty
  // useEffect(async () => {
  //   await firestore().collection(Collections.RecommendedLoyalty).doc('171aa621-c27a-4f70-aeb7-ad2407ffc8c8').delete();
  // }, [])

  // Display recommendedLoyalty List Data
  const renderLoyaltyList = ({ item, index }) => {
    const isSelected = index === selectedLoyalty;

    return (
      <View style={{ width: '50%', height: windowHeight * 0.18, }}>
        <TouchableOpacity
          style={{
            flexDirection: 'row',
            justifyContent: 'center',
            alignItems: 'center',
            paddingHorizontal: 3,
            marginHorizontal: 10,
            borderWidth: 3,
            borderColor: Colors.primaryColor,
            borderRadius: 15,
            height: windowHeight * 0.16,
            backgroundColor: isSelected ? Colors.lightGrey : Colors.backgroundColor,
          }}
          onPress={() => {
            setSelectedLoyalty(prevSelectedIndex => (prevSelectedIndex === index ? null : index));
            OutletStore.update((s) => {
              if (s.selectedRecommendedLoyalty === item) {
                s.selectedRecommendedLoyalty = null;
              } else {
                s.selectedRecommendedLoyalty = item;
              }
            });
          }}
        >

          {/* Type and Descriptions */}
          <View style={{
            height: windowHeight * 0.1,
            width: '80%',
            flexDirection: 'column',
          }}>

            <View style={{ height: '40%', justifyContent: 'flex-end' }}>
              <Text style={{
                fontFamily: 'NunitoSans-Bold',
                fontSize: 20,
                fontWeight: 'bold',
                opacity: 0.7
              }}
              >
                {item.campaignName}
              </Text>
            </View>

            <View style={{ height: '60%', justifyContent: 'flex-start' }}>
              <Text style={{
                fontFamily: 'NunitoSans-Regular',
                fontSize: 16,
                color: Colors.blackColor,
                opacity: 0.55
              }}
                numberOfLines={2}
              >
                {item.campaignDescription}
              </Text>
            </View>

          </View>

          {/* Right Icon */}
          <View style={{ marginLeft: 20, marginRight: 10 }}>
            <FontAwesome name="chevron-right" size={28} color={Colors.primaryColor} />
          </View>
        </TouchableOpacity>

      </View>
    );
  }

  /////////////////////////////////////////////////

  // Modal Display Control State
  const [showLoyaltyCampaignSendTimePicker, setShowLoyaltyCampaignSendTimePicker] = useState(false);
  const [showBatchSendTimePicker, setShowBatchSendTimePicker] = useState(false);

  /////////////////////////////////////////////////

  const [temp, setTemp] = useState('');

  const [loyaltyCampaignGuestNotVisitedDays, setLoyaltyCampaignGuestNotVisitedDays] = useState('');
  const [loyaltyCampaignGuestNotVisitedDaysMax, setLoyaltyCampaignGuestNotVisitedDaysMax] = useState('');
  const [loyaltyCampaignGuestBirthdayBeforeDays, setLoyaltyCampaignGuestBirthdayBeforeDays] = useState('');
  const [loyaltyCampaignGuestAfterEveryVisits, setLoyaltyCampaignGuestAfterEveryVisits] = useState('');
  const [loyaltyCampaignGuestAfterEverySpends, setLoyaltyCampaignGuestAfterEverySpends] = useState('');
  const [taggableVoucherDropdownList, setTaggableVoucherDropdownList] = useState([]);

  const [batchList, setBatchList] = useState([
    {
      batchId: uuidv4(),
      orderIndex: 0,
      batchType: LOYALTY_CAMPAIGN_BATCH_TYPE.SEQUENCE,
      voucherId: '',
      activationDays: 1,
      expirationDays: 14,
      sendTime: moment().set('hour', 10).valueOf(),
    },
  ]);

  const renderBatchList = ({ item, index }) => {
    var isValidToRender = true;

    // let taggableVoucherDropdownListSub = taggableVoucherDropdownList.filter(voucherOption => {
    //   if (voucherOption.value.length > 0) {
    //     if (index === 1) {
    //       console.log('break');
    //     }

    //     if (
    //       taggableVoucherId === voucherOption.value
    //       ||
    //       batchList.find(batch => {
    //         if (batch.voucherId === voucherOption.value &&
    //           batch.batchId !== item.batchId) {

    //           return true;
    //         }
    //       })
    //     ) {
    //       return false;
    //     }
    //     else {
    //       return true;
    //     }
    //   }
    //   else {
    //     return true;
    //   }
    // })

    let taggableVoucherDropdownListSub = taggableVoucherDropdownList.filter(voucherOption => {
      if (voucherOption.value.length === 0) {
        return true;
      }

      if (taggableVoucherId === voucherOption.value) {
        return false;
      }

      const isVoucherUsed = batchList.some(batch => 
        batch.voucherId === voucherOption.value && batch.batchId !== item.batchId
      );

      return !isVoucherUsed;
    });

    return (
      isValidToRender && (
        <View
          style={{
            backgroundColor: '#ffffff',
            flexDirection: 'row',
            paddingVertical: 20,
            paddingLeft: 15,
            paddingRight: 5,
            borderBottomWidth: StyleSheet.hairlineWidth,
            borderBottomColor: '#c4c4c4',
            alignItems: 'center',
            width: '100%',
            // height: (Dimensions.get('window').width * 0.1) * 3,

            // marginBottom: (index === batchList.length - 1) ? 200 : 0,
            zIndex: 10000 - index,
          }}>
          <View style={{ width: '40%', zIndex: 10000 - index, }}>
            <View style={{
              width: '80%',
              zIndex: 1,
              height: 40,
              borderRadius: 5,
              justifyContent: 'center',
              backgroundColor: 'white',
              paddingHorizontal: 8,
              borderWidth: 1,
              borderColor: '#E5E5E5',
              shadowOpacity: 0.22,
              shadowColor: '#000',
              shadowOffset: {
                height: 2,
              },
              shadowRadius: 3.22,
              elevation: 2,
            }}
            >
              {
                taggableVoucherDropdownListSub.find(option => item.voucherId === option.value)
                  ?
                  <RNPickerSelect
                    placeholder={{}}
                    useNativeAndroidPickerStyle={false}
                    //pickerProps={{ style: { height: 160, overflow: 'hidden',} }}
                    style={{
                      inputIOS: { fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 16, paddingVertical: 5 },
                      inputAndroid: { fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 16, paddingVertical: 5 },
                      inputAndroidContainer: {
                        //backgroundColor: 'red',
                        width: '100%',
                      }
                    }}
                    //contentContainerStyle={{ fontSize: switchMerchant ? 10 : 14, }}
                    items={taggableVoucherDropdownListSub}
                    value={
                      item.voucherId
                    }
                    onValueChange={(value) => {
                      setBatchList(batchList.map((batch, i) => (i === index ? {
                        ...batch,

                        voucherId: value,
                      } : batch)));
                    }}
                  />
                  :
                  <></>
              }
            </View>

            {/* <View style={{ width: '85%', marginTop: '2%', zIndex: 10000 - index, }}>
              {
                taggableVoucherDropdownListSub.find(option => item.voucherId === option.value)
                  ?
                  <DropDownPicker
                    containerStyle={[
                      { height: 40 },
                      switchMerchant ? { height: 35 } : {},
                    ]}
                    arrowColor={'black'}
                    arrowSize={20}
                    arrowStyle={{ fontWeight: 'bold' }}
                    labelStyle={{
                      fontFamily: 'NunitoSans-Regular',
                      fontSize: 14,
                    }}
                    style={[
                      {
                        width: '100%',
                        paddingVertical: 0,
                        backgroundColor: Colors.fieldtBgColor,
                        borderRadius: 10,
                      },
                      switchMerchant
                        ? {
                          width: '100%',
                        }
                        : {},
                    ]}
                    placeholderStyle={{
                      color: Colors.fieldtTxtColor,
                      fontFamily: 'NunitoSans-Regular',
                      fontSize: 14,
                    }}
                    items={taggableVoucherDropdownListSub}
                    itemStyle={{
                      justifyContent: 'flex-start',
                      marginLeft: 5,
                      zIndex: 10000 - index,
                      fontFamily: 'NunitoSans-Regular',
                      fontSize: 14,
                    }}
                    placeholder={'Select'}
                    //multiple={true}
                    customTickIcon={(press) => (
                      <Ionicon
                        name={'checkmark-outline'}
                        color={
                          press
                            ? Colors.fieldtBgColor
                            : Colors.primaryColor
                        }
                        size={25}
                      />
                    )}
                    onChangeItem={(option) => {
                      setBatchList(batchList.map((batch, i) => (i === index ? {
                        ...batch,

                        voucherId: option.value,
                      } : batch)));
                    }}
                    defaultValue={item.voucherId}
                    dropDownMaxHeight={150}
                    dropDownStyle={[
                      {
                        width: '100%',
                        height: 150,
                        backgroundColor: Colors.fieldtBgColor,
                        borderRadius: 10,
                        borderWidth: 1,
                        textAlign: 'left',
                        zIndex: 10000 - index,
                      },
                      switchMerchant
                        ? {
                          width: '100%',
                        }
                        : {},
                    ]}
                    globalTextStyle={{
                      fontSize: switchMerchant ? 10 : 14,
                    }}
                  //dropDownStyle={{ borderWidth: 1, borderColor: '#E5E5E5', borderRadius: 5, width: 200, paddingVertical: 0, margin: 5, marginLeft: 0 }}
                  />
                  :
                  <></>
              }
            </View> */}
          </View>

          <View style={{
            width: '15%',
          }}>
            <TextInput
              style={{
                borderWidth: 1,
                borderColor: '#E5E5E5',
                borderRadius: 5,
                height: switchMerchant ? 35 : 40,
                // width: 80,
                justifyContent: 'center',
                textAlign: 'center',
                marginTop: 5,
                width: '80%',
                fontFamily: 'NunitoSans-Regular',
                fontSize: switchMerchant ? 10 : 16,
                //left: 8
              }}
              placeholder={'0'}
              placeholderTextColor={Platform.select({
                ios: '#a9a9a9',
              })}
              placeholderStyle={{
                justifyContent: 'center',
                fontFamily: 'NunitoSans-Regular',
                fontSize: switchMerchant ? 10 : 16,
              }}
              //iOS
              clearTextOnFocus
              //////////////////////////////////////////////
              //Android
              onFocus={() => {
                setTemp(item.activationDays)
                setBatchList(batchList.map((batch, i) => (i === index ? {
                  ...batch,

                  activationDays: '',
                } : batch)));
              }}
              ///////////////////////////////////////////////
              //When textinput is not selected
              onEndEditing={() => {
                if (item.activationDays == '') {
                  setBatchList(batchList.map((batch, i) => (i === index ? {
                    ...batch,

                    activationDays: temp,
                  } : batch)));
                }
              }}
              //////////////////////////////////////////////
              onChangeText={(text) => {
                var value =
                  text.length >= 0
                    ? !isNaN(text)
                      ? text
                      : '0'
                    : '0';

                setBatchList(batchList.map((batch, i) => (i === index ? {
                  ...batch,

                  activationDays: value,
                } : batch)));
              }}
              defaultValue={item.activationDays != 0 ? item.activationDays : ''}
              keyboardType={'decimal-pad'}
            />
          </View>

          <View style={{
            width: '15%',
          }}>
            <TextInput
              style={{
                borderWidth: 1,
                borderColor: '#E5E5E5',
                borderRadius: 5,
                height: switchMerchant ? 35 : 40,
                // width: 80,
                justifyContent: 'center',
                textAlign: 'center',
                marginTop: 5,
                width: '80%',
                fontFamily: 'NunitoSans-Regular',
                fontSize: switchMerchant ? 10 : 16,
                //left: 8
              }}
              placeholder={'0'}
              placeholderTextColor={Platform.select({
                ios: '#a9a9a9',
              })}
              placeholderStyle={{
                justifyContent: 'center',
                fontFamily: 'NunitoSans-Regular',
                fontSize: switchMerchant ? 10 : 16,
              }}
              //iOS
              clearTextOnFocus
              //////////////////////////////////////////////
              //Android
              onFocus={() => {
                setTemp(item.expirationDays)
                setBatchList(batchList.map((batch, i) => (i === index ? {
                  ...batch,

                  expirationDays: '',
                } : batch)));
              }}
              ///////////////////////////////////////////////
              //When textinput is not selected
              onEndEditing={() => {
                if (item.expirationDays == '') {
                  setBatchList(batchList.map((batch, i) => (i === index ? {
                    ...batch,

                    expirationDays: temp,
                  } : batch)));
                }
              }}
              //////////////////////////////////////////////
              onChangeText={(text) => {
                var value =
                  text.length >= 0
                    ? !isNaN(text)
                      ? text
                      : '0'
                    : '0';

                setBatchList(batchList.map((batch, i) => (i === index ? {
                  ...batch,

                  expirationDays: value,
                } : batch)));
              }}
              defaultValue={item.expirationDays != 0 ? item.expirationDays : ''}
              keyboardType={'decimal-pad'}
            />
          </View>

          <View style={[{
            width: '20%',
          }]}>
            <TouchableOpacity onPress={() => {
              global.currBatchIndex = index;

              setShowBatchSendTimePicker(true);
            }}
              style={[{
                backgroundColor: Colors.fieldtBgColor,
                width: 130,
                padding: 5,
                height: 40,
                borderRadius: 5,
                marginVertical: 5,
                alignItems: 'center',
                justifyContent: 'center',
                borderWidth: 1,
                borderColor: '#E5E5E5',
                fontFamily: 'NunitoSans-Regular',
                fontSize: 16
              }, switchMerchant ? {
                fontSize: 10,
                width: 93,
                height: 35,
              } : {}]}
            >
              <Text style={[{ fontVariant: ['tabular-nums'], fontFamily: 'NunitoSans-Regular', fontSize: 16 }, switchMerchant ? {
                fontSize: 10,
              } : {}]}>{moment(item.sendTime).format('HH:mm A')}</Text>
            </TouchableOpacity>
          </View>

          <View style={{
            width: '10%',
          }}
          >
            <TouchableOpacity style={{ marginLeft: switchMerchant ? 5 : 10, }}
              onPress={() => {
                setBatchList([
                  ...batchList.slice(0, index),
                  ...batchList.slice(index + 1),
                ]);
              }}>
              <Feather name="trash-2" size={switchMerchant ? 15 : 20} color="#eb3446" />
            </TouchableOpacity>
          </View>
        </View>
      )
    )
  };

  /////////////////////////////////////////////////
  // Target Segment
  const crmSegments = OutletStore.useState((s) => s.crmSegments);
  const [crmSegmentsDropdownList, setCrmSegmentsDropdownList] = useState([]);
  const [selectedTargetSegmentGroupList, setSelectedTargetSegmentGroupList] = useState([]);
  const [estimatedReachCustomer, setEstimatedReachCustomer] = useState(null);

  useEffect(() => {
    var crmSegmentsDropdownListTemp = crmSegments.map((segment) => ({
      label: segment.name,
      value: segment.uniqueId,
    }));

    setCrmSegmentsDropdownList(
      crmSegmentsDropdownListTemp
    );

    if (selectedTargetSegmentGroupList.length > 0) {
      var selectedTargetSegmentGroupListTemp = [];

      var combinedCrmSegmentsDropdownListTemp = CRM_SEGMENT_DROPDOWN_LIST.concat(crmSegmentsDropdownListTemp);

      for (var i = 0; i < selectedTargetSegmentGroupList.length; i++) {
        if (combinedCrmSegmentsDropdownListTemp.find(item => item.value === selectedTargetSegmentGroupList[i])) {
          selectedTargetSegmentGroupListTemp.push(selectedTargetSegmentGroupList[i]);
        }
      }

      var isChanged = false;
      if (selectedTargetSegmentGroupList.length !== selectedTargetSegmentGroupListTemp.length) {
        isChanged = true;
      } else {
        for (var i = 0; i < selectedTargetSegmentGroupList.length; i++) {
          const isEqual = selectedTargetSegmentGroupList[i] === selectedTargetSegmentGroupListTemp[i];

          if (!isEqual) {
            isChanged = true;
            break;
          }
        }
      }

      if (isChanged) {
        setSelectedTargetSegmentGroupList(selectedTargetSegmentGroupListTemp);
      }
    }
  }, [crmSegments, selectedTargetSegmentGroupList]);

  useEffect(() => {
    if (selectedLoyalty){
      OutletStore.update((s) => {
          s.selectedRecommendedLoyalty.targetSegmentGroupList = selectedTargetSegmentGroupList;
      });
    }
  }, [selectedTargetSegmentGroupList])

  // Calculate Estimated Reach Customer
  useEffect(() => {
    const countEstimateReachCustomer = async () => {
        setEstimatedReachCustomer(null);
  
        // Total Customer for Filtered Segments
        const uniqueUserIds = new Set();

        // Filter Selected Segments only  
        const filteredSegments = crmSegments.filter(item => selectedTargetSegmentGroupList.includes(item.uniqueId));
  
        filteredSegments.forEach(segment => {
          segment.crmUserTagIdList.forEach(userId => {
            uniqueUserIds.add(userId);
          });
        });

        setEstimatedReachCustomer(uniqueUserIds.size);
    };
  
    countEstimateReachCustomer();
  }, [crmSegments, selectedTargetSegmentGroupList]);
  
  /////////////////////////////////////////////////

  useEffect(() => {
    setTaggableVoucherDropdownList(
      [
        {
          label: 'N/A',
          value: '',
        }
      ].concat(
        taggableVouchers.filter(voucher => {
          var endTime = moment(voucher.promoTimeEnd);
          var endDate = moment(voucher.promoDateEnd).set({
            hour: endTime.get('hour'),
            minute: endTime.get('minute'),
          });
          if (!moment().isSameOrAfter(endDate)) {
            return true;
          }
          else {
            return false;
          }
        }).map((item) => ({ label: `${item.campaignName} [${item.voucherType.replaceAll('_', ' ')}]`, value: item.uniqueId })),
      ),
    );
  }, [taggableVouchers]);
  
  /////////////////////////////////////////////////
  // TextInput Checking Before Proceed Next
  const [errorMessages, setErrorMessages] = useState([]);

  useEffect(() => {
    setErrorMessages([]);

    if (!selectedLoyalty || !recommendedLoyalty.campaigns[selectedLoyalty]) {
      return;
    }
    
    const campaignType = recommendedLoyalty.campaigns[selectedLoyalty].loyaltyCampaignType;

    const conditions = {
      [LOYALTY_CAMPAIGN_TYPE.FIRST_VISIT]: {
          fields: [loyaltyCampaignExpirationDays],
          messages: ['Expiration days must be filled'],
      },
      [LOYALTY_CAMPAIGN_TYPE.AT_RISK]: {
          fields: [loyaltyCampaignExpirationDays, loyaltyCampaignGuestNotVisitedDays, loyaltyCampaignGuestNotVisitedDaysMax],
          messages: [
              'Expiration days must be filled',
              'Guest not visited days must be filled',
              'Maximum Guest not visited days must be filled'
          ],
      },
      [LOYALTY_CAMPAIGN_TYPE.BIRTHDAY]: {
          fields: [loyaltyCampaignExpirationDays, loyaltyCampaignGuestBirthdayBeforeDays],
          messages: [
              'Expiration days must be filled',
              'Birthday before days must be filled'
          ],
      },
      [LOYALTY_CAMPAIGN_TYPE.GROWTH]: {
          fields: [loyaltyCampaignExpirationDays, loyaltyCampaignGuestAfterEveryVisits],
          messages: [
              'Expiration days must be filled',
              'Guest visits must be filled'
          ],
      },
      [LOYALTY_CAMPAIGN_TYPE.BIG_SPENDER]: {
          fields: [loyaltyCampaignExpirationDays, loyaltyCampaignGuestAfterEverySpends],
          messages: [
              'Expiration days must be filled',
              'Guest spent must be filled'
          ],
      },
      [LOYALTY_CAMPAIGN_TYPE.VOUCHER]: {
          fields: [loyaltyCampaignExpirationDays],
          messages: ['Expiration days must be filled'],
      },
    };

    const selectedCampaignCondition = conditions[campaignType];

    if (selectedCampaignCondition) {
      const { fields, messages } = selectedCampaignCondition;

      let newErrorMessages = [];

      fields.forEach((field, index) => {
        if (typeof field === 'string' && field.trim() === '') {
          newErrorMessages.push(messages[index]);
        }
        else if (typeof field === 'number' && field === '') {
          newErrorMessages.push(messages[index]);
        }
      });

      setErrorMessages(newErrorMessages);
    }
  }, [
    selectedLoyalty, 
    loyaltyCampaignExpirationDays,
    loyaltyCampaignGuestNotVisitedDays,
    loyaltyCampaignGuestNotVisitedDaysMax,
    loyaltyCampaignGuestBirthdayBeforeDays,
    loyaltyCampaignGuestAfterEveryVisits,
    loyaltyCampaignGuestAfterEverySpends,
    recommendedLoyalty.campaigns])
  
  const nextButtonOnClick = () => {
    if (errorMessages.length === 0 && selectedTargetSegmentGroupList.length !== 0) {
      navigation.navigate('AiLoyaltyVoucherScreen');
  
      CommonStore.update((s) => {
        s.currPage = 'AiLoyaltyVoucherScreen';
        s.currPageStack = [...s.currPageStack, 'AiLoyaltyVoucherScreen'];
      });
    } else {
      let errorMessage = [...errorMessages];
  
      if (selectedTargetSegmentGroupList.length === 0) {
        errorMessage.push('Choose at least 1 Target Segment Group.');
      }
  
      Alert.alert(
        'Error',
        errorMessage.join('\n'),
        [{ text: 'OK', onPress: () => {} }],
        { cancelable: false }
      );
    }
  };
  /////////////////////////////////////////////////

  // Analyze Button Click Event
  const analyzeLoyalty = async () => {
    CommonStore.update((s) => {s.isLoading = true});

    const body = {
      merchantId,
      outletId: currOutlet.uniqueId,
      outletName: currOutlet.name,
      merchantName,
      outletCover: currOutlet.cover,
      merchantLogo,
      recommendedLoyaltyId: (recommendedLoyalty && recommendedLoyalty.uniqueId) ? recommendedLoyalty.uniqueId : '',
    };

    try {
      await ApiClient.POST(API.analyzeRecommendedLoyalty, body)
        .then((result) => {
          if (result && result.status === 'success') {
            Alert.alert('Request Sent', "We will notify you once the analysis is complete.");
          } 
          else {
            console.error('Analyze Loyalty Error:', result);
            Alert.alert('Error', "Something went wrong. Please try again later.");
          }
        })
        .catch((error) => {
          console.error('Analyze Loyalty Error:', error);
          Alert.alert('Error', "Something went wrong. Please try again later.");
        });
    }
    catch (error) {
      console.error('Analyze Loyalty Error:', error);
      Alert.alert('Error', "Something went wrong. Please try again later.");
    } 
    finally {
      CommonStore.update((s) => {s.isLoading = false});
    }
  };

  /////////////////////////////////////////////////

  navigation.setOptions({
    headerLeft: () => (
      <TouchableOpacity
        onPress={() => {
          if (isAlphaUser || true) {
            navigation.navigate('MenuOrderingScreen');

            CommonStore.update((s) => {
              s.currPage = 'MenuOrderingScreen';
              s.currPageStack = [...currPageStack, 'MenuOrderingScreen'];
            });
          }
          else {
            navigation.navigate('Table');

            CommonStore.update((s) => {
              s.currPage = 'Table';
              s.currPageStack = [...currPageStack, 'Table'];
            });
          }
          if (expandTab !== EXPAND_TAB_TYPE.OPERATION) {
            CommonStore.update((s) => {
              s.expandTab = EXPAND_TAB_TYPE.OPERATION;
            });
          }
        }}
        style={styles.headerLeftStyle}>
        <Image
          style={[{
            width: 124,
            height: 26,
          }, switchMerchant ? {
            transform: [
              { scaleX: 0.7 },
              { scaleY: 0.7 }
            ],
          } : {}]}
          resizeMode="contain"
          source={require('../assets/image/logo.png')}
        />
      </TouchableOpacity>
    ),
    headerTitle: () => (
      <View
        style={[
          {
            // justifyContent: Platform.OS === 'android' ? 'center' : 'flex-start',
            // alignItems: Platform.OS === 'android' ? 'center' : 'flex-start',
            // marginRight: Platform.OS === 'ios' ? "27%" : 0,
            // bottom: switchMerchant ? '2%' : 0,
            // width: switchMerchant ? '100%' : Platform.OS === 'ios' ? "96%" : "55%",
            ...global.getHeaderTitleStyle(),
          },
          // windowWidth >= 768 && switchMerchant
          //   ? { right: windowWidth * 0.1 }
          //   : {},
          // windowWidth <= 768
          //   ? { right: 20 }
          //   : {},
        ]}>
        <Text
          style={{
            fontSize: switchMerchant ? 20 : 24,
            // lineHeight: 25,
            textAlign: 'left',
            alignItems: 'flex-start',
            justifyContent: 'flex-start',
            fontFamily: 'NunitoSans-Bold',
            color: Colors.whiteColor,
            opacity: 1,
            paddingLeft: Platform.OS === 'ios' ? "1%" : 0,
          }}>
          AI Campaign

        </Text>
      </View>
    ),
    headerRight: () => (
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
        }}>
        {outletSelectDropdownView()}
        <View
          style={{
            backgroundColor: 'white',
            width: 0.5,
            height: windowHeight * 0.025,
            opacity: 0.8,
            marginHorizontal: 15,
            bottom: -1,
          }} />
        <TouchableOpacity
          onPress={() => {
            if (global.currUserRole === 'admin') {
              navigation.navigate('Setting');
            }
          }}
          style={{ flexDirection: 'row', alignItems: 'center' }}>
          <Text
            style={[{
              fontFamily: 'NunitoSans-SemiBold',
              fontSize: switchMerchant ? 10 : 16,
              color: Colors.secondaryColor,
              marginRight: 15,
            }, switchMerchant ? { width: windowWidth / 8 } : {}]}
            numberOfLines={switchMerchant ? 1 : 1}
          >
            {userName}
          </Text>
          <View
            style={{
              marginRight: 30,
              width: windowHeight * 0.05,
              height: windowHeight * 0.05,
              borderRadius: windowHeight * 0.05 * 0.5,
              alignItems: 'center',
              justifyContent: 'center',
              backgroundColor: 'white',
            }}>
            <Image
              style={{
                width: windowHeight * 0.035,
                height: windowHeight * 0.035,
                alignSelf: 'center',
              }}
              source={require('../assets/image/profile-pic.jpg')}
            />
          </View>
        </TouchableOpacity>
      </View>
    ),
  });

  /////////////////////////////////////////////////

  return (
    <UserIdleWrapper disabled={!isMounted}>
      <View style={[
        styles.container,
        !isTablet() && {
          transform: [{ scaleX: 1 }, { scaleY: 1 }],
        },
        getTransformForScreenInsideNavigation(),
      ]}>

        {/* Sidebar */}
        {/* <View
          style={[
            styles.sidebar,
            { width: windowWidth * 0.08, }
          ]}>
          <SideBar
            navigation={props.navigation}
            selectedTab={1}
            expandOperation
          />
        </View> */}

        <View style={{ width: windowWidth * 0.92, height: windowHeight * 0.95, }}>

          {Object.keys(recommendedLoyalty).length === 0 ?
            // No AI Loyalty Recommendation
            <View style={{
              height: '100%',
              width: '100%',
              flexDirection: 'column',
              marginVertical: 10
            }}>

              <View
                style={
                  [
                    styles.flexCenterContainer,
                    {
                      height: '10%',
                      width: '100%',
                      paddingHorizontal: 30,
                    }
                  ]
                }
              >
                <TouchableOpacity
                  style={{
                    width: '15%',
                    height: '70%',
                    backgroundColor: Colors.primaryColor,
                    borderRadius: 5,
                    flexDirection: 'row',
                    justifyContent: 'center',
                    alignItems: 'center',
                    alignSelf: 'flex-end'
                  }}
                  onPress={analyzeLoyalty}
                  disabled={isLoading}
                >
                  <FontAwesome name="search" size={16} color={Colors.whiteColor} />

                  <Text style={{
                    fontFamily: 'NunitoSans-Regular',
                    color: Colors.whiteColor,
                    fontWeight: "bold",
                    marginStart: 15
                  }}
                  >
                    ANALYSIS
                  </Text>
                </TouchableOpacity>
              </View>

              {/* Divider */}
              <View style={styles.divider} />

              <View style={{
                width: '100%',
                alignItems: 'center'
              }}>

                <Text style={{
                  fontFamily: 'NunitoSans-Regular',
                  fontSize: 16,
                  opacity: 0.7,
                  marginVertical: '10%'
                }}>
                  No loyalty recommendation found.
                </Text>

              </View>

            </View>
            :
            // AI Loyalty Recommendation Page
            <View style={{
              height: '100%',
              width: '100%',
              flexDirection: 'column',
              marginVertical: 20,
            }}>

              <ScrollView 
                style={{ paddingHorizontal: 25 }} 
                showsVerticalScrollIndicator={false} 
                nestedScrollEnabled
                >

                <View style={{
                  width: '100%',
                  paddingHorizontal: 25,
                  justifyContent: 'center',
                }}>

                  <Text style={{
                    fontFamily: 'NunitoSans-Bold',
                    fontSize: 26,
                    fontWeight: 'bold',
                  }}
                  >
                    Select your Loyalty Type
                  </Text>

                  <Text style={{
                    fontFamily: 'NunitoSans-Regular',
                    fontSize: 16,
                    opacity: 0.7,
                  }}
                  >
                    Please select the promotion type according to your needs.
                  </Text>

                </View>

                {/* Divider */}
                <View style={styles.divider} />

                {/* Loyalty Type Container */}
                <View style={{
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  marginHorizontal: '1%'
                }}>

                  <FlatList
                    data={recommendedLoyalty.campaigns}
                    renderItem={renderLoyaltyList}
                    keyExtractor={(item, index) => index.toString()}
                    numColumns={2}
                  />

                </View>

                {/* Divider */}
                <View style={styles.divider} />

                {/* */}
                {(selectedLoyalty !== null) && (

                  <View 
                    style={{
                      width: '100%',
                      marginBottom: windowHeight * 0.2,
                      justifyContent: 'center',
                      alignItems: 'center'
                    }}
                  >

                    <DateTimePickerModal
                      isVisible={showLoyaltyCampaignSendTimePicker}
                      mode={'time'}
                      onConfirm={(text) => {
                        LoyaltyStore.update(s => {
                          s.loyaltyCampaignSendTime = moment(text);
                        })
                        setShowLoyaltyCampaignSendTimePicker(false);
                      }}
                      onCancel={() => {
                        setShowLoyaltyCampaignSendTimePicker(false);
                      }}
                      date={moment(loyaltyCampaignSendTime).toDate()}
                    />

                    <DateTimePickerModal
                      isVisible={showBatchSendTimePicker}
                      mode={'time'}
                      onConfirm={(text) => {
                        setBatchList(batchList.map((batch, i) => (i === global.currBatchIndex ? {
                          ...batch,

                          sendTime: moment(text).valueOf(),
                        } : batch)));

                        setShowBatchSendTimePicker(false);
                      }}
                      onCancel={() => {
                        setShowBatchSendTimePicker(false);
                      }}
                    />

                    <View style={{
                      width: '100%',
                      justifyContent: 'flex-start',
                      paddingHorizontal: '2%',
                      paddingVertical: "1%"
                    }}>

                      {(selectedLoyalty !== null && recommendedLoyalty.campaigns[selectedLoyalty].loyaltyCampaignType !== LOYALTY_CAMPAIGN_TYPE.RETURN) && (
                        <>
                          <Text
                            style={[
                              {
                                fontWeight: '500',

                                fontFamily: 'NunitoSans-Bold',
                                fontSize: 18,
                              },
                              switchMerchant
                                ? {
                                  fontSize: 10,
                                }
                                : {},
                            ]}>
                            {`What time of the day should it be sent `}
                          </Text>

                          <Text
                            style={[
                              {
                                fontWeight: '300',

                                fontFamily: 'NunitoSans-Bold',
                                fontSize: 16,
                              },
                              switchMerchant
                                ? {
                                  fontSize: 10,
                                }
                                : {},
                            ]}>
                            {`(suggest to create the SMS blasting campaign 1 day before the promotion date)`}
                          </Text>

                          <TouchableOpacity
                            onPress={() => {
                              setShowLoyaltyCampaignSendTimePicker(true);
                            }}
                            style={[
                              {
                                backgroundColor: Colors.fieldtBgColor,
                                width: '20%',
                                height: 45,
                                borderRadius: 5,
                                padding: 1,
                                marginVertical: 10,
                                alignItems: 'center',
                                justifyContent: 'center',
                                borderWidth: 1,
                                borderColor: '#E5E5E5',
                                fontFamily: 'NunitoSans-Regular',

                              },
                              switchMerchant
                                ? {
                                  height: 35,
                                }
                                : {},
                            ]}>
                            <Text
                              style={[
                                {
                                  fontVariant: ['tabular-nums'],
                                  fontFamily: 'NunitoSans-Regular',
                                  fontSize: 16,
                                },
                                switchMerchant
                                  ? {
                                    fontSize: 10,
                                  }
                                  : {},
                              ]}>
                              {moment(loyaltyCampaignSendTime).format(
                                'hh:mm A',
                              )}
                            </Text>
                          </TouchableOpacity>
                        </>
                      )}

                      {/* Expiration Days */}
                      {(selectedLoyalty !== null && recommendedLoyalty.campaigns[selectedLoyalty].loyaltyCampaignType !== LOYALTY_CAMPAIGN_TYPE.RETURN) && (
                        <View
                          style={{
                            flexDirection: 'column',
                            alignItems: 'flex-start',
                            marginTop: 10,
                          }}>
                          <Text
                            style={[
                              {
                                fontWeight: '500',
                                fontFamily: 'NunitoSans-Bold',
                                fontSize: 18,
                              },
                              switchMerchant
                                ? {
                                  fontSize: 10,
                                }
                                : {},
                            ]}>
                            Expiration (days)
                          </Text>

                          <TextInput
                            style={{
                              borderWidth: 1,
                              borderColor: '#E5E5E5',
                              borderRadius: 5,
                              height: switchMerchant ? 35 : 45,
                              justifyContent: 'center',
                              textAlign: 'center',
                              marginTop: 5,
                              width: '20%',
                              fontFamily: 'NunitoSans-Regular',
                              fontSize: switchMerchant ? 10 : 16,
                              backgroundColor: Colors.fieldtBgColor
                            }}
                            placeholder={'0'}
                            placeholderTextColor={Platform.select({
                              ios: '#a9a9a9',
                            })}
                            placeholderStyle={{
                              justifyContent: 'center',
                              fontFamily: 'NunitoSans-Regular',
                              fontSize: switchMerchant ? 10 : 16,
                            }}
                            //iOS
                            clearTextOnFocus
                            //////////////////////////////////////////////
                            //Android
                            onFocus={() => {
                              setTemp(loyaltyCampaignExpirationDays)
                              LoyaltyStore.update(s => {
                                s.loyaltyCampaignExpirationDays = '';
                              });
                              //setLoyaltyCampaignExpirationDays('');
                            }}
                            ///////////////////////////////////////////////
                            //When textinput is not selected
                            onEndEditing={() => {
                              if (loyaltyCampaignExpirationDays == '') {
                                LoyaltyStore.update(s => {
                                  s.loyaltyCampaignExpirationDays = temp;
                                });
                                //setLoyaltyCampaignExpirationDays(temp);
                              }
                            }}
                            //////////////////////////////////////////////
                            onChangeText={(text) => {
                              var value =
                                text.length >= 0
                                  ? !isNaN(text)
                                    ? text
                                    : '0'
                                  : '0';
                              LoyaltyStore.update(s => {
                                s.loyaltyCampaignExpirationDays = value;
                              });
                              //setLoyaltyCampaignExpirationDays(value);
                            }}
                            keyboardType={'decimal-pad'}
                          />
                        </View>
                      )}

                      {/* Absent Customer - Send Absent  */}
                      {(recommendedLoyalty.campaigns[selectedLoyalty].loyaltyCampaignType === LOYALTY_CAMPAIGN_TYPE.AT_RISK) && (
                        <>
                          <View
                            style={{
                              flexDirection: 'column',
                              alignItems: 'flex-start',
                              marginTop: 10,
                            }}>
                            <Text
                              style={[
                                {
                                  fontWeight: '500',
                                  marginRight: 15,
                                  fontFamily: 'NunitoSans-Bold',
                                  fontSize: 18,
                                },
                                switchMerchant
                                  ? {
                                    fontSize: 10,
                                  }
                                  : {},
                              ]}>
                              Send when guest hasn't visited for (days)
                            </Text>
                            <TextInput
                              style={{
                                borderWidth: 1,
                                borderColor: '#E5E5E5',
                                borderRadius: 5,
                                height: switchMerchant ? 35 : 45,
                                justifyContent: 'center',
                                textAlign: 'center',
                                marginTop: 5,
                                width: '20%',
                                fontFamily: 'NunitoSans-Regular',
                                fontSize: switchMerchant ? 10 : 16,
                                backgroundColor: Colors.fieldtBgColor
                              }}
                              placeholder={'0'}
                              placeholderTextColor={Platform.select({
                                ios: '#a9a9a9',
                              })}
                              placeholderStyle={{
                                justifyContent: 'center',
                                fontFamily: 'NunitoSans-Regular',
                                fontSize: switchMerchant ? 10 : 16,
                              }}
                              //iOS
                              clearTextOnFocus
                              //////////////////////////////////////////////
                              //Android
                              onFocus={() => {
                                setTemp(loyaltyCampaignGuestNotVisitedDays)
                                setLoyaltyCampaignGuestNotVisitedDays('');
                              }}
                              ///////////////////////////////////////////////
                              //When textinput is not selected
                              onEndEditing={() => {
                                if (loyaltyCampaignGuestNotVisitedDays == '') {
                                  setLoyaltyCampaignGuestNotVisitedDays(temp);
                                }
                              }}
                              //////////////////////////////////////////////
                              onChangeText={(text) => {
                                var value =
                                  text.length >= 0
                                    ? !isNaN(text)
                                      ? text
                                      : '0'
                                    : '0';

                                setLoyaltyCampaignGuestNotVisitedDays(value);
                              }}
                              keyboardType={'decimal-pad'}
                            />
                          </View>
                        </>
                      )}

                      {/* Absent Customer - Send Absent(MAX) */}
                      {(recommendedLoyalty.campaigns[selectedLoyalty].loyaltyCampaignType === LOYALTY_CAMPAIGN_TYPE.AT_RISK) && (
                        <>
                          <View
                            style={{
                              flexDirection: 'column',
                              alignItems: 'flex-start',
                              marginTop: 10,
                            }}>
                            <Text
                              style={[
                                {
                                  fontWeight: '500',
                                  marginRight: 15,
                                  fontFamily: 'NunitoSans-Bold',
                                  fontSize: 18,
                                },
                                switchMerchant
                                  ? {
                                    fontSize: 10,
                                  }
                                  : {},
                              ]}>
                              Send when guest hasn't visited for (days) (max)
                            </Text>
                            <TextInput
                              style={{
                                borderWidth: 1,
                                borderColor: '#E5E5E5',
                                borderRadius: 5,
                                height: switchMerchant ? 35 : 45,
                                // width: 80,
                                justifyContent: 'center',
                                textAlign: 'center',
                                marginTop: 5,
                                width: '20%',
                                fontFamily: 'NunitoSans-Regular',
                                fontSize: switchMerchant ? 10 : 16,
                                backgroundColor: Colors.fieldtBgColor
                              }}
                              placeholder={'0'}
                              placeholderTextColor={Platform.select({
                                ios: '#a9a9a9',
                              })}
                              placeholderStyle={{
                                justifyContent: 'center',
                                fontFamily: 'NunitoSans-Regular',
                                fontSize: switchMerchant ? 10 : 14,
                              }}
                              //iOS
                              clearTextOnFocus
                              //////////////////////////////////////////////
                              //Android
                              onFocus={() => {
                                setTemp(loyaltyCampaignGuestNotVisitedDaysMax)
                                setLoyaltyCampaignGuestNotVisitedDaysMax('');
                              }}
                              ///////////////////////////////////////////////
                              //When textinput is not selected
                              onEndEditing={() => {
                                if (loyaltyCampaignGuestNotVisitedDaysMax == '') {
                                  setLoyaltyCampaignGuestNotVisitedDaysMax(temp);
                                }
                              }}
                              //////////////////////////////////////////////
                              onChangeText={(text) => {
                                var value =
                                  text.length >= 0
                                    ? !isNaN(text)
                                      ? text
                                      : '0'
                                    : '0';

                                setLoyaltyCampaignGuestNotVisitedDaysMax(value);
                              }}
                              keyboardType={'decimal-pad'}
                            />
                          </View>
                        </>
                      )}

                      {/* Birthday - Send when Bd in */}
                      {(recommendedLoyalty.campaigns[selectedLoyalty].loyaltyCampaignType === LOYALTY_CAMPAIGN_TYPE.BIRTHDAY) && (
                        <>
                          <View
                            style={{
                              flexDirection: 'column',
                              alignItems: 'flex-start',
                              marginTop: 10,
                            }}>
                            <Text
                              style={[
                                {
                                  fontWeight: '500',
                                  marginRight: 15,
                                  fontFamily: 'NunitoSans-Bold',
                                  fontSize: 18,
                                },
                                switchMerchant
                                  ? {
                                    fontSize: 10,
                                  }
                                  : {},
                              ]}>
                              Send when guest birthday is in (days)
                            </Text>
                            <TextInput
                              style={{
                                borderWidth: 1,
                                borderColor: '#E5E5E5',
                                borderRadius: 5,
                                height: switchMerchant ? 35 : 45,
                                justifyContent: 'center',
                                textAlign: 'center',
                                marginTop: 5,
                                width: '20%',
                                fontFamily: 'NunitoSans-Regular',
                                fontSize: switchMerchant ? 10 : 16,
                                backgroundColor: Colors.fieldtBgColor
                              }}
                              placeholder={'0'}
                              placeholderTextColor={Platform.select({
                                ios: '#a9a9a9',
                              })}
                              placeholderStyle={{
                                justifyContent: 'center',
                                fontFamily: 'NunitoSans-Regular',
                                fontSize: switchMerchant ? 10 : 16,
                              }}
                              //iOS
                              clearTextOnFocus
                              //////////////////////////////////////////////
                              //Android
                              onFocus={() => {
                                setTemp(loyaltyCampaignGuestBirthdayBeforeDays)
                                setLoyaltyCampaignGuestBirthdayBeforeDays('');
                              }}
                              ///////////////////////////////////////////////
                              //When textinput is not selected
                              onEndEditing={() => {
                                if (loyaltyCampaignGuestBirthdayBeforeDays == '') {
                                  setLoyaltyCampaignGuestBirthdayBeforeDays(temp);
                                }
                              }}
                              //////////////////////////////////////////////
                              onChangeText={(text) => {
                                var value =
                                  text.length >= 0
                                    ? !isNaN(text)
                                      ? text
                                      : '0'
                                    : '0';

                                setLoyaltyCampaignGuestBirthdayBeforeDays(value);
                              }}
                              keyboardType={'decimal-pad'}
                            />
                          </View>
                        </>
                      )}

                      {/* Growth - Send after every # visits */}
                      {(recommendedLoyalty.campaigns[selectedLoyalty].loyaltyCampaignType === LOYALTY_CAMPAIGN_TYPE.GROWTH) && (
                        <>
                          <View
                            style={{
                              flexDirection: 'column',
                              alignItems: 'flex-start',
                              marginTop: 10,
                            }}>
                            <Text
                              style={[
                                {
                                  fontWeight: '500',
                                  marginRight: 15,
                                  fontFamily: 'NunitoSans-Bold',
                                  fontSize: 18,
                                },
                                switchMerchant
                                  ? {
                                    fontSize: 10,
                                  }
                                  : {},
                              ]}>
                              Send after every (visits)
                            </Text>
                            <TextInput
                              style={{
                                borderWidth: 1,
                                borderColor: '#E5E5E5',
                                borderRadius: 5,
                                height: switchMerchant ? 35 : 45,
                                justifyContent: 'center',
                                textAlign: 'center',
                                marginTop: 5,
                                width: '20%',
                                fontFamily: 'NunitoSans-Regular',
                                fontSize: switchMerchant ? 10 : 16,
                                backgroundColor: Colors.fieldtBgColor
                              }}
                              placeholder={'0'}
                              placeholderTextColor={Platform.select({
                                ios: '#a9a9a9',
                              })}
                              placeholderStyle={{
                                justifyContent: 'center',
                                fontFamily: 'NunitoSans-Regular',
                                fontSize: switchMerchant ? 10 : 16,
                              }}
                              //iOS
                              clearTextOnFocus
                              //////////////////////////////////////////////
                              //Android
                              onFocus={() => {
                                setTemp(loyaltyCampaignGuestAfterEveryVisits)
                                setLoyaltyCampaignGuestAfterEveryVisits('');
                              }}
                              ///////////////////////////////////////////////
                              //When textinput is not selected
                              onEndEditing={() => {
                                if (loyaltyCampaignGuestAfterEveryVisits == '') {
                                  setLoyaltyCampaignGuestAfterEveryVisits(temp);
                                }
                              }}
                              //////////////////////////////////////////////
                              onChangeText={(text) => {
                                var value =
                                  text.length >= 0
                                    ? !isNaN(text)
                                      ? text
                                      : '0'
                                    : '0';

                                setLoyaltyCampaignGuestAfterEveryVisits(value);
                              }}
                              keyboardType={'decimal-pad'}
                            />
                          </View>
                        </>
                      )}

                      {/* Big Spender - Every time spends */}
                      {(recommendedLoyalty.campaigns[selectedLoyalty].loyaltyCampaignType === LOYALTY_CAMPAIGN_TYPE.BIG_SPENDER) && (
                        <>
                          <View
                            style={{
                              flexDirection: 'column',
                              alignItems: 'flex-start',
                              marginTop: 10,
                            }}>
                            <Text
                              style={[
                                {
                                  fontWeight: '500',
                                  marginRight: 15,
                                  fontFamily: 'NunitoSans-Bold',
                                  fontSize: 18,
                                },
                                switchMerchant
                                  ? {
                                    fontSize: 10,
                                  }
                                  : {},
                              ]}>
                              Send every time guest spends (RM)
                            </Text>
                            <TextInput
                              style={{
                                borderWidth: 1,
                                borderColor: '#E5E5E5',
                                borderRadius: 5,
                                height: switchMerchant ? 35 : 45,
                                justifyContent: 'center',
                                textAlign: 'center',
                                marginTop: 5,
                                width: '20%',
                                fontFamily: 'NunitoSans-Regular',
                                fontSize: switchMerchant ? 10 : 16,
                                backgroundColor: Colors.fieldtBgColor
                              }}
                              placeholder={'0'}
                              placeholderTextColor={Platform.select({
                                ios: '#a9a9a9',
                              })}
                              placeholderStyle={{
                                justifyContent: 'center',
                                fontFamily: 'NunitoSans-Regular',
                                fontSize: switchMerchant ? 10 : 16,
                              }}
                              //iOS
                              clearTextOnFocus
                              //////////////////////////////////////////////
                              //Android
                              onFocus={() => {
                                setTemp(loyaltyCampaignGuestAfterEverySpends)
                                setLoyaltyCampaignGuestAfterEverySpends('');
                              }}
                              ///////////////////////////////////////////////
                              //When textinput is not selected
                              onEndEditing={() => {
                                if (loyaltyCampaignGuestAfterEverySpends == '') {
                                  setLoyaltyCampaignGuestAfterEverySpends(temp);
                                }
                              }}
                              //////////////////////////////////////////////
                              onChangeText={(text) => {
                                var value =
                                  text.length >= 0
                                    ? !isNaN(text)
                                      ? text
                                      : '0'
                                    : '0';

                                setLoyaltyCampaignGuestAfterEverySpends(value);
                              }}
                              keyboardType={'decimal-pad'}
                            />
                          </View>
                        </>
                      )}

                      {/* Return */}
                      {(recommendedLoyalty.campaigns[selectedLoyalty].loyaltyCampaignType === LOYALTY_CAMPAIGN_TYPE.RETURN) && (
                        <View
                          style={{
                            marginTop: 10,
                            marginBottom: 20,
                            zIndex: -1,
                          }}>
                          <View style={{ width: '100%' }}>
                            <View style={{ marginBottom: 5, flexDirection: 'row', alignItems: 'center', }}>
                              <TouchableOpacity
                                style={[
                                  {

                                    alignSelf: 'flex-start',
                                  }
                                ]}
                                onPress={() => {
                                  setBatchList([
                                    ...batchList,
                                    {
                                      batchId: uuidv4(),
                                      orderIndex: 0,
                                      batchType: LOYALTY_CAMPAIGN_BATCH_TYPE.SEQUENCE,
                                      voucherId: '',
                                      activationDays: '1',
                                      expirationDays: '14',
                                      sendTime: moment().set('hour', 10).valueOf(),
                                    }
                                  ]);
                                }}>
                                <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                                  <FontAwesome name="plus-circle" size={switchMerchant ? 15 : 20} color={Colors.primaryColor} />
                                  <Text style={{ marginLeft: switchMerchant ? 5 : 5, color: Colors.primaryColor, marginBottom: Platform.OS === 'ios' ? 0 : 1, fontFamily: 'NunitoSans-SemiBold', fontSize: switchMerchant ? 10 : 16, }}>
                                    Add New Batch
                                  </Text>
                                </View>
                              </TouchableOpacity>
                            </View>

                            <View
                              style={{
                                borderWidth: 1,
                                borderColor: '#E5E5E5',
                                borderRadius: 3,
                                paddingTop: 10,
                                zIndex: -1,
                              }}>

                              <View
                                style={{
                                  backgroundColor: '#ffffff',
                                  flexDirection: 'row',
                                  paddingVertical: 20,
                                  paddingHorizontal: 15,
                                  paddingRight: 5,
                                  // marginTop: 10,
                                  borderBottomWidth: StyleSheet.hairlineWidth,
                                  width: '100%',
                                }}>
                                <Text style={{ fontFamily: 'NunitoSans-Bold', fontSize: switchMerchant ? 10 : 16, width: '40%', }}>
                                  Voucher
                                </Text>
                                <Text style={{ fontFamily: 'NunitoSans-Bold', fontSize: switchMerchant ? 10 : 16, width: '15%', }}>
                                  Activation Day(s)
                                </Text>
                                <Text style={{ fontFamily: 'NunitoSans-Bold', fontSize: switchMerchant ? 10 : 16, width: '15.5%', }}>
                                  Expiration Day(s)
                                </Text>
                                <Text style={{ fontFamily: 'NunitoSans-Bold', fontSize: switchMerchant ? 10 : 16, width: '20%', }}>
                                  Send Time
                                </Text>
                              </View>

                              <View style={{
                                shadowOpacity: 0,
                                shadowColor: '#000',
                                shadowOffset: {
                                  width: 0,
                                  height: 1,
                                },
                                shadowOpacity: 0.22,
                                shadowRadius: 1.22,
                                elevation: 1,
                                //maxHeight: switchMerchant ? Dimensions.get('screen').height * 0.6 : null, 
                              }}>
                                <FlatList
                                  nestedScrollEnabled
                                  //horizontal={true}
                                  data={batchList}
                                  extraData={batchList}
                                  renderItem={renderBatchList}
                                  keyExtractor={(item, index) => String(index)}
                                />
                              </View>
                            </View>
                          </View>
                        </View>
                      )}

                    </View>

                    {/* Divider */}
                    <View style={styles.divider} />

                    <View style={{
                      width: '100%',
                      justifyContent: 'flex-start',
                      marginBottom: 5,
                      paddingHorizontal: '2.1%',
                    }}>
                      <Text
                        style={[
                          {
                            fontWeight: '500',

                            fontFamily: 'NunitoSans-Bold',
                            fontSize: 18,
                          },
                          switchMerchant
                            ? {
                              fontSize: 10,
                            }
                            : {},
                        ]}>
                        {`Select Segment `}
                      </Text>
                    </View>

                    {/* Target Segments */}
                    <View style={{
                      width: '100%',
                      flexDirection: 'row',
                      justifyContent: 'flex-start',
                      alignItems: 'center',
                      paddingHorizontal: '2%',
                    }}>

                      {/* DropDownPicker */}
                      <View style={{ width: '25%'}}>
                        <DropDownPicker
                          arrowColor={'black'}
                          arrowSize={20}
                          arrowStyle={{ fontWeight: 'bold' }}
                          style={{
                            width: '100%',
                            backgroundColor: Colors.fieldtBgColor,
                            borderRadius: 10
                          }}
                          placeholderStyle={{
                            color: Colors.fieldtTxtColor,
                            fontFamily: 'NunitoSans-Regular',
                            fontSize: 14,
                          }}
                          placeholder="Select the Segment(s)"
                          itemStyle={{
                            justifyContent: 'flex-start',
                            fontFamily: 'NunitoSans-Regular',
                            fontSize: 16,
                          }}
                          items={CRM_SEGMENT_DROPDOWN_LIST.concat(crmSegmentsDropdownList)}
                          multiple
                          multipleText={'%d segment(s) selected'}
                          customTickIcon={(press) => (
                            <Ionicon
                              name={'checkmark-outline'}
                              color={
                                press
                                  ? Colors.fieldtBgColor
                                  : Colors.primaryColor
                              }
                              size={25}
                            />
                          )}
                          onChangeItem={(items) => {
                            setSelectedTargetSegmentGroupList(items);
                          }}
                          defaultValue={selectedTargetSegmentGroupList}
                          searchable
                          dropDownStyle={{
                            width: '100%',
                            backgroundColor: Colors.fieldtBgColor,
                            borderRadius: 30,
                            borderWidth: 1,
                          }}/>
                      </View>

                      {/* ? Button */}
                      <TouchableOpacity
                        style={{
                          width: '15%',
                          backgroundColor: Colors.primaryColor,
                          borderRadius: 5,
                          justifyContent: 'center',
                          alignItems: 'center',
                          marginLeft: 10,
                          paddingVertical: 10,
                        }}
                      >
                        <Text style={{
                          fontFamily: 'NunitoSans-Regular',
                          color: Colors.whiteColor,
                          fontWeight: "bold",
                          fontSize: switchMerchant ? 10 : 16
                        }}
                        >
                          ESTIMATE REACH
                        </Text>
                      </TouchableOpacity>
                      
                      {/* Estimated Reach */}
                      {selectedTargetSegmentGroupList.length > 0 && (
                        <Text style={{
                          fontFamily: 'NunitoSans-SemiBold',
                          color: Colors.blackColor,
                          marginVertical: 8,
                          marginHorizontal: 10,
                          fontSize: switchMerchant ? 10 : 14
                        }}>
                          {selectedTargetSegmentGroupList.includes('EVERYONE') ? (
                            selectedTargetSegmentGroupList.length === 1 ? (
                              <>Estimated reach: Everyone</>
                            ) : (
                              <>Estimated reach: Everyone (+ {estimatedReachCustomer} customers)</>
                            )
                          ) : (
                            <>Estimated reach: {estimatedReachCustomer} customers</>
                          )}
                        </Text>
                      )}
                      
                    </View>

                    {/* Divider */}
                    <View style={styles.divider} />

                    {/* Next Button - Create Voucher */}
                    <TouchableOpacity
                      style={{
                        width: '16%',
                        backgroundColor: Colors.primaryColor,
                        borderRadius: 5,
                        justifyContent: 'center',
                        alignItems: 'center',
                        paddingVertical: 10,
                        marginHorizontal: 40,
                      }}
                      onPress={nextButtonOnClick}
                    >
                      <Text style={{
                        fontFamily: 'NunitoSans-Regular',
                        color: Colors.whiteColor,
                        fontWeight: "bold",
                        fontSize: switchMerchant ? 10 : 18,
                      }}
                      >
                        Next
                      </Text>
                    </TouchableOpacity>

                  </View>
                )}
              </ScrollView>
            </View>
          }

        </View>
      </View>
    </UserIdleWrapper>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.highlightColor,
    flexDirection: 'row'
  },

  sidebar: {
    width: Dimensions.get('window').width * Styles.sideBarWidth,
  },


  headerLeftStyle: {
    width: Dimensions.get('window').width * 0.17,
    justifyContent: 'center',
    alignItems: 'center'
  },

  divider: {
    width: '95%',
    opacity: 0.7,
    marginVertical: 20,
    alignSelf: 'center',
    borderBottomColor: 'grey',
    borderBottomWidth: 0.8
  },

  flexCenterContainer: {
    justifyContent: 'center',
    alignItems: 'center'
  }
});

export default AiCampaignScreen;