import React, {
  Component,
  useEffect,
  useReducer,
  useState,
  useCallback,
} from 'react';
import {
  StyleSheet,
  ScrollView,
  Image,
  View,
  Text,
  Alert,
  TouchableOpacity,
  TextInput,
  Dimensions,
  FlatList,
  Modal,
  PermissionsAndroid,
  Platform,
  ActivityIndicator,
  useWindowDimensions,
} from 'react-native';
import Colors from '../constant/Colors';
import SideBar from './SideBar';
import * as User from '../util/User';
import Icon from 'react-native-vector-icons/Feather';
import Icon1 from 'react-native-vector-icons/FontAwesome';
import { ReactComponent as EditGreen } from '../assets/svg/EditGreen.svg';
import Icon2 from 'react-native-vector-icons/EvilIcons';
import Icon3 from 'react-native-vector-icons/Foundation';
import Icon4 from 'react-native-vector-icons/FontAwesome5';
import DropDownPicker from 'react-native-dropdown-picker';
// import { ceil } from 'react-native-reanimated';
// import CheckBox from '@react-native-community/checkbox';
import DatePicker from 'react-native-modal-datetime-picker';
import moment from 'moment';
import Close from 'react-native-vector-icons/AntDesign';
// import DatePicker from "react-datepicker";
import ApiClient from '../util/ApiClient';
import API from '../constant/API';
import AsyncStorage from '@react-native-async-storage/async-storage';
import Styles from '../constant/Styles';
// import DocumentPicker from 'react-native-document-picker';
import Ionicons from 'react-native-vector-icons/Ionicons';
import 'react-native-get-random-values';
import { customAlphabet } from 'nanoid';
import { CommonStore } from '../store/commonStore';
import { MerchantStore } from '../store/merchantStore';
import { UserStore } from '../store/userStore';
import { Picker } from 'react-native';
import {
  STOCK_TRANSFER_STATUS,
  STOCK_TRANSFER_STATUS_PARSED,
  EMAIL_REPORT_TYPE,
  PURCHASE_ORDER_STATUS,
} from '../constant/common';
import { convertArrayToCSV, generateEmailReport } from '../util/common';
// import RNPickerSelect from 'react-native-picker-select';
import AntDesign from 'react-native-vector-icons/AntDesign';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import XLSX from 'xlsx';
import { v4 as uuidv4 } from 'uuid';
import GCalendar from '../assets/svg/GCalendar';
import GCalendarGrey from '../assets/svg/GCalendarGrey';
// import { CSVLink } from "react-csv";
// import Select from "react-select";
import { OutletStore } from '../store/outletStore';
// import MultiSelect from "react-multiple-select-dropdown-lite";
// import "../constant/styles.css";
import personicon from '../assets/image/default-profile.png';
import headerLogo from '../assets/image/logo.png';
// import DatePicker from "react-datepicker";
// import "react-datepicker/dist/react-datepicker.css";
// import "../constant/datePicker.css";
import APILocal from '../util/apiLocalReplacers';
// import {isMobile} from '../util/common';
// import TopBar from './TopBar';
// import { Html5QrcodeScanner } from "html5-qrcode";
import Ionicon from 'react-native-vector-icons/Ionicons';
import { Camera, CameraType } from 'react-native-camera-kit';
// const RNFS = require('react-native-fs');

const alphabet = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ';
const nanoid = customAlphabet(alphabet, 12);

const RackStockTransfer = props => {
  //port til may 11 2023 changes but without dec 15 2022(delivery order htmltopdf export) commit
  const { navigation } = props;

  const { width: windowWidth, height: windowHeight } = useWindowDimensions();

  const [stockTransfer, setStockTransfer] = useState(true);
  const [addPurchase, setAddPurchase] = useState(false);
  const [editPurchase, setEditPurchase] = useState(false);
  const [addStockTransfer, setAddStockTransfer] = useState(false);
  const [stockList, setStockList] = useState([]);
  const [stockTransferList, setStockTransferList] = useState([]);
  const [stockTakeList, setStockTakeList] = useState([]);
  const [orderList, setOrderList] = useState([]);
  const [itemsToOrder, setItemsToOrder] = useState([{}, {}, {}]);
  const [itemsToOrder2, setItemsToOrder2] = useState([{}, {}, {}]);
  const [productList, setProductList] = useState([]);
  const [isSelected, setIsSelected] = useState(false);
  const [isSelected2, setIsSelected2] = useState(false);
  const [isSelected3, setIsSelected3] = useState(true);
  const [isSelected4, setIsSelected4] = useState(false);
  const [isDateTimePickerVisible, setIsDateTimePickerVisible] = useState(false);
  const [date, setDate] = useState(Date.now());
  const [date1, setDate1] = useState(Date.now());
  const [createdDate, setCreatedDate] = useState(Date.now());
  const [visible, setVisible] = useState(false);
  const [Email, setEmail] = useState('');
  const [modal, setModal] = useState(false);
  // const [// outletId, set// outletId] = useState(1);
  const [outletId, setOutletId] = useState(User.getOutletId());
  const [search, setSearch] = useState('');
  const [search2, setSearch2] = useState('');
  const [search3, setSearch3] = useState('');
  const [ideal, setIdeal] = useState('');
  const [minimum, setMinimum] = useState('');
  const [itemId, setItemId] = useState('');
  const [choose, setChoose] = useState(null);

  const [loading, setLoading] = useState(false);
  const [visibleItems, setVisibleItems] = useState(1);
  //////////////////////////////////////////////////////////////////////
  const [isButtonDisabled, setIsButtonDisabled] = useState(true);
  const [poId, setPoId] = useState('');
  const [editMode, setEditMode] = useState(false);
  const [switchMerchant, setSwitchMerchant] = useState(false);
  const [exportEmail, setExportEmail] = useState('');
  const [exportModal, setExportModal] = useState(false);
  const [importModal, setImportModal] = useState(false);

  const [isLoadingExcel, setIsLoadingExcel] = useState(false);
  const [isLoadingCsv, setIsLoadingCsv] = useState(false);

  const [poStatus, setPoStatus] = useState(STOCK_TRANSFER_STATUS.CREATED);

  const [targetOutletDropdownList, setTargetOutletDropdownList] = useState([]);
  const [selectedTargetOutletId, setSelectedTargetOutletId] = useState('');

  const [selectedSourceOutletIdPrev, setSelectedSourceOutletIdPrev] =
    useState('');
  const [selectedSourceOutletId, setSelectedSourceOutletId] = useState('');

  const [outletSupplyItemDropdownList, setOutletSupplyItemDropdownList] =
    useState([]);

  const [selectedSourceRackId, setSelectedSourceRackId] = useState('NONE');
  const [selectedSourceRackName, setSelectedSourceRackName] = useState('');
  const [selectedSourceRack, setSelectedSourceRack] = useState(null);
  const [selectedTargetRackId, setSelectedTargetRackId] = useState('NONE');
  const [selectedTargetRackName, setSelectedTargetRackName] = useState('');
  const [selectedTargetRack, setSelectedTargetRack] = useState(null);
  const [rackDropdownList, setRackDropdownList] = useState([]);
  const [showScannerSource, setShowScannerSource] = useState(false);
  const [showScannerTarget, setShowScannerTarget] = useState(false);

  const [poItems, setPoItems] = useState([
    {
      outletSupplyItemId: '',
      name: '',
      sku: '',
      unit: '',
      skuMerchant: '',
      quantity: 0,
      transferQuantity: 0,
      balance: 0,
      price: 0,
      totalPrice: 0,

      supplyItem: null,
    },
  ]);

  const [subtotal, setSubtotal] = useState(0);
  const [taxTotal, setTaxTotal] = useState(0);
  const [discountTotal, setDiscountTotal] = useState(0);
  const [finalTotal, setFinalTotal] = useState(0);

  const [filteredDropdownList, setFilteredDropdownList] = useState([]);
  const [outletSupplyItems, setOutletSupplyItems] = useState([]);
  const [showScanner, setShowScanner] = useState(false);
  const [showScannerItem, setShowScannerItem] = useState(false);
  const [cartItemBybc, setCartItemBybc] = useState([]);
  const [sLable, setSLabel] = useState('');

  let html5QrcodeScannerRef = null;
  // let html5QrcodeScannerRef2 = null

  const handleIconPress = () => {
    setShowScanner(prev => !prev); // Toggle showScanner state
  };
  const handleIconPressSource = () => {
    setShowScannerSource(prev => !prev); // Toggle showScannerRack state
  };
  const handleIconPressTarget = () => {
    setShowScannerTarget(prev => !prev); // Toggle showScannerProduct state
  };

  const qrReaderCallback = useCallback(
    node => {
      if (node !== null && showScannerSource) {
        // do stuff

        console.log(node);

        console.log('readerSource');
        // console.log(document.getElementById('readerSource'));

        /*   let html5QrcodeScannerRef = new Html5QrcodeScanner(
          node.id,
          {fps: 10, qrbox: {width: 250, height: 250}},
          /* verbose= *false,
        );
        html5QrcodeScannerRef.render(onScanSuccess, onScanFailure);
   */
      }
    },
    [showScannerSource, showScannerTarget],
  );

  const onScanSuccess = (decodedText, decodedResult) => {
    // handle the scanned code as you like, for example:
    console.log(`Code matched = ${decodedText}`, decodedResult);
    console.log(global.cameraScanState);
    const rack = getRackByQrCode(decodedText);
    if (!rack) {
      Alert.alert('This QR code does not belong to any rack.');
      return;
    }
    if (global.cameraScanState === 'readerSource') {
      setActiveSourceRack(rack, true);
      setShowScannerSource(false);
    } else {
      setActiveTargetRack(rack);
      setShowScannerTarget(false);
    }
    console.log(global.cameraScanState);
  };

  const onScanFailure = error => {
    // handle scan failure, usually better to ignore and keep scanning.
    // for example:
    console.warn(`Code scan error = ${error}`);
  };

  const onScanSuccessSource = (decodedText, decodedResult) => {
    // handle the scanned code as you like, for example:
    console.log(`Code matched = ${decodedText}`, decodedResult);
    console.log(global.cameraScanState);
    const rack = getRackByQrCode(decodedText);
    if (!rack) {
      Alert.alert('This QR code does not belong to any rack.');
      return;
    }
    if (global.cameraScanState === 'readerSource') {
      setActiveSourceRack(rack, true);
      setShowScannerSource(false);
    } else {
      setActiveTargetRack(rack);
      setShowScannerTarget(false);
    }
    console.log(global.cameraScanState);
  };

  const onScanFailureSource = error => {
    // handle scan failure, usually better to ignore and keep scanning.
    // for example:
    console.warn(`Code scan error = ${error}`);
  };

  const onScanSuccessTarget = (decodedText, decodedResult) => {
    // handle the scanned code as you like, for example:
    console.log(`Code matched = ${decodedText}`, decodedResult);
    console.log(global.cameraScanState);
    const rack = getRackByQrCode(decodedText);
    if (!rack) {
      Alert.alert('This QR code does not belong to any rack.');
      return;
    }
    if (global.cameraScanState === 'readerSource') {
      setActiveSourceRack(rack, true);
      setShowScannerSource(false);
    } else {
      setActiveTargetRack(rack);
      setShowScannerTarget(false);
    }
    console.log(global.cameraScanState);
  };

  const onScanFailureTarget = error => {
    // handle scan failure, usually better to ignore and keep scanning.
    // for example:
    console.warn(`Code scan error = ${error}`);
  };

  const racks = CommonStore.useState(s => s.racks);

  // const outletItems = OutletStore.useState((s) => s.outletItems);
  const outletItems = CommonStore.useState(s => s.outletSupplyItems); // use composite data but remain the name
  // const allOutletsItems = OutletStore.useState((s) => s.allOutletsItems);
  const allOutletsItems = CommonStore.useState(s => s.supplyItems); // use composite data but remain the name
  // const [supplyItems, setSupplyItems] = useState([]);
  const supplyItems = CommonStore.useState(s => s.supplyItems);
  const supplyItemsSkuDict = CommonStore.useState(s => s.supplyItemsSkuDict);

  const allOutletsSupplyItemsSkuDict = CommonStore.useState(
    s => s.allOutletsSupplyItemsSkuDict,
  );
  const allOutletsSupplyItems = CommonStore.useState(
    s => s.allOutletsSupplyItems,
  );
  const allOutletsSupplyItemsDict = CommonStore.useState(
    s => s.allOutletsSupplyItemsDict,
  );

  const allOutlets = MerchantStore.useState(s => s.allOutlets);
  const merchantId = UserStore.useState(s => s.merchantId);
  const stockTransfersRack = CommonStore.useState(s => s.stockTransfersRack);

  const userName = UserStore.useState(s => s.name);
  const userId = UserStore.useState(s => s.firebaseUid);
  const merchantName = MerchantStore.useState(s => s.name);

  const currOutlet = MerchantStore.useState(s => s.currOutlet);
  const currOutletId = MerchantStore.useState(s => s.currOutletId);

  const isLoading = CommonStore.useState(s => s.isLoading);

  const dropDownRef = React.useRef();
  const dropDownRef1 = React.useRef();

  const selectedRackStockTransferEdit = CommonStore.useState(
    s => s.selectedRackStockTransferEdit,
  );
  const outletSelectDropdownView = CommonStore.useState(
    s => s.outletSelectDropdownView,
  );

  const [rev_date, setRev_date] = useState(
    moment().subtract(6, 'days').startOf('day'),
  );
  const [isDatePickerVisible, setDatePickerVisibility] = useState(false);

  const [rev_date1, setRev_date1] = useState(
    moment().endOf(Date.now()).endOf('day'),
  );
  const [isDatePickerVisible1, setDatePickerVisibility1] = useState(false);

  const [openSS, setOpenSS] = useState(false);
  const [openDS, setOpenDS] = useState(false);
  const [openList, setOpenList] = useState([]);

  useEffect(() => {
    if (
      currOutletId !== '' &&
      allOutlets.length > 0 &&
      Array.isArray(stockTransfer) &&
      stockTransfersRack.length > 0
    ) {
      var stockTransferProductTemp = [];
      for (var i = 0; i < stockTransfersRack.length; i++) {
        if (
          moment(rev_date).isSameOrBefore(stockTransfersRack[i].createdAt) &&
          moment(rev_date1).isAfter(stockTransfersRack[i].createdAt)
        ) {
          stockTransferProductTemp.push(stockTransfersRack[i]);
        }
      }
      stockTransferProductTemp.sort((a, b) => b.orderDate - a.orderDate);
      setStockTransferList(stockTransferProductTemp);
    }
  }, [currOutletId, rev_date, rev_date1, stockTransfersRack]);

  useEffect(() => {
    if (selectedRackStockTransferEdit) {
      // insert info

      setEditMode(false);

      setPoId(selectedRackStockTransferEdit.stId);
      setPoStatus(selectedRackStockTransferEdit.status);
      setSelectedSourceOutletId(selectedRackStockTransferEdit.sourceOutletId);
      setSelectedTargetOutletId(selectedRackStockTransferEdit.targetOutletId);
      setSelectedSourceRackName(selectedRackStockTransferEdit.sourceRackName);
      const foundSourceRack = racks.find(
        rack => rack.uniqueId === selectedRackStockTransferEdit.rackId,
      );
      setSelectedSourceRackId(
        foundSourceRack ? foundSourceRack.uniqueId : 'NONE',
      );
      setSelectedSourceRack(foundSourceRack ? foundSourceRack : null);
      const foundTargetRack = racks.find(
        rack => rack.uniqueId === selectedRackStockTransferEdit.rackId,
      );
      setSelectedTargetRackId(
        foundTargetRack ? foundTargetRack.uniqueId : 'NONE',
      );
      setSelectedTargetRack(foundTargetRack ? foundTargetRack : null);
      setSelectedTargetRackName(selectedRackStockTransferEdit.targetRackName);

      setDate(selectedRackStockTransferEdit.estimatedArrivalDate);
      setCreatedDate(selectedRackStockTransferEdit.createdAt);

      if (selectedRackStockTransferEdit.stItems) {
        setPoItems(
          selectedRackStockTransferEdit.stItems.map(mapItem => {
            return {
              ...mapItem,
              updated: false,
            };
          }),
        );

        // delay the state update
        setTimeout(() => {
          setPoItems(
            selectedRackStockTransferEdit.stItems.map(mapItem => {
              return {
                ...mapItem,
                updated: false,
              };
            }),
          );
        }, 100);
      }
    } else {
      // designed to always mounted, thus need clear manually...

      setEditMode(false);

      if (Array.isArray(stockTransfersRack) && stockTransfersRack.length > 0) {
        // setPoId(`ST${moment().format('MMM').toUpperCase() + moment().format('YY') + (stockTransfers.length + 1).toString().padStart(4, '0')}`);
        setPoId(
          `ST${(stockTransfersRack.length + 1).toString().padStart(4, '0')}`,
        );
      }
      setPoStatus(STOCK_TRANSFER_STATUS.CREATED);

      if (allOutlets.length > 0) {
        setSelectedSourceOutletId(allOutlets[0].uniqueId);
        setSelectedTargetOutletId(allOutlets[0].uniqueId);
      }

      setSelectedSourceRackId('NONE');
      setSelectedSourceRack(null);
      setSelectedSourceRackName('');
      setSelectedTargetRackId('NONE');
      setSelectedTargetRack(null);
      setSelectedTargetRackName('');

      setDate(Date.now());

      if (outletItems.length > 0) {
        setPoItems([
          {
            outletSupplyItemId: outletItems[0].uniqueId,
            name: outletItems[0].name,
            sku: outletItems[0].sku,
            unit: '',
            skuMerchant: outletItems[0].skuMerchant,
            quantity: outletItems[0].stockCount || 0,
            transferQuantity: 0,
            balance: 0,
            price: outletItems[0].price,
            totalPrice: 0,

            supplyItem: outletItems[0],
          },
        ]);
      }
    }
  }, [selectedRackStockTransferEdit, addStockTransfer]);

  useEffect(() => {
    if (
      selectedRackStockTransferEdit === null &&
      Array.isArray(stockTransfersRack) &&
      stockTransfersRack.length > 0
    ) {
      // setPoId(`ST${moment().format('MMM').toUpperCase() + moment().format('YY') + (stockTransfers.length + 1).toString().padStart(4, '0')}`);
      setPoId(
        `ST${(stockTransfersRack.length + 1).toString().padStart(4, '0')}`,
      );
    }
  }, [stockTransfersRack]);

  useEffect(() => {
    if (outletItems.length > 0) {
    } else {
      setPoItems([
        {
          outletSupplyItemId: '',
          name: '',
          sku: '',
          unit: '',
          skuMerchant: '',
          quantity: 0,
          transferQuantity: 0,
          price: 0,
          totalPrice: 0,

          supplyItem: null,
        },
      ]);
    }
  }, [selectedSourceOutletId]);

  // useEffect(() => {
  //     setOutletSupplyItems(
  //         allOutletsSupplyItems.filter((outletSupplyItem) => {
  //             if (
  //                 outletSupplyItem.outletId === selectedSourceOutletId &&
  //                 outletSupplyItem.quantity > 0
  //             ) {
  //                 return true;
  //             }
  //         })
  //     );
  // }, [allOutletsSupplyItems, selectedSourceOutletId]);

  useEffect(() => {
    var outletSupplyItemsTemp = [];

    if (selectedSourceRack) {
      const rackItemSkuList = selectedSourceRack.itemSkuList;

      outletSupplyItemsTemp = outletItems.filter(outletSupplyItem => {
        if (
          outletSupplyItem.outletId === currOutletId &&
          rackItemSkuList.includes(outletSupplyItem.sku)
        ) {
          return true;
        } else {
          return false;
        }
        //return true;
      });
    }

    setOutletSupplyItems(outletSupplyItemsTemp);
  }, [
    // allOutletsSupplyItems,

    // racks,

    outletItems,
    // selectedTargetOutletId,
    currOutletId,
    // selectedSupplierId,
    selectedSourceRackId,
    selectedSourceRack,
  ]);

  useEffect(() => {
    const outletDropdownListTemp = allOutlets.map(outlet => ({
      label: outlet.name,
      value: outlet.uniqueId,
    }));

    setTargetOutletDropdownList(outletDropdownListTemp);

    if (selectedTargetOutletId === '' && allOutlets.length > 0) {
      setSelectedTargetOutletId(allOutlets[0].uniqueId);
      setSelectedSourceOutletId(allOutlets[0].uniqueId);
    }
  }, [allOutlets]);

  useEffect(() => {
    setRackDropdownList(
      [
        {
          label: 'None',
          value: 'NONE',
        },
      ].concat(
        racks?.map(rack => ({
          label: rack.name,
          value: rack.uniqueId,
        })),
      ),
    );

    // if (selectedRackId === "" && racks.length > 0) {
    //   // setSelectedRackId(racks[0].uniqueId);
    //   setSelectedRackId('ALL');
    // }
    // console.log('1');
  }, [racks]);

  useEffect(() => {
    setOutletSupplyItemDropdownList(
      outletItems.map(outletSupplyItem => {
        // if (selectedSupplierId === supplyItem.supplierId) {
        //   return { label: supplyItem.name, value: supplyItem.uniqueId };
        // }

        return {
          label: outletSupplyItem.name,
          value: outletSupplyItem.uniqueId,
        };
      }),
    );

    if (
      outletItems.length > 0 &&
      poItems.length === 1 &&
      poItems[0].outletSupplyItemId === ''
    ) {
      setPoItems([
        {
          outletSupplyItemId: outletItems[0].uniqueId,
          name: outletItems[0].name,
          sku: outletItems[0].sku,
          unit: '',
          skuMerchant: outletItems[0].skuMerchant,
          quantity: outletItems[0].stockCount || 0,
          transferQuantity: 0,
          balance: 0,
          price: outletItems[0].price,
          totalPrice: 0,

          supplyItem: outletItems[0],
        },
      ]);
    } else if (
      poItems[0].outletSupplyItemId !== ''
      // &&
      // Object.keys(allOutletsSupplyItemsDict).length > 0
    ) {
      if (
        selectedSourceOutletIdPrev.length > 0 &&
        selectedSourceOutletIdPrev !== selectedSourceOutletId
      ) {
        // reset current outlet supply items

        setPoItems([
          {
            outletSupplyItemId: outletItems[0].uniqueId,
            name: outletItems[0].name,
            unit: '',
            sku: outletItems[0].sku,
            skuMerchant: outletItems[0].skuMerchant,
            quantity: outletItems[0].stockCount || 0,
            transferQuantity: 0,
            balance: 0,
            price: outletItems[0].price,
            totalPrice: 0,

            supplyItem: outletItems[0],
          },
        ]);

        // disabled first, outletSupplyItems might slow to retrieve
        // setSelectedSourceOutletIdPrev(selectedSourceOutletId);
      } else {
        var poItemsTemp = [...poItems];

        for (var i = 0; i < poItemsTemp.length; i++) {
          const foundOutletItem = allOutletsItems.find(
            item => item.uniqueId === poItemsTemp[i].outletSupplyItemId,
          );

          let stockCountToUse = 0;
          if (foundOutletItem) {
            stockCountToUse = foundOutletItem.stockCount
              ? foundOutletItem.stockCount
              : 0;
          }

          if (poItemsTemp[i].quantity !== undefined) {
            stockCountToUse = poItemsTemp[i].quantity;
          }

          poItemsTemp[i] = {
            ...poItemsTemp[i],
            quantity: stockCountToUse ? stockCountToUse : 0,
            price: foundOutletItem ? foundOutletItem.price || 0 : 0,
          };
        }

        setPoItems(poItemsTemp);
      }
    }
  }, [
    // outletSupplyItems,
    // allOutletsSupplyItemsDict,
    // supplyItems,
    outletItems,
    allOutletsItems,
    selectedSourceOutletIdPrev,
  ]);

  useEffect(() => {
    console.log('balance');
    console.log(poItems.reduce((accum, poItem) => accum + poItem.balance, 0));
    setSubtotal(poItems.reduce((accum, poItem) => accum + poItem.balance, 0));
  }, [poItems]);

  useEffect(() => {
    console.log('subtotal');
    console.log(
      poItems.reduce((accum, poItem) => accum + poItem.totalPrice, 0),
    );
    setSubtotal(
      poItems.reduce((accum, poItem) => accum + poItem.totalPrice, 0),
    );
  }, [poItems]);

  useEffect(() => {
    console.log('finalTotal');
    console.log(subtotal - discountTotal + taxTotal);
    setFinalTotal(subtotal - discountTotal + taxTotal);
  }, [subtotal, discountTotal, taxTotal]);

  useEffect(() => {
    requestStoragePermission();

    setPoId(nanoid());
  }, []);

  //////////////////////////////////////////////////////////////////////

  const setState = () => { };

  // navigation.dangerouslyGetParent().setOptions({
  //   tabBarVisible: false
  // });

  navigation.setOptions({
    headerBackVisible: false,
    headerLeft: () => (
      <TouchableOpacity
        style={{}}
        onPress={() => {
          props.navigation.goBack();
        }}>
        <View
          style={{
            marginRight: 10,
            display: 'flex',
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'flex-start',
            marginTop: 10,
            // opacity: 0.8,
          }}>
          <Ionicons
            name="chevron-back"
            size={26}
            color={Colors.whiteColor}
            style={{}}
          />

          <Text
            style={{
              color: Colors.whiteColor,
              fontSize: 16,
              textAlign: 'center',
              fontFamily: 'NunitoSans-SemiBold',
              // lineHeight: 22,
              marginTop: -3,
            }}>
            Back
          </Text>
        </View>
      </TouchableOpacity>
    ),
    headerTitle: () => (
      <View
        style={{
          justifyContent: 'center',
          alignItems: 'center',
          bottom: -2,
        }}>
        <Text
          style={{
            fontSize: 25,
            // lineHeight: 25,
            textAlign: 'center',
            fontFamily: 'NunitoSans-Bold',
            color: Colors.whiteColor,
            // opacity: 0.8,
            width: windowWidth * 0.55,
          }}
          numberOfLines={1}>
          Rack Stock Transfer
        </Text>
      </View>
    ),
    headerRight: () => (
      <View
        style={{
          flexDirection: 'row',
          justifyContent: 'center',
          alignItems: 'center',
        }}>
        <TouchableOpacity
          onPress={() => {
            props.navigation.navigate('Profile');
          }}>
          <Image
            style={{
              width: 32,
              height: 32,
              marginTop: 8,
              marginRight: 10,
            }}
            source={require('../assets/image/drawer.png')}
          />
        </TouchableOpacity>
      </View>
    ),
  });

  const requestStoragePermission = async () => {
    try {
      const granted = await PermissionsAndroid.request(
        PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE,
        {
          title: 'KooDoo Merchant Storage Permission',
          message: 'KooDoo Merchant App needs access to your storage ',
          buttonNegative: 'Cancel',
          buttonPositive: 'OK',
        },
      );
      if (granted === PermissionsAndroid.RESULTS.GRANTED) {
        console.log('Storage permission granted');
      } else {
        console.log('Storage permission denied');
      }
    } catch (err) {
      console.warn(err);
    }
  };

  const convertTemplateToExcelFormat = () => {
    var excelTemplate = [];

    for (var i = 0; i < poItems.length; i++) {
      if (i <= 5) {
        var excelColumn = {
          'Product Name': poItems[i].name,
          SKU: poItems[i].skuMerchant,
          Unit: poItems[i].unit,
          'In stock': poItems[i].quantity,
          'Transfer Qty': poItems[i].transferQuantity,
          'Balance Stock': poItems[i].balance,
          'Cost(RM)': poItems[i].price,
          'Subtotal(RM)': poItems[i].totalPrice,
        };
        excelTemplate.push(excelColumn);
      } else {
      }
    }

    console.log('excelTemplate');
    console.log(excelTemplate);

    return excelTemplate;
  };

  const importSelectFile = async () => { };

  //error show readAsArrayBuffer not implemented

  const convertDataToExcelFormat = () => {
    var excelData = [];

    for (var i = 0; i < stockTransferList.length; i++) {
      for (var j = 0; j < stockTransferList[i].stItems.length; j++) {
        var excelRow = {
          'Stock Transfer ID': stockTransferList[i].stId,
          'Created Date': moment(stockTransferList[i].orderDate).format(
            'DD/MM/YYYY',
          ),
          From: stockTransferList[i].sourceRackName,
          To: stockTransferList[i].targetRackName,
          'Transfer Product': stockTransferList[i].stItems[j].name,
          'In Stock': stockTransferList[i].stItems[j].quantity
            ? stockTransferList[i].stItems[j].quantity.toFixed(2)
            : '0',
          'Transfer Quantity': stockTransferList[i].stItems[j].transferQuantity
            ? stockTransferList[i].stItems[j].transferQuantity
            : '0',
          Balance: stockTransferList[i].stItems[j].balance
            ? stockTransferList[i].stItems[j].balance.toFixed(2)
            : '0',
          Status: stockTransferList[i].status,
        };

        excelData.push(excelRow);
      }
    }

    // console.log('excelData');
    // console.log(excelData);

    return excelData;
  };
  const handleExportExcel = () => {
    var wb = XLSX.utils.book_new(),
      ws = XLSX.utils.json_to_sheet(convertDataToExcelFormat());

    XLSX.utils.book_append_sheet(wb, ws, 'StockTransferProduct');
    XLSX.writeFile(wb, 'KooDoo_Stock_Transfer_Product_Report.xlsx');
  };

  // function here

  const renderStockTransferItem = ({ item }) => (
    <TouchableOpacity
      style={{
        backgroundColor: '#ffffff',
        flexDirection: 'row',
        paddingVertical: 20,
        paddingHorizontal: 15,
        borderBottomWidth: StyleSheet.hairlineWidth,
        borderBottomColor: '#c4c4c4',
        alignItems: 'center',
        borderBottomLeftRadius: 5,
        borderBottomRightRadius: 5,
      }}
      onPress={() => {
        CommonStore.update(s => {
          s.selectedRackStockTransferEdit = item;
        });

        setStockTransfer(false);
        setAddStockTransfer(true);
      }}>
      <Text
        style={{
          fontSize: switchMerchant ? 10 : 14,
          fontFamily: 'NunitoSans-Regular',
          width: '20%',
          color: Colors.primaryColor,
          marginRight: 2,
        }}>
        ST{item.stId}
      </Text>
      <Text
        style={{
          fontSize: switchMerchant ? 10 : 14,
          fontFamily: 'NunitoSans-Regular',
          width: '20%',
          marginRight: 2,
        }}>
        {moment(item.createdAt).format('DD MMM YYYY')}
      </Text>
      <Text
        style={{
          fontSize: switchMerchant ? 10 : 14,
          fontFamily: 'NunitoSans-Regular',
          width: '30%',
          marginHorizontal: 2,
        }}>
        {`${item.sourceRackName}/${item.targetRackName}`}
      </Text>
      {/* <Text
                style={{
                    fontSize: switchMerchant ? 10 : 14,
                    fontFamily: "NunitoSans-Regular",
                    width: "21%",
                    marginHorizontal: 4,
                }}
            >
                {item.targetOutletName}
            </Text> */}
      <View
        style={{
          width: '25%',
          marginLeft: 2,
        }}>
        <View
          style={{
            // width: 80,
            alignItems: 'center',
            borderRadius: 10,
            padding: 5,
            backgroundColor:
              item.status == 0
                ? '#dedede'
                : item.status == 1
                  ? Colors.secondaryColor
                  : Colors.primaryColor,
          }}>
          <Text
            style={{
              color:
                item.status == 0
                  ? Colors.blackColor
                  : item.status == 1
                    ? Colors.blackColor
                    : Colors.whiteColor,
              fontSize: 14,
              fontFamily: 'NunitoSans-Regular',
            }}>
            {/* {item.status == 0 ? "Created" : item.status == 1 ? "Shipped" : "Completed"} */}
            {STOCK_TRANSFER_STATUS_PARSED[item.status]}
            {/* {selectedRackStockTransferEdit.status} */}
          </Text>
        </View>
      </View>
    </TouchableOpacity>
  );

  const renderAddStock = ({ item, index }) => {
    return (
      <View
        style={{
          backgroundColor: '#ffffff',
          flexDirection: 'row',
          paddingVertical: 20,
          paddingHorizontal: 2,
          //paddingBottom: 100,
          borderBottomWidth: StyleSheet.hairlineWidth,
          borderBottomColor: '#c4c4c4',
          alignItems: 'center',
          height: 170,
        }}>
        <View style={{ flex: 1.8 }}>
          <View
            style={{
              width: 230,
              height: 40,
            }}>
            {filteredDropdownList.find(
              dropdownItem =>
                dropdownItem.value === poItems[index].outletSupplyItemId,
            ) ? (
              <DropDownPicker
                style={{
                  backgroundColor: Colors.fieldtBgColor,
                  width: windowWidth * 0.23,
                  height: windowHeight * 0.05,
                  borderRadius: 10,
                  borderWidth: 1,
                  borderColor: '#E5E5E5',
                  flexDirection: 'row',
                  alignItems: 'center', // Center vertically
                }}
                dropDownContainerStyle={{
                  width: windowWidth * 0.23,
                  height: windowHeight * 0.15,
                  backgroundColor: Colors.fieldtBgColor,
                  borderColor: '#E5E5E5',
                  position: 'relative',
                  top: 0,
                }}
                labelStyle={{
                  marginLeft: 5,
                  flexDirection: 'row',

                  alignSelf: 'center',
                }}
                textStyle={{
                  fontSize: 14,
                  fontFamily: 'NunitoSans-Regular',
                  marginLeft: 5,
                  paddingVertical: 10,
                  flexDirection: 'row',

                  flex: 1,
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  whiteSpace: 'nowrap',
                }}
                selectedItemContainerStyle={{
                  flexDirection: 'row',
                }}
                showArrowIcon={true}
                ArrowDownIconComponent={({ style }) => (
                  <Ionicon
                    size={25}
                    color={Colors.fieldtTxtColor}
                    style={{ paddingHorizontal: 5 }}
                    name="chevron-down-outline"
                  />
                )}
                ArrowUpIconComponent={({ style }) => (
                  <Ionicon
                    size={25}
                    color={Colors.fieldtTxtColor}
                    style={{ paddingHorizontal: 5 }}
                    name="chevron-up-outline"
                  />
                )}
                showTickIcon={true}
                TickIconComponent={({ press }) => (
                  <Ionicon
                    style={{ paddingHorizontal: 5, marginTop: 5 }}
                    color={press ? Colors.fieldtBgColor : Colors.primaryColor}
                    name={'md-checkbox'}
                    size={25}
                  />
                )}
                dropDownDirection="BOTTOM"
                disabled={
                  selectedRackStockTransferEdit &&
                  (selectedRackStockTransferEdit.status ===
                    STOCK_TRANSFER_STATUS.COMPLETED ||
                    selectedRackStockTransferEdit.status ===
                    STOCK_TRANSFER_STATUS.CREATED)
                }
                value={poItems[index].outletSupplyItemId}
                onSelectItem={value => {
                  if (value) {
                    // const outletSupplyItemSku = allOutletsSupplyItemsDict[value].sku;

                    // const supplyItem = supplyItemsSkuDict[outletSupplyItemSku] ? supplyItemsSkuDict[outletSupplyItemSku] : null;

                    const supplyItem = allOutletsItems.find(
                      findItem => findItem.uniqueId === value.value,
                    );

                    setPoItems(
                      poItems.map((poItem, i) =>
                        i === index
                          ? {
                            ...poItem,
                            outletSupplyItemId: value.value,
                            name: supplyItem.name,
                            sku: supplyItem.sku,
                            unit: '',
                            quantity: supplyItem.stockCount || 0,
                            transferQuantity: 0,
                            price: supplyItem.price,
                            totalPrice: 0,

                            supplyItem: supplyItem,
                          }
                          : poItem,
                      ),
                    );
                  }
                }}
                listMode="SCROLLVIEW"
                scrollViewProps={{
                  nestedScrollEnabled: true,
                }}
                items={filteredDropdownList}
                open={openList[index]}
                setOpen={value => {
                  setOpenList(prevOpenList => {
                    const newOpenList = [...prevOpenList];
                    newOpenList[index] = value;
                    return newOpenList;
                  });
                }}
              />
            ) : (
              <DropDownPicker
                style={{
                  backgroundColor: Colors.fieldtBgColor,
                  width: windowWidth * 0.23,
                  height: windowHeight * 0.05,
                  borderRadius: 10,
                  borderWidth: 1,
                  borderColor: '#E5E5E5',
                  flexDirection: 'row',
                }}
                containerStyle={{
                  width: windowWidth * 0.23,
                  height: windowHeight * 0.04,
                }}
                dropDownContainerStyle={{
                  // width: 210,
                  width: windowWidth * 0.23,
                  backgroundColor: Colors.fieldtBgColor,
                  borderColor: '#E5E5E5',
                  position: 'relative',
                  top: 0,
                }}
                labelStyle={{
                  marginLeft: 5,
                  flexDirection: 'row',
                }}
                textStyle={{
                  fontSize: 14,
                  fontFamily: 'NunitoSans-Regular',

                  marginLeft: 5,
                  paddingVertical: 10,
                  flexDirection: 'row',

                  flex: 1,
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  whiteSpace: 'nowrap',
                }}
                selectedItemContainerStyle={{
                  flexDirection: 'row',
                }}
                showArrowIcon={true}
                ArrowDownIconComponent={({ style }) => (
                  <Ionicon
                    size={25}
                    color={Colors.fieldtTxtColor}
                    style={{ paddingHorizontal: 5, marginTop: 5 }}
                    name="chevron-down-outline"
                  />
                )}
                ArrowUpIconComponent={({ style }) => (
                  <Ionicon
                    size={25}
                    color={Colors.fieldtTxtColor}
                    style={{ paddingHorizontal: 5, marginTop: 5 }}
                    name="chevron-up-outline"
                  />
                )}
                showTickIcon={true}
                TickIconComponent={({ press }) => (
                  <Ionicon
                    style={{ paddingHorizontal: 5, marginTop: 5 }}
                    color={press ? Colors.fieldtBgColor : Colors.primaryColor}
                    name={'md-checkbox'}
                    size={25}
                  />
                )}
                dropDownDirection="BOTTOM"
                disabled={
                  selectedRackStockTransferEdit &&
                  (selectedRackStockTransferEdit.status ===
                    STOCK_TRANSFER_STATUS.COMPLETED ||
                    selectedRackStockTransferEdit.status ===
                    STOCK_TRANSFER_STATUS.CREATED)
                }
                value={poItems[index].outletSupplyItemId}
                onSelectItem={value => {
                  if (value) {
                    // const outletSupplyItemSku = allOutletsSupplyItemsDict[value].sku;

                    // const supplyItem = supplyItemsSkuDict[outletSupplyItemSku] ? supplyItemsSkuDict[outletSupplyItemSku] : null;

                    const supplyItem = allOutletsItems.find(
                      findItem => findItem.uniqueId === value.value,
                    );

                    setPoItems(
                      poItems.map((poItem, i) =>
                        i === index
                          ? {
                            ...poItem,
                            outletSupplyItemId: value.value,
                            name: supplyItem.name,
                            sku: supplyItem.sku,
                            unit: '',
                            quantity: supplyItem.stockCount || 0,
                            transferQuantity: 0,
                            price: supplyItem.price,
                            totalPrice: 0,

                            supplyItem: supplyItem,
                          }
                          : poItem,
                      ),
                    );
                  }
                }}
                listMode="SCROLLVIEW"
                scrollViewProps={{
                  nestedScrollEnabled: true,
                }}
                items={filteredDropdownList}
                open={openList[index]}
                setOpen={value => {
                  setOpenList(prevOpenList => {
                    const newOpenList = [...prevOpenList];
                    newOpenList[index] = value;
                    return newOpenList;
                  });
                }}
              />
            )}
          </View>
        </View>

        <Text
          style={{
            flex: 0.8,
            color: '#949494',

            fontFamily: 'NunitoSans-Regular',
            fontSize: switchMerchant ? 10 : 14,
          }}>
          {poItems[index].skuMerchant || '-'}
          {/* {poItems[index].skuMerchant || poItems[index].sku ? poItems[index].sku : poItems[index].supplyItem.skuMerchant || poItems[index].supplyItem.skuMerchant ? poItems[index].supplyItem.skuMerchant : poItems[index].sku } */}
        </Text>

        {/* <Text
                    style={{
                        flex: 0.8,
                        color: "#949494",
                        fontFamily: "NunitoSans-Regular",
                        fontSize: switchMerchant ? 10 : 14,
                    }}
                >
                    {poItems[index].unit}
                </Text> */}

        <Text
          style={{
            flex: 0.6,
            color: '#949494',
            fontFamily: 'NunitoSans-Regular',
            fontSize: switchMerchant ? 10 : 14,
          }}>
          {poItems[index].quantity}
        </Text>

        {console.log('transferQuantity')}
        {console.log(poItems)}

        <View style={{ flex: 1 }}>
          {!selectedRackStockTransferEdit ? (
            // || (selectedRackStockTransferEdit && selectedRackStockTransferEdit.status !== STOCK_TRANSFER_STATUS.COMPLETED)
            <TextInput
              editable={true}
              underlineColorAndroid={Colors.fieldtBgColor}
              style={{
                backgroundColor: Colors.fieldtBgColor,
                width: windowWidth * 0.11,
                borderRadius: 5,
                padding: 5,
                borderWidth: 1,
                borderColor: '#E5E5E5',
                paddingLeft: 7,
                fontFamily: 'NunitoSans-Regular',
                fontSize: switchMerchant ? 10 : 14,
              }}
              placeholder={'0'}
              placeholderStyle={{
                fontFamily: 'NunitoSans-Regular',
                fontSize: switchMerchant ? 10 : 14,
              }}
              keyboardType={'decimal-pad'}
              onChangeText={text => {
                // setState({ itemName: text });
                setPoItems(
                  poItems.map((poItem, i) =>
                    i === index
                      ? {
                        ...poItem,
                        transferQuantity:
                          text.length > 0
                            ? parseInt(text) >= poItem.quantity
                              ? Alert.prompt('Quantity is more than Stock')
                              : parseInt(text)
                            : 0, // will cause bugs, some outlet item usage consume 0.01, 0.05, etc
                        //balance: poItem.quantity - parseInt(text),
                        balance:
                          parseInt(text) < poItem.quantity
                            ? poItem.quantity - parseInt(text)
                            : 0,
                        totalPrice: parseInt(text) * poItem.price,

                        updated: true,
                      }
                      : poItem,
                  ),
                );
              }}
              value={
                poItems[index].transferQuantity
                  ? poItems[index].transferQuantity
                  : '0'
              }
            />
          ) : (
            <TextInput
              editable={false}
              underlineColorAndroid={Colors.fieldtBgColor}
              style={{
                backgroundColor: Colors.fieldtBgColor,
                width: switchMerchant ? 55 : 80,
                height: 35,
                borderRadius: 5,
                padding: 5,
                borderWidth: 1,
                borderColor: '#E5E5E5',
                paddingLeft: 10,
                fontFamily: 'NunitoSans-Regular',
                fontSize: switchMerchant ? 10 : 14,
              }}
              placeholder={'0'}
              placeholderStyle={{
                fontFamily: 'NunitoSans-Regular',
                fontSize: switchMerchant ? 10 : 14,
              }}
              keyboardType={'decimal-pad'}
              onChangeText={text => {
                // setState({ itemName: text });
                setPoItems(
                  poItems.map((poItem, i) =>
                    i === index
                      ? {
                        ...poItem,
                        transferQuantity:
                          text.length > 0
                            ? parseInt(text) >= poItem.quantity
                              ? poItem.quantity
                              : parseInt(text)
                            : 0, // will cause bugs, some outlet item usage consume 0.01, 0.05, etc
                        balance: poItem.quantity - parseInt(text),
                        totalPrice: parseInt(text) * poItem.price,

                        updated: true,
                      }
                      : poItem,
                  ),
                );
              }}
              value={poItems[index].transferQuantity.toFixed(0)}
            />
          )}
        </View>

        <Text
          style={{
            flex: 1,
            color: '#949494',
            fontFamily: 'NunitoSans-Regular',
            fontSize: switchMerchant ? 10 : 14,
          }}>
          {allOutletsItems.find(
            item => item.uniqueId === poItems[index].outletSupplyItemId,
          )
            ? allOutletsItems.find(
              item => item.uniqueId === poItems[index].outletSupplyItemId,
            ).stockCount || 0
            : 0}
        </Text>
        {/*  <Text
                    style={{
                        flex: 0.9,
                        color: "#949494",
                        fontFamily: "NunitoSans-Regular",
                        fontSize: switchMerchant ? 10 : 14,
                    }}
                >
                    {poItems[index].price}
                </Text>
                <Text
                    style={{
                        flex: 0.9,
                        color: "#949494",
                        fontFamily: "NunitoSans-Regular",
                        fontSize: switchMerchant ? 10 : 14,
                    }}
                >
                    {poItems[index].totalPrice}
                </Text>
                */}
        <TouchableOpacity
          style={{ flex: 0.3 }}
          onPress={() => {
            setPoItems([
              ...poItems.slice(0, index),
              ...poItems.slice(index + 1),
            ]);
          }}>
          <Icon
            name="trash-2"
            size={switchMerchant ? 15 : 20}
            color="#eb3446"
          />
        </TouchableOpacity>
      </View>
    );
  };

  const createStockTransfer = (
    stockTransferStatus = STOCK_TRANSFER_STATUS.CREATED,
  ) => {
    if (selectedRackStockTransferEdit === null) {
      var body = {
        stId: poId,
        stItems: poItems,
        tax: +parseFloat(taxTotal).toFixed(2),
        discount: +parseFloat(discountTotal).toFixed(2),
        totalPrice: +parseFloat(subtotal).toFixed(2),
        finalTotal: +parseFloat(finalTotal).toFixed(2),
        estimatedArrivalDate: moment(date).valueOf(),

        // status: STOCK_TRANSFER_STATUS.CREATED,
        status: stockTransferStatus,

        sourceOutletId: selectedSourceOutletId,
        targetOutletId: selectedTargetOutletId,
        sourceOutletName: allOutlets.find(
          outlet => outlet.uniqueId === selectedSourceOutletId,
        ).name,
        targetOutletName: allOutlets.find(
          outlet => outlet.uniqueId === selectedTargetOutletId,
        ).name,

        merchantId: merchantId,
        outletId: currOutletId,
        remarks: '',

        staffName: userName,
        staffId: userId,

        sourceRackId: selectedSourceRackId,
        sourceRackName: selectedSourceRack ? selectedSourceRack.rackName : '',
        sourceRackItems: selectedSourceRack
          ? Object.entries(selectedSourceRack.items).map(([key, value]) => {
            return {
              ...value,
            };
          })
          : [],
        targetRackId: selectedTargetRackId,
        targetRackName: selectedTargetRack ? selectedTargetRack.rackName : '',
        targetRackItems: selectedTargetRack
          ? Object.entries(selectedTargetRack.items).map(([key, value]) => {
            return {
              ...value,
            };
          })
          : [],
      };

      console.log(body);

      APILocal.createStockTransferRack({ body: body }).then(result => {
        // ApiClient.POST(API.createStockTransferProduct, body).then((result) => {
        console.log('Result', result);
        if (result && result.status === 'success') {
          if (
            Alert.prompt('Success\n\nStock transfer has been saved') == true
          ) {
            setAddStockTransfer(false);
            setStockTransfer(true);
          } else {
            console.log('You canceled!');
          }
        } else {
          if (
            Alert.prompt('Error\n\nFailed to create stock transfer') == true
          ) {
          } else {
            console.log('You canceled!');
          }
        }
      });
    } else {
      var body = {
        stId: poId,
        stItems: poItems,
        tax: +parseFloat(taxTotal).toFixed(2),
        discount: +parseFloat(discountTotal).toFixed(2),
        totalPrice: +parseFloat(subtotal).toFixed(2),
        finalTotal: +parseFloat(finalTotal).toFixed(2),
        estimatedArrivalDate: moment(date).valueOf(),

        // status: STOCK_TRANSFER_STATUS.CREATED,
        status: stockTransferStatus,

        sourceOutletId: selectedSourceOutletId,
        targetOutletId: selectedTargetOutletId,
        sourceOutletName: allOutlets.find(
          outlet => outlet.uniqueId === selectedSourceOutletId,
        ).name,
        targetOutletName: allOutlets.find(
          outlet => outlet.uniqueId === selectedTargetOutletId,
        ).name,

        merchantId: merchantId,
        outletId: currOutletId,
        remarks: '',

        staffName: userName,
        staffId: userId,

        sourceRackId: selectedSourceRackId,
        sourceRackName: selectedSourceRack ? selectedSourceRack.rackName : '',
        sourceRackItems: selectedSourceRack
          ? Object.entries(selectedSourceRack.items).map(([key, value]) => {
            return {
              ...value,
            };
          })
          : [],
        targetRackId: selectedTargetRackId,
        targetRackName: selectedTargetRack ? selectedTargetRack.rackName : '',
        targetRackItems: selectedTargetRack
          ? Object.entries(selectedTargetRack.items).map(([key, value]) => {
            return {
              ...value,
            };
          })
          : [],

        uniqueId: selectedRackStockTransferEdit.uniqueId,
      };

      console.log(body);

      APILocal.updateStockTransferRack({ body: body }).then(result => {
        // ApiClient.POST(API.updateStockTransferProduct, body).then((result) => {
        console.log('Result', result);
        if (result && result.status === 'success') {
          Alert.prompt(
            'Success\n\nStock transfer has been updated',
            [
              {
                text: 'OK',
                onPress: () => {
                  // setState({ addStockTransfer: false, stockTransfer: true })
                  setAddStockTransfer(false);
                  setStockTransfer(true);
                },
              },
            ],
            { cancelable: false },
          );
        } else {
          Alert.prompt(
            'Error\n\nFailed to update stock transfer',
            [{ text: 'OK', onPress: () => { } }],
            { cancelable: false },
          );
        }
      });
    }
  };

  ////////////////////////////////////////////////////////////////

  // 2024-05-24 - stock take helper changes

  // get Rack by qr code, if not found will return null
  const getRackByQrCode = qrCode => {
    const foundRack = racks.find(rack => rack.qr === qrCode);

    return foundRack;
  };

  // set the source rack (and the items that inside it, to display on ui)
  // can pass null, if needed to reset to no-rack-chosen state
  const setActiveSourceRack = (rack, overrideList = false) => {
    if (rack) {
      setSelectedSourceRack(rack);
      setSelectedSourceRackId(rack.uniqueId);
      setSelectedSourceRackName(rack.rackName);
      console.log('Rack set in setCurrentActiveRack:', rack);
      console.log('Selected Rack ID in setCurrentActiveRack:', rack.uniqueId);

      if (overrideList) {
        const newPoItems = Object.entries(rack.items).map(([key, value]) => {
          console.log('Processing rack item:', value);
          let foundOutletItem = outletItems.find(
            findItem => findItem.uniqueId === value.id,
          );
          console.log('Found Outlet Item:', foundOutletItem);
          if (foundOutletItem) {
            const foundRackItem = rack.items[foundOutletItem.sku];
            console.log('Found Rack Item:', foundRackItem);
            if (foundRackItem) {
              return {
                outletSupplyItemId: value.id,
                name: value.n,
                sku: value.sku,
                skuMerchant: value.skuM,
                unit: '',
                quantity: value.sc,
                countedQuantity: value.sc,
                diffQuantity: 0,
                diffCost: 0,
                price: foundOutletItem.price,
                totalPrice: foundOutletItem.price * value.sc,
                remarks: '',
              };
            }
          }
          // If no foundOutletItem or foundRackItem, return a default structure or handle appropriately
          return {
            outletSupplyItemId: value.id,
            name: value.n,
            sku: value.sku,
            skuMerchant: value.skuM,
            unit: '',
            quantity: value.sc,
            countedQuantity: value.sc,
            diffQuantity: 0,
            diffCost: 0,
            price: 0, // Default price if not found
            totalPrice: 0,
            remarks: '',
          };
        });
        setPoItems(newPoItems);

        // Create a filtered dropdown list
        const filteredDropdownList = outletSupplyItemDropdownList.filter(item =>
          newPoItems.some(poItem => poItem.outletSupplyItemId === item.value),
        );
        console.log('Filtered Dropdown List:', filteredDropdownList); // Add this line
        setFilteredDropdownList(filteredDropdownList);
      }
    } else {
      setSelectedSourceRack(null);
      setSelectedSourceRackId('NONE');
      console.log('No rack set');
      if (overrideList) {
        setPoItems([]);
        setFilteredDropdownList([]);
      }
    }
  };
  // set the target rack
  // can pass null, if needed to reset to no-rack-chosen state
  const setActiveTargetRack = rack => {
    if (rack) {
      setSelectedTargetRack(rack);
      setSelectedTargetRackId(rack.uniqueId);
      setSelectedTargetRackName(rack.rackName);
    } else {
      setSelectedTargetRack(null);
      setSelectedTargetRackId('NONE');
    }
  };

  // get rack item (Rack.items[i]), by barcode (will return null if no rack item's barcode matched)
  const getRackItemByBarcode = (barcode, rack) => {
    // const foundRackItem = rack.items.find(rackItem => rackItem.bc === barcode);

    const foundOutletItem = outletItems.find(item => item.bc === barcode);

    if (foundOutletItem) {
      const foundRackItem = rack.items[foundOutletItem.sku];

      return foundRackItem;
    } else {
      return null;
    }
  };

  // insert stock count into the current stock item list (for matched item)
  // will return true if operation done & success
  // will return false if operation failed (ex: stock count not enough)
  const insertStockCountIntoItemList = (rackItem, stockCount) => {
    // note: poItems[i].outletSupplyItemId = OutletItem.uniqueId

    const matchedPoItemIndex = poItems.findIndex(
      item => item.sku === rackItem.sku,
    );

    const foundOutletItem = outletItems.find(item => item.sku === rackItem.sku);

    if (foundOutletItem) {
      if (matchedPoItemIndex >= 0) {
        // means matched and existed

        const value = stockCount.length > 0 ? parseFloat(stockCount) : 0;

        const stockCountCurrRack = [selectedSourceRack].reduce(
          (accum, rack) => {
            const matchedCount = rack.items.reduce(
              (matchedCount, findRackItem) => {
                if (findRackItem.sku === rackItem.sku) {
                  return matchedCount + findRackItem.sc;
                } else {
                  return matchedCount;
                }
              },
              0,
            );

            return accum + matchedCount;
          },
          0,
        );

        if (value <= stockCountCurrRack) {
          // means inputed amount + current rack's owned amount

          setPoItems(
            poItems.map((poItem, i) =>
              i === matchedPoItemIndex
                ? {
                  ...poItem,
                  countedQuantity: value,
                  diffQuantity: value - poItem.quantity,
                  diffCost: (value - poItem.quantity) * poItem.price,
                  totalPrice:
                    value * poItem.price < 0 ? 0 : value * poItem.price, // total price should be positive
                  updated: true,
                }
                : poItem,
            ),
          );

          return true;
        } else {
          return false;
        }
      } else {
        return false;
      }
    } else {
      return false;
    }
  };

  ////////////////////////////////////////////////////////////////

  return (
    <View>
      {/*isMobile() && <TopBar navigation={navigation} />*/}

      <View
        style={[styles.container, { height: windowHeight, width: windowWidth }]}>
        {/* <View style={[styles.sidebar]}>
                <SideBar
                    navigation={props.navigation}
                    selectedTab={3}
                    expandInventory={true}
                />
            </View> */}
        <View style={{ height: windowHeight, width: windowWidth }}>
          <ScrollView
            showsVerticalScrollIndicator={false}
            style={{}}
            contentContainerStyle={{
              backgroundColor: Colors.highlightColor,
            }}>
            {/*
            <TouchableOpacity
              style={{
                // height: windowHeight * 0.05,
                flexDirection: 'row',
                alignContent: 'center',
                alignItems: 'center',
                marginLeft: 11,
                marginTop: 13,
                marginBottom: 5,
              }}
              onPress={() => {
                if (addStockTransfer) {
                  CommonStore.update(s => {
                    s.selectedStockTransferEdit = null;
                  });
                  setStockTransfer(true);
                  setAddStockTransfer(false);
                  setVisibleItems(1);
                } else {
                  props.navigation.navigate('RA Dashboard - KooDoo Assistant');
                }
              }}>
              <Icon
                name="chevron-left"
                size={switchMerchant ? 20 : 20}
                color={Colors.primaryColor}
              />
              <Text
                style={{
                  fontFamily: 'NunitoSans-Bold',
                  fontSize: switchMerchant ? 14 : 16,
                  color: Colors.primaryColor,
                }}>
                Back
              </Text>
            </TouchableOpacity>*/}

            {/* <ScrollView horizontal={true} showsHorizontalScrollIndicator={true}> */}
            <View style={styles.content}>
              <Modal
                supportedOrientations={['portrait', 'landscape']}
                style={
                  {
                    // flex: 1
                  }
                }
                visible={exportModal}
                transparent={true}
                animationType={'fade'}>
                <View
                  style={{
                    flex: 1,
                    backgroundColor: Colors.modalBgColor,
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}>
                  <View
                    style={{
                      width: Dimensions.get('screen').width * 0.75,
                      backgroundColor: Colors.whiteColor,
                      borderRadius: 12,
                      padding: Dimensions.get('screen').width * 0.05,
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}>
                    <TouchableOpacity
                      disabled={isLoading}
                      style={{
                        position: 'absolute',
                        right: Dimensions.get('screen').width * 0.015,
                        top: Dimensions.get('screen').width * 0.01,

                        elevation: 1000,
                        zIndex: 1000,
                      }}
                      onPress={() => {
                        setExportModal(false);
                      }}>
                      <AntDesign
                        name="closecircle"
                        size={switchMerchant ? 15 : 25}
                        color={Colors.fieldtTxtColor}
                      />
                    </TouchableOpacity>
                    <View
                      style={{
                        alignItems: 'center',
                        top: '20%',
                        position: 'absolute',
                      }}>
                      <Text
                        style={{
                          fontFamily: 'NunitoSans-Bold',
                          textAlign: 'center',
                          fontSize: switchMerchant ? 18 : 24,
                        }}>
                        Download Report
                      </Text>
                    </View>
                    <View style={{ top: switchMerchant ? '14%' : '10%' }}>
                      <View
                        style={{
                          alignItems: 'center',
                          justifyContent: 'center',
                          //top: '10%',
                          flexDirection: 'row',
                          marginTop: 30,
                        }}>
                        <TouchableOpacity
                          disabled={isLoading}
                          style={{
                            justifyContent: 'center',
                            flexDirection: 'row',
                            borderWidth: 1,
                            borderColor: Colors.primaryColor,
                            backgroundColor: '#0A1F44',
                            borderRadius: 5,
                            width: switchMerchant ? 100 : 100,
                            paddingHorizontal: 10,
                            height: switchMerchant ? 35 : 40,
                            alignItems: 'center',
                            shadowOffset: {
                              width: 0,
                              height: 2,
                            },
                            shadowOpacity: 0.22,
                            shadowRadius: 3.22,
                            elevation: 1,
                            zIndex: -1,
                            marginRight: 15,
                          }}
                          onPress={() => {
                            if (
                              convertDataToExcelFormat() &&
                              convertDataToExcelFormat().length > 0
                            ) {
                              handleExportExcel();
                            } else {
                              Alert.alert('Info, Empty data to export.');

                              CommonStore.update(s => {
                                s.isLoading = false;
                              });
                              setIsLoadingExcel(false);
                            }
                          }}>
                          {isLoadingExcel ? (
                            <ActivityIndicator
                              size={'small'}
                              color={Colors.whiteColor}
                            />
                          ) : (
                            <Text
                              style={{
                                color: Colors.whiteColor,
                                //marginLeft: 5,
                                fontSize: switchMerchant ? 10 : 16,
                                fontFamily: 'NunitoSans-Bold',
                              }}>
                              EXCEL
                            </Text>
                          )}
                        </TouchableOpacity>
                        {/* <CSVLink
                          style={{
                            justifyContent: "center",
                            alignContent: "center",
                            alignItems: "center",
                            display: "inline-block",
                            flexDirection: "row",
                            textDecoration: "none",
                            borderWidth: 1,
                            borderColor: Colors.primaryColor,
                            backgroundColor: "#0A1F44",
                            borderRadius: 5,
                            width: switchMerchant ? 100 : 100,
                            paddingHorizontal: 10,
                            height: switchMerchant ? 35 : 40,
                            alignItems: "center",
                            shadowOffset: {
                              width: 0,
                              height: 2,
                            },
                            shadowOpacity: 0.22,
                            shadowRadius: 3.22,
                            elevation: 1,
                            zIndex: -1,
                          }}
                          data={convertDataToExcelFormat()}
                          filename="KooDoo_Stock_Transfer_Product_Report.csv"
                        >
                          <View
                            style={{
                              width: "100%",
                              height: "100%",
                              alignContent: "center",
                              alignItems: "center",
                              alignSelf: "center",
                              justifyContent: "center",
                            }}
                          >
                            <Text
                              style={{
                                color: Colors.whiteColor,
                                //marginLeft: 5,
                                fontSize: switchMerchant ? 10 : 16,
                                fontFamily: "NunitoSans-Bold",
                              }}
                            >
                              CSV
                            </Text>
                          </View>
                        </CSVLink> */}
                      </View>
                    </View>
                  </View>
                </View>
              </Modal>

              <Modal
                style={
                  {
                    // flex: 1
                  }
                }
                supportedOrientations={['portrait', 'landscape']}
                visible={importModal}
                transparent={true}
                animationType={'fade'}>
                <View style={styles.modalContainer}>
                  <View style={[styles.modalViewImport, {}]}>
                    <TouchableOpacity
                      style={styles.closeButton}
                      onPress={() => {
                        // setState({ changeTable: false });
                        setImportModal(false);
                      }}>
                      <AntDesign
                        name="closecircle"
                        size={25}
                        color={Colors.fieldtTxtColor}
                      />
                    </TouchableOpacity>
                    <View style={{ padding: 10, margin: 30 }}>
                      <View
                        style={[
                          styles.modalTitle1,
                          { justifyContent: 'center', alignItems: 'center' },
                        ]}>
                        <Text
                          style={[
                            styles.modalTitleText1,
                            { fontSize: 16, fontWeight: '500' },
                          ]}>
                          Imported List
                        </Text>
                      </View>

                      <View
                        style={{
                          flexDirection: 'row',
                          justifyContent: 'space-between',
                          alignItems: 'center',
                        }}>
                        <View
                          style={{
                            backgroundColor: Colors.primaryColor,
                            width: 150,
                            height: 40,
                            marginVertical: 15,
                            borderRadius: 5,
                            alignSelf: 'center',
                          }}>
                          <TouchableOpacity
                            onPress={() => {
                              importSelectFile();
                            }}>
                            <Text
                              style={{
                                color: Colors.whiteColor,
                                alignSelf: 'center',
                                marginVertical: 10,
                              }}>
                              IMPORT
                            </Text>
                          </TouchableOpacity>
                        </View>
                        <View style={{ flexDirection: 'row' }}>
                          <View
                            style={{
                              backgroundColor: Colors.whiteColor,
                              width: 150,
                              height: 40,
                              marginVertical: 15,
                              borderRadius: 5,
                              alignSelf: 'center',
                            }}>
                            <TouchableOpacity
                              onPress={() => {
                                // setImportTemplate(false);
                              }}>
                              <Text
                                style={{
                                  color: Colors.primaryColor,
                                  alignSelf: 'center',
                                  marginVertical: 10,
                                }}>
                                CANCEL
                              </Text>
                            </TouchableOpacity>
                          </View>
                          <View
                            style={{
                              backgroundColor: Colors.primaryColor,
                              width: 150,
                              height: 40,
                              marginVertical: 15,
                              borderRadius: 5,
                              alignSelf: 'center',
                            }}>
                            <TouchableOpacity onPress={() => { }}>
                              <Text
                                style={{
                                  color: Colors.whiteColor,
                                  alignSelf: 'center',
                                  marginVertical: 10,
                                }}>
                                SAVE
                              </Text>
                            </TouchableOpacity>
                          </View>
                        </View>
                      </View>
                    </View>
                  </View>
                </View>
              </Modal>

              {stockTransfer ? (
                <View
                  style={{
                    alignItems: 'center',
                    paddingBottom: 15,
                  }}>
                  <View
                    style={{
                      width: windowWidth * 0.95,
                      paddingHorizontal: 15,
                      alignSelf: 'center',
                    }}>
                    <View
                      style={{
                        width: windowWidth * 0.9,
                        alignSelf: 'center',
                        marginLeft: 15,
                      }}>
                      <View
                        style={{ alignItems: 'center', flexDirection: 'row' }}>
                        <Text
                          style={{
                            fontSize: switchMerchant ? 20 : 26,
                            fontFamily: 'NunitoSans-Bold',
                          }}>
                          {stockTransferList.length} Stock Transfer
                        </Text>
                      </View>
                      {/*
                      <View
                        style={{
                          flexDirection: 'row',
                          justifyContent: 'space-between',
                          marginVertical: 10,
                        }}>
                        <View style={{alignItem: 'center'}}>
                          <TouchableOpacity
                            style={{
                              justifyContent: 'center',
                              flexDirection: 'row',
                              borderWidth: 1,
                              borderColor: Colors.primaryColor,
                              backgroundColor: '#0A1F44',
                              borderRadius: 5,
                              width: switchMerchant ? 200 : 250,
                              paddingHorizontal: 10,
                              height: switchMerchant ? 35 : 40,
                              alignItems: 'center',
                              shadowOffset: {
                                width: 0,
                                height: 2,
                              },
                              shadowOpacity: 0.22,
                              shadowRadius: 3.22,
                              elevation: 1,
                              zIndex: -1,
                            }}
                            onPress={() => {
                              setExportModal(true);
                            }}>
                            <View
                              style={{
                                flexDirection: 'row',
                                alignItems: 'center',
                              }}>
                              <Icon
                                name="download"
                                size={switchMerchant ? 10 : 20}
                                color={Colors.whiteColor}
                              />
                              <Text
                                style={{
                                  color: Colors.whiteColor,
                                  marginLeft: 5,
                                  fontSize: switchMerchant ? 10 : 16,
                                  fontFamily: 'NunitoSans-Bold',
                                }}>
                                DOWNLOAD
                              </Text>
                            </View>
                          </TouchableOpacity>
                        </View>
                      </View>*/}

                      <View style={{ flexDirection: 'row' }}>
                        <TouchableOpacity
                          style={{
                            justifyContent: 'center',
                            flexDirection: 'row',
                            borderWidth: 1,
                            borderColor: Colors.primaryColor,
                            backgroundColor: '#0A1F44',
                            borderRadius: 5,
                            width: switchMerchant ? 200 : 250,
                            paddingHorizontal: switchMerchant ? 5 : 10,
                            height: switchMerchant ? 35 : 40,
                            alignItems: 'center',
                            shadowOffset: {
                              width: 0,
                              height: 2,
                            },
                            shadowOpacity: 0.22,
                            shadowRadius: 3.22,
                            elevation: 1,
                            zIndex: -1,
                            marginBottom: 15,
                          }}
                          onPress={() => {
                            CommonStore.update(s => {
                              s.selectedRackStockTransferEdit = null;
                            });

                            setStockTransfer(false);
                            setAddStockTransfer(true);
                          }}>
                          <View
                            style={{
                              flexDirection: 'row',
                              alignItems: 'center',
                            }}>
                            <AntDesign
                              name="pluscircle"
                              size={switchMerchant ? 10 : 20}
                              color={Colors.whiteColor}
                            />
                            <Text
                              style={{
                                color: Colors.whiteColor,
                                marginLeft: 5,
                                fontSize: switchMerchant ? 10 : 16,
                                fontFamily: 'NunitoSans-Bold',
                              }}>
                              STOCK TRANSFER
                            </Text>
                          </View>
                        </TouchableOpacity>
                      </View>

                      <View style={{ flexDirection: 'row' }}>
                        <View style={[{ height: switchMerchant ? 35 : 40 }]}>
                          <View
                            style={{
                              width: switchMerchant ? 200 : 250,
                              height: switchMerchant ? 35 : 40,
                              backgroundColor: 'white',
                              borderRadius: 5,
                              // marginLeft: '53%',
                              flexDirection: 'row',
                              alignContent: 'center',
                              alignItems: 'center',
                              alignSelf: 'flex-end',
                              shadowColor: '#000',
                              shadowOffset: {
                                width: 0,
                                height: 2,
                              },
                              shadowOpacity: 0.22,
                              shadowRadius: 3.22,
                              elevation: 3,
                              borderWidth: 1,
                              borderColor: '#E5E5E5',
                            }}>
                            <Icon
                              name="search"
                              size={switchMerchant ? 13 : 18}
                              color={Colors.primaryColor}
                              style={{ marginLeft: 15 }}
                            />
                            <TextInput
                              editable={!loading}
                              // underlineColorAndroid={Colors.primaryColor}
                              style={{
                                width: switchMerchant ? 180 : 220,
                                fontSize: 15,
                                fontFamily: 'NunitoSans-Regular',
                                padding: 0,
                                paddingLeft: 5,
                                fontSize: switchMerchant ? 10 : 16,
                                height: 35,
                                display: 'flex',
                                alignItems: 'center',
                                textAlignVertical: 'center',
                              }}
                              clearButtonMode="while-editing"
                              placeholder=" Search"
                              placeholderTextColor={Platform.select({
                                ios: '#a9a9a9',
                              })}
                              onChangeText={text => {
                                setSearch(text);
                              }}
                              value={search}
                            />
                          </View>
                        </View>
                      </View>
                    </View>

                    <View
                      style={{
                        flexDirection: 'row',
                        width: windowWidth * 0.9,
                        alignSelf: 'center',
                        marginLeft: 15,
                      }}>
                      <View
                        style={[
                          {
                            //marginRight: Platform.OS === 'ios' ? 0 : 10,
                            // paddingLeft: 15,
                            paddingHorizontal: 15,
                            flexDirection: 'row',
                            alignItems: 'center',
                            borderRadius: 5,
                            paddingVertical: 10,
                            justifyContent: 'center',
                            backgroundColor: Colors.whiteColor,
                            shadowOpacity: 0,
                            shadowColor: '#000',
                            shadowOffset: {
                              width: 0,
                              height: 2,
                            },
                            shadowOpacity: 0.22,
                            shadowRadius: 3.22,
                            elevation: 1,
                            marginTop: 10,
                          },
                        ]}>
                        <View
                          style={{ alignSelf: 'center', marginRight: 5 }}
                          onPress={() => {
                            setState({
                              pickerMode: 'date',
                              showDateTimePicker: true,
                            });
                          }}>
                          <GCalendar
                            width={switchMerchant ? 15 : 20}
                            height={switchMerchant ? 15 : 20}
                          />
                        </View>

                        <TouchableOpacity
                          onPress={() => setDatePickerVisibility(true)}>
                          <DatePicker
                            isVisible={isDatePickerVisible}
                            mode="date"
                            onConfirm={data => {
                              setRev_date(moment(data));
                              setDatePickerVisibility(false);
                            }}
                            onCancel={() => setDatePickerVisibility(false)}
                            date={rev_date.toDate()}
                            maximumDate={new Date()} // This is an example, you can replace with your rev_date1
                          />
                          <Text
                            style={
                              switchMerchant
                                ? {
                                  fontSize: 10,
                                  fontFamily: 'NunitoSans-Regular',
                                  marginHorizontal: 8,
                                }
                                : {
                                  fontFamily: 'NunitoSans-Regular',
                                  marginHorizontal: 8,
                                }
                            }>
                            {moment(rev_date).format('DD/MM/YYYY')?.toString()}
                          </Text>
                        </TouchableOpacity>
                        <Text
                          style={
                            switchMerchant
                              ? {
                                fontSize: 10,
                                fontFamily: 'NunitoSans-Regular',
                                marginHorizontal: 4,
                              }
                              : {
                                fontFamily: 'NunitoSans-Regular',
                                marginHorizontal: 4,
                              }
                          }>
                          -
                        </Text>
                        <TouchableOpacity
                          onPress={() => setDatePickerVisibility1(true)}>
                          <DatePicker
                            isVisible={isDatePickerVisible1}
                            mode="date"
                            onConfirm={data => {
                              setRev_date1(moment(data));
                              setDatePickerVisibility1(false);
                            }}
                            onCancel={() => setDatePickerVisibility1(false)}
                            date={rev_date1.toDate()}
                            maximumDate={new Date()} // This is an example, you can replace with your rev_date1
                          />
                          <Text
                            style={
                              switchMerchant
                                ? {
                                  fontSize: 10,
                                  fontFamily: 'NunitoSans-Regular',
                                  marginHorizontal: 8,
                                }
                                : {
                                  fontFamily: 'NunitoSans-Regular',
                                  marginHorizontal: 8,
                                }
                            }>
                            {moment(rev_date1).format('DD/MM/YYYY')?.toString()}
                          </Text>
                        </TouchableOpacity>
                      </View>
                    </View>

                    <View
                      style={{
                        backgroundColor: Colors.whiteColor,
                        width: windowWidth * 0.9,
                        height: windowHeight * 0.72,
                        marginTop: 10,
                        marginLeft: 15,
                        marginBottom: 30,
                        alignSelf: 'center',
                        borderRadius: 5,
                        shadowOpacity: 0,
                        shadowColor: '#000',
                        shadowOffset: {
                          width: 0,
                          height: 2,
                        },
                        shadowOpacity: 0.22,
                        shadowRadius: 3.22,
                        elevation: 3,
                        zIndex: -1,
                      }}>
                      <View
                        style={{
                          borderTopLeftRadius: 10,
                          borderTopRightRadius: 10,
                          backgroundColor: '#ffffff',
                          flexDirection: 'row',
                          paddingVertical: 20,
                          paddingHorizontal: 15,
                          //marginTop: 10,
                          borderBottomWidth: StyleSheet.hairlineWidth,
                        }}>
                        <Text
                          style={{
                            fontSize: switchMerchant ? 10 : 14,
                            width: '20%',
                            alignSelf: 'center',
                            fontFamily: 'NunitoSans-Bold',
                            marginRight: 2,
                          }}>
                          ID
                        </Text>
                        <Text
                          style={{
                            fontSize: switchMerchant ? 10 : 14,
                            width: '20%',
                            alignSelf: 'center',
                            fontFamily: 'NunitoSans-Bold',
                            marginRight: 2,
                          }}>
                          {'Created\nDate'}
                        </Text>
                        <Text
                          style={{
                            fontSize: switchMerchant ? 10 : 14,
                            width: '30%',
                            alignSelf: 'center',
                            fontFamily: 'NunitoSans-Bold',
                            marginHorizontal: 2,
                          }}>
                          From/To
                        </Text>
                        {/* <Text
                                                    style={{
                                                        fontSize: switchMerchant ? 10 : 14,
                                                        width: "21%",
                                                        alignSelf: "center",
                                                        fontFamily: "NunitoSans-Bold",
                                                        marginHorizontal: 4,
                                                    }}
                                                >
                                                    To
                                                </Text> */}
                        <Text
                          style={{
                            fontSize: switchMerchant ? 10 : 14,
                            width: '25%',
                            alignSelf: 'center',
                            fontFamily: 'NunitoSans-Bold',
                            marginLeft: 2,
                          }}>
                          Status
                        </Text>
                      </View>

                      <FlatList
                        scrollEnabled={true}
                        nestedScrollEnabled={true}
                        showsVerticalScrollIndicator={false}
                        data={stockTransferList.filter(item => {
                          if (search !== '') {
                            const searchLowerCase = search.toLowerCase();

                            return (
                              item.sourceRackName
                                .toLowerCase()
                                .includes(searchLowerCase) ||
                              item.targetRackName
                                .toLowerCase()
                                .includes(searchLowerCase)
                            );
                          } else {
                            return true;
                          }
                        })}
                        extraData={stockTransferList.filter(item => {
                          if (search !== '') {
                            const searchLowerCase = search.toLowerCase();

                            return (
                              item.sourceRackName
                                .toLowerCase()
                                .includes(searchLowerCase) ||
                              item.targetRackName
                                .toLowerCase()
                                .includes(searchLowerCase)
                            );
                          } else {
                            return true;
                          }
                        })}
                        renderItem={renderStockTransferItem}
                        keyExtractor={(item, index) => String(index)}
                        contentContainerStyle={{
                          paddingRight: 5,
                          paddingBottom: switchMerchant ? 0 : 80,
                        }}
                        style={{
                          flex: 1,
                        }}
                      />
                    </View>
                  </View>
                </View>
              ) : null}

              {addStockTransfer ? (
                <View style={{ height: Dimensions.get('window').height }}>
                  <View style={{}}>
                    <View
                      style={{
                        backgroundColor: Colors.whiteColor,
                        width: windowWidth * 0.9,
                        height: windowHeight * 0.83,
                        marginTop: switchMerchant ? 5 : 5,
                        marginLeft: 15,
                        alignSelf: 'center',
                        shadowOpacity: 0,
                        shadowColor: '#000',
                        shadowOffset: {
                          width: 0,
                          height: 2,
                        },
                        shadowOpacity: 0.22,
                        shadowRadius: 3.22,
                        elevation: 3,
                        borderRadius: 5,
                      }}>
                      <ScrollView
                        showsVerticalScrollIndicator={false}
                        style={{ paddingTop: 20, padding: 10 }}
                        contentContainerStyle={{}}>
                        <View
                          style={{ borderBottomWidth: StyleSheet.hairlineWidth }}>
                          <View style={{ marginBottom: 10 }}>
                            <View
                              style={{
                                flexDirection: 'row',
                                justifyContent: 'center',
                                marginBottom: 20,
                              }}>
                              <View style={{ flexDirection: 'column' }}>
                                <Text
                                  style={{
                                    marginTop: 0,
                                    fontSize: switchMerchant ? 20 : 26,
                                    fontWeight: 'bold',
                                    textAlign: 'center',
                                  }}>
                                  Stock Transfer
                                </Text>

                                <Text
                                  style={{
                                    fontSize: switchMerchant ? 10 : 16,
                                    color: '#adadad',
                                    textAlign: 'center',
                                  }}>
                                  Fill In The Stock Transfer Information
                                </Text>
                              </View>
                            </View>

                            {selectedRackStockTransferEdit &&
                              selectedRackStockTransferEdit.status ===
                              STOCK_TRANSFER_STATUS.CREATED &&
                              currOutletId ===
                              selectedRackStockTransferEdit.targetOutletId ? (
                              <>
                                <View style={{}}>
                                  <TouchableOpacity
                                    style={{
                                      justifyContent: 'center',
                                      flexDirection: 'row',
                                      borderWidth: 1,
                                      borderColor: Colors.primaryColor,
                                      backgroundColor: '#0A1F44',
                                      borderRadius: 5,
                                      width: switchMerchant ? 120 : 170,
                                      paddingHorizontal: 10,
                                      height: switchMerchant ? 35 : 40,
                                      alignItems: 'center',
                                      shadowOffset: {
                                        width: 0,
                                        height: 2,
                                      },
                                      shadowOpacity: 0.22,
                                      shadowRadius: 3.22,
                                      elevation: 1,
                                      zIndex: -1,
                                      marginRight: 20,
                                      marginBottom: 10,
                                    }}
                                    onPress={() => {
                                      createStockTransfer(
                                        STOCK_TRANSFER_STATUS.COMPLETED,
                                      );
                                    }}
                                    disabled={
                                      selectedRackStockTransferEdit
                                        ? false
                                        : true
                                    }>
                                    <Text
                                      style={{
                                        color: Colors.whiteColor,
                                        //marginLeft: 5,
                                        fontSize: switchMerchant ? 10 : 16,
                                        fontFamily: 'NunitoSans-Bold',
                                      }}>
                                      {'MARK COMPLETED'}
                                    </Text>
                                  </TouchableOpacity>
                                </View>
                              </>
                            ) : (
                              <></>
                            )}

                            {selectedRackStockTransferEdit &&
                              selectedRackStockTransferEdit.status ===
                              STOCK_TRANSFER_STATUS.CREATED &&
                              currOutletId ===
                              selectedRackStockTransferEdit.targetOutletId ? (
                              <></>
                            ) : (
                              <View style={{}}>
                                <TouchableOpacity
                                  style={{
                                    justifyContent: 'center',
                                    flexDirection: 'row',
                                    borderWidth: 1,
                                    borderColor: Colors.primaryColor,
                                    backgroundColor: '#0A1F44',
                                    borderRadius: 5,

                                    width:
                                      selectedRackStockTransferEdit &&
                                        selectedRackStockTransferEdit.status ===
                                        STOCK_TRANSFER_STATUS.CREATED &&
                                        currOutletId ===
                                        selectedRackStockTransferEdit.targetOutletId &&
                                        !switchMerchant
                                        ? 170
                                        : selectedRackStockTransferEdit &&
                                          selectedRackStockTransferEdit.status ===
                                          STOCK_TRANSFER_STATUS.CREATED &&
                                          currOutletId ===
                                          selectedRackStockTransferEdit.targetOutletId &&
                                          switchMerchant
                                          ? 160
                                          : 170,
                                    paddingHorizontal: 10,

                                    height: switchMerchant ? 35 : 40,
                                    alignItems: 'center',
                                    shadowOffset: {
                                      width: 0,
                                      height: 2,
                                    },
                                    shadowOpacity: 0.22,
                                    shadowRadius: 3.22,
                                    elevation: 1,
                                    zIndex: -1,
                                    marginRight: 20,
                                    marginTop: 0,
                                    marginBottom: 10,
                                  }}
                                  onPress={() => {
                                    if (poItems.length > 0) {
                                      if (
                                        selectedSourceRackName !== '' &&
                                        selectedTargetRackName !== ''
                                      ) {
                                        createStockTransfer();
                                      } else {
                                        Alert.alert(
                                          'Info. Please fill in source rack and target rack',
                                        );
                                      }
                                    } else {
                                      Alert.alert(
                                        'Info. Please add at least 1 product',
                                      );
                                    }
                                  }}>
                                  <Text
                                    style={{
                                      color: Colors.whiteColor,
                                      //marginLeft: 5,
                                      fontSize: switchMerchant ? 10 : 16,
                                      fontFamily: 'NunitoSans-Bold',
                                    }}>
                                    SAVE
                                  </Text>
                                </TouchableOpacity>
                                <View style={{}}>
                                  <TouchableOpacity
                                    style={{
                                      justifyContent: 'center',
                                      flexDirection: 'row',
                                      borderWidth: 1,
                                      borderColor: Colors.primaryColor,
                                      backgroundColor: '#0A1F44',
                                      borderRadius: 5,
                                      width: switchMerchant ? 120 : 170,
                                      paddingHorizontal: 10,
                                      height: switchMerchant ? 35 : 40,
                                      alignItems: 'center',
                                      shadowOffset: {
                                        width: 0,
                                        height: 2,
                                      },
                                      shadowOpacity: 0.22,
                                      shadowRadius: 3.22,
                                      elevation: 1,
                                      zIndex: -1,
                                      marginRight: 10,
                                    }}
                                    onPress={() => {
                                      handleIconPressSource();
                                      setShowScannerTarget(false); // Ensure the other scanner is hidden
                                      global.cameraScanState = 'readerSource';

                                      ///////////////////////////////////

                                      //  const node = document.getElementById('readerSource');

                                      //   if (node && node !== null) {
                                      // do stuff

                                      //   console.log(node);

                                      //   console.log('readerSource');
                                      //   console.log(document.getElementById('readerSource'));
                                      //
                                      //   let html5QrcodeScannerRef = new Html5QrcodeScanner(
                                      //     node.id,
                                      //     { fps: 10, qrbox: { width: 250, height: 250 } },
                                      //</View></View>                                       /* verbose= */ false);
                                      //    html5QrcodeScannerRef.render(onScanSuccessSource, onScanFailureSource);
                                      //    }

                                      ///////////////////////////////////
                                    }}>
                                    <MaterialCommunityIcons
                                      name={
                                        showScannerSource
                                          ? 'close'
                                          : 'qrcode-scan'
                                      }
                                      size={18}
                                      style={{ color: Colors.whiteColor }}
                                    />

                                    <Text
                                      style={{
                                        color: Colors.whiteColor,
                                        marginLeft: 7,
                                        fontSize: switchMerchant ? 10 : 16,
                                        fontFamily: 'NunitoSans-Bold',
                                      }}>
                                      SOURCE
                                    </Text>
                                  </TouchableOpacity>
                                  {/*   <View
                                    style={{
                                      display: showScannerSource
                                        ? 'flex'
                                        : 'none',
                                    }}
                                    // ref={qrReaderCallback}
                                    id="readerSource"
                                    nativeID="readerSource"
                                    width="600px"></View>
                                */}
                                </View>
                                {showScannerSource ? (
                                  <Camera
                                    ref={qrReaderCallback}
                                    scanBarcode={true}
                                    onReadCode={() =>
                                      setShowScannerSource(false)
                                    }
                                    cameraType={CameraType.Back}
                                    flashMode="auto"
                                  />
                                ) : (
                                  <></>
                                )}
                                <View style={{}}>
                                  <TouchableOpacity
                                    style={{
                                      justifyContent: 'center',
                                      flexDirection: 'row',
                                      borderWidth: 1,
                                      borderColor: Colors.primaryColor,
                                      backgroundColor: '#0A1F44',
                                      borderRadius: 5,
                                      width: switchMerchant ? 120 : 170,
                                      paddingHorizontal: 10,
                                      height: switchMerchant ? 35 : 40,
                                      alignItems: 'center',
                                      shadowOffset: {
                                        width: 0,
                                        height: 2,
                                      },
                                      shadowOpacity: 0.22,
                                      shadowRadius: 3.22,
                                      elevation: 1,
                                      zIndex: -1,
                                      marginRight: 10,
                                      marginTop: 10,
                                    }}
                                    onPress={() => {
                                      handleIconPressTarget();
                                      setShowScannerSource(false); // Ensure the other scanner is hidden
                                      global.cameraScanState = 'readerTarget';

                                      ///////////////////////////////////

                                      //  const node =
                                      //  document.getElementById('readerTarget');

                                      // if (node && node !== null) {
                                      // do stuff

                                      //  console.log(node);

                                      console.log('readerTarget');
                                      console
                                        .log
                                        //   document.getElementById(
                                        //     'readerTarget',
                                        //   ),
                                        ();

                                      //   let html5QrcodeScannerRef =
                                      //    new Html5QrcodeScanner(
                                      //       node.id,
                                      //       {
                                      //        fps: 10,
                                      //        qrbox: {width: 250, height: 250},
                                      //</View>        },
                                      //</View>        /* verbose= */ false,
                                      //     );
                                      //    html5QrcodeScannerRef.render(
                                      //</View>      onScanSuccessTarget,
                                      //</View>     onScanFailureTarget,
                                      //    );
                                      //    }

                                      ///////////////////////////////////
                                    }}>
                                    <MaterialCommunityIcons
                                      name={
                                        showScannerTarget
                                          ? 'close'
                                          : 'qrcode-scan'
                                      }
                                      size={18}
                                      style={{ color: Colors.whiteColor }}
                                    />

                                    <Text
                                      style={{
                                        color: Colors.whiteColor,
                                        marginLeft: 7,
                                        fontSize: switchMerchant ? 10 : 16,
                                        fontFamily: 'NunitoSans-Bold',
                                      }}>
                                      DESTINATION
                                    </Text>
                                  </TouchableOpacity>
                                  {/*      <View
                                    style={{
                                      display: showScannerTarget
                                        ? 'flex'
                                        : 'none',
                                    }}
                                    // ref={qrReaderCallback}
                                    id="readerTarget"
                                    nativeID="readerTarget"
                                    width="600px"></View>
                                  {console.log('readerTarget open')}
                              */}
                                </View>
                                {showScannerTarget ? (
                                  <Camera
                                    ref={qrReaderCallback}
                                    scanBarcode={true}
                                    onReadCode={() =>
                                      setShowScannerTarget(false)
                                    }
                                    cameraType={CameraType.Back}
                                    flashMode="auto"
                                  />
                                ) : (
                                  <></>
                                )}
                              </View>
                            )}

                            <View
                              style={{
                                // flexDirection: "row",
                                marginTop: 20,
                                // justifyContent: "space-between",
                                width: '96%',
                                alignSelf: 'center',
                              }}>
                              <View
                                style={{
                                  flexDirection: 'row',
                                  // width: "45%",
                                  alignItems: 'center',
                                }}>
                                <Text
                                  style={{
                                    // width: "42%",
                                    fontFamily: 'NunitoSans-Bold',
                                    fontSize: switchMerchant ? 10 : 14,
                                    width: windowWidth * 0.21,
                                  }}>
                                  ID
                                </Text>
                                <View
                                  style={{
                                    // width: "58%",
                                    justifyContent: 'space-between',
                                    flexDirection: 'row',
                                    alignItems: 'center',
                                  }}>
                                  {editMode ? (
                                    <TextInput
                                      editable={true}
                                      style={{
                                        backgroundColor: Colors.fieldtBgColor,
                                        width: switchMerchant ? '85%' : 200,
                                        height: 35,
                                        borderRadius: 5,
                                        padding: 5,
                                        //marginVertical: 5,
                                        borderWidth: 1,
                                        borderColor: '#E5E5E5',
                                        paddingLeft: 10,
                                        fontFamily: 'NunitoSans-Regular',
                                        fontSize: switchMerchant ? 10 : 14,
                                      }}
                                      placeholder="ID (Max Length 12)"
                                      placeholderStyle={{
                                        fontFamily: 'NunitoSans-Regular',
                                        fontSize: switchMerchant ? 10 : 14,
                                      }}
                                      onChangeText={text => {
                                        setPoId(text);
                                      }}
                                      maxLength={12}
                                    //value={`ST${poId}`}
                                    />
                                  ) : (
                                    <View
                                      style={{
                                        borderRadius: 5,
                                        borderWidth: 1,
                                        borderColor: '#E5E5E5',
                                        height: 35,
                                        width: switchMerchant ? '85%' : 200,
                                        justifyContent: 'center',
                                        paddingHorizontal: 10,
                                        backgroundColor: Colors.fieldtBgColor,
                                      }}>
                                      <Text
                                        style={{
                                          fontSize: 15,
                                          fontFamily: 'NunitoSans-Regular',
                                          fontSize: switchMerchant ? 10 : 14,
                                        }}>
                                        {`${poId}`}
                                      </Text>
                                    </View>
                                  )}

                                  {selectedRackStockTransferEdit ? (
                                    <></>
                                  ) : (
                                    <TouchableOpacity
                                      style={{ marginLeft: 5 }}
                                      onPress={() => {
                                        setEditMode(!editMode);
                                      }}>
                                      {/* <MaterialCommunityIcons name="pencil" size={20} color={Colors.primaryColor} /> */}
                                      <Icon
                                        name="edit"
                                        size={switchMerchant ? 15 : 20}
                                        color={Colors.primaryColor}
                                      />
                                    </TouchableOpacity>
                                  )}
                                </View>
                              </View>

                              <View
                                style={{
                                  flexDirection: 'row',
                                  // width: "50%",
                                  alignItems: 'center',
                                  marginTop: 10,
                                }}>
                                <Text
                                  style={{
                                    // width: "38%",
                                    fontFamily: 'NunitoSans-Bold',
                                    fontSize: switchMerchant ? 10 : 14,
                                    width: windowWidth * 0.21,
                                  }}
                                  numberOfLines={2}>
                                  Source Rack
                                </Text>
                                <View
                                  style={{
                                    width: switchMerchant ? '60%' : 220,
                                    height: 30,
                                    backgroundColor: Colors.whiteColor,
                                    borderRadius: 10,
                                    justifyContent: 'center',
                                    alignSelf: 'center',
                                    zIndex: 5,
                                  }}>
                                  <View style={{ width: 220 }}>
                                    <TextInput
                                      editable={false}
                                      value={selectedSourceRackName}
                                      style={{
                                        backgroundColor: Colors.fieldtBgColor,
                                        width: 200,
                                        height: 35,
                                        borderRadius: 5,
                                        borderWidth: 1,
                                        borderColor: '#E5E5E5',
                                        flexDirection: 'row',
                                        padding: 5,
                                        paddingLeft: 10,
                                        fontFamily: 'NunitoSans-Regular',
                                        fontSize: switchMerchant ? 10 : 14,
                                      }}
                                    />
                                  </View>
                                  {/* )} */}
                                </View>
                              </View>
                            </View>
                            <View
                              style={{
                                // flexDirection: "row",
                                marginTop: 10,
                                // justifyContent: "space-between",
                                width: '96%',
                                alignSelf: 'center',
                                zIndex: -1,
                              }}>
                              <View
                                style={{
                                  flexDirection: 'row',
                                  // width: "45%",
                                  alignItems: 'center',
                                }}>
                                <Text
                                  style={{
                                    fontFamily: 'NunitoSans-Bold',
                                    fontSize: switchMerchant ? 10 : 14,
                                    // width: "42%",
                                    width: windowWidth * 0.21,
                                  }}>
                                  Created Date
                                </Text>
                                <View
                                  style={{
                                    flexDirection: 'row',
                                    // width: switchMerchant ? "50%" : "71%",
                                    marginTop: 0,
                                  }}>
                                  <View
                                    style={{
                                      flexDirection: 'row',
                                      backgroundColor: Colors.fieldtBgColor,
                                      width: switchMerchant ? '85%' : 200,
                                      height: 35,
                                      borderRadius: 5,
                                      padding: 5,
                                      //marginVertical: 5,
                                      borderWidth: 1,
                                      borderColor: '#E5E5E5',
                                      paddingLeft: 10,
                                      alignItems: 'center',
                                      //justifyContent: 'space-between'
                                    }}>
                                    <GCalendarGrey
                                      width={switchMerchant ? 15 : 20}
                                      height={switchMerchant ? 15 : 20}
                                      style={{ marginRight: 5 }}
                                    />
                                    <Text
                                      style={{
                                        marginRight: '18%',
                                        fontFamily: 'NunitoSans-Regular',
                                        fontSize: switchMerchant ? 10 : 14,
                                      }}>
                                      {selectedRackStockTransferEdit
                                        ? moment(createdDate).format(
                                          'DD MMM YYYY',
                                        )
                                        : moment(date1).format('DD MMM YYYY')}
                                    </Text>
                                  </View>
                                </View>
                              </View>

                              <View
                                style={{
                                  flexDirection: 'row',
                                  // width: "50%",
                                  alignItems: 'center',
                                  marginTop: 10,
                                }}>
                                <Text
                                  style={{
                                    fontFamily: 'NunitoSans-Bold',
                                    fontSize: switchMerchant ? 10 : 14,
                                    // width: "38%",
                                    width: windowWidth * 0.21,
                                  }}>
                                  Destination Rack
                                </Text>
                                <View
                                  style={{
                                    width: switchMerchant ? '60%' : 220,
                                    height: 30,
                                    backgroundColor: Colors.whiteColor,
                                    borderRadius: 10,
                                    justifyContent: 'center',
                                    alignSelf: 'center',
                                    zIndex: 5,
                                  }}>
                                  <View style={{ width: 220 }}>
                                    <TextInput
                                      editable={false}
                                      value={selectedTargetRackName}
                                      style={{
                                        backgroundColor: Colors.fieldtBgColor,
                                        width: 200,
                                        height: 35,
                                        borderRadius: 5,
                                        borderWidth: 1,
                                        borderColor: '#E5E5E5',
                                        flexDirection: 'row',
                                        padding: 5,
                                        paddingLeft: 10,
                                        fontFamily: 'NunitoSans-Regular',
                                        fontSize: switchMerchant ? 10 : 14,
                                      }}
                                    />
                                  </View>
                                  {/* )} */}
                                </View>
                              </View>
                            </View>

                            <View
                              style={{
                                // flexDirection: "row",
                                // justifyContent: "space-between",
                                marginTop: 10,
                                marginBottom: 20,

                                width: '96%',
                                alignSelf: 'center',
                                alignItem: 'center',
                              }}>
                              <View
                                style={{
                                  flexDirection: 'row',
                                  // width: "45%",
                                  alignItems: 'center',
                                }}>
                                <Text
                                  style={{
                                    fontFamily: 'NunitoSans-Bold',
                                    fontSize: switchMerchant ? 10 : 14,
                                    // width: "42%",
                                    width: windowWidth * 0.21,
                                  }}>
                                  Shipped Date
                                </Text>
                                {/* <Text style={{ color: '#adadad', marginLeft: 80, fontSize: 16, }}>8/10/2020</Text> */}

                                <View
                                  style={{
                                    flexDirection: 'row',
                                    backgroundColor: Colors.fieldtBgColor,
                                    width: switchMerchant ? '85%' : 200,
                                    height: 35,
                                    borderRadius: 5,
                                    padding: 5,
                                    //marginVertical: 5,
                                    borderWidth: 1,
                                    borderColor: '#E5E5E5',
                                    paddingLeft: 10,
                                    alignItems: 'center',
                                    zIndex: -1,
                                    //justifyContent: 'space-between'
                                  }}>
                                  <TouchableOpacity
                                    style={{ marginRight: 5 }}
                                    disabled={
                                      poStatus ===
                                      PURCHASE_ORDER_STATUS.COMPLETED
                                    }
                                    onPress={() => {
                                      setIsDateTimePickerVisible(true);
                                    }}>
                                    {poStatus ===
                                      PURCHASE_ORDER_STATUS.COMPLETED ? (
                                      <GCalendarGrey
                                        width={switchMerchant ? 15 : 20}
                                        height={switchMerchant ? 15 : 20}
                                      />
                                    ) : (
                                      <GCalendar
                                        width={switchMerchant ? 15 : 20}
                                        height={switchMerchant ? 15 : 20}
                                      />
                                    )}
                                  </TouchableOpacity>
                                  <View style={{ paddingRight: '15%' }}>
                                    <DatePicker
                                      disabled={
                                        poStatus ===
                                        PURCHASE_ORDER_STATUS.COMPLETED
                                      }
                                      selected={moment(date).toDate()}
                                      onChange={date => {
                                        setDate(moment(date));
                                      }}
                                      minDate={moment().toDate()}
                                      className="background"
                                    />
                                  </View>
                                </View>
                              </View>
                            </View>
                          </View>
                        </View>
                        <View
                          style={{
                            borderWidth: 1,
                            borderColor: '#E5E5E5',
                            zIndex: -5,
                          }}
                        />

                        <View
                          style={{
                            flexDirection: 'row',
                            justifyContent: 'center',
                            marginBottom: 10,
                            zIndex: -5,
                          }}>
                          <Text
                            style={{
                              alignSelf: 'center',
                              marginTop: 30,
                              fontSize: switchMerchant ? 15 : 20,
                              fontWeight: 'bold',
                            }}>
                            Stock to Transfer
                          </Text>
                        </View>

                        <View
                          style={{
                            backgroundColor: '#ffffff',
                            flexDirection: 'row',
                            paddingVertical: 20,
                            paddingHorizontal: 10,
                            marginTop: 10,
                            borderBottomWidth: StyleSheet.hairlineWidth,
                            zIndex: -5,
                          }}>
                          <Text
                            style={{
                              flex: 1.5,
                              fontSize: switchMerchant ? 10 : 14,
                              fontFamily: 'NunitoSans-Bold',
                              fontWeight: '500',
                            }}>
                            Product Name
                          </Text>
                          <Text
                            style={{
                              flex: 1,
                              fontSize: switchMerchant ? 10 : 14,
                              fontFamily: 'NunitoSans-Bold',
                              fontWeight: '500',
                              marginLeft: 10,
                            }}>
                            SKU
                          </Text>
                          <Text
                            style={{
                              flex: 0.8,
                              fontSize: switchMerchant ? 10 : 14,
                              fontFamily: 'NunitoSans-Bold',
                              fontWeight: '500',
                            }}>
                            In Stock
                          </Text>
                          <Text
                            style={{
                              flex: 1,
                              fontSize: switchMerchant ? 10 : 14,
                              fontFamily: 'NunitoSans-Bold',
                              fontWeight: '500',
                            }}>
                            Transfer Qty
                          </Text>
                          <Text
                            style={{
                              flex: 1,
                              fontSize: switchMerchant ? 10 : 14,
                              fontFamily: 'NunitoSans-Bold',
                              fontWeight: '500',
                            }}>
                            Balance Stock
                          </Text>
                          {/* <Text
                                                        style={{
                                                            flex: 0.9,
                                                            fontSize: switchMerchant ? 10 : 14,
                                                            fontFamily: "NunitoSans-Bold",
                                                            fontWeight: "500",
                                                        }}
                                                    >
                                                        Cost (RM)
                                                    </Text> */}
                          {/* <Text
                                                        style={{
                                                            flex: 0.9,
                                                            fontSize: switchMerchant ? 10 : 14,
                                                            fontFamily: "NunitoSans-Bold",
                                                            fontWeight: "500",
                                                        }}
                                                    >
                                                        Subtotal (RM)
                                                    </Text> */}
                          <View style={{ flex: 0.3 }} />
                        </View>
                        {outletItems.length > 0 ? (
                          <FlatList
                            nestedScrollEnabled={true}
                            showsVerticalScrollIndicator={false}
                            data={poItems.slice(0, visibleItems)}
                            extraData={poItems}
                            renderItem={renderAddStock}
                            keyExtractor={(item, index) => String(index)}
                            style={{ zIndex: -5 }}
                          />
                        ) : (
                          <View
                            style={{
                              alignItems: 'center',
                              marginVertical: 20,
                              marginTop: 50,
                              zIndex: -5,
                            }}>
                            <Text
                              style={{
                                color: Colors.descriptionColor,
                                fontFamily: 'NunitoSans-Regular',
                                fontSize: switchMerchant ? 10 : 14,
                              }}>
                              No supply items In current store
                            </Text>
                          </View>
                        )}

                        {outletItems.length > 0 ? (
                          <View
                            style={{
                              flexDirection: 'row',
                              marginBottom: 20,
                              justifyContent: 'space-between',
                            }}>
                            {!selectedRackStockTransferEdit ? (
                              <TouchableOpacity
                                style={styles.submitText2}
                                onPress={() => {
                                  if (outletItems.length > 0) {
                                    setPoItems([
                                      ...poItems,
                                      {
                                        // supplyItemId: '',
                                        // name: '',
                                        // sku: '',
                                        // quantity: '',
                                        // insertQuantity: 0,
                                        outletSupplyItemId:
                                          outletItems[0].uniqueId,
                                        name: outletItems[0].name,
                                        sku: outletItems[0].sku,
                                        unit: '',
                                        skuMerchant: outletItems[0].skuMerchant,
                                        quantity:
                                          outletItems[0].stockCount || 0,
                                        transferQuantity: 0,
                                        balance: 0,
                                        price: outletItems[0].price,
                                        totalPrice: 0,

                                        supplyItem: outletItems[0],
                                      },
                                    ]);
                                    setVisibleItems(
                                      prevVisible => prevVisible + 1,
                                    );
                                  } else {
                                    Alert.prompt(
                                      'Error',
                                      'No supplier items added',
                                    );
                                  }
                                }}>
                                <View
                                  style={{
                                    flexDirection: 'row',
                                    alignItems: 'center',
                                    marginTop: 5,
                                    marginLeft: 0,
                                    zIndex: -5,
                                  }}>
                                  <Icon1
                                    name="plus-circle"
                                    size={switchMerchant ? 15 : 20}
                                    color={Colors.primaryColor}
                                  />
                                  <Text
                                    style={{
                                      marginLeft: 10,
                                      color: Colors.primaryColor,
                                      marginBottom: 1,
                                      fontFamily: 'NunitoSans-Regular',
                                      fontSize: switchMerchant ? 10 : 14,
                                    }}>
                                    Add Product Slot
                                  </Text>
                                </View>
                              </TouchableOpacity>
                            ) : (
                              <></>
                            )}

                            <View
                              style={{
                                alignItems: 'flex-end',
                                marginTop: 5,
                                marginRight: 20,
                              }}>
                              <Text
                                style={{
                                  fontWeight: 'bold',
                                  fontSize: switchMerchant ? 10 : 16,
                                }}>
                                {poItems.totalPrice}
                              </Text>
                            </View>
                          </View>
                        ) : null}
                        {/* </ScrollView> */}

                        <View style={{ height: 50 }}></View>
                      </ScrollView>
                    </View>
                    {/* </ScrollView> */}
                  </View>
                </View>
              ) : null}
            </View>
            {/* </ScrollView> */}
          </ScrollView>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.highlightColor,
    flexDirection: 'row',
  },
  headerLogo: {
    width: 112,
    height: 25,
    marginLeft: 10,
  },
  list: {
    paddingVertical: 20,
    paddingHorizontal: 30,
    flexDirection: 'row',
    alignItems: 'center',
  },
  listItem: {
    marginLeft: 20,
    color: Colors.descriptionColor,
    fontSize: 16,
  },
  sidebar: {
    width: 0,
    // shadowColor: "#000",
    // shadowOffset: {
    //   width: 0,
    //   height: 8,
    // },
    // shadowOpacity: 0.44,
    // shadowRadius: 10.32,

    // elevation: 16,
  },
  content: {
    width: '100%',
  },
  submitText: {
    height: Dimensions.get('screen').height * 0.05,
    paddingVertical: 5,
    paddingHorizontal: 20,
    flexDirection: 'row',
    color: '#4cd964',
    textAlign: 'center',
    borderRadius: 10,
    borderWidth: 1,
    borderColor: Colors.primaryColor,
    justifyContent: 'center',
    alignContent: 'center',
    alignItems: 'center',
    marginRight: 10,
  },
  submitText1: {
    paddingVertical: 10,
    paddingHorizontal: 20,
    flexDirection: 'row',
    color: '#4cd964',
    textAlign: 'center',
    borderRadius: 10,
    borderWidth: 1,
    borderColor: Colors.primaryColor,
    alignSelf: 'flex-end',
    marginRight: 20,
    // marginTop: 15,
    height: 40,
    width: 220,
  },
  submitText2: {
    paddingVertical: 10,
    paddingHorizontal: 20,
    flexDirection: 'row',
    color: '#4cd964',
    textAlign: 'center',
    alignSelf: 'flex-end',
    marginRight: 20,
    marginTop: 15,
  },
  textInput: {
    width: 200,
    height: 40,
    // padding:5,
    backgroundColor: Colors.whiteColor,
    borderRadius: 0,
    marginRight: '35%',
    flexDirection: 'row',
    alignContent: 'center',
    alignItems: 'flex-end',

    shadowOffset: {
      width: 0,
      height: 7,
    },
    shadowOpacity: 0.43,
    shadowRadius: 0.51,
    elevation: 15,
  },
  searchIcon: {
    backgroundColor: Colors.whiteColor,
    height: 40,
    padding: 10,
    shadowOffset: {
      width: 0,
      height: 7,
    },
    shadowOpacity: 0.43,
    shadowRadius: 9.51,

    elevation: 15,
  },
  textInput1: {
    width: 160,
    padding: 5,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 5,
    borderRadius: 5,
    paddingTop: 5,
  },
  textInput2: {
    width: 100,
    padding: 5,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 5,
    borderRadius: 5,
    paddingTop: 5,
    textAlign: 'center',
  },
  confirmBox: {
    width: 450,
    height: 450,
    borderRadius: 30,
    backgroundColor: Colors.whiteColor,
  },
  textFieldInput: {
    height: 80,
    width: 900,
    paddingHorizontal: 20,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 5,
    borderRadius: 5,
  },
  circleIcon: {
    width: 30,
    height: 30,
    // resizeMode: 'contain',
    marginRight: 10,
    alignSelf: 'center',
  },
  headerLeftStyle: {
    width: Dimensions.get('screen').width * 0.17,
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    flex: 1,
    backgroundColor: Colors.modalBgColor,
    alignItems: 'center',
    justifyContent: 'center',
  },
  modalView: {
    height: Dimensions.get('screen').width * 0.2,
    width: Dimensions.get('screen').width * 0.3,
    backgroundColor: Colors.whiteColor,
    borderRadius: 12,
    padding: Dimensions.get('screen').width * 0.03,
    alignItems: 'center',
    justifyContent: 'center',
  },
  modalView1: {
    //height: Dimensions.get('screen').width * 0.2,
    width: Dimensions.get('screen').width * 0.3,
    backgroundColor: Colors.whiteColor,
    borderRadius: 12,
    padding: Dimensions.get('screen').width * 0.03,
    alignItems: 'center',
    justifyContent: 'center',
  },
  modalViewImport: {
    height: Dimensions.get('screen').width * 0.6,
    width: Dimensions.get('screen').width * 0.6,
    backgroundColor: Colors.whiteColor,
    borderRadius: Dimensions.get('screen').width * 0.03,
    padding: 20,
    paddingTop: 25,
    //paddingBottom: 30,
  },
  closeButton: {
    position: 'absolute',
    right: Dimensions.get('screen').width * 0.02,
    top: Dimensions.get('screen').width * 0.02,

    elevation: 1000,
    zIndex: 1000,
  },
  modalTitle: {
    alignItems: 'center',
    top: '20%',
    position: 'absolute',
  },
  modalBody: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  modalTitleText: {
    fontFamily: 'NunitoSans-Bold',
    textAlign: 'center',
    fontSize: 20,
  },
  modalDescText: {
    fontFamily: 'NunitoSans-SemiBold',
    fontSize: 18,
    color: Colors.fieldtTxtColor,
  },
  modalBodyText: {
    flex: 1,
    fontFamily: 'NunitoSans-SemiBold',
    fontSize: 25,
    width: '20%',
  },
  modalSaveButton: {
    width: Dimensions.get('screen').width * 0.15,
    backgroundColor: Colors.fieldtBgColor,
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 8,

    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 3.22,
    elevation: 1,

    marginVertical: 10,
  },
});
export default RackStockTransfer;
