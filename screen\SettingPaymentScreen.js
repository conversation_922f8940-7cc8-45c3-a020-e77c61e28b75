import { Text } from "react-native-fast-text";
import React, {
  Component,
  useReducer,
  useState,
  useEffect,
  useMemo,
  useCallback,
} from 'react';
import {
  StyleSheet,
  Image,
  View,
  Modal as ModalComponent,
  Alert,
  Dimensions,
  TouchableOpacity,
  Linking,
  ActivityIndicator,
  Platform,
  KeyboardAvoidingView,
  useWindowDimensions,
} from 'react-native';
import { ModalView as ModalViewComponent } from 'react-native-multiple-modals';
import Colors from '../constant/Colors';
import SideBar from './SideBar';
import FontAwesome from 'react-native-vector-icons/FontAwesome';
import FontAwesome5 from 'react-native-vector-icons/FontAwesome5';
import Entypo from 'react-native-vector-icons/Entypo';
import Icon from 'react-native-vector-icons/Feather';
import Icon3 from 'react-native-vector-icons/EvilIcons';
// import Swipeout from 'react-native-swipeout';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import Icon1 from 'react-native-vector-icons/AntDesign';
import AntDesign from 'react-native-vector-icons/AntDesign';
import Icon2 from 'react-native-vector-icons/Ionicons';
import ApiClient from '../util/ApiClient';
import API from '../constant/API';
import AsyncStorage from '@react-native-async-storage/async-storage';
import DropDownPicker from 'react-native-dropdown-picker';
import * as User from '../util/User';
import Styles from '../constant/Styles';
import moment from 'moment';
import MIcon from 'react-native-vector-icons/MaterialCommunityIcons';
import Switch from 'react-native-switch-pro';
import Swipeable from 'react-native-gesture-handler/Swipeable';
import {
  getTransformForModalInsideNavigation,
  getTransformForScreenInsideNavigation,
  isTablet,
  parseImagePickerResponse
} from '../util/common';
import { OutletStore } from '../store/outletStore';
import { UserStore } from '../store/userStore';
import { MerchantStore } from '../store/merchantStore';
import { TextInput, ScrollView, FlatList } from 'react-native-gesture-handler';
import RNPickerSelect from 'react-native-picker-select';
import { CommonStore } from '../store/commonStore';
import AsyncImage from '../components/asyncImage';
import Feather from 'react-native-vector-icons/Feather';
import { USER_QUEUE_STATUS, USER_QUEUE_STATUS_PARSED, OTHER_DELIVERY_PARTNER_TYPES_DROPDOWN_LIST } from '../constant/common';
import { launchCamera, launchImageLibrary } from 'react-native-image-picker';
import {
  QUEUE_SORT_FIELD_TYPE,
  REPORT_SORT_FIELD_TYPE_COMPARE,
  QUEUE_SORT_FIELD_TYPE_VALUE,
  REPORT_SORT_COMPARE_OPERATOR,
  PAYMENT_SORT_FIELD_TYPE,
  PAYMENT_SORT_FIELD_TYPE_VALUE,
  PAYMENT_SORT_FILED_TYPE_COMPARE,
  EXPAND_TAB_TYPE,
  OFFLINE_PAYMENT_METHOD_DROPDOWN_LIST,
  OFFLINE_PAYMENT_METHOD_TYPE,
} from '../constant/common';
import { color } from 'react-native-reanimated';
import { uploadImageToFirebaseStorage, sortPaymentDataList } from '../util/common';
import 'react-native-get-random-values';
import { v4 as uuidv4 } from 'uuid';
import APILocal from '../util/apiLocalReplacers';
import { useFocusEffect } from '@react-navigation/native';
import UserIdleWrapper from '../components/userIdleWrapper';
const ModalView = Platform.OS === 'android' ? ModalComponent : ModalViewComponent;

const SettingPaymentScreen = (props) => {
  const { navigation } = props;

  ///////////////////////////////////////////////////////////

  const [isMounted, setIsMounted] = useState(true);

  useFocusEffect(
    useCallback(() => {
      setIsMounted(true);
      return () => {
        setIsMounted(false);
      };
    }, [])
  );

  ///////////////////////////////////////////////////////////

  const METHOD_SECTION = {
    CUSTOM: 'CUSTOM',
    DEFAULT: 'DEFAULT',
  };
  const [methodSection, setMethodSection] = useState(METHOD_SECTION.CUSTOM);
  const [paymentMethods, setPaymentMethods] = useState(
    OFFLINE_PAYMENT_METHOD_DROPDOWN_LIST,
  );
  const [
    outletPaymentMethodsDropdownList,
    setOutletPaymentMethodsDropdownList,
  ] = useState([]);
  const [paymentMethodsHidden, setPaymentMethodsHidden] = useState({});

  const { width: windowWidth, height: windowHeight } = useWindowDimensions();

  const [visible, setVisible] = useState(false);

  const [addPaymentMethodModal, setAddPaymentMethodModal] = useState(false);
  const [confirmQueueModal, setConfirmQueueModal] = useState(false);

  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState({});

  const [image, setImage] = useState('');
  const [imageType, setImageType] = useState('');
  const [isImageChanged, setIsImageChanged] = useState(false);

  const [paymentName, setPaymentName] = useState('');

  const [table, setTable] = useState([]);
  const [queue, setQueue] = useState([]);
  const [newReservationStatus, setNewReservationStatus] = useState(false);

  const [filterType, setFilterType] = useState(0);

  const [userQueues, setUserQueues] = useState([]);

  const [switchMerchant, setSwitchMerchant] = useState(isTablet() ? false : true);

  const [loadingDict, setLoadingDict] = useState({});

  const userQueuesRaw = OutletStore.useState((s) => s.userQueues);
  const outletPaymentMethods = OutletStore.useState((s) => s.outletPaymentMethods);

  const currOutlet = MerchantStore.useState((s) => s.currOutlet);

  const [temp, setTemp] = useState('');

  const userName = UserStore.useState((s) => s.name);
  const userId = UserStore.useState((s) => s.firebaseUid);
  const userEmail = UserStore.useState((s) => s.email);
  const merchantId = UserStore.useState((s) => s.merchantId);
  const merchantName = MerchantStore.useState((s) => s.name);
  const merchantLogo = MerchantStore.useState((s) => s.logo);

  const isLoading = CommonStore.useState((s) => s.isLoading);

  const isAlphaUser = UserStore.useState((s) => s.isAlphaUser);
  const expandTab = CommonStore.useState((s) => s.expandTab);
  const currPageStack = CommonStore.useState((s) => s.currPageStack);

  const outletSelectDropdownView = CommonStore.useState(
    (s) => s.outletSelectDropdownView,
  );

  const [partnerType, setPartnerType] = useState('');

  //New UX
  const [controller1, setController1] = useState({});

  useEffect(() => {
    var userQueuesTemp = [];

    if (filterType == 0) {
      //Prioritize
      userQueuesTemp = userQueuesRaw;
    }
    if (filterType == 1) {
      //Prioritize
      userQueuesTemp = userQueuesRaw.filter(
        (order) => order.status === USER_QUEUE_STATUS.PENDING,
      );
    }
    if (filterType == 2) {
      //orderid
      userQueuesTemp = userQueuesRaw.filter(
        (order) => order.status === USER_QUEUE_STATUS.ACCEPTED,
      );
    }
    if (filterType == 3) {
      //date time
      userQueuesTemp = userQueuesRaw.filter(
        (order) => order.status === USER_QUEUE_STATUS.SEATED,
      );
    }
    if (filterType == 4) {
      //Name
      userQueuesTemp = userQueuesRaw.filter(
        (order) => order.status === USER_QUEUE_STATUS.SERVED,
      );
    }
    if (filterType == 5) {
      //Waiting Time
      userQueuesTemp = userQueuesRaw.filter(
        (order) => order.status === USER_QUEUE_STATUS.CANCELED,
      );
    }
    if (filterType == 6) {
      //Waiting Time
      userQueuesTemp = userQueuesRaw.filter(
        (order) => order.status === USER_QUEUE_STATUS.NO_SHOW,
      );
    }

    setUserQueues(userQueuesTemp);
  }, [filterType, userQueuesRaw]);

  useEffect(() => {
    if (currOutlet.paymentMethodsHidden) {
      setPaymentMethodsHidden(currOutlet.paymentMethodsHidden);
    }
    else {
      setPaymentMethodsHidden({});
    }
  }, [currOutlet]);

  const handleChoosePhoto = () => {
    const imagePickerOptions = {
      mediaType: 'photo',
      quality: 0.5,
      includeBase64: false,
      maxWidth: 512,
      maxHeight: 512,
    };

    launchImageLibrary(imagePickerOptions, (response) => {
      if (response.didCancel) {
      } else if (response.error) {
        Alert.alert(response.error.toString());
      } else {
        const responseParsed = parseImagePickerResponse(response);

        // setState({ image: response.uri });
        setImage(responseParsed.uri);
        setImageType(responseParsed.uri.slice(responseParsed.uri.lastIndexOf('.')));

        setIsImageChanged(true);
      }
    });
  }


  const createPaymentMethod = async () => {
    if (
      !image ||
      paymentName.length < 0
    ) {
      Alert.alert(
        'Info',
        'Please fill in the information',
      );

    } else {
      var isExisted = false;

      for (var i = 0; i < outletPaymentMethods.length; i++) {
        if (outletPaymentMethods[i].name === paymentName) {
          isExisted = true;
          break;
        }
      }

      if (isExisted) {
        Alert.alert('Info', 'Payment method with the same name exists');

      }
      else {
        CommonStore.update((s) => {
          s.isLoading = true;
        });

        ///UPLOAD IMAGE///

        var outletItemImagePath = '';
        // var outletItemIdLocal = selectedPaymentMethod ? selectedPaymentMethod.uniqueId : uuidv4();
        var outletItemIdLocal = uuidv4();

        if (image && imageType) {

          outletItemImagePath = await uploadImageToFirebaseStorage({
            uri: image,
            type: imageType,
          }, `/merchant/${merchantId}/paymentMethod/${outletItemIdLocal}/image${imageType}`);
        }

        ////////////////

        var body = {
          outletId: currOutlet.uniqueId,
          merchantId,
          name: paymentName,
          image: outletItemImagePath,
          deliveryPartner: partnerType ? partnerType : '',
        };

        // ApiClient.POST(API.createOutletPaymentMethod, body, false)
        APILocal.createOutletPaymentMethod({ body, uid: userId })
          .then((result) => {
            if (result.status) {

              // setSelectedPaymentMethod(result.userQueue);

              if (Platform.OS === 'ios') {
                Alert.alert('Success', 'Payment method has been created');
              } else {
                Alert.alert('Success', 'Payment method has been created');
              }

              setAddPaymentMethodModal(false);

              CommonStore.update((s) => {
                s.isLoading = false;
              });
            }
          })
          .catch((err) => {
            // console.log(err);

            CommonStore.update((s) => {
              s.isLoading = false;
            });
          });
      }
    }
  };

  const updatePaymentMethod = async () => {
    if (
      !image ||
      paymentName.length < 0
    ) {
      Alert.alert(
        'Info',
        'Please fill in the information',
      );

    } else {
      var isExisted = false;

      for (var i = 0; i < outletPaymentMethods.length; i++) {
        if (outletPaymentMethods[i].name === paymentName && outletPaymentMethods[i].uniqueId !== selectedPaymentMethod.uniqueId) {
          isExisted = true;
          break;
        }
      }

      if (isExisted) {
        Alert.alert('Info', 'Payment method with the same name exists');

      }
      else {
        CommonStore.update((s) => {
          s.isLoading = true;
        });

        ///UPLOAD IMAGE///

        var outletItemImagePath = '';
        var outletItemIdLocal = selectedPaymentMethod ? selectedPaymentMethod.uniqueId : uuidv4();
        // var outletItemIdLocal = uuidv4();

        if (image && imageType) {

          outletItemImagePath = await uploadImageToFirebaseStorage({
            uri: image,
            type: imageType,
          }, `/merchant/${merchantId}/paymentMethod/${outletItemIdLocal}/image${imageType}`);
        }

        ////////////////

        var body = {
          outletId: currOutlet.uniqueId,
          merchantId,
          name: paymentName,
          image: outletItemImagePath || image,

          paymentMethodId: selectedPaymentMethod.uniqueId,

          deliveryPartner: partnerType ? partnerType : '',
        };

        // ApiClient.POST(API.updateOutletPaymentMethod, body, false)
        APILocal.updateOutletPaymentMethod({ body, uid: userId })
          .then((result) => {
            if (result.status) {

              // setSelectedPaymentMethod(result.userQueue);

              if (Platform.OS === 'ios') {
                Alert.alert('Success', 'Payment method has been updated successfully');
              } else {
                Alert.alert('Success', 'Payment method has been updated successfully');
              }

              setAddPaymentMethodModal(false);

              CommonStore.update((s) => {
                s.isLoading = false;
              });
            }
          })
          .catch((err) => {
            // console.log(err);

            CommonStore.update((s) => {
              s.isLoading = false;
            });
          });
      }
    }
  };

  const deletePaymentMethod = async (paymentMethodId) => {
    // means existing item

    ///////////////////////////////////

    var body = {
      paymentMethodId,
    };

    // console.log(body);

    CommonStore.update(s => {
      s.isLoading = true;
    });

    // ApiClient.POST(API.deleteOutletPaymentMethod, body, false)
    APILocal.deleteOutletPaymentMethod({ body, uid: userId })
      .then((result) => {
        if (result && result.status === 'success') {
          Alert.alert(
            'Success',
            "Payment method has been removed",
            [
              {
                text: 'OK',
                onPress: () => {

                },
              },
            ],
            { cancelable: false },
          );
        }

        CommonStore.update(s => {
          s.isLoading = false;
        });
      });
  }

  const togglePaymentMethod = async (paymentMethodId, status) => {
    // means existing item

    ///////////////////////////////////

    setLoadingDict({
      ...loadingDict,
      [paymentMethodId]: true,
    });

    var body = {
      paymentMethodId,
      status,
    };

    // console.log(body);

    CommonStore.update(s => {
      s.isLoading = true;
    });

    // ApiClient.POST(API.toggleOutletPaymentMethodStatus, body, false)
    APILocal.toggleOutletPaymentMethodStatus({ body, uid: userId })
      .then((result) => {
        if (result && result.status === 'success') {
          // Alert.alert(
          //   'Success',
          //   "Payment method deleted successfully.",
          //   [
          //     {
          //       text: 'OK',
          //       onPress: () => {

          //       },
          //     },
          //   ],
          //   { cancelable: false },
          // );
        }

        CommonStore.update(s => {
          s.isLoading = false;
        });

        setLoadingDict({
          ...loadingDict,
          [paymentMethodId]: false,
        });
      });
  }

  const toggleDeposit = async (paymentMethodId, status) => {
    // means existing item

    ///////////////////////////////////

    setLoadingDict({
      ...loadingDict,
      [paymentMethodId]: true,
    });

    var body = {
      paymentMethodId,
      status,
    };

    // console.log(body);

    CommonStore.update(s => {
      s.isLoading = true;
    });

    // ApiClient.POST(API.toggleOutletPaymentMethodStatus, body, false)
    APILocal.toggleOutletPaymentMethodDeposit({ body, uid: userId })
      .then((result) => {
        if (result && result.status === 'success') {
          // Alert.alert(
          //   'Success',
          //   "Payment method deleted successfully.",
          //   [
          //     {
          //       text: 'OK',
          //       onPress: () => {

          //       },
          //     },
          //   ],
          //   { cancelable: false },
          // );
        }

        CommonStore.update(s => {
          s.isLoading = false;
        });

        setLoadingDict({
          ...loadingDict,
          [paymentMethodId]: false,
        });
      });
  }

  const updateOutletPaymentMethodsHidden = async () => {
    var body = {
      paymentMethodsHidden,
      outletId: currOutlet.uniqueId,
    };

    // console.log(body);

    CommonStore.update(s => {
      s.isLoading = true;
    });

    // ApiClient.POST(API.updateOutletPaymentMethodsHidden, body, false)
    APILocal.updateOutletPaymentMethodsHidden({ body, })
      .then((result) => {
        if (result && result.status === 'success') {
          Alert.alert(
            'Success',
            'Payment methods has been updated.',
            [
              {
                text: 'OK',
                onPress: () => {
                },
              },
            ],
            { cancelable: false },
          );
        }
        else {
          Alert.alert(
            'Error',
            'Failed to update',
          );
        }

        CommonStore.update(s => {
          s.isLoading = false;
        });
      }).catch(err => { console.log(err) });
  }

  const [queueOrders, setQueueOrders] = useState([]);
  // console.log('USER_QUEUE_STATUS_PARSED.SEATED');
  // console.log(setQueueOrders);

  const [sort, setSort] = useState('');
  const [search, setSearch] = useState('');

  //Start Here Sorting

  const sortOperationQueue = (dataList, queueSortFieldType) => {
    var dataListTemp = [...dataList];
    // console.log('dataList');
    // console.log(dataList);

    // console.log('queueSortFieldType');
    // console.log(queueSortFieldType);

    const queueSortFieldTypeValue =
      QUEUE_SORT_FIELD_TYPE_VALUE[queueSortFieldType];

    const queueSortFieldTypeCompare =
      REPORT_SORT_FIELD_TYPE_COMPARE[queueSortFieldType];

    //QUEUE_ID
    if (queueSortFieldTypeCompare === REPORT_SORT_COMPARE_OPERATOR.ASC) {
      if (queueSortFieldType === QUEUE_SORT_FIELD_TYPE.QUEUE_ID_ASC) {
        dataListTemp.sort(
          (a, b) =>
            (a[queueSortFieldTypeValue] ? a[queueSortFieldTypeValue] : '') -
            (b[queueSortFieldTypeValue] ? b[queueSortFieldTypeValue] : ''),
        );
      } else {
        dataListTemp.sort(
          (a, b) =>
            (a[queueSortFieldTypeValue] ? a[queueSortFieldTypeValue] : '') -
            (b[queueSortFieldTypeValue] ? b[queueSortFieldTypeValue] : ''),
        );
      }
    } else if (
      queueSortFieldTypeCompare === REPORT_SORT_COMPARE_OPERATOR.DESC
    ) {
      if (queueSortFieldType === QUEUE_SORT_FIELD_TYPE.QUEUE_ID_DESC) {
        dataListTemp.sort(
          (a, b) =>
            (b[queueSortFieldTypeValue] ? b[queueSortFieldTypeValue] : '') -
            (a[queueSortFieldTypeValue] ? a[queueSortFieldTypeValue] : ''),
        );
      } else {
        dataListTemp.sort(
          (a, b) =>
            (b[queueSortFieldTypeValue] ? b[queueSortFieldTypeValue] : '') -
            (a[queueSortFieldTypeValue] ? a[queueSortFieldTypeValue] : ''),
        );
      }
    }

    //NAME
    if (queueSortFieldTypeCompare === REPORT_SORT_COMPARE_OPERATOR.ASC) {
      if (queueSortFieldType === QUEUE_SORT_FIELD_TYPE.NAME_ASC) {
        dataListTemp.sort((a, b) =>
          (a[queueSortFieldTypeValue]
            ? a[queueSortFieldTypeValue]
            : ''
          ).localeCompare(
            b[queueSortFieldTypeValue] ? b[queueSortFieldTypeValue] : '',
          ),
        );
      } else {
        dataListTemp.sort(
          (a, b) =>
            (a[queueSortFieldTypeValue] ? a[queueSortFieldTypeValue] : '') -
            (b[queueSortFieldTypeValue] ? b[queueSortFieldTypeValue] : ''),
        );
      }
    } else if (
      queueSortFieldTypeCompare === REPORT_SORT_COMPARE_OPERATOR.DESC
    ) {
      if (queueSortFieldType === QUEUE_SORT_FIELD_TYPE.NAME_DESC) {
        dataListTemp.sort((a, b) =>
          (b[queueSortFieldTypeValue]
            ? b[queueSortFieldTypeValue]
            : ''
          ).localeCompare(
            a[queueSortFieldTypeValue] ? a[queueSortFieldTypeValue] : '',
          ),
        );
      } else {
        dataListTemp.sort(
          (a, b) =>
            (b[queueSortFieldTypeValue] ? b[queueSortFieldTypeValue] : '') -
            (a[queueSortFieldTypeValue] ? a[queueSortFieldTypeValue] : ''),
        );
      }
    }

    //DATE_TIME
    if (queueSortFieldTypeCompare === REPORT_SORT_COMPARE_OPERATOR.ASC) {
      if (queueSortFieldType === QUEUE_SORT_FIELD_TYPE.DATE_TIME_ASC) {
        dataListTemp.sort(
          (a, b) =>
            (moment(a[queueSortFieldTypeValue]).valueOf()
              ? moment(a[queueSortFieldTypeValue]).valueOf()
              : '') -
            (moment(b[queueSortFieldTypeValue]).valueOf()
              ? moment(b[queueSortFieldTypeValue]).valueOf()
              : ''),
        );
      } else {
        dataListTemp.sort(
          (a, b) =>
            (a[queueSortFieldTypeValue] ? a[queueSortFieldTypeValue] : '') -
            (b[queueSortFieldTypeValue] ? b[queueSortFieldTypeValue] : ''),
        );
      }
    } else if (
      queueSortFieldTypeCompare === REPORT_SORT_COMPARE_OPERATOR.DESC
    ) {
      if (queueSortFieldType === QUEUE_SORT_FIELD_TYPE.DATE_TIME_DESC) {
        dataListTemp.sort(
          (a, b) =>
            (moment(b[queueSortFieldTypeValue]).valueOf()
              ? moment(b[queueSortFieldTypeValue]).valueOf()
              : '') -
            (moment(a[queueSortFieldTypeValue]).valueOf()
              ? moment(a[queueSortFieldTypeValue]).valueOf()
              : ''),
        );
      } else {
        dataListTemp.sort(
          (a, b) =>
            (b[queueSortFieldTypeValue] ? b[queueSortFieldTypeValue] : '') -
            (a[queueSortFieldTypeValue] ? a[queueSortFieldTypeValue] : ''),
        );
      }
    }

    //CAPACITY
    if (queueSortFieldTypeCompare === REPORT_SORT_COMPARE_OPERATOR.ASC) {
      if (queueSortFieldType === QUEUE_SORT_FIELD_TYPE.CAPACITY_ASC) {
        dataListTemp.sort(
          (a, b) =>
            (a[queueSortFieldTypeValue] ? a[queueSortFieldTypeValue] : '') -
            (b[queueSortFieldTypeValue] ? b[queueSortFieldTypeValue] : ''),
        );
      } else {
        dataListTemp.sort(
          (a, b) =>
            (b[queueSortFieldTypeValue] ? b[queueSortFieldTypeValue] : '') -
            (a[queueSortFieldTypeValue] ? a[queueSortFieldTypeValue] : ''),
        );
      }
    } else if (
      queueSortFieldTypeCompare === REPORT_SORT_COMPARE_OPERATOR.DESC
    ) {
      if (queueSortFieldType === QUEUE_SORT_FIELD_TYPE.CAPACITY_DESC) {
        dataListTemp.sort(
          (a, b) =>
            (b[queueSortFieldTypeValue] ? b[queueSortFieldTypeValue] : '') -
            (a[queueSortFieldTypeValue] ? a[queueSortFieldTypeValue] : ''),
        );
      } else {
        dataListTemp.sort(
          (a, b) =>
            (a[queueSortFieldTypeValue] ? a[queueSortFieldTypeValue] : '') -
            (b[queueSortFieldTypeValue] ? b[queueSortFieldTypeValue] : ''),
        );
      }
    }

    //WAITING_TIME
    if (queueSortFieldTypeCompare === REPORT_SORT_COMPARE_OPERATOR.ASC) {
      if (queueSortFieldType === QUEUE_SORT_FIELD_TYPE.WAITING_TIME_ASC) {
        dataListTemp.sort(
          (a, b) =>
            (moment(a[queueSortFieldTypeValue]).valueOf()
              ? moment(a[queueSortFieldTypeValue]).valueOf()
              : '') -
            (moment(b[queueSortFieldTypeValue]).valueOf()
              ? moment(b[queueSortFieldTypeValue]).valueOf()
              : ''),
        );
      }
    } else if (
      queueSortFieldTypeCompare === REPORT_SORT_COMPARE_OPERATOR.DESC
    ) {
      if (queueSortFieldType === QUEUE_SORT_FIELD_TYPE.WAITING_TIME_DESC) {
        dataListTemp.sort(
          (a, b) =>
            (moment(b[queueSortFieldTypeValue]).valueOf()
              ? moment(b[queueSortFieldTypeValue]).valueOf()
              : '') -
            (moment(a[queueSortFieldTypeValue]).valueOf()
              ? moment(a[queueSortFieldTypeValue]).valueOf()
              : ''),
        );
      }
    }

    //STATUS
    if (queueSortFieldTypeCompare === REPORT_SORT_COMPARE_OPERATOR.ASC) {
      if (queueSortFieldType === QUEUE_SORT_FIELD_TYPE.STATUS_ASC) {
        dataListTemp.sort((a, b) =>
          (a[queueSortFieldTypeValue]
            ? a[queueSortFieldTypeValue]
            : ''
          ).localeCompare(
            b[queueSortFieldTypeValue] ? b[queueSortFieldTypeValue] : '',
          ),
        );
      } else {
        dataListTemp.sort(
          (a, b) =>
            (a[queueSortFieldTypeValue] ? a[queueSortFieldTypeValue] : '') -
            (b[queueSortFieldTypeValue] ? b[queueSortFieldTypeValue] : ''),
        );
      }
    } else if (
      queueSortFieldTypeCompare === REPORT_SORT_COMPARE_OPERATOR.DESC
    ) {
      if (queueSortFieldType === QUEUE_SORT_FIELD_TYPE.STATUS_DESC) {
        dataListTemp.sort((a, b) =>
          (b[queueSortFieldTypeValue]
            ? b[queueSortFieldTypeValue]
            : ''
          ).localeCompare(
            a[queueSortFieldTypeValue] ? a[queueSortFieldTypeValue] : '',
          ),
        );
      } else {
        dataListTemp.sort(
          (a, b) =>
            (b[queueSortFieldTypeValue] ? b[queueSortFieldTypeValue] : '') -
            (a[queueSortFieldTypeValue] ? a[queueSortFieldTypeValue] : ''),
        );
      }
    }

    return dataListTemp;
  };

  navigation.setOptions({
    headerLeft: () => (
      <TouchableOpacity
        onPress={() => {
          if (isAlphaUser || true) {
            navigation.navigate('MenuOrderingScreen');

            CommonStore.update((s) => {
              s.currPage = 'MenuOrderingScreen';
              s.currPageStack = [...currPageStack, 'MenuOrderingScreen'];
            });
          }
          else {
            navigation.navigate('Table');

            CommonStore.update((s) => {
              s.currPage = 'Table';
              s.currPageStack = [...currPageStack, 'Table'];
            });
          }
          if (expandTab !== EXPAND_TAB_TYPE.OPERATION) {
            CommonStore.update((s) => {
              s.expandTab = EXPAND_TAB_TYPE.OPERATION;
            });
          }
        }}
        style={styles.headerLeftStyle}>
        <Image
          style={[{
            width: 124,
            height: 26,
          }, switchMerchant ? {
            transform: [
              { scaleX: 0.7 },
              { scaleY: 0.7 }
            ],
          } : {}]}
          resizeMode="contain"
          source={require('../assets/image/logo.png')}
        />
      </TouchableOpacity>
    ),
    headerTitle: () => (
      <View
        style={[
          {
            // justifyContent: Platform.OS === 'android' ? 'center' : 'flex-start',
            // alignItems: Platform.OS === 'android' ? 'center' : 'flex-start',
            // marginRight: Platform.OS === 'ios' ? "27%" : 0,
            // bottom: switchMerchant ? '2%' : 0,
            ...global.getHeaderTitleStyle(),
          },
          // windowWidth >= 768 && switchMerchant
          //   ? { right: windowWidth * 0.1 }
          //   : {},
          // windowWidth <= 768
          //   ? { right: 20 }
          //   : {},
        ]}>
        <Text
          style={{
            fontSize: switchMerchant ? 20 : 24,
            // lineHeight: 25,
            textAlign: 'left',
            alignItems: 'flex-start',
            justifyContent: 'flex-start',
            fontFamily: 'NunitoSans-Bold',
            color: Colors.whiteColor,
            opacity: 1,
            paddingLeft: Platform.OS === 'ios' ? "1%" : 0,
          }}>
          Payment Settings
        </Text>
      </View>
    ),
    headerRight: () => (
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
        }}>
        {outletSelectDropdownView()}
        <View
          style={{
            backgroundColor: 'white',
            width: 0.5,
            height: windowHeight * 0.025,
            opacity: 0.8,
            marginHorizontal: 15,
            bottom: -1,
          }} />
        <TouchableOpacity
          onPress={() => {
            if (global.currUserRole === 'admin') {
              navigation.navigate('Setting');
            }
          }}
          style={{ flexDirection: 'row', alignItems: 'center' }}>
          <Text
            style={[{
              fontFamily: 'NunitoSans-SemiBold',
              fontSize: switchMerchant ? 10 : 16,
              color: Colors.secondaryColor,
              marginRight: 15,
            }, switchMerchant ? { width: windowWidth / 8 } : {}]}
            numberOfLines={switchMerchant ? 1 : 1}
          >
            {userName}
          </Text>
          <View
            style={{
              marginRight: 30,
              width: windowHeight * 0.05,
              height: windowHeight * 0.05,
              borderRadius: windowHeight * 0.05 * 0.5,
              alignItems: 'center',
              justifyContent: 'center',
              backgroundColor: 'white',
            }}>
            <Image
              style={{
                width: windowHeight * 0.035,
                height: windowHeight * 0.035,
                alignSelf: 'center',
              }}
              source={require('../assets/image/profile-pic.jpg')}
            />
          </View>
        </TouchableOpacity>
      </View>
    ),
  });

  const renderPaymentMethod = ({ item, index }) => {

    return (
      <View
        style={{
          paddingVertical: 5,
          shadowOffset: {
            width: 0,
            height: 2,
          },
          shadowOpacity: 0.22,
          shadowRadius: 3.22,
          elevation: 1,
        }}>
        <View
          style={[
            { elevation: 1, borderRadius: 7, backgroundColor: 'white' },
            switchMerchant
              ? {
                //width: windowWidth * 0.8,
              }
              : {},
          ]}>
          <View
            style={[
              {
                width: '100%',
                flexDirection: 'row',
                height: windowHeight * 0.1,
                alignItems: 'center',
                borderBottomColor: Colors.fieldtT,
                // borderBottomWidth: expandViewDict[item.uniqueId] == true ? StyleSheet.hairlineWidth : null
              },
              switchMerchant
                ? {
                  //width: windowWidth * 0.82,
                  // height: windowHeight * 0.1,
                }
                : {},
            ]}>
            <View
              style={[
                {
                  width: '8%',
                  marginLeft: '2.5%',
                  //marginHorizontal: 0.5,
                  alignItems: 'flex-start',
                },
                switchMerchant
                  ? {
                    width: '8%',
                    left: '10%',
                  }
                  : {},
              ]}>
              <AsyncImage
                style={[
                  {
                    width: 30,
                    height: 30,
                    marginLeft: 0,
                  },
                  switchMerchant
                    ? {
                      width: windowWidth * 0.03,
                      height: windowHeight * 0.05,
                    }
                    : {},
                ]}
                resizeMode="contain"
                // source={require('../assets/image/dineinGrey.png')}
                source={{
                  uri: item.image
                }}
                item={item}
              />
            </View>

            <View
              style={[
                {
                  width: '18%',
                  flexDirection: 'row',
                  //marginHorizontal: 0.5,
                  alignItems: 'flex-start',
                },
                switchMerchant
                  ? {
                    width: '18%',
                    alignItems: 'flex-start',
                    // left: '10%'
                  }
                  : {},
              ]}>
              <View
                style={{
                  flexDirection: 'row',
                  //marginHorizontal: 0.5,
                  width: Platform.OS == 'ios' ? '60%' : '60%',
                }}>
                <Text
                  numberOfLines={3}
                  style={[
                    {
                      color: Colors.fontDark,
                      fontSize: 16,
                      fontFamily: 'NunitoSans-Bold',
                      textAlign: 'left',
                    },
                    switchMerchant
                      ? {
                        fontSize: 10,
                      }
                      : {},
                  ]}>
                  {item.name}
                </Text>
              </View>
            </View>

            <View
              style={[
                {
                  width: '18%',
                  //marginHorizontal: 0.5,
                  textAlign: 'left',
                },
                switchMerchant
                  ? {
                    width: '18%',
                    // left: '10%'
                  }
                  : {},
              ]}>

              <Text
                style={[
                  {
                    color: Colors.fontDark,
                    fontSize: 16,
                    fontFamily: 'NunitoSans-Bold',
                  },
                  switchMerchant
                    ? {
                      fontSize: 10,
                    }
                    : {},
                ]}>
                {moment(item.createdAt).format('DD MMM YYYY')}
              </Text>
              <Text
                style={[
                  {
                    color: Colors.fontDark,
                    fontSize: 16,
                    fontFamily: 'NunitoSans-Bold',
                    marginTop: 2,
                  },
                  switchMerchant
                    ? {
                      fontSize: 10,
                    }
                    : {},
                ]}>
                {moment(item.createdAt).format('hh:mm A')}
              </Text>
            </View>

            <View
              style={[
                {
                  width: '18%',
                  //marginHorizontal: 0.5,
                  textAlign: 'left',
                },
                switchMerchant
                  ? {
                    width: '18%',
                    // left: '10%'
                  }
                  : {},
              ]}>

              <Text
                style={[
                  {
                    color: Colors.fontDark,
                    fontSize: 16,
                    fontFamily: 'NunitoSans-Bold',
                  },
                  switchMerchant
                    ? {
                      fontSize: 10,
                    }
                    : {},
                ]}>
                {moment(item.updatedAt).format('DD MMM YYYY')}
              </Text>
              <Text
                style={[
                  {
                    color: Colors.fontDark,
                    fontSize: 16,
                    fontFamily: 'NunitoSans-Bold',
                    marginTop: 2,
                  },
                  switchMerchant
                    ? {
                      fontSize: 10,
                    }
                    : {},
                ]}>
                {moment(item.updatedAt).format('hh:mm A')}
              </Text>
            </View>

            <View
              style={[
                {
                  width: '12%',
                  //marginHorizontal: 0.5,
                  textAlign: 'left',
                },
                switchMerchant
                  ? {
                    width: '10%',
                    // left: '10%'
                  }
                  : {},
              ]}>
              {
                !loadingDict[item.uniqueId]
                  ?
                  <>
                    {switchMerchant ? (
                      <Switch
                        //value={currOutlet.queueStatus}
                        onSyncPress={(value) => {
                          // setState({ newReservationStatus: item }, function  = () => {
                          //   switchQueueStatus();
                          // });

                          togglePaymentMethod(item.uniqueId, value);
                        }}
                        width={20}
                        height={10}
                        circleColorActive={Colors.primaryColor}
                        circleColorInactive={Colors.fieldtTxtColor}
                        backgroundActive="#dddddd"
                        value={item.isActive}
                      />
                    ) : (

                      <Switch
                        //value={currOutlet.queueStatus}
                        onSyncPress={(value) => {
                          // setState({ newReservationStatus: item }, function  = () => {
                          //   switchQueueStatus();
                          // });

                          togglePaymentMethod(item.uniqueId, value);
                        }}
                        width={42}
                        circleColorActive={Colors.primaryColor}
                        circleColorInactive={Colors.fieldtTxtColor}
                        backgroundActive="#dddddd"
                        value={item.isActive}
                      />
                    )}
                    <Text
                      style={[
                        {
                          color: item.isActive ? Colors.primaryColor : Colors.tabRed,
                          fontSize: 16,
                          fontFamily: 'NunitoSans-Bold',
                          marginTop: 2,
                        },
                        switchMerchant
                          ? {
                            fontSize: 10,
                          }
                          : {},
                      ]}>
                      {item.isActive ? 'Active' : 'Disabled'}
                    </Text>
                  </>
                  :
                  <View style={{
                    // backgroundColor: 'blue',
                    alignItems: 'flex-start',
                  }}>
                    <ActivityIndicator color={Colors.primaryColor} size={switchMerchant ? 'small' : 'large'} />
                  </View>
              }
            </View>

            <View
              style={[
                {
                  width: '12%',
                  //marginHorizontal: 0.5,
                  textAlign: 'left',
                },
                switchMerchant
                  ? {
                    width: '10%',
                    // left: '10%'
                  }
                  : {},
              ]}>
              {
                !loadingDict[item.uniqueId]
                  ?
                  <>
                    {switchMerchant ? (
                      <Switch
                        //value={currOutlet.queueStatus}
                        onSyncPress={(value) => {
                          // setState({ newReservationStatus: item }, function  = () => {
                          //   switchQueueStatus();
                          // });

                          toggleDeposit(item.uniqueId, value);
                        }}
                        width={20}
                        height={10}
                        circleColorActive={Colors.primaryColor}
                        circleColorInactive={Colors.fieldtTxtColor}
                        backgroundActive="#dddddd"
                        value={item.isDepo}
                      />
                    ) : (

                      <Switch
                        //value={currOutlet.queueStatus}
                        onSyncPress={(value) => {
                          // setState({ newReservationStatus: item }, function  = () => {
                          //   switchQueueStatus();
                          // });

                          toggleDeposit(item.uniqueId, value);
                        }}
                        width={42}
                        circleColorActive={Colors.primaryColor}
                        circleColorInactive={Colors.fieldtTxtColor}
                        backgroundActive="#dddddd"
                        value={item.isDepo}
                      />
                    )}
                    <Text
                      style={[
                        {
                          color: item.isDepo ? Colors.primaryColor : Colors.tabRed,
                          fontSize: 16,
                          fontFamily: 'NunitoSans-Bold',
                          marginTop: 2,
                        },
                        switchMerchant
                          ? {
                            fontSize: 10,
                          }
                          : {},
                      ]}>
                      {item.isDepo ? 'Active' : 'Disabled'}
                    </Text>
                  </>
                  :
                  <View style={{
                    // backgroundColor: 'blue',
                    alignItems: 'flex-start',
                  }}>
                    <ActivityIndicator color={Colors.primaryColor} size={switchMerchant ? 'small' : 'large'} />
                  </View>
              }
            </View>

            <View
              style={{
                width: '5%',
                marginHorizontal: 0.5,
                alignItems: 'flex-start',
              }}>
              <View
                style={switchMerchant ? {
                  width: '100%',
                  flexDirection: 'row',
                  marginHorizontal: 0.5,
                  justifyContent: 'center',
                  alignItems: 'center',
                  right: windowWidth * 0.015
                } : {
                  width: '100%',
                  flexDirection: 'row',
                  marginHorizontal: 0.5,
                  justifyContent: 'center',
                  alignItems: 'center',
                }}>
                <TouchableOpacity
                  onPress={() => {
                    setSelectedPaymentMethod(item);

                    setImage(item.image);
                    setImageType('');
                    setIsImageChanged(false);

                    setPaymentName(item.name);
                    setPartnerType(item.deliveryPartner ? item.deliveryPartner : '')

                    setAddPaymentMethodModal(true);
                  }}>
                  {switchMerchant ? (
                    <FontAwesome5
                      name="edit"
                      size={13}
                      color={Colors.primaryColor}
                    />
                  ) : (
                    <FontAwesome5
                      name="edit"
                      size={23}
                      color={Colors.primaryColor}
                    />
                  )}
                </TouchableOpacity>
              </View>
            </View>
            <View
              style={{
                width: '5%',
                marginHorizontal: 0.5,
                alignItems: 'flex-start',
              }}>
              <View
                style={switchMerchant ? {
                  width: '100%',
                  flexDirection: 'row',
                  marginHorizontal: 0.5,
                  justifyContent: 'center',
                  alignItems: 'center',
                  right: windowWidth * 0.015
                } : {
                  width: '100%',
                  flexDirection: 'row',
                  marginHorizontal: 0.5,
                  justifyContent: 'center',
                  alignItems: 'center',
                }}>
                <TouchableOpacity
                  onPress={() => {
                    Alert.alert(
                      'Info',
                      'Are you sure you want to remove this payment method?',
                      [
                        {
                          text: 'YES', onPress: () => {
                            deletePaymentMethod(item.uniqueId)
                          }
                        },
                        { text: 'NO', onPress: () => { } },
                      ],
                      { cancelable: true },
                    );

                  }}>
                  {switchMerchant ? (
                    <Icon
                      name="trash-2"
                      size={13}
                      color="#eb3446"
                    />
                  ) : (
                    <Icon
                      name="trash-2"
                      size={23}
                      color="#eb3446"
                    />
                  )}
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </View>
      </View>
    );
  };
  const renderDefaultPaymentMethod = ({ item, index }) => {
    var imageWidth = 100;

    if (
      // item.value == OFFLINE_PAYMENT_METHOD_TYPE.CREDIT_CARD ||
      // item.value == OFFLINE_PAYMENT_METHOD_TYPE.DEBIT_CARD ||
      item.value == OFFLINE_PAYMENT_METHOD_TYPE.VISA ||
      item.value == OFFLINE_PAYMENT_METHOD_TYPE.MASTERCARD ||
      item.value == OFFLINE_PAYMENT_METHOD_TYPE.AMEX ||
      // item.value == OFFLINE_PAYMENT_METHOD_TYPE.BANK_TRANSFER
      item.value == OFFLINE_PAYMENT_METHOD_TYPE.MAYBANK_QRPAY
    ) {
      imageWidth = 70;
    } else if (
      item.value == OFFLINE_PAYMENT_METHOD_TYPE.EPAY ||
      // item.value == OFFLINE_PAYMENT_METHOD_TYPE.MAYBANK_QRPAY ||
      item.value == OFFLINE_PAYMENT_METHOD_TYPE.RAZER_PAY ||
      item.value == OFFLINE_PAYMENT_METHOD_TYPE.RAZER_CASH ||
      item.value == OFFLINE_PAYMENT_METHOD_TYPE.WEBCASH ||
      item.value == OFFLINE_PAYMENT_METHOD_TYPE.CASH ||
      item.value == OFFLINE_PAYMENT_METHOD_TYPE.BANK_TRANSFER
    ) {
      imageWidth = 50;
    }

    if (item.paymentMethodId) {
      imageWidth = 50;
    }
    return (
      <View
        style={{
          paddingVertical: 5,
          shadowOffset: {
            width: 0,
            height: 2,
          },
          shadowOpacity: 0.22,
          shadowRadius: 3.22,
          elevation: 1,
        }}>
        <View
          style={[
            { elevation: 1, borderRadius: 7, backgroundColor: 'white' },
            switchMerchant
              ? {
                //width: windowWidth * 0.8,
              }
              : {},
          ]}>
          <View
            style={[
              {
                width: '100%',
                flexDirection: 'row',
                height: windowHeight * 0.1,
                alignItems: 'center',
                borderBottomColor: Colors.fieldtT,
                // borderBottomWidth: expandViewDict[item.uniqueId] == true ? StyleSheet.hairlineWidth : null
              },
              switchMerchant
                ? {
                  //width: windowWidth * 0.82,
                  // height: windowHeight * 0.1,
                }
                : {},
            ]}>
            <View
              style={[
                {
                  width: '15%',
                  marginLeft: '2.5%',
                  //marginHorizontal: 0.5,
                  alignItems: 'flex-start',
                },
                switchMerchant
                  ? {
                    width: '15%',
                    //left: '10%',
                    marginLeft: '2.5%',
                    alignItems: 'flex-start',
                  }
                  : {},
              ]}>
              {item.paymentMethodId ? (
                <AsyncImage
                  source={{
                    uri: item.image,
                  }}
                  item={item}
                  style={{
                    resizeMode: 'contain',
                    width: imageWidth,
                    height: 30,
                    // backgroundColor: 'blue',
                  }}
                />
              ) : (
                <Image
                  source={item.image}
                  style={{
                    resizeMode: 'contain',
                    width: imageWidth,
                    height: 30,
                    // backgroundColor: 'blue',
                  }}
                />
              )}
            </View>

            <View
              style={[
                {
                  width: '18%',
                  flexDirection: 'row',
                  //marginHorizontal: 0.5,
                  alignItems: 'flex-start',
                },
                switchMerchant
                  ? {
                    width: '18%',
                    alignItems: 'flex-start',
                    // left: '10%'
                    marginHorizontal: 0.5,
                  }
                  : {},
              ]}>
              <View
                style={{
                  flexDirection: 'row',
                  //marginHorizontal: 0.5,
                  width: Platform.OS == 'ios' ? '60%' : '60%',
                }}>
                <Text
                  style={[
                    {
                      color: Colors.fontDark,
                      fontSize: 16,
                      fontFamily: 'NunitoSans-Bold',
                      textAlign: 'left',
                    },
                    switchMerchant
                      ? {
                        fontSize: 10,
                      }
                      : {},
                  ]}>
                  {item.name}
                </Text>
              </View>
            </View>


            <View
              style={[
                {
                  width: '24%',
                  //marginHorizontal: 0.5,
                  textAlign: 'left',
                },
                switchMerchant
                  ? {
                    width: '24%',
                    // left: '10%'
                  }
                  : {},
              ]}>
              {
                !loadingDict[item.uniqueId]
                  ?
                  <>
                    {switchMerchant ? (
                      <Switch
                        //value={currOutlet.queueStatus}
                        onSyncPress={(value) => {
                          // setState({ newReservationStatus: item }, function  = () => {
                          //   switchQueueStatus();
                          // });

                          //togglePaymentMethod(item.uniqueId, value);
                          setPaymentMethodsHidden({
                            ...paymentMethodsHidden,
                            // [item.value.channel]: paymentMethodsHidden[item.value.channel] ? false : true,
                            [item.value.channel]: value,
                          });
                        }}
                        width={20}
                        height={10}
                        circleColorActive={Colors.primaryColor}
                        circleColorInactive={Colors.fieldtTxtColor}
                        backgroundActive="#dddddd"
                        // value={item.isActive}
                        value={paymentMethodsHidden[item.value.channel]}
                      />
                    ) : (

                      <Switch
                        //value={currOutlet.queueStatus}
                        onSyncPress={(value) => {
                          // setState({ newReservationStatus: item }, function  = () => {
                          //   switchQueueStatus();
                          // });

                          //togglePaymentMethod(item.uniqueId, value);
                          setPaymentMethodsHidden({
                            ...paymentMethodsHidden,
                            // [item.value.channel]: paymentMethods[item.value.channel] ? false : true,
                            [item.value.channel]: value,
                          });
                        }}
                        width={42}
                        // circleColorActive={Colors.primaryColor}
                        // circleColorInactive={Colors.fieldtTxtColor}
                        circleColorActive={Colors.fieldtTxtColor}
                        circleColorInactive={Colors.primaryColor}
                        backgroundActive="#dddddd"
                        // value={item.isActive}
                        value={paymentMethodsHidden[item.value.channel]}
                      />
                    )}
                    <Text
                      style={[
                        {
                          color: paymentMethodsHidden[item.value.channel] ? Colors.fieldtTxtColor : Colors.primaryColor,
                          fontSize: 16,
                          fontFamily: 'NunitoSans-Bold',
                          marginTop: 2,
                        },
                        switchMerchant
                          ? {
                            fontSize: 10,
                          }
                          : {},
                      ]}>
                      {paymentMethodsHidden[item.value.channel] ? 'Hidden' : 'Show'}
                    </Text>
                  </>
                  :
                  <View style={{
                    // backgroundColor: 'blue',
                    alignItems: 'flex-start',
                  }}>
                    <ActivityIndicator color={Colors.primaryColor} size={switchMerchant ? 'small' : 'large'} />
                  </View>
              }
            </View>
          </View>
        </View>
      </View>
    );
  };


  // function end

  return (
    <UserIdleWrapper disabled={!isMounted}>
      <View
        style={[
          styles.container,
          !isTablet()
            ? {
              transform: [{ scaleX: 1 }, { scaleY: 1 }],
            }
            : {},
          {
            ...getTransformForScreenInsideNavigation(),
          }
        ]}>
        {/* <View
          style={[
            styles.sidebar,
            !isTablet()
              ? {
                width: windowWidth * 0.08,
              }
              : {},
            {
              width: windowWidth * 0.08,
            }
          ]}>
          <SideBar
            navigation={props.navigation}
            selectedTab={1}
            expandOperation
          />
        </View> */}

        {/* New UX */}
        {/* <ScrollView scrollEnabled={switchMerchant} horizontal={true}> */}
        <ScrollView
          showsVerticalScrollIndicator={false}
          showsHorizontalScrollIndicator={false}
          // scrollEnabled={switchMerchant}
          style={{ backgroundColor: Colors.highlightColor }}
          contentContainerStyle={{
            paddingBottom: windowHeight * 0.1,
            backgroundColor: Colors.highlightColor,
          }}>
          <ScrollView horizontal>
            <View style={[styles.content, {
              width: Dimensions.get('window').width * (1 - Styles.sideBarWidth),
            }]}>
              <View
                style={[{
                  flexDirection: 'row',
                  alignItems: 'center',
                  padding: switchMerchant ? 0 : 2,
                  width: '100%',
                  justifyContent: 'space-between',
                }, switchMerchant ? {
                  marginBottom: windowHeight * 0.03,
                  // borderWidth: 1,
                  //marginTop: windowHeight * -0.03
                } : {}]}>
                <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                  <Text
                    style={[
                      { fontSize: 26, fontFamily: 'NunitoSans-Bold' },
                      switchMerchant
                        ? {
                          // fontSize: 15,
                          //follow dashboard
                          fontSize: 20,
                          // borderWidth: 1,
                          // top: windowHeight * -0.05,
                        }
                        : {},
                    ]}>
                    {methodSection === METHOD_SECTION.CUSTOM ? `${outletPaymentMethods.length} Payment Method` : `${paymentMethods.length} Payment Method`}
                  </Text>
                </View>
                <View
                  style={[
                    { flexDirection: 'row', alignItems: 'center' },
                    switchMerchant
                      ? {
                        //left: windowWidth * -0.05,
                      }
                      : {},
                  ]}>
                  <TouchableOpacity
                    style={[
                      styles.submitText,
                      {
                        flexDirection: 'row',
                        justifyContent: 'center',
                        alignItems: 'center',
                        borderRadius: 10,
                        height: 40,
                        left: 0,
                        backgroundColor: '#0A1F44',
                        borderWidth: 1,
                        borderColor: Colors.primaryColor,
                        shadowOffset: {
                          width: 0,
                          height: 2,
                        },
                        shadowOpacity: 0.22,
                        shadowRadius: 3.22,
                      },
                      switchMerchant
                        ? {
                          // top: windowHeight * -0.046,
                          height: 35,
                          //width: windowWidth * 0.12,
                        }
                        : {},
                    ]}
                    onPress={() => {
                      setSelectedPaymentMethod({});

                      setImage('');
                      setImageType('');
                      setIsImageChanged(false);

                      setPaymentName('');

                      setAddPaymentMethodModal(true);
                    }}>
                    <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                      {switchMerchant ? (
                        <AntDesign name="pluscircle" size={10} color="#FFFFFF" />
                      ) : (
                        <AntDesign name="pluscircle" size={20} color="#FFFFFF" />
                      )}

                      <Text
                        style={[
                          {
                            marginLeft: 5,
                            color: Colors.primaryColor,
                            fontSize: 16,
                            color: '#FFFFFF',
                            fontFamily: 'NunitoSans-Bold',
                          },
                          switchMerchant
                            ? {
                              fontSize: 10,
                              // paddingLeft: '10%',
                            }
                            : {},
                        ]}>
                        PAYMENT METHOD
                      </Text>
                    </View>
                  </TouchableOpacity>

                  {/* <View style={{ flexDirection: 'row', justifyContent: 'center', alignItems: 'center', borderRadius: 10, height: 40, }}>
              
                  <View style={{
                    flexDirection: 'row',
                    justifyContent: 'center',
                    alignItems: 'center',
                    paddingLeft: 10,
                    borderRadius: 5,
                    height: 40,
                    borderRadius: 5,
                    borderWidth: 1,
                    borderColor: '#E5E5E5',
                    backgroundColor: 'white',
                    marginRight: 15,
                    shadowColor: '#000',
                    shadowOffset: {
                      width: 0,
                      height: 2,
                    },
                    shadowOpacity: 0.22,
                    shadowRadius: 3.22,
                    elevation: 3,
  
                  }}>
                    <Text style={{ fontSize: 16, paddingRight: Platform.OS == 'ios' ? 20 : 20, borderColor: Colors.fieldtTxtColor, fontFamily: 'NunitoSans-Bold' }}>
                      Filter
                    </Text>
                    <DropDownPicker
                      controller={instance => setController1(instance)}
                      arrowColor={Colors.primaryColor}
                      arrowSize={23}
                      arrowStyle={{ fontWeight: 'bold' }}
                      labelStyle={{ fontFamily: 'NunitoSans-Regular' }}
                      itemStyle={{ justifyContent: 'flex-start', marginLeft: 5 }}
                      placeholderStyle={{ color: 'black' }}
                      style={{ width: 140, borderWidth: 0, paddingHorizontal: 5, paddingVertical: 0, borderRadius: 5, borderColor: '#E5E5E5', borderWidth: 0, borderLeftWidth: 0, }}
                      items={[{ label: 'Pending', value: 0 }, { label: 'Accepted', value: 1 }, { label: 'Seated', value: 2 }, { label: 'Served', value: 3 }, { label: 'Rejected', value: 4 }, { label: 'No Show', value: 5 }]} //Awaiting Authorization
                      placeholder={"Pending"}
                      onChangeItem={selectedFilter => {
                        filterOrders(selectedFilter);
                      }
                      }
                    />
                  </View>
                </View> */}

                  {/* <View
                style={[
                  {
                    flexDirection: 'row',
                    justifyContent: 'center',
                    alignItems: 'center',
                    paddingLeft: 10,
                    borderRadius: 5,
                    height: 40,
                    borderRadius: 5,
                    borderWidth: 1,
                    borderColor: '#E5E5E5',
                    backgroundColor: 'white',
                    marginRight: 15,
                    shadowColor: '#000',
                    shadowOffset: {
                      width: 0,
                      height: 2,
                    },
                    shadowOpacity: 0.22,
                    shadowRadius: 3.22,
                    // elevation: 3,
                  },
                  switchMerchant
                    ? {
                        // top: windowHeight * -0.046,
                        width: windowWidth * 0.21,
                        height: 35,
                        marginRight: '5%',
                        // left: windowWidth * -0.12,
                      }
                    : {},
                ]}>
                <Text
                  style={[
                    {
                      fontSize: 16,
                      paddingLeft: '1%',
                      paddingRight: Platform.OS == 'ios' ? 20 : 20,
                      borderColor: Colors.fieldtTxtColor,
                      fontFamily: 'NunitoSans-Bold',
                    },
                    switchMerchant
                      ? {
                          fontSize: 10,
                          // left: windowWidth * -0.015,
                          left: '30%',
                          // backgroundColor: 'white',
                          // height: '100%',
                          // left: '-20%',
                          // borderRadius: 5,
                          // textAlignVertical: 'center'
                        }
                      : {},
                  ]}>
                  Filter
                </Text>
                <DropDownPicker
                  // controller={instance => setController1(instance)}
                  arrowColor={Colors.primaryColor}
                  arrowSize={switchMerchant? 13 : 23}
                  arrowStyle={[
                    {fontWeight: 'bold'},
                    switchMerchant
                      ? {
                          top: windowHeight * -0.005,
                          height: '200%',
                          // borderWidth: 1
                        }
                      : {},
                  ]}
                  labelStyle={[
                    {fontFamily: 'NunitoSans-Regular'},
                    switchMerchant
                      ? {
                          fontSize: 10,
                        }
                      : {},
                  ]}
                  itemStyle={[
                    {justifyContent: 'flex-start', marginLeft: 6},
                    switchMerchant
                      ? {
                          fontSize: 10,
                        }
                      : {},
                  ]}
                  placeholderStyle={[
                    {color: 'black'},
                    switchMerchant
                      ? {
                          fontSize: 10,
                        }
                      : {},
                  ]}
                  style={[
                    {
                      width: 140,
                      borderWidth: 0,
                      paddingHorizontal: 5,
                      paddingVertical: 0,
                      borderRadius: 5,
                      borderColor: '#E5E5E5',
                      borderWidth: 0,
                      borderLeftWidth: 0,
                    },
                    switchMerchant
                      ? {
                          fontSize: 10,
                          // width: windowWidth * 0.12,
                        }
                      : {},
                  ]}
                  items={[
                    {label: 'All', value: 0},
                    {label: 'Pending', value: 1},
                    {label: 'Seated', value: 3},
                    {label: 'Served', value: 4},
                    {label: 'No Show', value: 6},
                  ]} //Awaiting Authorization
                  // placeholder={"Pending"}
                  defaultValue={filterType}
                  onChangeItem={(selectedFilter) => {
                    // filterOrders(selectedFilter);
                    setFilterType(selectedFilter.value);
                  }}
                  //onOpen={() => controller.close()}
                />
              </View> */}

                  <View
                    style={[
                      {
                        height: switchMerchant ? 35 : 40,
                      },
                      !isTablet()
                        ? {
                          marginLeft: 0,
                        }
                        : {},
                    ]}>
                    <View
                      style={[
                        {
                          width: 250,
                          height: 40,
                          backgroundColor: 'white',
                          borderRadius: 5,
                          flexDirection: 'row',
                          alignContent: 'center',
                          alignItems: 'center',
                          shadowColor: '#000',
                          shadowOffset: {
                            width: 0,
                            height: 2,
                          },
                          shadowOpacity: 0.22,
                          shadowRadius: 3.22,
                          elevation: 3,
                          borderWidth: 1,
                          borderColor: '#E5E5E5',
                        },
                        switchMerchant
                          ? {
                            height: 35,
                            width: 200,
                            // left: windowWidth * -0.05,
                            // top: windowHeight * -0.03,
                          }
                          : {},
                      ]}>
                      {switchMerchant ? (
                        <Icon
                          name="search"
                          size={13}
                          color={Colors.primaryColor}
                          style={{ marginLeft: 15 }}
                        />
                      ) : (
                        <Icon
                          name="search"
                          size={18}
                          color={Colors.primaryColor}
                          style={{ marginLeft: 15 }}
                        />
                      )}
                      {switchMerchant ? (
                        <TextInput
                          // underlineColorAndroid={Colors.whiteColor}
                          style={[
                            {
                              width: 170,
                              fontSize: 15,
                              fontFamily: 'NunitoSans-Regular',
                              paddingLeft: 5,
                              height: 45
                            },
                            switchMerchant
                              ? {
                                fontSize: 10,
                                // borderWidth:1,
                                // width: windowWidth * 0.17,
                                height: 35,
                              }
                              : {},
                          ]}
                          clearButtonMode="while-editing"
                          placeholder=" Search"
                          placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                          onChangeText={(text) => {
                            setSearch(text);
                            // setSearch(text.trim());
                          }}
                        // value={search}
                        />
                      ) : (
                        <TextInput
                          underlineColorAndroid={Colors.whiteColor}
                          style={{
                            width: 220,
                            fontSize: 15,
                            fontFamily: 'NunitoSans-Regular',
                            paddingLeft: 5,
                            height: 45
                          }}
                          clearButtonMode="while-editing"
                          placeholder=" Search"
                          placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                          onChangeText={(text) => {
                            setSearch(text);
                            // setSearch(text.trim());
                          }}
                        // value={search}
                        />
                      )}
                    </View>
                  </View>
                </View>
              </View>
              <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', paddingVertical: 15, marginTop: isTablet() && Dimensions.get('screen').width < 1350 && Dimensions.get('screen').height < 670 ? 10 : 10, height: 40, }}>
                <View style={{ flexDirection: 'row', }}>
                  <TouchableOpacity
                    style={{
                      justifyContent: 'center',
                      flexDirection: 'row',
                      borderWidth: 1,
                      borderColor: Colors.primaryColor,
                      backgroundColor: '#0A1F44',
                      borderRadius: 10,
                      //width: 120,
                      paddingHorizontal: 20,
                      // paddingVertical: 10,
                      height: 40,
                      alignItems: 'center',
                      shadowOffset: {
                        width: 0,
                        height: 2,
                      },
                      shadowOpacity: 0.22,
                      shadowRadius: 3.22,
                      elevation: 1,
                      zIndex: -1,
                      marginRight: 10,

                      // marginVertical: 10,
                    }}
                    onPress={() => {
                      if (methodSection === METHOD_SECTION.DEFAULT) {
                        setMethodSection(METHOD_SECTION.CUSTOM);
                      }
                      else {
                        setMethodSection(METHOD_SECTION.DEFAULT);
                      }
                    }}>
                    <Text
                      style={[
                        {
                          color: Colors.whiteColor,
                          //marginLeft: 5,
                          fontSize: 16,
                          fontFamily: 'NunitoSans-Bold',
                        },
                        switchMerchant
                          ? {
                            fontSize: 10,
                          }
                          : {},
                      ]}>{methodSection === METHOD_SECTION.CUSTOM ? 'DEFAULT METHODS' : 'CUSTOM METHODS'}</Text>
                  </TouchableOpacity>
                  {/* <TouchableOpacity
                  style={{
                    justifyContent: 'center',
                    flexDirection: 'row',
                    borderWidth: 1,
                    borderColor: Colors.primaryColor,
                    backgroundColor: '#0A1F44',
                    borderRadius: 5,
                    //width: 120,
                    paddingHorizontal: 10,
                    height: 40,
                    alignItems: 'center',
                    shadowOffset: {
                      width: 0,
                      height: 2,
                    },
                    shadowOpacity: 0.22,
                    shadowRadius: 3.22,
                    elevation: 1,
                    zIndex: -1,
                  }}
                  onPress={() => {
                    setMethodSection(METHOD_SECTION.DEFAULT);
                  }}>
                  <Text
                    style={[
                      {
                        color: Colors.whiteColor,
                        //marginLeft: 5,
                        fontSize: 16,
                        fontFamily: 'NunitoSans-Bold',
                      },
                      switchMerchant
                        ? {
                          fontSize: 10,
                        }
                        : {},
                    ]}>Default Methods</Text>
                </TouchableOpacity> */}
                </View>
                {methodSection === METHOD_SECTION.DEFAULT ?
                  <TouchableOpacity
                    style={{
                      justifyContent: 'center',
                      flexDirection: 'row',
                      borderWidth: 1,
                      borderColor: Colors.primaryColor,
                      backgroundColor: '#0A1F44',
                      borderRadius: 10,
                      width: 120,
                      paddingHorizontal: 10,
                      height: 40,
                      alignItems: 'center',
                      shadowOffset: {
                        width: 0,
                        height: 2,
                      },
                      shadowOpacity: 0.22,
                      shadowRadius: 3.22,
                      elevation: 1,
                      zIndex: -1,
                    }}
                    onPress={() => {
                      updateOutletPaymentMethodsHidden();
                    }}>
                    <Text
                      style={[
                        {
                          color: Colors.whiteColor,
                          //marginLeft: 5,
                          fontSize: 16,
                          fontFamily: 'NunitoSans-Bold',
                        },
                        switchMerchant
                          ? {
                            fontSize: 10,
                          }
                          : {},
                      ]}>
                      SAVE
                    </Text>
                  </TouchableOpacity>
                  : null}
              </View>
              {methodSection === METHOD_SECTION.CUSTOM ?
                <View
                  style={[
                    { marginTop: 15, marginBottom: 50, zIndex: -1 },
                    switchMerchant
                      ? {
                        // borderWidth: 1,
                        //width: windowWidth * 0.79,
                        marginTop: 5,
                        marginBottom: windowHeight * 0.23,
                      }
                      : {},
                  ]}>
                  <View
                    style={{ width: '100%', flexDirection: 'row', alignItems: 'center' }}>
                    <View
                      style={{
                        marginLeft: '2.5%',
                        width: '8%',
                        alignItems: 'flex-start',
                      }}>
                      <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                        <View style={{ marginHorizontal: 0.5 }}>
                          <TouchableOpacity
                            onPress={() => {
                              if (sort === QUEUE_SORT_FIELD_TYPE.QUEUE_ID_ASC) {
                                setSort(QUEUE_SORT_FIELD_TYPE.QUEUE_ID_DESC);
                              } else {
                                setSort(QUEUE_SORT_FIELD_TYPE.QUEUE_ID_ASC);
                              }
                            }}>
                            <Text
                              style={[
                                {
                                  color: 'black',
                                  fontFamily: 'NunitoSans-Regular',
                                },
                                switchMerchant
                                  ? {
                                    fontSize: 10,
                                  }
                                  : {},
                              ]} />
                          </TouchableOpacity>
                        </View>
                        <View style={{ opacity: 0 }}>
                          <TouchableOpacity
                            onPress={() => {
                              setSort(QUEUE_SORT_FIELD_TYPE.QUEUE_ID_DESC);
                            }}>
                            {switchMerchant ? (
                              <Entypo
                                name="triangle-up"
                                size={8}
                                color={
                                  sort === QUEUE_SORT_FIELD_TYPE.QUEUE_ID_ASC
                                    ? Colors.secondaryColor
                                    : Colors.descriptionColor
                                } />
                            ) : (
                              <Entypo
                                name="triangle-up"
                                size={14}
                                color={
                                  sort === QUEUE_SORT_FIELD_TYPE.QUEUE_ID_ASC
                                    ? Colors.secondaryColor
                                    : Colors.descriptionColor
                                } />
                            )}
                          </TouchableOpacity>
                          <TouchableOpacity
                            onPress={() => {
                              setSort(QUEUE_SORT_FIELD_TYPE.QUEUE_ID_ASC);
                            }}>
                            {switchMerchant ? (
                              <Entypo
                                name="triangle-down"
                                size={8}
                                color={
                                  sort === QUEUE_SORT_FIELD_TYPE.QUEUE_ID_DESC
                                    ? Colors.secondaryColor
                                    : Colors.descriptionColor
                                } />
                            ) : (
                              <Entypo
                                name="triangle-down"
                                size={14}
                                color={
                                  sort === QUEUE_SORT_FIELD_TYPE.QUEUE_ID_DESC
                                    ? Colors.secondaryColor
                                    : Colors.descriptionColor
                                } />
                            )}
                          </TouchableOpacity>
                        </View>
                      </View>
                    </View>

                    {/* ///////////////////////////Name/////////////////////// */}
                    <View style={{ width: '18%', alignItems: 'flex-start' }}>
                      <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                        <View style={{ marginHorizontal: 0.5 }}>
                          <TouchableOpacity
                            onPress={() => {
                              if (sort === PAYMENT_SORT_FIELD_TYPE.PAYMENT_NAME_ASC) {
                                setSort(PAYMENT_SORT_FIELD_TYPE.PAYMENT_NAME_DESC);
                              } else {
                                setSort(PAYMENT_SORT_FIELD_TYPE.PAYMENT_NAME_ASC);
                              }
                            }}>
                            <Text
                              style={[
                                {
                                  color: 'black',
                                  fontFamily: 'NunitoSans-Regular',
                                },
                                switchMerchant
                                  ? {
                                    fontSize: 10,
                                  }
                                  : {},
                              ]}>
                              Name
                            </Text>
                          </TouchableOpacity>
                        </View>
                        <View style={{}}>
                          <TouchableOpacity
                            onPress={() => {
                              setSort(PAYMENT_SORT_FIELD_TYPE.PAYMENT_NAME_DESC);
                            }}>
                            {switchMerchant ? (
                              <Entypo
                                name="triangle-up"
                                size={8}
                                color={
                                  sort === PAYMENT_SORT_FIELD_TYPE.PAYMENT_NAME_ASC
                                    ? Colors.secondaryColor
                                    : Colors.descriptionColor
                                } />
                            ) : (
                              <Entypo
                                name="triangle-up"
                                size={14}
                                color={
                                  sort === PAYMENT_SORT_FIELD_TYPE.PAYMENT_NAME_ASC
                                    ? Colors.secondaryColor
                                    : Colors.descriptionColor
                                } />
                            )}
                          </TouchableOpacity>
                          <TouchableOpacity
                            onPress={() => {
                              setSort(QUEUE_SORT_FIELD_TYPE.NAME_ASC);
                            }}>
                            {switchMerchant ? (
                              <Entypo
                                name="triangle-down"
                                size={8}
                                color={
                                  sort === PAYMENT_SORT_FIELD_TYPE.PAYMENT_NAME_DESC
                                    ? Colors.secondaryColor
                                    : Colors.descriptionColor
                                } />
                            ) : (
                              <Entypo
                                name="triangle-down"
                                size={14}
                                color={
                                  sort === PAYMENT_SORT_FIELD_TYPE.PAYMENT_NAME_DESC
                                    ? Colors.secondaryColor
                                    : Colors.descriptionColor
                                } />
                            )}
                          </TouchableOpacity>
                        </View>
                      </View>
                    </View>

                    {/* ///////////////////////////Date Time/////////////////////// */}
                    <View style={{ width: '18%', alignItems: 'flex-start' }}>
                      <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                        <View
                          style={{
                            marginHorizontal: 0.5,
                            left: Platform.OS === 'android' ? '1%' : 0,
                          }}>
                          <TouchableOpacity
                            onPress={() => {
                              if (sort === PAYMENT_SORT_FIELD_TYPE.PAYMENT_CREATED_DATE_ASC) {
                                setSort(PAYMENT_SORT_FIELD_TYPE.PAYMENT_CREATED_DATE_DESC);
                              } else {
                                setSort(PAYMENT_SORT_FIELD_TYPE.PAYMENT_CREATED_DATE_ASC);
                              }
                            }}>
                            <Text
                              style={[
                                {
                                  color: 'black',
                                  fontFamily: 'NunitoSans-Regular',
                                },
                                switchMerchant
                                  ? {
                                    fontSize: 10,
                                  }
                                  : {},
                              ]}>
                              Created Date
                            </Text>
                          </TouchableOpacity>
                        </View>
                        <View style={{}}>
                          <TouchableOpacity
                            onPress={() => {
                              setSort(PAYMENT_SORT_FIELD_TYPE.PAYMENT_CREATED_DATE_ASC);
                            }}>
                            {switchMerchant ? (
                              <Entypo
                                name="triangle-up"
                                size={8}
                                color={
                                  sort === PAYMENT_SORT_FIELD_TYPE.PAYMENT_CREATED_DATE_ASC
                                    ? Colors.secondaryColor
                                    : Colors.descriptionColor
                                } />
                            ) : (
                              <Entypo
                                name="triangle-up"
                                size={14}
                                color={
                                  sort === PAYMENT_SORT_FIELD_TYPE.PAYMENT_CREATED_DATE_ASC
                                    ? Colors.secondaryColor
                                    : Colors.descriptionColor
                                } />
                            )}
                          </TouchableOpacity>
                          <TouchableOpacity
                            onPress={() => {
                              setSort(PAYMENT_SORT_FIELD_TYPE.PAYMENT_CREATED_DATE_ASC);
                            }}>
                            {switchMerchant ? (
                              <Entypo
                                name="triangle-down"
                                size={8}
                                color={
                                  sort === PAYMENT_SORT_FIELD_TYPE.PAYMENT_CREATED_DATE_DESC
                                    ? Colors.secondaryColor
                                    : Colors.descriptionColor
                                } />
                            ) : (
                              <Entypo
                                name="triangle-down"
                                size={14}
                                color={
                                  sort === PAYMENT_SORT_FIELD_TYPE.PAYMENT_CREATED_DATE_DESC
                                    ? Colors.secondaryColor
                                    : Colors.descriptionColor
                                } />
                            )}
                          </TouchableOpacity>
                        </View>
                      </View>
                    </View>

                    <View style={{ width: '18%', alignItems: 'flex-start' }}>
                      <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                        <View
                          style={{
                            marginHorizontal: 0.5,
                            left: Platform.OS === 'android' ? '1%' : 0,
                          }}>
                          <TouchableOpacity
                            onPress={() => {
                              if (sort === PAYMENT_SORT_FIELD_TYPE.PAYMENT_UPDATED_DATE_ASC) {
                                setSort(PAYMENT_SORT_FIELD_TYPE.PAYMENT_UPDATED_DATE_DESC);
                              } else {
                                setSort(PAYMENT_SORT_FIELD_TYPE.PAYMENT_UPDATED_DATE_ASC);
                              }
                            }}>
                            <Text
                              style={[
                                {
                                  color: 'black',
                                  fontFamily: 'NunitoSans-Regular',
                                },
                                switchMerchant
                                  ? {
                                    fontSize: 10,
                                  }
                                  : {},
                              ]}>
                              Modified Date
                            </Text>
                          </TouchableOpacity>
                        </View>
                        <View style={{}}>
                          <TouchableOpacity
                            onPress={() => {
                              setSort(PAYMENT_SORT_FIELD_TYPE.PAYMENT_UPDATED_DATE_DESC);
                            }}>
                            {switchMerchant ? (
                              <Entypo
                                name="triangle-up"
                                size={8}
                                color={
                                  sort === PAYMENT_SORT_FIELD_TYPE.PAYMENT_UPDATED_DATE_ASC
                                    ? Colors.secondaryColor
                                    : Colors.descriptionColor
                                } />
                            ) : (
                              <Entypo
                                name="triangle-up"
                                size={14}
                                color={
                                  sort === PAYMENT_SORT_FIELD_TYPE.PAYMENT_UPDATED_DATE_ASC
                                    ? Colors.secondaryColor
                                    : Colors.descriptionColor
                                } />
                            )}
                          </TouchableOpacity>
                          <TouchableOpacity
                            onPress={() => {
                              setSort(PAYMENT_SORT_FIELD_TYPE.PAYMENT_UPDATED_DATE_ASC);
                            }}>
                            {switchMerchant ? (
                              <Entypo
                                name="triangle-down"
                                size={8}
                                color={
                                  sort === PAYMENT_SORT_FIELD_TYPE.PAYMENT_UPDATED_DATE_DESC
                                    ? Colors.secondaryColor
                                    : Colors.descriptionColor
                                } />
                            ) : (
                              <Entypo
                                name="triangle-down"
                                size={14}
                                color={
                                  sort === PAYMENT_SORT_FIELD_TYPE.PAYMENT_UPDATED_DATE_DESC
                                    ? Colors.secondaryColor
                                    : Colors.descriptionColor
                                } />
                            )}
                          </TouchableOpacity>
                        </View>
                      </View>
                    </View>

                    <View style={{ width: '12%', alignItems: 'flex-start' }}>
                      <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                        <View
                          style={{
                            marginHorizontal: 0.5,
                            left: Platform.OS === 'android' ? '1%' : 0,
                          }}>
                          <TouchableOpacity
                            onPress={() => {
                              if (sort === PAYMENT_SORT_FIELD_TYPE.PAYMENT_STATUS_ASC) {
                                setSort(PAYMENT_SORT_FIELD_TYPE.PAYMENT_STATUS_DESC);
                              } else {
                                setSort(PAYMENT_SORT_FIELD_TYPE.PAYMENT_STATUS_ASC);
                              }
                            }}>
                            <Text
                              style={[
                                {
                                  color: 'black',
                                  fontFamily: 'NunitoSans-Regular',
                                },
                                switchMerchant
                                  ? {
                                    fontSize: 10,
                                  }
                                  : {},
                              ]}>
                              Status
                            </Text>
                          </TouchableOpacity>
                        </View>
                        <View style={{}}>
                          <TouchableOpacity
                            onPress={() => {
                              setSort(PAYMENT_SORT_FIELD_TYPE.PAYMENT_STATUS_DESC);
                            }}>
                            {switchMerchant ? (
                              <Entypo
                                name="triangle-up"
                                size={8}
                                color={
                                  sort === PAYMENT_SORT_FIELD_TYPE.PAYMENT_STATUS_ASC
                                    ? Colors.secondaryColor
                                    : Colors.descriptionColor
                                } />
                            ) : (
                              <Entypo
                                name="triangle-up"
                                size={14}
                                color={
                                  sort === PAYMENT_SORT_FIELD_TYPE.PAYMENT_STATUS_ASC
                                    ? Colors.secondaryColor
                                    : Colors.descriptionColor
                                } />
                            )}
                          </TouchableOpacity>
                          <TouchableOpacity
                            onPress={() => {
                              setSort(PAYMENT_SORT_FIELD_TYPE.PAYMENT_STATUS_ASC);
                            }}>
                            {switchMerchant ? (
                              <Entypo
                                name="triangle-down"
                                size={8}
                                color={
                                  sort === PAYMENT_SORT_FIELD_TYPE.PAYMENT_STATUS_DESC
                                    ? Colors.secondaryColor
                                    : Colors.descriptionColor
                                } />
                            ) : (
                              <Entypo
                                name="triangle-down"
                                size={14}
                                color={
                                  sort === PAYMENT_SORT_FIELD_TYPE.PAYMENT_STATUS_DESC
                                    ? Colors.secondaryColor
                                    : Colors.descriptionColor
                                } />
                            )}
                          </TouchableOpacity>
                        </View>
                      </View>
                    </View>

                    <View style={{ width: '12%', alignItems: 'flex-start' }}>
                      <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                        <View
                          style={{
                            marginHorizontal: 0.5,
                            left: Platform.OS === 'android' ? '1%' : 0,
                          }}>
                          <TouchableOpacity
                            onPress={() => {
                              if (sort === PAYMENT_SORT_FIELD_TYPE.PAYMENT_DEPOSIT_ASC) {
                                setSort(PAYMENT_SORT_FIELD_TYPE.PAYMENT_DEPOSIT_DESC);
                              } else {
                                setSort(PAYMENT_SORT_FIELD_TYPE.PAYMENT_DEPOSIT_ASC);
                              }
                            }}>
                            <Text
                              style={[
                                {
                                  color: 'black',
                                  fontFamily: 'NunitoSans-Regular',
                                },
                                switchMerchant
                                  ? {
                                    fontSize: 10,
                                  }
                                  : {},
                              ]}>
                              Deposit
                            </Text>
                          </TouchableOpacity>
                        </View>
                        <View style={{}}>
                          <TouchableOpacity
                            onPress={() => {
                              setSort(PAYMENT_SORT_FIELD_TYPE.PAYMENT_DEPOSIT_DESC);
                            }}>
                            {switchMerchant ? (
                              <Entypo
                                name="triangle-up"
                                size={8}
                                color={
                                  sort === PAYMENT_SORT_FIELD_TYPE.PAYMENT_DEPOSIT_ASC
                                    ? Colors.secondaryColor
                                    : Colors.descriptionColor
                                } />
                            ) : (
                              <Entypo
                                name="triangle-up"
                                size={14}
                                color={
                                  sort === PAYMENT_SORT_FIELD_TYPE.PAYMENT_DEPOSIT_ASC
                                    ? Colors.secondaryColor
                                    : Colors.descriptionColor
                                } />
                            )}
                          </TouchableOpacity>
                          <TouchableOpacity
                            onPress={() => {
                              setSort(PAYMENT_SORT_FIELD_TYPE.PAYMENT_DEPOSIT_ASC);
                            }}>
                            {switchMerchant ? (
                              <Entypo
                                name="triangle-down"
                                size={8}
                                color={
                                  sort === PAYMENT_SORT_FIELD_TYPE.PAYMENT_DEPOSIT_DESC
                                    ? Colors.secondaryColor
                                    : Colors.descriptionColor
                                } />
                            ) : (
                              <Entypo
                                name="triangle-down"
                                size={14}
                                color={
                                  sort === PAYMENT_SORT_FIELD_TYPE.PAYMENT_DEPOSIT_DESC
                                    ? Colors.secondaryColor
                                    : Colors.descriptionColor
                                } />
                            )}
                          </TouchableOpacity>
                        </View>
                      </View>
                    </View>


                  </View>
                  <FlatList
                    showsVerticalScrollIndicator={false}
                    // data={sortOperationQueue(userQueues, sort).filter((item) => {
                    //   if (search !== '') {
                    //     const searchLowerCase = search.toLowerCase();
                    //     if (
                    //       item.number
                    //         .toString()
                    //         .toLowerCase()
                    //         .includes(searchLowerCase) ||
                    //       item.userName.toLowerCase().includes(searchLowerCase) ||
                    //       item.createdAt
                    //         .toString()
                    //         .toLowerCase()
                    //         .includes(searchLowerCase) ||
                    //       item.pax.toString().toLowerCase().includes(searchLowerCase) ||
                    //       item.updatedAt
                    //         .toString()
                    //         .toLowerCase()
                    //         .includes(searchLowerCase) ||
                    //       item.status.toLowerCase().includes(searchLowerCase)
                    //     ) {
                    //       return true;
                    //     } else {
                    //       return false;
                    //     }
                    //   } else {
                    //     return true;
                    //   }
                    // })}
                    data={sortPaymentDataList(outletPaymentMethods, sort).filter((item) => {
                      if (search !== '') {
                        const searchLowerCase = search.toLowerCase();
                        if (
                          item.name.toLowerCase().includes(searchLowerCase)
                        ) {
                          return true;
                        } else {
                          return false;
                        }
                      } else {
                        return true;
                      }
                    })}
                    renderItem={renderPaymentMethod}
                    keyExtractor={(item, index) => String(index)}
                  />
                </View>
                :
                <View
                  style={[
                    { marginTop: 15, marginBottom: 50, zIndex: -1 },
                    switchMerchant
                      ? {
                        // borderWidth: 1,
                        //width: windowWidth * 0.79,
                        marginTop: 5,
                        marginBottom: windowHeight * 0.23,
                      }
                      : {},
                  ]}>
                  <View
                    style={{ width: '100%', flexDirection: 'row', alignItems: 'center' }}>
                    <View
                      style={{
                        marginLeft: '2.5%',
                        width: '15%',
                        alignItems: 'flex-start',
                      }}>
                      <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                        <View style={{ marginHorizontal: 0.5 }}>
                          <TouchableOpacity
                            onPress={() => {
                              if (sort === QUEUE_SORT_FIELD_TYPE.QUEUE_ID_ASC) {
                                setSort(QUEUE_SORT_FIELD_TYPE.QUEUE_ID_DESC);
                              } else {
                                setSort(QUEUE_SORT_FIELD_TYPE.QUEUE_ID_ASC);
                              }
                            }}>
                            <Text
                              style={[
                                {
                                  color: 'black',
                                  fontFamily: 'NunitoSans-Regular',
                                },
                                switchMerchant
                                  ? {
                                    fontSize: 10,
                                  }
                                  : {},
                              ]} />
                          </TouchableOpacity>
                        </View>
                        <View style={{ opacity: 0 }}>
                          <TouchableOpacity
                            onPress={() => {
                              setSort(QUEUE_SORT_FIELD_TYPE.QUEUE_ID_DESC);
                            }}>
                            {switchMerchant ? (
                              <Entypo
                                name="triangle-up"
                                size={8}
                                color={
                                  sort === QUEUE_SORT_FIELD_TYPE.QUEUE_ID_ASC
                                    ? Colors.secondaryColor
                                    : Colors.descriptionColor
                                } />
                            ) : (
                              <Entypo
                                name="triangle-up"
                                size={14}
                                color={
                                  sort === QUEUE_SORT_FIELD_TYPE.QUEUE_ID_ASC
                                    ? Colors.secondaryColor
                                    : Colors.descriptionColor
                                } />
                            )}
                          </TouchableOpacity>
                          <TouchableOpacity
                            onPress={() => {
                              setSort(QUEUE_SORT_FIELD_TYPE.QUEUE_ID_ASC);
                            }}>
                            {switchMerchant ? (
                              <Entypo
                                name="triangle-down"
                                size={8}
                                color={
                                  sort === QUEUE_SORT_FIELD_TYPE.QUEUE_ID_DESC
                                    ? Colors.secondaryColor
                                    : Colors.descriptionColor
                                } />
                            ) : (
                              <Entypo
                                name="triangle-down"
                                size={14}
                                color={
                                  sort === QUEUE_SORT_FIELD_TYPE.QUEUE_ID_DESC
                                    ? Colors.secondaryColor
                                    : Colors.descriptionColor
                                } />
                            )}
                          </TouchableOpacity>
                        </View>
                      </View>
                    </View>

                    {/* ///////////////////////////Name/////////////////////// */}
                    <View style={{ width: '18%', alignItems: 'flex-start' }}>
                      <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                        <View style={{ marginHorizontal: 0.5 }}>
                          <TouchableOpacity
                            onPress={() => {
                              if (sort === PAYMENT_SORT_FIELD_TYPE.PAYMENT_NAME_ASC) {
                                setSort(PAYMENT_SORT_FIELD_TYPE.PAYMENT_NAME_DESC);
                              } else {
                                setSort(PAYMENT_SORT_FIELD_TYPE.PAYMENT_NAME_ASC);
                              }
                            }}>
                            <Text
                              style={[
                                {
                                  color: 'black',
                                  fontFamily: 'NunitoSans-Regular',
                                },
                                switchMerchant
                                  ? {
                                    fontSize: 10,
                                  }
                                  : {},
                              ]}>
                              Name
                            </Text>
                          </TouchableOpacity>
                        </View>
                        <View style={{}}>
                          <TouchableOpacity
                            onPress={() => {
                              setSort(PAYMENT_SORT_FIELD_TYPE.PAYMENT_NAME_DESC);
                            }}>
                            {switchMerchant ? (
                              <Entypo
                                name="triangle-up"
                                size={8}
                                color={
                                  sort === PAYMENT_SORT_FIELD_TYPE.PAYMENT_NAME_ASC
                                    ? Colors.secondaryColor
                                    : Colors.descriptionColor
                                } />
                            ) : (
                              <Entypo
                                name="triangle-up"
                                size={14}
                                color={
                                  sort === PAYMENT_SORT_FIELD_TYPE.PAYMENT_NAME_ASC
                                    ? Colors.secondaryColor
                                    : Colors.descriptionColor
                                } />
                            )}
                          </TouchableOpacity>
                          <TouchableOpacity
                            onPress={() => {
                              setSort(QUEUE_SORT_FIELD_TYPE.NAME_ASC);
                            }}>
                            {switchMerchant ? (
                              <Entypo
                                name="triangle-down"
                                size={8}
                                color={
                                  sort === PAYMENT_SORT_FIELD_TYPE.PAYMENT_NAME_DESC
                                    ? Colors.secondaryColor
                                    : Colors.descriptionColor
                                } />
                            ) : (
                              <Entypo
                                name="triangle-down"
                                size={14}
                                color={
                                  sort === PAYMENT_SORT_FIELD_TYPE.PAYMENT_NAME_DESC
                                    ? Colors.secondaryColor
                                    : Colors.descriptionColor
                                } />
                            )}
                          </TouchableOpacity>
                        </View>
                      </View>
                    </View>

                    <View style={{ width: '24%', alignItems: 'flex-start' }}>
                      <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                        <View
                          style={{
                            marginHorizontal: 0.5,
                            left: Platform.OS === 'android' ? '1%' : 0,
                          }}>
                          <TouchableOpacity
                            onPress={() => {
                              if (sort === PAYMENT_SORT_FIELD_TYPE.PAYMENT_STATUS_ASC) {
                                setSort(PAYMENT_SORT_FIELD_TYPE.PAYMENT_STATUS_DESC);
                              } else {
                                setSort(PAYMENT_SORT_FIELD_TYPE.PAYMENT_STATUS_ASC);
                              }
                            }}>
                            <Text
                              style={[
                                {
                                  color: 'black',
                                  fontFamily: 'NunitoSans-Regular',
                                },
                                switchMerchant
                                  ? {
                                    fontSize: 10,
                                  }
                                  : {},
                              ]}>
                              Status
                            </Text>
                          </TouchableOpacity>
                        </View>
                        <View style={{}}>
                          <TouchableOpacity
                            onPress={() => {
                              setSort(PAYMENT_SORT_FIELD_TYPE.PAYMENT_STATUS_DESC);
                            }}>
                            {switchMerchant ? (
                              <Entypo
                                name="triangle-up"
                                size={8}
                                color={
                                  sort === PAYMENT_SORT_FIELD_TYPE.PAYMENT_STATUS_ASC
                                    ? Colors.secondaryColor
                                    : Colors.descriptionColor
                                } />
                            ) : (
                              <Entypo
                                name="triangle-up"
                                size={14}
                                color={
                                  sort === PAYMENT_SORT_FIELD_TYPE.PAYMENT_STATUS_ASC
                                    ? Colors.secondaryColor
                                    : Colors.descriptionColor
                                } />
                            )}
                          </TouchableOpacity>
                          <TouchableOpacity
                            onPress={() => {
                              setSort(PAYMENT_SORT_FIELD_TYPE.PAYMENT_STATUS_ASC);
                            }}>
                            {switchMerchant ? (
                              <Entypo
                                name="triangle-down"
                                size={8}
                                color={
                                  sort === PAYMENT_SORT_FIELD_TYPE.PAYMENT_STATUS_DESC
                                    ? Colors.secondaryColor
                                    : Colors.descriptionColor
                                } />
                            ) : (
                              <Entypo
                                name="triangle-down"
                                size={14}
                                color={
                                  sort === PAYMENT_SORT_FIELD_TYPE.PAYMENT_STATUS_DESC
                                    ? Colors.secondaryColor
                                    : Colors.descriptionColor
                                } />
                            )}
                          </TouchableOpacity>
                        </View>
                      </View>
                    </View>


                  </View>
                  <FlatList
                    scrollEnabled={Platform.OS == 'ios' ? false : true}
                    showsVerticalScrollIndicator={false}
                    data={paymentMethods.concat(outletPaymentMethodsDropdownList)}
                    renderItem={renderDefaultPaymentMethod}
                    keyExtractor={(item, index) => String(index)}
                  />

                </View>
              }
            </View>
            {/* </ScrollView> */}


            {/* add modal */}
            <ModalView supportedOrientations={['landscape', 'portrait']} style={{ flex: 1 }} visible={addPaymentMethodModal} transparent>
              <KeyboardAvoidingView behavior={'padding'} style={styles.modalContainer}>
                <View
                  style={[
                    {
                      width: windowWidth <= 1133 ? windowWidth * 0.45 : windowWidth * 0.4,
                      height: windowHeight * 0.795,
                      backgroundColor: Colors.whiteColor,
                      borderRadius: 12,
                      padding: windowWidth * 0.04,
                      paddingBottom: 0,
                      alignItems: 'center',
                      justifyContent: 'space-between',
                    },
                    switchMerchant
                      ? {
                        padding: windowWidth * 0.005,
                        width: windowWidth * 0.3,
                        height: windowHeight * 0.7,
                        paddingBottom: 0,
                      }
                      : {},
                    {
                      ...getTransformForModalInsideNavigation(),
                    }
                  ]}>
                  <TouchableOpacity
                    style={{
                      position: 'absolute',
                      right: windowWidth * 0.02,
                      top: windowWidth * 0.02,

                      elevation: 1000,
                      zIndex: 1000,
                    }}
                    onPress={() => {
                      setAddPaymentMethodModal(false);
                    }}>
                    <AntDesign
                      name="closecircle"
                      size={switchMerchant ? 15 : 25}
                      color={Colors.fieldtTxtColor}
                    />
                  </TouchableOpacity>
                  <View style={[{ justifyContent: 'space-between', }, switchMerchant ? { height: '100%' } : {}]}>
                    <View style={{}}>
                      <Text
                        style={[
                          {
                            fontSize: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 16 : 24,
                            justifyContent: 'center',
                            alignSelf: 'center',
                            fontFamily: 'NunitoSans-Bold',
                          },
                          switchMerchant
                            ? {
                              fontSize: 16,
                            }
                            : {},
                        ]}>
                        {selectedPaymentMethod ? 'Payment Method Info' : 'Add Payment Method'}
                      </Text>
                      {methodSection === METHOD_SECTION.CUSTOM ?
                        <Text
                          style={[
                            {
                              fontSize: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 15 : 20,
                              justifyContent: 'center',
                              alignSelf: 'center',
                              marginTop: 5,
                              color: Colors.descriptionColor,
                              fontFamily: 'NunitoSans-Regular',
                            },
                            switchMerchant
                              ? {
                                fontSize: 10,
                              }
                              : {},
                          ]}>{`There are currently ${outletPaymentMethods.length} payment method${outletPaymentMethods.length > 1 ? 's' : ''}.`}</Text>
                        :
                        <Text
                          style={[
                            {
                              fontSize: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 15 : 20,
                              justifyContent: 'center',
                              alignSelf: 'center',
                              marginTop: 5,
                              color: Colors.descriptionColor,
                              fontFamily: 'NunitoSans-Regular',
                            },
                            switchMerchant
                              ? {
                                fontSize: 10,
                              }
                              : {},
                          ]}>{`There are currently ${paymentMethods.length} payment method${paymentMethods.length > 1 ? 's' : ''}.`}</Text>}
                    </View>

                    <View style={[{ justifyContent: 'center', }, switchMerchant ? {
                      // borderWidth: 1,
                      // marginTop: '-30%'
                      bottom: windowHeight * 0.11
                    } : {}]}>
                      <View
                        style={[{
                          justifyContent: 'center',
                          alignSelf: 'center',
                          alignContent: 'center',
                          marginTop: 30,
                          flexDirection: 'row',
                          width: '100%',
                        }, switchMerchant ? {
                          marginTop: '3%',
                          // borderWidth: 1
                        } : {}]}>
                        <View style={{ justifyContent: 'center', width: switchMerchant ? '80%' : '100%' }}>
                          <TouchableOpacity
                            onPress={() => {
                              handleChoosePhoto();
                            }}>
                            <View style={{ height: switchMerchant ? 135 : windowWidth * 0.22, backgroundColor: Colors.fieldtBgColor, }}>
                              <View style={{ zIndex: 1, borderWidth: 1, borderRadius: 5, borderColor: '#E5E5E5' }}>
                                <AsyncImage
                                  //placeholder={{uri: 'https://i.imgur.com/R5TraVR.png'}}
                                  //placeholderSource
                                  source={{ uri: image }}
                                  style={{ width: switchMerchant ? '100%' : '100%', height: switchMerchant ? 135 : windowWidth * 0.22, }}
                                  hideLoading
                                />
                                <View style={{
                                  position: 'absolute',
                                  bottom: 5,
                                  right: 5,
                                  //   backgroundColor: 'black',
                                  //opacity: 0.5,
                                  // width: 120, 
                                  // height: 120,
                                  // borderRadius: 13,
                                }}>

                                  <FontAwesome5 name='edit' size={switchMerchant ? 17 : ((!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 17 : 23)} color={Colors.primaryColor}
                                  //  style={{ position: 'absolute', zIndex: -1 }}
                                  />

                                </View>

                              </View>
                              <Icon name='upload' size={switchMerchant ? 80 : windowWidth * 0.1} color='lightgrey' style={{ top: switchMerchant ? 27 : 55, position: 'absolute', alignSelf: 'center', zIndex: -1 }} />

                            </View>
                          </TouchableOpacity>
                        </View>
                      </View>

                      <View
                        style={{
                          justifyContent: 'center',
                          alignSelf: 'center',
                          alignContent: 'center',
                          marginTop: switchMerchant ? 10 : 20,
                          flexDirection: 'row',
                          width: '100%',
                        }}>
                        <View style={{ justifyContent: 'center', width: '40%' }}>
                          <Text
                            style={[
                              {
                                color: 'black',
                                fontFamily: 'NunitoSans-Bold',
                                fontSize: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 16 : 20,
                              },
                              switchMerchant
                                ? {
                                  fontSize: 10,
                                  left: windowWidth * 0.01,
                                }
                                : {},
                            ]}>
                            Payment Name
                          </Text>
                        </View>
                        <View style={{ justifyContent: 'center', width: '60%' }}>
                          <TextInput
                            placeholder="Cash-on Delivery"
                            placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                            style={[
                              {
                                backgroundColor: Colors.fieldtBgColor,
                                width: '100%',
                                height: 40,
                                borderRadius: 5,
                                padding: 5,
                                marginVertical: 5,
                                borderWidth: 1,
                                borderColor: '#E5E5E5',
                                paddingLeft: 10,
                              },
                              switchMerchant
                                ? {
                                  fontSize: 10,
                                  width: windowWidth * 0.16,
                                  height: windowHeight * 0.06,
                                  padding: 0,
                                  margin: 0,
                                }
                                : {},
                            ]}
                            //iOS
                            clearTextOnFocus
                            //////////////////////////////////////////////
                            //Android
                            onFocus={() => {
                              setTemp(paymentName)
                              setPaymentName('');
                            }}
                            ///////////////////////////////////////////////
                            //When textinput is not selected
                            onEndEditing={() => {
                              if (paymentName == '') {
                                setPaymentName(temp);
                              }
                            }}
                            //////////////////////////////////////////////
                            onChangeText={(text) => {
                              setPaymentName(text);
                            }}
                            keyboardType={'default'}
                            defaultValue={paymentName}
                          />
                        </View>
                      </View>
                      <View
                        style={{
                          justifyContent: 'center',
                          alignSelf: 'center',
                          alignContent: 'center',
                          marginTop: 10,
                          flexDirection: 'row',
                          width: '100%',
                        }}>
                        <View style={{ justifyContent: 'center', width: '40%' }}>
                          <Text
                            style={[
                              {
                                color: 'black',
                                fontFamily: 'NunitoSans-Bold',
                                fontSize: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 16 : 20,
                              },
                              switchMerchant
                                ? {
                                  fontSize: 10,
                                  left: windowWidth * 0.01,
                                }
                                : {},
                            ]}>
                            Partner Name
                          </Text>
                        </View>
                        <View style={{ justifyContent: 'center', width: '60%', borderWidth: 1, borderColor: '#E5E5E5', borderRadius: 5, height: 40, backgroundColor: Colors.fieldtBgColor }}>
                          <RNPickerSelect
                            placeholder={{}}
                            useNativeAndroidPickerStyle={false}
                            style={{
                              inputIOS: {
                                fontSize: switchMerchant ? 10 : 14,
                                fontFamily: 'NunitoSans-Regular',
                                textAlign: 'center',
                              },
                              inputAndroid: {
                                fontSize: switchMerchant ? 10 : 14,
                                fontFamily: 'NunitoSans-Regular',
                                justifyContent: 'center',
                                textAlign: 'center',
                                height: 40,
                                color: 'black',
                              },
                              inputAndroidContainer: { width: '100%' },
                              //backgroundColor: 'red',
                              height: 35,

                              chevronContainer: {
                                display: 'none',
                              },
                              chevronDown: {
                                display: 'none',
                              },
                              chevronUp: {
                                display: 'none',
                              },
                            }}
                            items={[
                              { label: 'None', value: '' },
                              ...OTHER_DELIVERY_PARTNER_TYPES_DROPDOWN_LIST,
                            ]}
                            value={partnerType}
                            onValueChange={(value) => {
                              setPartnerType(value);
                            }}
                          />
                        </View>
                      </View>
                    </View>

                    <View
                      style={[{
                        flexDirection: 'row',
                        // alignItems: 'center',
                        justifyContent: 'center',
                        marginTop: 35,
                      }, switchMerchant ? {
                        // marginTop: windowHeight * 0.067,
                        // borderWidth: 1,
                        position: 'absolute',
                        bottom: 0,
                        width: '100%'
                      } : {}]}>
                      <TouchableOpacity
                        disabled={isLoading}
                        onPress={() => {
                          // setConfirmQueueModal(true)
                          // setAddPaymentMethodModal(false)

                          if (selectedPaymentMethod.uniqueId === undefined) {
                            createPaymentMethod();
                          } else {
                            updatePaymentMethod();
                          }
                        }}
                        style={[{
                          backgroundColor: Colors.fieldtBgColor,
                          width: windowWidth <= 1133 ? '56%' : '57.8%',
                          justifyContent: 'center',
                          alignItems: 'center',
                          alignContent: 'center',
                          height: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 50 : 60,
                          borderBottomLeftRadius: switchMerchant ? windowWidth * 0.25 : 25,
                          borderRightWidth: StyleSheet.hairlineWidth,
                          borderTopWidth: StyleSheet.hairlineWidth,
                        }, switchMerchant ? {
                          height: 35,
                          width: '51%'
                        } : {}]}>
                        {isLoading ? (
                          <>
                            {switchMerchant ? <ActivityIndicator
                              size={'small'}
                              color={Colors.primaryColor}
                            /> : <ActivityIndicator
                              size={'large'}
                              color={Colors.primaryColor}
                            />}

                          </>
                        ) : (
                          <Text
                            style={[
                              {
                                fontSize: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 15 : 22,
                                color: Colors.primaryColor,
                                fontFamily: 'NunitoSans-SemiBold',
                              },
                              switchMerchant
                                ? {
                                  fontSize: 10,
                                }
                                : {},
                            ]}>
                            Confirm
                          </Text>
                        )}
                      </TouchableOpacity>
                      <TouchableOpacity
                        disabled={isLoading}
                        onPress={() => {
                          // setState({ visible: false });
                          setAddPaymentMethodModal(false);
                        }}
                        style={[{
                          backgroundColor: Colors.fieldtBgColor,
                          width: '57.8%',
                          justifyContent: 'center',
                          alignItems: 'center',
                          alignContent: 'center',
                          height: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 50 : 60,
                          borderBottomRightRadius: switchMerchant ? windowWidth * 0.03 : 25,
                          borderTopWidth: StyleSheet.hairlineWidth,
                        }, switchMerchant ? {
                          height: 35,
                          width: '51%'
                        } : {}]}>
                        <Text
                          style={[
                            {
                              fontSize: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 15 : 22,
                              color: Colors.descriptionColor,
                              fontFamily: 'NunitoSans-SemiBold',
                            },
                            switchMerchant
                              ? {
                                fontSize: 10,
                              }
                              : {},
                          ]}>
                          Cancel
                        </Text>
                      </TouchableOpacity>
                    </View>
                  </View>
                </View>
              </KeyboardAvoidingView>
            </ModalView>

            {/* confirm modal */}
            <ModalView supportedOrientations={['landscape', 'portrait']} style={{ flex: 1 }} visible={confirmQueueModal} transparent>
              <View style={styles.modalContainer}>
                <View
                  style={[{
                    width: windowWidth * 0.4,
                    height: windowHeight * 0.4,
                    backgroundColor: Colors.whiteColor,
                    borderRadius: windowWidth * 0.03,
                    padding: windowWidth * 0.04,
                    alignItems: 'center',
                    justifyContent: 'space-between',
                  },
                  switchMerchant
                    ? {
                      padding: windowWidth * 0.03,
                      height: windowHeight * 0.7,
                    }
                    : {},
                  {
                    ...getTransformForModalInsideNavigation(),
                  }
                  ]}>
                  <View style={{}}>
                    <View style={{ height: windowHeight * 0.1 }}>
                      <Text
                        style={[{
                          textAlign: 'center',
                          fontWeight: '700',
                          fontSize: 30,
                        },
                        switchMerchant
                          ? {
                            fontSize: 22,
                          }
                          : {},
                        ]}>
                        Done!
                      </Text>
                    </View>

                    <View
                      style={[{
                        justifyContent: 'center',
                        alignItems: 'center',
                        alignContent: 'center',
                        alignSelf: 'center',
                      }, switchMerchant ? { marginTop: windowHeight * 0.02, } : {}]}>
                      <Text
                        style={[{
                          textAlign: 'center',
                          color: Colors.descriptionColor,
                          fontSize: 25,
                          width: '80%',
                          alignSelf: 'center',
                        },
                        switchMerchant
                          ? {
                            fontSize: 16,
                          }
                          : {},
                        ]}>
                        You’ve added queue
                      </Text>
                      <Text
                        style={[{
                          textAlign: 'center',
                          color: Colors.descriptionColor,
                          fontSize: 25,
                          width: '80%',
                          alignSelf: 'center',
                        },
                        switchMerchant
                          ? {
                            fontSize: 18,
                          }
                          : {},
                        ]}>
                        successfully with number:{' '}
                        {selectedPaymentMethod ? `#${selectedPaymentMethod.number}` : 'N/A'}
                        {/* {item.number} */}
                      </Text>
                    </View>

                    <View
                      style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                        justifyContent: 'center',
                        height: windowHeight * 0.25,
                        width: windowWidth * 0.4,
                      }}>
                      <TouchableOpacity
                        onPress={() => {
                          setConfirmQueueModal(false);
                        }}
                        style={[{
                          backgroundColor: Colors.fieldtBgColor,
                          width: windowWidth * 0.4,
                          justifyContent: 'center',
                          alignItems: 'center',
                          alignContent: 'center',
                          height: 60,
                          borderBottomLeftRadius: 35,
                          borderBottomRightRadius: 35,
                          borderTopWidth: StyleSheet.hairlineWidth,
                        }, switchMerchant ? { marginTop: windowHeight * 0.23, } : {}]}>
                        <Text
                          style={[{
                            fontSize: 22,
                            color: Colors.primaryColor,
                            fontFamily: 'NunitoSans-SemiBold',
                          },
                          switchMerchant
                            ? {
                              fontSize: 16,

                            }
                            : {},
                          ]}>
                          Confirm
                        </Text>
                      </TouchableOpacity>
                    </View>
                  </View>
                </View>
              </View>
            </ModalView>
          </ScrollView>
        </ScrollView>
      </View>
    </UserIdleWrapper>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.highlightColor,
    flexDirection: 'row',
  },
  headerLogo: {
    width: 112,
    height: 25,
    marginLeft: 10,
  },
  list: {
    paddingVertical: 20,
    paddingHorizontal: 30,
    flexDirection: 'row',
    alignItems: 'center',
  },
  listItem: {
    marginLeft: 20,
    color: Colors.descriptionColor,
    fontSize: 16,
  },
  content7: {
    backgroundColor: '#e4e8eb',
    width: 800,
    height: 120,
    marginLeft: 50,
    marginVertical: 30,
    borderRadius: 20,
  },

  content8: {
    flex: 3,
    backgroundColor: '#e4e8eb',

    height: 120,
  },
  content9: {
    flex: 1,
    backgroundColor: Colors.primaryColor,

    height: 120,
  },
  content10: {
    flex: 1,
    backgroundColor: Colors.secondaryColor,

    height: 120,
  },
  content11: {
    flex: 1,
    backgroundColor: '#848f96',

    height: 120,
  },
  content6: {
    backgroundColor: Colors.whiteColor,
    width: 120,
    shadowColor: '#000',
    shadowOffset: {
      width: 8,
      height: 8,
    },
    shadowOpacity: 0.55,
    shadowRadius: 10.32,
    width: 120,
    height: 120,
    marginLeft: 50,
    marginVertical: 15,
    borderRadius: 5,
  },

  sidebar: {
    width: Dimensions.get('window').width * Styles.sideBarWidth,
    // shadowColor: '#000',
    // shadowOffset: {
    //   width: 0,
    //   height: 8,
    // },
    // shadowOpacity: 0.44,
    // shadowRadius: 10.32,

    // elevation: 16,
  },
  content: {
    width: Dimensions.get('window').width * (1 - Styles.sideBarWidth),
    padding: 20,
  },
  headerLeftStyle: {
    width: Dimensions.get('window').width * 0.17,
    justifyContent: 'center',
    alignItems: 'center',
  },
  submitText: {
    height:
      Platform.OS == 'ios'
        ? Dimensions.get('window').height * 0.06
        : Dimensions.get('window').height * 0.05,
    left: 295,
    paddingVertical: 5,
    paddingHorizontal: 20,
    flexDirection: 'row',
    color: '#4cd964',
    textAlign: 'center',
    borderRadius: 10,
    borderWidth: 1,
    borderColor: Colors.primaryColor,
    justifyContent: 'center',
    alignContent: 'center',
    alignItems: 'center',
    marginRight: 10,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: Colors.modalBgColor,
    alignItems: 'center',
    justifyContent: 'center',
  },
});
export default SettingPaymentScreen;
