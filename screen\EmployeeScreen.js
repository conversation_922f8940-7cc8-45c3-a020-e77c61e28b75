import { Text } from "react-native-fast-text";
import React, { Component, useReducer, useState, useEffect, useCallback } from 'react';
import {
  StyleSheet,
  Image,
  View,
  Alert,
  TouchableOpacity,
  Dimensions,
  TextInput,
  Modal as ModalComponent,
  ActivityIndicator,
  Platform,
  useWindowDimensions,
} from 'react-native';
import { ModalView as ModalViewComponent } from 'react-native-multiple-modals';
import { ScrollView, FlatList } from "react-native-gesture-handler";
import Colors from '../constant/Colors';
import SideBar from './SideBar';
import * as User from '../util/User';
import ApiClient from '../util/ApiClient';
import API from '../constant/API';
import Icon from 'react-native-vector-icons/Feather';
import FontAwesome5 from 'react-native-vector-icons/FontAwesome5';
import DropDownPicker from 'react-native-dropdown-picker';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { launchCamera, launchImageLibrary } from 'react-native-image-picker';
import Styles from '../constant/Styles';
import {
  isTablet,
  getTransformForModalInsideNavigation,
  getTransformForScreenInsideNavigation,
  logEventAnalytics,
  parseImagePickerResponse,
} from '../util/common';
import { MerchantStore } from '../store/merchantStore';
import { uploadImageToFirebaseStorage } from '../util/common';
import 'react-native-get-random-values';
import { v4 as uuidv4 } from 'uuid';
import { UserStore } from '../store/userStore';
import { OutletStore } from '../store/outletStore';
import { EMAIL_REPORT_TYPE, ROLE_TYPE_PARSED, PRIVILEGES_NAME, ROLE_TYPE, EXPAND_TAB_TYPE, SCREEN_NAME, SCREEN_NAME_PARSED } from '../constant/common';
import { convertArrayToCSV, generateEmailReport } from '../util/common';
import RNFetchBlob from 'rn-fetch-blob';
import moment from 'moment';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import RNPickerSelect from 'react-native-picker-select';
import { useKeyboard } from '../hooks';
import GCalendar from '../assets/svg/GCalendar';

//////////////////////////// Test clocked in time ////////////////////////////
import EvilIcons from 'react-native-vector-icons/EvilIcons';
import DateTimePickerModal from 'react-native-modal-datetime-picker';
import { CommonStore } from '../store/commonStore';
import AntDesign from 'react-native-vector-icons/AntDesign';
import AsyncImage from '../components/asyncImage';
import { firebase } from '@react-native-firebase/firestore';
import { Collections } from '../constant/firebase';
import APILocal from '../util/apiLocalReplacers';
import { useFocusEffect } from '@react-navigation/native';
import UserIdleWrapper from '../components/userIdleWrapper';
import { ANALYTICS, ANALYTICS_PARSED } from '../constant/analytics';

const ModalView = Platform.OS === 'android' ? ModalComponent : ModalViewComponent;

const EmployeeScreen = (props) => {
  const { navigation } = props;

  ///////////////////////////////////////////////////////////

  const [isMounted, setIsMounted] = useState(true);

  useFocusEffect(
    useCallback(() => {
      setIsMounted(true);
      return () => {
        setIsMounted(false);
      };
    }, [])
  );

  ///////////////////////////////////////////////////////////

  const { width: windowWidth, height: windowHeight } = useWindowDimensions();

  const [keyboardHeight] = useKeyboard();

  const [employeeList, setEmployeeList] = useState([]);
  const [employeeListLength, setEmployeeListLength] = useState(0);
  const [lists, setLists] = useState([]);
  const [list1, setList1] = useState(true);
  const [searchList, setSearchList] = useState(false);
  const [addEmployee, setAddEmployee] = useState(false);
  const [addEmployeeItem, setAddEmployeeItem] = useState(true);
  const [position, setPosition] = useState('');
  // const [// outletId, set// outletId] = useState(1);
  const [outletId, setOutletId] = useState(User.getOutletId());
  const [waiter, setWaiter] = useState('');
  const [search, setSearch] = useState('');
  const [countries, setCountries] = useState(['uk']);
  const [showDetail, setShowDetail] = useState(false);
  const [confirmRemove, setConfirmRemove] = useState(false);
  const [visible, setVisible] = useState(false);
  const [showEmployee, setShowEmployee] = useState([]);
  const [switchMerchant, setSwitchMerchant] = useState(isTablet() ? false : true);
  const [loading, setLoading] = useState(false);
  const [role, setRole] = useState('frontliner');
  const [pin, setPin] = useState('');
  const [number, setNumber] = useState('');
  const [password, setPassword] = useState('');
  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const [username, setUsername] = useState('');
  const [privileges, setPrivileges] = useState([]);
  const [screensToBlock, setScreensToBlock] = useState([]);

  const [image, setImage] = useState('');
  const [imageType, setImageType] = useState('');
  const [isImageChanged, setIsImageChanged] = useState(false);

  //////////////////////////// Test clocked in time ////////////////////////////
  const [showDateTimePicker, setShowDateTimePicker] = useState(false);
  const [pickDate, setPickDate] = useState('');
  const [dayOfWeek, setDayOfWeek] = useState('');

  const [showDateTimePicker1, setShowDateTimePicker1] = useState(false);
  const [pickTime, setPickTime] = useState('');
  const [regularTime, setRegularTime] = useState('');
  const [overTime, setOverTime] = useState('');

  //////////////////////////////////////////////////////////////

  const currOutletId = MerchantStore.useState((s) => s.currOutletId);
  const allOutletsDict = MerchantStore.useState((s) => s.allOutletsDict);
  const allOutlets = MerchantStore.useState((s) => s.allOutlets);
  const selectedOutletEmployeeEdit = CommonStore.useState(
    (s) => s.selectedOutletEmployeeEdit,
  );

  const [targetOutletDropdownList, setTargetOutletDropdownList] = useState([]);
  const [selectedTargetOutletId, setSelectedTargetOutletId] = useState('');
  const outletSelectDropdownView = CommonStore.useState(
    (s) => s.outletSelectDropdownView,
  );

  const [exportModalVisibility, setExportModalVisibility] = useState(false);
  const [exportEmail, setExportEmail] = useState('');
  const isLoading = CommonStore.useState((s) => s.isLoading);
  const [isLoadingExcel, setIsLoadingExcel] = useState(false);
  const [isLoadingCsv, setIsLoadingCsv] = useState(false);

  const [clockInModal, setClockInModal] = useState(false);
  const [clockOutModal, setClockOutModal] = useState(false);
  const [showShiftDatePicker, setShowShiftDatePicker] = useState(false);
  const [showShiftTimePicker, setShowShiftTimePicker] = useState(false);
  const [shiftDate, setShiftDate] = useState(moment());
  const [shiftTime, setShiftTime] = useState(moment());

  const [temp, setTemp] = useState('');

  const [currEditEmployee, setCurrEditEmployee] = useState({});

  useEffect(() => {
    setTargetOutletDropdownList(
      allOutlets.map((outlet) => ({
        label: outlet.name,
        value: outlet.uniqueId,
      })),
    );

    // console.log('targetOutletDropdownList');
    // console.log(targetOutletDropdownList);

    if (allOutlets.length > 0) {
      setSelectedTargetOutletId(currOutletId);
    }
  }, [allOutlets, currOutletId]);

  //////////////////////////////////////////////////////////////

  const [outletDropdownList, setOutletDropdownList] = useState([]);
  const [selectedOutletId, setSelectedOutletId] = useState('');

  const merchantId = UserStore.useState((s) => s.merchantId);

  const allOutletsEmployees = OutletStore.useState(
    (s) => s.allOutletsEmployees,
  );

  const userId = UserStore.useState((s) => s.firebaseUid);
  const userName = UserStore.useState((s) => s.name);
  const userRole = UserStore.useState((s) => s.role);
  const merchantName = MerchantStore.useState((s) => s.name);

  const isMasterAccount = UserStore.useState((s) => s.isMasterAccount);

  const isAlphaUser = UserStore.useState((s) => s.isAlphaUser);
  const expandTab = CommonStore.useState((s) => s.expandTab);
  const currPageStack = CommonStore.useState((s) => s.currPageStack);

  useEffect(() => {
    setOutletDropdownList(
      allOutlets.filter(outlet => {
        if (outlet.uniqueId === currOutletId || isMasterAccount) {
          return true;
        }
      }).map((item) => ({ label: item.name, value: item.uniqueId })),
    );

    var selectedOutletIdTemp = '';

    if (selectedOutletId === '' && allOutlets.length > 0) {
      selectedOutletIdTemp = allOutlets[0].uniqueId;
    }

    if (selectedOutletEmployeeEdit && selectedOutletEmployeeEdit.outletId &&
      allOutlets.find(outlet => outlet.uniqueId && selectedOutletEmployeeEdit.outletId)) {
      selectedOutletIdTemp = selectedOutletEmployeeEdit.outletId;
    }

    setSelectedOutletId(selectedOutletIdTemp);
  }, [allOutlets, selectedOutletEmployeeEdit, currOutletId, isMasterAccount]);

  useEffect(() => {
    if (selectedOutletEmployeeEdit) {
      setName(selectedOutletEmployeeEdit.name);
      setUsername(selectedOutletEmployeeEdit.uniqueName);
      setRole(selectedOutletEmployeeEdit.role);
      // setSelectedOutletId(selectedOutletEmployeeEdit.selectedOutletId);
      setNumber(selectedOutletEmployeeEdit.number);
      setEmail(selectedOutletEmployeeEdit.email);
      // setPassword(selectedOutletEmployeeEdit.password);
      setPassword('password');
      setImage(selectedOutletEmployeeEdit.image);
      setIsImageChanged(false);
      setPin(selectedOutletEmployeeEdit.pinNo || '');
      setPrivileges(selectedOutletEmployeeEdit.privileges || []);
      setScreensToBlock(selectedOutletEmployeeEdit.screensToBlock || []);
    } else {
      setName('');
      setUsername('');
      setRole('frontliner');
      //setSelectedOutletId('');
      setNumber('');
      setEmail('');
      setPassword('');
      setImage('');
      setIsImageChanged(false);
      setPin('');
      setPrivileges([]);
      setScreensToBlock([]);
    }
  }, [selectedOutletEmployeeEdit, addEmployee, addEmployeeItem]);

  const setState = () => { };

  // navigation.dangerouslyGetParent().setOptions({
  //   tabBarVisible: false,
  // });

  navigation.setOptions({
    headerLeft: () => (
      <TouchableOpacity
        onPress={() => {
          if (isAlphaUser || true) {
            navigation.navigate('MenuOrderingScreen');

            CommonStore.update((s) => {
              s.currPage = 'MenuOrderingScreen';
              s.currPageStack = [...currPageStack, 'MenuOrderingScreen'];
            });
          }
          else {
            navigation.navigate('Table');

            CommonStore.update((s) => {
              s.currPage = 'Table';
              s.currPageStack = [...currPageStack, 'Table'];
            });
          }
          if (expandTab !== EXPAND_TAB_TYPE.OPERATION) {
            CommonStore.update((s) => {
              s.expandTab = EXPAND_TAB_TYPE.OPERATION;
            });
          }

          logEventAnalytics({
            eventName: ANALYTICS.MODULE_EMPLOYEE_MANAGE_EMPLOYEE_C_LOGO,
            eventNameParsed: ANALYTICS_PARSED.MODULE_EMPLOYEE_MANAGE_EMPLOYEE_C_LOGO
          })
        }}
        style={styles.headerLeftStyle}>
        <Image
          style={[{
            width: 124,
            height: 26,
          }, switchMerchant ? {
            transform: [
              { scaleX: 0.7 },
              { scaleY: 0.7 }
            ],
          } : {}]}
          resizeMode="contain"
          source={require('../assets/image/logo.png')}
        />
      </TouchableOpacity>
    ),
    headerTitle: () => (
      <View
        style={[
          {
            // justifyContent: Platform.OS === 'android' ? 'center' : 'flex-start',
            // alignItems: Platform.OS === 'android' ? 'center' : 'flex-start',
            // marginRight: Platform.OS === 'ios' ? "27%" : 0,
            // bottom: switchMerchant ? '2%' : 0,
            ...global.getHeaderTitleStyle(),
          },
          // windowWidth >= 768 && switchMerchant
          //   ? { right: windowWidth * 0.1 }
          //   : {},
          // windowWidth <= 768
          //   ? { right: 20 }
          //   : {},
        ]}>
        <Text
          style={{
            fontSize: switchMerchant ? 20 : 24,
            // lineHeight: 25,
            textAlign: 'left',
            alignItems: 'flex-start',
            justifyContent: 'flex-start',
            fontFamily: 'NunitoSans-Bold',
            color: Colors.whiteColor,
            opacity: 1,
            paddingLeft: Platform.OS === 'ios' ? "1%" : 0,
          }}>
          Employee
        </Text>
      </View>
    ),
    headerRight: () => (
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
        }}>
        {outletSelectDropdownView()}
        <View
          style={{
            backgroundColor: 'white',
            width: 0.5,
            height: windowHeight * 0.025,
            opacity: 0.8,
            marginHorizontal: 15,
            bottom: -1,
          }} />
        <TouchableOpacity
          onPress={() => {
            if (global.currUserRole === 'admin') {
              navigation.navigate('Setting');
            }

            logEventAnalytics({
              eventName: ANALYTICS.MODULE_EMPLOYEE_MANAGE_EMPLOYEE_C_PROFILE,
              eventNameParsed: ANALYTICS_PARSED.MODULE_EMPLOYEE_MANAGE_EMPLOYEE_C_PROFILE
            })
          }}
          style={{ flexDirection: 'row', alignItems: 'center' }}>
          <Text
            style={[{
              fontFamily: 'NunitoSans-SemiBold',
              fontSize: switchMerchant ? 10 : 16,
              color: Colors.secondaryColor,
              marginRight: 15,
            }, switchMerchant ? { width: windowWidth / 8 } : {}]}
            numberOfLines={switchMerchant ? 1 : 1}
          >
            {userName}
          </Text>
          <View
            style={{
              marginRight: 30,
              width: windowHeight * 0.05,
              height: windowHeight * 0.05,
              borderRadius: windowHeight * 0.05 * 0.5,
              alignItems: 'center',
              justifyContent: 'center',
              backgroundColor: 'white',
            }}>
            <Image
              style={{
                width: windowHeight * 0.035,
                height: windowHeight * 0.035,
                alignSelf: 'center',
              }}
              source={require('../assets/image/profile-pic.jpg')}
            />
          </View>
        </TouchableOpacity>
      </View>
    ),
  });

  // componentDidMount = () => {
  //   getEmployeeList();
  // }

  const renderItem = ({ item }) => (
    <TouchableOpacity
      onPress={() => {
        //setState({ showDetail: true, showEmployee: item, addEmployeeItem: false }),
        CommonStore.update((s) => {
          s.selectedOutletEmployeeEdit = item;
        });
        setAddEmployee(true);
        setAddEmployeeItem(false);
        // console.log('PRESSWS');
      }}>
      <View
        style={{
          backgroundColor: '#ffffff',
          flexDirection: 'row',
          paddingVertical: 20,
          paddingHorizontal: 10,
          borderBottomWidth: StyleSheet.hairlineWidth,
          borderBottomColor: '#c4c4c4',
          //justifyContent: 'space-between'
          borderBottomLeftRadius: 5,
          borderBottomRightRadius: 5,
        }}>
        <AsyncImage
          source={{ uri: item.avatar }}
          style={{ width: 35, height: 35, borderRadius: 100, marginRight: 15 }}
        />
        <Text
          style={{
            width: switchMerchant ? '10%' : '12%',
            marginRight: 5,
            fontFamily: 'NunitoSans-Regular',
            fontSize: switchMerchant ? 10 : 14,
          }}>
          {item.name}
        </Text>
        <Text
          style={{
            width: '20%', //15%
            marginRight: 5,
            fontFamily: 'NunitoSans-Regular',
            fontSize: switchMerchant ? 10 : 14,
          }}>
          {allOutletsDict[item.outletId] ? allOutletsDict[item.outletId].name : 'N/A'}
        </Text>
        <Text
          style={{
            width: switchMerchant ? '10%' : '14%',
            marginRight: 5,
            fontFamily: 'NunitoSans-Regular',
            fontSize: switchMerchant ? 10 : 14,
          }}>
          {ROLE_TYPE_PARSED[item.role]}
        </Text>
        <Text
          style={{
            width: switchMerchant ? '10%' : '10%',
            marginRight: 5,
            fontFamily: 'NunitoSans-Regular',
            fontSize: switchMerchant ? 10 : 14,
          }}>
          {'Active'}
        </Text>
        {/* <Text style={{ width: '13%' }}>{item.number}</Text> */}
        {/* <Text style={{ width: '17.5%', marginRight: 0 }}>{item.email}</Text> */}
        {/* <Text style={{ width: '10%' }}>******</Text> */}
        {/* <Text style={{ width: '7%' }}>****</Text> */}
        {/* <Text style={{ width: '9%', marginRight: 0 }}>{item.dayOfWeek == null || item.dayOfWeek == undefined ? "-" : moment(item.dayOfWeek).format('L')}</Text> */}
        {/* <Text style={{ width: '9%', marginRight: 0 }}>{item.regularTime == null || item.regularTime == undefined ? "-" : moment(item.regularTime).format('L')}</Text> */}
        {/* <Text style={{ width: '9%', marginRight: 0 }}>{item.overTime == null || item.overTime == undefined ? "-" : moment(item.overTime).format('L')}</Text> */}
        <View
          style={{ width: switchMerchant ? '13%' : '16%' }} //11%
        >
          {!item.lastLoginAt ? (
            <Text
              style={{
                fontVariant: ['tabular-nums'],
                fontFamily: 'NunitoSans-Regular',
                fontSize: switchMerchant ? 10 : 14,
              }}>
              {'N/A'}
            </Text>
          ) : (
            <>
              <Text
                style={{
                  fontVariant: ['tabular-nums'],
                  fontFamily: 'NunitoSans-Regular',
                  fontSize: switchMerchant ? 10 : 14,
                }}>
                {moment(item.lastLoginAt).format('DD MMM YYYY')}
              </Text>
              <Text
                style={{
                  fontVariant: ['tabular-nums'],
                  fontFamily: 'NunitoSans-Regular',
                  fontSize: switchMerchant ? 10 : 14,
                }}>
                {moment(item.lastLoginAt).format('hh:mm A')}
              </Text>
            </>
          )}
        </View>

        <View
          style={{ width: '16%' }} //11%
        >
          {!item.lastLogoutAt ? (
            <Text
              style={{
                fontVariant: ['tabular-nums'],
                fontFamily: 'NunitoSans-Regular',
                fontSize: switchMerchant ? 10 : 14,
              }}>
              {'N/A'}
            </Text>
          ) : (
            <>
              <Text
                style={{
                  fontVariant: ['tabular-nums'],
                  fontFamily: 'NunitoSans-Regular',
                  fontSize: switchMerchant ? 10 : 14,
                }}>
                {moment(item.lastLogoutAt).format('DD MMM YYYY')}
              </Text>
              <Text
                style={{
                  fontVariant: ['tabular-nums'],
                  fontFamily: 'NunitoSans-Regular',
                  fontSize: switchMerchant ? 10 : 14,
                }}>
                {moment(item.lastLogoutAt).format('hh:mm A')}
              </Text>
            </>
          )}
        </View>
        {/*  <View style={{ width: '11%' }}>
          <TouchableOpacity
            style={{
              justifyContent: 'center',
              flexDirection: 'row',
              borderWidth: 1,
              borderColor: Colors.primaryColor,
              backgroundColor: '#0A1F44',
              borderRadius: 5,
              //width: 200,
              paddingHorizontal: 5,
              height: switchMerchant ? 35 : 40,
              alignItems: 'center',
              shadowOffset: {
                width: 0,
                height: 2,
              },
              shadowOpacity: 0.22,
              shadowRadius: 3.22,
              elevation: 1,
              zIndex: -1,
              marginRight: switchMerchant ? 10 : 15,
              paddingVertical: '5%',
            }}
            onPress={() => {
              setCurrEditEmployee(item);

              setClockInModal(true);
            }}>
            <View style={{ flexDirection: 'row' }}>
              <Text
                style={
                  switchMerchant
                    ? {
                      color: 'white',
                      fontSize: 8,
                      fontFamily: 'NunitoSans-Bold',
                      textAlign: 'center',
                    }
                    : {
                      color: 'white',
                      fontSize: 14,
                      fontFamily: 'NunitoSans-Bold',
                      textAlign: 'center',
                      //paddingVertical: '5%'
                    }
                }>
                CLOCK IN
              </Text>
            </View>
          </TouchableOpacity>
        </View> */}
        {/* <View style={{ width: '11%' }}>
          <TouchableOpacity
            style={{
              justifyContent: 'center',
              flexDirection: 'row',
              borderWidth: 1,
              borderColor: Colors.primaryColor,
              backgroundColor: '#0A1F44',
              borderRadius: 5,
              //width: 200,
              paddingHorizontal: 5,
              height: switchMerchant ? 35 : 40,
              alignItems: 'center',
              shadowOffset: {
                width: 0,
                height: 2,
              },
              shadowOpacity: 0.22,
              shadowRadius: 3.22,
              elevation: 1,
              zIndex: -1,
              marginRight: switchMerchant ? 10 : 15,
              paddingVertical: '5%',
            }}
            onPress={() => {
              setCurrEditEmployee(item);

              setClockOutModal(true);
            }}>
            <View style={{ flexDirection: 'row' }}>
              <Text
                style={
                  switchMerchant
                    ? {
                      color: 'white',
                      fontSize: 8,
                      fontFamily: 'NunitoSans-Bold',
                      textAlign: 'center',
                    }
                    : {
                      color: 'white',
                      fontSize: 14,
                      fontFamily: 'NunitoSans-Bold',
                      textAlign: 'center',
                      //paddingVertical: '5%'
                    }
                }>
                CLOCK OUT
              </Text>
            </View>
          </TouchableOpacity>
        </View> */}
      </View>
    </TouchableOpacity>
  );

  // function here

  const getEmployeeList = () => {
    ApiClient.GET(API.getEmployee + User.getOutletId()).then((results) => {
      //// console.log('employee', results)
      setState({ employeeList: results, employeeListLength: results.length });
    });
  };

  const addEmployeeFunc = async () => {
    // if (
    //   !name ||
    //   !email ||
    //   !outletId ||
    //   !role ||
    //   !password ||
    //   !number
    //   // !dayOfWeek ||
    //   // !regularTime ||
    //   // !overTime
    // ) {
    //   Alert.alert(
    //     'Error',
    //     'Please fill in the following information.\n\nName\nEmail\nRole\nPassword\nPhone Number',
    //     [{ text: 'OK', onPress: () => { } }],
    //     { cancelable: false },
    //   );
    const missingFields = [];

    if (!name) {
      missingFields.push("Name");
    }

    if (!email) {
      missingFields.push("Email");
    }

    if (!outletId) {
      missingFields.push("Outlet ID");
    }

    if (!role) {
      missingFields.push("Role");
    }

    if (!password) {
      missingFields.push("Password");
    }

    if (!number) {
      missingFields.push("Phone Number");
    }

    if (missingFields.length > 0) {
      const errorMessage = `Please fill in the following information:\n\n${missingFields.join("\n")}`;
      Alert.alert('Error', errorMessage, [{ text: 'OK', onPress: () => { } }], { cancelable: false });

    } else {
      if (password.length < 6) {
        Alert.alert(
          'Error',
          'Password must be equal or more than 6 characters',
          [{ text: 'OK', onPress: () => { } }],
          { cancelable: false },
        );
        return;
      }

      if (
        selectedOutletEmployeeEdit &&
        selectedOutletEmployeeEdit.firebaseUid
      ) {
        // pin unique check
        if (pin) {
          var userSnapshot = await firebase.firestore()
            .collection(Collections.User)
            .where('pinNo', '==', pin)
            .where('outletId', '==', currOutletId)
            .get();

          if (userSnapshot.size > 0) {
            var foundUser = userSnapshot.docs[0].data();

            if (foundUser.firebaseUid !== selectedOutletEmployeeEdit.firebaseUid) {
              // means not same user

              Alert.alert(
                'Error',
                'This pin number was already existed.',
                [{ text: 'OK', onPress: () => { } }],
                { cancelable: false },
              );
              return;
            }
          }
        }
        else {
          Alert.alert(
            'Info',
            'Please enter the pin number to proceed.',
            [{ text: 'OK', onPress: () => { } }],
            { cancelable: false },
          );
          return;
        }

        var employeeImagePath = '';
        var uniqueIdLocal = selectedOutletEmployeeEdit.employeeId;

        if (image && imageType && isImageChanged) {
          uniqueIdLocal = uuidv4();

          employeeImagePath = await uploadImageToFirebaseStorage(
            {
              uri: image,
              type: imageType,
            },
            `/merchant/${merchantId}/employee/${uniqueIdLocal}/image${imageType}`,
          );
        }

        if (!isImageChanged) {
          employeeImagePath = selectedOutletEmployeeEdit.avatar;
        }

        var body = {
          name,
          email,
          outletId: selectedOutletId,
          role,
          // password: password,
          number,
          merchantId,
          uniqueName: username,
          image: employeeImagePath,
          // dayOfWeek: moment(dayOfWeek).valueOf(),
          // regularTime: moment(regularTime).valueOf(),
          // overTime: moment(overTime).valueOf(),
          pinNo: pin,
          privileges,
          screensToBlock,

          uniqueIdLocal,

          outletEmployeeId: selectedOutletEmployeeEdit.firebaseUid,
        };
        // console.log('BODY', body);

        // ApiClient.POST(API.updateOutletEmployee, body)
        APILocal.updateOutletEmployee({ body, uid: userId })
          .then((result) => {
            // console.log('RESULT', result);

            // if (result.email.toLowerCase() == email.toLowerCase()) {
            if (result && result.status === 'success') {
              // emailFunc(email);

              Alert.alert(
                'Success',
                'Employee has been updated',
                [
                  {
                    text: 'OK',
                    onPress: () => {
                      // setState({ email: '' });
                      // setState({ password: '' });
                      // setState({ name: '' });
                      // setState({ pin: '' });
                      // setState({ waiter: '' });
                      // setState({ number: '' });
                      // setState({ role: '' });
                      // setState({ addEmployee: false })
                      setEmail('');
                      setPassword('');
                      setName('');
                      setNumber('');
                      setUsername('');
                      // setRole('');
                      setImage('');
                      setImageType('');
                      setDayOfWeek('');
                      setRegularTime('');
                      setOverTime('');

                      setPin('');
                      setPrivileges([]);
                      setScreensToBlock([]);

                      setAddEmployee(false);
                      setAddEmployeeItem(true);
                    },
                  },
                ],
                { cancelable: false },
              );
            } else {
              Alert.alert(
                'Error',
                'Failed to update employee',
                [
                  {
                    text: 'OK',
                    onPress: () => {
                      // setState({ email: '' });
                      // setState({ password: '' });
                      // setState({ name: '' });
                      // setState({ pin: '' });
                      // setState({ waiter: '' });
                      // setState({ number: '' });
                      // setState({ role: '' });
                    },
                  },
                ],
                { cancelable: false },
              );
            }
          });
      } else {
        // create new employee

        // email unique check
        if (email) {
          var userEmailSnapshot = await firebase.firestore()
            .collection(Collections.User)
            .where('email', '==', email)
            .get();

          if (userEmailSnapshot.size > 0) {
            Alert.alert(
              'Error',
              'This email was already used.',
              [{ text: 'OK', onPress: () => { } }],
              { cancelable: false },
            );
            return;
          }
        }

        // pin unique check
        if (pin) {
          var UserSnapshot = await firebase.firestore()
            .collection(Collections.User)
            .where('pinNo', '==', pin)
            .where('outletId', '==', currOutletId)
            .get();

          if (UserSnapshot.size > 0) {
            Alert.alert(
              'Error',
              'This pin number was already existed.',
              [{ text: 'OK', onPress: () => { } }],
              { cancelable: false },
            );
            return;
          }
        }
        else {
          Alert.alert(
            'Info',
            'Please enter the pin number to proceed.',
            [{ text: 'OK', onPress: () => { } }],
            { cancelable: false },
          );
          return;
        }

        var employeeImagePath = '';
        var uniqueIdLocal = '';

        if (image && imageType && isImageChanged) {
          uniqueIdLocal = uuidv4();

          employeeImagePath = await uploadImageToFirebaseStorage(
            {
              uri: image,
              type: imageType,
            },
            `/merchant/${merchantId}/employee/${uniqueIdLocal}/image${imageType}`,
          );
        }

        var body = {
          name,
          email,
          outletId,
          role,
          password,
          number,
          merchantId,
          uniqueName: username,
          image: employeeImagePath,
          // dayOfWeek: moment(dayOfWeek).valueOf(),
          // regularTime: moment(regularTime).valueOf(),
          // overTime: moment(overTime).valueOf(),
          pinNo: pin,
          privileges,
          screensToBlock,

          uniqueIdLocal,
        };
        // console.log('BODY', body);

        CommonStore.update(s => {
          s.isLoading = true;
        });

        ApiClient.POST(API.createOutletEmployee, body).then(async (result) => {
          // console.log('RESULT', result);

          // if (result.email.toLowerCase() == email.toLowerCase()) {
          if (result && result.status === 'success') {
            // emailFunc(email);

            Alert.alert(
              'Success',
              'Employee has been added',
              [
                {
                  text: 'OK',
                  onPress: () => {
                    // setState({ email: '' });
                    // setState({ password: '' });
                    // setState({ name: '' });
                    // setState({ pin: '' });
                    // setState({ waiter: '' });
                    // setState({ number: '' });
                    // setState({ role: '' });
                    // setState({ addEmployee: false })
                    setEmail('');
                    setPassword('');
                    setUsername('');
                    setName('');
                    setNumber('');
                    // setRole('');
                    setImage('');
                    setImageType('');
                    setDayOfWeek('');
                    setRegularTime('');
                    setOverTime('');

                    setPin('');
                    setPrivileges([]);
                    setScreensToBlock([]);

                    setAddEmployee(false);
                    setAddEmployeeItem(true);
                  },
                },
              ],
              { cancelable: false },
            );
          } else {
            // var apiErrorMsg = await AsyncStorage.getItem('api.error.msg');

            var apiErrorMsg = global['api.error.msg'];

            Alert.alert(
              'Error',
              apiErrorMsg ? apiErrorMsg : 'Failed to add employee',
              [
                {
                  text: 'OK',
                  onPress: () => {
                    // setState({ email: '' });
                    // setState({ password: '' });
                    // setState({ name: '' });
                    // setState({ pin: '' });
                    // setState({ waiter: '' });
                    // setState({ number: '' });
                    // setState({ role: '' });
                  },
                },
              ],
              { cancelable: false },
            );
          }

          CommonStore.update(s => {
            s.isLoading = false;
          });
        }, (result) => {
          CommonStore.update(s => {
            s.isLoading = false;
          });

          // console.log(result);
        }).catch(result => {
          CommonStore.update(s => {
            s.isLoading = false;
          });

          // console.log(result);
        });
      }
    }
  };

  const clockInEmployeeDateTime = async () => {
    var body = {
      clockInDate: moment(shiftDate).valueOf(),
      clockInTime: moment(shiftTime).valueOf(),
      employeeUserId: currEditEmployee.firebaseUid,
    };

    ApiClient.POST(API.clockInEmployeeDateTime, body).then((result) => {
      setClockInModal(false);
    });
  };

  const clockOutEmployeeDateTime = async () => {
    var body = {
      clockOutDate: moment(shiftDate).valueOf(),
      clockOutTime: moment(shiftTime).valueOf(),
      employeeUserId: currEditEmployee.firebaseUid,
    };

    ApiClient.POST(API.clockOutEmployeeDateTime, body).then((result) => {
      setClockOutModal(false);
    });
  };

  const deleteOutletEmployee = async () => {
    var body = {
      employeeUserId: selectedOutletEmployeeEdit.firebaseUid,
    };

    ApiClient.POST(API.deleteOutletEmployee, body).then((result) => {
      Alert.alert(
        'Success',
        'Employee has been removed',
        [{
          text: 'OK', onPress: () => {
            setAddEmployeeItem(true);
            setAddEmployee(false);
          }
        }],
        { cancelable: false },
      );
    });
  };

  const emailFunc = (email) => {
    var body = {
      data: `Your employee account has been created.\n The password is ${password}`,
      email,
    };
    ApiClient.POST(API.emailEmployee, body, false).then((result) => {
      if (result == true) {
        Alert.alert(
          'Success',
          'Email has sent',
          [{ text: 'OK', onPress: () => { } }],
          { cancelable: false },
        );
      }
    });
  };

  const removeEmployee = (employeeId) => {
    var body = {
      role: 'user',
    };
    ApiClient.POST(`${API.updateRole + employeeId}/role`, body).then(
      (result) => {
        Alert.alert(
          'Success',
          'Employee has been removed',
          [
            {
              text: 'OK',
              onPress: () => {
                // setState({ email: '' });
                // setState({ password: '' });
                // setState({ name: '' });
                // setState({ pin: '' });
                // setState({ waiter: '' });
                // setState({ number: '' });
                // setState({ role: '' });
              },
            },
          ],
          { cancelable: false },
        );
        getEmployeeList();
        setState({ showDetail: false, addEmployeeItem: true });
      },
    );
  };

  const searchBarItem = () => {
    ApiClient.GET(
      `${API.searchBarEmployee + search}&outletId=${outletId}`,
    ).then((result) => {
      setState({ lists: result });
    });
  };

  const renderSearchItem = ({ elements }) => {
    return (
      <View
        style={{
          backgroundColor: '#ffffff',
          flexDirection: 'row',
          paddingVertical: 20,
          paddingHorizontal: 20,
          borderBottomWidth: StyleSheet.hairlineWidth,
          borderBottomColor: '#c4c4c4',
        }}>
        <TouchableOpacity>
          <AsyncImage
            source={{ uri: elements.avatar }}
            item={elements}
            style={{ width: 20, height: 20, borderRadius: 10 }}
          />
          <Text
            style={{
              width: '2%',
              fontFamily: 'NunitoSans-Regular',
              fontSize: switchMerchant ? 10 : 14,
            }} />
          <Text
            style={{
              width: '14%',
              fontFamily: 'NunitoSans-Regular',
              fontSize: switchMerchant ? 10 : 14,
            }}>
            {elements.name}
          </Text>
          <Text
            style={{
              width: '15%',
              fontFamily: 'NunitoSans-Regular',
              fontSize: switchMerchant ? 10 : 14,
            }}>
            {elements.role}
          </Text>
          <Text
            style={{
              width: '13%',
              fontFamily: 'NunitoSans-Regular',
              fontSize: switchMerchant ? 10 : 14,
            }}>
            {elements.role}
          </Text>
          {/* <Text style={{ width: '15%' }}>{elements.number}</Text>
          <Text style={{ width: '16%', marginRight: 20 }}>{elements.email}</Text> */}
          <Text
            style={{
              width: '10%',
              fontFamily: 'NunitoSans-Regular',
              fontSize: switchMerchant ? 10 : 14,
            }}>
            ******
          </Text>
          <Text
            style={{
              width: '7%',
              fontFamily: 'NunitoSans-Regular',
              fontSize: switchMerchant ? 10 : 14,
            }}>
            ****
          </Text>
          <Text
            style={{
              width: '9%',
              fontFamily: 'NunitoSans-Regular',
              fontSize: switchMerchant ? 10 : 14,
            }}>
            {elements.dayOfWeek}
          </Text>
          <Text
            style={{
              width: '9%',
              fontFamily: 'NunitoSans-Regular',
              fontSize: switchMerchant ? 10 : 14,
            }}>
            {elements.regularTime}
          </Text>
          <Text
            style={{
              width: '9%',
              fontFamily: 'NunitoSans-Regular',
              fontSize: switchMerchant ? 10 : 14,
            }}>
            {elements.overTime}
          </Text>
        </TouchableOpacity>
      </View>
    );
  };

  const exportFunc = () => {
    // var body = {
    //   data: orderList
    // }

    if (allOutletsEmployees) {
      const csvData = convertArrayToCSV(allOutletsEmployees);

      const pathToWrite = `${RNFetchBlob.fs.dirs.DownloadDir
        }/koodoo-report-employee-${moment().format('YYYY-MM-DD-HH-mm-ss')}.csv`;
      // console.log('PATH', pathToWrite);
      RNFetchBlob.fs
        .writeFile(pathToWrite, csvData, 'utf8')
        .then(() => {
          // console.log(`wrote file ${pathToWrite}`);
          // wrote file /storage/emulated/0/Download/data.csv
          Alert.alert(
            'Success',
            `Sent to ${pathToWrite}`,
            [{ text: 'OK', onPress: () => { } }],
            { cancelable: false },
          );
        })
        .catch((error) => console.error(error));
    }
  };

  const handleChoosePhoto = () => {
    const imagePickerOptions = {
      mediaType: 'photo',
      quality: 0.5,
      includeBase64: false,
      maxWidth: 512,
      maxHeight: 512,
    };

    launchImageLibrary(imagePickerOptions, (response) => {
      if (response.didCancel) {
      } else if (response.error) {
        Alert.alert(response.error.toString());
      } else {
        const responseParsed = parseImagePickerResponse(response);

        // setState({ image: response.uri });
        setImage(responseParsed.uri);
        // console.log('response.uri: ', response.uri);
        setImageType(responseParsed.uri.slice(responseParsed.uri.lastIndexOf('.')));
        // console.log(
        //   `response.uri.slice(response.uri.lastIndexOf('.')): `,
        //   response.uri.slice(response.uri.lastIndexOf('.')),
        // );

        setIsImageChanged(false);
      }
    });
  };

  const convertDataToExcelFormat = () => {
    var excelData = [];

    for (var i = 0; i < allOutletsEmployees.length; i++) {
      var excelRow = {
        Name: allOutletsEmployees[i].name ? allOutletsEmployees[i].name : 'N/A',
        // 'Outlet': outletSupplyItems[i].skuMerchant ? outletSupplyItems[i].skuMerchant : 'N/A',
        // 'Staff': parseFloat(outletSupplyItems[i].quantity).toFixed(2),
        // 'Status': outletSupplyItems[i].unit ? outletSupplyItems[i].unit : 'N/A',
        // 'Clock In': parseFloat(outletSupplyItems[i].stockIdealQuantity).toFixed(2),
      };

      excelData.push(excelRow);
    }

    // console.log('excelData');
    // console.log(excelData);

    return excelData;
  };

  // function end

  return (
    // <View style={styles.container}>
    //   <View style={styles.sidebar}>
    (<UserIdleWrapper disabled={!isMounted}>
      <View
        style={[
          styles.container,
          !isTablet()
            ? {
              transform: [{ scaleX: 1 }, { scaleY: 1 }],
            }
            : {},
          {
            ...getTransformForScreenInsideNavigation(),
          }
        ]}>
        {/* <View
          style={[
            styles.sidebar,
            !isTablet()
              ? {
                width: windowWidth * 0.08,
              }
              : {},
            switchMerchant
              ? {
                // width: '10%'
              }
              : {},
            {
              width: windowWidth * 0.08,
            }
          ]}>
          <SideBar
            navigation={props.navigation}
            selectedTab={9}
            expandEmployees
          />
        </View> */}

        {/************ Test clocked in time modals************/}
        <ScrollView
          style={{ backgroundColor: Colors.highlightColor }}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={{
            paddingBottom: windowHeight * 0.1,
            backgroundColor: Colors.highlightColor,
          }}>
          <ScrollView horizontal>
            <DateTimePickerModal
              date={new Date()}
              minimumDate={new Date()}
              isVisible={showDateTimePicker}
              mode={'date'}
              //display={"default"} //for iOS to use minuteInterval
              onConfirm={(text) => {
                var date_ob = new Date(text);
                let date = (`0${date_ob.getDate()}`).slice(-2);
                let month = (`0${date_ob.getMonth() + 1}`).slice(-2);
                let year = date_ob.getFullYear();
                if (pickDate == 'dayOfWeek') {
                  // setState({ startDate: year + "-" + month + "-" + date })
                  setDayOfWeek(`${year}-${month}-${date}`);
                }
                // setState({ showDateTimePicker: false })
                setShowDateTimePicker(false);
              }}
              onCancel={() => {
                // setState({ showDateTimePicker: false })
                setShowDateTimePicker(false);
              }}
            />

            <DateTimePickerModal
              isVisible={showDateTimePicker1}
              mode={'time'}
              onConfirm={(text) => {
                if (pickTime == 'regularTime') {
                  setRegularTime(text);
                } else {
                  setOverTime(text);
                }
                setShowDateTimePicker1(false);
              }}
              onCancel={() => {
                setShowDateTimePicker1(false);
              }}
            />
            {/************ Test clocked in time modals************/}

            <View
              style={{
                // padding: 20,
                // width: switchMerchant
                //   ? windowWidth * 0.8
                //   : windowWidth * (1 - Styles.sideBarWidth),
                // height: windowHeight * 0.8,
                // backgroundColor: Colors.highlightColor,
                // marginHorizontal: switchMerchant ? 30 : 0,
                padding: 20,
                width: Dimensions.get('window').width * (1 - Styles.sideBarWidth),
                backgroundColor: Colors.highlightColor,
              }}>
              <ModalView
                supportedOrientations={['landscape', 'portrait']}
                style={{ flex: 1 }}
                visible={visible}
                transparent
                animationType="slide">
                <View
                  style={{
                    backgroundColor: 'rgba(0,0,0,0.5)',
                    flex: 1,
                    justifyContent: 'center',
                    alignItems: 'center',
                    minHeight: windowHeight,
                  }}>
                  <View style={[styles.confirmBoxRemove, { ...getTransformForModalInsideNavigation(), }]}>
                    <Text
                      style={{
                        fontWeight: '700',
                        fontSize: 24,
                        justifyContent: 'center',
                        alignSelf: 'center',
                        marginTop: 40,
                      }}>
                      Remove employee
                    </Text>
                    <View
                      style={{
                        justifyContent: 'center',
                        alignSelf: 'center',
                        alignContent: 'center',
                        marginTop: 20,
                        flexDirection: 'row',
                        width: '80%',
                      }}>
                      <View
                        style={{ justifyContent: 'center', marginHorizontal: 5 }}>
                        <Text
                          style={{
                            color: Colors.descriptionColor,
                            fontSize: 20,
                            marginBottom: 5,
                          }}>
                          Are you sure you want to remove this employee?
                        </Text>
                        <Text
                          style={{ color: Colors.descriptionColor, fontSize: 16 }}>
                          Name: {showEmployee.name}
                        </Text>
                        <Text
                          style={{ color: Colors.descriptionColor, fontSize: 16 }}>
                          Position: {showEmployee.role}
                        </Text>
                      </View>
                    </View>

                    <View
                      style={{
                        alignSelf: 'center',
                        marginTop: 20,
                        justifyContent: 'center',
                        alignItems: 'center',
                        width: 260,
                        height: 40,
                        alignContent: 'center',
                        flexDirection: 'row',
                        marginTop: 40,
                      }}>
                      <TouchableOpacity
                        onPress={() => {
                          removeEmployee(showEmployee.id),
                            setState({ visible: false });
                        }}
                        style={{
                          backgroundColor: Colors.fieldtBgColor,
                          width: '100%',
                          justifyContent: 'center',
                          alignItems: 'center',
                          alignContent: 'center',
                          height: 80,
                          borderBottomLeftRadius: 10,
                          borderRightWidth: StyleSheet.hairlineWidth,
                          borderTopWidth: StyleSheet.hairlineWidth,
                        }}>
                        <Text style={{ fontSize: 22, color: Colors.primaryColor }}>
                          Confirm
                        </Text>
                      </TouchableOpacity>
                      <TouchableOpacity
                        onPress={() => {
                          setState({ visible: false });
                        }}
                        style={{
                          backgroundColor: Colors.fieldtBgColor,
                          width: '100%',
                          justifyContent: 'center',
                          alignItems: 'center',
                          alignContent: 'center',
                          height: 80,
                          borderBottomRightRadius: 10,
                          borderTopWidth: StyleSheet.hairlineWidth,
                        }}>
                        <Text
                          style={{ fontSize: 22, color: Colors.descriptionColor }}>
                          Cancel
                        </Text>
                      </TouchableOpacity>
                    </View>
                  </View>
                </View>
              </ModalView>

              {/****************************************** DOWNLOAD *******************************************/}
              <ModalView
                style={
                  {
                    // flex: 1
                  }
                }
                visible={exportModalVisibility}
                supportedOrientations={['portrait', 'landscape']}
                transparent
                animationType={'fade'}>
                <View
                  style={{
                    flex: 1,
                    backgroundColor: Colors.modalBgColor,
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}>
                  <View
                    style={{
                      height: windowWidth * 0.3,
                      width: windowWidth * 0.4,
                      backgroundColor: Colors.whiteColor,
                      borderRadius: 12,
                      padding: windowWidth * 0.03,
                      alignItems: 'center',
                      justifyContent: 'center',
                      ...getTransformForModalInsideNavigation(),
                    }}>
                    <TouchableOpacity
                      disabled={isLoading}
                      style={{
                        position: 'absolute',
                        right: windowWidth * 0.02,
                        top: windowWidth * 0.02,

                        elevation: 1000,
                        zIndex: 1000,
                      }}
                      onPress={() => {
                        setExportModalVisibility(false);
                      }}>
                      <AntDesign
                        name="closecircle"
                        size={switchMerchant ? 15 : 25}
                        color={Colors.fieldtTxtColor}
                      />
                    </TouchableOpacity>
                    <View
                      style={{
                        alignItems: 'center',
                        top: '20%',
                        position: 'absolute',
                      }}>
                      <Text
                        style={{
                          fontFamily: 'NunitoSans-Bold',
                          textAlign: 'center',
                          fontSize: switchMerchant ? 16 : 24,
                        }}>
                        Download Report
                      </Text>
                    </View>
                    <View style={{ top: '10%' }}>
                      <Text
                        style={{
                          fontSize: switchMerchant ? 12 : 20,
                          fontFamily: 'NunitoSans-Bold',
                        }}>
                        Email Address:
                      </Text>
                      <TextInput
                        style={{
                          backgroundColor: Colors.fieldtBgColor,
                          width: switchMerchant ? 260 : 370,
                          height: switchMerchant ? 35 : 50,
                          borderRadius: 5,
                          padding: 5,
                          marginVertical: 5,
                          borderWidth: 1,
                          borderColor: '#E5E5E5',
                          paddingLeft: 10,
                        }}
                        autoCapitalize='none'
                        placeholderStyle={{ padding: 5 }}
                        placeholder="Enter your email"
                        placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                        onChangeText={(text) => {
                          setExportEmail(text);

                          logEventAnalytics({
                            eventName: ANALYTICS.MODULE_EMPLOYEE_MANAGE_EMPLOYEE_DL_BTN_TB_EMAIL,
                            eventNameParsed: ANALYTICS_PARSED.MODULE_EMPLOYEE_MANAGE_EMPLOYEE_DL_BTN_TB_EMAIL
                          })
                        }}
                        value={exportEmail}
                      />
                      <Text
                        style={{
                          fontSize: switchMerchant ? 12 : 20,
                          fontFamily: 'NunitoSans-Bold',
                          marginTop: 15,
                        }}>
                        Send As:
                      </Text>

                      <View
                        style={{
                          alignItems: 'center',
                          justifyContent: 'center',
                          //top: '10%',
                          flexDirection: 'row',
                          marginTop: 15,
                        }}>
                        <TouchableOpacity
                          disabled={isLoading}
                          style={{
                            justifyContent: 'center',
                            flexDirection: 'row',
                            borderWidth: 1,
                            borderColor: Colors.primaryColor,
                            backgroundColor: '#0A1F44',
                            borderRadius: 5,
                            width: switchMerchant ? 80 : 100,
                            paddingHorizontal: 10,
                            height: switchMerchant ? 30 : 40,
                            alignItems: 'center',
                            shadowOffset: {
                              width: 0,
                              height: 2,
                            },
                            shadowOpacity: 0.22,
                            shadowRadius: 3.22,
                            elevation: 1,
                            zIndex: -1,
                          }}
                          onPress={() => {
                            if (exportEmail.length > 0) {
                              CommonStore.update((s) => {
                                s.isLoading = true;
                              });
                              setIsLoadingExcel(true);
                              const excelData = convertDataToExcelFormat();

                              generateEmailReport(
                                EMAIL_REPORT_TYPE.EXCEL,
                                excelData,
                                'KooDoo Employee Report',
                                'KooDoo Employee Report.xlsx',
                                `/merchant/${merchantId}/reports/${uuidv4()}.xlsx`,
                                exportEmail,
                                'KooDoo Employee Report',
                                'KooDoo Employee Report',
                                () => {
                                  CommonStore.update((s) => {
                                    s.isLoading = false;
                                  });
                                  setIsLoadingExcel(false);

                                  Alert.alert(
                                    'Success',
                                    'Report will be sent to the email address shortly',
                                  );

                                  setExportModalVisibility(false);

                                  logEventAnalytics({
                                    eventName: ANALYTICS.MODULE_EMPLOYEE_MANAGE_EMPLOYEE_DL_BTN_C_REP_EXCEL,
                                    eventNameParsed: ANALYTICS_PARSED.MODULE_EMPLOYEE_MANAGE_EMPLOYEE_DL_BTN_C_REP_EXCEL
                                  })
                                },
                              );
                            } else {
                              Alert.alert('Info', 'Invalid email address');
                            }
                          }}>
                          {isLoadingExcel ? (
                            <ActivityIndicator
                              size={'small'}
                              color={Colors.whiteColor}
                            />
                          ) : (
                            <Text
                              style={{
                                color: Colors.whiteColor,
                                //marginLeft: 5,
                                fontSize: switchMerchant ? 10 : 16,
                                fontFamily: 'NunitoSans-Bold',
                              }}>
                              EXCEL
                            </Text>
                          )}
                        </TouchableOpacity>

                        <TouchableOpacity
                          disabled={isLoading}
                          style={{
                            justifyContent: 'center',
                            flexDirection: 'row',
                            borderWidth: 1,
                            borderColor: Colors.primaryColor,
                            backgroundColor: '#0A1F44',
                            borderRadius: 5,
                            width: switchMerchant ? 80 : 100,
                            paddingHorizontal: 10,
                            height: switchMerchant ? 30 : 40,
                            alignItems: 'center',
                            shadowOffset: {
                              width: 0,
                              height: 2,
                            },
                            shadowOpacity: 0.22,
                            shadowRadius: 3.22,
                            elevation: 1,
                            zIndex: -1,
                            marginLeft: 15,
                          }}
                          onPress={() => {
                            if (exportEmail.length > 0) {
                              CommonStore.update((s) => {
                                s.isLoading = true;
                              });
                              setIsLoadingCsv(true);
                              const csvData =
                                convertArrayToCSV(allOutletsEmployees);

                              generateEmailReport(
                                EMAIL_REPORT_TYPE.CSV,
                                csvData,
                                'KooDoo Employee Report',
                                'KooDoo Employee Report.csv',
                                `/merchant/${merchantId}/reports/${uuidv4()}.csv`,
                                exportEmail,
                                'KooDoo Employee Report',
                                'KooDoo Employee Report',
                                () => {
                                  CommonStore.update((s) => {
                                    s.isLoading = false;
                                  });
                                  setIsLoadingCsv(false);
                                  Alert.alert(
                                    'Success',
                                    'Report will be sent to the email address shortly',
                                  );

                                  setExportModalVisibility(false);
                                },
                              );
                            } else {
                              Alert.alert('Info', 'Invalid email address');
                            }

                            logEventAnalytics({
                              eventName: ANALYTICS.MODULE_EMPLOYEE_MANAGE_EMPLOYEE_DL_BTN_C_REP_CSV,
                              eventNameParsed: ANALYTICS_PARSED.MODULE_EMPLOYEE_MANAGE_EMPLOYEE_DL_BTN_C_REP_CSV
                            })
                          }}>
                          {isLoadingCsv ? (
                            <ActivityIndicator
                              size={'small'}
                              color={Colors.whiteColor}
                            />
                          ) : (
                            <Text
                              style={{
                                color: Colors.whiteColor,
                                //marginLeft: 5,
                                fontSize: switchMerchant ? 10 : 16,
                                fontFamily: 'NunitoSans-Bold',
                              }}>
                              CSV
                            </Text>
                          )}
                        </TouchableOpacity>

                        {/* <TouchableOpacity
                            style={[styles.modalSaveButton, {
                                zIndex: -1
                            }]}
                            onPress={() => { downloadPDF() }}>
                            <Text style={[styles.modalDescText, { color: Colors.primaryColor }]}>PDF</Text>
                        </TouchableOpacity> */}
                      </View>
                    </View>
                  </View>
                </View>
              </ModalView>

              {/****************************************** Clock In *******************************************/}
              <ModalView
                style={
                  {
                    // flex: 1
                  }
                }
                visible={clockInModal}
                transparent
                animationType={'slide'}
                supportedOrientations={['portrait', 'landscape']}>
                <DateTimePickerModal
                  isVisible={showShiftDatePicker}
                  mode={'date'}
                  onConfirm={(text) => {
                    setShiftDate(moment(text));

                    setShowShiftDatePicker(false);
                  }}
                  onCancel={() => {
                    setShowShiftDatePicker(false);
                  }}
                />

                <DateTimePickerModal
                  isVisible={showShiftTimePicker}
                  mode={'time'}
                  onConfirm={(text) => {
                    setShiftTime(moment(text));

                    setShowShiftTimePicker(false);
                  }}
                  onCancel={() => {
                    setShowShiftTimePicker(false);
                  }}
                />
                <View
                  style={{
                    flex: 1,
                    backgroundColor: Colors.modalBgColor,
                    alignItems: 'center',
                    justifyContent: 'center',

                    top:
                      Platform.OS === 'android'
                        ? 0
                        : keyboardHeight > 0
                          ? -keyboardHeight * 0.45
                          : 0,
                  }}>
                  <View
                    style={{
                      height: windowWidth * 0.2,
                      width: windowWidth * 0.4,
                      backgroundColor: Colors.whiteColor,
                      borderRadius: 12,
                      padding: windowWidth * 0.05,
                      alignItems: 'center',
                      justifyContent: 'center',

                      // top: keyboardHeight > 0 ? -keyboardHeight * 0.45 : 0,
                      ...getTransformForModalInsideNavigation(),
                    }}>
                    <TouchableOpacity
                      style={{
                        position: 'absolute',
                        right: windowWidth * 0.02,
                        top: windowWidth * 0.02,

                        elevation: 1000,
                        zIndex: 1000,
                      }}
                      onPress={() => {
                        setClockInModal(false);
                      }}>
                      <AntDesign
                        name="closecircle"
                        size={switchMerchant ? 15 : 25}
                        color={Colors.fieldtTxtColor}
                      />
                    </TouchableOpacity>
                    <View
                      style={{
                        alignItems: 'center',
                        top: '20%',
                        position: 'absolute',
                      }}>
                      <Text
                        style={{
                          fontFamily: 'NunitoSans-Bold',
                          textAlign: 'center',
                          fontSize: switchMerchant ? 16 : 24,
                        }}>
                        Clock In
                      </Text>
                    </View>
                    <View
                      style={{
                        flexDirection: 'row',
                        flexDirection: 'row',
                        marginTop: 15,
                        justifyContent: 'center',
                      }}>
                      <View style={{ flexDirection: 'column' }}>
                        <Text
                          style={{
                            fontSize: switchMerchant ? 11 : 14,
                            fontFamily: 'NunitoSans-Regular',
                            fontWeight: '500',
                          }}>
                          Date
                        </Text>
                        <TouchableOpacity
                          onPress={() => {
                            setShowShiftDatePicker(true);
                          }}
                          style={{
                            // height: 50,
                            height: switchMerchant ? 35 : 40,
                            paddingHorizontal: 20,
                            backgroundColor: Colors.fieldtBgColor,
                            //marginBottom: 20,
                            width: switchMerchant ? 140 : 160,
                            //marginHorizontal: 10,
                            flexDirection: 'row',
                            alignItems: 'center',
                            justifyContent: 'space-between',
                            alignContent: 'center',
                            borderColor: '#E5E5E5',
                            borderWidth: 1,
                            fontFamily: 'NunitoSans-Regular',
                            fontSize: switchMerchant ? 11 : 14,
                            borderRadius: 12,
                            marginTop: switchMerchant ? 5 : 10,
                          }}>
                          <GCalendar
                            width={20}
                            height={20}
                            style={{ marginRight: 0 }}
                          />
                          <Text
                            style={{
                              //marginLeft: 15,
                              color: '#B6B6B6',
                              fontSize: switchMerchant ? 11 : 14,
                              fontFamily: 'NunitoSans-Regular',
                            }}>
                            {moment(shiftDate).format('DD MMM YYYY')}
                          </Text>
                        </TouchableOpacity>
                      </View>

                      <View style={{ flexDirection: 'column', marginLeft: 15 }}>
                        <Text
                          style={{
                            fontSize: switchMerchant ? 11 : 14,
                            fontFamily: 'NunitoSans-Regular',
                            fontWeight: '500',
                          }}>
                          Time
                        </Text>
                        <TouchableOpacity
                          onPress={() => {
                            setShowShiftTimePicker(true);
                          }}
                          style={{
                            // height: 50,
                            height: switchMerchant ? 35 : 40,
                            paddingHorizontal: 20,
                            backgroundColor: Colors.fieldtBgColor,
                            //marginBottom: 20,
                            width: switchMerchant ? 140 : 160,
                            //marginHorizontal: 10,
                            flexDirection: 'row',
                            alignItems: 'center',
                            justifyContent: 'space-between',
                            alignContent: 'center',
                            borderColor: '#E5E5E5',
                            borderWidth: 1,
                            fontFamily: 'NunitoSans-Regular',
                            fontSize: switchMerchant ? 11 : 14,
                            borderRadius: 12,
                            marginTop: switchMerchant ? 5 : 10,
                          }}>
                          <EvilIcons
                            name="clock"
                            size={25}
                            color={Colors.primaryColor}
                            style={{ marginRight: 0 }}
                          />
                          <Text
                            style={{
                              //marginLeft: 15,
                              color: '#B6B6B6',
                              fontSize: switchMerchant ? 11 : 14,
                              fontFamily: 'NunitoSans-Regular',
                            }}>
                            {moment(shiftTime).format('hh:mm A')}
                          </Text>
                        </TouchableOpacity>
                      </View>
                    </View>
                    <View
                      style={{
                        flexDirection: 'row',
                        justifyContent: 'center',
                        flexDirection: 'row',
                        marginTop: '5%',
                      }}>
                      <TouchableOpacity
                        style={{
                          justifyContent: 'center',
                          flexDirection: 'row',
                          borderWidth: 1,
                          borderColor: Colors.primaryColor,
                          backgroundColor: '#0A1F44',
                          borderRadius: 5,
                          //width: 200,
                          paddingHorizontal: 10,
                          height: switchMerchant ? 35 : 40,
                          alignItems: 'center',
                          shadowOffset: {
                            width: 0,
                            height: 2,
                          },
                          shadowOpacity: 0.22,
                          shadowRadius: 3.22,
                          elevation: 1,
                          zIndex: -1,
                          marginRight: switchMerchant ? 10 : 15,
                        }}
                        onPress={() => {
                          clockInEmployeeDateTime();
                        }}>
                        <Text
                          style={{
                            color: 'white',
                            fontSize: switchMerchant ? 10 : 16,
                            fontFamily: 'NunitoSans-Bold',
                          }}>
                          UPDATE
                        </Text>
                      </TouchableOpacity>
                    </View>
                  </View>
                </View>
              </ModalView>

              {/****************************************** Clock Out *******************************************/}
              <ModalView
                style={
                  {
                    // flex: 1
                  }
                }
                visible={clockOutModal}
                transparent
                animationType={'slide'}
                supportedOrientations={['portrait', 'landscape']}>
                <DateTimePickerModal
                  isVisible={showShiftDatePicker}
                  mode={'date'}
                  onConfirm={(text) => {
                    setShiftDate(moment(text));

                    setShowShiftDatePicker(false);
                  }}
                  onCancel={() => {
                    setShowShiftDatePicker(false);
                  }}
                />

                <DateTimePickerModal
                  isVisible={showShiftTimePicker}
                  mode={'time'}
                  onConfirm={(text) => {
                    setShiftTime(moment(text));

                    setShowShiftTimePicker(false);
                  }}
                  onCancel={() => {
                    setShowShiftTimePicker(false);
                  }}
                />
                <View
                  style={{
                    flex: 1,
                    backgroundColor: Colors.modalBgColor,
                    alignItems: 'center',
                    justifyContent: 'center',

                    top:
                      Platform.OS === 'android'
                        ? 0
                        : keyboardHeight > 0
                          ? -keyboardHeight * 0.45
                          : 0,
                  }}>
                  <View
                    style={{
                      height: windowWidth * 0.2,
                      width: windowWidth * 0.4,
                      backgroundColor: Colors.whiteColor,
                      borderRadius: 12,
                      padding: windowWidth * 0.05,
                      alignItems: 'center',
                      justifyContent: 'center',

                      // top: keyboardHeight > 0 ? -keyboardHeight * 0.45 : 0,
                      ...getTransformForModalInsideNavigation(),
                    }}>
                    <TouchableOpacity
                      style={{
                        position: 'absolute',
                        right: windowWidth * 0.02,
                        top: windowWidth * 0.02,

                        elevation: 1000,
                        zIndex: 1000,
                      }}
                      onPress={() => {
                        setClockOutModal(false);
                      }}>
                      <AntDesign
                        name="closecircle"
                        size={switchMerchant ? 15 : 25}
                        color={Colors.fieldtTxtColor}
                      />
                    </TouchableOpacity>
                    <View
                      style={{
                        alignItems: 'center',
                        top: '20%',
                        position: 'absolute',
                      }}>
                      <Text
                        style={{
                          fontFamily: 'NunitoSans-Bold',
                          textAlign: 'center',
                          fontSize: switchMerchant ? 16 : 24,
                        }}>
                        Clock Out
                      </Text>
                    </View>
                    <View
                      style={{
                        flexDirection: 'row',
                        flexDirection: 'row',
                        marginTop: 15,
                        justifyContent: 'center',
                      }}>
                      <View style={{ flexDirection: 'column' }}>
                        <Text
                          style={{
                            fontSize: switchMerchant ? 11 : 14,
                            fontFamily: 'NunitoSans-Regular',
                            fontWeight: '500',
                          }}>
                          Date
                        </Text>
                        <TouchableOpacity
                          onPress={() => {
                            setShowShiftDatePicker(true);
                          }}
                          style={{
                            // height: 50,
                            height: switchMerchant ? 35 : 40,
                            paddingHorizontal: 20,
                            backgroundColor: Colors.fieldtBgColor,
                            //marginBottom: 20,
                            width: switchMerchant ? 140 : 160,
                            //marginHorizontal: 10,
                            flexDirection: 'row',
                            alignItems: 'center',
                            justifyContent: 'space-between',
                            alignContent: 'center',
                            borderColor: '#E5E5E5',
                            borderWidth: 1,
                            fontFamily: 'NunitoSans-Regular',
                            fontSize: switchMerchant ? 11 : 14,
                            borderRadius: 12,
                            marginTop: switchMerchant ? 5 : 10,
                          }}>
                          <GCalendar
                            width={20}
                            height={20}
                            style={{ marginRight: 0 }}
                          />
                          <Text
                            style={{
                              //marginLeft: 15,
                              color: '#B6B6B6',
                              fontSize: switchMerchant ? 11 : 14,
                              fontFamily: 'NunitoSans-Regular',
                            }}>
                            {moment(shiftDate).format('DD MMM YYYY')}
                          </Text>
                        </TouchableOpacity>
                      </View>

                      <View style={{ flexDirection: 'column', marginLeft: 15 }}>
                        <Text
                          style={{
                            fontSize: switchMerchant ? 11 : 14,
                            fontFamily: 'NunitoSans-Regular',
                            fontWeight: '500',
                          }}>
                          Time
                        </Text>
                        <TouchableOpacity
                          onPress={() => {
                            setShowShiftTimePicker(true);
                          }}
                          style={{
                            // height: 50,
                            height: switchMerchant ? 35 : 40,
                            paddingHorizontal: 20,
                            backgroundColor: Colors.fieldtBgColor,
                            //marginBottom: 20,
                            width: switchMerchant ? 140 : 160,
                            //marginHorizontal: 10,
                            flexDirection: 'row',
                            alignItems: 'center',
                            justifyContent: 'space-between',
                            alignContent: 'center',
                            borderColor: '#E5E5E5',
                            borderWidth: 1,
                            fontFamily: 'NunitoSans-Regular',
                            fontSize: switchMerchant ? 11 : 14,
                            borderRadius: 12,
                            marginTop: switchMerchant ? 5 : 10,
                          }}>
                          <EvilIcons
                            name="clock"
                            size={25}
                            color={Colors.primaryColor}
                            style={{ marginRight: 0 }}
                          />
                          <Text
                            style={{
                              //marginLeft: 15,
                              color: '#B6B6B6',
                              fontSize: switchMerchant ? 11 : 14,
                              fontFamily: 'NunitoSans-Regular',
                            }}>
                            {moment(shiftTime).format('hh:mm A')}
                          </Text>
                        </TouchableOpacity>
                      </View>
                    </View>
                    <View
                      style={{
                        flexDirection: 'row',
                        justifyContent: 'center',
                        flexDirection: 'row',
                        marginTop: 15,
                      }}>
                      <TouchableOpacity
                        style={{
                          justifyContent: 'center',
                          flexDirection: 'row',
                          borderWidth: 1,
                          borderColor: Colors.primaryColor,
                          backgroundColor: '#0A1F44',
                          borderRadius: 5,
                          //width: 200,
                          paddingHorizontal: 10,
                          height: switchMerchant ? 35 : 40,
                          alignItems: 'center',
                          shadowOffset: {
                            width: 0,
                            height: 2,
                          },
                          shadowOpacity: 0.22,
                          shadowRadius: 3.22,
                          elevation: 1,
                          zIndex: -1,
                          marginRight: switchMerchant ? 10 : 15,
                        }}
                        onPress={() => {
                          clockOutEmployeeDateTime();
                        }}>
                        <Text
                          style={{
                            color: 'white',
                            fontSize: switchMerchant ? 10 : 16,
                            fontFamily: 'NunitoSans-Bold',
                          }}>
                          UPDATE
                        </Text>
                      </TouchableOpacity>
                    </View>
                  </View>
                </View>
              </ModalView>

              {addEmployeeItem ? (
                <View
                  style={{
                    flexDirection: 'row',
                    marginBottom: 0,
                    alignSelf: 'center',
                    width: windowWidth * 0.87,
                    marginHorizontal: switchMerchant ? 30 : 0,
                    //paddingHorizontal: switchMerchant ? 30 : 0,
                  }}>
                  <View
                    style={{
                      flexDirection: 'row',
                      justifyContent: 'space-between',
                      width: '100%',
                    }}>
                    <View style={{ alignItems: 'center', flexDirection: 'row' }}>
                      <Text
                        style={{
                          fontSize: switchMerchant ? 20 : 26,
                          fontFamily: 'NunitoSans-Bold',
                        }}>
                        {allOutletsEmployees.length}
                      </Text>
                      <Text
                        style={{
                          fontSize: switchMerchant ? 20 : 26,
                          fontFamily: 'NunitoSans-Bold',
                          marginLeft: 10,
                        }}>
                        {allOutletsEmployees.length > 1
                          ? 'Employees'
                          : 'Employee'}
                      </Text>
                    </View>
                    <View style={{ flexDirection: 'row' }}>
                      <TouchableOpacity
                        style={{
                          justifyContent: 'center',
                          flexDirection: 'row',
                          borderWidth: 1,
                          borderColor: Colors.primaryColor,
                          backgroundColor: '#0A1F44',
                          borderRadius: 5,
                          //width: 200,
                          paddingHorizontal: 10,
                          height: switchMerchant ? 35 : 40,
                          alignItems: 'center',
                          shadowOffset: {
                            width: 0,
                            height: 2,
                          },
                          shadowOpacity: 0.22,
                          shadowRadius: 3.22,
                          elevation: 1,
                          zIndex: -1,
                          marginRight: switchMerchant ? 10 : 15,
                        }}
                        onPress={() => {
                          setExportModalVisibility(true);

                          logEventAnalytics({
                            eventName: ANALYTICS.MODULE_EMPLOYEE_MANAGE_EMPLOYEE_C_DOWNLOAD_BTN,
                            eventNameParsed: ANALYTICS_PARSED.MODULE_EMPLOYEE_MANAGE_EMPLOYEE_C_DOWNLOAD_BTN
                          })
                        }}>
                        <View style={{ flexDirection: 'row' }}>
                          <View style={{ marginTop: switchMerchant ? 1 : 0 }}>
                            <Icon
                              name="download"
                              size={switchMerchant ? 10 : 20}
                              color={Colors.whiteColor}
                            />
                          </View>
                          <Text
                            style={{
                              color: Colors.whiteColor,
                              marginLeft: 5,
                              fontSize: switchMerchant ? 10 : 16,
                              fontFamily: 'NunitoSans-Bold',
                            }}>
                            DOWNLOAD
                          </Text>
                        </View>
                      </TouchableOpacity>
                      <TouchableOpacity
                        style={{
                          justifyContent: 'center',
                          flexDirection: 'row',
                          borderWidth: 1,
                          borderColor: Colors.primaryColor,
                          backgroundColor: '#0A1F44',
                          borderRadius: 5,
                          //width: 200,
                          paddingHorizontal: 10,
                          height: switchMerchant ? 35 : 40,
                          alignItems: 'center',
                          shadowOffset: {
                            width: 0,
                            height: 2,
                          },
                          shadowOpacity: 0.22,
                          shadowRadius: 3.22,
                          elevation: 1,
                          zIndex: -1,
                          marginRight: 15,
                        }}
                        onPress={() => {
                          // setState({
                          //   addEmployee: true,
                          //   addEmployeeItem: false,
                          // });
                          CommonStore.update((s) => {
                            s.selectedOutletEmployeeEdit = null;
                          });
                          setAddEmployee(true);
                          setAddEmployeeItem(false);

                          logEventAnalytics({
                            eventName: ANALYTICS.MODULE_EMPLOYEE_MANAGE_EMPLOYEE_C_NEW_EMPLOYEE,
                            eventNameParsed: ANALYTICS_PARSED.MODULE_EMPLOYEE_MANAGE_EMPLOYEE_C_NEW_EMPLOYEE
                          })
                        }}>
                        <View
                          style={{ flexDirection: 'row', alignItems: 'center' }}>
                          <AntDesign
                            name="pluscircle"
                            size={switchMerchant ? 10 : 20}
                            color={Colors.whiteColor}
                          />
                          <Text
                            style={{
                              color: Colors.whiteColor,
                              marginLeft: 5,
                              fontSize: switchMerchant ? 10 : 16,
                              fontFamily: 'NunitoSans-Bold',
                            }}>
                            EMPLOYEE
                          </Text>
                        </View>
                      </TouchableOpacity>
                      <View
                        style={{
                          width: switchMerchant ? 200 : 250,
                          height: switchMerchant ? 35 : 40,
                          backgroundColor: 'white',
                          borderRadius: 5,
                          flexDirection: 'row',
                          alignContent: 'center',
                          alignItems: 'center',
                          shadowColor: '#000',
                          shadowOffset: {
                            width: 0,
                            height: 2,
                          },
                          shadowOpacity: 0.22,
                          shadowRadius: 3.22,
                          elevation: 3,
                          borderWidth: 1,
                          borderColor: '#E5E5E5',
                        }}>
                        <Icon
                          name="search"
                          size={switchMerchant ? 13 : 18}
                          color={Colors.primaryColor}
                          style={{ marginLeft: 15 }}
                        />
                        <TextInput
                          editable={!loading}
                          underlineColorAndroid={Colors.whiteColor}
                          style={{
                            width: switchMerchant ? 150 : 220,
                            fontSize: switchMerchant ? 10 : 16,
                            fontFamily: 'NunitoSans-Regular',
                            paddingLeft: 5,
                            height: 45,
                          }}
                          clearButtonMode="while-editing"
                          placeholder=" Search"
                          onChangeText={(text) => {
                            // setSearch(text.trim());
                            // setList1(false);
                            // setSearchList(true);
                            // setSearch(text.trim());
                            setSearch(text);

                            logEventAnalytics({
                              eventName: ANALYTICS.MODULE_EMPLOYEE_MANAGE_EMPLOYEE_TB_SEARCH,
                              eventNameParsed: ANALYTICS_PARSED.MODULE_EMPLOYEE_MANAGE_EMPLOYEE_TB_SEARCH
                            })
                          }}
                          placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                          value={search}
                        />
                      </View>
                    </View>
                  </View>
                </View>
              ) : (
                <></>
              )}

              {addEmployeeItem ? (
                <View
                  style={{
                    height: windowHeight * 0.8,
                    width: windowWidth * 0.87,
                    alignSelf: 'center',
                    backgroundColor: 'white',
                    borderRadius: 5,
                    marginTop: 15,
                    shadowColor: '#000',
                    shadowOffset: {
                      width: 0,
                      height: 2,
                    },
                    shadowOpacity: 0.34,
                    shadowRadius: 3.32,

                    elevation: 3,
                    marginHorizontal: switchMerchant ? 30 : 0,
                  }}>
                  <View
                    style={[
                      styles.titleList,
                      { paddingHorizontal: 10, marginTop: 0, borderRadius: 5 },
                    ]}>
                    <Text
                      style={{
                        width: 35,
                        marginRight: 15,
                        alignSelf: 'center',
                      }} />
                    <Text
                      style={{
                        width: switchMerchant ? '10%' : '12%',
                        alignSelf: 'center',
                        marginRight: 5,
                        fontFamily: 'NunitoSans-Regular',
                        fontSize: switchMerchant ? 10 : 14,
                      }}>
                      Name
                    </Text>
                    <Text
                      style={{
                        width: '20%', //15%
                        alignSelf: 'center',
                        marginRight: 5,
                        fontFamily: 'NunitoSans-Regular',
                        fontSize: switchMerchant ? 10 : 14,
                      }}>
                      Outlet
                    </Text>
                    <Text
                      style={{
                        width: switchMerchant ? '10%' : '14%',
                        alignSelf: 'center',
                        marginRight: 5,
                        fontFamily: 'NunitoSans-Regular',
                        fontSize: switchMerchant ? 10 : 14,
                      }}>
                      Staff
                    </Text>
                    <Text
                      style={{
                        width: switchMerchant ? '10%' : '10%',
                        alignSelf: 'center',
                        marginRight: 5,
                        fontFamily: 'NunitoSans-Regular',
                        fontSize: switchMerchant ? 10 : 14,
                      }}>
                      Status
                    </Text>
                    {/* <Text style={{ width: '13%', alignSelf: 'center' }}>
                Contact No
              </Text>
              <Text style={{ width: '17.5%', alignSelf: 'center' }}>
                Email
              </Text> */}
                    {/* <Text style={{ width: '11%', alignSelf: 'center' }}>
                Password
              </Text>
              <Text style={{ width: '7%', alignSelf: 'center' }}>Pin </Text> */}

                    {/* <Text style={{ width: '9%', alignSelf: 'center' }}>
              Day of Week
            </Text>
            <Text style={{ width: '9%', alignSelf: 'center' }}>
              Regular Time
            </Text> */}
                    <Text
                      style={{
                        width: switchMerchant ? '13%' : '16%', //11%
                        alignSelf: 'center',
                        fontFamily: 'NunitoSans-Regular',
                        fontSize: switchMerchant ? 10 : 14,
                      }}>
                      Clock In
                    </Text>

                    <Text
                      style={{
                        width: '16%', //11%
                        alignSelf: 'center',
                        fontFamily: 'NunitoSans-Regular',
                        fontSize: switchMerchant ? 10 : 14,
                      }}>
                      Clock Out
                    </Text>
                  </View>
                  <View style={{ flex: 1 }}>
                    {list1 ? (
                      <FlatList
                        nestedScrollEnabled
                        showsVerticalScrollIndicator={false}
                        data={allOutletsEmployees.filter((item) => {
                          if (search !== '') {
                            const searchLowerCase = search.toLowerCase();

                            return (
                              item.email
                                .toLowerCase()
                                .includes(searchLowerCase) ||
                              item.name.toLowerCase().includes(searchLowerCase)
                            );
                          } else {
                            return true;
                          }
                        })}
                        extraData={allOutletsEmployees.filter((item) => {
                          if (search !== '') {
                            const searchLowerCase = search.toLowerCase();

                            return (
                              item.email
                                .toLowerCase()
                                .includes(searchLowerCase) ||
                              item.name.toLowerCase().includes(searchLowerCase)
                            );
                          } else {
                            return true;
                          }
                        })}
                        renderItem={renderItem}
                        keyExtractor={(item, index) => String(index)}
                      />
                    ) : // </ScrollView>
                      null}
                    {searchList ? (
                      <ScrollView>
                        <FlatList
                          nestedScrollEnabled
                          showsVerticalScrollIndicator={false}
                          data={lists}
                          extraData={lists}
                          renderItem={renderSearchItem}
                          keyExtractor={(item, index) => String(index)}
                        />
                      </ScrollView>
                    ) : null}
                  </View>

                  {/* <View style={styles.footer}>
              
            </View> */}
                </View>
              ) : null}

              {addEmployee == true ? (
                <View
                  style={{
                    //alignItems: 'center',
                    shadowColor: '#000',
                    shadowOffset: {
                      width: 0,
                      height: 1,
                    },
                    shadowOpacity: 0.34,
                    shadowRadius: 3.22,
                    elevation: 2,
                    width: windowWidth * 0.87,
                    marginHorizontal: switchMerchant ? -20 : 15,
                    marginBottom: switchMerchant ? -130 : -130,
                    alignSelf: 'center',
                  }}>
                  <View
                    style={{
                      flexDirection: 'row',
                      justifyContent: 'space-between',
                      width: '100%',
                      marginTop: 0,
                      marginLeft: switchMerchant ? 5 : -25,
                    }}>
                    <TouchableOpacity
                      style={{
                        width: 90,
                        flexDirection: 'row',
                        alignSelf: 'flex-start',
                        alignItems: 'center',
                        paddingLeft: switchMerchant ? 0 : 15,
                        borderRadius: 10,
                        marginHorizontal: switchMerchant ? -10 : 0,
                      }}
                      onPress={() => {
                        // setState({ addEmployee: false, addEmployeeItem: true })
                        setAddEmployee(false);
                        setAddEmployeeItem(true);
                      }}>
                      <Icon
                        name="chevron-left"
                        size={switchMerchant ? 20 : 30}
                        color={Colors.primaryColor}
                      />
                      <Text
                        style={{
                          fontFamily: 'NunitoSans-Bold',
                          color: Colors.primaryColor,
                          fontSize: switchMerchant ? 14 : 17,
                          marginBottom: Platform.OS === 'ios' ? 0 : 1,
                        }}>
                        Back
                      </Text>
                    </TouchableOpacity>
                  </View>
                  <KeyboardAwareScrollView
                    showsVerticalScrollIndicator={false}
                    contentContainerStyle={
                      {
                        //top: Platform.OS === 'ios' ? -keyboardHeight * 0.3 : 0,
                      }
                    }>
                    <View
                      style={{
                        marginTop: 10,
                        justifyContent: 'center',
                        alignItems: 'center',
                        //width: switchMerchant ? windowWidth * 0.8 : windowWidth * 0.87,
                        backgroundColor: Colors.whiteColor,
                        borderRadius: 5,
                        shadowColor: '#000',
                        shadowOffset: {
                          width: 0,
                          height: 2,
                        },
                        shadowOpacity: 0.22,
                        shadowRadius: 3.22,
                        elevation: 3,
                        paddingBottom: 100,
                        //paddingHorizontal : -30
                      }}>
                      <View
                        style={{
                          alignItems: 'center',
                          marginBottom: 30,
                          marginTop: 20,
                          // marginRight: 80
                          width: '100%',
                        }}>
                        <Text
                          style={{
                            fontFamily: 'NunitoSans-Bold',
                            fontSize: switchMerchant ? 20 : 40,
                            fontWeight: 'bold',
                          }}>
                          {selectedOutletEmployeeEdit
                            ? 'Edit Employee'
                            : 'Add Employee'}
                        </Text>
                        <Text
                          style={{
                            color: Colors.descriptionColor,
                            //marginTop: 10,
                            fontSize: switchMerchant ? 15 : 16,
                          }}>
                          Fill In Employees Information
                        </Text>

                        <View
                          style={{
                            alignSelf: 'flex-end',
                            position: 'absolute',
                            marginTop: 10,
                            zIndex: 10000,
                          }}>

                          {/* { selectedOutletEmployeeEdit ?
                <TouchableOpacity
                  style={{
                    backgroundColor: Colors.primaryColor,
                    width: 100,
                    height: 35,
                    borderRadius: 5,
                    flexDirection: 'row',
                    justifyContent: 'center',
                    marginRight: 15,
                  }}
                  onPress={() => {
                    
                  }}>
                  <Text
                    style={{
                      color: Colors.whiteColor,
                      alignSelf: 'center',
                      textAlign: 'center',
                      //marginVertical: 10,
                      fontSize: 16,
                      fontWeight: '600'
                    }}>
                    Disabled
                  </Text>
                </TouchableOpacity>
                :
                null
              } */}

                          <TouchableOpacity
                            disabled={isLoading}
                            style={{
                              justifyContent: 'center',
                              flexDirection: 'row',
                              borderWidth: 1,
                              borderColor: Colors.primaryColor,
                              backgroundColor: '#0A1F44',
                              borderRadius: 5,
                              width: switchMerchant ? 100 : 120,
                              paddingHorizontal: 10,
                              height: switchMerchant ? 35 : 40,
                              alignItems: 'center',
                              shadowOffset: {
                                width: 0,
                                height: 2,
                              },
                              shadowOpacity: 0.22,
                              shadowRadius: 3.22,
                              elevation: 1,
                              zIndex: -1,
                              marginRight: 20,
                            }}
                            onPress={() => {
                              addEmployeeFunc();
                              logEventAnalytics({
                                eventName: ANALYTICS.MODULE_EMPLOYEE_MANAGE_EMPLOYEE_EMPLOYEE_C_SAVE,
                                eventNameParsed: ANALYTICS_PARSED.MODULE_EMPLOYEE_MANAGE_EMPLOYEE_EMPLOYEE_C_SAVE
                              })

                              logEventAnalytics({
                                eventName: ANALYTICS.MODULE_EMPLOYEE_MANAGE_EMPLOYEE_EMPLOYEE_C_UPDATE,
                                eventNameParsed: ANALYTICS_PARSED.MODULE_EMPLOYEE_MANAGE_EMPLOYEE_EMPLOYEE_C_UPDATE
                              })
                            }}>
                            <Text
                              style={{
                                color: Colors.whiteColor,
                                //marginLeft: 5,
                                fontSize: switchMerchant ? 10 : 16,
                                fontFamily: 'NunitoSans-Bold',
                              }}>
                              {selectedOutletEmployeeEdit ? 'UPDATE' : 'SAVE'}
                            </Text>
                          </TouchableOpacity>

                          {selectedOutletEmployeeEdit ?
                            <TouchableOpacity
                              style={{
                                justifyContent: 'center',
                                flexDirection: 'row',
                                borderWidth: 1,
                                borderColor: Colors.tabRed,
                                backgroundColor: Colors.tabRed,
                                borderRadius: 5,
                                width: switchMerchant ? 100 : 120,
                                paddingHorizontal: 10,
                                height: switchMerchant ? 35 : 40,
                                alignItems: 'center',
                                shadowOffset: {
                                  width: 0,
                                  height: 2,
                                },
                                shadowOpacity: 0.22,
                                shadowRadius: 3.22,
                                elevation: 1,
                                zIndex: -1,
                                marginRight: 20,
                                marginTop: 20,
                              }}
                              onPress={() => {
                                Alert.alert(
                                  'Info',
                                  'Are you sure you want to remove this employee?',
                                  [
                                    {
                                      text: 'YES',
                                      onPress: () => {
                                        deleteOutletEmployee();

                                        logEventAnalytics({
                                          eventName: ANALYTICS.MODULE_EMPLOYEE_MANAGE_EMPLOYEE_EMPLOYEE_C_DELETE,
                                          eventNameParsed: ANALYTICS_PARSED.MODULE_EMPLOYEE_MANAGE_EMPLOYEE_EMPLOYEE_C_DELETE
                                        })
                                      },
                                    },
                                    { text: 'NO', onPress: () => { } },
                                  ],
                                  { cancelable: true },
                                );
                              }}>
                              <Text
                                style={{
                                  color: Colors.whiteColor,
                                  //marginLeft: 5,
                                  fontSize: switchMerchant ? 10 : 16,
                                  fontFamily: 'NunitoSans-Bold',
                                }}>
                                {'DELETE'}
                              </Text>
                            </TouchableOpacity>
                            :
                            <></>
                          }
                        </View>
                      </View>

                      <View
                        style={{
                          flexDirection: 'row',
                          paddingBottom: 30,
                          width: '90%',
                        }}>
                        <View
                          style={[
                            { width: '40%' },
                            switchMerchant
                              ? {
                                width: '40%',
                              }
                              : {},
                          ]}>
                          <View
                            style={{
                              alignItems: 'center',
                              justifyContent: 'center',
                            }}>
                            <View
                              style={[
                                windowWidth <= 1823 &&
                                  windowWidth >= 1820
                                  ? { marginRight: 180 }
                                  : {},
                              ]}>
                              <TouchableOpacity onPress={handleChoosePhoto}>
                                <View
                                  style={{
                                    flexDirection: 'row',
                                    alignItems: 'center',
                                  }}>
                                  {image !== '' ? (
                                    <View style={{ marginRight: '10%' }}>
                                      <View
                                        style={[
                                          {
                                            alignItems: 'center',
                                            // justifyContent: 'center',
                                            // backgroundColor: '#F7F7F7',
                                            borderRadius: 5,
                                            width: 200,
                                            height: 200,
                                            alignSelf: 'center',
                                          },
                                          switchMerchant
                                            ? {
                                              width: 100,
                                              height: 100,
                                            }
                                            : {},
                                        ]}>
                                        <AsyncImage
                                          source={{
                                            uri:
                                              image ||
                                              (selectedOutletEmployeeEdit
                                                ? selectedOutletEmployeeEdit.avatar
                                                : ''),
                                          }}
                                          item={
                                            selectedOutletEmployeeEdit
                                              ? selectedOutletEmployeeEdit
                                              : null
                                          }
                                          style={[
                                            {
                                              width: 148,
                                              height: 148,
                                              alignSelf: 'center',
                                              borderRadius: 100,
                                            },
                                            switchMerchant
                                              ? {
                                                width: 100,
                                                height: 100,
                                              }
                                              : {},
                                          ]} />
                                        <View
                                          style={{
                                            position: 'absolute',
                                            bottom: switchMerchant ? 5 : 60,
                                            right: switchMerchant ? -15 : 15,
                                            //opacity: 0.5,
                                          }}>
                                          <FontAwesome5
                                            name="edit"
                                            size={switchMerchant ? 15 : 23}
                                            color={Colors.primaryColor}
                                          />
                                        </View>
                                      </View>
                                    </View>
                                  ) : (
                                    <View
                                      style={[
                                        {
                                          alignItems: 'center',
                                          // justifyContent: 'center',
                                          // backgroundColor: '#F7F7F7',
                                          borderRadius: 5,
                                          width: 200,
                                          height: 200,
                                          alignSelf: 'center',
                                        },
                                        switchMerchant
                                          ? {
                                            width: 100,
                                            height: 100,
                                          }
                                          : {},
                                      ]}>
                                      <Image
                                        style={[
                                          {
                                            width: 148,
                                            height: 148,
                                            borderRadius: 100,
                                            alignSelf: 'center',
                                          },
                                          switchMerchant
                                            ? {
                                              width: 100,
                                              height: 100,
                                            }
                                            : {},
                                        ]}
                                        source={require('../assets/image/default-profile.png')}
                                      />
                                      <View
                                        style={{
                                          position: 'absolute',
                                          bottom: switchMerchant ? 5 : 60,
                                          right: switchMerchant ? -15 : 15,
                                          //opacity: 0.5,
                                        }}>
                                        <FontAwesome5
                                          name="edit"
                                          size={switchMerchant ? 15 : 23}
                                          color={Colors.primaryColor}
                                        />
                                      </View>
                                    </View>
                                  )}

                                  {/* <Icon name="paperclip" size={16} color={Colors.primaryColor} style={{ marginTop: 5, marginLeft: 10 }}>
                          <Text style={{
                            fontWeight: 'bold',
                            marginLeft: 10,
                            color: Colors.primaryColor,
                          }}>Upload photo
                          </Text>
                        </Icon> */}
                                </View>
                              </TouchableOpacity>
                            </View>
                          </View>

                          <View
                            style={{
                              flexDirection: 'row',
                              width: '50%',
                              marginTop: 30,
                            }}>
                            {/* <View style={{ flexDirection: 'row', width: '100%', alignItems: 'center',  }}>
                      <Text style={{ fontSize: 14, color: '#9FA2B4', fontWeight: '700', fontFamily: 'Nunitosans-Regular', width: '30%' }}>
                        Position:
                      </Text>
                      <View style={{ width: '75%', justifyContent: 'space-between', flexDirection: 'row', alignItems: 'center' }}>
                        <TextInput
                            editable={!loading}
                            // underlineColorAndroid={Colors.whiteColor}
                            clearButtonMode="while-editing"
                            style={{fontSize: 15, color: "black", fontWeight: '600', fontFamily: 'Nunitosans-Regular', marginLeft: 5} }
                            placeholder="Name"
                            multiline={true}
                            onChangeText={(text) => {
                              // setState({ name: text });
                              setName(text);
                            }}
                            value={name}
                          />
                    </View>
                  </View> */}
                          </View>

                          {/* ***************Border Line**************  */}
                          {/* <View style={{ borderWidth: 0.5, borderColor: '#E5E5E5', marginVertical: 25 }} /> */}

                          <View style={{ flexDirection: 'column' }}>
                            <View
                              style={{
                                flexDirection: 'row',
                                width: '90%',
                                alignItems: 'center',
                                marginBottom: 10,
                              }}>
                              <Text
                                style={{
                                  fontFamily: 'Nunitosans-Bold',
                                  fontSize: switchMerchant ? 10 : 14,
                                  color: 'black',
                                  width: '30%',
                                  fontWeight: '500',
                                  textAlign: 'left',
                                }}>
                                Role
                              </Text>

                              {
                                [
                                  {
                                    label: 'Frontliner',
                                    value: 'frontliner',
                                  },
                                  {
                                    label: 'Store Manager',
                                    value: 'store_manager',
                                  },
                                  {
                                    label: 'Admin',
                                    value: 'admin',
                                  },
                                ].find(option => option.value === role)
                                  ?
                                  <DropDownPicker
                                    arrowColor={'black'}
                                    arrowSize={20}
                                    arrowStyle={{ fontWeight: 'bold' }}
                                    style={[
                                      {
                                        //width: 250,
                                        height: switchMerchant ? 35 : 40,
                                        width: 200,
                                        paddingVertical: 0,
                                        backgroundColor: Colors.fieldtBgColor,
                                        borderRadius: 10,
                                        zIndex: 1000,
                                      },
                                      switchMerchant
                                        ? {
                                          width:
                                            windowWidth * 0.195,
                                        }
                                        : {},
                                    ]}
                                    placeholderStyle={{
                                      color: Colors.fieldtTxtColor,
                                      fontFamily: 'NunitoSans-Regular',
                                      fontSize: switchMerchant ? 10 : 14,
                                    }}
                                    items={[
                                      {
                                        label: 'Frontliner',
                                        value: 'frontliner',
                                      },
                                      {
                                        label: 'Store Manager',
                                        value: 'store_manager',
                                      },
                                      {
                                        label: 'Admin',
                                        value: 'admin',
                                      },
                                    ]}
                                    itemStyle={{
                                      justifyContent: 'flex-start',
                                      zIndex: 1000,
                                    }}
                                    placeholder={'Position'}
                                    onChangeItem={(item) => {
                                      setRole(item.value);

                                      logEventAnalytics({
                                        eventName: ANALYTICS.MODULE_EMPLOYEE_MANAGE_EMPLOYEE_EMPLOYEE_DD_ROLE,
                                        eventNameParsed: ANALYTICS_PARSED.MODULE_EMPLOYEE_MANAGE_EMPLOYEE_EMPLOYEE_DD_ROLE
                                      })
                                    }}
                                    defaultValue={role}
                                    value={role}
                                    dropDownMaxHeight={150}
                                    dropDownStyle={[
                                      {
                                        //width: 250,
                                        width: 200,
                                        height: 120,
                                        backgroundColor: Colors.fieldtBgColor,
                                        borderRadius: 10,
                                        borderWidth: 1,
                                        textAlign: 'left',
                                        zIndex: 1000,
                                      },
                                      switchMerchant
                                        ? {
                                          width:
                                            windowWidth * 0.195,
                                        }
                                        : {},
                                    ]}
                                    globalTextStyle={{
                                      fontFamily: 'NunitoSans-Regular',
                                      fontSize: switchMerchant ? 10 : 14,
                                      color: Colors.fontDark,
                                      marginLeft: 5,
                                    }}
                                  // items={[
                                  //   {
                                  //     label: 'Staff',
                                  //     value: 'frontliner',
                                  //   },
                                  //   {
                                  //     label: 'Store Manager',
                                  //     value: 'store_manager',
                                  //   },
                                  //   {
                                  //     label: 'Super Admin',
                                  //     value: 'admin',
                                  //   },
                                  // ]}
                                  // defaultValue={role}
                                  // value={role}

                                  // placeholder="Position"
                                  // placeholderStyle={{ color: 'black' }}
                                  // containerStyle={{ height: 50, width: 120 }}
                                  // style={{ backgroundColor: '#fafafa' }}
                                  // itemStyle={{
                                  //   justifyContent: 'flex-start', marginLeft: 5
                                  // }}
                                  // dropDownStyle={{ backgroundColor: '#fafafa', width: 120 }}
                                  // onChangeItem={(item) => {
                                  //   // setState({
                                  //   //   role: item.value,
                                  //   // });
                                  //   setRole(item.value);
                                  // }}
                                  />
                                  :
                                  <></>
                              }
                            </View>

                            <View
                              style={{
                                flexDirection: 'row',
                                width: '90%',
                                alignItems: 'center',
                                zIndex: -1,
                              }}>
                              <Text
                                style={{
                                  fontSize: switchMerchant ? 10 : 14,
                                  color: 'black',
                                  fontFamily: 'Nunitosans-Bold',
                                  width: '30%',
                                }}>
                                Outlet
                              </Text>

                              {
                                outletDropdownList.find(outlet => outlet.value === selectedOutletId)
                                  ?
                                  <DropDownPicker
                                    arrowColor={'black'}
                                    arrowSize={20}
                                    arrowStyle={{ fontWeight: 'bold' }}
                                    style={[
                                      {
                                        //width: 250,
                                        height: switchMerchant ? 35 : 40,
                                        width: 200,
                                        paddingVertical: 0,
                                        backgroundColor: Colors.fieldtBgColor,
                                        borderRadius: 10,
                                        zIndex: -1,
                                      },
                                      switchMerchant
                                        ? {
                                          width:
                                            windowWidth * 0.195,
                                        }
                                        : {},
                                    ]}
                                    placeholderStyle={{
                                      color: Colors.fieldtTxtColor,
                                      fontFamily: 'NunitoSans-Regular',
                                      fontSize: switchMerchant ? 10 : 14,
                                    }}
                                    items={outletDropdownList}
                                    itemStyle={{
                                      justifyContent: 'flex-start',
                                      zIndex: 2,
                                      fontFamily: 'NunitoSans-Regular',
                                      fontSize: switchMerchant ? 10 : 14,
                                    }}
                                    placeholder="Choose Location"
                                    onChangeItem={(item) => {
                                      setSelectedOutletId(item.value);

                                      logEventAnalytics({
                                        eventName: ANALYTICS.MODULE_EMPLOYEE_MANAGE_EMPLOYEE_EMPLOYEE_DD_OUTLET,
                                        eventNameParsed: ANALYTICS_PARSED.MODULE_EMPLOYEE_MANAGE_EMPLOYEE_EMPLOYEE_DD_OUTLET
                                      })
                                    }}
                                    defaultValue={selectedOutletId}
                                    dropDownMaxHeight={150}
                                    dropDownStyle={[
                                      {
                                        //width: 250,
                                        width: 200,
                                        height: 85,
                                        backgroundColor: Colors.fieldtBgColor,
                                        borderRadius: 10,
                                        borderWidth: 1,
                                        textAlign: 'left',
                                        zIndex: -1,
                                      },
                                      switchMerchant
                                        ? {
                                          width:
                                            windowWidth * 0.195,
                                        }
                                        : {},
                                    ]}
                                    globalTextStyle={{
                                      fontFamily: 'NunitoSans-Regular',
                                      fontSize: switchMerchant ? 10 : 14,
                                      color: Colors.fontDark,
                                      marginLeft: 5,
                                    }}
                                  // items={outletDropdownList}
                                  // // defaultValue={'petalingJaya'}
                                  // defaultValue={selectedOutletId}
                                  // placeholder="Choose Location"
                                  // placeholderStyle={{ color: 'black' }}
                                  // containerStyle={{ height: 50, width: 170 }}
                                  // style={{ backgroundColor: '#fafafa' }}
                                  // itemStyle={{
                                  //   justifyContent: 'flex-start', marginLeft: 5,
                                  // }}
                                  // dropDownStyle={{ backgroundColor: '#fafafa', width: 170 }}

                                  // placeholderStyle={{ color: 'black' }}
                                  // containerStyle={{ height: 70 }}
                                  // style={[styles.textInput, {
                                  //   opacity: 0,
                                  // }]}
                                  // itemStyle={{
                                  //   justifyContent: 'flex-start',
                                  // }}
                                  // dropDownStyle={{ backgroundColor: '#fafafa', width: 300 }}

                                  // onChangeItem={(item) =>
                                  //   // setState({
                                  //   //     place: item.value,
                                  //   // })
                                  //   setSelectedOutletId(item.value)
                                  // }
                                  />
                                  :
                                  <></>
                              }
                            </View>
                          </View>

                          {/* ***************Border Line**************  */}
                          {/* <View style={{ borderWidth: 0.5, borderColor: '#E5E5E5', marginVertical: 25 }} />

                <View style={{ flexDirection: 'row',}}>

                </View> */}
                          {/* ***************Border Line**************  */}
                          <View
                            style={{
                              borderWidth: 0,
                              borderColor: '#E5E5E5',
                              marginVertical: 25,
                              zIndex: -2,
                            }}
                          />
                        </View>

                        {/* Right Side */}
                        <View
                          style={[
                            { width: '60%', paddingLeft: 10 },
                            switchMerchant
                              ? {
                                width: '60%',
                              }
                              : {},
                          ]}>
                          <View>
                            <View
                              style={{ flexDirection: 'column', marginLeft: 5, paddingTop: 50, }}>
                              <View style={{ flexDirection: 'row' }}>
                                <View
                                  style={{
                                    flexDirection: 'row',
                                    width: '50%',
                                    alignItems: 'center',
                                  }}>
                                  <Text
                                    style={{
                                      fontSize: switchMerchant ? 10 : 14,
                                      color: 'black',
                                      fontFamily: 'Nunitosans-Bold',
                                      width: '30%',
                                    }}>
                                    Name
                                  </Text>
                                  <TextInput
                                    // editable={!loading}
                                    editable
                                    // underlineColorAndroid={Colors.whiteColor}
                                    clearButtonMode="while-editing"
                                    style={{
                                      backgroundColor: Colors.fieldtBgColor,
                                      width:
                                        windowWidth * 0.12,
                                      height: switchMerchant ? 35 : 40,
                                      borderRadius: 5,
                                      padding: 5,
                                      marginVertical: 5,
                                      borderWidth: 1,
                                      borderColor: '#E5E5E5',
                                      paddingLeft: 10,
                                      fontSize: switchMerchant ? 10 : 14,
                                      color: 'black',
                                      fontFamily: 'Nunitosans-Regular',
                                    }}
                                    placeholder="Name"
                                    placeholderStyle={{
                                      fontFamily: 'NunitoSans-Regular',
                                      fontSize: switchMerchant ? 10 : 14,
                                    }}
                                    placeholderTextColor={Platform.select({
                                      ios: '#a9a9a9',
                                    })}
                                    multiline={false}
                                    //iOS
                                    clearTextOnFocus
                                    //////////////////////////////////////////////
                                    //Android
                                    onFocus={() => {
                                      setTemp(name)
                                      setName('');
                                    }}
                                    ///////////////////////////////////////////////
                                    //When textinput is not selected
                                    onEndEditing={() => {
                                      if (name == '') {
                                        setName(temp);
                                      }
                                    }}
                                    //////////////////////////////////////////////
                                    onChangeText={(text) => {
                                      // setState({ name: text });
                                      setName(text);

                                      logEventAnalytics({
                                        eventName: ANALYTICS.MODULE_EMPLOYEE_MANAGE_EMPLOYEE_EMPLOYEE_TB_NAME,
                                        eventNameParsed: ANALYTICS_PARSED.MODULE_EMPLOYEE_MANAGE_EMPLOYEE_EMPLOYEE_TB_NAME
                                      })
                                    }}
                                    value={name}
                                    maxLength={30}
                                  />
                                </View>

                                <View
                                  style={{
                                    flexDirection: 'row',
                                    width: '50%',
                                    alignItems: 'center',
                                  }}>
                                  <Text
                                    style={{
                                      fontSize: switchMerchant ? 10 : 14,
                                      color: 'black',
                                      fontFamily: 'Nunitosans-Bold',
                                      width: '37%',
                                    }}>
                                    Username
                                  </Text>
                                  <TextInput
                                    editable={!loading}
                                    //underlineColorAndroid={Colors.whiteColor}
                                    clearButtonMode="while-editing"
                                    style={{
                                      backgroundColor: Colors.fieldtBgColor,
                                      width:
                                        windowWidth * 0.12,
                                      height: switchMerchant ? 35 : 40,
                                      borderRadius: 5,
                                      padding: 5,
                                      marginVertical: 5,
                                      borderWidth: 1,
                                      borderColor: '#E5E5E5',
                                      paddingLeft: 10,
                                      fontSize: switchMerchant ? 10 : 14,
                                      color: 'black',
                                      fontFamily: 'Nunitosans-Regular',
                                    }}
                                    placeholder="Username"
                                    placeholderStyle={{
                                      fontFamily: 'NunitoSans-Regular',
                                      fontSize: switchMerchant ? 10 : 14,
                                    }}
                                    placeholderTextColor={Platform.select({
                                      ios: '#a9a9a9',
                                    })}
                                    multiline={false}
                                    //iOS
                                    clearTextOnFocus
                                    //////////////////////////////////////////////
                                    //Android
                                    onFocus={() => {
                                      setTemp(username)
                                      setUsername('');
                                    }}
                                    ///////////////////////////////////////////////
                                    //When textinput is not selected
                                    onEndEditing={() => {
                                      if (username == '') {
                                        setUsername(temp);
                                      }
                                    }}
                                    //////////////////////////////////////////////
                                    onChangeText={(text) => {
                                      // setState({ name: text });
                                      setUsername(text);

                                      logEventAnalytics({
                                        eventName: ANALYTICS.MODULE_EMPLOYEE_MANAGE_EMPLOYEE_EMPLOYEE_TB_USERNAME,
                                        eventNameParsed: ANALYTICS_PARSED.MODULE_EMPLOYEE_MANAGE_EMPLOYEE_EMPLOYEE_TB_USERNAME
                                      })
                                    }}
                                    value={username}
                                  />
                                </View>
                              </View>

                              {/* ***************Border Line**************  */}
                              <View
                                style={{
                                  borderWidth: 0.5,
                                  borderColor: '#E5E5E5',
                                  marginVertical: 21,
                                }}
                              />

                              <View style={{ flexDirection: 'row' }}>
                                <View
                                  style={{
                                    flexDirection: 'row',
                                    width: '50%',
                                    alignItems: 'center',
                                  }}>
                                  <Text
                                    style={{
                                      fontSize: switchMerchant ? 10 : 14,
                                      color: 'black',
                                      fontFamily: 'Nunitosans-Bold',
                                      width: '30%',
                                    }}>
                                    Email
                                  </Text>
                                  <TextInput
                                    editable={
                                      !loading && !selectedOutletEmployeeEdit
                                    }
                                    // underlineColorAndroid={Colors.whiteColor}
                                    clearButtonMode="while-editing"
                                    style={{
                                      backgroundColor: Colors.fieldtBgColor,
                                      width:
                                        windowWidth * 0.12,
                                      height: switchMerchant ? 35 : 40,
                                      borderRadius: 5,
                                      padding: 5,
                                      marginVertical: 5,
                                      borderWidth: 1,
                                      borderColor: '#E5E5E5',
                                      paddingLeft: 10,
                                      fontSize: switchMerchant ? 10 : 14,
                                      color: 'black',
                                      fontFamily: 'Nunitosans-Regular',
                                    }}
                                    placeholder="Email"
                                    placeholderStyle={{
                                      fontFamily: 'NunitoSans-Regular',
                                      fontSize: switchMerchant ? 10 : 14,
                                    }}
                                    placeholderTextColor={Platform.select({
                                      ios: '#a9a9a9',
                                    })}
                                    autoCapitalize="none"
                                    //iOS
                                    clearTextOnFocus
                                    //////////////////////////////////////////////
                                    //Android
                                    onFocus={() => {
                                      setTemp(email)
                                      setEmail('');
                                    }}
                                    ///////////////////////////////////////////////
                                    //When textinput is not selected
                                    onEndEditing={() => {
                                      if (email == '') {
                                        setEmail(temp);
                                      }
                                    }}
                                    //////////////////////////////////////////////
                                    onChangeText={(text) => {
                                      // setState({ email: text });
                                      setEmail(text);
                                      logEventAnalytics({
                                        eventName: ANALYTICS.MODULE_EMPLOYEE_MANAGE_EMPLOYEE_EMPLOYEE_TB_EMAIL,
                                        eventNameParsed: ANALYTICS_PARSED.MODULE_EMPLOYEE_MANAGE_EMPLOYEE_EMPLOYEE_TB_EMAIL
                                      })
                                    }}
                                    value={email}
                                  />
                                </View>

                                <View
                                  style={{
                                    flexDirection: 'row',
                                    width: '50%',
                                    alignItems: 'center',
                                  }} />
                              </View>

                              {/* ***************Border Line**************  */}
                              <View
                                style={{
                                  borderWidth: 0.5,
                                  borderColor: '#E5E5E5',
                                  marginVertical: 21,
                                }}
                              />

                              <View style={{ flexDirection: 'row' }}>
                                <View
                                  style={{
                                    flexDirection: 'row',
                                    width: '50%',
                                    alignItems: 'center',
                                  }}>
                                  <Text
                                    style={{
                                      fontSize: switchMerchant ? 10 : 14,
                                      color: 'black',
                                      fontFamily: 'Nunitosans-Bold',
                                      width: '30%',
                                    }}>
                                    Number
                                  </Text>
                                  <TextInput
                                    // editable={!loading}
                                    editable
                                    // underlineColorAndroid={Colors.whiteColor}
                                    clearButtonMode="while-editing"
                                    style={{
                                      backgroundColor: Colors.fieldtBgColor,
                                      width:
                                        windowWidth * 0.12,
                                      height: switchMerchant ? 35 : 40,
                                      borderRadius: 5,
                                      padding: 5,
                                      marginVertical: 5,
                                      borderWidth: 1,
                                      borderColor: '#E5E5E5',
                                      paddingLeft: 10,
                                      fontSize: switchMerchant ? 10 : 14,
                                      color: 'black',
                                      fontFamily: 'Nunitosans-Regular',
                                    }}
                                    placeholder="Contact Number"
                                    placeholderStyle={{
                                      fontFamily: 'NunitoSans-Regular',
                                      fontSize: switchMerchant ? 10 : 14,
                                    }}
                                    placeholderTextColor={Platform.select({
                                      ios: '#a9a9a9',
                                    })}
                                    multiline={false}
                                    //iOS
                                    clearTextOnFocus
                                    //////////////////////////////////////////////
                                    //Android
                                    onFocus={() => {
                                      setTemp(number)
                                      setNumber('');
                                    }}
                                    ///////////////////////////////////////////////
                                    //When textinput is not selected
                                    onEndEditing={() => {
                                      if (number == '') {
                                        setNumber(temp);
                                      }
                                    }}
                                    //////////////////////////////////////////////
                                    onChangeText={(text) => {
                                      // setState({ number: text });
                                      setNumber(text);
                                      logEventAnalytics({
                                        eventName: ANALYTICS.MODULE_EMPLOYEE_MANAGE_EMPLOYEE_EMPLOYEE_TB_CONTACT_NUMBER,
                                        eventNameParsed: ANALYTICS_PARSED.MODULE_EMPLOYEE_MANAGE_EMPLOYEE_EMPLOYEE_TB_CONTACT_NUMBER
                                      })
                                    }}
                                    value={number}
                                  />
                                </View>

                                <View
                                  style={{
                                    flexDirection: 'row',
                                    width: '50%',
                                    alignItems: 'center',
                                  }}>
                                  <Text
                                    style={{
                                      fontSize: switchMerchant ? 10 : 14,
                                      color: 'black',
                                      fontFamily: 'Nunitosans-Bold',
                                      width: '37%',
                                    }}>
                                    Password
                                  </Text>
                                  <View
                                    style={{
                                      width: '75%',
                                      justifyContent: 'space-between',
                                      flexDirection: 'row',
                                      alignItems: 'center',
                                    }}>
                                    {!selectedOutletEmployeeEdit ? (
                                      <TextInput
                                        editable={!loading}
                                        //underlineColorAndroid={Colors.whiteColor}
                                        clearButtonMode="while-editing"
                                        style={{
                                          backgroundColor: Colors.fieldtBgColor,
                                          width:
                                            windowWidth * 0.12,
                                          height: switchMerchant ? 35 : 40,
                                          borderRadius: 5,
                                          padding: 5,
                                          marginVertical: 5,
                                          borderWidth: 1,
                                          borderColor: '#E5E5E5',
                                          paddingLeft: 10,
                                          fontSize: switchMerchant ? 10 : 14,
                                          color: 'black',
                                          fontFamily: 'Nunitosans-Regular',
                                        }}
                                        placeholder="Password"
                                        placeholderStyle={{
                                          fontFamily: 'NunitoSans-Regular',
                                          fontSize: switchMerchant ? 10 : 14,
                                        }}
                                        placeholderTextColor={Platform.select({
                                          ios: '#a9a9a9',
                                        })}
                                        //placeholderStyle={{paddingTop: switchMerchant ? 0 : 5}}
                                        secureTextEntry
                                        autoCapitalize="none"
                                        //iOS
                                        clearTextOnFocus
                                        //////////////////////////////////////////////
                                        //Android
                                        onFocus={() => {
                                          setTemp(password)
                                          setPassword('');
                                        }}
                                        ///////////////////////////////////////////////
                                        //When textinput is not selected
                                        onEndEditing={() => {
                                          if (password == '') {
                                            setPassword(temp);
                                          }
                                        }}
                                        //////////////////////////////////////////////
                                        onChangeText={(text) => {
                                          // setState({ password: text });
                                          setPassword(text);
                                          logEventAnalytics({
                                            eventName: ANALYTICS.MODULE_EMPLOYEE_MANAGE_EMPLOYEE_EMPLOYEE_TB_PASSWORD,
                                            eventNameParsed: ANALYTICS_PARSED.MODULE_EMPLOYEE_MANAGE_EMPLOYEE_EMPLOYEE_TB_PASSWORD
                                          })
                                        }}
                                        value={password}
                                      />
                                    ) : (
                                      <Text
                                        style={{
                                          marginLeft: 5,
                                          fontFamily: 'NunitoSans-Regular',
                                          fontSize: switchMerchant ? 10 : 14,
                                        }}>
                                        ********
                                      </Text>
                                    )}
                                  </View>
                                </View>
                              </View>

                              {/* ***************Border Line**************  */}
                              <View
                                style={{
                                  borderWidth: 0.5,
                                  borderColor: '#E5E5E5',
                                  marginVertical: 21,
                                  zIndex: -1,
                                }}
                              />

                              <View style={{ flexDirection: 'row', zIndex: -1 }}>
                                <View
                                  style={{
                                    flexDirection: 'row',
                                    width: '50%',
                                    alignItems: 'center',
                                  }}>
                                  <Text
                                    style={{
                                      fontSize: switchMerchant ? 10 : 14,
                                      color: 'black',
                                      fontFamily: 'Nunitosans-Bold',
                                      width: '30%',
                                    }}>
                                    PIN
                                  </Text>
                                  <TextInput
                                    // editable={!loading}

                                    // editable={!(
                                    //   selectedOutletEmployeeEdit &&
                                    //   selectedOutletEmployeeEdit.firebaseUid
                                    // )}
                                    // disabled={userRole != ROLE_TYPE.ADMIN || (
                                    //   selectedOutletEmployeeEdit &&
                                    //   selectedOutletEmployeeEdit.firebaseUid
                                    // )}
                                    disabled={userRole != ROLE_TYPE.ADMIN}

                                    // underlineColorAndroid={Colors.whiteColor}
                                    clearButtonMode="while-editing"
                                    style={{
                                      backgroundColor: Colors.fieldtBgColor,
                                      width:
                                        windowWidth * 0.12,
                                      height: switchMerchant ? 35 : 40,
                                      borderRadius: 5,
                                      padding: 5,
                                      marginVertical: 5,
                                      borderWidth: 1,
                                      borderColor: '#E5E5E5',
                                      paddingLeft: 10,
                                      fontSize: switchMerchant ? 10 : 14,
                                      color: 'black',
                                      fontFamily: 'Nunitosans-Regular',
                                    }}
                                    placeholder="PIN"
                                    placeholderStyle={{
                                      fontFamily: 'NunitoSans-Regular',
                                      fontSize: switchMerchant ? 10 : 14,
                                    }}
                                    placeholderTextColor={Platform.select({
                                      ios: '#a9a9a9',
                                    })}
                                    keyboardType='numeric'

                                    //iOS
                                    clearTextOnFocus
                                    //////////////////////////////////////////////
                                    //Android
                                    onFocus={() => {
                                      setTemp(pin)
                                      setPin('');
                                    }}
                                    ///////////////////////////////////////////////
                                    //When textinput is not selected
                                    onEndEditing={() => {
                                      if (pin == '') {
                                        setPin(temp);
                                      }
                                    }}
                                    //////////////////////////////////////////////

                                    onChangeText={(text) => {
                                      // setState({ name: text });
                                      if (text.length > 4) {
                                        Alert.alert('Please enter 4 digit pin')
                                      } else {
                                        setPin(text);
                                      }

                                      logEventAnalytics({
                                        eventName: ANALYTICS.MODULE_EMPLOYEE_MANAGE_EMPLOYEE_EMPLOYEE_TB_PIN,
                                        eventNameParsed: ANALYTICS_PARSED.MODULE_EMPLOYEE_MANAGE_EMPLOYEE_EMPLOYEE_TB_PIN
                                      })
                                    }}
                                    value={pin}
                                  />
                                </View>

                                <View
                                  style={{
                                    flexDirection: 'row',
                                    width: '50%',
                                    alignItems: 'center',
                                  }}>
                                  <Text
                                    style={{
                                      fontSize: switchMerchant ? 10 : 14,
                                      color: 'black',
                                      fontFamily: 'Nunitosans-Bold',
                                      width: '37%',
                                    }}>
                                    Privileges
                                  </Text>
                                  <DropDownPicker
                                    disabled={userRole != ROLE_TYPE.ADMIN}
                                    arrowColor={'black'}
                                    arrowSize={20}
                                    arrowStyle={{ fontWeight: 'bold' }}
                                    style={[
                                      {
                                        height: switchMerchant ? 35 : 40,
                                        width: windowWidth * 0.12,
                                        paddingVertical: 0,
                                        backgroundColor: Colors.fieldtBgColor,
                                        borderRadius: 10,
                                        zIndex: 1000,
                                      },
                                      switchMerchant
                                        ? {
                                          width:
                                            windowWidth * 0.12,
                                        }
                                        : {},
                                    ]}
                                    placeholderStyle={{
                                      color: Colors.fieldtTxtColor,
                                      fontFamily: 'NunitoSans-Regular',
                                      fontSize: switchMerchant ? 10 : 14,
                                    }}
                                    items={Object.values(PRIVILEGES_NAME).map((item) => ({
                                      label: item,
                                      value: item,
                                    }))}
                                    itemStyle={{
                                      justifyContent: 'flex-start',
                                      zIndex: 1000,
                                    }}
                                    placeholder={'Access'}
                                    onChangeItem={(item) => {
                                      setPrivileges(item);

                                      logEventAnalytics({
                                        eventName: ANALYTICS.MODULE_EMPLOYEE_MANAGE_EMPLOYEE_EMPLOYEE_DD_PRIVILEGES,
                                        eventNameParsed: ANALYTICS_PARSED.MODULE_EMPLOYEE_MANAGE_EMPLOYEE_EMPLOYEE_DD_PRIVILEGES
                                      })
                                    }}
                                    defaultValue={privileges}
                                    value={privileges}
                                    dropDownMaxHeight={150}
                                    dropDownStyle={[
                                      {
                                        width: windowWidth * 0.12,
                                        height: 120,
                                        backgroundColor: Colors.fieldtBgColor,
                                        borderRadius: 10,
                                        borderWidth: 1,
                                        textAlign: 'left',
                                        zIndex: 1000,
                                        position: 'absolute',
                                      },
                                      switchMerchant
                                        ? {
                                          width:
                                            windowWidth * 0.12,
                                        }
                                        : {},
                                    ]}
                                    multiple
                                    globalTextStyle={{
                                      fontFamily: 'NunitoSans-Regular',
                                      fontSize: switchMerchant ? 10 : 14,
                                      color: Colors.fontDark,
                                      marginLeft: 5,
                                    }}
                                  />
                                </View>
                              </View>
                              {/* ***************Border Line**************  */}
                              <View
                                style={{
                                  borderWidth: 0.5,
                                  borderColor: '#E5E5E5',
                                  marginVertical: 21,
                                  zIndex: -2,
                                }}
                              />
                              <View
                                style={{
                                  flexDirection: 'row',
                                  width: '50%',
                                  alignItems: 'center',
                                  zIndex: -2,
                                }}>
                                <Text
                                  style={{
                                    fontSize: switchMerchant ? 10 : 14,
                                    color: 'black',
                                    fontFamily: 'Nunitosans-Bold',
                                    width: '30%',
                                  }}>
                                  {'Screens to\nblock'}
                                </Text>
                                <DropDownPicker
                                  disabled={userRole != ROLE_TYPE.ADMIN}
                                  arrowColor={'black'}
                                  arrowSize={20}
                                  arrowStyle={{ fontWeight: 'bold' }}
                                  style={[
                                    {
                                      height: switchMerchant ? 35 : 40,
                                      width: windowWidth * 0.12,
                                      paddingVertical: 0,
                                      backgroundColor: Colors.fieldtBgColor,
                                      borderRadius: 10,
                                      zIndex: 1000,
                                    },
                                    switchMerchant
                                      ? {
                                        width:
                                          windowWidth * 0.12,
                                      }
                                      : {},
                                  ]}
                                  placeholderStyle={{
                                    color: Colors.fieldtTxtColor,
                                    fontFamily: 'NunitoSans-Regular',
                                    fontSize: switchMerchant ? 10 : 14,
                                  }}
                                  items={Object.values(SCREEN_NAME).map((item) => ({
                                    label: SCREEN_NAME_PARSED[item] ? SCREEN_NAME_PARSED[item] : item,
                                    value: item,
                                  }))}
                                  itemStyle={{
                                    justifyContent: 'flex-start',
                                    zIndex: 1000,
                                  }}
                                  placeholder={'Screens\nto Block'}
                                  onChangeItem={(item) => {
                                    setScreensToBlock(item);

                                    logEventAnalytics({
                                      eventName: ANALYTICS.MODULE_EMPLOYEE_MANAGE_EMPLOYEE_EMPLOYEE_DD_SCREEN_BLOCK,
                                      eventNameParsed: ANALYTICS_PARSED.MODULE_EMPLOYEE_MANAGE_EMPLOYEE_EMPLOYEE_DD_SCREEN_BLOCK
                                    })
                                  }}
                                  defaultValue={screensToBlock}
                                  value={screensToBlock}
                                  dropDownMaxHeight={150}
                                  dropDownStyle={[
                                    {
                                      width: windowWidth * 0.12,
                                      height: 120,
                                      backgroundColor: Colors.fieldtBgColor,
                                      borderRadius: 10,
                                      borderWidth: 1,
                                      textAlign: 'left',
                                      zIndex: 1000,
                                      position: 'absolute',
                                    },
                                    switchMerchant
                                      ? {
                                        width:
                                          windowWidth * 0.12,
                                      }
                                      : {},
                                  ]}
                                  multiple
                                  globalTextStyle={{
                                    fontFamily: 'NunitoSans-Regular',
                                    fontSize: switchMerchant ? 10 : 14,
                                    color: Colors.fontDark,
                                    marginLeft: 5,
                                  }}
                                />
                              </View>
                            </View>
                          </View>
                        </View>
                      </View>

                      {/* <View style={{ flexDirection: 'row' }}>
                        <View style={{ justifyContent: 'center', height: 50, width: '35%' }}>
                          <Text style={{ fontFamily: 'NunitoSans-SemiBold', fontSize: 16, }}>Day of Week</Text>
                        </View>
                        <View
                          style={{ height: 50, paddingHorizontal: 20, backgroundColor: Colors.fieldtBgColor, borderRadius: 5, marginBottom: 20, width: "55%", marginHorizontal: 10, flexDirection: 'row', alignItems: 'center', justifyContent: "center", borderColor: '#E5E5E5', borderWidth: 1, alignContent: 'center', fontFamily: 'NunitoSans-Regular', fontSize: 16, borderRadius: 12, paddingLeft: 30, }}>
                          <Text style={{ width: "90%", fontFamily: "NunitoSans-Regular", color: Colors.descriptionColor, }}>
                            {dayOfWeek ? moment(dayOfWeek).format('DD/MM/YYYY') : 'Day of Week'}
                          </Text>
                          <TouchableOpacity
                            style={{
                              alignItems: 'center'
                            }}
                            onPress={() => {
                              setPickDate('dayOfWeek');
                              setShowDateTimePicker(true);
                            }}>
                            <EvilIcons name="calendar" size={40} color={Colors.primaryColor} />
                          </TouchableOpacity>
                        </View>
                      </View> */}
                    </View>

                    {/****************** Test clock regular and over time *******************/}

                    {/* <View style={{ flexDirection: 'row' }}>
                        <View style={{ justifyContent: 'center', height: 50, width: '35%' }}>
                          <Text style={{ fontFamily: 'NunitoSans-SemiBold', fontSize: 16, }}>Regular</Text>
                        </View>
                        <View
                          style={{ height: 50, paddingHorizontal: 20, backgroundColor: Colors.fieldtBgColor, borderRadius: 5, marginBottom: 20, width: "55%", marginHorizontal: 10, flexDirection: 'row', alignItems: 'center', justifyContent: "center", borderColor: '#E5E5E5', borderWidth: 1, alignContent: 'center', fontFamily: 'NunitoSans-Regular', fontSize: 16, borderRadius: 12, paddingLeft: 30, }}>
                          <Text style={{ width: "90%", fontFamily: "NunitoSans-Regular", color: Colors.descriptionColor }}>
                            {regularTime ? moment(regularTime).format('hh:mmA') : 'Regular Time'}
                          </Text> 
                          <TouchableOpacity
                            style={{
                              alignItems: 'center'
                            }}
                            onPress={() => {
                              setPickTime('regularTime');
                              setShowDateTimePicker1(true);
                            }}>
                            <EvilIcons name="calendar" size={40} color={Colors.primaryColor} />
                          </TouchableOpacity>
                        </View>
                      </View> */}

                    {/* <View style={{ flexDirection: 'row' }}>
                        <View style={{ justifyContent: 'center', height: 50, width: '35%' }}>
                          <Text style={{ fontFamily: 'NunitoSans-SemiBold', fontSize: 16, }}>Overtime</Text>
                        </View>
                        <View
                          style={{ height: 50, paddingHorizontal: 20, backgroundColor: Colors.fieldtBgColor, borderRadius: 5, marginBottom: 20, width: "55%", marginHorizontal: 10, flexDirection: 'row', alignItems: 'center', justifyContent: "center", borderColor: '#E5E5E5', borderWidth: 1, alignContent: 'center', fontFamily: 'NunitoSans-Regular', fontSize: 16, borderRadius: 12, paddingLeft: 30, }}>
                          <Text style={{ width: "90%", fontFamily: "NunitoSans-Regular", color: Colors.descriptionColor }}>
                            {overTime ? moment(overTime).format('hh:mmA') : 'Over Time'}
                          </Text>
                          <TouchableOpacity
                            style={{
                              alignItems: 'center'
                            }}
                            onPress={() => {
                              setPickTime('overTime');
                              setShowDateTimePicker1(true);
                            }}>
                            <EvilIcons name="calendar" size={40} color={Colors.primaryColor} />
                          </TouchableOpacity>
                        </View>
                      </View> */}
                    {/****************** Test clock regular and over time *******************/}

                    <View style={{ height: 120 }} />
                    {/* <View
                  style={{
                    backgroundColor: Colors.primaryColor,
                    width: 200,
                    height: 40,
                    marginLeft: 20,
                    marginVertical: 15,
                    borderRadius: 5,
                    marginBottom: 60
                  }}>
                  <TouchableOpacity
                    onPress={() => {
                      addEmployeeFunc();
                    }}>
                    <Text
                      style={{
                        color: Colors.whiteColor,
                        alignSelf: 'center',
                        marginVertical: 10,
                      }}>
                      Save
                  </Text>
                  </TouchableOpacity>

                </View> */}
                  </KeyboardAwareScrollView>
                </View>
              ) : null}

              {showDetail ? (
                <View
                  style={{
                    marginTop: 20,
                    marginLeft: 30,
                    backgroundColor: Colors.whiteColor,
                    width: '87%',
                    height: 480,
                    elevation: 5,
                    shadowColor: Colors.blackColor,
                    shadowOffset: 1,
                    shadowOpacity: 10,
                  }}>
                  <Text
                    style={{
                      paddingVertical: 15,
                      marginLeft: 20,
                      color: '#a3a3a3',
                    }}>
                    Employee Details
                  </Text>
                  <View
                    style={{
                      flexDirection: 'row',
                      justifyContent: 'space-between',
                    }}>
                    <TouchableOpacity
                      style={{
                        marginRight: 430,
                        width: 120,
                        flexDirection: 'row',
                        alignItems: 'center',
                        paddingLeft: 15,
                        borderRadius: 10,
                        height: windowHeight * 0.055,
                        backgroundColor: Colors.whiteColor,
                      }}
                      onPress={() => {
                        setState({ showDetail: false, addEmployeeItem: true });

                        logEventAnalytics({
                          eventName: ANALYTICS.MODULE_EMPLOYEE_MANAGE_EMPLOYEE_EMPLOYEE_C_BACK,
                          eventNameParsed: ANALYTICS_PARSED.MODULE_EMPLOYEE_MANAGE_EMPLOYEE_EMPLOYEE_C_BACK
                        })
                      }}>
                      <Icon
                        name="chevron-left"
                        size={30}
                        color={Colors.primaryColor}
                      />
                      <Text
                        style={{
                          fontFamily: 'NunitoSans-Regular',
                          color: Colors.primaryColor,
                          marginBottom: Platform.OS === 'ios' ? 0 : 1,
                        }}>
                        Back
                      </Text>
                    </TouchableOpacity>
                    <TouchableOpacity
                      onPress={() => {
                        setState({ visible: true });
                      }}
                      style={{
                        marginHorizontal: 30,
                        flexDirection: 'row',
                        alignSelf: 'center',
                        alignItems: 'center',
                      }}>
                      <Icon name="trash-2" size={20} color="#eb3446" />
                      <Text style={{ color: '#eb3446' }}>Remove this employee</Text>
                    </TouchableOpacity>
                  </View>
                  <AsyncImage
                    source={{ uri: showEmployee.avatar }}
                    item={showEmployee}
                    style={{
                      alignSelf: 'center',
                      backgroundColor: Colors.secondaryColor,
                      paddingVertical: 60,
                      paddingHorizontal: 60,
                      borderRadius: 100,
                    }}
                  />

                  <Text
                    style={{
                      alignSelf: 'center',
                      fontWeight: 'bold',
                      fontSize: 20,
                      marginTop: 10,
                    }}>
                    {Employee}
                  </Text>
                  <View style={{ flexDirection: 'row', marginTop: 20 }}>
                    <View
                      style={{ flexDirection: 'row', flex: 1, marginLeft: 100 }}>
                      <Text
                        style={{
                          alignSelf: 'center',
                          fontWeight: '700',
                          fontSize: 15,
                          marginTop: 10,
                        }}>
                        Name:
                      </Text>
                      <Text
                        style={{
                          alignSelf: 'center',
                          fontSize: 15,
                          marginTop: 10,
                          marginLeft: 48,
                          color: '#9c9c9c',
                        }}>
                        {showEmployee.name}
                      </Text>
                    </View>
                    <View
                      style={{ flexDirection: 'row', flex: 1, marginLeft: 100 }}>
                      <Text
                        style={{
                          alignSelf: 'center',
                          fontWeight: '700',
                          fontSize: 15,
                          marginTop: 10,
                        }}>
                        Contact No.:
                      </Text>
                      <Text
                        style={{
                          alignSelf: 'center',
                          fontSize: 15,
                          marginTop: 10,
                          marginLeft: 27,
                          color: '#9c9c9c',
                        }}>
                        {showEmployee.number}
                      </Text>
                    </View>
                  </View>
                  <View style={{ flexDirection: 'row', marginTop: 20 }}>
                    <View
                      style={{ flexDirection: 'row', flex: 1, marginLeft: 100 }}>
                      <Text
                        style={{
                          alignSelf: 'center',
                          fontWeight: '700',
                          fontSize: 15,
                          marginTop: 10,
                        }}>
                        Status:
                      </Text>
                      <Text
                        style={{
                          alignSelf: 'center',
                          fontSize: 15,
                          marginTop: 10,
                          marginLeft: 45,
                          color: '#9c9c9c',
                        }}>
                        {showEmployee.role}
                      </Text>
                    </View>
                    <View
                      style={{ flexDirection: 'row', flex: 1, marginLeft: 100 }}>
                      <Text
                        style={{
                          alignSelf: 'center',
                          fontWeight: '700',
                          fontSize: 15,
                          marginTop: 10,
                        }}>
                        Email:
                      </Text>
                      <Text
                        style={{
                          alignSelf: 'center',
                          fontSize: 15,
                          marginTop: 10,
                          marginLeft: 70,
                          color: '#9c9c9c',
                        }}>
                        {showEmployee.email}
                      </Text>
                    </View>
                  </View>
                  <View style={{ flexDirection: 'row', marginTop: 20 }}>
                    <View
                      style={{ flexDirection: 'row', flex: 1, marginLeft: 100 }}>
                      <Text
                        style={{
                          alignSelf: 'center',
                          fontWeight: '700',
                          fontSize: 15,
                          marginTop: 10,
                        }}>
                        Position:
                      </Text>
                      <Text
                        style={{
                          alignSelf: 'center',
                          fontSize: 15,
                          marginTop: 10,
                          marginLeft: 33,
                          color: '#9c9c9c',
                        }}>
                        {showEmployee.role}
                      </Text>
                    </View>
                  </View>
                  <View style={{ flexDirection: 'row', marginTop: 20 }}>
                    <View
                      style={{ flexDirection: 'row', flex: 1, marginLeft: 100 }}>
                      <Text
                        style={{
                          alignSelf: 'center',
                          fontWeight: '700',
                          fontSize: 15,
                          marginTop: 10,
                        }}>
                        Day of Week:
                      </Text>
                      <Text
                        style={{
                          alignSelf: 'center',
                          fontSize: 15,
                          marginTop: 10,
                          marginLeft: 33,
                          color: '#9c9c9c',
                        }}>
                        {showEmployee.dayOfWeek}
                      </Text>
                    </View>
                  </View>
                  <View style={{ flexDirection: 'row', marginTop: 20 }}>
                    <View
                      style={{ flexDirection: 'row', flex: 1, marginLeft: 100 }}>
                      <Text
                        style={{
                          alignSelf: 'center',
                          fontWeight: '700',
                          fontSize: 15,
                          marginTop: 10,
                        }}>
                        Regular Time:
                      </Text>
                      <Text
                        style={{
                          alignSelf: 'center',
                          fontSize: 15,
                          marginTop: 10,
                          marginLeft: 33,
                          color: '#9c9c9c',
                        }}>
                        {showEmployee.regularTime}
                      </Text>
                    </View>
                  </View>
                  <View style={{ flexDirection: 'row', marginTop: 20 }}>
                    <View
                      style={{ flexDirection: 'row', flex: 1, marginLeft: 100 }}>
                      <Text
                        style={{
                          alignSelf: 'center',
                          fontWeight: '700',
                          fontSize: 15,
                          marginTop: 10,
                        }}>
                        Over Time:
                      </Text>
                      <Text
                        style={{
                          alignSelf: 'center',
                          fontSize: 15,
                          marginTop: 10,
                          marginLeft: 33,
                          color: '#9c9c9c',
                        }}>
                        {showEmployee.overTime}
                      </Text>
                    </View>
                  </View>
                </View>
              ) : null}
            </View>
          </ScrollView>
        </ScrollView>
      </View>
    </UserIdleWrapper>)
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
    flexDirection: 'row',
  },
  headerLogo: {
    width: 112,
    height: 25,
    marginLeft: 10,
  },
  list: {
    paddingVertical: 20,
    paddingHorizontal: 30,
    flexDirection: 'row',
    alignItems: 'center',
  },
  listItem: {
    marginLeft: 20,
    color: Colors.descriptionColor,
    fontSize: 16,
  },
  sidebar: {
    //width: windowWidth * Styles.sideBarWidth,
    // shadowColor: '#000',
    // shadowOffset: {
    //   width: 0,
    //   height: 8,
    // },
    // shadowOpacity: 0.44,
    // shadowRadius: 10.32,

    // elevation: 16,
  },
  content: {
    padding: 20,
    width: Dimensions.get('window').width * (1 - Styles.sideBarWidth),
    backgroundColor: Colors.highlightColor,
  },
  textInput: {
    height: 50,
    paddingHorizontal: 20,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 5,
    marginBottom: 20,
    width: 300,
  },
  dropDown: {
    height: 50,
    paddingHorizontal: 20,
    backgroundColor: 'red',
    borderRadius: 5,
    marginBottom: 20,
    width: 300,
  },
  addEmployee: {
    marginTop: 20,
    justifyContent: 'center',
    alignItems: 'center',
    width: Dimensions.get('window').width - 170,
    backgroundColor: Colors.whiteColor,
    borderRadius: 5,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.22,
    shadowRadius: 3.22,
    elevation: 3,
  },
  submitText: {
    height:
      Platform.OS == 'ios'
        ? Dimensions.get('window').height * 0.06
        : Dimensions.get('window').height * 0.05,
    paddingVertical: 5,
    paddingHorizontal: 20,
    flexDirection: 'row',
    color: '#4cd964',
    textAlign: 'center',
    borderRadius: 10,
    borderWidth: 1,
    borderColor: Colors.primaryColor,
    justifyContent: 'center',
    alignContent: 'center',
    alignItems: 'center',
    marginRight: 10,
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignContent: 'center',
    alignItems: 'center',
  },
  titleList: {
    backgroundColor: '#ffffff',
    flexDirection: 'row',
    paddingVertical: 20,
    paddingHorizontal: 20,
    marginTop: 20,
    borderBottomWidth: StyleSheet.hairlineWidth,
  },
  textInput1: {
    width: 250,
    height: 40,
    backgroundColor: 'white',

    borderRadius: 20,
    marginRight: '25%',
    flexDirection: 'row',
    alignContent: 'center',
    alignItems: 'center',
  },
  circleIcon: {
    width: 30,
    height: 30,
    // resizeMode: 'contain',
    marginRight: 10,
    alignSelf: 'center',
  },
  circleIcon2: {
    width: 60,
    height: 60,
    marginRight: 10,
    alignSelf: 'center',
  },
  confirmBox: {
    width: 350,
    height: 260,
    borderRadius: 10,
    backgroundColor: Colors.whiteColor,
  },
  confirmBoxRemove: {
    width: 520,
    height: 300,
    borderRadius: 12,
    backgroundColor: Colors.whiteColor,
    justifyContent: 'space-between',
  },
  headerLeftStyle: {
    width: Dimensions.get('screen').width * 0.17,
    justifyContent: 'center',
    alignItems: 'center',
  },
});
export default EmployeeScreen;
