# OSX
#
.DS_Store

# Xcode
#
build/
*.pbxuser
!default.pbxuser
*.mode1v3
!default.mode1v3
*.mode2v3
!default.mode2v3
*.perspectivev3
!default.perspectivev3
xcuserdata
*.xccheckout
*.moved-aside
DerivedData
*.hmap
*.ipa
*.xcuserstate
**/.xcode.env.local
ios/GoogleService-Info.plist
ios/KooDooMerchant/GoogleService-Info.plist

# Android/IntelliJ
#
build/
.idea
.gradle
local.properties
secret.properties
*.iml
*.hprof
.cxx/
*.keystore
!debug.keystore
android/google-services.json
android/app/google-services.json
android/*.log
android/app/src/main/assets/*.bundle
android/app/release
android/app/src/main/res/drawable-mdpi
android/app/src/main/res/drawable-hdpi
android/app/src/main/res/drawable-xhdpi
android/app/src/main/res/drawable-xxhdpi
android/app/src/main/res/drawable-xxxhdpi
android/app/src/main/res/raw

# node.js
#
node_modules/
npm-debug.log
yarn-error.log

# fastlane
#
# It is recommended to not store the screenshots in the git repo. Instead, use fastlane to re-generate the
# screenshots whenever they are needed.
# For more information about the recommended setup visit:
# https://docs.fastlane.tools/best-practices/source-control/

**/fastlane/report.xml
**/fastlane/Preview.html
**/fastlane/screenshots
**/fastlane/test_output

# Bundle artifact
*.jsbundle

# Ruby / CocoaPods
**/Pods/
/vendor/bundle/

# Temporary files created by Metro to check the health of the file watcher
.metro-health-check*

# testing
/coverage

# Yarn
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/sdks
!.yarn/versions

constant/env.js
constant/prod.env.js
firebaseConfigs/prod
bfg.jar
ios/KooDoo_pos/GoogleService-Info.plist

# android/app/koodoo.jks

android/*.log
android/app/src/main/assets/*.bundle
git-filter-repo.py
.cursorrules

build-notes.txt
