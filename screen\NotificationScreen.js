import { Text } from "react-native-fast-text";
import React, { Component, useReducer, useState, useEffect } from 'react';
import {
  StyleSheet,
  Image,
  View,
  Alert,
  TouchableOpacity,
  Modal as ModalComponent,
  Dimensions,
  TextInput,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { ModalView as ModalViewComponent } from 'react-native-multiple-modals';
import { ScrollView, FlatList } from "react-native-gesture-handler";
import Colors from '../constant/Colors';
import Styles from '../constant/Styles';
import SideBar from './SideBar';
import AsyncStorage from '@react-native-async-storage/async-storage'
import * as User from '../util/User';
import Icon from 'react-native-vector-icons/Ionicons';
import ApiClient from '../util/ApiClient';
import API from '../constant/API';
import DropDownPicker from 'react-native-dropdown-picker';
import Feather from 'react-native-vector-icons/Feather';
import EvilIcons from 'react-native-vector-icons/EvilIcons';
import moment from 'moment';
import DateTimePickerModal from "react-native-modal-datetime-picker";
import {
  isTablet
} from '../util/common';
import { MERCHANT_VOUCHER_CODE_FORMAT, MERCHANT_VOUCHER_TYPE, SEGMENT_TYPE, EXPAND_TAB_TYPE, } from '../constant/common';
import { CommonStore } from '../store/commonStore';
import { UserStore } from '../store/userStore';
import { MerchantStore } from '../store/merchantStore';
import { OutletStore } from '../store/outletStore';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import RNPickerSelect from 'react-native-picker-select';
import { useKeyboard } from '../hooks';
import { get } from 'react-native/Libraries/Utilities/PixelRatio';
import Entypo from 'react-native-vector-icons/Entypo';
import AntDesign from 'react-native-vector-icons/AntDesign';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import Switch from 'react-native-switch-pro';

//////////////////////////////////////////////////////////////////////////////////////////////////////////
const ModalView = Platform.OS === 'android' ? ModalComponent : ModalViewComponent;

const NotificationScreen = props => {
  const {
    navigation,
  } = props;

  //////////////////////////////////////////////////////////////////////////////////////////////////////////

  const userName = UserStore.useState(s => s.name);
  const merchantName = MerchantStore.useState(s => s.name);
  const [switchMerchant, setSwitchMerchant] = useState(isTablet() ? false : true);
  const [list1, setList1] = useState(true);
  const [customerList, setCustomerList] = useState([]);

  const [perPage, setPerPage] = useState(10);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageCount, setPageCount] = useState(0);
  const [page, setPage] = useState(0);

  const [visible, setVisible] = useState(false);
  const [distance, setDistance] = useState('');

  //////////////////////////////////////////////////////////////////////////////////////////////////////////

  const [search, setSearch] = useState('');

  const [promotionsNotifications, setPromotionsNotifications] = useState([]);

  const promotions = OutletStore.useState(s => s.promotions);
  const currOutlet = MerchantStore.useState(s => s.currOutlet);

  const isAlphaUser = UserStore.useState((s) => s.isAlphaUser);
  const expandTab = CommonStore.useState((s) => s.expandTab);
  const currPageStack = CommonStore.useState((s) => s.currPageStack);

  //////////////////////////////////////////////////////////////////////////////////////////////////////////

  useEffect(() => {
    var promotionsNotificationsTemp = [];

    for (var i = 0; i < promotions.length; i++) {
      if (promotions[i].notification) {
        var pushToTargetArr = [];

        if (promotions[i].notification.isPushToSMS) {
          pushToTargetArr.push('SMS');
        }

        if (promotions[i].notification.isPushToEmail) {
          pushToTargetArr.push('Email');
        }

        if (promotions[i].notification.isPushToApp) {
          pushToTargetArr.push('App');
        }

        promotionsNotificationsTemp.push({
          ...promotions[i],
          notification: {
            ...promotions[i].notification,
            pushTargets: pushToTargetArr.join('/'),
          }
        });
      }
    }

    setPromotionsNotifications(promotionsNotificationsTemp);

    setCurrentPage(1);
    setPageCount(Math.ceil(promotionsNotificationsTemp.length / perPage));
  }, [promotions]);

  //////////////////////////////////////////////////////////////////////////////////////////////////////////

  //To remove unwanted sidebar
  // navigation.dangerouslyGetParent().setOptions({
  //   tabBarVisible: false,
  // });

  //Header
  navigation.setOptions({
    headerLeft: () => (
      <TouchableOpacity
        onPress={() => {
          if (isAlphaUser || true) {
            navigation.navigate('MenuOrderingScreen');

            CommonStore.update((s) => {
              s.currPage = 'MenuOrderingScreen';
              s.currPageStack = [...currPageStack, 'MenuOrderingScreen'];
            });
          }
          else {
            navigation.navigate('Table');

            CommonStore.update((s) => {
              s.currPage = 'Table';
              s.currPageStack = [...currPageStack, 'Table'];
            });
          }
          if (expandTab !== EXPAND_TAB_TYPE.OPERATION) {
            CommonStore.update((s) => {
              s.expandTab = EXPAND_TAB_TYPE.OPERATION;
            });
          }
        }}
        style={styles.headerLeftStyle}>
        <Image
          style={{
            width: 124,
            height: 26,
          }}
          resizeMode="contain"
          source={require('../assets/image/logo.png')}
        />
      </TouchableOpacity>
    ),
    headerTitle: () => (
      <View style={[{
        // justifyContent: Platform.OS === 'android' ? 'center' : 'flex-start',
        // alignItems: Platform.OS === 'android' ? 'center' : 'flex-start',
        // marginRight: Platform.OS === 'ios' ? "27%" : 0,
        // bottom: switchMerchant ? '2%' : 0,
        ...global.getHeaderTitleStyle(),
      },
      // Dimensions.get('screen').width >= 768 && switchMerchant ? { right: Dimensions.get('screen').width * 0.1 } : {}, Dimensions.get('screen').width <= 768 ? { right: Dimensions.get('screen').width / 40 } : {}
      ]}>
        <Text
          style={{
            textAlign: 'left',
            fontSize: switchMerchant ? 20 : 24,
            alignItems: 'flex-start',
            justifyContent: 'flex-start',
            fontFamily: 'NunitoSans-Bold',
            color: Colors.whiteColor,
            opacity: 1,
            paddingLeft: Platform.OS === 'ios' ? "1%" : 0,
          }}>
          Promotion
        </Text>
      </View>
    ),
    headerRight: () => (
      <TouchableOpacity onPress={() => {
        if (global.currUserRole === 'admin') {
          navigation.navigate('Setting');
        }
      }}>
        <View style={{
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
          // backgroundColor: 'red',
        }}>
          <Text
            style={{
              fontFamily: 'NunitoSans-SemiBold',
              fontSize: 16,
              color: Colors.whiteColor,
            }}>
            {merchantName}
          </Text>

          <View style={{
            backgroundColor: 'white',
            width: 0.5,
            height: Dimensions.get('screen').height * 0.025,
            opacity: 0.8,
            marginHorizontal: 15,
            bottom: -1,
          }}>
          </View>

          <Text
            style={{
              fontFamily: 'NunitoSans-SemiBold',
              fontSize: 16,
              color: Colors.secondaryColor,
              marginRight: 15,
            }}>
            {userName}
          </Text>

          <View style={{
            marginRight: 30,
            width: Dimensions.get('screen').height * 0.05,
            height: Dimensions.get('screen').height * 0.05,
            borderRadius: Dimensions.get('screen').height * 0.05 * 0.5,
            alignItems: 'center',
            justifyContent: 'center',
            backgroundColor: 'white',
          }}>
            <Image style={{
              width: Dimensions.get('screen').height * 0.035,
              height: Dimensions.get('screen').height * 0.035,
              alignSelf: 'center',
            }} source={require('../assets/image/profile-pic.jpg')} />
          </View>
        </View>

      </TouchableOpacity>
    ),
  });
  /////////////////////////////////////////////////////////////////////////////////////////
  //Function Start Here

  const add = async () => {
    if (page + 1 < pageCount) {
      await setState({ page: page + 1, currentPage: currentPage + 1 })
      // console.log(page)
      var e = page
      next(e)
    }
  }

  const next = (e) => {
    const offset = e * perPage;
    setState({ offset: offset })
    loadMoreData()
  }

  const less = async () => {
    if (page > 0) {
      await setState({ page: page - 1, currentPage: currentPage - 1 })
      // console.log(page)
      var y = page
      pre(y)
    }
  }

  const nextPage = () => {
    setCurrentPage(currentPage + 1 > pageCount ? currentPage : currentPage + 1);
  };

  const prevPage = () => {
    setCurrentPage(currentPage - 1 < 1 ? currentPage : currentPage - 1);
  };

  const loadMoreData = () => {
    const data = oriList;
    const slice = data.slice(offset, offset + perPage) //30
    setState({ list: slice, pageCount: Math.ceil(data.length / perPage) })
  }

  const getDetail = () => {
    ApiClient.GET(API.salesByTransactionCategory + 1 + '&startDate=' + startDate + '&endDate=' + endDate).then((result) => {
      var data = result
      var slice = data.slice(offset, offset + perPage)
      setState({ list: slice, oriList: data, pageCount: Math.ceil(data.length / perPage) })
    });
  }

  const switchPromotionNotificationAutoPushStatus = (promotion, value) => {
    // self.onButtonClickHandler();

    var body = {
      // outletId: User.getOutletId()
      promotionId: promotion.uniqueId,
      notificationAutoPushStatus: value,
    };

    ApiClient.POST(API.switchPromotionNotificationAutoPushStatus, body).then(result => {
      if (result && result.status === 'success' && value) {
        Alert.alert(
          'Success',
          "Auto-push has been enabled.\nPlease note that notification will not auto-push itself if this notification has pushed",
          [
            {
              text: 'OK',
              onPress: () => {
              },
            },
          ],
          { cancelable: false },
        );
      }
    }).catch(err => { console.log(err) });
  };

  const deletePromotion = (promotion) => {
    var body = {
      promotionId: promotion.uniqueId,
    };

    ApiClient.POST(API.deletePromotion, body).then(result => {
      if (result && result.status === 'success') {
        Alert.alert(
          'Success',
          "Promotion has been removed",
          [
            {
              text: 'OK',
              onPress: () => {
                // navigation.goBack();
              },
            },
          ],
          { cancelable: false },
        );
      }
      else {
        Alert.alert(
          'Error',
          'Failed to remove this promotion',
          [{ text: 'OK', onPress: () => { } }],
          { cancelable: false },
        );
      }
    }).catch(err => { console.log(err) });
  };

  const renderItem = ({ item, index }) => (
    <View style={{
      //backgroundColor: (index + 1) % 2 == 0 ? Colors.whiteColor : Colors.highlightColor,
      backgroundColor: '#FFFFFF',
      paddingVertical: 10,
      paddingHorizontal: 3,
      paddingLeft: 1,
      borderColor: '#BDBDBD',
      borderTopWidth: (index + 1) % 2 == 0 ? 0 : 0.5,
      borderBottomWidth: (index + 1) % 2 == 0 ? 0 : 0.5,
      // width: '100%',
    }}>

      <View style={{ flexDirection: 'row', marginTop: 5, marginBottom: 5, alignItems: 'center', flex: 1 }}>

        <View style={{ flex: 2.5, marginRight: 5 }}>
          <Text style={{ fontSize: 12, fontWeight: '500', textAlign: 'left', color: Colors.primaryColor, textDecorationLine: 'underline' }}>{item.notification.title}</Text>
        </View>


        <View style={{ flexDirection: 'column', flex: 2, marginHorizontal: 3, }}>
          <Text style={{ fontSize: 12, fontWeight: '500', textAlign: 'left' }}>{`${moment(item.notification.date).format('DD/MM/YYYY')}`}</Text>
          <Text style={{ fontSize: 11, fontWeight: '500', textAlign: 'left', color: Colors.fieldtTxtColor }}>{`${moment(item.notification.time).format('HH.mmA')}`}</Text>
        </View>

        <View style={{ flex: 2, marginHorizontal: 3, }}>
          <Text style={{ fontSize: 12, fontWeight: '500', textAlign: 'left' }}>{'<'}{item.promoCode}{'>'}</Text>
        </View>

        <View style={{ flex: 2, marginHorizontal: 3, }}>
          <View
            style={{
              width: Platform.OS === 'ios' ? 90 : 140,
              //height: Platform.OS === 'ios' ? 30: 35,
              borderColor: '#E5E5E5',
              borderRadius: 8,
              borderWidth: 0.5,
              //justifyContent: 'space-between',
              //paddingLeft: 10,
              //padding: 5,
              //flexDirection: 'row'
            }}>
            <RNPickerSelect
              placeholder={{}}
              style={Styles.rnPickerSelectStyle}
              items={[{ label: '100m', value: '100m' }, { label: '200m', value: '200m' }, { label: '300m', value: '300m' }, { label: '400m', value: '400m' }, { label: '500m', value: '500m' }]}
              onValueChange={(value) => {
                setDistance(value.index)
              }}
              value={distance}
            />
            {/* <Icon name='chevron-down' size={18}/> */}
          </View>
        </View>

        <View style={{ flexDirection: 'column', flex: 1, marginHorizontal: 3, }}>
          <Switch
            onSyncPress={(value) => {
              switchPromotionNotificationAutoPushStatus(item, value);
            }}
            style={{ transform: [{ scaleX: .9 }, { scaleY: .8 }] }}
            circleColorActive={Colors.primaryColor}
            circleColorInactive={Colors.fieldtTxtColor}
            backgroundActive={'#dddddd'}
            value={item.notification.isAutoPush}
          />
          <Text style={{ fontSize: 12, fontWeight: '500', textAlign: 'left', marginLeft: 9, color: item.notification.isAutoPush ? Colors.primaryColor : Colors.fieldtTxtColor }}
          >
            {item.notification.isAutoPush ? 'ON' : 'OFF'}
          </Text>
        </View>


      </View>
    </View>
  )

  //////////////////////////////////////////////////////////////////////////////////////////////////////////////  
  ///////////////Render start here
  return (
    <View style={[styles.container, !isTablet() ? {
      transform: [
        { scaleX: 1 },
        { scaleY: 1 },
      ],
    } : {
    }]}>
      {/* <View style={[styles.sidebar, !isTablet() ? {
      } : {}, switchMerchant ? {
        // width: '10%'
      } : {}]}>
        <SideBar navigation={props.navigation} selectedTab={11} expandPromotions={true} />
      </View> */}


      {/* **********Manage FIlter Modal*************** */}
      <ModalView
        supportedOrientations={['landscape', 'portrait']}
        style={{ flex: 1 }}
        visible={visible}
        transparent={true}
        animationType="slide">

        <KeyboardAvoidingView
          behavior="padding"
          style={{
            backgroundColor: 'rgba(0,0,0,0.5)',
            flex: 1,
            justifyContent: 'center',
            alignItems: 'center',
            minHeight: Dimensions.get('window').height,
          }}>

          <View style={styles.ManageFilterBox}>

            <View style={{
              justifyContent: 'flex-end',
              alignItems: 'flex-end',
              marginTop: 7,
              marginRight: 7,
            }}>
              <TouchableOpacity
                onPress={() => {
                  setVisible(false);
                }}>
                <AntDesign name={"close"} size={25} color={'#858C94'} />
              </TouchableOpacity>
            </View>
            <View style={{
              justifyContent: 'center',
              alignItems: 'center',
              marginTop: 10
            }}>
              <Text style={{ fontSize: 26, fontFamily: 'Nunitosans-bold' }}>Manage Filter</Text>
            </View>

            <View style={styles.ManageFilterBox1}>
              <View style={{
                width: '20%',
                //marginRight: '33%', 
                flexDirection: 'row',
                justifyContent: 'center',
                alignItems: 'center',
                paddingLeft: 30,
                borderRadius: 10,
                height: Dimensions.get('screen').height * 0.055
              }}>
                <DropDownPicker
                  // controller={instance => controller = instance}
                  controller={instance => setController(instance)}
                  arrowColor={'#BDBDBD'}
                  arrowSize={23}
                  arrowStyle={{ fontWeight: 'bold' }}
                  style={{ width: 150, backgroundColor: '#F2F3F7', borderRadius: 6 }}
                  itemStyle={{ justifyContent: 'flex-start' }}
                  placeholderStyle={{ color: '#B6B6B6' }}
                  //change value below (Eason)
                  items={[{ label: 'A', value: 1 }, { label: 'B', value: 2 }]}
                  placeholder={"Product Category"}
                  labelStyle={{ fontSize: 12.5 }}
                  onChangeItem={selectedSort => {
                    // setState({ sort: selectedSort }),
                    //sortingOrders(selectedSort);
                  }
                  }
                //onOpen={() => controller1.close()}
                />
                <DropDownPicker
                  // controller={instance => controller = instance}
                  controller={instance => setController(instance)}
                  arrowColor={'#BDBDBD'}
                  arrowSize={23}
                  arrowStyle={{ fontWeight: 'bold' }}
                  style={{ width: 90, backgroundColor: '#F2F3F7', borderRadius: 6, marginLeft: 20 }}
                  itemStyle={{ justifyContent: 'flex-start' }}
                  placeholderStyle={{ color: '#B6B6B6' }}
                  //change value below (Eason)
                  items={[{ label: 'A', value: 1 }, { label: 'B', value: 2 }]}
                  placeholder={"Is"}
                  labelStyle={{ fontSize: 12.5 }}
                  onChangeItem={selectedSort => {
                    // setState({ sort: selectedSort }),
                    //sortingOrders(selectedSort);
                  }
                  }
                //onOpen={() => controller1.close()}
                />
                <MaterialCommunityIcons name={"delete-sweep"} size={25} color={'#858C94'} style={{ marginLeft: 20 }} />
              </View>
              <View style={{
                width: '20%',
                marginRight: '33%',
                flexDirection: 'row',
                justifyContent: 'center',
                alignItems: 'center',
                paddingLeft: 30,
                borderRadius: 10,
                marginTop: 15,
                height: Dimensions.get('screen').height * 0.055
              }}>
                <DropDownPicker
                  // controller={instance => controller = instance}
                  controller={instance => setController(instance)}
                  arrowColor={'#BDBDBD'}
                  arrowSize={23}
                  arrowStyle={{ fontWeight: 'bold' }}
                  style={{ width: 240, backgroundColor: '#F2F3F7', borderRadius: 6 }}
                  itemStyle={{ justifyContent: 'flex-start' }}
                  placeholderStyle={{ color: '#B6B6B6' }}
                  //change value below (Eason)
                  items={[{ label: 'A', value: 1 }, { label: 'B', value: 2 }]}
                  placeholder={"Package A"}
                  labelStyle={{ fontSize: 12.5 }}
                  onChangeItem={selectedSort => {
                    // setState({ sort: selectedSort }),
                    //sortingOrders(selectedSort);
                  }
                  }
                //onOpen={() => controller1.close()}
                />
              </View>
              <View style={{
                flexDirection: 'row',
                justifyContent: 'center',
                alignItems: 'center',
                borderWidth: 1,
                borderColor: '#E0E0E0',
                backgroundColor: '#F2F3F7',
                width: 100,
                height: Dimensions.get('screen').height * 0.055,
                borderRadius: 6,
                marginTop: 15,
              }}>
                <Text style={{
                  fontSize: 12.5,
                  Color: '#B6B6B6',
                }}>
                  Package A
                </Text>
                <TouchableOpacity
                  onPress={() => {

                  }}
                  style={{
                    marginLeft: 5
                  }}
                >
                  <AntDesign name={"close"} size={16} color={'#B6B6B6'} />
                </TouchableOpacity>
              </View>

              <View style={{ margin: 10, borderWidth: 0.3, width: 380, borderColor: 'grey' }}>
                {/* <Text style={{
                      color:'lightgrey'
                      }}>____________</Text> */}
              </View>

              <View style={{
                width: '20%',
                marginRight: '33%',
                flexDirection: 'row',
                justifyContent: 'center',
                alignItems: 'center',
                paddingLeft: 30,
                borderRadius: 10,
                height: Dimensions.get('screen').height * 0.055,
                marginTop: 10,
              }}>
                <DropDownPicker
                  // controller={instance => controller = instance}
                  controller={instance => setController(instance)}
                  arrowColor={'#BDBDBD'}
                  arrowSize={23}
                  arrowStyle={{ fontWeight: 'bold' }}
                  style={{ width: 150, backgroundColor: '#F2F3F7', borderRadius: 6 }}
                  itemStyle={{ justifyContent: 'flex-start' }}
                  placeholderStyle={{ color: '#B6B6B6' }}
                  //change value below (Eason)
                  items={[{ label: 'A', value: 1 }, { label: 'B', value: 2 }]}
                  placeholder={"Product Category"}
                  labelStyle={{ fontSize: 12.5 }}
                  onChangeItem={selectedSort => {
                    // setState({ sort: selectedSort }),
                    //sortingOrders(selectedSort);
                  }
                  }
                //onOpen={() => controller1.close()}
                />
                <DropDownPicker
                  // controller={instance => controller = instance}
                  controller={instance => setController(instance)}
                  arrowColor={'#BDBDBD'}
                  arrowSize={23}
                  arrowStyle={{ fontWeight: 'bold' }}
                  style={{ width: 90, backgroundColor: '#F2F3F7', borderRadius: 6, marginLeft: 20 }}
                  itemStyle={{ justifyContent: 'flex-start' }}
                  placeholderStyle={{ color: '#B6B6B6' }}
                  //change value below (Eason)
                  items={[{ label: 'A', value: 1 }, { label: 'B', value: 2 }]}
                  placeholder={"Is"}
                  labelStyle={{ fontSize: 12.5 }}
                  onChangeItem={selectedSort => {
                    // setState({ sort: selectedSort }),
                    //sortingOrders(selectedSort);
                  }
                  }
                //onOpen={() => controller1.close()}
                />
                <MaterialCommunityIcons name={"delete-sweep"} size={25} color={'#858C94'} style={{ marginLeft: 20 }} />
              </View>
              <View style={{
                width: '20%',
                marginRight: '33%',
                flexDirection: 'row',
                justifyContent: 'center',
                alignItems: 'center',
                paddingLeft: 30,
                borderRadius: 10,
                height: Dimensions.get('screen').height * 0.055,
                marginTop: 15,
              }}>
                <DropDownPicker
                  // controller={instance => controller = instance}
                  controller={instance => setController(instance)}
                  arrowColor={'#BDBDBD'}
                  arrowSize={23}
                  arrowStyle={{ fontWeight: 'bold' }}
                  style={{ width: 240, backgroundColor: '#F2F3F7', borderRadius: 6 }}
                  itemStyle={{ justifyContent: 'flex-start' }}
                  placeholderStyle={{ color: '#B6B6B6' }}
                  //change value below (Eason)
                  items={[{ label: 'A', value: 1 }, { label: 'B', value: 2 }]}
                  placeholder={"Package A"}
                  labelStyle={{ fontSize: 12.5 }}
                  onChangeItem={selectedSort => {
                    // setState({ sort: selectedSort }),
                    //sortingOrders(selectedSort);
                  }
                  }
                //onOpen={() => controller1.close()}
                />
              </View>
              <View style={{
                flexDirection: 'row',
                justifyContent: 'center',
                alignItems: 'center',
                borderWidth: 1,
                borderColor: '#E0E0E0',
                backgroundColor: '#F2F3F7',
                width: 100,
                height: Dimensions.get('screen').height * 0.055,
                borderRadius: 6,
                marginTop: 15,
              }}>
                <Text style={{
                  fontSize: 12.5,
                  Color: '#B6B6B6',
                }}>
                  Package A
                </Text>
                <TouchableOpacity
                  onPress={() => {

                  }}
                  style={{
                    marginLeft: 5
                  }}
                >
                  <AntDesign name={"close"} size={16} color={'#B6B6B6'} />
                </TouchableOpacity>
              </View>

              <View style={{ flexDirection: 'row', justifyContent: 'flex-end', alignItems: 'flex-end', marginTop: 30 }}>
                <TouchableOpacity
                  style={{
                    borderWidth: 1,
                    width: 120,
                    height: Dimensions.get('screen').height * 0.055,
                    borderColor: '#0A1F44',
                    borderRadius: 8,
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}
                  onPress={() => {
                  }}>
                  <Text
                    style={{
                      marginLeft: 10,
                      color: Colors.primaryColor,
                      fontSize: 16,
                      color: '#0A1F44',
                      fontFamily: 'Nunitosans-bold',
                    }}>
                    Cancel
                  </Text>
                </TouchableOpacity>

                <TouchableOpacity
                  style={{
                    borderWidth: 1,
                    width: 120,
                    height: Dimensions.get('screen').height * 0.055,
                    borderColor: '#0A1F44',
                    borderRadius: 8,
                    alignItems: 'center',
                    justifyContent: 'center',
                    backgroundColor: '#0A1F44',
                    marginLeft: 10,
                  }}
                  onPress={() => {
                  }}>
                  <Text
                    style={{
                      marginLeft: 10,
                      color: Colors.primaryColor,
                      fontSize: 16,
                      color: '#FFFFFF',
                      fontFamily: 'Nunitosans-bold',
                    }}>
                    Apply
                  </Text>
                </TouchableOpacity>
              </View>

            </View>
          </View>
        </KeyboardAvoidingView>
      </ModalView>
      {/* **********Manage FIlter Modal END *************** */}


      <KeyboardAvoidingView>

        <View style={{
          flexDirection: 'row',
          justifyContent: 'space-between',
          width: Dimensions.get('screen').width * 0.9,
          margin: 10,
          paddingHorizontal: 25,
          padding: 10,
        }}>
          <View style={{
            flexDirection: 'row',
            width: '55%',
            justifyContent: 'space-between',
            alignItems: 'center',
          }}>
            <TouchableOpacity style={{ marginRight: 10 }}
              onPress={() => { props.navigation.navigate('PromotionList') }}
            >
              <Text style={{ color: Colors.descriptionColor, fontFamily: 'Nunitosans-Bold' }}>
                Promotion List
              </Text>
            </TouchableOpacity>
            <TouchableOpacity style={{ marginHorizontal: 10 }}
              onPress={() => { props.navigation.navigate('AutoPushNotification') }}
            >
              <Text style={{ color: Colors.descriptionColor, fontFamily: 'Nunitosans-Bold' }}>
                Auto Push Notification
              </Text>
            </TouchableOpacity>
            <TouchableOpacity style={{ marginHorizontal: 10 }}
              onPress={() => { props.navigation.navigate('ManualPushNotification') }}
            >
              <Text style={{ color: Colors.descriptionColor, fontFamily: 'Nunitosans-Bold' }}>
                Manual Push Notification
              </Text>
            </TouchableOpacity>
            <TouchableOpacity style={{ marginHorizontal: 10 }}>
              <Text style={{ color: Colors.primaryColor, fontFamily: 'Nunitosans-Bold' }}>
                Notification
              </Text>
            </TouchableOpacity>
          </View>
          <View style={{ height: 35 }} />
        </View>

        <View style={styles.list}>

          <View style={{
            flexDirection: 'row',
            justifyContent: 'space-between',
            margin: 20,
            //backgroundColor: 'red',
            height: '10%'
          }}>
            <View style={{ width: '55%', justifyContent: 'center' }}>
              <Text style={{ fontFamily: 'Nunitosans-bold', fontSize: 18, }}>
                Notification
              </Text>
            </View>
            <View style={{ width: '45%', flexDirection: 'row', justifyContent: 'space-between' }}>
              {/* Filter bar  */}
              <View style={{ width: '40%' }} />
              {/* <TouchableOpacity style={{ justifyContent: 'center', width: '40%' }}
                onPress={() => { setVisible(true) }}
              >
                <View style={{
                  justifyContent: 'center',
                  width: '100%',
                  flexDirection: 'row',
                  borderWidth: 1,
                  borderRadius: 3,
                  borderColor: Colors.primaryColor,
                  height: 35,
                  alignItems: 'center'
                }}>
                  <Feather name='filter' color={Colors.primaryColor} size={18} />
                  <Text style={{ color: Colors.primaryColor }}>
                    Manage Filter
                  </Text>
                </View>
              </TouchableOpacity> */}
              {/* Search Bar */}
              <View style={{
                flexDirection: 'row',
                justifyContent: 'center',
                width: '53%',
                height: 35,
                alignItems: 'center',
                borderWidth: 1,
                borderRadius: 3,
                borderColor: '#E5E5E5',
                alignSelf: 'center'
              }}>
                <View style={{ flex: 3 }}>
                  <TextInput placeholder='Search By Name...'
                    style={{ marginLeft: 10 }}
                    onChangeText={(text) => {
                      setSearch(text);
                    }}
                    placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                    defaultValue={search}
                  >
                  </TextInput>
                </View>
                <View style={{ flex: 1, height: '100%', justifyContent: 'center', alignItems: 'center', backgroundColor: Colors.primaryColor }}>
                  <Icon name='search' size={25} color={Colors.whiteColor} />
                </View>
              </View>
            </View>
          </View>

          {/* ~~~~~~~~~~~~~~~~~List View Start Here~~~~~~~~~~~~~~~~~~~~~ */}
          <View style={{ width: '100%', marginTop: 0 }}>
            <View style={{ backgroundColor: Colors.whiteColor, padding: 12, paddingTop: 0, height: '82%' }}>
              <View style={{ flexDirection: 'row' }}>

                <View style={{ flexDirection: 'row', flex: 2.5, marginRight: 5, borderRightColor: 'lightgrey', alignItems: 'center', justifyContent: 'flex-start' }}>
                  <View style={{}}>
                    <Text style={{ fontSize: 13, color: Colors.fieldtTxtColor, fontFamily: 'NunitoSans-Bold', textAlign: 'left' }}>Notification Name</Text>
                  </View>
                </View>

                <View style={{ flexDirection: 'row', flex: 2, marginHorizontal: 3, borderRightColor: 'lightgrey', alignItems: 'center', justifyContent: 'flex-start' }}>
                  <View style={{}}>
                    <Text style={{ fontSize: 13, color: Colors.fieldtTxtColor, fontFamily: 'NunitoSans-Bold', textAlign: 'left' }}>Date/Time</Text>
                  </View>
                </View>

                <View style={{ flexDirection: 'row', flex: 2, marginHorizontal: 3, borderRightColor: 'lightgrey', alignItems: 'center', justifyContent: 'flex-start' }}>
                  <View style={{}}>
                    <Text style={{ fontSize: 13, color: Colors.fieldtTxtColor, fontFamily: 'NunitoSans-Bold', textAlign: 'left' }}>Promo Code</Text>
                  </View>
                </View>

                <View style={{ flexDirection: 'row', flex: 2, marginHorizontal: 3, borderRightColor: 'lightgrey', alignItems: 'center', justifyContent: 'flex-start' }}>
                  <View style={{}}>
                    <Text style={{ fontSize: 13, color: Colors.fieldtTxtColor, fontFamily: 'NunitoSans-Bold', textAlign: 'left' }}>Distance (Radius)</Text>
                  </View>
                </View>

                <View style={{ flexDirection: 'row', flex: 1, marginHorizontal: 3, borderRightColor: 'lightgrey', alignItems: 'center', justifyContent: 'flex-start' }}>
                  <View style={{ flexDirection: 'row' }}>
                    <Text style={{ fontSize: 13, color: Colors.fieldtTxtColor, fontFamily: 'NunitoSans-Bold', textAlign: 'left', alignSelf: 'center' }}>Status</Text>
                    <View style={{ flexDirection: 'column' }}>
                      <Feather name='chevron-up' style={{ marginBottom: -2 }} />
                      <Feather name='chevron-down' style={{ marginTop: -2 }} />
                    </View>
                  </View>
                </View>
              </View>
              {list1 ? (

                <FlatList
                  data={promotionsNotifications.filter(promotion => {
                    if (search !== '') {
                      const searchLowerCase = search.toLowerCase();

                      if (promotion.notification.title.toLowerCase().includes(searchLowerCase) ||
                        promotion.notification.description.toLowerCase().includes(searchLowerCase) ||
                        promotion.notification.pushTargets.toLowerCase().includes(searchLowerCase) ||
                        promotion.promoCode.toLowerCase().includes(searchLowerCase)) {
                        return true;
                      }

                      return false;
                    }
                    else {
                      return true;
                    }
                  }).slice((currentPage - 1) * perPage, currentPage * perPage)}
                  // extraData={renderItem}
                  renderItem={renderItem}
                  keyExtractor={(item, index) => String(index)}
                  style={{ marginTop: 10 }}
                  initialNumToRender={4}
                />

              ) : null}
            </View>

            <View style={{ marginLeft: '77%', flexDirection: 'row', marginTop: 10, width: '100%' }}>
              <Text style={{ marginRight: '1%' }}>Page</Text>
              <View style={{ borderWidth: 1, borderColor: Colors.blackColor, width: '6%', height: 20, alignItems: 'center', backgroundColor: Colors.whiteColor }}>
                <TextInput
                  onChangeText={(text) => {
                    // setState({ exportEmail: text });
                    // setCurrentPage(text);

                    var currentPageTemp = text.length > 0 ? parseInt(text) : 1;

                    setCurrentPage(currentPageTemp > pageCount ? pageCount : (currentPageTemp < 1 ? 1 : currentPageTemp));
                  }}
                  style={{
                    color: Colors.fontDark,
                    fontFamily: 'NunitoSans-Regular',
                    fontSize: 14,
                    marginTop: Platform.OS === 'ios' ? 0 : -15,
                    marginBottom: Platform.OS === 'ios' ? 0 : -15,
                    textAlign: 'center',
                    width: '100%',
                  }}
                  placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                  value={currentPage.toString()}
                  defaultValue={currentPage.toString()}
                  keyboardType={'numeric'}
                />
              </View>
              <Text style={{ marginLeft: '1%', marginRight: '1%' }}>of {pageCount}</Text>
              <TouchableOpacity style={{ width: '3%', height: 20, backgroundColor: Colors.primaryColor, borderRadius: 2, justifyContent: 'center', alignItems: 'center' }} onPress={() => { prevPage() }}>
                <MaterialIcons name='keyboard-arrow-left' size={16} style={{ color: Colors.whiteColor }} />
              </TouchableOpacity>
              <TouchableOpacity style={{ width: '3%', height: 20, backgroundColor: Colors.primaryColor, borderRadius: 2, justifyContent: 'center', alignItems: 'center' }} onPress={() => { nextPage() }}>
                <MaterialIcons name='keyboard-arrow-right' size={16} style={{ color: Colors.whiteColor }} />
              </TouchableOpacity>
            </View>
          </View>
          {/* ~~~~~~~~~~~~~~~~~List View END Here~~~~~~~~~~~~~~~~~~~~~ */}


        </View>

      </KeyboardAvoidingView>



    </View>
  )

};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.highlightColor,
    flexDirection: 'row',
  },
  list: {
    backgroundColor: Colors.whiteColor,
    width: Dimensions.get('screen').width * 0.85,
    height: Dimensions.get('screen').height * 0.825,
    marginTop: 0,
    marginHorizontal: 20,
    alignSelf: 'center',
    borderRadius: 5,
    shadowOpacity: 0,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.22,
    shadowRadius: 3.22,
    elevation: 3,
  },
  listItem: {
    marginLeft: 20,
    color: Colors.descriptionColor,
    fontSize: 16,
  },
  sidebar: {
    //width: Dimensions.get('screen').width * Styles.sideBarWidth,
    // shadowColor: "#000",
    // shadowOffset: {
    //   width: 0,
    //   height: 8,
    // },
    // shadowOpacity: 0.44,
    // shadowRadius: 10.32,

    // elevation: 16,
  },
  content: {
    padding: 16
  },
  circleIcon: {
    width: 30,
    height: 30,
    // resizeMode: 'contain',
    marginRight: 10,
    alignSelf: 'center'
  },
  titleList: {
    backgroundColor: '#ffffff',
    flexDirection: 'row',
    paddingVertical: 20,
    paddingHorizontal: 20,
    //marginTop: 20,
    borderBottomWidth: StyleSheet.hairlineWidth,
    borderTopLeftRadius: 8,
    borderTopRightRadius: 8,

    alignItems: 'center',

    // shadowOpacity: 0,
    // shadowColor: '#000',
    // shadowOffset: {
    //   width: 0,
    //   height: 2,
    // },
    // shadowOpacity: 0.22,
    // shadowRadius: 3.22,
    // elevation: 3,
  },
  textTitle: {
    fontSize: 16,
    fontFamily: 'NunitoSans-Bold',
  },
  textItem: {
    fontSize: 14,
    fontFamily: 'NunitoSans-Regular',
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'center',
    // alignContent: 'center',
    alignItems: 'center',
    backgroundColor: 'white',
    padding: 20,
    paddingTop: 15,
    marginTop: 0,
    width: '100%',

    shadowOpacity: 0,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.22,
    shadowRadius: 3.22,
    elevation: 7,
  },
  addVoucher: {
    marginTop: 0,
    //justifyContent: 'center',
    alignItems: 'center',
    //alignContent: 'center',
    width: Dimensions.get('screen').width * 0.78,
    backgroundColor: Colors.whiteColor,
    // marginRight: 100,

    borderRadius: 4,

    shadowOpacity: 0,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.22,
    shadowRadius: 3.22,
    elevation: 1,
  },
  ManageFilterBox: {
    width: Dimensions.get('screen').width * 0.4,
    height: Dimensions.get('screen').height * 0.7,
    borderRadius: 12,
    backgroundColor: Colors.whiteColor,
    //justifyContent: 'space-between'
  },
  ManageFilterBox1: {
    width: Dimensions.get('screen').width * 0.35,
    height: Dimensions.get('screen').height * 0.55,
    //borderRadius: 10,
    backgroundColor: Colors.whiteColor,
    justifyContent: 'center',
    borderColor: '#E5E5E5',
    borderWidth: 1,
    alignItems: 'center',
    alignSelf: 'center',
    //padding: 10,
    margin: 15,
  },
  headerLeftStyle: {
    width: Dimensions.get('screen').width * 0.17,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default NotificationScreen;