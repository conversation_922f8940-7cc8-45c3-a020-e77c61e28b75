import React, { Component, useReducer, useState, useEffect, useRef, useCallback, } from "react";
import {
  StyleSheet,
  ScrollView,
  Image,
  View,
  Text,
  Alert,
  TouchableOpacity,
  Dimensions,
  Modal,
  ActivityIndicator,
  useWindowDimensions,
} from "react-native";
import firestore from "@react-native-firebase/firestore";
import Colors from "../constant/Colors";
import SideBar from "./SideBar";
import Icon from "react-native-vector-icons/Feather";
// import TopBar from '../screen/TopBar';
import { PermissionsAndroid, Platform } from "react-native";
import AntDesign from "react-native-vector-icons/AntDesign";
import Ionicon from "react-native-vector-icons/Ionicons";
import { RFPercentage } from 'react-native-responsive-fontsize';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import SimpleLineIcons from "react-native-vector-icons/SimpleLineIcons";
import { TextInput, FlatList } from "react-native-gesture-handler";
import DropDownPicker from "react-native-dropdown-picker";
import DateTimePickerModal from "react-native-modal-datetime-picker";
import API from "../constant/API";
import ApiClient from "../util/ApiClient";
// import {launchCamera, launchImageLibrary} from 'react-native-image-picker';
// import DialogInput from 'react-native-dialog-input';
import * as User from "../util/User";
import LoginScreen from "./LoginScreen";
import AsyncStorage from "@react-native-async-storage/async-storage";
// import CheckBox from 'react-native-check-box';
import { color } from "react-native-reanimated";
import Close from "react-native-vector-icons/AntDesign";
// import NumericInput from 'react-native-numeric-input';
import Styles from "../constant/Styles";
import moment, { isDate } from "moment";
// import Barcode from 'react-native-barcode-builder';
// import Switch from 'react-native-switch-pro';
import Ionicons from "react-native-vector-icons/Ionicons";
// import {isTablet} from 'react-native-device-detection';
import { OutletStore } from "../store/outletStore";
import { OUTLET_SHIFT_STATUS, SHIFT_PAY_TYPE, USER_ORDER_STATUS } from "../constant/common";
import { EXPAND_TAB_TYPE } from "../constant/common";
import { MerchantStore } from "../store/merchantStore";
import { UserStore } from "../store/userStore";
import {
  getTransformForModalFullScreen,
  getTransformForModalInsideNavigation,
  getTransformForScreenInsideNavigation,
  sliceUnicodeStringV2WithDots,
} from "../util/common";
import personicon from "../assets/image/default-profile.png";
import headerLogo from "../assets/image/logo.png";
import headerLogo1 from "../assets/image/logo_2.png";
// import '../constant/styles.css';
import { CommonStore } from "../store/commonStore";
import { openCashDrawer } from '../util/printer';
import AsyncImage from "../components/asyncImage";
import { ReactComponent as Logo } from "../assets/svg/logo_2.svg";
import APILocal from "../util/apiLocalReplacers";
import { Collections } from "../constant/firebase";
import { printShiftReport } from "../util/printer";
import { PRINTER_USAGE_TYPE } from "../constant/printer";
import { isMobile } from "../util/common";
import { Camera } from 'react-native-camera-kit';
// To use Html5QrcodeScanner (more info below)

const isTablet = () => {
  return true;
};

global.packedCartItemDict = {};

const FulfillmentAssistant = (props) => {
  const { navigation } = props;
  const { height: windowHeight, width: windowWidth } = useWindowDimensions();

  const [selfCollect, setSelfCollect] = useState(true);
  const [openHourPickerVisible, setOpenHourPickerVisible] = useState(false);
  const [closeHourPickerVisible, setCloseHourPickerVisible] = useState(false);
  const [openHour, setOpenHour] = useState("");
  const [closeHour, setCloseHour] = useState("");
  const [isChecked2, setIsChecked2] = useState(false);
  const [isChecked3, setIsChecked3] = useState(false);
  const [isChecked4, setIsChecked4] = useState(false);
  const [isChecked5, setIsChecked5] = useState(false);
  const [isChecked6, setIsChecked6] = useState(false);
  const [isChecked7, setIsChecked7] = useState(false);
  const [isChecked8, setIsChecked8] = useState(false);
  const [isChecked9, setIsChecked9] = useState(false);
  const [isChecked10, setIsChecked10] = useState(false);
  const [isChecked11, setIsChecked11] = useState(false);
  const [isChecked12, setIsChecked12] = useState(false);
  const [isChecked13, setIsChecked13] = useState(false);
  const [value1, setValue1] = useState("");
  const [value2, setValue2] = useState("");
  const [amount, setAmount] = useState("");
  const [hourStart, setHourStart] = useState("");
  const [hourEnd, setHourEnd] = useState("");
  const [days, setDays] = useState(false);
  const [days1, setDays1] = useState(false);
  const [loading, setLoading] = useState(false);
  const [showDistance, setShowDistance] = useState("");
  const [expiryPeriod, setExpiryPeriod] = useState("");
  const [extentionCharges, setExtentionCharges] = useState("");
  const [extentionDuration, setExtentionDuration] = useState("");
  const [showDateTimePicker, setShowDateTimePicker] = useState(false);
  // const [pickerMode, setPickerMode] = useState('time');
  const [pickerMode, setPickerMode] = useState("datetime");
  const [merchantDisplay, setMerchantDisplay] = useState(false);
  const [shift, setShift] = useState(true);
  const [tax, setTax] = useState(false);
  const [sample, setSample] = useState(false);
  const [redemption, setRedemption] = useState(false);
  const [redemptionList, setRedemptionList] = useState(true);
  const [redemptionAdd, setRedemptionAdd] = useState(false);
  const [order, setOrder] = useState(false);
  const [previousState, setPreviousState] = useState(false);
  const [receipt, setReceipt] = useState([]);
  const [detail, setDetail] = useState([]);
  const [merchantInfo, setMerchantInfo] = useState([]);
  const [outlet, setOutlet] = useState([]);
  const [outletInfo, setOutletInfo] = useState([]);
  const [isModalVisible, setIsModalVisible] = useState(false);
  // const [outletId, setOutletId] = useState([]);
  const [merInfo, setMerInfo] = useState([]);
  // const [merchantId, setMerchantId] = useState([]);
  const [showKeypad, setShowKeypad] = useState(false);
  // const [showKeypad, setShowKeypad] = useState(false);

  const [controller, setController] = useState({});
  const [showModal3, setShowModal3] = useState(false);
  const [showModal4, setShowModal4] = useState(false);
  const [showModal5, setShowModal5] = useState(false);
  const [showModalConfirm, setShowModalConfirm] = useState(false);
  const [closingAmount, setClosingAmount] = useState("");
  const [options, setOptions] = useState([]);
  const [shift1, setShift1] = useState([]);
  const [status, setStatus] = useState(false);
  const [logo, setLogo] = useState("");
  const [cover, setCover] = useState("");
  const [name, setName] = useState("");
  const [tname, setTname] = useState("");
  const [rate, setRate] = useState("");
  const [address, setAddress] = useState("");
  const [phone, setPhone] = useState("");
  const [payment, setPayment] = useState("");
  const [time, setTime] = useState("");
  const [statue, setStatue] = useState("");
  const [status1, setStatus1] = useState(false);
  const [outlets, setOutlets] = useState([]);
  const [outletId, setOutletId] = useState(null);
  const [myTextInput, setMyTextInput] = useState(React.createRef());
  const [start_time, setStart_time] = useState(false);
  const [end_time, setEnd_time] = useState(false);
  const [rev_time, setRev_time] = useState("");
  const [category, setCategory] = useState("");
  const [close, setClose] = useState("Closed");
  const [showNote, setShowNote] = useState(false);
  const [expandView, setExpandView] = useState(false);
  const [value, setValue] = useState("");
  const [sLable, setSLabel] = useState('');
  const [itemSKU, setItemSKU] = useState('');
  const [extendOption, setExtendOption] = useState([

    { optionId: 1, price: 20, day: 7, days: false },
  ]);
  const [switchMerchant, setSwitchMerchant] = useState(false);
  const [alloutlet, setAlloutlet] = useState([]);
  const [discount, setDiscount] = useState("");
  const [amount1, setAmount1] = useState("");
  const [selectedCategoryId, setSelectedCategoryId] = useState("");
  const [categoryOutlet, setCategoryOutlet] = useState([]);
  const [extend, setExtend] = useState([]);
  const [outletss, setOutletss] = useState([]);
  const [redemptionDetail, setRedemptionDetail] = useState([]);
  const [outletInfo1, setOutletInfo1] = useState([]);
  const [category1, setCategory1] = useState([]);
  // const [merchantName, setMerchantName] = useState('');
  const [addOutletName, setAddOutletName] = useState("");
  const [addOutletWindow, setAddOutletWindow] = useState(false);
  const [taxList, setTaxList] = useState([]);
  const [note1, setNote1] = useState("");
  const [note2, setNote2] = useState("");
  const [note3, setNote3] = useState("");
  const [openings, setOpenings] = useState([]);
  const [editOpeningIndex, setEditOpeningIndex] = useState(0);
  const [data, setData] = useState('');
  const [showScanner, setShowScanner] = useState(false);
  const [showScannerItem, setShowScannerItem] = useState(false);
  const [userOrderBybc, setUserOrderBybc] = useState(null);
  const [cartItemBybc, setCartItemBybc] = useState(null);
  const [scannedCode, setScannedCode] = useState(null);

  // let html5QrcodeScannerRef = null
  // let html5QrcodeScannerRef2 = null

  const handleIconPress = () => {
    setShowScanner((prev) => !prev); // Toggle showScanner state
  };

  const handleIconPress2 = () => {
    setShowScannerItem((prev) => !prev); // Toggle showScanner state
  };

  // 2024-05-21 - fulfillment assistant changes

  const [scannedUserOrder, setScannedUserOrder] = useState(null);

  // const [packedCartItemDict, setPackedCartItemDict] = useState({});

  const outletItems = OutletStore.useState(s => s.outletItems);

  //////////////////////////////////////////////////////////

  const outletPrinters = OutletStore.useState((s) => s.outletPrinters);

  const currOutletShift = OutletStore.useState((s) => s.currOutletShift);
  const currOutletShiftStatus = OutletStore.useState(
    (s) => s.currOutletShiftStatus
  );

  const currOutletId = MerchantStore.useState((s) => s.currOutletId);

  const merchantId = UserStore.useState((s) => s.merchantId);


  const userId = UserStore.useState((s) => s.firebaseUid);
  const userName = UserStore.useState((s) => s.name);
  const merchantName = MerchantStore.useState((s) => s.name);
  const isLoading = CommonStore.useState((s) => s.isLoading);

  const allOutlets = MerchantStore.useState((s) => s.allOutlets);

  const isAlphaUser = UserStore.useState((s) => s.isAlphaUser);

  const expandTab = CommonStore.useState((s) => s.expandTab);

  const currPageStack = CommonStore.useState((s) => s.currPageStack);
  const outletSelectDropdownView = CommonStore.useState(
    (s) => s.outletSelectDropdownView
  );

  const [expandBarSelection, setExpandBarSelection] = useState(
    props.expandBarSelection === undefined ? false : props.expandBarSelection,
  );

  const [expandLineSelection, setExpandLineSelection] = useState(false);

  const setState = () => { };

  // navigation.dangerouslyGetParent().setOptions({
  //   tabBarVisible: false,
  // });

  // const currOutletShiftStatus = OutletStore.useState(
  //   (s) => s.currOutletShiftStatus,
  // );
  // const [outletDropdownList, setOutletDropdownList] = useState([]);
  // const [selectedOutletList, setSelectedOutletList] = useState([]); // multi-outlets

  // var outletNames = [];

  // for (var i = 0; i < allOutlets.length; i++) {
  //   for (var j = 0; j < selectedOutletList.length; j++) {
  //     if (selectedOutletList.includes(allOutlets[i].uniqueId)) {
  //       outletNames.push(allOutlets[i].name);
  //       break;
  //     }
  //   }
  // }

  // useEffect(() => {
  //   setOutletDropdownList(
  //     allOutlets.map((item) => {
  //       return { label: item.name, value: item.uniqueId };
  //     })
  //   );
  // }, [allOutlets]);

  var targetOutletDropdownListTemp = allOutlets.map((outlet) => ({
    label: sliceUnicodeStringV2WithDots(outlet.name, 20),
    value: outlet.uniqueId,
  }));

  // useEffect(() => {
  //   CommonStore.update((s) => {
  //     s.outletSelectDropdownView = () => {
  //       return (
  //         <View
  //           style={{
  //             flexDirection: "row",
  //             alignItems: "center",
  //             borderRadius: 8,
  //             width: 200,
  //             backgroundColor: "white",
  //           }}
  //         >
  //           {currOutletId.length > 0 &&
  //             allOutlets.find((item) => item.uniqueId === currOutletId) ? (
  //             <MultiSelect
  //               clearable={false}
  //               singleSelect={true}
  //               defaultValue={currOutletId}
  //               placeholder={"Choose Outlet"}
  //               onChange={(value) => {
  //                 if (value) { // if choose the same option again, value = ''
  //                   MerchantStore.update((s) => {
  //                     s.currOutletId = value;
  //                     s.currOutlet =
  //                       allOutlets.find(
  //                         (outlet) => outlet.uniqueId === value
  //                       ) || {};
  //                   });
  //                 }

  //                 CommonStore.update((s) => {
  //                   s.shiftClosedModal = false;
  //                 });
  //               }}
  //               options={targetOutletDropdownListTemp}
  //               className="msl-varsHEADER"
  //             />
  //           ) : (
  //             <ActivityIndicator size={"small"} color={Colors.whiteColor} />
  //           )}
  //           {/* <Select

  //             placeholder={"Choose Outlet"}
  //             onChange={(items) => {
  //               setSelectedOutletList(items);
  //             }}
  //             options={outletDropdownList}
  //             isMulti
  //           /> */}
  //         </View>
  //       );
  //     };
  //   });
  // }, [allOutlets, currOutletId, isLoading, currOutletShiftStatus]);

  // useEffect(() => {
  //   console.log('reader');
  //   console.log(document.getElementById('reader'));
  //   function onScanSuccess(decodedText, decodedResult) {
  //     // handle the scanned code as you like, for example:
  //     console.log(`Code matched = ${decodedText}`, decodedResult);
  //   }

  //   function onScanFailure(error) {
  //     // handle scan failure, usually better to ignore and keep scanning.
  //     // for example:
  //     console.warn(`Code scan error = ${error}`);
  //   }

  //   let html5QrcodeScanner = new Html5QrcodeScanner(
  //     "reader",
  //     { fps: 10, qrbox: { width: 250, height: 250 } },
  //   /* verbose= */ false);
  //   html5QrcodeScanner.render(onScanSuccess, onScanFailure);
  // }, []);

  //////////////////////////////////////////////////////////////////////
  // ask for camera permission once when screen mounts
  useEffect(() => {
    async function requestCameraPermission() {
      if (Platform.OS === "android") {
        try {
          const granted = await PermissionsAndroid.request(
            PermissionsAndroid.PERMISSIONS.CAMERA,
            {
              title: "Camera Permission",
              message:
                "This app needs access to your camera to scan shipping labels and SKUs.",
              buttonPositive: "OK",
            }
          );

          if (granted !== PermissionsAndroid.RESULTS.GRANTED) {
            alert("Camera permission denied. Scanning will not work.");
          }
        } catch (err) {
          console.warn(err);
        }
      }
      // On iOS, system asks automatically because of Info.plist
    }

    requestCameraPermission();
  }, []);

  //////////////////////////////////////////////////////////////////////

  // const qrReaderCallback = useCallback((node) => {
  //   if (node !== null && (showScanner)) {
  //     // do stuff here

  //     console.log('reader');
  //     console.log(document.getElementById('reader'));


  //     let html5QrcodeScannerRef = new Html5QrcodeScanner(
  //       node.id,
  //       { fps: 10, qrbox: { width: 250, height: 250 } },
  //   /* verbose= */ false);
  //     html5QrcodeScannerRef.render(onScanSuccess, onScanFailure);
  //   }
  // }, [showScanner, showScannerItem])

  const onScanSuccess = async (decodedText, decodedResult) => {
    // handle the scanned code as you like, for example:
    console.log(`Code matched = ${decodedText}`, decodedResult);
    console.log(global.cameraScanState)
    if (global.cameraScanState === 'reader') {
      const foundOrder = await getUserOrderByAwb(decodedText);

      if (foundOrder) {
        if (foundOrder.packed === false) {
          setSLabel(decodedText);
          setShowScanner(false);
          // getUserOrderByAwb(decodedText);

        } else {
          alert('This order is already packed.');
          setSLabel('');
          getUserOrderByAwb(null);
          setShowScanner(false);

        }
      }
      else {
        alert('Please scan a valid shipping label.');
        setShowScanner(false);
      }
    } else {
      setShowScannerItem(false);
      console.log('cart order item', userOrderBybc);
      // getCartItemByBarcode(decodedText, userOrderBybc);
      setScannedCode(decodedText);
    }
    console.log(global.cameraScanState)

  }

  const onScanFailure = (error) => {
    // handle scan failure, usually better to ignore and keep scanning.
    // for example:
    console.warn(`Code scan error = ${error}`);
  }

  useEffect(() => {
    if (scannedCode !== null && userOrderBybc !== null && global.cameraScanState !== 'reader') {
      getCartItemByBarcode(scannedCode, userOrderBybc);
    }
  }, [userOrderBybc, scannedCode]);
  // Use effect to pack cart item when both userOrderBybc and cartItemBybc are available
  useEffect(() => {
    if (cartItemBybc !== null && userOrderBybc !== null) {
      packCartItem(cartItemBybc, 1, userOrderBybc);
    }
  }, [cartItemBybc, userOrderBybc]);

  // const qrReaderCallback2 = useCallback((node) => {
  //   if (node !== null && (showScannerItem)) {
  //     // do stuff here

  //     console.log('reader2');
  //     console.log(document.getElementById('reader2'));


  //     let html5QrcodeScannerRef2 = new Html5QrcodeScanner(
  //       node.id,
  //       { fps: 10, qrbox: { width: 250, height: 250 } },
  //   /* verbose= */ false);
  //     html5QrcodeScannerRef2.render(onScanSuccess2, onScanFailure2);
  //   }
  // }, [showScannerItem, showScanner])

  // const onScanSuccess2 = (decodedText, decodedResult) => {
  //   // handle the scanned code as you like, for example:
  //   console.log(`Code matched = ${decodedText}`, decodedResult);
  //   setItemSKU(decodedText);
  //   setShowScannerItem(false);
  //   getCartItemByBarcode(decodedText);
  // }

  // const onScanFailure2 = (error) => {
  //   // handle scan failure, usually better to ignore and keep scanning.
  //   // for example:
  //   console.warn(`Code scan error = ${error}`);
  // }

  useEffect(() => {
    return () => {
      setSLabel('');
    }
  }, [])


  // useEffect(() => {
  //   return () => {
  //     if (html5QrcodeScannerRef) {
  //       html5QrcodeScannerRef.clear();
  //     }
  //   };
  // }, []);

  // useEffect(() => {
  //   return () => {
  //     if (html5QrcodeScannerRef2) {
  //       html5QrcodeScannerRef2.clear();
  //     }
  //   };
  // }, []);

navigation.setOptions({
  headerShown: false,
  headerBackVisible: false,
  headerTitleAlign: "center", 
  headerTitle: () => (
    <Text
      style={{
        fontSize: switchMerchant ? 20 : 24,
        textAlign: "center",
        fontFamily: "NunitoSans-Bold",
        color: Colors.whiteColor,
      }}
    >
      Order Fulfillment Assistant
    </Text>
  ),
  headerRight: () => (
    <View
      style={{
        flexDirection: "row",
        alignItems: "center",
        paddingRight: 15,
      }}
    >
      {outletSelectDropdownView && outletSelectDropdownView()}

      <View
        style={{
          backgroundColor: "white",
          width: 1,
          height: windowHeight * 0.025,
          opacity: 0.8,
          marginHorizontal: 12,
        }}
      />

      <TouchableOpacity
        onPress={() => {
          if (global.currUserRole === "admin") {
            navigation.navigate("General Settings - KooDoo Assistant");
          }
        }}
        style={{ flexDirection: "row", alignItems: "center" }}
      >
        <Text
          style={{
            fontFamily: "NunitoSans-SemiBold",
            fontSize: switchMerchant ? 12 : 16,
            color: Colors.secondaryColor,
            marginRight: 10,
            maxWidth: windowWidth / 4, 
          }}
          numberOfLines={1}
          ellipsizeMode="tail"
        >
          {userName}
        </Text>

        <View
          style={{
            width: 32,
            height: 32,
            borderRadius: 16,
            alignItems: "center",
            justifyContent: "center",
            backgroundColor: "white",
          }}
        >
          <Image
            source={personicon}
            style={{
              width: 24,
              height: 24,
              borderRadius: 12,
            }}
            resizeMode="cover"
          />
        </View>
      </TouchableOpacity>
    </View>
  ),
});

  const _logout = async () => {
    await AsyncStorage.clear();
    User.setlogin(false);
    User.getRefreshMainScreen();
  };


  const barFilterPressed = (barFilterTapped) => {
    setExpandBarSelection(true);
  };

  const lineFilterPressed = (lineFilterTapped) => {
    setExpandLineSelection(true);
  };

  const flatListRef = useRef();

  const ScrollToTop = () => {
    flatListRef.current.scrollToOffset({ animated: true, offset: 0 });
  };

  const ScrollToBottom = () => {
    flatListRef.current.scrollToOffset({ animated: true, offset: 100 });
  };

  const [isSidebarOpen, setIsSidebarOpen] = useState(true);

  const toggleSidebar = () => {
    setIsSidebarOpen(!isSidebarOpen);
  };

  //////////////////////////////////////////////////////////////////

  // 2024-05-21 - fulfillment assistant changes

  // get UserOrder by airway bill number
  const getUserOrderByAwb = async (awb) => {
    const userOrderSnapshot = await firebase.firestore()
      .collection(Collections.UserOrder)
      .where('epParcelAwbList', 'array-contains', awb)
      .where('outletId', '==', currOutletId)
      .where('orderStatus', '==', USER_ORDER_STATUS.ORDER_COMPLETED)
      .limit(1)
      .get();

    let foundOrder = null;
    if (!userOrderSnapshot.empty) {
      foundOrder = userOrderSnapshot.docs[0].data();
    }

    // setScannedUserOrder(foundOrder);
    console.log('get user order by awb', foundOrder);
    setUserOrderBybc(foundOrder);
    return foundOrder;
  };

  // get OutletItem by barcode
  const getOutletItemByBarcode = (productBarcode) => {
    return outletItems.find(item => item.bc === productBarcode); // return null if not existed
  };

  // get CartItem by barcode + UserOrder
  const getCartItemByBarcode = (productBarcode, userOrder) => {
    let cartItemFound = false;
    const foundCartItem = userOrder.cartItems.find(cartItem => {
      if (isCartItemMatchedWithBarcode(productBarcode, cartItem)) {
        if (isCartItemPacked(cartItem, userOrder)) {
          alert('Info. This product has reached the maximum amount.')
          return false; // return false (even matched), since already fully packed
        }
        else {
          cartItemFound = true;
          return true;
        }
      }
      else {
        // alert('Info, This product is not found in the current order.')
        return false;
      }
    });
    if (foundCartItem) {
      console.log('get cart item by barcode', foundCartItem);
      setCartItemBybc(foundCartItem);
      CommonStore.update(s => {
        s.isLoading = true;
      });
    } else {
      if (!cartItemFound) {
        alert(' SKU is not found .');
      }
    }
    return foundCartItem;
  };

  // get true/false, if the scanned barcode matched with the cartItem (will check with addons as well)
  const isCartItemMatchedWithBarcode = (productBarcode, cartItem) => {
    let isMatchedByAddOns = false;
    let isQualifiedAddOnExisted = false;

    if (cartItem.addOns && cartItem.addOns.length > 0) {
      const atLeastGotOneAddOnBarcode = cartItem.addOns.filter(addOn => addOn.bc).length > 0;

      if (atLeastGotOneAddOnBarcode) {
        isQualifiedAddOnExisted = true;

        const foundAddOn = cartItem.addOns.find(addOn => addOn.bc === productBarcode);

        if (foundAddOn) {
          isMatchedByAddOns = true;
        }
        else {
          isMatchedByAddOns = false;
        }
      }
    }

    if (isQualifiedAddOnExisted) {
      return isMatchedByAddOns;
    }
    else {
      if (cartItem.bc === productBarcode) {
        return true;
      }
      else {
        return false;
      }
    }
  };

  // check if the cart item is already full packed or not 
  // (if let's say still got 1 item haven't packed, will still return false)
  const isCartItemPacked = (cartItem, userOrder) => {
    const packKey = `${userOrder.uniqueId}-${cartItem.itemId}-${cartItem.cartItemDate.toString()}`;

    if (global.packedCartItemDict[packKey] >= cartItem.quantity) {
      return true; // already fully packed
    }
    else {
      return false; // still havent packed/fully packed
    }
  };

  // to pack/record (temporarily first) cart item's quantity that already scanned
  const packCartItem = (cartItem, packQty, userOrder) => {
    const packKey = `${userOrder.uniqueId}-${cartItem.itemId}-${cartItem.cartItemDate.toString()}`;

    if (global.packedCartItemDict[packKey] !== undefined) {
      if ((global.packedCartItemDict[packKey] < cartItem.quantity)) {
        global.packedCartItemDict[packKey] += packQty;
      }
    }
    else {
      global.packedCartItemDict[packKey] = packQty;
    }
  };

  // can call this to release some memory (after submitted order packing info to db)
  const clearPackedCartItemDict = () => {
    global.packedCartItemDict = {};
  };

  // api - save pack info into UserOrder (can call this when going to submit order pack info)
  const updateUserOrderPackInfo = (userOrder) => {
    var body = {
      userOrderId: userOrder.uniqueId,
    };

    CommonStore.update(s => {
      s.isLoading = true;
    });

    APILocal.updateUserOrderPackInfo({ body: body }).then((result) => {
      // ApiClient.POST(API.createStockTakeProduct, body).then((result) => {
      console.log("result", result);

      if (result && result.status === "success") {
        CommonStore.update(s => {
          s.isLoading = false;
        });

        if (window.confirm("Success\n\nOrder has been packed successfully.")) {
          props.navigation.navigate('RA Dashboard - KooDoo Assistant');
        };

      } else {
        CommonStore.update(s => {
          s.isLoading = false;
        });

        window.confirm("Info\n\nPlease try again later.");
      }
    });
  };

  // to get cart item's quantity that is already packed
  const getPackedItemQty = (cartItem, userOrder) => {
    const packKey = `${userOrder.uniqueId}-${cartItem.itemId}-${cartItem.cartItemDate.toString()}`;

    if (typeof global.packedCartItemDict[packKey] === 'number') {
      CommonStore.update(s => {
        s.isLoading = false;
      });
      return global.packedCartItemDict[packKey];
    }
    else {
      return 0;
    }
  };

  const renderItem = ({ item, index }) => {
    return (
      <>
        <View style={{ flexDirection: "row", alignItems: 'center', marginTop: 15 }}>
          <View style={{ paddingLeft: 5, width: '50%' }}>
            <Text style={{ fontSize: 14, fontFamily: 'NunitoSans-Regular' }}>
              {item.name}
            </Text>
          </View>
          <View style={{ paddingLeft: 5, width: '49%' }}>
            <Text style={{ fontFamily: 'NunitoSans-Regular', textAlign: 'center' }}>
              {`${getPackedItemQty(item, userOrderBybc)}/${item.quantity}`}
            </Text>
          </View>

        </View>
        {/* <View style={{ width: '100%', height: 1, backgroundColor: Colors.descriptionColor, marginTop: 10, }} /> */}
      </>
    );

  };

  //////////////////////////////////////////////////////////////////

  // function end

  return (
    // <View style={styles.container}>
    //   <View style={styles.sidebar}>
    // Whole page view
    <View
        style={{
          flex: 1,
          backgroundColor: Colors.highlightColor,
        }}
      >
      {isMobile() && <TopBar navigation={navigation} />}
      <View
        style={[
          styles.container,
          {
            height: windowHeight,
            width: windowWidth,
            ...getTransformForScreenInsideNavigation(),
          },
        ]}
      >
        <View style={[{ height: windowHeight, flex: 9, },
        {
          ...isMobile() && {
            flex: 3,
            //backgroundColor:'yellow',
          }
        },
        ]}>

        <View
          style={{
            width: "100%",
            backgroundColor: Colors.darkBgColor,
            paddingVertical: 12,
            paddingHorizontal: 10,
            flexDirection: "row",
            alignItems: "center",
            justifyContent: "space-between", // distribute left & right
          }}
        >
          {/* Left: Back Button */}
          <TouchableOpacity
            style={{ flexDirection: "row", alignItems: "center" }}
            onPress={() => props.navigation.goBack()}
          >
            <Icon name="chevron-left" size={20} color={Colors.whiteColor} />
            <Text
              style={{
                fontFamily: "NunitoSans-Bold",
                fontSize: 16,
                color: Colors.whiteColor,
                marginLeft: 5,
              }}
            >
              Back
            </Text>
          </TouchableOpacity>

          {/* Middle: Title */}
          <Text
            style={{
              fontSize: switchMerchant ? 20 : 22,
              fontFamily: "NunitoSans-Bold",
              color: Colors.whiteColor,
              flex: 1,
              textAlign: "center",
              marginHorizontal: 5,
            }}
            numberOfLines={1}
            ellipsizeMode="tail"
          >
            Order Fulfillment Assistant
          </Text>

          {/* Right: Menu Icon */}
          <TouchableOpacity
            onPress={() => props.navigation.navigate("Profile")}
          >
            <Image
              style={{
                width: 28,
                height: 28,
                marginLeft: 10,
              }}
              source={require("../assets/image/drawer.png")}
            />
          </TouchableOpacity>
        </View>

          {/* <View style={{
            flexDirection: 'row',
          }}>
            <View
              style={[
                {
                  flexDirection: 'row',
                  alignItems: 'center',
                  borderRadius: 10,
                  marginLeft: 10,
                },
              ]}>

              <DropDownPicker
                style={{
                  padding: 2,
                  width: windowWidth * 0.24,
                  backgroundColor: Colors.whiteColor,
                  height: windowHeight * 0.03,
                  borderRadius: 5,
                  borderWidth: 1,
                  flexDirection: "row",
                  borderColor: "#E5E5E5",
                  shadowColor: '#000',
                  shadowOffset: {
                    width: 0,
                    height: 2,
                  },
                  shadowOpacity: 0.22,
                  shadowRadius: 3.22,
                  elevation: 3,
                }}
                dropDownContainerStyle={{
                  width: windowWidth * 0.24,
                  backgroundColor: Colors.whiteColor,
                  borderColor: "#E5E5E5",
                  flexDirection: "row",
                }}
                labelStyle={{
                  flexDirection: "row",
                  fontSize: RFPercentage(1.5),
                  marginLeft: 5,
                  fontFamily: 'NunitoSans-Regular',
                }}
                textStyle={{
                  flexDirection: "row",
                  fontSize: RFPercentage(1.5),
                  fontFamily: 'NunitoSans-Regular',
                  marginLeft: 5,


                }}
                selectedItemContainerStyle={{
                  flexDirection: "row",
                }}

                showArrowIcon={true}
                ArrowDownIconComponent={({ style }) => (
                  <Ionicon
                    size={RFPercentage(1.5)}
                    color={Colors.fieldtTxtColor}
                    style={{ marginLeft: '1%', marginTop: '5%' }}

                    name="chevron-down-outline"

                  />
                )}
                ArrowUpIconComponent={({ style }) => (
                  <Ionicon
                    size={RFPercentage(1.8)}
                    color={Colors.fieldtTxtColor}
                    style={{ marginLeft: '1%', marginTop: '5%' }}
                    name="chevron-up-outline"
                  />
                )}

                showTickIcon={true}
                TickIconComponent={({ press }) => (
                  <Ionicon
                    style={{ paddingHorizontal: 5, marginTop: 5 }}
                    color={
                      press ? Colors.fieldtBgColor : Colors.primaryColor
                    }
                    name={'md-checkbox'}
                    size={14}
                  />
                )}
                dropDownDirection="BOTTOM"
                // disabled={selectedStockTransferEdit ? true : false}
                items={[
                  { label: 'All Orders', value: 1 },

                ]}
                //   value={filterType}
                placeholder={'Select 1'}
                placeholderStyle={{
                  color: Colors.fieldtTxtColor,
                  // marginTop: 15,
                }}
                onSelectItem={(item) => {
                  //       setFilterType(item.value);
                }}
              //   open={openFilter}
              //  setOpen={setOpenFilter}
              />
            </View>
            <TouchableOpacity

              style={{
                width: windowWidth * 0.33,
                alignItems: "center",
                borderWidth: 1,
                padding: 3,
                borderColor: Colors.primaryColor,
                backgroundColor: "#4E9F7D",
                borderRadius: 5,
                //width: 160,
                marginLeft: 10,
                paddingHorizontal: 8,
                shadowOffset: {
                  width: 0,
                  height: 2,
                },
                shadowOpacity: 0.22,
                shadowRadius: 3.22,
                elevation: 1,

              }}
              onPress={() => {
                // Handle onPress event
              }}
            >

              <Text
                style={{
                  color: Colors.whiteColor,
                  //marginLeft: 5,
                  fontSize: RFPercentage(1.5),
                  fontFamily: "NunitoSans-Bold",
                }}
              >
                View Live Monitor
              </Text>

            </TouchableOpacity>
          </View> */}
          <View style={{
            width: '90%',
            marginLeft: '5%',

          }}
          >
            <View
              style={{
                borderWidth: 1,
                marginLeft: '2%',
                width: '95%',
                backgroundColor: Colors.whiteColor,
                height: windowHeight * 0.75,
                marginTop: 5,
                borderRadius: 5,
                padding: windowHeight * 0.02,
                borderColor: "#E5E5E5",
                shadowColor: '#000',
                shadowOffset: {
                  width: 0,
                  height: 2,
                },
                shadowOpacity: 0.22,
                shadowRadius: 3.22,
                elevation: 1,
              }}

            >
               <ScrollView
                showsVerticalScrollIndicator={false} contentContainerStyle={{ height: windowHeight * 0.75 }}>
              <TouchableOpacity
                style={{
                  justifyContent: "center",
                  flexDirection: "row",
                  borderWidth: 1,
                  borderColor: Colors.primaryColor,
                  backgroundColor: Colors.primaryColor,
                  borderRadius: 5,
                  width: switchMerchant ? 120 : 170,
                  paddingHorizontal: 10,
                  height: switchMerchant ? 35 : 40,
                  alignItems: "center",
                  shadowOffset: {
                    width: 0,
                    height: 2,
                  },
                  shadowOpacity: 0.22,
                  shadowRadius: 3.22,
                  elevation: 1,
                  zIndex: -1,
                  marginRight: 10,
                  marginBottom: 10,
                }}
                onPress={() => {
                  updateUserOrderPackInfo(userOrderBybc);
                }}>
                <Text
                  style={{
                    color: Colors.whiteColor,
                    //marginLeft: 5,
                    fontSize: switchMerchant ? 10 : 16,
                    fontFamily: "NunitoSans-Bold",
                  }}
                >
                  SAVE
                </Text>
              </TouchableOpacity>
             
                <TouchableOpacity
                  style={{
                    justifyContent: "center",
                    flexDirection: "row",
                    borderWidth: 1,
                    borderColor: Colors.primaryColor,
                    backgroundColor: Colors.primaryColor,
                    borderRadius: 5,
                    width: switchMerchant ? 120 : 170,
                    paddingHorizontal: 10,
                    height: switchMerchant ? 35 : 40,
                    alignItems: "center",
                    shadowOffset: {
                      width: 0,
                      height: 2,
                    },
                    shadowOpacity: 0.22,
                    shadowRadius: 3.22,
                    elevation: 1,
                    zIndex: -1,
                    marginBottom: 10,
                  }}
                  onPress={() => {
                    handleIconPress();
                    setShowScanner(true);
                    setShowScannerItem(false);
                    global.cameraScanState = 'reader';
                    }}
                  >
                  <MaterialCommunityIcons
                    name={showScanner ? "close" : "qrcode-scan"}
                    size={15}
                    style={{ color: Colors.whiteColor, }}
                  />
                  <Text
                    style={{
                      color: Colors.whiteColor,
                      marginLeft: 5,
                      fontSize: switchMerchant ? 10 : 15.5,
                      fontFamily: "NunitoSans-Bold",
                    }}
                  >
                    SHIPPING LABEL
                  </Text>
                </TouchableOpacity>
                    {/* //////////////////////////////////// */}

                    {showScanner && (
                      <View style={{ flex: 1, width: "100%", height: 300, marginVertical: 10 }}>
                        <Camera
                          style={{ flex: 1 }}
                          showFrame={true}
                          scanBarcode={true}
                          onReadCode={(event) => {
                            const scannedValue = event.nativeEvent.codeStringValue;
                            console.log("Shipping scanned:", scannedValue);
                            onScanSuccess(scannedValue, "shipping");
                          }}
                        />
                      </View>
                    )}

                    {/* //////////////////////////////////// */}
                <TouchableOpacity
                  style={{
                    justifyContent: "center",
                    flexDirection: "row",
                    borderWidth: 1,
                    borderColor: Colors.primaryColor,
                    backgroundColor: Colors.primaryColor,
                    borderRadius: 5,
                    width: switchMerchant ? 120 : 170,
                    paddingHorizontal: 10,
                    height: switchMerchant ? 35 : 40,
                    alignItems: "center",
                    shadowOffset: {
                      width: 0,
                      height: 2,
                    },
                    shadowOpacity: 0.22,
                    shadowRadius: 3.22,
                    elevation: 1,
                    zIndex: -1,
                    marginRight: 10,
                    marginBottom: 20,
                  }}
                  onPress={() => {
                    handleIconPress2();
                    setShowScanner(false);
                    setShowScannerItem(true);
                    global.cameraScanState = 'reader2';
                    }}
                    disabled={false}
                  >
                  <MaterialCommunityIcons
                    name={showScannerItem ? "close" : "qrcode-scan"}
                    size={15}
                    style={{ color: Colors.whiteColor, }}
                  />
                  <Text
                    style={{
                      color: Colors.whiteColor,
                      marginLeft: 5,
                      fontSize: switchMerchant ? 10 : 16,
                      fontFamily: "NunitoSans-Bold",
                    }}
                  >
                    SKU
                  </Text>
                </TouchableOpacity>
                    {/* //////////////////////////////////// */}

                  {showScannerItem && (
                    <View style={{ flex: 1, width: "100%", height: 300, marginVertical: 10 }}>
                      <Camera
                        style={{ flex: 1 }}
                        showFrame={true}
                        scanBarcode={true}
                        onReadCode={(event) => {
                          const scannedValue = event.nativeEvent.codeStringValue;
                          console.log("SKU scanned:", scannedValue);
                          onScanSuccess(scannedValue, "sku");
                        }}
                      />
                    </View>
                  )}
                  
                    {/* //////////////////////////////////// */}
                <Text style={{
                  fontFamily: "NunitoSans-Bold",
                  fontSize: RFPercentage(1.9),

                }}>
                SHIPPING LABEL
                </Text>

                <TextInput
                  disabled={true}
                  underlineColorAndroid={Colors.fieldtBgColor}
                  style={{
                    fontSize: RFPercentage(1.8),
                    width: '100%',
                    height: 40,
                    backgroundColor: Colors.fieldtBgColor,
                    padding: 5,
                    marginVertical: 5,
                    borderRadius: 5,
                    borderWidth: 1,
                    borderColor: "#E5E5E5",
                    marginBottom: windowHeight * 0.015
                  }}
                  placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                  placeholder="SLabel"
                  // style={{
                  //     // paddingLeft: 1,
                  // }}
                  defaultValue={sLable}
                  onChangeText={(text) => {
                    setSLabel(text);
                  }}
                />

                <Text style={{
                  fontFamily: "NunitoSans-Bold",
                  fontSize: RFPercentage(1.9),
                }}>
                 ORDER ID
                </Text>

                <Text
                  disabled={true}
                  underlineColorAndroid={Colors.fieldtBgColor}
                  style={{
                    fontSize: RFPercentage(1.8),
                    width: '100%',
                    height: 35,
                    lineHeight: 23,
                    backgroundColor: Colors.fieldtBgColor,
                    padding: 5,
                    marginVertical: 5,
                    borderRadius: 5,
                    borderWidth: 1,
                    borderColor: "#E5E5E5",
                    marginBottom: windowHeight * 0.015,


                  }}
                >
                  {userOrderBybc ? userOrderBybc.orderId : 'N/A'}
                </Text>

             


                <Text style={{
                  fontFamily: "NunitoSans-Bold",
                  fontSize: RFPercentage(1.9),
                  marginTop: windowHeight * 0.02
                }}>
                  ORDER DETAILS
                </Text>
                {/* <View style={{
                flexDirection: "row",
                marginTop: windowHeight * 0.015
              }}
              >
                <Text style={{
                  fontFamily: "NunitoSans-Regular",
                  fontSize: RFPercentage(1.57),
                  color: Colors.darkBgColor,
                }}>
                  Channel
                </Text>
                <Text style={{

                  fontSize: RFPercentage(1.57),
                  marginLeft: windowWidth * 0.22
                }}>
                  Dreamel-TESTING
                </Text>
              </View> */}

                <View style={{
                  flexDirection: "row",
                  marginTop: windowWidth * 0.017,
                }}
                >
                  <Text style={{
                    fontFamily: "NunitoSans-SemiBold",
                    fontSize: switchMerchant ? 10 : 14,
                    color: Colors.darkBgColor,
                    width: windowWidth * 0.3
                  }}>
                    Order ID
                  </Text>
                  <Text style={{
                    fontFamily: "NunitoSans-Regular",
                    fontSize: switchMerchant ? 10 : 14,
                    marginLeft: windowWidth * 0.08
                  }}>
                    {userOrderBybc ? userOrderBybc.orderId : 'N/A'}
                  </Text>
                </View>

                <View style={{
                  flexDirection: "row",
                  marginTop: windowHeight * 0.01
                }}
                >
                  <Text style={{
                    fontFamily: "NunitoSans-SemiBold",
                    fontSize: switchMerchant ? 10 : 14,
                    color: Colors.darkBgColor,
                    width: windowWidth * 0.3
                  }}>
                    Shopping Company
                  </Text>
                  <Text style={{
                    fontFamily: "NunitoSans-Regular",
                    fontSize: switchMerchant ? 10 : 14,
                    marginLeft: windowWidth * 0.08
                  }}>
                    {userOrderBybc ? userOrderBybc.epCourier : 'N/A'}
                  </Text>
                </View>

                {/* <View style={{
                flexDirection: "row",
                marginTop: windowWidth * 0.01
              }}
              >
                <Text style={{
                  fontFamily: "NunitoSans-Regular",
                  fontSize: RFPercentage(1.57),
                  color: Colors.darkBgColor,
                }}>
                  Marketplace Order ID
                </Text>
                <Text style={{
                  fontSize: RFPercentage(1.57),
                  marginLeft: windowWidth * 0.061
                }}>
                  21012312AAAA
                </Text>

              </View> */}
                <View
                  style={{
                    width: '100%',
                    backgroundColor: Colors.fieldtBgColor,
                    // height: '6%',
                    marginTop: 18,
                    borderRadius: 5,
                  }}
                >
                  <View style={{
                    flexDirection: "row",
                    alignItems: 'center',
                    padding: 5,
                  }}>
                    <View style={{ width: '50%' }}>
                      <Text style={{
                        fontFamily: "NunitoSans-Regular",
                        fontSize: switchMerchant ? 10 : 14,
                      }}>
                        Product Name
                      </Text>
                    </View>
                    <View style={{ width: '49%', paddingLeft: 5, }}>
                      <Text style={{
                        fontFamily: "NunitoSans-Regular",
                        fontSize: switchMerchant ? 10 : 14,
                      }}>
                        {'Packed/Total Quantity'}
                      </Text>
                    </View>
                  </View>

                </View>
                {userOrderBybc && userOrderBybc.packed !== true ?
                  <FlatList
                    showsVerticalScrollIndicator={false}
                    data={userOrderBybc.cartItems}
                    renderItem={renderItem}
                    keyExtractor={(item, index) => String(index)}
                    contentContainerStyle={{
                      paddingBottom: 40,
                    }}
                  />
                  : null
                }
              </ScrollView>
            </View>

          </View>
        </View >
      </View >
    </View>


  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.highlightColor,
    flexDirection: "row",
  },
  iosStyle: {
    paddingHorizontal: 30,
  },
  headerLogo: {
    width: 112,
    height: 25,
    marginLeft: 10,
  },
  headerLogo1: {
    width: "100%",
    height: "100%",
  },
  list: {
    paddingVertical: 20,
    paddingHorizontal: 30,
    flexDirection: "row",
    alignItems: "center",
  },
  listItem: {
    fontFamily: "NunitoSans-Regular",
    marginLeft: 20,
    color: Colors.descriptionColor,
    fontSize: 16,
  },
  sidebar: {
    width: Dimensions.get("screen").width * Styles.sideBarWidth,



    elevation: 16,
  },
  content: {
    padding: 30,
    width: Dimensions.get("screen").width * (1 - Styles.sideBarWidth),
    // backgroundColor: 'lightgrey',
    backgroundColor: Colors.highlightColor,
    height: Dimensions.get("screen").height * 1,
  },
  textInput: {
    fontFamily: "NunitoSans-Regular",
    width: 300,
    height: 50,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 10,
    marginRight: 0,
    flex: 1,
    flexDirection: "row",
  },
  textInputLocation: {
    fontFamily: "NunitoSans-Regular",
    width: 300,
    height: 100,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 10,
    marginRight: 10,
  },
  textInput8: {
    fontFamily: "NunitoSans-Regular",
    width: 75,
    height: 50,
    flex: 1,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 5,
  },
  textInput9: {
    fontFamily: "NunitoSans-Regular",
    width: 110,
    height: 50,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 5,
    flexDirection: "row",
    justifyContent: "center",
  },
  textInput10: {
    fontFamily: "NunitoSans-Regular",
    width: 200,
    height: 50,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 5,
    flexDirection: "row",
    justifyContent: "center",
  },
  textInput1: {
    fontFamily: "NunitoSans-Regular",
    width: 250,
    height: 40,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 5,
    marginBottom: 10,
  },
  textInput2: {
    fontFamily: "NunitoSans-Regular",
    width: 300,
    height: 50,
    paddingHorizontal: 20,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 5,
    marginRight: 30,
  },
  textInput3: {
    fontFamily: "NunitoSans-Regular",
    width: "85%",
    height: 50,
    backgroundColor: Colors.fieldtBgColor,
    marginTop: 10,
    marginBottom: 10,
    borderRadius: 10,
    alignSelf: "center",
    paddingHorizontal: 10,
  },
  textInput4: {
    width: "85%",
    height: 70,
    paddingHorizontal: 20,
    backgroundColor: Colors.fieldtBgColor,
    marginTop: 10,
    marginBottom: 10,
    borderRadius: 10,
  },
  textInput5: {
    fontFamily: "NunitoSans-Regular",
    width: 300,
    height: 50,
    paddingHorizontal: 20,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 50,
  },
  textInput6: {
    fontFamily: "NunitoSans-Regular",
    width: "80 %",
    padding: 16,
    backgroundColor: Colors.fieldtBgColor,
    marginRight: 30,
    borderRadius: 10,
    alignSelf: "center",
  },
  textInput7: {
    fontFamily: "NunitoSans-Regular",
    width: 300,
    height: 80,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 7,
    marginRight: 30,
  },
  button: {
    backgroundColor: Colors.primaryColor,
    width: 150,
    padding: 8,
    borderRadius: 10,
    alignItems: "center",
    alignSelf: "center",
    marginBottom: 20,
  },
  button1: {
    width: "15%",
    padding: 8,
    borderRadius: 10,
    alignItems: "center",
    alignSelf: "center",
    marginBottom: 20,
  },
  button2: {
    backgroundColor: Colors.primaryColor,
    width: "60%",
    padding: 8,
    borderRadius: 10,
    alignItems: "center",
    marginLeft: "2%",
  },
  button3: {
    backgroundColor: Colors.primaryColor,
    width: "30%",
    height: 50,
    borderRadius: 10,
    alignItems: "center",
    alignSelf: "center",
    marginBottom: 30,
  },
  textSize: {
    fontSize: 19,
    fontFamily: "NunitoSans-SemiBold",
  },
  viewContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    flex: 0,
    width: "100%",
    marginBottom: 15,
  },
  openHourContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    flex: 1,
    marginBottom: 15,
    width: "100%",
  },
  addButtonView: {
    flexDirection: "row",
    borderWidth: 1,
    borderColor: Colors.primaryColor,
    borderRadius: 5,
    width: 200,
    height: 40,
    alignItems: "center",
  },
  addButtonView1: {
    flexDirection: "row",
    borderWidth: 1,
    borderColor: Colors.primaryColor,
    borderRadius: 5,
    width: 150,
    height: 40,
    alignItems: "center",
  },
  addNewView: {
    flexDirection: "row",
    justifyContent: "flex-start",
    marginBottom: 65,
    marginTop: 7,
    width: "83%",
    alignSelf: "flex-end",
  },
  addNewView1: {
    flexDirection: "row",
    justifyContent: "center",
    marginBottom: 10,
    alignItems: "center",
  },
  merchantDisplayView: {
    flexDirection: "row",
    flex: 1,
    marginLeft: "17%",
  },
  shiftView: {
    flexDirection: "row",
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 50,
    width: 200,
    height: 60,
    alignItems: "center",
    marginTop: 30,
    alignSelf: "center",
    justifyContent: "center",
  },
  shiftText: {
    // marginLeft: '15%',
    color: Colors.primaryColor,
    fontFamily: "NunitoSans-SemiBold",
    fontSize: 25,
  },
  closeView: {
    flexDirection: "row",
    borderRadius: 5,
    borderColor: Colors.primaryColor,
    borderWidth: 1,
    width: 200,
    height: 40,
    alignItems: "center",
    marginTop: 30,
    alignSelf: "center",
  },
  taxView: {
    flexDirection: "row",
    borderWidth: 2,
    borderColor: Colors.primaryColor,
    borderRadius: 5,
    width: 150,
    height: 40,
    alignItems: "center",
    marginTop: 20,
    alignSelf: "flex-end",
  },
  sectionView: {
    flexDirection: "row",
    borderRadius: 5,
    padding: 16,
    alignItems: "center",
  },
  receiptView: {
    flexDirection: "row",
    borderWidth: 1,
    borderColor: Colors.primaryColor,
    borderRadius: 5,
    width: 200,
    height: 40,
    alignItems: "center",
    alignSelf: "flex-end",
  },
  pinBtn: {
    backgroundColor: Colors.fieldtBgColor,
    width: 70,
    height: 70,
    marginBottom: 16,
    alignContent: "center",
    justifyContent: "center",
    alignItems: "center",
    borderRadius: 10,
  },
  pinNo: {
    fontSize: 20,
    fontWeight: "bold",
  },
  confirmBox: {
    width: "30%",
    height: "30%",
    borderRadius: 30,
    backgroundColor: Colors.whiteColor,
  },
  logoTxt: {
    color: Colors.descriptionColor,
    fontSize: 20,
    letterSpacing: 7,
    marginTop: 10,
    alignSelf: "center",
    marginBottom: 40,
  },
  headerLeftStyle: {
    width: useWindowDimensions.width * 0.17,
    justifyContent: "center",
    alignItems: "center",
  },
});
export default FulfillmentAssistant;
