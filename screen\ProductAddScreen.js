import { Text } from "react-native-fast-text";
import React, { useState, useEffect, useCallback } from 'react';
import {
  StyleSheet,
  Image,
  View,
  Alert,
  TouchableOpacity,
  Dimensions,
  TextInput,
  Modal as ModalComponent,
  Platform,
  ActivityIndicator,
  useWindowDimensions,
  TouchableWithoutFeedback,
  Keyboard,
} from 'react-native';
import { ModalView as ModalViewComponent } from 'react-native-multiple-modals';
import { ScrollView, FlatList } from "react-native-gesture-handler";
import { useFocusEffect } from '@react-navigation/native';
import Feather from 'react-native-vector-icons/Feather';
import Colors from '../constant/Colors';
import Close from 'react-native-vector-icons/AntDesign';
import SideBar from './SideBar';
import Icon from 'react-native-vector-icons/Feather';
import Entypo from 'react-native-vector-icons/Entypo';
import FontAwesome5 from 'react-native-vector-icons/FontAwesome5';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { launchImageLibrary } from 'react-native-image-picker';
import API from '../constant/API';
import DropDownPicker from 'react-native-dropdown-picker';
import * as User from '../util/User';
import Styles from '../constant/Styles';
import ApiClient from '../util/ApiClient';
import Switch from 'react-native-switch-pro';
import {
  extractImageNameFromUrl,
  getTransformForModalFullScreen,
  getTransformForModalInsideNavigation,
  getTransformForScreenInsideNavigation,
  isTablet,
  parseImagePickerResponse
} from '../util/common';
import { OutletStore } from '../store/outletStore';
import { CommonStore } from '../store/commonStore';
import { MerchantStore } from '../store/merchantStore';
import {
  parseValidIntegerText,
  parseValidPriceText,
  uploadImageToFirebaseStorage,
} from '../util/common';
import { UserStore } from '../store/userStore';
import 'react-native-get-random-values';
import { v4 as uuidv4 } from 'uuid';
import AsyncImage from '../components/asyncImage';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { useKeyboard } from '../hooks';
import RNPickerSelect from 'react-native-picker-select';
import AntDesign from 'react-native-vector-icons/AntDesign';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import XLSX from 'xlsx';
import moment from 'moment';
import DocumentPicker from 'react-native-document-picker';
import Ionicon from 'react-native-vector-icons/Ionicons';
import APILocal from '../util/apiLocalReplacers';
import {
  CHARGES_TYPE,
  CHARGES_TYPE_DROPDOWN_LIST,
  ORDER_TYPE,
  PRODUCT_PRICE_TYPE,
  PRODUCT_PRICE_TYPE_DROPDOWN_LIST,
  EXPAND_TAB_TYPE,
  OUTLET_SHIFT_STATUS,
  SPECIAL_TAGS_DROPDOWN_LIST,
} from '../constant/common';
import DateTimePickerModal from 'react-native-modal-datetime-picker';
import {
  EFFECTIVE_DAY_DROPDOWN_LIST1,
  EFFECTIVE_DAY,
} from '../constant/promotions';
import { Table, TableWrapper, Row, Rows, Cell } from 'react-native-table-component';
import { nanoid } from 'nanoid';
import RadioForm, { RadioButton, RadioButtonInput, RadioButtonLabel } from 'react-native-simple-radio-button';
import Tooltip from 'react-native-walkthrough-tooltip';
import DraggableFlatList from 'react-native-draggable-flatlist';
import { PRINTER_USAGE_TYPE_DROPDOWN_LIST } from "../constant/printer";
const RNFS = require('@dr.pogodin/react-native-fs');
const ModalView = Platform.OS === 'android' ? ModalComponent : ModalViewComponent;


const ProductAddScreen = React.memo((props) => {
  const { navigation } = props;

  const { width: windowWidth, height: windowHeight } = useWindowDimensions();
  const [keyboardHeight] = useKeyboard();

  const [switchMerchant, setSwitchMerchant] = useState(!isTablet());

  /////////////////////////////////////////////////////////////////////////
  // Shared States
  const [outletId, setOutletId] = useState(User.getOutletId());
  const selectedOutletCategoryEdit = CommonStore.useState(s => s.selectedOutletCategoryEdit);

  /////////////////////////////////////////////////////////////////////////
  // Local States
  const [temp, setTemp] = useState('');

  /////////////////////////////////////////////////////////////////////////
  // 16 Aug 2024 - Reordered Req Body State

  // Image
  const [image, setImage] = useState('');
  const [imageType, setImageType] = useState('');
  const [isImageChanged, setIsImageChanged] = useState(false);
  const [isClearProductImage, setIsClearProductImage] = useState(false);

  // Outlet Selected
  const [selectedOutletId, setSelectedOutletId] = useState('');
  const [selectedOutletList, setSelectedOutletList] = useState([]); // multi-outlets
  const [outletDropdownList, setOutletDropdownList] = useState([]);

  // Tags
  const [tagModal, setTagModal] = useState(false);
  const [userTagList, setUserTagList] = useState([]);
  const [userTagDropdownList, setUserTagDropdownList] = useState([]);
  const [selectedUserTagList, setSelectedUserTagList] = useState([]);
  const [searchingUserTagText, setSearchingUserTagText] = useState('');

  // Printer Area
  const [selectedPrinterAreaList, setSelectedPrinterAreaList] = useState([]);
  const [printerAreaDropdownList, setPrinterAreaDropdownList] = useState([]);

  const [selectedPrintingTypeList, setSelectedPrintingTypeList] = useState([]);
  const [printingTypeDropdownList, setPrintingTypeDropdownList] = useState(PRINTER_USAGE_TYPE_DROPDOWN_LIST);

  // Takeaway Charges
  const [pickUpCharges, setPickUpCharges] = useState('0.00');
  const [pickUpChargesType, setPickUpChargesType] = useState(CHARGES_TYPE.AMOUNT_BASED);
  const [pickUpChargesActive, setPickUpChargesActive] = useState(false);

  // Delivery Charges
  const [deliveryCharges, setDeliveryCharges] = useState('0.00');
  const [deliveryChargesType, setDeliveryChargesType] = useState(CHARGES_TYPE.AMOUNT_BASED);
  const [deliveryChargesActive, setDeliveryChargesActive] = useState(false);

  // Other D. Charges
  const [otherDCharges, setOtherDCharges] = useState('0.00');
  const [otherDChargesType, setOtherDChargesType] = useState(CHARGES_TYPE.AMOUNT_BASED);
  const [otherDChargesActive, setOtherDChargesActive] = useState(false);

  // Print Docket
  const [printDocketQuantity, setPrintDocketQuantity] = useState('1');

  // Reservation Menu
  const [isReservationMenu, setIsReservationMenu] = useState(false);

  // Exclude from Manual Discount
  const [noManualDisc, setNoManualDisc] = useState(false);

  // Do Not Display
  const [hideInOrderTypes, setHideInOrderTypes] = useState([]);

  ///////////////// RHS Menu Items /////////////////
  // Item Details
  const [itemName, setItemName] = useState('');
  const [itemDescription, setItemDescription] = useState('');
  const [sku, setSku] = useState('');
  const [itemSKU, setItemSKU] = useState('');
  const [itemDpName, setDpName] = useState('');
  const [itemPrice, setItemPrice] = useState('');
  const [itemPriceType, setItemPriceType] = useState(PRODUCT_PRICE_TYPE.FIXED);
  const [itemQuantity, setItemQuantity] = useState(0);

  // 2024-08-15 - special tags support
  const [specialTags, setSpecialTags] = useState([]);
  ////////////////////////////////////////////////////////////////////


  const [costPrice, setCostPrice] = useState('')
  const [itemCredit, setItemCredit] = useState(''); // Points

  //Stock Count
  const [isStockCountActive, setIsStockCountActive] = useState(false);
  const [stockCount, setStockCount] = useState('');

  // Category
  const [outletCategoryDropdownList, setOutletCategoryDropdownList] = useState([]);
  const [selectedOutletCategoryId, setSelectedOutletCategoryId] = useState('');

  // Available On
  const [isAvailableDayActive, setIsAvailableDayActive] = useState(false);
  const [selectedEffectiveTypeOptions, setSelectedEffectiveTypeOptions] = useState([EFFECTIVE_DAY_DROPDOWN_LIST1[0].value]);
  const [effectiveStartTime, setEffectiveStartTime] = useState(moment().startOf('day').toDate());
  const [effectiveEndTime, setEffectiveEndTime] = useState(moment().endOf('day').toDate());
  const [showEffectiveStartTimePicker, setShowEffectiveStartTimePicker] = useState(false);
  const [showEffectiveEndTimePicker, setShowEffectiveEndTimePicker] = useState(false);

  // Variant AddOn Options
  const [variantGroupList, setVariantGroupList] = useState([]);
  const [addOnGroupList, setAddOnGroupList] = useState([]);

  const [showVariantModal, setShowVariantModal] = useState(false);
  const [showAddOnModal, setShowAddOnModal] = useState(false);

  const [variantGroupIndex, setVariantGroupIndex] = useState(0);
  const [addOnGroupIndex, setAddOnGroupIndex] = useState(0);

  /////////////////////////////////////////////////////////////////////////

  const [myTextInput, setMyTextInput] = useState(React.createRef());

  /////////////////////////////////////////////////////////////////////////\
  const [exportModal, setExportModal] = useState(false);
  const [importModal, setImportModal] = useState(false);

  const [loadingModal, setLoadingModal] = useState(false);
  const [isSave, setIsSave] = useState(false);
  const [isDelete, setIsDelete] = useState(false);
  //////////////////////////////////////////////////////////////

  const [stockLinkItems, setStockLinkItems] = useState([]);
  const [outletSupplyItemDropdownList, setOutletSupplyItemDropdownList] = useState([]);

  ////////////////////////////////////////////////////////////////////

  // docket support
  const [isDocket, setIsDocket] = useState(false);

  ////////////////////////////////////////////////////////////////////

  // 2022-08-18 - Product and outlet supply item table

  const [tableHead, setTableHead] = useState(['Head', 'Head2', 'Head3', 'Head4', 'Head5', 'Head6', 'Head7', 'Head8', 'Head9', 'Head10', 'Head11']);
  const [widthArr, setWidthArr] = useState([240, 240, 240, 240, 240, 240, 240, 240, 240, 240, 240]);

  const [tableData, setTableData] = useState([]);

  const [outletSupplyItemRows, setOutletSupplyItemRows] = useState([]);

  const [selectedOutletSupplyItemIdToAdd, setSelectedOutletSupplyItemIdToAdd] = useState('');

  const [isJustDeleted, setIsJustDeleted] = useState(false);
  const [justDeletedOutletSupplyItemId, setJustDeletedOutletSupplyItemId] = useState('');

  ////////////////////////////////////////////////////////////////////

  // 2022-12-12 - Prevent product deletion when got active shift

  const currOutletShiftStatus = OutletStore.useState(s => s.currOutletShiftStatus);

  ////////////////////////////////////////////////////////////////////

  // 2022-12-20 - Unit pricing support

  const [unitType, setUnitType] = useState('Gram');

  ////////////////////////////////////////////////////////////////////

  const tableElementHeader = (data, index, indexCol) => {
    return (
      <View style={{

      }}>
        <Text style={{
          fontFamily: 'NunitoSans-Bold',
          fontSize: 16,
          textAlign: 'center',
        }}>
          {
            data
          }
        </Text>
      </View>
    );
  };

  const tableElement = (data, index, indexCol) => {
    // if (data.isSelection) {

    // }

    return (
      // <TouchableOpacity onPress={() => { }}>
      //   <View style={{ width: 58, height: 18, backgroundColor: '#78B7BB', borderRadius: 2 }}>
      //     <Text style={{ textAlign: 'center', color: '#fff' }}>button</Text>
      //   </View>
      // </TouchableOpacity>
      // <TextInput>

      // </TextInput>
      (<>
        {
          data.isSelection
            ?
            <View style={{

            }}>
              <Text style={{
                fontFamily: 'NunitoSans-SemiBold',
                fontSize: 16,
                marginLeft: 15,
              }}>
                {
                  outletSupplyItemRows[index].name
                    ? outletSupplyItemRows[index].name
                    : 'N/A'
                }
              </Text>
            </View>
            // <>
            //   {
            //     outletSupplyItemDropdownList.length > 0 &&
            //       Object.keys(outletSupplyItemsDict).length > 0 &&
            //       data.outletSupplyItemId &&
            //       outletSupplyItemsDict[
            //       data.outletSupplyItemId
            //       ] ? (
            //       <RNPickerSelect
            //         // Icon={''}
            //         placeholder={{}}
            //         //placeholderStyle={{ }}
            //         style={{
            //           // textAlign: 'center', 
            //           // fontSize: 14,

            //           iconContainer: {
            //             opacity: 0,
            //           },

            //           inputAndroid: {
            //             textAlign: 'center',
            //             fontFamily: 'NunitoSans-Regular',
            //             fontSize: 12,
            //             borderWidth: 1,
            //             color: Colors.blackColor,
            //             // borderColor: Colors.primaryColor,
            //             // backgroundColor: Colors.primaryColor,
            //             // color: Colors.whiteColor,
            //             // borderRadius: 5,
            //             // width: Dimensions.get('window').width * 0.1,
            //             // height: Dimensions.get('window').height * 0.05,
            //             // justifyContent: 'center',
            //             // paddingHorizontal: 10,
            //             // paddingVertical: 5,

            //             // shadowColor: '#000',
            //             // shadowOffset: {
            //             //   width: 0,
            //             //   height: 2,
            //             // },
            //             // shadowOpacity: 0.22,
            //             // shadowRadius: 3.22,
            //             // elevation: 1,
            //           },
            //           inputIOS: {
            //             textAlign: 'center',
            //             fontFamily: 'NunitoSans-Regular',
            //             fontSize: 12,
            //             borderWidth: 1,
            //             color: Colors.blackColor,
            //             // borderColor: Colors.primaryColor,
            //             // backgroundColor: Colors.primaryColor,
            //             // color: Colors.whiteColor,
            //             // borderRadius: 5,
            //             // width: Dimensions.get('window').width * 0.1,
            //             // height: Dimensions.get('window').height * 0.05,
            //             // justifyContent: 'center',
            //             // paddingHorizontal: 10,
            //             // paddingVertical: 5,

            //             // shadowColor: '#000',
            //             // shadowOffset: {
            //             //   width: 0,
            //             //   height: 2,
            //             // },
            //             // shadowOpacity: 0.22,
            //             // shadowRadius: 3.22,
            //             // elevation: 1,
            //           },
            //         }}
            //         items={outletSupplyItemDropdownList.filter(selection => {
            //           return (
            //             !outletSupplyItemRows.find(rowItem => rowItem.outletSupplyItemId === selection.value)
            //             ||
            //             selection.value === data.outletSupplyItemId
            //           );
            //         })}
            //         value={
            //           outletSupplyItemRows[index].outletSupplyItemId
            //             ? outletSupplyItemRows[index].outletSupplyItemId
            //             : ''
            //         }
            //         onValueChange={(value) => {
            //           // var outletSupplyItemRow = outletSupplyItemRows.find(rowData => rowData.outletSupplyItemId === value);

            //           var outletSupplyItem = outletSupplyItems.find(item => item.uniqueId === value);

            //           if (outletSupplyItem && !justDeletedOutletSupplyItemId) {
            //             setJustDeletedOutletSupplyItemId(data.outletSupplyItemId);

            //             replaceOutletSupplyItemToStockArrays(data, {
            //               ...outletSupplyItem,

            //               outletSupplyItemIdPrev: data.outletSupplyItemId,
            //               outletSupplyItemId: value,
            //               quantityUsage: data.quantityUsage,
            //             });
            //           }

            //           // setStockLinkItems(
            //           //   stockLinkItems.map((stockLinkItem, i) =>
            //           //     i === index
            //           //       ? {
            //           //         ...stockLinkItem,
            //           //         outletSupplyItemId: value,
            //           //         sku: outletSupplyItemsDict[value].sku,
            //           //         skuMerchant:
            //           //           outletSupplyItemsDict[value].skuMerchant,
            //           //         quantityUsage: stockLinkItem.quantityUsage, // use previous
            //           //         name: outletSupplyItemsDict[value].name,
            //           //         unit: outletSupplyItemsDict[value].unit,
            //           //       }
            //           //       : stockLinkItem,
            //           //   ),
            //           // );
            //         }}
            //       />

            //       // <DropDownPicker
            //       //   containerStyle={{
            //       //     height: switchMerchant ? 30 : 30,
            //       //     zIndex: 2,
            //       //   }}
            //       //   arrowColor={'black'}
            //       //   arrowSize={20}
            //       //   arrowStyle={{
            //       //     fontWeight: 'bold',
            //       //     marginTop: switchMerchant ? -3 : -3,
            //       //   }}
            //       //   labelStyle={{
            //       //     fontFamily: 'NunitoSans-Regular',
            //       //     fontSize: switchMerchant ? 10 : 14,
            //       //   }}
            //       //   style={{
            //       //     width: switchMerchant ? 90 : 150,
            //       //     paddingVertical: 0,
            //       //     backgroundColor: Colors.fieldtBgColor,
            //       //     borderRadius: 10,
            //       //     fontSize: switchMerchant ? 11 : 14,
            //       //   }}
            //       //   placeholderStyle={{ color: Colors.fieldtTxtColor }}
            //       //   items={outletSupplyItemDropdownList}
            //       //   itemStyle={{
            //       //     justifyContent: 'flex-start',
            //       //     marginLeft: 5,
            //       //     zIndex: 2,
            //       //   }}
            //       //   placeholder={'Product'}
            //       //   zIndex={10000 + outletSupplyItemRows.length - index}
            //       //   //customTickIcon={(press) => <Ionicon name={"checkbox-outline"} color={press ? Colors.fieldtBgColor : Colors.primaryColor} size={25} />}
            //       //   searchable={true}
            //       //   searchableStyle={{
            //       //     paddingHorizontal: windowWidth * 0.0079,
            //       //   }}
            //       //   //{...(outletSupplyItemDropdownList.find(o => o.value === selectedOutletCategoryId)) && { defaultValue: selectedOutletCategoryId, }}
            //       //   defaultValue={
            //       //     outletSupplyItemRows[index].outletSupplyItemId
            //       //       ? outletSupplyItemRows[index].outletSupplyItemId
            //       //       : ''
            //       //   }
            //       //   onChangeItem={(item) => {
            //       //     // setStockLinkItems(
            //       //     //   stockLinkItems.map((stockLinkItem, i) =>
            //       //     //     i === index
            //       //     //       ? {
            //       //     //         ...stockLinkItem,
            //       //     //         outletSupplyItemId: item.value,
            //       //     //         sku: outletSupplyItemsDict[item.value].sku,
            //       //     //         skuMerchant:
            //       //     //           outletSupplyItemsDict[item.value].skuMerchant,
            //       //     //         name: outletSupplyItemsDict[item.value].name,
            //       //     //         unit: outletSupplyItemsDict[item.value].unit,
            //       //     //         quantityUsage: stockLinkItem.quantityUsage,
            //       //     //       }
            //       //     //       : stockLinkItem,
            //       //     //   ),
            //       //     // );
            //       //   }}
            //       //   dropDownMaxHeight={150}
            //       //   dropDownStyle={{
            //       //     width: switchMerchant ? 90 : 150,
            //       //     height: 150,
            //       //     backgroundColor: Colors.fieldtBgColor,
            //       //     borderRadius: 10,
            //       //     borderWidth: 1,
            //       //     textAlign: 'left',
            //       //     zIndex: 2,
            //       //   }}
            //       // />
            //     )
            //       :
            //       <></>
            //   }
            // </>
            :
            <>
              {
                data.isDeleteButton
                  ?
                  <View style={{
                    // backgroundColor: 'red',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}>
                    <TouchableOpacity
                      onPress={() => {
                        Alert.alert(
                          'Info',
                          'Are you sure you want to remove this item?',
                          [
                            {
                              text: 'YES',
                              onPress: () => {
                                // if (!justDeletedOutletSupplyItemId) {
                                //   setJustDeletedOutletSupplyItemId(data.outletSupplyItemId);

                                //   removeOutletSupplyItemToStockArrays(data);
                                // }

                                setJustDeletedOutletSupplyItemId(data.outletSupplyItemId);

                                removeOutletSupplyItemToStockArrays(data);

                                // setStockLinkItems([
                                //   ...stockLinkItems.slice(0, index),
                                //   ...stockLinkItems.slice(index + 1),
                                // ]);
                              }
                            },
                            { text: 'NO', onPress: () => { } },
                          ],
                          { cancelable: true },
                        );
                      }}>
                      <Icon
                        name="trash-2"
                        size={switchMerchant ? 17 : 20}
                        color="#eb3446"
                      />
                    </TouchableOpacity>
                  </View>
                  :
                  <View style={{
                    // backgroundColor: 'red',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}>
                    <TextInput
                      underlineColorAndroid={Colors.fieldtBgColor}
                      style={[
                        styles.textFieldInput,
                        {
                          fontSize: switchMerchant ? 10 : 14,
                          fontFamily: 'NunitoSans-Regular',
                          height: switchMerchant ? 35 : 40,
                          // width: switchMerchant
                          //   ? windowWidth * 0.1
                          //   : windowWidth * 0.12,
                          paddingLeft: 0,
                          width: '80%',
                        },
                      ]}
                      textAlign='center'
                      placeholder={'0'}
                      placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                      //iOS
                      clearTextOnFocus
                      ////////////////////////////////////////////
                      //Android
                      onFocus={() => {
                        // if (!isUsingVariantStockCount) {
                        //   setTemp(stockCount)
                        //   setStockCount('');
                        // }

                        setTemp(data.quantityUsage);

                        writeQuantityUsageToStockArrays(data, '');
                      }}
                      /////////////////////////////////////////////
                      //When textinput is not selected
                      onEndEditing={() => {
                        if (data.quantityUsage == '') {
                          // setStockCount(temp);

                          writeQuantityUsageToStockArrays(data, temp);
                        }
                      }}
                      onChangeText={(text) => {
                        // setStockCount(parseValidIntegerText(text));

                        writeQuantityUsageToStockArrays(data, parseValidPriceText(text));
                      }}
                      value={data.quantityUsage}
                      keyboardType='decimal-pad'
                    // disabled={!isStockCountActive}
                    // editable={isStockCountActive}
                    />
                  </View>
              }
            </>

        }
      </>)
    );
  };

  // const tableData = [];
  // for (let i = 0; i < 30; i += 1) {
  //   const rowData = [];
  //   for (let j = 0; j < 11; j += 1) {
  //     rowData.push(`${i}${j}`);
  //   }
  //   tableData.push(rowData);
  // }

  // Stock array format converter, ex: stock arrays -> table form
  // useEffect(() => {
  //   CommonStore.update((s) => {
  //     s.selectedProductEdit = null;
  //     s.timestamp = Date.now();
  //   });
  // }, []);

  useEffect(() => {
    var tableHeadTemp = [];
    var tableDataTemp = [];

    ///////////////////////////////////////////////////////

    // try to get each potential row first

    var headerNameList = ['', ''];

    var outletSupplyItemDataList = [];
    var productStockLinkItemList = [];

    headerNameList.push(itemName);

    // var stockLinkItemsTemp = [...stockLinkItems];
    // var variantGroupListTemp = [...variantGroupList];
    // var addOnGroupListTemp = [...addOnGroupList];

    if (stockLinkItems && stockLinkItems.length > 0) {
      for (var i = 0; i < stockLinkItems.length; i++) {
        // var tableRowId = nanoid();

        if (!outletSupplyItemDataList.find(item => item.outletSupplyItemId === stockLinkItems[i].outletSupplyItemId) &&
          justDeletedOutletSupplyItemId !== stockLinkItems[i].outletSupplyItemId
        ) {
          outletSupplyItemDataList.push({
            // outletSupplyItemId: stockLinkItems[i].outletSupplyItemId,
            ...stockLinkItems[i],

            stockIndex: i,
            isProduct: true,

            // tableRowId: tableRowId,
          });
        }

        productStockLinkItemList.push({
          ...stockLinkItems[i],

          stockIndex: i,
          isProduct: true,

          // tableRowId: tableRowId,
        });
      }
    }

    var variantChoiceStockLinkItemList = [];

    var variantChoiceList = [];

    if (variantGroupList && variantGroupList.length > 0) {
      for (var i = 0; i < variantGroupList.length; i++) {
        var variantGroup = variantGroupList[i];

        if (
          variantGroup &&
          variantGroup.choices &&
          variantGroup.choices.length > 0
        ) {
          for (var j = 0; j < variantGroup.choices.length; j++) {
            var variantChoice = variantGroup.choices[j];

            headerNameList.push(variantChoice.choiceName);

            var summaryId = nanoid();
            variantChoiceList.push({
              ...variantChoice,

              name: variantChoice.choiceName,

              currGroupIndex: i,
              currChoiceIndex: j,

              summaryId,
            });

            if (
              variantChoice &&
              // variantChoice.choiceStockLinkItemsActive &&
              variantChoice.choiceStockLinkItems &&
              variantChoice.choiceStockLinkItems.length > 0
            ) {
              for (var k = 0; k < variantChoice.choiceStockLinkItems.length; k++) {
                // var tableRowId = nanoid();

                var quantityUsageStr = '';
                if (variantChoice.choiceStockLinkItems[k].quantityUsage && !isNaN(variantChoice.choiceStockLinkItems[k].quantityUsage)) {
                  if (typeof variantChoice.choiceStockLinkItems[k].quantityUsage === 'number') {
                    quantityUsageStr = variantChoice.choiceStockLinkItems[k].quantityUsage.toFixed(2);
                  }
                  else {
                    quantityUsageStr = variantChoice.choiceStockLinkItems[k].quantityUsage;
                  }
                }

                if (!outletSupplyItemDataList.find(item => item.outletSupplyItemId === variantChoice.choiceStockLinkItems[k].outletSupplyItemId) &&
                  justDeletedOutletSupplyItemId !== variantChoice.choiceStockLinkItems[k].outletSupplyItemId
                ) {
                  outletSupplyItemDataList.push({
                    // outletSupplyItemId: variantChoice.choiceStockLinkItems[k].outletSupplyItemId,
                    ...variantChoice.choiceStockLinkItems[k],

                    quantityUsage: quantityUsageStr,

                    stockIndex: i,
                    isVariant: true,

                    // tableRowId: tableRowId,
                  });
                }

                variantChoiceStockLinkItemList.push({
                  ...variantChoice.choiceStockLinkItems[k],

                  quantityUsage: quantityUsageStr,

                  currGroupIndex: i,
                  currChoiceIndex: j,
                  choiceName: variantChoice.choiceName || '-',

                  stockIndex: k,
                  isVariant: true,

                  summaryId,

                  // tableRowId: tableRowId,
                });
              }
            }
          }
        }
      }
    }

    var addOnChoiceStockLinkItemList = [];

    var addOnChoiceList = [];

    if (addOnGroupList && addOnGroupList.length > 0) {
      for (var i = 0; i < addOnGroupList.length; i++) {
        var addOnGroup = addOnGroupList[i];

        if (addOnGroup && addOnGroup.choices && addOnGroup.choices.length > 0) {
          for (var j = 0; j < addOnGroup.choices.length; j++) {
            var addOnChoice = addOnGroup.choices[j];

            headerNameList.push(addOnChoice.choiceName);

            var summaryId = nanoid();
            addOnChoiceList.push({
              ...addOnChoice,

              name: addOnChoice.choiceName,

              currGroupIndex: i,
              currChoiceIndex: j,

              summaryId,
            });

            if (
              addOnChoice &&
              // addOnChoice.choiceStockLinkItemsActive &&
              addOnChoice.choiceStockLinkItems &&
              addOnChoice.choiceStockLinkItems.length > 0
            ) {
              for (var k = 0; k < addOnChoice.choiceStockLinkItems.length; k++) {
                // var tableRowId = nanoid();

                var quantityUsageStr = '';
                if (!isNaN(addOnChoice.choiceStockLinkItems[k].quantityUsage) && addOnChoice.choiceStockLinkItems[k].quantityUsage) {
                  if (typeof addOnChoice.choiceStockLinkItems[k].quantityUsage === 'number') {
                    quantityUsageStr = addOnChoice.choiceStockLinkItems[k].quantityUsage.toFixed(2);
                  }
                  else {
                    quantityUsageStr = addOnChoice.choiceStockLinkItems[k].quantityUsage;
                  }
                }

                if (!outletSupplyItemDataList.find(item => item.outletSupplyItemId === addOnChoice.choiceStockLinkItems[k].outletSupplyItemId) &&
                  justDeletedOutletSupplyItemId !== addOnChoice.choiceStockLinkItems[k].outletSupplyItemId) {

                  outletSupplyItemDataList.push({
                    // outletSupplyItemId: addOnChoice.choiceStockLinkItems[k].outletSupplyItemId,
                    ...addOnChoice.choiceStockLinkItems[k],

                    quantityUsage: quantityUsageStr,

                    stockIndex: i,
                    isAddOn: true,

                    // tableRowId: tableRowId,
                  });
                }

                addOnChoiceStockLinkItemList.push({
                  ...addOnChoice.choiceStockLinkItems[k],

                  quantityUsage: quantityUsageStr,

                  currGroupIndex: i,
                  currChoiceIndex: j,
                  choiceName: addOnChoice.choiceName || '-',

                  stockIndex: k,
                  isAddOn: true,

                  summaryId,

                  // tableRowId: tableRowId,
                });
              }
            }
          }
        }
      }
    }

    ///////////////////////////////////////////////////////

    // for header row
    for (var i = 0; i < headerNameList.length; i++) {
      tableHeadTemp.push(headerNameList[i]);
    }

    // for each row
    for (var i = 0; i < outletSupplyItemDataList.length; i++) {
      var outletSupplyItemData = outletSupplyItemDataList[i];

      // for each column      

      var tableRow = [
        {
          ...outletSupplyItemData,

          isDeleteButton: true,
        },
        {
          ...outletSupplyItemData,

          isSelection: true,

          isProduct: false,
          isVariant: false,
          isAddOn: false,
        }
      ];

      var productStockLinkItem = productStockLinkItemList.find(stockLinkItem => stockLinkItem.outletSupplyItemId === outletSupplyItemData.outletSupplyItemId);
      if (productStockLinkItem) {
        tableRow.push(productStockLinkItem);
      }
      else {
        // not exist, try create a local one

        if (outletSupplyItemData.outletSupplyItemId !== justDeletedOutletSupplyItemId) {
          // var stockLinkItemsTemp = [
          //   ...stockLinkItemsTemp,
          //   {
          //     outletSupplyItemId: outletSupplyItemData.outletSupplyItemId,
          //     sku: outletSupplyItemData.sku,
          //     skuMerchant: outletSupplyItemData.skuMerchant,
          //     name: outletSupplyItemData.name,
          //     unit: outletSupplyItemData.unit,
          //     quantityUsage: '',
          //   },
          // ];

          var stockLinkItemsNew = [
            ...stockLinkItems,
            ...(!stockLinkItems.find(stock => stock.outletSupplyItemId === outletSupplyItemData.outletSupplyItemId) ? [
              {
                outletSupplyItemId: outletSupplyItemData.outletSupplyItemId,
                sku: outletSupplyItemData.sku,
                skuMerchant: outletSupplyItemData.skuMerchant,
                name: outletSupplyItemData.name,
                unit: outletSupplyItemData.unit,
                quantityUsage: '',
              },
            ] : []),
          ];

          var isEqual = JSON.prune(stockLinkItems) === JSON.prune(stockLinkItemsNew);

          if (!isEqual) {
            setStockLinkItems(stockLinkItemsNew);
          }
        }
      }

      ////////////////////////////////////////////////////////////////

      for (var j = 0; j < variantChoiceList.length; j++) {
        var variantStockLinkItem = variantChoiceStockLinkItemList.find(stockLinkItem =>
        (stockLinkItem.outletSupplyItemId === outletSupplyItemData.outletSupplyItemId
          &&
          stockLinkItem.summaryId === variantChoiceList[j].summaryId
        )
        );

        if (variantStockLinkItem) {
          tableRow.push(variantStockLinkItem);
        }
        else {
          // not exist, try create a local one

          if (outletSupplyItemData.outletSupplyItemId !== justDeletedOutletSupplyItemId) {
            // variantGroupListTemp = variantGroupListTemp.map((variantGroup, index) =>
            //   index === variantChoiceList[j].currGroupIndex
            //     ? {
            //       ...variantGroup,
            //       choices: [
            //         ...variantGroup.choices.map(
            //           (choice, choiceIndex) =>
            //             choiceIndex === variantChoiceList[j].currChoiceIndex
            //               ? {
            //                 ...choice,
            //                 choiceStockLinkItems: [
            //                   ...choice.choiceStockLinkItems,
            //                   {
            //                     outletSupplyItemId: outletSupplyItemData.outletSupplyItemId,
            //                     sku: outletSupplyItemData.sku,
            //                     skuMerchant: outletSupplyItemData.skuMerchant,
            //                     name: outletSupplyItemData.name,
            //                     unit: outletSupplyItemData.unit,
            //                     quantityUsage: '',
            //                   },
            //                 ],
            //               }
            //               : choice,
            //         ),
            //       ],
            //     }
            //     : variantGroup,
            // );

            var variantGroupListNew = variantGroupList.map((variantGroup, index) =>
              index === variantChoiceList[j].currGroupIndex
                ? {
                  ...variantGroup,
                  choices: [
                    ...variantGroup.choices.map(
                      (choice, choiceIndex) =>
                        choiceIndex === variantChoiceList[j].currChoiceIndex
                          ? {
                            ...choice,
                            choiceStockLinkItems: [
                              ...choice.choiceStockLinkItems,
                              ...(!choice.choiceStockLinkItems.find(choiceStock => choiceStock.outletSupplyItemId === outletSupplyItemData.outletSupplyItemId) ? [
                                {
                                  outletSupplyItemId: outletSupplyItemData.outletSupplyItemId,
                                  sku: outletSupplyItemData.sku,
                                  skuMerchant: outletSupplyItemData.skuMerchant,
                                  name: outletSupplyItemData.name,
                                  unit: outletSupplyItemData.unit,
                                  quantityUsage: '',
                                },
                              ] : []),
                            ],
                          }
                          : choice,
                    ),
                  ],
                }
                : variantGroup,
            );

            var isEqual = JSON.prune(variantGroupList) === JSON.prune(variantGroupListNew);

            if (!isEqual) {
              setVariantGroupList(variantGroupListNew);
            }
          }
        }
      }

      ////////////////////////////////////////////////////////////////

      for (var j = 0; j < addOnChoiceList.length; j++) {
        var addOnStockLinkItem = addOnChoiceStockLinkItemList.find(stockLinkItem =>
        (stockLinkItem.outletSupplyItemId === outletSupplyItemData.outletSupplyItemId
          &&
          stockLinkItem.summaryId === addOnChoiceList[j].summaryId
        )
        );

        if (addOnStockLinkItem) {
          tableRow.push(addOnStockLinkItem);
        }
        else {
          // not exist, try create a local one

          if (outletSupplyItemData.outletSupplyItemId !== justDeletedOutletSupplyItemId) {
            // addOnGroupListTemp = addOnGroupListTemp.map((addOnGroup, index) =>
            //   index === addOnChoiceList[j].currGroupIndex
            //     ? {
            //       ...addOnGroup,
            //       choices: [
            //         ...addOnGroup.choices.map(
            //           (choice, choiceIndex) =>
            //             choiceIndex === addOnChoiceList[j].currChoiceIndex
            //               ? {
            //                 ...choice,
            //                 choiceStockLinkItems: [
            //                   ...choice.choiceStockLinkItems,
            //                   {
            //                     outletSupplyItemId: outletSupplyItemData.outletSupplyItemId,
            //                     sku: outletSupplyItemData.sku,
            //                     skuMerchant: outletSupplyItemData.skuMerchant,
            //                     name: outletSupplyItemData.name,
            //                     unit: outletSupplyItemData.unit,
            //                     quantityUsage: '',
            //                   },
            //                 ],
            //               }
            //               : choice,
            //         ),
            //       ],
            //     }
            //     : addOnGroup,
            // );

            var addOnGroupListNew = addOnGroupList.map((addOnGroup, index) =>
              index === addOnChoiceList[j].currGroupIndex
                ? {
                  ...addOnGroup,
                  choices: [
                    ...addOnGroup.choices.map(
                      (choice, choiceIndex) =>
                        choiceIndex === addOnChoiceList[j].currChoiceIndex
                          ? {
                            ...choice,
                            choiceStockLinkItems: [
                              ...choice.choiceStockLinkItems,
                              ...(!choice.choiceStockLinkItems.find(choiceStock => choiceStock.outletSupplyItemId === outletSupplyItemData.outletSupplyItemId) ? [
                                {
                                  outletSupplyItemId: outletSupplyItemData.outletSupplyItemId,
                                  sku: outletSupplyItemData.sku,
                                  skuMerchant: outletSupplyItemData.skuMerchant,
                                  name: outletSupplyItemData.name,
                                  unit: outletSupplyItemData.unit,
                                  quantityUsage: '',
                                },
                              ] : []),
                            ],
                          }
                          : choice,
                    ),
                  ],
                }
                : addOnGroup,
            );

            var isEqual = JSON.prune(addOnGroupList) === JSON.prune(addOnGroupListNew);

            if (!isEqual) {
              setAddOnGroupList(addOnGroupListNew);
            }
          }
        }
      }

      ////////////////////////////////////////////////////////////////

      tableDataTemp.push(tableRow);
    }

    ///////////////////////////////////////////////////////

    var isEqual = JSON.prune(tableHead) === JSON.prune(tableHeadTemp);
    if (!isEqual) { setTableHead(tableHeadTemp); }

    // var isEqual = JSON.prune(tableData) === JSON.prune(tableDataTemp);
    // if (!isEqual) {
    //   setTableData(tableDataTemp);
    // }
    setTableData(tableDataTemp);

    var isEqual = JSON.prune(outletSupplyItemRows) === JSON.prune(outletSupplyItemDataList);
    if (!isEqual) {
      setOutletSupplyItemRows(outletSupplyItemDataList);
    }

    // setStockLinkItems(stockLinkItemsTemp);
    // setVariantGroupList(variantGroupListTemp);
    // setAddOnGroupList(addOnGroupListTemp);
  }, [
    stockLinkItems, // outlet supply item stock arrays for product itself
    variantGroupList, // variantGroupList.choiceStockLinkItems contains the outlet supply item stock arrays, for variant
    addOnGroupList, // addOnGroupList.choiceStockLinkItems contains the outlet supply item stock arrays, for addon

    itemName,

    justDeletedOutletSupplyItemId,
  ]);


  useEffect(() => {
    if (justDeletedOutletSupplyItemId) {
      setTimeout(() => {
        setJustDeletedOutletSupplyItemId('');
      }, 100);
    }
  }, [justDeletedOutletSupplyItemId]);

  const writeQuantityUsageToStockArrays = (data, text) => {
    if (data.isProduct) {
      if (stockLinkItems.find(stockLinkItem => stockLinkItem.outletSupplyItemId === data.outletSupplyItemId)) {
        // existed

        setStockLinkItems(
          stockLinkItems.map((stockLinkItem, i) =>
            stockLinkItem.outletSupplyItemId === data.outletSupplyItemId
              ? {
                ...stockLinkItem,
                quantityUsage: text,
              }
              : stockLinkItem,
          ),
        );
      }
      else {
        // not existed

        // setStockLinkItems(
        //   [
        //     ...stockLinkItems,
        //     {
        //       outletSupplyItemId: data.outletSupplyItemId,
        //       sku: data.sku,
        //       skuMerchant: data.skuMerchant,
        //       name: data.name,
        //       unit: data.unit,
        //       quantityUsage: text,
        //     },
        //   ]
        // );
      }
    }
    else if (data.isVariant) {
      setVariantGroupList(
        variantGroupList.map((variantGroup, index) =>
          index === data.currGroupIndex
            ? {
              ...variantGroup,
              choices: [
                ...variantGroup.choices.map(
                  (choice, choiceIndex) =>
                    choiceIndex === data.currChoiceIndex
                      ? {
                        ...choice,
                        choiceStockLinkItems: [
                          ...choice.choiceStockLinkItems.map(
                            (choiceStock, choiceStockIndex) => {
                              return (
                                // choiceStockIndex === data.stockIndex
                                (choiceStock.outletSupplyItemId === data.outletSupplyItemId ? {
                                  ...choiceStock,
                                  quantityUsage: text,
                                } : choiceStock)
                              );
                            }
                          )
                        ],
                      }
                      : choice,
                ),
              ],
            }
            : variantGroup,
        ),
      );
    }
    else if (data.isAddOn) {
      setAddOnGroupList(
        addOnGroupList.map((addOnGroup, index) =>
          index === data.currGroupIndex
            ? {
              ...addOnGroup,
              choices: [
                ...addOnGroup.choices.map(
                  (choice, choiceIndex) =>
                    choiceIndex === data.currChoiceIndex
                      ? {
                        ...choice,
                        choiceStockLinkItems: [
                          ...choice.choiceStockLinkItems.map(
                            (choiceStock, choiceStockIndex) => {
                              return (
                                // choiceStockIndex === data.stockIndex
                                (choiceStock.outletSupplyItemId === data.outletSupplyItemId ? {
                                  ...choiceStock,
                                  quantityUsage: text,
                                } : choiceStock)
                              );
                            }
                          )
                        ],
                      }
                      : choice,
                ),
              ],
            }
            : addOnGroup,
        ),
      );
    }
  }

  // const replaceOutletSupplyItemToStockArrays = (data, outletSupplyItem) => {
  //   if (stockLinkItems.find(stockLinkItem => stockLinkItem.outletSupplyItemId === data.outletSupplyItemId)) {
  //     // existed

  //     setStockLinkItems(
  //       stockLinkItems.map((stockLinkItem, i) => {
  //         return stockLinkItem.outletSupplyItemId === data.outletSupplyItemId
  //           ? {
  //             ...stockLinkItem,
  //             outletSupplyItemId: outletSupplyItem.outletSupplyItemId,
  //             sku: outletSupplyItem.sku,
  //             skuMerchant: outletSupplyItem.skuMerchant,
  //             name: outletSupplyItem.name,
  //             unit: outletSupplyItem.unit,
  //             quantityUsage: stockLinkItem.quantityUsage,
  //           }
  //           : stockLinkItem;
  //       }),
  //     );
  //   }

  //   setVariantGroupList(
  //     variantGroupList.map((variantGroup, index) =>
  //       index === data.currGroupIndex
  //         ? {
  //           ...variantGroup,
  //           choices: [
  //             ...variantGroup.choices.map(
  //               (choice, choiceIndex) =>
  //                 choiceIndex === data.currChoiceIndex
  //                   ? {
  //                     ...choice,
  //                     choiceStockLinkItems: [
  //                       ...choice.choiceStockLinkItems.map(
  //                         (choiceStock, choiceStockIndex) => {
  //                           return (
  //                             // choiceStockIndex === data.stockIndex
  //                             choiceStock.outletSupplyItemId === data.outletSupplyItemId
  //                               ?
  //                               {
  //                                 ...choiceStock,
  //                                 outletSupplyItemId: outletSupplyItem.outletSupplyItemId,
  //                                 sku: outletSupplyItem.sku,
  //                                 skuMerchant: outletSupplyItem.skuMerchant,
  //                                 name: outletSupplyItem.name,
  //                                 unit: outletSupplyItem.unit,
  //                                 quantityUsage: choiceStock.quantityUsage,
  //                               }
  //                               :
  //                               choiceStock
  //                           );
  //                         }
  //                       )
  //                     ],
  //                   }
  //                   : choice,
  //             ),
  //           ],
  //         }
  //         : variantGroup,
  //     ),
  //   );

  //   setAddOnGroupList(
  //     addOnGroupList.map((addOnGroup, index) =>
  //       index === data.currGroupIndex
  //         ? {
  //           ...addOnGroup,
  //           choices: [
  //             ...addOnGroup.choices.map(
  //               (choice, choiceIndex) =>
  //                 choiceIndex === data.currChoiceIndex
  //                   ? {
  //                     ...choice,
  //                     choiceStockLinkItems: [
  //                       ...choice.choiceStockLinkItems.map(
  //                         (choiceStock, choiceStockIndex) => {
  //                           return (
  //                             // choiceStockIndex === data.stockIndex
  //                             choiceStock.outletSupplyItemId === data.outletSupplyItemId
  //                               ?
  //                               {
  //                                 ...choiceStock,
  //                                 outletSupplyItemId: outletSupplyItem.outletSupplyItemId,
  //                                 sku: outletSupplyItem.sku,
  //                                 skuMerchant: outletSupplyItem.skuMerchant,
  //                                 name: outletSupplyItem.name,
  //                                 unit: outletSupplyItem.unit,
  //                                 quantityUsage: choiceStock.quantityUsage,
  //                               }
  //                               :
  //                               choiceStock
  //                           );
  //                         }
  //                       )
  //                     ],
  //                   }
  //                   : choice,
  //             ),
  //           ],
  //         }
  //         : addOnGroup,
  //     ),
  //   );
  // }

  const removeOutletSupplyItemToStockArrays = (data, outletSupplyItem) => {
    // need to clear outletSupplyItemRows as well

    // setOutletSupplyItemRows(outletSupplyItemRows.filter(supplyItemRow => {
    //   return supplyItemRow.outletSupplyItemId !== data.outletSupplyItemId;
    // }));

    var indexToSlice = 0;

    if (stockLinkItems.find(stockLinkItem => stockLinkItem.outletSupplyItemId === data.outletSupplyItemId)) {
      // existed      

      indexToSlice = stockLinkItems.findIndex(stockLinkItem => stockLinkItem.outletSupplyItemId === data.outletSupplyItemId);

      setStockLinkItems([
        ...stockLinkItems.slice(0, indexToSlice),
        ...stockLinkItems.slice(indexToSlice + 1),
      ]);
    }

    setVariantGroupList(
      variantGroupList.map((variantGroup, index) => {
        return {
          ...variantGroup,
          choices: [
            ...variantGroup.choices.map(
              (choice, choiceIndex) => {
                return {
                  ...choice,
                  choiceStockLinkItems: choice.choiceStockLinkItems.filter(choiceStock => {
                    return choiceStock.outletSupplyItemId !== data.outletSupplyItemId;
                  }),
                };
              },
            ),
          ],
        };
      }),
    );

    setAddOnGroupList(
      addOnGroupList.map((addOnGroup, index) => {
        return {
          ...addOnGroup,
          choices: [
            ...addOnGroup.choices.map(
              (choice, choiceIndex) => {
                return {
                  ...choice,
                  choiceStockLinkItems: [
                    ...choice.choiceStockLinkItems,
                  ].filter(choiceStock => {
                    return choiceStock.outletSupplyItemId !== data.outletSupplyItemId;
                  }),
                };
              }
            ),
          ],
        };
      }),
    );
  }

  ////////////////////////////////////////////////////////////////////

  const isAlphaUser = UserStore.useState((s) => s.isAlphaUser);
  const outletPrinters = OutletStore.useState((s) => s.outletPrinters.concat(s.sunmiPrinters).concat(s.iminPrinters).concat(s.blePrinters));
  const allOutlets = MerchantStore.useState((s) => s.allOutlets);
  const outletSupplyItems = CommonStore.useState((s) => s.outletSupplyItems);
  const outletSupplyItemsDict = CommonStore.useState((s) => s.outletSupplyItemsDict);
  const outletSupplyItemsSkuDict = CommonStore.useState((s) => s.outletSupplyItemsSkuDict);
  const supplyItemsDict = CommonStore.useState((s) => s.supplyItemsDict);

  const allOutletsItemAddOn = CommonStore.useState((s) => s.allOutletsItemAddOn);
  const allOutletsItemAddOnDict = CommonStore.useState((s) => s.allOutletsItemAddOnDict);
  const allOutletsItemAddOnChoiceDict = CommonStore.useState((s) => s.allOutletsItemAddOnChoiceDict);

  const outletsTaxDict = CommonStore.useState((s) => s.outletsTaxDict);
  const outletCategories = OutletStore.useState((s) => s.outletCategories);
  const outletCategoriesDict = OutletStore.useState((s) => s.outletCategoriesDict);

  const merchantId = UserStore.useState((s) => s.merchantId);
  const merchantName = MerchantStore.useState((s) => s.name);

  const userId = UserStore.useState((s) => s.firebaseUid);
  const userName = UserStore.useState((s) => s.name);
  const selectedProductEdit = CommonStore.useState((s) => s.selectedProductEdit);

  const allOutletsItemsSkuDict = OutletStore.useState((s) => s.allOutletsItemsSkuDict);

  const allOutletsItems = OutletStore.useState((s) => s.allOutletsItems);

  const allOutletsCategoriesNameDict = OutletStore.useState((s) => s.allOutletsCategoriesNameDict);

  const crmUserTags = OutletStore.useState((s) => s.crmUserTags);
  const crmUserTagsDict = OutletStore.useState((s) => s.crmUserTagsDict);

  const isLoading = CommonStore.useState((s) => s.isLoading);
  const outletSelectDropdownView = CommonStore.useState((s) => s.outletSelectDropdownView);

  const currOutletId = MerchantStore.useState((s) => s.currOutletId);
  const expandTab = CommonStore.useState((s) => s.expandTab);
  const currPageStack = CommonStore.useState((s) => s.currPageStack);

  //////////////////////////////////////////////////////////////

  // Get UserTag DropDownList Item
  useEffect(() => {
    if (crmUserTags.length > 0) {
      setUserTagDropdownList(
        crmUserTags
          .filter(item => !userTagList.some(userTag => userTag.uniqueId === item.uniqueId))
          .map(item => ({ label: item.name, value: item.uniqueId }))
      );
    }
  }, [crmUserTags, userTagList]);

  // Get Outlet DropDownList Item
  useEffect(() => {
    setOutletDropdownList(
      allOutlets.map(item => ({
        label: item.name,
        value: item.uniqueId,
      }))
    );

    if (!selectedProductEdit && !selectedOutletId && allOutlets.length > 0 && currOutletId) {
      setSelectedOutletId(currOutletId);
      setSelectedOutletList([currOutletId]);
    }
  }, [allOutlets, selectedProductEdit, currOutletId]);

  // Get Outlet Category DropDownList Item
  useEffect(() => {
    setOutletCategoryDropdownList(
      outletCategories.map(({ name, uniqueId }) => ({
        label: name,
        value: uniqueId,
      }))
    );

    if (!selectedOutletCategoryId && outletCategories.length > 0) {
      setSelectedOutletCategoryId(null);
    }
  }, [outletCategories]);

  // Data Mounting (Creating or Editing Product)
  useEffect(() => {
    if (selectedProductEdit && allOutletsItems.length > 0) {
      let foundItemList = allOutletsItems.filter(findItem => findItem.sku === selectedProductEdit.sku);
      const selectedOutletListTemp = foundItemList.map(item => item.outletId);
      setSelectedOutletList(selectedOutletListTemp);

      setItemSKU(selectedProductEdit.skuMerchant);
      setSku(selectedProductEdit.sku);
      setItemName(selectedProductEdit.name);
      setDpName(selectedProductEdit.dpName);
      setItemDescription(selectedProductEdit.description);
      if (!isClearProductImage) { setImage(selectedProductEdit.image) }
      setItemPrice((selectedProductEdit.price || 0).toFixed(2));
      setItemCredit((selectedProductEdit.creditToBuy || 0).toFixed(2));
      setCostPrice((selectedProductEdit.itemCostPrice || 0).toFixed(2));
      setItemPriceType(selectedProductEdit.priceType || PRODUCT_PRICE_TYPE.FIXED);
      setItemQuantity(selectedProductEdit.itemQuantity);
      setStockCount((selectedProductEdit.stockCount || 0).toFixed(0));

      setIsAvailableDayActive(selectedProductEdit.isAvailableDayActive || false);

      if (
        selectedProductEdit &&
        selectedProductEdit.effectiveTypeOptions &&
        selectedProductEdit.effectiveTypeOptions.length > 0
      ) {
        const effectiveTypeOptionsTemp = selectedProductEdit.effectiveTypeOptions.filter(option =>
          EFFECTIVE_DAY_DROPDOWN_LIST1.some(item => item.value === option)
        );

        if (selectedProductEdit.isAvailableDayActive) {
          setSelectedEffectiveTypeOptions(effectiveTypeOptionsTemp);
        }
      }

      setEffectiveStartTime(selectedProductEdit.effectiveStartTime ? moment(selectedProductEdit.effectiveStartTime).toDate() : moment().startOf('day').toDate());
      setEffectiveEndTime(selectedProductEdit.effectiveEndTime ? moment(selectedProductEdit.effectiveEndTime).toDate() : moment().endOf('day').toDate());

      setSelectedOutletCategoryId(
        outletCategories.length > 0 &&
          !outletCategories.some(o => o.uniqueId === selectedProductEdit.categoryId)
          ? null
          : selectedProductEdit.categoryId
      );

      if (selectedProductEdit.stockLinkItems) {
        setStockLinkItems(
          selectedProductEdit.stockLinkItems.map((stockLinkItem) => ({
            outletSupplyItemId: stockLinkItem.outletSupplyItemId,
            sku: stockLinkItem.sku,
            skuMerchant: stockLinkItem.skuMerchant,
            name: stockLinkItem.name,
            unit: stockLinkItem.unit,
            quantityUsage: stockLinkItem.quantityUsage
              ? stockLinkItem.quantityUsage.toFixed(2)
              : '0.00',
          })),
        );
      }

      ///////////////// Variant AddOn Group List /////////////////
      var variantGroupListTemp = [];
      var addOnGroupListTemp = [];

      if (Object.keys(allOutletsItemAddOnDict).length > 0 && Object.keys(allOutletsItemAddOnChoiceDict)) {
        const productAddOns = allOutletsItemAddOnDict[selectedProductEdit.uniqueId];
        if (productAddOns) {
          productAddOns.forEach(currGroup => {
            if (!currGroup.isShared) {
              const groupData = {
                groupId: currGroup.uniqueId,
                choices: [],
                name: currGroup.name,
                orderIndex: sortedVariantsAddOns.length + 1,
                printerAreaList: (currGroup.pal && currGroup.pal.length > 0) ? currGroup.pal : printerAreaDropdownList.map(area => area.value),
                isGroupAutoSelect: currGroup.isGroupAutoSelect ? currGroup.isGroupAutoSelect : false,
              };

              if (currGroup.maxSelect) {
                groupData.minSelect = currGroup.minSelect.toFixed(0);
                groupData.maxSelect = currGroup.maxSelect.toFixed(0);
                groupData.isHideVariant = currGroup.isHideVariant || false;
                variantGroupListTemp.push(groupData);
              } else {
                groupData.isHideAddOn = currGroup.isHideAddOn || false;
                groupData.min = (currGroup.min ? currGroup.min : 0).toFixed(0);
                groupData.max = (currGroup.max ? currGroup.max : 0).toFixed(0);
                addOnGroupListTemp.push(groupData);
              }

              const choices = allOutletsItemAddOnChoiceDict[currGroup.uniqueId];
              if (choices) {
                groupData.choices = choices.map(choice => ({
                  choiceId: choice.uniqueId,
                  choiceName: choice.name,
                  choicePrice: (choice.price || 0).toFixed(2),
                  choiceStockCount: (choice.choiceStockCount || 0).toFixed(0),
                  choiceStockLinkItems: choice.choiceStockLinkItems || [],
                  choiceStockLinkItemsActive: choice.choiceStockLinkItemsActive || false,
                  orderIndex: choice.orderIndex,
                  ...(currGroup.maxSelect ? {} : {
                    choiceMinSelect: choice.minSelect.toFixed(0),
                    choiceMaxSelect: choice.maxSelect.toFixed(0),
                  }),
                  isAutoSelect: choice.isAutoSelect ? choice.isAutoSelect : false,
                }));

                groupData.choices.sort((a, b) => {
                  const aOrderIndex = a.orderIndex !== undefined ? a.orderIndex : Infinity;
                  const bOrderIndex = b.orderIndex !== undefined ? b.orderIndex : Infinity;

                  if (aOrderIndex === Infinity && bOrderIndex === Infinity) {
                    return 0;
                  }

                  return aOrderIndex - bOrderIndex;
                });
              }

              const existingGroup = currGroup.maxSelect
                ? selectedProductEdit.variantGroupList.find(g => g.groupId === groupData.groupId)
                : selectedProductEdit.addOnGroupList.find(g => g.groupId === groupData.groupId);

              if (existingGroup) {
                groupData.orderIndex = existingGroup.orderIndex;
              }
            }
          });
        }
      }

      setVariantGroupList(variantGroupListTemp);
      setVariantGroupIndex(0);
      setAddOnGroupList(addOnGroupListTemp);
      setAddOnGroupIndex(0);

      ////////////////////////////////////////////////////////////

      setSelectedUserTagList(selectedProductEdit.crmUserTagIdList || []);
      setSearchingUserTagText('');

      setPickUpCharges((selectedProductEdit.pickUpCharges ? parseFloat(selectedProductEdit.pickUpCharges).toFixed(2) : '0.00'));
      setDeliveryCharges((selectedProductEdit.deliveryCharges ? parseFloat(selectedProductEdit.deliveryCharges).toFixed(2) : '0.00'));
      setOtherDCharges((selectedProductEdit.otherDCharges ? parseFloat(selectedProductEdit.otherDCharges).toFixed(2) : '0.00'));

      setPickUpChargesActive(selectedProductEdit.pickUpChargesActive || false);
      setDeliveryChargesActive(selectedProductEdit.deliveryChargesActive || false);
      setOtherDChargesActive(selectedProductEdit.otherDChargesActive || false);

      setPickUpChargesType(selectedProductEdit.pickUpChargesType || CHARGES_TYPE.AMOUNT_BASED);
      setDeliveryChargesType(selectedProductEdit.deliveryChargesType || CHARGES_TYPE.AMOUNT_BASED);
      setOtherDChargesType(selectedProductEdit.otherDChargesType || CHARGES_TYPE.AMOUNT_BASED);

      setIsDocket(selectedProductEdit.isDocket || false);
      setPrintDocketQuantity((selectedProductEdit.printDocketQuantity || 1).toFixed(0));
      setSpecialTags(
        Array.isArray(selectedProductEdit.specialTags)
          ?
          selectedProductEdit.specialTags
          :
          []
      );

      setIsReservationMenu(selectedProductEdit.isReservationMenu || false);

      setNoManualDisc(selectedProductEdit.noManualDisc || false);

      setIsClearProductImage(false);

      setIsStockCountActive(selectedProductEdit.isStockCountActive || false);

      setHideInOrderTypes(
        Array.isArray(selectedProductEdit.hideInOrderTypes)
          ? selectedProductEdit.hideInOrderTypes
          : (typeof selectedProductEdit.hideInOrderTypes === 'string' && selectedProductEdit.hideInOrderTypes.includes(','))
            ? selectedProductEdit.hideInOrderTypes.split(',')
            : []
      );
    }
    else {
      // designed to always mounted, thus need clear manually...
      // setSelectedOutletList([]);
      // setSelectedOutletList(allOutlets.map(outlet => outlet.uniqueId));
      if (currOutletId) {
        setSelectedOutletId(currOutletId);
        setSelectedOutletList([currOutletId]);
      }

      setItemSKU('');
      setItemName('');
      setDpName('');
      setItemDescription('');
      setImage('');
      setItemPrice('0.00');
      setItemCredit('0.00');
      setCostPrice('0.00');
      setItemPriceType(PRODUCT_PRICE_TYPE.FIXED);
      setItemQuantity(0);
      setStockCount('0');
      setSelectedEffectiveTypeOptions([EFFECTIVE_DAY_DROPDOWN_LIST1[0].value]);
      setEffectiveStartTime(moment().startOf('day').toDate());
      setEffectiveEndTime(moment().endOf('day').toDate());

      setStockLinkItems(
        outletSupplyItems.length > 0
          ? [{
            outletSupplyItemId: outletSupplyItems[0].uniqueId,
            sku: outletSupplyItems[0].sku,
            skuMerchant: outletSupplyItems[0].skuMerchant,
            name: outletSupplyItems[0].name,
            unit: outletSupplyItems[0].unit,
            quantityUsage: '0',
          }]
          : []
      );

      setIsImageChanged(false);

      setVariantGroupList([]);
      setAddOnGroupList([]);

      setSelectedUserTagList([]);
      setSearchingUserTagText('');

      setPickUpCharges('0.00');
      setDeliveryCharges('0.00');
      setOtherDCharges('0.00');
      setPickUpChargesActive(false);
      setDeliveryChargesActive(false);
      setOtherDChargesActive(false);

      setPickUpChargesType(CHARGES_TYPE.AMOUNT_BASED);
      setDeliveryChargesType(CHARGES_TYPE.AMOUNT_BASED);
      setOtherDChargesType(CHARGES_TYPE.AMOUNT_BASED)

      setIsDocket(false);
      setPrintDocketQuantity((1).toFixed(0));

      setIsReservationMenu(false);

      setNoManualDisc(false);

      setIsClearProductImage(false);

      setIsStockCountActive(false);
      setIsAvailableDayActive(false);

      setHideInOrderTypes([]);
      setSpecialTags([]);
    }
  }, [
    selectedProductEdit,
    allOutletsItemsSkuDict,
    allOutletsItems,
    allOutletsItemAddOnDict,
    allOutletsItemAddOnChoiceDict,
    outletSupplyItemsSkuDict,
    supplyItemsDict,
  ]);

  useEffect(() => {
    var uniquePrinterAreaNameList = [];
    var uniquePrinterAreaList = [];

    for (var i = 0; i < outletPrinters.length; i++) {
      var name = '';

      if (outletPrinters[i].area) {
        name = outletPrinters[i].area;
      }

      if (name && !uniquePrinterAreaNameList.includes(name)) {
        uniquePrinterAreaNameList.push(name);
        uniquePrinterAreaList.push(outletPrinters[i]);
      }
    }

    const printerAreaDropdownListTemp = uniquePrinterAreaList.map((item) => ({
      label: item.area || 'N/A',
      value: item.area || 'N/A',
    }));

    setPrinterAreaDropdownList(printerAreaDropdownListTemp);

    if (selectedProductEdit === null) {
      console.log('========================================');
      console.log('null product');
      console.log(printerAreaDropdownListTemp.length);

      if (printerAreaDropdownListTemp.length > 0) {
        console.log('help select');

        // setSelectedPrinterAreaList([printerAreaDropdownListTemp[0].value]);
        setSelectedPrinterAreaList(printerAreaDropdownListTemp.map(printerArea => printerArea.value));
      } else {
        console.log('make blank');

        setSelectedPrinterAreaList([]);
      }

      setSelectedPrintingTypeList(printingTypeDropdownList.map(printingType => printingType.value));
    } else {
      var selectedPrinterAreaListTemp = [];

      if (
        selectedProductEdit.printerAreaList &&
        selectedProductEdit.printerAreaList.length > 0
      ) {
        for (var i = 0; i < selectedProductEdit.printerAreaList.length; i++) {
          var isPrinterAreaMissing = false;

          const printerArea = selectedProductEdit.printerAreaList[i];

          for (var j = 0; j < printerAreaDropdownListTemp.length; j++) {
            if (printerAreaDropdownListTemp[j].value === printerArea) {
              isPrinterAreaMissing = true;
              break;
            }
          }

          if (!isPrinterAreaMissing) {
            // means the saved printer area deleted or renamed
            // skip the printer area
          } else {
            selectedPrinterAreaListTemp.push(printerArea);
          }
        }
      }

      setSelectedPrinterAreaList([...new Set(selectedPrinterAreaListTemp)]);

      if (selectedProductEdit.printingTypeList &&
        selectedProductEdit.printingTypeList.length >= 0) {
        // means is array

        setSelectedPrintingTypeList(selectedProductEdit.printingTypeList);
      }
      else {
        // means not array and haven't saved with this new field before

        setSelectedPrintingTypeList(printingTypeDropdownList.map(printingType => printingType.value));
      }
    }
  }, [outletPrinters, selectedProductEdit]);

  useEffect(() => {
    if (selectedProductEdit === null && selectedOutletCategoryEdit !== null &&
      selectedOutletCategoryEdit.uniqueId &&
      outletCategoryDropdownList.find(category => category.value === selectedOutletCategoryEdit.uniqueId)) {
      setSelectedOutletCategoryId(selectedOutletCategoryEdit.uniqueId);
    }
    else if (selectedProductEdit === null && outletCategoryDropdownList.length > 0) {
      setSelectedOutletCategoryId(outletCategoryDropdownList[0].value);
    }
  }, [selectedOutletCategoryEdit, selectedProductEdit, outletCategoryDropdownList]);

  ///////////////////////////////////////

  // in case categories haven't loaded when user edit the item

  useEffect(() => {
    if (selectedProductEdit && selectedProductEdit.categoryId &&
      outletCategoryDropdownList.find(category => category.value === selectedProductEdit.categoryId)
    ) {
      setSelectedOutletCategoryId(selectedProductEdit.categoryId);
    }
  }, [outletCategoryDropdownList]);

  ///////////////////////////////////////

  useEffect(() => {
    setOutletSupplyItemDropdownList(
      outletSupplyItems.map((item) => ({
        label: item.name,
        value: item.uniqueId,
      })),
    );

    if (outletSupplyItems.length > 0) {
      setSelectedOutletSupplyItemIdToAdd(outletSupplyItems[0].uniqueId);
    }
    else {
      setSelectedOutletSupplyItemIdToAdd('');
    }

    if (
      outletSupplyItems.length > 0 &&
      stockLinkItems.length === 1 &&
      stockLinkItems[0].outletSupplyItemId === '' &&
      selectedProductEdit === null
    ) {
      setStockLinkItems([
        {
          outletSupplyItemId: outletSupplyItems[0].uniqueId,
          sku: outletSupplyItems[0].sku,
          skuMerchant: outletSupplyItems[0].skuMerchant,
          name: outletSupplyItems[0].name,
          unit: outletSupplyItems[0].unit,
          quantityUsage: '0',
        },
      ]);
    }
  }, [
    outletSupplyItems,
    supplyItemsDict,
    outletSupplyItemsSkuDict,
    selectedProductEdit,
  ]);

  //Greg - Hide Category
  const [hideModal, setHideModal] = useState(false);
  const [selectedChoice, setSelectedChoice] = useState(null);
  const [hideHelp, setHideHelp] = useState(false);

  const [customTimeSelectStart, setCustomTimeSelectStart] = useState(false);
  const [customTimeSelectEnd, setCustomTimeSelectEnd] = useState(false);
  const hideCategoryChoice = [
    {
      label: '2 HOURS',
      value: 'HOURS_2',
    },
    {
      label: '6 HOURS',
      value: 'HOURS_6',
    },
    {
      label: 'END OF THE DAY',
      value: 'END_DAY',
    },
    {
      label: 'CUSTOM TIME',
      value: 'CUSTOM_TIME',
    },
    {
      label: 'FOREVER',
      value: 'FOREVER',
    },
  ];

  const [customStartTime, setCustomStartTime] = useState(moment().startOf('day').toDate());
  const [customEndTime, setCustomEndTime] = useState(moment().endOf('day').toDate());

  const [isHidden, setIsHidden] = useState(false);

  //////////////////////////////////////////////////////////////////

  // 2024-08-09 Shared Variant / AddOn
  const [sharedVariantAddOnList, setSharedVariantAddOnList] = useState([]); // All Shared VariantAddOn List for current Outlet
  const [showSharedModal, setShowSharedGroup] = useState(false);  // Modal Visibility Control State

  const [prevOutletItemSharedAddOnList, setPrevOutletItemSharedAddOnList] = useState([]); // Record old data before changes
  const [outletItemSharedAddOnList, setOutletItemSharedAddOnList] = useState([]); // Selected Shared VariantAddOn Item for Current Outlet Item
  const [tempSelectedSharedAddOnList, setTempSelectedSharedAddOnList] = useState([]); // Temp List

  //////////////////////////////////////////////////////////////////
  const [sortedVariantsAddOns, setSortedVariantsAddOns] = useState([]);

  useEffect(() => {
    var tempAddOnsSort = variantGroupList.concat(addOnGroupList).concat(outletItemSharedAddOnList);

    tempAddOnsSort.forEach((item, index) => {
      item.orderIndex = (item.orderIndex != null ? item.orderIndex : index + 1);
    });

    tempAddOnsSort.sort((a, b) => {
      return (
        ((a.orderIndex !== undefined)
          ? a.orderIndex
          : tempAddOnsSort.length) -
        ((b.orderIndex !== undefined)
          ? b.orderIndex
          : tempAddOnsSort.length)
      );
    });

    setSortedVariantsAddOns(tempAddOnsSort);
  }, [variantGroupList, addOnGroupList, outletItemSharedAddOnList])
  //////////////////////////////////////////////////////////////////
  // 2024-08-23 Printer Area for Each Variant/AddOns

  const [showPrinterAreaModal, setShowPrinterAreaModal] = useState(false);
  const [editingVariantItemIndex, setEditingVariantItemIndex] = useState(null);
  const [sharedVariantAddOnPrinterAreaDict, setSharedVariantAddOnPrinterAreaDict] = useState({});

  const handlePrinterAreaOnClick = (index) => {
    console.log(`handlePrinterAreaOnClick: ${index}`);

    setEditingVariantItemIndex(index);
    setShowPrinterAreaModal(true);
  }

  const handleSelectPrinterArea = (printerArea) => {
    const updatedSortedVariantsAddOns = sortedVariantsAddOns.map((item, index) => {
      if (index === editingVariantItemIndex) {
        if (item.isShared) {
          handleSelectSharedVAPrinterArea(item.uniqueId, printerArea)
          return {
            ...item, // No Changes
          };
        } else {
          return {
            ...item,
            printerAreaList: item.printerAreaList.includes(printerArea)
              ? item.printerAreaList.filter(area => area !== printerArea)
              : [...item.printerAreaList, printerArea]
          };
        }
      }

      return item;
    });

    setSortedVariantsAddOns(updatedSortedVariantsAddOns);
  };

  const handleSelectSharedVAPrinterArea = (sharedVariantUniqueId, printerArea) => {
    setSharedVariantAddOnPrinterAreaDict(prevDict => {
      const updatedDict = { ...prevDict };

      if (updatedDict[sharedVariantUniqueId]) {
        if (updatedDict[sharedVariantUniqueId].includes(printerArea)) {
          updatedDict[sharedVariantUniqueId] = updatedDict[sharedVariantUniqueId].filter(area => area !== printerArea);
        } else {
          updatedDict[sharedVariantUniqueId] = [...updatedDict[sharedVariantUniqueId], printerArea];
        }
      } else {
        updatedDict[sharedVariantUniqueId] = [printerArea];
      }

      return updatedDict;
    });
  };

  const renderPrinterAreaList = ({ item: printerArea }) => {
    let currVariantAddOn = sortedVariantsAddOns[editingVariantItemIndex];
    let currItemSelectedPrinterArea = currVariantAddOn && currVariantAddOn.isShared
      ? (sharedVariantAddOnPrinterAreaDict[currVariantAddOn.uniqueId] ? sharedVariantAddOnPrinterAreaDict[currVariantAddOn.uniqueId] : printerAreaDropdownList.map(area => area.value))
      : (currVariantAddOn && currVariantAddOn.printerAreaList ? currVariantAddOn.printerAreaList : printerAreaDropdownList.map(area => area.value));

    if (currItemSelectedPrinterArea.length <= 0) {
      currItemSelectedPrinterArea = printerAreaDropdownList.map(area => area.value);
    }

    return (
      <View style={{
        width: '100%',
        flexDirection: 'row',
        alignItems: 'center',
        marginVertical: 3,
      }}>

        <TouchableOpacity
          onPress={() => {
            handleSelectPrinterArea(printerArea.value);
          }}
        >
          <View style={{
            height: windowWidth * 0.015,
            width: windowWidth * 0.015,
            marginRight: 10,
            justifyContent: 'center',
            alignItems: 'center',
            backgroundColor: currItemSelectedPrinterArea.includes(printerArea.value) ? Colors.primaryColor : Colors.fieldtTxtColor,
          }}>
            <Ionicons name="checkmark-sharp" size={13} color={'#FFFFFF'} style={{ margin: 2 }} />
          </View>
        </TouchableOpacity>

        <View style={{}}>
          <Text>{printerArea.label}</Text>
        </View>
      </View>
    )
  };

  //////////////////////////////////////////////////////////////////
  // 2024-08-27 Order Index

  const [sharedVariantAddOnOrderIndexDict, setSharedVariantAddOnOrderIndexDict] = useState({});

  useEffect(() => {
    setSharedVariantAddOnOrderIndexDict(
      Object.fromEntries(
        sortedVariantsAddOns
          .filter(item => item.isShared)
          .map(item => [item.uniqueId, item.orderIndex])
      )
    );
  }, [sortedVariantsAddOns])

  //////////////////////////////////////////////////////////////////

  const addSlotV2 = () => {
    if (outletSupplyItems.length > 0) {
      if (outletSupplyItemRows.find(rowData => rowData.outletSupplyItemId === selectedOutletSupplyItemIdToAdd)) {
        // means existed alrady

        Alert.alert('Info', 'This supply item is already linked.');
      }
      else {
        var outletSupplyItem = outletSupplyItems.find(supplyItem => supplyItem.uniqueId === selectedOutletSupplyItemIdToAdd);

        setStockLinkItems([
          ...stockLinkItems,
          {
            outletSupplyItemId: outletSupplyItem.uniqueId,
            sku: outletSupplyItem.sku,
            skuMerchant: outletSupplyItem.skuMerchant,
            name: outletSupplyItem.name,
            unit: outletSupplyItem.unit,
            quantityUsage: '',
          },
        ]);
      }
    } else {
      Alert.alert('Error', 'No inventories linked');
    }
  };

  const handleChoosePhoto = () => {
    const imagePickerOptions = {
      mediaType: 'photo',
      quality: 0.5,
      includeBase64: false,
      maxWidth: 512,
      maxHeight: 512,
    };

    launchImageLibrary(imagePickerOptions, (response) => {
      if (response.didCancel) {
      } else if (response.error) {
        Alert.alert(response.error.toString());
      } else {
        const responseParsed = parseImagePickerResponse(response);

        if (responseParsed.width > 512 || responseParsed.height > 512) {
          Alert.alert(
            "Image's width and height must be same or less than 512x512.",
          );
          return;
        }

        setImage(responseParsed.uri);
        setImageType(responseParsed.uri.slice(responseParsed.uri.lastIndexOf('.')));

        setIsImageChanged(true);
      }
    });
  };

  const convertTemplateToExcelFormat = () => {
    var excelTemplate = [];

    for (var i = 0; i < stockLinkItems.length; i++) {
      if (i <= 5) {
        var excelColumn = {
          SKU: stockLinkItems[i].sku,
          Product: stockLinkItems[i].outletSupplyItemId,
          Quantity: stockLinkItems[i].quantityUsage,
          Unit: stockLinkItems[i].unit,
          'Cost Price':
            outletSupplyItemsDict[stockLinkItems[i].outletSupplyItemId].price,
          'Total stock at inventory':
            outletSupplyItemsDict[stockLinkItems[i].outletSupplyItemId]
              .quantity,
        };
        excelTemplate.push(excelColumn);
      } else {
      }
    }

    return excelTemplate;
  };

  const exportTemplate = () => {
    const excelTemplate = convertTemplateToExcelFormat();

    var excelFile = `${Platform.OS === 'ios'
      ? RNFS.DocumentDirectoryPath
      : RNFS.DownloadDirectoryPath
      }/koodoo-Inventory-Supplier-Product${moment().format(
        'YYYY-MM-DD-HH-mm-ss',
      )}.xlsx`;
    var excelWorkSheet = XLSX.utils.json_to_sheet(excelTemplate);
    var excelWorkBook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(
      excelWorkBook,
      excelWorkSheet,
      'Supplier Product Template',
    );

    const workBookData = XLSX.write(excelWorkBook, {
      type: 'binary',
      bookType: 'xlsx',
    });

    RNFS.writeFile(excelFile, workBookData, 'ascii')
      .then((success) => {
        Alert.alert(
          'Success',
          `Sent to ${excelFile}`,
          [{ text: 'OK', onPress: () => { } }],
          { cancelable: false },
        );
      })
      .catch((err) => {
      });
  };

  const handleCreateOrSaveOutletItemAction = async () => {
    const missingFields = [];

    if (!itemName) {
      missingFields.push("Item Name");
    }

    if (!itemDescription) {
      missingFields.push("Item Description");
    }

    if (!itemPrice) {
      missingFields.push("Item Price");
    }
    if (!selectedOutletList) {
      missingFields.push("Selected Outlet");
    }

    if (!selectedOutletCategoryId) {
      missingFields.push("Selected Outlet Category");
    }

    if (missingFields.length > 0) {
      const errorMessage = `Please fill in the following information:\n\n${missingFields.join("\n")}`;
      Alert.alert('Error', errorMessage, [{ text: 'OK', onPress: () => { } }], { cancelable: false });
      setLoadingModal(false);

      return;
    } else {
      setLoadingModal(true);

      var taxIdList = []; // for each outlet
      var categoryIdList = []; // for each outlet
      var taxName = '';
      var taxRate = 0.05;

      var categoryName = outletCategoriesDict[selectedOutletCategoryId].name;

      for (var i = 0; i < selectedOutletList.length; i++) {
        const outletIdTemp = selectedOutletList[i];

        if (outletsTaxDict[outletIdTemp]) {
          taxIdList.push(outletsTaxDict[outletIdTemp].uniqueId);

          taxName = outletsTaxDict[outletIdTemp].name;
          taxRate = outletsTaxDict[outletIdTemp].rate;
        } else {
          taxIdList.push('');
        }

        if (allOutletsCategoriesNameDict[categoryName]) {
          var isSameCategoryNameExisted = false;
          var categoryIdTemp = '';

          for (
            var j = 0;
            j < allOutletsCategoriesNameDict[categoryName].length;
            j++
          ) {
            if (
              allOutletsCategoriesNameDict[categoryName][j].outletId ===
              outletIdTemp
            ) {
              isSameCategoryNameExisted = true;
              categoryIdTemp =
                allOutletsCategoriesNameDict[categoryName][j].uniqueId;
              break;
            }
          }

          categoryIdList.push(categoryIdTemp);
        } else {
          categoryIdList.push('');
        }
      }

      //////////////////////////
      // Revert Shared Variant AddOn List Key Name
      const revertChoiceProperties = (choices) => {
        return choices.map(choice =>
          Object.fromEntries(
            Object.entries(choice).map(([key, value]) => {
              switch (key) {
                case 'choiceId':
                  return ['uniqueId', value];
                case 'choiceName':
                  return ['name', value];
                case 'choicePrice':
                  return ['price', value];
                case 'choiceMinSelect':
                  return ['minSelect', value];
                case 'choiceMaxSelect':
                  return ['maxSelect', value];
                default:
                  return [key, value];
              }
            })
          )
        );
      };

      let revertOutletItemSharedAddOnList = [];
      let revertPrevOutletItemSharedAddOnList = [];

      outletItemSharedAddOnList.forEach(item => {
        const revertedItem = {
          ...item,
          choices: revertChoiceProperties(item.choices)
        };
        revertOutletItemSharedAddOnList.push(revertedItem);
      });

      prevOutletItemSharedAddOnList.forEach(item => {
        const revertedItem = {
          ...item,
          choices: revertChoiceProperties(item.choices)
        };
        revertPrevOutletItemSharedAddOnList.push(revertedItem);
      });

      //////////////////////////
      if (selectedProductEdit === null) {
        // means new item

        ///////////////////////////////////
        // upload image

        var outletItemImagePath = '';
        var outletItemIdLocal = selectedProductEdit
          ? selectedProductEdit.commonId
          : uuidv4();

        if (image && imageType) {
          // outletItemIdLocal = selectedProductEdit.uniqueId;

          outletItemImagePath = await uploadImageToFirebaseStorage(
            {
              uri: image,
              type: imageType,
            },
            `/merchant/${merchantId}/outletItem/${outletItemIdLocal}/image${imageType}`,
          );
        }

        var variantNewList = [];
        var addonNewList = [];

        sortedVariantsAddOns.forEach(item => {
          if (!item.isShared) {
            if (item.choices[0].choiceMinSelect >= 0) {
              addonNewList = addonNewList.concat(item);
            } else {
              variantNewList = variantNewList.concat(item);
            }
          }
        });

        const getOrdinalSuffix = (n) => ['st', 'nd', 'rd'][((n + 90) % 100 - 10) % 10 - 1] || 'th';

        const checkMissingNames = (list, type) => {
          return list
            .map((item, i) => item.name === '' ? `${i + 1}${getOrdinalSuffix(i + 1)} ${type}` : null)
            .filter(Boolean);
        };

        const missingNamesV = checkMissingNames(variantNewList, 'Variant');
        const missingNamesA = checkMissingNames(addonNewList, 'Add On');

        if (missingNamesV.length || missingNamesA.length) {
          const errorMessage = `Please fill in the following titles: \n\n${missingNamesV.concat(missingNamesA).join('\n')}`;
          Alert.alert('Error', errorMessage, [{ text: 'OK' }], { cancelable: false });
          setLoadingModal(false);
          return;
        }

        let messages = [];

        variantNewList.forEach((group, index) => {
          if (!group.choices || group.choices.length === 0) {
            const name = group.name?.trim() || `N/A`;
            messages.push(`Variant group "${name}" has empty choices.`);
          }
        });

        addonNewList.forEach((group, index) => {
          if (!group.choices || group.choices.length === 0) {
            const name = group.name?.trim() || `N/A`;
            messages.push(`Addon group "${name}" has empty choices.`);
          }
        });

        if (messages.length > 0) {
          const errorMessage = messages.join('\n');
          Alert.alert('Error', errorMessage, [{ text: 'OK' }], { cancelable: false });
          setLoadingModal(false);
          return;
        }

        ///////////////////////////////////

        var body = {
          merchantId,
          name: itemName,
          dpName: itemDpName,

          // categoryId: selectedOutletCategoryId,
          code: '',
          currency: 'MYR',
          description: itemDescription || '',
          image: outletItemImagePath,
          price: +parseFloat(itemPrice).toFixed(2),
          creditToBuy: +parseFloat(itemCredit).toFixed(2),
          itemCostPrice: +parseFloat(costPrice).toFixed(2),
          priceType: itemPriceType,
          itemQuantity: +parseFloat(itemQuantity).toFixed(0),
          // taxId: outletsTaxDict[place].uniqueId, // some outlets might not got tax at first place, need create
          // isActive:
          //   effectiveDays == selectedEffectiveTypeOptions ? true : false,
          isActive: true,
          isComposite: true,
          isDelivery: true,
          isSellable: true,
          isTakeaway: true,
          options: '',
          prepareTime: 600,
          // sku: itemName,
          sku: uuidv4(),

          effectiveTypeOptions: selectedEffectiveTypeOptions,
          effectiveStartTime: moment(effectiveStartTime).valueOf(),
          effectiveEndTime: moment(effectiveEndTime).valueOf(),

          skuMerchant: itemSKU || '',

          //stockLinkItems: stockLinkItems,

          stockLinkItems: stockLinkItems.map((item) => ({
            outletSupplyItemId: item.outletSupplyItemId,
            sku: item.sku,
            skuMerchant: item.skuMerchant,
            name: item.name,
            unit: item.unit,
            quantityUsage: (!isNaN(item.quantityUsage) && item.quantityUsage)
              ? +parseFloat(item.quantityUsage).toFixed(2)
              : 0,
          })),

          outletItemIdLocal,
          commonId: outletItemIdLocal,

          variantGroupList: variantNewList.map((item) => ({

            groupId: item.groupId,
            choices: item.choices.map((param) => ({
              choiceId: param.choiceId,
              choiceName: param.choiceName,
              choicePrice: !isNaN(param.choicePrice)
                ? +parseFloat(param.choicePrice).toFixed(2)
                : 0,
              choiceStockCount: !isNaN(param.choiceStockCount)
                ? +parseInt(param.choiceStockCount)
                : 0,

              choiceStockLinkItems:
                param.choiceStockLinkItems &&
                  param.choiceStockLinkItems.length > 0
                  ? param.choiceStockLinkItems.map((item) => ({
                    outletSupplyItemId: item.outletSupplyItemId,
                    sku: item.sku,
                    skuMerchant: item.skuMerchant,
                    name: item.name,
                    unit: item.unit,
                    quantityUsage: (!isNaN(item.quantityUsage) && item.quantityUsage)
                      ? +parseFloat(item.quantityUsage).toFixed(2)
                      : 0,
                  }))
                  : [],
              choiceStockLinkItemsActive:
                param.choiceStockLinkItemsActive || false,

              orderIndex: param.orderIndex,
              isAutoSelect: param.isAutoSelect || false,
            })),
            name: item.name,
            minSelect: +parseFloat(item.minSelect).toFixed(2) >= 0 ? +parseFloat(item.minSelect).toFixed(2) : 1,
            maxSelect: +parseFloat(item.maxSelect).toFixed(2) > 0 ? +parseFloat(item.maxSelect).toFixed(2) : 1,

            isHideVariant: item.isHideVariant ? item.isHideVariant : false,
            orderIndex: item.orderIndex,
            isGroupAutoSelect: item.isGroupAutoSelect || false,
          })),

          addOnGroupList: addonNewList.map((item) => ({

            groupId: item.groupId,
            choices: item.choices.map((param) => ({
              choiceId: param.choiceId,
              choiceName: param.choiceName,
              choicePrice: !isNaN(param.choicePrice)
                ? +parseFloat(param.choicePrice).toFixed(2)
                : 0,
              choiceMinSelect: !isNaN(param.choiceMinSelect)
                ? +parseFloat(param.choiceMinSelect).toFixed(2)
                : 0,
              choiceMaxSelect: !isNaN(param.choiceMaxSelect)
                ? +parseFloat(param.choiceMaxSelect).toFixed(2)
                : 0,
              choiceStockCount: !isNaN(param.choiceStockCount)
                ? +parseInt(param.choiceStockCount)
                : 0,

              choiceStockLinkItems:
                param.choiceStockLinkItems &&
                  param.choiceStockLinkItems.length > 0
                  ? param.choiceStockLinkItems.map((item) => ({
                    outletSupplyItemId: item.outletSupplyItemId,
                    sku: item.sku,
                    skuMerchant: item.skuMerchant,
                    name: item.name,
                    unit: item.unit,
                    quantityUsage: (!isNaN(item.quantityUsage) && item.quantityUsage)
                      ? +parseFloat(item.quantityUsage).toFixed(2)
                      : 0,
                  }))
                  : [],
              choiceStockLinkItemsActive:
                param.choiceStockLinkItemsActive || false,

              orderIndex: param.orderIndex,
              isAutoSelect: param.isAutoSelect || false,
            })),
            name: item.name,
            min: +parseFloat(item.min).toFixed(2) >= 0 ? +parseFloat(item.min).toFixed(2) : 0,
            max: +parseFloat(item.max).toFixed(2) > 0 ? +parseFloat(item.max).toFixed(2) : 0,

            isHideAddOn: item.isHideAddOn ? item.isHideAddOn : false,
            orderIndex: item.orderIndex,
            isGroupAutoSelect: item.isGroupAutoSelect || false,
          })),

          //////////////
          // multi outlet supports

          categoryIdList,
          categoryName: outletCategoriesDict[selectedOutletCategoryId].name,

          outletIdList: selectedOutletList,

          taxIdList,
          taxName,
          taxRate,

          crmUserTagIdList: selectedUserTagList,
          searchingUserTagText,

          printerAreaList: selectedPrinterAreaList,
          printingTypeList: selectedPrintingTypeList,

          pickUpCharges: +parseFloat(pickUpCharges).toFixed(2),
          deliveryCharges: +parseFloat(deliveryCharges).toFixed(2),
          otherDCharges: +parseFloat(otherDCharges).toFixed(2),
          pickUpChargesActive,
          deliveryChargesActive,
          otherDChargesActive,

          pickUpChargesType,
          deliveryChargesType,
          otherDChargesType,

          stockCount: +parseInt(stockCount).toFixed(0),
          // isStockCountActive: !isUsingVariantStockCount,
          isStockCountActive,
          isAvailableDayActive,

          isDocket,
          printDocketQuantity: +parseInt(printDocketQuantity).toFixed(0),

          isReservationMenu,

          hideInOrderTypes,
          specialTags,

          unitType,

          noManualDisc: noManualDisc,

          uid: userId,

          outletItemSharedAddOnList: revertOutletItemSharedAddOnList,
          sharedVariantAddOnPrinterAreaDict,
          sharedVariantAddOnOrderIndexDict,
        };

        // console.log('create product body', body);

        CommonStore.update((s) => {
          s.isLoading = true;
        });

        setIsSave(true);

        // ApiClient.POST(API.createOutletItem, body, false).then((result) => {
        APILocal.createOutletItem(body).then((result) => {
          // console.log('result', result);
          if (result && result.status === 'success') {
            setVariantGroupList([]);
            setAddOnGroupList([]);

            Alert.alert(
              'Success',
              'Product has been added.\n\nFor printer area updates, please wait up to 60 seconds to allow the changes fully propagate across the printers.',
              [
                {
                  text: 'OK',
                  onPress: () => {
                    setLoadingModal(false);
                    navigation.goBack();
                  },
                },
              ],
              { cancelable: false },
            );
          }

          CommonStore.update((s) => {
            s.isLoading = false;
          });

          setIsSave(false);

          setIsClearProductImage(false);
        });
      } else {
        // means existing item
        // setLoadingModal(false);

        var outletItemImagePath = '';
        var outletItemIdLocal = selectedProductEdit.uniqueId;

        if (image && imageType && isImageChanged) {
          // outletItemIdLocal = selectedProductEdit.uniqueId;

          let imageName = 'image';
          if (selectedProductEdit.image) {
            let resultImg = extractImageNameFromUrl(selectedProductEdit.image);

            if (resultImg) {
              imageName = 'image' + (resultImg.number + 1);
            }
          }

          outletItemImagePath = await uploadImageToFirebaseStorage(
            {
              uri: image,
              type: imageType,
            },
            `/merchant/${merchantId}/outletItem/${outletItemIdLocal}/${imageName}${imageType}`,
          );
        }

        var variantNewList = [];
        var addonNewList = [];

        sortedVariantsAddOns.forEach(item => {
          if (!item.isShared) {
            if (item.choices[0].choiceMinSelect >= 0) {
              addonNewList = addonNewList.concat(item);
            } else {
              variantNewList = variantNewList.concat(item);
            }
          }
        });

        const getOrdinalSuffix = (n) => ['st', 'nd', 'rd'][((n + 90) % 100 - 10) % 10 - 1] || 'th';

        const checkMissingNames = (list, type) => {
          return list
            .map((item, i) => item.name === '' ? `${i + 1}${getOrdinalSuffix(i + 1)} ${type}` : null)
            .filter(Boolean);
        };

        const missingNamesV = checkMissingNames(variantNewList, 'Variant');
        const missingNamesA = checkMissingNames(addonNewList, 'Add On');

        if (missingNamesV.length || missingNamesA.length) {
          const errorMessage = `Please fill in the following titles: \n\n${missingNamesV.concat(missingNamesA).join('\n')}`;
          Alert.alert('Error', errorMessage, [{ text: 'OK' }], { cancelable: false });
          setLoadingModal(false);
          return;
        }

        let messages = [];

        variantNewList.forEach((group, index) => {
          if (!group.choices || group.choices.length === 0) {
            const name = group.name?.trim() || `N/A`;
            messages.push(`Variant group "${name}" has empty choices.`);
          }
        });

        addonNewList.forEach((group, index) => {
          if (!group.choices || group.choices.length === 0) {
            const name = group.name?.trim() || `N/A`;
            messages.push(`Addon group "${name}" has empty choices.`);
          }
        });

        if (messages.length > 0) {
          const errorMessage = messages.join('\n');
          Alert.alert('Error', errorMessage, [{ text: 'OK' }], { cancelable: false });
          setLoadingModal(false);
          return;
        }

        ///////////////////////////////////

        var body = {
          merchantId,
          // outletId: place,
          name: itemName,
          dpName: itemDpName,
          // categoryId: selectedOutletCategoryId,
          code: '',
          currency: 'MYR',
          description: itemDescription || '',
          image: outletItemImagePath,
          price: +parseFloat(itemPrice).toFixed(2),
          creditToBuy: +parseFloat(itemCredit).toFixed(2),
          itemCostPrice: +parseFloat(costPrice).toFixed(2),
          priceType: itemPriceType,
          itemQuantity: +parseFloat(itemQuantity).toFixed(0),
          // taxId: outletsTaxDict[place].uniqueId,
          isActive: true,
          isComposite: true,
          isDelivery: true,
          isSellable: true,
          isTakeaway: true,
          options: '',
          prepareTime: 600,
          sku: uuidv4(),

          effectiveTypeOptions: selectedEffectiveTypeOptions,
          effectiveStartTime: moment(effectiveStartTime).valueOf(),
          effectiveEndTime: moment(effectiveEndTime).valueOf(),

          skuMerchant: itemSKU || '',

          outletItemIdLocal,
          commonId: outletItemIdLocal,

          stockLinkItems: stockLinkItems.map((item) => ({
            outletSupplyItemId: item.outletSupplyItemId,
            sku: item.sku,
            skuMerchant: item.skuMerchant,
            name: item.name,
            unit: item.unit,
            quantityUsage: (!isNaN(item.quantityUsage) && item.quantityUsage)
              ? +parseFloat(item.quantityUsage).toFixed(2)
              : 0,
          })),

          variantGroupList: variantNewList.map((item) => ({

            groupId: item.groupId,
            choices: item.choices.map((param) => ({
              choiceId: param.choiceId,
              choiceName: param.choiceName,
              choicePrice: !isNaN(param.choicePrice)
                ? +parseFloat(param.choicePrice).toFixed(2)
                : 0,
              choiceStockCount: !isNaN(param.choiceStockCount)
                ? +parseInt(param.choiceStockCount)
                : 0,

              choiceStockLinkItems:
                param.choiceStockLinkItems &&
                  param.choiceStockLinkItems.length > 0
                  ? param.choiceStockLinkItems.map((item) => ({
                    outletSupplyItemId: item.outletSupplyItemId,
                    sku: item.sku,
                    skuMerchant: item.skuMerchant,
                    name: item.name,
                    unit: item.unit,
                    quantityUsage: (!isNaN(item.quantityUsage) && item.quantityUsage)
                      ? +parseFloat(item.quantityUsage).toFixed(2)
                      : 0,
                  }))
                  : [],
              choiceStockLinkItemsActive:
                param.choiceStockLinkItemsActive || false,

              orderIndex: param.orderIndex,
              isAutoSelect: param.isAutoSelect || false,
            })),
            name: item.name,
            minSelect: +parseFloat(item.minSelect).toFixed(2) >= 0 ? +parseFloat(item.minSelect).toFixed(2) : 1,
            maxSelect: +parseFloat(item.maxSelect).toFixed(2) > 0 ? +parseFloat(item.maxSelect).toFixed(2) : 1,

            isHideVariant: item.isHideVariant ? item.isHideVariant : false,
            orderIndex: item.orderIndex,
            printerAreaList: (item.printerAreaList && item.printerAreaList.length > 0) ? item.printerAreaList : printerAreaDropdownList.map(area => area.value),
            isGroupAutoSelect: item.isGroupAutoSelect || false,
          })),

          addOnGroupList: addonNewList.map((item) => ({

            groupId: item.groupId,
            choices: item.choices.map((param) => ({
              choiceId: param.choiceId,
              choiceName: param.choiceName,
              choicePrice: !isNaN(param.choicePrice)
                ? +parseFloat(param.choicePrice).toFixed(2)
                : 0,
              choiceMinSelect: !isNaN(param.choiceMinSelect)
                ? +parseFloat(param.choiceMinSelect).toFixed(2)
                : 0,
              choiceMaxSelect: !isNaN(param.choiceMaxSelect)
                ? +parseFloat(param.choiceMaxSelect).toFixed(2)
                : 0,
              choiceStockCount: !isNaN(param.choiceStockCount)
                ? +parseInt(param.choiceStockCount)
                : 0,

              choiceStockLinkItems:
                param.choiceStockLinkItems &&
                  param.choiceStockLinkItems.length > 0
                  ? param.choiceStockLinkItems.map((item) => ({
                    outletSupplyItemId: item.outletSupplyItemId,
                    sku: item.sku,
                    skuMerchant: item.skuMerchant,
                    name: item.name,
                    unit: item.unit,
                    quantityUsage: (!isNaN(item.quantityUsage) && item.quantityUsage)
                      ? +parseFloat(item.quantityUsage).toFixed(2)
                      : 0,
                  }))
                  : [],
              choiceStockLinkItemsActive:
                param.choiceStockLinkItemsActive || false,

              orderIndex: param.orderIndex,
              isAutoSelect: param.isAutoSelect || false,
            })),
            name: item.name,
            min: +parseFloat(item.min).toFixed(2) >= 0 ? +parseFloat(item.min).toFixed(2) : 0,
            max: +parseFloat(item.max).toFixed(2) > 0 ? +parseFloat(item.max).toFixed(2) : 0,

            isHideAddOn: item.isHideAddOn ? item.isHideAddOn : false,
            orderIndex: item.orderIndex,
            printerAreaList: (item.printerAreaList && item.printerAreaList.length > 0) ? item.printerAreaList : printerAreaDropdownList.map(area => area.value),
            isGroupAutoSelect: item.isGroupAutoSelect || false,
          })),

          //////////////
          // multi outlet supports

          categoryIdList,
          categoryName: outletCategoriesDict[selectedOutletCategoryId].name,

          outletIdList: selectedOutletList,

          taxIdList,
          taxName,
          taxRate,

          //////////////
          // for update purpose

          editItemId: selectedProductEdit.uniqueId,
          editItemSku: selectedProductEdit.sku,
          editItemImage: selectedProductEdit.image,

          crmUserTagIdList: selectedUserTagList,
          searchingUserTagText,

          printerAreaList: selectedPrinterAreaList,
          printingTypeList: selectedPrintingTypeList,

          pickUpCharges: +parseFloat(pickUpCharges).toFixed(2),
          deliveryCharges: +parseFloat(deliveryCharges).toFixed(2),
          otherDCharges: +parseFloat(otherDCharges).toFixed(2),
          pickUpChargesActive,
          deliveryChargesActive,
          otherDChargesActive,

          pickUpChargesType,
          deliveryChargesType,
          otherDChargesType,

          stockCount: +parseInt(stockCount).toFixed(0),
          // isStockCountActive: !isUsingVariantStockCount,
          isStockCountActive,
          isAvailableDayActive,

          isDocket,
          printDocketQuantity: +parseInt(printDocketQuantity).toFixed(0),

          isReservationMenu,

          isClearProductImage,

          hideInOrderTypes,

          specialTags,

          unitType,

          noManualDisc: noManualDisc ? noManualDisc : false,

          uid: userId,

          //sortedVariantsIndexIdList,
          prevOutletItemSharedAddOnList: revertPrevOutletItemSharedAddOnList,
          outletItemSharedAddOnList: revertOutletItemSharedAddOnList,
          sharedVariantAddOnPrinterAreaDict,
          sharedVariantAddOnOrderIndexDict,
        };

        CommonStore.update((s) => {
          s.isLoading = true;
        });

        setIsSave(true);

        // ApiClient.POST(API.updateOutletItem, body, false).then((result) => {
        APILocal.updateOutletItem(body).then((result) => {
          if (result && result.status === 'success') {
            Alert.alert(
              'Success',
              'Product has been updated.\n\nFor printer area updates, please wait up to 60 seconds to allow the changes fully propagate across the printers.',
              [
                {
                  text: 'OK',
                  onPress: () => {
                    setLoadingModal(false);
                    navigation.goBack();
                  },
                },
              ],
              { cancelable: false },
            );
          }

          CommonStore.update((s) => {
            s.isLoading = false;
          });

          setIsSave(false);

          setIsClearProductImage(false);
        });
      }
    }
  };

  const handleDeleteOutletItemAction = async () => {
    var outletItemImagePath = '';
    var outletItemIdLocal = selectedProductEdit.uniqueId;

    ///////////////////////////////////

    var body = {
      outletItem: selectedProductEdit,
      outletItemAddOnDict: allOutletsItemAddOnDict[selectedProductEdit.uniqueId],
      allOutletsItemAddOnChoiceDict,
    };

    CommonStore.update((s) => {
      s.isLoading = true;
    });

    setIsDelete(true);

    APILocal.deleteOutletItem({
      body,
      uid: userId
    })
      .then((result) => {
        if (result && result.status === 'success') {
          setTimeout(() => {
            Alert.alert(
              'Success',
              'Product has been removed',
              [
                {
                  text: 'OK',
                  onPress: () => {
                    navigation.goBack();
                  },
                },
              ],
              { cancelable: false },
            );
          }, 500);
        }

        CommonStore.update((s) => {
          s.isLoading = false;
        });

        setIsDelete(false);
      });
  };

  //-----Required-----
  const handleBackButtonAction = () => {
    return Alert.alert('Alert', 'Have you saved your work?', [
      {
        text: 'Yes',
        onPress: () => { props.navigation.goBack() },
      },
      {
        text: 'No',
        onPress: () => { },
      },
    ]);
  };

  // -----Required-----
  const createCRMUserTagOrAddCRMUserTagToProduct = async () => {
    CommonStore.update((s) => {
      s.isLoading = true;
    });

    let body = {
      crmUserTagIdList: selectedUserTagList,
      merchantId,
      outletItemId: selectedProductEdit.uniqueId,
      searchingUserTagText,
    };

    APILocal.createCRMUserTagOrAddCRMUserTagToProduct({
      body,
      uid: userId,
    }).then((result) => {
      if (result && result.status === 'success') {
        Alert.alert(
          'Success',
          'Tag(s) has been saved to the product',
          [{ text: 'OK' }], { cancelable: false },
        );

        if (result.outletItem) {
          CommonStore.update((s) => { s.selectedProductEdit = result.outletItem; });
        }

        CommonStore.update((s) => {
          s.isLoading = false;
        });

        setTagModal(false);
        setSearchingUserTagText('');
      }
    });
  };

  const renderVariantGroupChoices = () => {
    const isGroupAutoSelect = variantGroupList[variantGroupIndex].isGroupAutoSelect || false;
    const productOptions = variantGroupList[variantGroupIndex].choices;

    return (
      <>
        {productOptions.map((option, i) => {
          return (
            <View
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                marginTop: switchMerchant ? 10 : 20,
                alignContent: 'space-between',
                height: 50,
                width: '100%',
                // backgroundColor: 'red',
              }}>
              <TextInput
                underlineColorAndroid={Colors.fieldtBgColor}
                style={{
                  backgroundColor: Colors.fieldtBgColor,
                  width: switchMerchant ? 100 : 120,
                  height: switchMerchant ? 35 : 40,
                  borderRadius: 5,
                  padding: 5,
                  marginVertical: 5,
                  borderWidth: 1,
                  borderColor: '#E5E5E5',
                  paddingLeft: 10,
                  marginRight: '5%',
                  fontSize: switchMerchant ? 10 : 14,
                }}
                placeholder="Option Name"
                placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                //iOS
                clearTextOnFocus
                //////////////////////////////////////////////
                //Android
                onFocus={() => {
                  setTemp(
                    variantGroupList[variantGroupIndex].choices[i].choiceName,
                  );
                  setVariantGroupList(
                    variantGroupList.map((variantGroup, index) =>
                      index === variantGroupIndex
                        ? {
                          ...variantGroup,
                          choices: [
                            ...variantGroup.choices.map(
                              (choice, choiceIndex) =>
                                choiceIndex === i
                                  ? {
                                    ...choice,
                                    choiceName: '',
                                  }
                                  : choice,
                            ),
                          ],
                        }
                        : variantGroup,
                    ),
                  );
                }}
                ///////////////////////////////////////////////
                //When textinput is not selected
                onEndEditing={() => {
                  if (
                    variantGroupList[variantGroupIndex].choices[i].choiceName ==
                    ''
                  ) {
                    setVariantGroupList(
                      variantGroupList.map((variantGroup, index) =>
                        index === variantGroupIndex
                          ? {
                            ...variantGroup,
                            choices: [
                              ...variantGroup.choices.map(
                                (choice, choiceIndex) =>
                                  choiceIndex === i
                                    ? {
                                      ...choice,
                                      choiceName: temp,
                                    }
                                    : choice,
                              ),
                            ],
                          }
                          : variantGroup,
                      ),
                    );
                  }
                }}
                onChangeText={(text) => {
                  // const productOptions = productOptionsState;
                  // const item = productOptions.find(
                  //     (obj) => obj.id === productOptions.id,
                  // );
                  // item.name = text;
                  // setState({ productOptions });

                  setVariantGroupList(
                    variantGroupList.map((variantGroup, index) =>
                      index === variantGroupIndex
                        ? {
                          ...variantGroup,
                          choices: [
                            ...variantGroup.choices.map(
                              (choice, choiceIndex) =>
                                choiceIndex === i
                                  ? {
                                    ...choice,
                                    choiceName: text,
                                  }
                                  : choice,
                            ),
                          ],
                        }
                        : variantGroup,
                    ),
                  );
                }}
                // value={(value) => {
                //     const productOptions = productOptionsState;
                //     const item = productOptions.find(
                //         (obj) => obj.id === productOptions.id,
                //     );
                //     value = item.name;
                // }}
                value={
                  variantGroupList[variantGroupIndex].choices[i].choiceName
                }
              />
              <Text
                style={{
                  fontSize: switchMerchant ? 10 : 20,
                  fontFamily: 'NunitoSans-Bold',
                  marginRight: '2%',
                }}>
                RM
              </Text>
              <TextInput
                underlineColorAndroid={Colors.fieldtBgColor}
                style={{
                  backgroundColor: Colors.fieldtBgColor,
                  width: 60,
                  height: switchMerchant ? 35 : 40,
                  borderRadius: 5,
                  padding: 5,
                  marginVertical: 5,
                  borderWidth: 1,
                  borderColor: '#E5E5E5',
                  paddingLeft: 10,
                  marginRight: '5%',
                  fontSize: switchMerchant ? 10 : 14,
                }}
                placeholder="0.00"
                keyboardType="decimal-pad"
                //iOS
                clearTextOnFocus
                //////////////////////////////////////////////
                //Android
                onFocus={() => {
                  setTemp(
                    variantGroupList[variantGroupIndex].choices[i].choicePrice,
                  );
                  setVariantGroupList(
                    variantGroupList.map((variantGroup, index) =>
                      index === variantGroupIndex
                        ? {
                          ...variantGroup,
                          choices: [
                            ...variantGroup.choices.map(
                              (choice, choiceIndex) =>
                                choiceIndex === i
                                  ? {
                                    ...choice,
                                    choicePrice: '',
                                  }
                                  : choice,
                            ),
                          ],
                        }
                        : variantGroup,
                    ),
                  );
                }}
                ///////////////////////////////////////////////
                //When textinput is not selected
                onEndEditing={() => {
                  if (
                    variantGroupList[variantGroupIndex].choices[i]
                      .choicePrice == ''
                  ) {
                    setVariantGroupList(
                      variantGroupList.map((variantGroup, index) =>
                        index === variantGroupIndex
                          ? {
                            ...variantGroup,
                            choices: [
                              ...variantGroup.choices.map(
                                (choice, choiceIndex) =>
                                  choiceIndex === i
                                    ? {
                                      ...choice,
                                      choicePrice:
                                        temp.length > 0 ? temp : '',
                                    }
                                    : choice,
                              ),
                            ],
                          }
                          : variantGroup,
                      ),
                    );
                  }
                }}
                onChangeText={(text) => {
                  // const productOptions = productOptionsState;
                  // const item = productOptions.find(
                  //     (obj) => obj.id === productOptions.id,
                  // );
                  // item.price = text;
                  // setState({ productOptions });

                  setVariantGroupList(
                    variantGroupList.map((variantGroup, index) =>
                      index === variantGroupIndex
                        ? {
                          ...variantGroup,
                          choices: [
                            ...variantGroup.choices.map(
                              (choice, choiceIndex) =>
                                choiceIndex === i
                                  ? {
                                    ...choice,
                                    choicePrice:
                                      // text.length > 0 ? text : '',
                                      parseValidPriceText(text),
                                  }
                                  : choice,
                            ),
                          ],
                        }
                        : variantGroup,
                    ),
                  );
                }}
                // value={(value) => {
                //     const productOptions = productOptionsState;
                //     const item = productOptions.find(
                //         (obj) => obj.id === productOptions.id,
                //     );
                //     value = item.price;
                // }}
                value={
                  variantGroupList[variantGroupIndex].choices[i].choicePrice
                }
                placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
              />

              <View
                style={{
                  flexDirection: 'column',
                  width: !switchMerchant && windowWidth < 1000 ? 50 : '12%',
                  alignItems: 'center',
                  alignSelf: 'flex-end',
                  marginRight: '5%',
                }}>
                <Text
                  style={{
                    fontSize: switchMerchant ? 10 : 14,
                    fontFamily: 'NunitoSans-Bold',
                    color: Colors.fieldtTxtColor,
                  }}>
                  Stock
                </Text>
                <TextInput
                  underlineColorAndroid={Colors.fieldtBgColor}
                  style={{
                    backgroundColor: Colors.fieldtBgColor,
                    width: switchMerchant ? 30 : 60,
                    height: switchMerchant ? 35 : 40,
                    borderRadius: 5,
                    padding: 5,
                    marginVertical: 1,
                    marginBottom: 5,
                    borderWidth: 1,
                    borderColor: '#E5E5E5',
                    paddingLeft: 10,
                    marginRight: '5%',
                    fontSize: switchMerchant ? 10 : 14,
                  }}
                  placeholder="0"
                  keyboardType="decimal-pad"
                  //iOS
                  clearTextOnFocus
                  //////////////////////////////////////////////
                  //Android
                  onFocus={() => {
                    setTemp(
                      variantGroupList[variantGroupIndex].choices[i]
                        .choiceStockCount,
                    );
                    setVariantGroupList(
                      variantGroupList.map((variantGroup, index) =>
                        index === variantGroupIndex
                          ? {
                            ...variantGroup,
                            choices: [
                              ...variantGroup.choices.map(
                                (choice, choiceIndex) =>
                                  choiceIndex === i
                                    ? {
                                      ...choice,
                                      choiceStockCount: '',
                                    }
                                    : choice,
                              ),
                            ],
                          }
                          : variantGroup,
                      ),
                    );
                  }}
                  ///////////////////////////////////////////////
                  //When textinput is not selected
                  onEndEditing={() => {
                    if (
                      variantGroupList[variantGroupIndex].choices[i]
                        .choiceStockCount == ''
                    ) {
                      setVariantGroupList(
                        variantGroupList.map((variantGroup, index) =>
                          index === variantGroupIndex
                            ? {
                              ...variantGroup,
                              choices: [
                                ...variantGroup.choices.map(
                                  (choice, choiceIndex) =>
                                    choiceIndex === i
                                      ? {
                                        ...choice,
                                        choiceStockCount:
                                          temp.length > 0 ? temp : 0,
                                      }
                                      : choice,
                                ),
                              ],
                            }
                            : variantGroup,
                        ),
                      );
                    }
                  }}
                  onChangeText={(text) => {
                    setVariantGroupList(
                      variantGroupList.map((variantGroup, index) =>
                        index === variantGroupIndex
                          ? {
                            ...variantGroup,
                            choices: [
                              ...variantGroup.choices.map(
                                (choice, choiceIndex) =>
                                  choiceIndex === i
                                    ? {
                                      ...choice,
                                      choiceStockCount:
                                        // text.length > 0
                                        //   ? text
                                        //   : 0,
                                        parseValidIntegerText(text),
                                    }
                                    : choice,
                              ),
                            ],
                          }
                          : variantGroup,
                      ),
                    );
                  }}
                  value={
                    variantGroupList[variantGroupIndex].choices[i]
                      .choiceStockCount
                  }
                  placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                />
              </View>

              {/* <View
                style={{
                  flexDirection: 'column',
                  width: '12%',
                  alignItems: 'center',
                  alignSelf: 'flex-end',
                  marginRight: '5%',
                }}>
                <Text
                  style={{
                    fontSize: switchMerchant ? 10 : 14,
                    fontFamily: 'NunitoSans-Bold',
                    color: Colors.fieldtTxtColor,
                  }}>
                  Link
                </Text>
                <View
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',

                    width: switchMerchant ? 30 : 60,
                    height: switchMerchant ? 35 : 40,
                    marginVertical: 1,
                    marginBottom: 5,
                    marginRight: '5%',
                    width: switchMerchant ? 30 : 60,
                    height: switchMerchant ? 35 : 40,
                  }}>
                  <Switch
                    width={switchMerchant ? 30 : 42}
                    style={{}}
                    value={
                      variantGroupList[variantGroupIndex].choices[i]
                        .choiceStockLinkItemsActive
                    }
                    onSyncPress={(statusTemp) => {
                      if (outletSupplyItems.length > 0) {
                        setVariantGroupList(
                          variantGroupList.map((variantGroup, index) =>
                            index === variantGroupIndex
                              ? {
                                ...variantGroup,
                                choices: [
                                  ...variantGroup.choices.map(
                                    (choice, choiceIndex) =>
                                      choiceIndex === i
                                        ? {
                                          ...choice,
                                          choiceStockLinkItemsActive:
                                            statusTemp,
                                          ...(choice.choiceStockLinkItems
                                            .length === 0 && {
                                            choiceStockLinkItems: [
                                              {
                                                outletSupplyItemId:
                                                  outletSupplyItems[0]
                                                    .uniqueId,
                                                sku: outletSupplyItems[0]
                                                  .sku,
                                                skuMerchant:
                                                  outletSupplyItems[0]
                                                    .skuMerchant,
                                                name: outletSupplyItems[0]
                                                  .name,
                                                unit: outletSupplyItems[0]
                                                  .unit,
                                                quantityUsage: '0',
                                              },
                                            ],
                                          }),
                                        }
                                        : choice,
                                  ),
                                ],
                              }
                              : variantGroup,
                          ),
                        );
                      } else {
                        Alert.alert('Error', 'No inventories linked');
                      }
                    }}
                    circleColorActive={Colors.primaryColor}
                    circleColorInactive={Colors.fieldtTxtColor}
                    backgroundActive="#dddddd"
                  />
                </View>
              </View> */}

              {i === 0 ? (
                <TouchableOpacity
                  onPress={() => {
                    // onadditem();
                    setVariantGroupList(
                      variantGroupList.map((variantGroup, index) =>
                        index === variantGroupIndex
                          ? {
                            ...variantGroup,
                            choices: [
                              ...variantGroup.choices,
                              {
                                choiceId: '',
                                choiceName: '',
                                choicePrice: '0.00',
                                choiceStockCount: '0',

                                choiceStockLinkItems: [],
                                choiceStockLinkItemsActive: false,
                              },
                            ],
                          }
                          : variantGroup,
                      ),
                    );
                  }}
                  style={{
                    backgroundColor: Colors.whiteColor,
                    alignItems: 'center',
                    flexDirection: 'row',
                  }}>
                  <Icon
                    name="plus-circle"
                    size={switchMerchant ? 17 : 20}
                    color={Colors.primaryColor}
                  />
                </TouchableOpacity>
              ) : (
                <TouchableOpacity
                  style={{}}
                  onPress={() => {
                    // setVariantGroupList([
                    //     ...variantGroupList.slice(0, variantGroupIndex),
                    //     ...variantGroupList.slice(variantGroupIndex + 1),
                    // ]
                    // );
                    setVariantGroupList(
                      variantGroupList.map((variantGroup, index) =>
                        index === variantGroupIndex
                          ? {
                            ...variantGroup,
                            choices: [
                              ...variantGroup.choices.slice(0, i),
                              ...variantGroup.choices.slice(i + 1),
                            ],
                          }
                          : variantGroup,
                      ),
                    );
                  }}>
                  <Icon
                    name="minus-circle"
                    size={switchMerchant ? 17 : 20}
                    color="#eb3446"
                  />
                </TouchableOpacity>
              )}

              <View style={{
                marginHorizontal: 10,
                height: '100%',
                width: '15%',
                flexDirection: 'row',
                justifyContent: 'space-between',
                alignItems: 'center',
              }}>
                <TouchableOpacity onPress={() => (moveChoicesToTop(option))}>
                  <Entypo name='align-top' size={25} color={Colors.primaryColor} />
                </TouchableOpacity>

                <TouchableOpacity onPress={() => (moveChoicesBackward(option))}>
                  <Entypo name='chevron-down' size={25} color={Colors.primaryColor} />
                </TouchableOpacity>

                <TouchableOpacity onPress={() => (moveChoicesForward(option))}>
                  <Entypo name='chevron-up' size={25} color={Colors.primaryColor} />
                </TouchableOpacity>
              </View>

              {
                isGroupAutoSelect && (
                  <View style={{
                    width: '12%',
                    height: '100%',
                    flexDirection: 'row',
                    justifyContent: 'center',
                    alignItems: 'center',
                    marginHorizontal: 10,
                  }}>

                    <Text style={{
                      // width: '70%',
                      fontSize: switchMerchant ? 10 : 18,
                      fontFamily: 'NunitoSans-SemiBold',
                      textAlign: 'right',
                      marginRight: 5,
                    }}>
                      AutoSelect
                    </Text>

                    <View style={{ marginLeft: switchMerchant ? 0 : 0 }}>
                      <TouchableOpacity
                        onPress={() => {
                          let maxLimit = 0;
                          let autoSelectCount = 0;

                          variantGroupList[variantGroupIndex].choices.forEach(choice => {
                            if (choice.isAutoSelect && choice !== variantGroupList[variantGroupIndex].choices[i]) {
                              autoSelectCount++;
                            }
                          });
                          if (!variantGroupList[variantGroupIndex].choices[i].isAutoSelect) {
                            autoSelectCount++;
                          }

                          // Get the max limit from the variant group
                          maxLimit = parseInt(variantGroupList[variantGroupIndex].maxSelect, 10) || 0;

                          // Check if we're exceeding the limit
                          if (maxLimit > 0 && autoSelectCount > maxLimit) {
                            Alert.alert(
                              'Auto-Select Limit Exceeded',
                              `You can only auto-select up to ${maxLimit} choices.`,
                              [{ text: 'OK' }]
                            );
                            return;
                          }

                          setVariantGroupList(
                            variantGroupList.map((variantGroup, index) =>
                              index === variantGroupIndex
                                ? {
                                  ...variantGroup,
                                  choices: variantGroup.choices.map(
                                    (choice, choiceIndex) =>
                                      choiceIndex === i
                                        ? {
                                          ...choice,
                                          isAutoSelect: !choice.isAutoSelect,
                                        }
                                        : choice
                                  ),
                                }
                                : variantGroup
                            )
                          );
                        }}
                      >
                        <View style={{
                          height: windowWidth * 0.02,
                          width: windowWidth * 0.02,
                          justifyContent: 'center',
                          alignItems: 'center',
                          backgroundColor: variantGroupList[variantGroupIndex].choices[i]?.isAutoSelect ? Colors.primaryColor : Colors.fieldtTxtColor,
                        }}>
                          <Ionicons name="checkmark-sharp" size={13} color={'#FFFFFF'} style={{ margin: 2 }} />
                        </View>
                      </TouchableOpacity>
                    </View>
                  </View>
                )
              }
            </View>
          );
        })}
      </>
    );
  };

  const renderAddOnGroupChoices = () => {
    const isGroupAutoSelect = addOnGroupList[addOnGroupIndex].isGroupAutoSelect || false;
    const productOptions = addOnGroupList[addOnGroupIndex].choices;

    return (
      <>
        {productOptions.map((option, i) => {
          return (
            <View
              style={{
                flexDirection: 'row',
                alignItems: 'flex-end',
                marginTop: switchMerchant ? 10 : 20,
                alignContent: 'space-between',
                height: 50,
                width: '100%',
              }}>
              <TextInput
                underlineColorAndroid={Colors.fieldtBgColor}
                style={{
                  backgroundColor: Colors.fieldtBgColor,
                  width: switchMerchant ? '20%' : '20%',
                  height: switchMerchant ? 35 : 40,
                  borderRadius: 5,
                  padding: 5,
                  marginVertical: 5,
                  borderWidth: 1,
                  borderColor: '#E5E5E5',
                  paddingLeft: 10,
                  marginRight: switchMerchant ? '2%' : '2%',
                  marginTop: 5,
                  fontSize: switchMerchant ? 10 : 14,
                }}
                placeholder="Option Name"
                placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                clearTextOnFocus

                onFocus={() => {
                  setTemp(
                    addOnGroupList[addOnGroupIndex].choices[i].choiceName,
                  );
                  setAddOnGroupList(
                    addOnGroupList.map((addOnGroup, index) =>
                      index === addOnGroupIndex
                        ? {
                          ...addOnGroup,
                          choices: [
                            ...addOnGroup.choices.map((choice, choiceIndex) =>
                              choiceIndex === i
                                ? {
                                  ...choice,
                                  choiceName: '',
                                }
                                : choice,
                            ),
                          ],
                        }
                        : addOnGroup,
                    ),
                  );
                }}

                onEndEditing={() => {
                  if (
                    addOnGroupList[addOnGroupIndex].choices[i].choiceName == ''
                  ) {
                    setAddOnGroupList(
                      addOnGroupList.map((addOnGroup, index) =>
                        index === addOnGroupIndex
                          ? {
                            ...addOnGroup,
                            choices: [
                              ...addOnGroup.choices.map(
                                (choice, choiceIndex) =>
                                  choiceIndex === i
                                    ? {
                                      ...choice,
                                      choiceName: temp,
                                    }
                                    : choice,
                              ),
                            ],
                          }
                          : addOnGroup,
                      ),
                    );
                  }
                }}

                onChangeText={(text) => {
                  setAddOnGroupList(
                    addOnGroupList.map((addOnGroup, index) =>
                      index === addOnGroupIndex
                        ? {
                          ...addOnGroup,
                          choices: [
                            ...addOnGroup.choices.map((choice, choiceIndex) =>
                              choiceIndex === i
                                ? {
                                  ...choice,
                                  choiceName: text,
                                }
                                : choice,
                            ),
                          ],
                        }
                        : addOnGroup,
                    ),
                  );
                }}
                value={addOnGroupList[addOnGroupIndex].choices[i].choiceName}
              />

              <View
                style={{
                  flexDirection: 'column',
                  width: '8%',
                  alignItems: 'center',
                }}>
                <Text
                  style={{
                    fontSize: switchMerchant ? 10 : 14,
                    fontFamily: 'NunitoSans-Bold',
                    color: Colors.fieldtTxtColor,
                  }}>
                  MIN
                </Text>

                <TextInput
                  underlineColorAndroid={Colors.fieldtBgColor}
                  style={{
                    backgroundColor: Colors.fieldtBgColor,
                    width: switchMerchant ? 30 : 50,
                    height: switchMerchant ? 35 : 40,
                    borderRadius: 5,
                    padding: 5,
                    marginVertical: 5,
                    borderWidth: 1,
                    borderColor: '#E5E5E5',
                    paddingLeft: 10,
                    fontSize: switchMerchant ? 10 : 14,
                  }}
                  placeholder="MIN"
                  keyboardType="decimal-pad"
                  clearTextOnFocus

                  onFocus={() => {
                    setTemp(addOnGroupList[addOnGroupIndex].choices[i].choiceMinSelect);

                    setAddOnGroupList(
                      addOnGroupList.map((addOnGroup, index) =>
                        index === addOnGroupIndex
                          ? {
                            ...addOnGroup,
                            choices: [
                              ...addOnGroup.choices.map((choice, choiceIndex) =>
                                choiceIndex === i
                                  ? {
                                    ...choice,
                                    choiceMinSelect: '',
                                  }
                                  : choice,
                              ),
                            ],
                          }
                          : addOnGroup,
                      ),
                    );
                  }}

                  onEndEditing={() => {
                    if (
                      addOnGroupList[addOnGroupIndex].choices[i]
                        .choiceMinSelect == ''
                    ) {
                      setAddOnGroupList(
                        addOnGroupList.map((addOnGroup, index) =>
                          index === addOnGroupIndex
                            ? {
                              ...addOnGroup,
                              choices: [
                                ...addOnGroup.choices.map(
                                  (choice, choiceIndex) =>
                                    choiceIndex === i
                                      ? {
                                        ...choice,
                                        choiceMinSelect:
                                          temp.length > 0 ? temp : '',
                                      }
                                      : choice,
                                ),
                              ],
                            }
                            : addOnGroup,
                        ),
                      );
                    }
                  }}

                  onChangeText={(text) => {
                    setAddOnGroupList(
                      addOnGroupList.map((addOnGroup, index) =>
                        index === addOnGroupIndex
                          ? {
                            ...addOnGroup,
                            choices: [
                              ...addOnGroup.choices.map(
                                (choice, choiceIndex) =>
                                  choiceIndex === i
                                    ? {
                                      ...choice,
                                      choiceMinSelect: parseValidIntegerText(text),
                                    }
                                    : choice,
                              ),
                            ],
                          }
                          : addOnGroup,
                      ),
                    );
                  }}
                  value={addOnGroupList[addOnGroupIndex].choices[i].choiceMinSelect}
                  placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                />
              </View>

              <Text
                style={{
                  fontSize: switchMerchant ? 10 : 20,
                  fontFamily: 'NunitoSans-Bold',
                  marginRight: switchMerchant ? '2%' : '1%',
                  marginLeft: switchMerchant ? '2%' : '1%',
                  marginBottom: switchMerchant ? '5%' : '3%',
                }}>
                -
              </Text>

              <View
                style={{
                  flexDirection: 'column',
                  width: '8%',
                  alignItems: 'center',
                }}>
                <Text
                  style={{
                    fontSize: switchMerchant ? 10 : 14,
                    fontFamily: 'NunitoSans-Bold',
                    color: Colors.fieldtTxtColor,
                  }}>
                  MAX
                </Text>
                <TextInput
                  underlineColorAndroid={Colors.fieldtBgColor}
                  style={{
                    backgroundColor: Colors.fieldtBgColor,
                    width: switchMerchant ? 30 : 50,
                    height: switchMerchant ? 35 : 40,
                    borderRadius: 5,
                    padding: 5,
                    marginVertical: 5,
                    borderWidth: 1,
                    borderColor: '#E5E5E5',
                    paddingLeft: 10,
                    marginRight: '5%',
                    fontSize: switchMerchant ? 10 : 14,
                  }}
                  placeholder="MAX"
                  keyboardType="decimal-pad"
                  clearTextOnFocus

                  onFocus={() => {
                    setTemp(
                      addOnGroupList[addOnGroupIndex].choices[i]
                        .choiceMaxSelect,
                    );
                    setAddOnGroupList(
                      addOnGroupList.map((addOnGroup, index) =>
                        index === addOnGroupIndex
                          ? {
                            ...addOnGroup,
                            choices: [
                              ...addOnGroup.choices.map(
                                (choice, choiceIndex) =>
                                  choiceIndex === i
                                    ? {
                                      ...choice,
                                      choiceMaxSelect: '',
                                    }
                                    : choice,
                              ),
                            ],
                          }
                          : addOnGroup,
                      ),
                    );
                  }}

                  onEndEditing={() => {
                    if (
                      addOnGroupList[addOnGroupIndex].choices[i]
                        .choiceMaxSelect == ''
                    ) {
                      setAddOnGroupList(
                        addOnGroupList.map((addOnGroup, index) =>
                          index === addOnGroupIndex
                            ? {
                              ...addOnGroup,
                              choices: [
                                ...addOnGroup.choices.map(
                                  (choice, choiceIndex) =>
                                    choiceIndex === i
                                      ? {
                                        ...choice,
                                        choiceMaxSelect:
                                          temp.length > 0 ? temp : '',
                                      }
                                      : choice,
                                ),
                              ],
                            }
                            : addOnGroup,
                        ),
                      );
                    }
                  }}

                  onChangeText={(text) => {
                    setAddOnGroupList(
                      addOnGroupList.map((addOnGroup, index) =>
                        index === addOnGroupIndex
                          ? {
                            ...addOnGroup,
                            choices: [
                              ...addOnGroup.choices.map(
                                (choice, choiceIndex) =>
                                  choiceIndex === i
                                    ? {
                                      ...choice,
                                      choiceMaxSelect:
                                        parseValidIntegerText(text),
                                    }
                                    : choice,
                              ),
                            ],
                          }
                          : addOnGroup,
                      ),
                    );
                  }}

                  value={addOnGroupList[addOnGroupIndex].choices[i].choiceMaxSelect}
                  placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                />
              </View>

              <Text
                style={{
                  marginBottom: switchMerchant ? '5%' : '3%',
                  fontSize: switchMerchant ? 10 : 16,
                  fontFamily: 'NunitoSans-Bold',
                  width: switchMerchant ? '5%' : '4%',
                  marginRight: switchMerchant ? '1%' : '1%',
                  marginLeft: switchMerchant ? '3%' : '1%',
                }}>
                RM
              </Text>

              <TextInput
                underlineColorAndroid={Colors.fieldtBgColor}
                style={{
                  backgroundColor: Colors.fieldtBgColor,
                  width: switchMerchant ? 50 : 55,
                  height: switchMerchant ? 35 : 40,
                  borderRadius: 5,
                  padding: 5,
                  marginVertical: switchMerchant ? 5 : 5,
                  borderWidth: 1,
                  borderColor: '#E5E5E5',
                  paddingLeft: 10,
                  marginRight: '1%',
                  fontSize: switchMerchant ? 10 : 14,
                }}
                placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                placeholder="0.00"
                keyboardType="decimal-pad"
                //iOS
                clearTextOnFocus
                //////////////////////////////////////////////
                //Android
                onFocus={() => {
                  setTemp(
                    addOnGroupList[addOnGroupIndex].choices[i].choicePrice,
                  );
                  setAddOnGroupList(
                    addOnGroupList.map((addOnGroup, index) =>
                      index === addOnGroupIndex
                        ? {
                          ...addOnGroup,
                          choices: [
                            ...addOnGroup.choices.map((choice, choiceIndex) =>
                              choiceIndex === i
                                ? {
                                  ...choice,
                                  choicePrice: '',
                                }
                                : choice,
                            ),
                          ],
                        }
                        : addOnGroup,
                    ),
                  );
                }}
                ///////////////////////////////////////////////
                //When textinput is not selected
                onEndEditing={() => {
                  if (
                    addOnGroupList[addOnGroupIndex].choices[i].choicePrice == ''
                  ) {
                    setAddOnGroupList(
                      addOnGroupList.map((addOnGroup, index) =>
                        index === addOnGroupIndex
                          ? {
                            ...addOnGroup,
                            choices: [
                              ...addOnGroup.choices.map(
                                (choice, choiceIndex) =>
                                  choiceIndex === i
                                    ? {
                                      ...choice,
                                      choicePrice:
                                        temp.length > 0 ? temp : '0.00',
                                    }
                                    : choice,
                              ),
                            ],
                          }
                          : addOnGroup,
                      ),
                    );
                  }
                }}
                //////////////////////////////////////////////
                // onSubmitEditing={(text) => {

                // }}
                onChangeText={(text) => {
                  // const productOptions = productOptionsState;
                  // const item = productOptions.find(
                  //     (obj) => obj.id === productOptions.id,
                  // );
                  // item.price = text;
                  // setState({ productOptions });

                  setAddOnGroupList(
                    addOnGroupList.map((addOnGroup, index) =>
                      index === addOnGroupIndex
                        ? {
                          ...addOnGroup,
                          choices: [
                            ...addOnGroup.choices.map((choice, choiceIndex) =>
                              choiceIndex === i
                                ? {
                                  ...choice,
                                  choicePrice:
                                    // text.length > 0 ? text : '0.00',
                                    parseValidPriceText(text),
                                }
                                : choice,
                            ),
                          ],
                        }
                        : addOnGroup,
                    ),
                  );
                }}
                // value={(value) => {
                //     const productOptions = productOptionsState;
                //     const item = productOptions.find(
                //         (obj) => obj.id === productOptions.id,
                //     );
                //     value = item.price;
                // }}
                // value={(
                //   (addOnGroupList[addOnGroupIndex].choices[i] && addOnGroupList[addOnGroupIndex].choices[i].choicePrice)
                //   ?
                //   addOnGroupList[addOnGroupIndex].choices[i].choicePrice
                //   :
                //   '0.00'
                // )}
                value={addOnGroupList[addOnGroupIndex].choices[i].choicePrice}
              />

              <View
                style={{
                  flexDirection: 'column',
                  width: '8%',
                  alignItems: 'center',
                  marginRight: '1%',
                }}>
                <Text
                  style={{
                    fontSize: switchMerchant ? 10 : 14,
                    fontFamily: 'NunitoSans-Bold',
                    color: Colors.fieldtTxtColor,
                  }}>
                  Stock
                </Text>
                <TextInput
                  underlineColorAndroid={Colors.fieldtBgColor}
                  style={{
                    backgroundColor: Colors.fieldtBgColor,
                    width: switchMerchant ? 30 : 50,
                    height: switchMerchant ? 35 : 40,
                    borderRadius: 5,
                    padding: 5,
                    marginVertical: 5,
                    borderWidth: 1,
                    borderColor: '#E5E5E5',
                    paddingLeft: 10,
                    marginRight: '2%',
                    fontSize: switchMerchant ? 10 : 14,
                  }}
                  placeholder="0"
                  keyboardType="decimal-pad"
                  // onChangeText={(text) => {
                  //   setAddOnGroupList(
                  //     addOnGroupList.map((addOnGroup, index) =>
                  //       index === addOnGroupIndex
                  //         ? {
                  //           ...addOnGroup,
                  //           choices: [
                  //             ...addOnGroup.choices.map(
                  //               (choice, choiceIndex) =>
                  //                 choiceIndex === i
                  //                   ? {
                  //                     ...choice,
                  //                     choiceStockCount:
                  //                       text.length > 0
                  //                         ? parseInt(text)
                  //                         : 0,
                  //                   }
                  //                   : choice,
                  //             ),
                  //           ],
                  //         }
                  //         : addOnGroup,
                  //     ),
                  //   );
                  // }}
                  // value={(addOnGroupList[addOnGroupIndex].choices[i]
                  //   .choiceStockCount > 0
                  //   ? addOnGroupList[addOnGroupIndex].choices[i]
                  //     .choiceStockCount
                  //   : '0'
                  // ).toString()}
                  //iOS
                  clearTextOnFocus
                  //////////////////////////////////////////////
                  //Android
                  onFocus={() => {
                    setTemp(
                      addOnGroupList[addOnGroupIndex].choices[i]
                        .choiceStockCount,
                    );
                    setAddOnGroupList(
                      addOnGroupList.map((addOnGroup, index) =>
                        index === addOnGroupIndex
                          ? {
                            ...addOnGroup,
                            choices: [
                              ...addOnGroup.choices.map(
                                (choice, choiceIndex) =>
                                  choiceIndex === i
                                    ? {
                                      ...choice,
                                      choiceStockCount: '',
                                    }
                                    : choice,
                              ),
                            ],
                          }
                          : addOnGroup,
                      ),
                    );
                  }}
                  ///////////////////////////////////////////////
                  //When textinput is not selected
                  onEndEditing={() => {
                    if (
                      addOnGroupList[addOnGroupIndex].choices[i]
                        .choiceStockCount == ''
                    ) {
                      setAddOnGroupList(
                        addOnGroupList.map((addOnGroup, index) =>
                          index === addOnGroupIndex
                            ? {
                              ...addOnGroup,
                              choices: [
                                ...addOnGroup.choices.map(
                                  (choice, choiceIndex) =>
                                    choiceIndex === i
                                      ? {
                                        ...choice,
                                        choiceStockCount:
                                          temp.length > 0 ? temp : 0,
                                      }
                                      : choice,
                                ),
                              ],
                            }
                            : addOnGroup,
                        ),
                      );
                    }
                  }}
                  onChangeText={(text) => {
                    setAddOnGroupList(
                      addOnGroupList.map((addOnGroup, index) =>
                        index === addOnGroupIndex
                          ? {
                            ...addOnGroup,
                            choices: [
                              ...addOnGroup.choices.map(
                                (choice, choiceIndex) =>
                                  choiceIndex === i
                                    ? {
                                      ...choice,
                                      choiceStockCount:
                                        // text.length > 0
                                        //   ? text
                                        //   : 0,
                                        parseValidIntegerText(text),
                                    }
                                    : choice,
                              ),
                            ],
                          }
                          : addOnGroup,
                      ),
                    );
                  }}
                  value={
                    addOnGroupList[addOnGroupIndex].choices[i].choiceStockCount
                  }
                  placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                />
              </View>

              {/* <View
                style={{
                  flexDirection: 'column',
                  width: '12%',
                  alignItems: 'center',
                  // alignSelf: 'flex-end',
                  marginRight: '2%',
                }}>
                <Text
                  style={{
                    fontSize: switchMerchant ? 10 : 14,
                    fontFamily: 'NunitoSans-Bold',
                    color: Colors.fieldtTxtColor,
                  }}>
                  Link
                </Text>
                <View
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',

                    width: switchMerchant ? 30 : 60,
                    height: switchMerchant ? 35 : 40,
                    marginVertical: 5,
                    marginBottom: 5,
                    marginRight: '5%',
                    width: switchMerchant ? 30 : 60,
                    height: switchMerchant ? 35 : 40,
                  }}>
                  <Switch
                    width={switchMerchant ? 30 : 42}
                    style={{}}
                    value={
                      addOnGroupList[addOnGroupIndex].choices[i]
                        .choiceStockLinkItemsActive
                    }
                    onSyncPress={(statusTemp) => {
                      if (outletSupplyItems.length > 0) {
                        setAddOnGroupList(
                          addOnGroupList.map((addOnGroup, index) =>
                            index === addOnGroupIndex
                              ? {
                                ...addOnGroup,
                                choices: [
                                  ...addOnGroup.choices.map(
                                    (choice, choiceIndex) =>
                                      choiceIndex === i
                                        ? {
                                          ...choice,
                                          choiceStockLinkItemsActive:
                                            statusTemp,
                                          ...(choice.choiceStockLinkItems
                                            .length === 0 && {
                                            choiceStockLinkItems: [
                                              {
                                                outletSupplyItemId:
                                                  outletSupplyItems[0]
                                                    .uniqueId,
                                                sku: outletSupplyItems[0]
                                                  .sku,
                                                skuMerchant:
                                                  outletSupplyItems[0]
                                                    .skuMerchant,
                                                name: outletSupplyItems[0]
                                                  .name,
                                                unit: outletSupplyItems[0]
                                                  .unit,
                                                quantityUsage: '0',
                                              },
                                            ],
                                          }),
                                        }
                                        : choice,
                                  ),
                                ],
                              }
                              : addOnGroup,
                          ),
                        );
                      } else {
                        Alert.alert('Error', 'No inventories linked');
                      }
                    }}
                    circleColorActive={Colors.primaryColor}
                    circleColorInactive={Colors.fieldtTxtColor}
                    backgroundActive="#dddddd"
                  />
                </View>
              </View> */}

              {i === 0 ? (
                <TouchableOpacity
                  onPress={() => {
                    // onadditem1();
                    setAddOnGroupList(
                      addOnGroupList.map((addOnGroup, index) =>
                        index === addOnGroupIndex
                          ? {
                            ...addOnGroup,
                            choices: [
                              ...addOnGroup.choices,
                              {
                                choiceId: '',
                                choiceName: '',
                                choicePrice: '0.00',
                                choiceMinSelect: '0',
                                choiceMaxSelect: '0',
                                choiceStockCount: '0',

                                choiceStockLinkItems: [],
                                choiceStockLinkItemsActive: false,
                              },
                            ],
                          }
                          : addOnGroup,
                      ),
                    );
                  }}
                  style={{
                    marginBottom: switchMerchant ? '4.5%' : '3%',
                    backgroundColor: Colors.whiteColor,
                    alignItems: 'center',
                    flexDirection: 'row',
                  }}>
                  <Icon
                    name="plus-circle"
                    size={switchMerchant ? 17 : 20}
                    color={Colors.primaryColor}
                  />
                </TouchableOpacity>
              ) : (
                <TouchableOpacity
                  style={{ marginBottom: switchMerchant ? '4.5%' : '3%' }}
                  onPress={() => {
                    setAddOnGroupList(
                      addOnGroupList.map((addOnGroup, index) =>
                        index === addOnGroupIndex
                          ? {
                            ...addOnGroup,
                            choices: [
                              ...addOnGroup.choices.slice(0, i),
                              ...addOnGroup.choices.slice(i + 1),
                            ],
                          }
                          : addOnGroup,
                      ),
                    );
                  }}>
                  <Icon
                    name="minus-circle"
                    size={switchMerchant ? 17 : 20}
                    color="#eb3446"
                  />
                </TouchableOpacity>
              )}

              <View style={{
                marginHorizontal: 10,
                height: '100%',
                width: '12%',
                flexDirection: 'row',
                justifyContent: 'space-between',
                alignItems: 'center',
              }}>
                <TouchableOpacity onPress={() => (moveChoicesToTop(option))}>
                  <Entypo name='align-top' size={25} color={Colors.primaryColor} />
                </TouchableOpacity>

                <TouchableOpacity onPress={() => (moveChoicesBackward(option))}>
                  <Entypo name='chevron-down' size={25} color={Colors.primaryColor} />
                </TouchableOpacity>

                <TouchableOpacity onPress={() => (moveChoicesForward(option))}>
                  <Entypo name='chevron-up' size={25} color={Colors.primaryColor} />
                </TouchableOpacity>
              </View>

              {
                isGroupAutoSelect && (
                  <View style={{
                    width: '12%',
                    height: '100%',
                    flexDirection: 'row',
                    justifyContent: 'center',
                    alignItems: 'center',
                    marginHorizontal: 10,
                  }}>

                    <Text style={{
                      // width: '70%',
                      fontSize: switchMerchant ? 10 : 18,
                      fontFamily: 'NunitoSans-SemiBold',
                      textAlign: 'right',
                      marginRight: 5,
                    }}>
                      AutoSelect
                    </Text>

                    <View style={{ marginLeft: switchMerchant ? 0 : 0 }}>
                      <TouchableOpacity
                        onPress={() => {
                          let maxLimit = 0;
                          let autoSelectCount = 0;

                          addOnGroupIndex[addOnGroupIndex].choices.forEach(choice => {
                            if (choice.isAutoSelect && choice !== addOnGroupIndex[addOnGroupIndex].choices[i]) {
                              autoSelectCount++;
                            }
                          });
                          if (!addOnGroupIndex[addOnGroupIndex].choices[i].isAutoSelect) {
                            autoSelectCount++;
                          }

                          // Get the max limit from the variant group
                          maxLimit = parseInt(addOnGroupIndex[addOnGroupIndex].maxSelect, 10) || 0;

                          // Check if we're exceeding the limit
                          if (maxLimit > 0 && autoSelectCount > maxLimit) {
                            Alert.alert(
                              'Auto-Select Limit Exceeded',
                              `You can only auto-select up to ${maxLimit} choices.`,
                              [{ text: 'OK' }]
                            );
                            return;
                          }

                          setAddOnGroupList(
                            addOnGroupList.map((addOnGroup, index) =>
                              index === addOnGroupIndex
                                ? {
                                  ...addOnGroup,
                                  choices: addOnGroup.choices.map(
                                    (choice, choiceIndex) =>
                                      choiceIndex === i
                                        ? {
                                          ...choice,
                                          isAutoSelect: !choice.isAutoSelect,
                                        }
                                        : choice
                                  ),
                                }
                                : addOnGroup
                            )
                          );
                        }}
                      >
                        <View style={{
                          height: windowWidth * 0.02,
                          width: windowWidth * 0.02,
                          justifyContent: 'center',
                          alignItems: 'center',
                          backgroundColor: addOnGroupList[addOnGroupIndex].choices[i]?.isAutoSelect ? Colors.primaryColor : Colors.fieldtTxtColor,
                        }}>
                          <Ionicons name="checkmark-sharp" size={13} color={'#FFFFFF'} style={{ margin: 2 }} />
                        </View>
                      </TouchableOpacity>
                    </View>
                  </View>
                )
              }
            </View>
          );
        })}
      </>
    );
  };

  const importSelectFile = async () => {
    try {
      const res = await DocumentPicker.pickSingle({
        type: [DocumentPicker.types.xlsx],
      });

      // console.log(res);
    } catch (err) {
      if (DocumentPicker.isCancel(err)) {
      } else {
        throw err;
      }
    }
  };

  const renderProductTag = ({ item }) => {
    return (
      <View
        style={{
          borderWidth: 1,
          borderColor: 'darkgreen',
          borderRadius: 5,
          padding: 5,
          alignSelf: 'baseline',
          justifyContent: 'space-between',
          flexDirection: 'row',
          alignItems: 'center',
          marginRight: 10,
          marginBottom: 5,
        }}>
        <Text style={{ fontWeight: '500', color: 'green' }}>TESTING</Text>
        <TouchableOpacity
          style={{ marginLeft: 5 }}
          onPress={() => {
            Alert.alert(
              'Info',
              `Are you sure you want to remove this tag?`,
              [
                { text: 'NO', onPress: () => { }, style: 'cancel' },
                {
                  text: 'YES',
                  onPress: () => { },
                },
              ],
              { cancelable: false },
            );
          }}>
          <AntDesign name="closecircle" color={Colors.fieldtTxtColor} />
        </TouchableOpacity>
      </View>
    );
  };

  const renderConfirmProductTag = ({ item }) => (
    <View
      style={{
        borderWidth: 1,
        borderColor: Colors.primaryColor,
        borderRadius: 5,
        padding: 2,
        paddingVertical: 0,
        alignSelf: 'baseline',
        alignItems: 'center',
        marginRight: 5,
        marginBottom: 5,
      }}>
      <Text
        style={{ fontWeight: '500', color: Colors.primaryColor, fontSize: 14 }}>
        TEST
      </Text>
    </View>
  );

  const renderOptionsList = (params) => {
    const { item, drag, isActive } = params;

    const index = params.getIndex();

    console.log('renderOptionsList');
    console.log(params);

    let indexIndividual = index;

    if (item.choices && item.choices[0] && !item.isShared) {
      if (item.choices[0].choiceMinSelect && item.choices[0].choiceMinSelect >= 0) {
        indexIndividual = addOnGroupList.findIndex(group => {

          if (group.groupId && group.groupId === item.groupId) {
            return true;
          }
          else if (group.hiddenId && item.hiddenId && group.hiddenId === item.hiddenId) {
            return true;
          }
        })
      }
      else {
        indexIndividual = variantGroupList.findIndex(group => {
          if (group.groupId && group.groupId === item.groupId) {
            return true;
          }
          else if (group.hiddenId && item.hiddenId && group.hiddenId === item.hiddenId) {
            return true;
          }
        })
      }
    }

    return (
      <TouchableOpacity onLongPress={drag} style={{ width: '100%' }}>
        <View style={{
          marginBottom: Platform.OS === 'ios' ? 20 : 10,
          flexDirection: 'column'
        }}>
          {(item.choices && item.choices[0]) ? (
            <>
              <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                {(!item.isShared && (item.choices[0].choiceMinSelect != null ? item.choices[0].choiceMinSelect : item.choices[0].choiceMinSelect))
                  ?
                  <>
                    <Text
                      style={{
                        fontSize: switchMerchant ? 10 : 16,
                        fontFamily: 'NunitoSans-SemiBold',
                      }}>
                      {'Add-Ons (Multiple)'}
                    </Text>

                    <Switch
                      width={42}
                      style={{ marginLeft: 10 }}
                      value={!((addOnGroupList[indexIndividual] && addOnGroupList[indexIndividual].isHideAddOn !== undefined) ? addOnGroupList[indexIndividual].isHideAddOn : false)}
                      onSyncPress={(statusTemp) => {
                        setAddOnGroupList(
                          addOnGroupList.map((addOnGroup, index2) =>
                            index2 === indexIndividual
                              ? {
                                ...addOnGroup,
                                isHideAddOn: !statusTemp,
                              }
                              : addOnGroup,
                          ),
                        )
                      }}
                      circleColorActive={Colors.primaryColor}
                      circleColorInactive={Colors.fieldtTxtColor}
                      backgroundActive="#dddddd"
                    />
                  </>
                  :
                  <></>
                }

                {!item.isShared && !((item.choices[0].choiceMinSelect || item.choices[0].choiceMinSelect) != null) ?
                  <>
                    <Text
                      style={{
                        fontSize: switchMerchant ? 10 : 16,
                        fontFamily: 'NunitoSans-SemiBold',
                      }}>
                      {'Variant (Single)'}
                    </Text>

                    <Switch
                      width={42}
                      style={{ marginLeft: 10 }}
                      value={!((variantGroupList[indexIndividual] && variantGroupList[indexIndividual].isHideVariant !== undefined) ? variantGroupList[indexIndividual].isHideVariant : false)}
                      onSyncPress={(statusTemp) => {
                        setVariantGroupList(
                          variantGroupList.map((variantGroup, index2) =>
                            index2 === indexIndividual
                              ? {
                                ...variantGroup,
                                isHideVariant: !statusTemp,
                              }
                              : variantGroup,
                          ),
                        );
                      }}
                      circleColorActive={Colors.primaryColor}
                      circleColorInactive={Colors.fieldtTxtColor}
                      backgroundActive="#dddddd"
                    />
                  </>
                  :
                  <></>
                }

                {item.isShared ? (
                  <>
                    <Text
                      style={{
                        fontSize: switchMerchant ? 10 : 16,
                        fontFamily: 'NunitoSans-SemiBold',
                      }}>
                      {`Shared ${item.type}`}
                    </Text>
                  </>
                ) : <></>}

                <TouchableOpacity onPress={() => { handlePrinterAreaOnClick(index) }} style={{ marginLeft: 10 }}>
                  <Feather name="printer" size={20} color={Colors.primaryColor} />
                </TouchableOpacity>
              </View>
            </>
          ) : <></>}

          <View style={{ ...styles.inputFieldContainer, width: '100%', marginVertical: 0 }}>
            <View style={{ flex: 0.1, flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' }}>
              <View style={{ top: 2, width: '45%' }}>
                <Ionicon
                  name="menu"
                  size={22}
                  color={Colors.primaryColor}
                />
              </View>

              <View style={{ width: '45%' }}>
                {!item.isShared ? (
                  <TouchableOpacity
                    onPress={() => {
                      if (item.choices && item.choices[0]) {
                        if (item.choices[0].choiceMinSelect && item.choices[0].choiceMinSelect >= 0) {
                          setAddOnGroupIndex(addOnGroupList.findIndex(group => {
                            if (group.groupId && group.groupId === item.groupId) {
                              return true;
                            }
                            else if (group.hiddenId && item.hiddenId && group.hiddenId === item.hiddenId) {
                              return true;
                            }
                          }));

                          setShowAddOnModal(true);
                        }
                        else {
                          setVariantGroupIndex(variantGroupList.findIndex(group => {
                            if (group.groupId && group.groupId === item.groupId) {
                              return true;
                            }
                            else if (group.hiddenId && item.hiddenId && group.hiddenId === item.hiddenId) {
                              return true;
                            }
                          }));

                          setShowVariantModal(true);
                        }
                      }
                    }}
                  >
                    <FontAwesome5
                      name={'edit'}
                      size={switchMerchant ? 17 : 18}
                      color={Colors.primaryColor}
                    />
                  </TouchableOpacity>
                ) : <></>}
              </View>
            </View>

            {/* Display Variant AddOn Info */}
            <View style={{ flex: 0.8 }}>
              <ScrollView
                showsHorizontalScrollIndicator={false}
                horizontal
                contentContainerStyle={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  paddingVertical: 4,
                }}
                style={{
                  marginLeft: 5
                }}
              >
                <View
                  style={{
                    alignItems: 'center',
                    justifyContent: 'center',
                    marginRight: 10,

                    backgroundColor: '#F6F7F9',
                    paddingVertical: 8,
                    paddingHorizontal: 32,
                    borderRadius: 12,
                    shadowColor: '#000',
                    shadowOffset: {
                      width: 0,
                      height: 2,
                    },
                    shadowOpacity: 0.22,
                    shadowRadius: 3.22,
                    elevation: 1,
                  }}>
                  <Text
                    style={{
                      fontSize: switchMerchant ? 10 : 16,
                      fontFamily: 'NunitoSans-SemiBold',
                      color: Colors.descriptionColor,
                    }}>
                    {item.name}
                  </Text>
                </View>

                <View
                  style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                  }}>
                  {(item.choices[0] && item.choices[0].choiceMinSelect && item.choices[0].choiceMinSelect >= 0) ?
                    <>{
                      item.choices.map((choice, j) =>
                        <View
                          style={{
                            alignItems: 'center',
                            justifyContent: 'center',
                            flexDirection: 'row',
                            marginRight: 10,

                            backgroundColor: '#EBECF0',
                            paddingVertical: 4,
                            paddingHorizontal: 24,
                            borderRadius: 12,
                            shadowColor: '#000',
                            shadowOffset: {
                              width: 0,
                              height: 2,
                            },
                            shadowOpacity: 0.22,
                            shadowRadius: 3.22,
                            elevation: 1,
                          }}>
                          <Text
                            style={{
                              fontSize: switchMerchant
                                ? 10
                                : 14,
                              fontFamily: 'NunitoSans-Regular',
                            }}>
                            {`${choice.choiceName} (${choice.choiceMinSelect || '0'
                              }-${choice.choiceMaxSelect || '0'
                              }) +(RM${choice.choicePrice})`}
                          </Text>
                        </View>
                      )}
                    </>
                    :
                    <>
                      {
                        item.choices.map((choice, j) =>
                          <View
                            style={{
                              alignItems: 'center',
                              justifyContent: 'center',
                              flexDirection: 'row',
                              marginRight: 10,

                              backgroundColor: '#EBECF0',
                              paddingVertical: 4,
                              paddingHorizontal: 24,
                              borderRadius: 12,
                              shadowColor: '#000',
                              shadowOffset: {
                                width: 0,
                                height: 2,
                              },
                              shadowOpacity: 0.22,
                              shadowRadius: 3.22,
                              elevation: 1,
                            }}>
                            <Text
                              style={{
                                fontSize: switchMerchant
                                  ? 10
                                  : 14,
                                fontFamily: 'NunitoSans-Regular',
                              }}>
                              {`${choice.choiceName} +(RM${choice.choicePrice})`}
                            </Text>
                          </View>
                        )}
                    </>
                  }
                </View>
              </ScrollView>
            </View>

            {/* Remove Button */}
            <View style={{ flex: 0.1, justifyContent: 'space-evenly', alignItems: 'center' }}>
              <TouchableOpacity
                onPress={() => {
                  if (item.isShared) {
                    Alert.alert(
                      'Alert',
                      `Are you sure you want to remove this shared ${item.type === 'Variant' ? 'variant' : 'add-on'}?`,
                      [
                        {
                          text: 'YES',
                          onPress: () => {
                            setOutletItemSharedAddOnList(outletItemSharedAddOnList.filter(listItem => listItem !== item));
                          },
                        },
                        {
                          text: 'NO',
                          onPress: () => { },
                        },
                      ],
                    );
                  }
                  else {
                    if (item.choices[0].choiceMinSelect && item.choices[0].choiceMinSelect >= 0) {
                      Alert.alert(
                        'Alert',
                        'Are you sure you want to remove this add-on?',
                        [
                          {
                            text: 'YES',
                            onPress: () => {
                              setAddOnGroupList([
                                ...addOnGroupList.slice(0, indexIndividual),
                                ...addOnGroupList.slice(indexIndividual + 1),
                              ]);
                            },
                          },
                          {
                            text: 'NO',
                            onPress: () => {

                            },
                          },
                        ],
                      );
                    }
                    else {
                      Alert.alert(
                        'Alert',
                        'Are you sure you want to remove this variant?',
                        [
                          {
                            text: 'YES',
                            onPress: () => {
                              setVariantGroupList([
                                ...variantGroupList.slice(0, indexIndividual),
                                ...variantGroupList.slice(indexIndividual + 1),
                              ]);
                            },
                          },
                          {
                            text: 'NO',
                            onPress: () => {

                            },
                          },
                        ],
                      );
                    }
                  }
                }}>
                <Icon
                  name="minus-circle"
                  size={20}
                  color="#eb3446"
                />
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  ////////////////////////////
  // 9-Aug-2024 - Shared Variant / AddOn

  // Get SharedVariantAddOnList (All shared, not specific Selected Product)
  // Invert Shared Variant AddOn List Key Name

  useEffect(() => {
    let sharedAddOnList = allOutletsItemAddOn.filter(obj => obj.isShared && !obj.deletedAt);
    const combinedAddOnList = sharedAddOnList.map(item => {

      const choices = allOutletsItemAddOnChoiceDict[item.uniqueId].map(choice =>
        Object.fromEntries(
          Object.entries(choice).map(([key, value]) => {
            switch (key) {
              case 'uniqueId':
                return ['choiceId', value];
              case 'name':
                return ['choiceName', value];
              case 'price':
                return ['choicePrice', value];
              case 'minSelect':
                return ['choiceMinSelect', value];
              case 'maxSelect':
                return ['choiceMaxSelect', value];
              default:
                return [key, value];
            }
          })
        )
      );

      const orderIndex = (item.orderIndexList && selectedProductEdit && item.orderIndexList[selectedProductEdit.uniqueId] != null)
        ? item.orderIndexList[selectedProductEdit.uniqueId]
        : null;

      return {
        ...item,
        groupId: item.uniqueId,
        orderIndex,

        printerAreaList: (item.pal && item.pal.length > 0) ? item.pal : printerAreaDropdownList.map(area => area.value),
        type: (item.minSelect || item.maxSelect) ? "Variant" : "AddOn",
        choices,
        //choices: allOutletsItemAddOnChoiceDict[item.uniqueId],
      };
    });

    setSharedVariantAddOnList(combinedAddOnList);

  }, [allOutletsItemAddOn, allOutletsItemAddOnChoiceDict, selectedProductEdit]);

  // Select for selected sharedVaraintAddOn when editing product
  useEffect(() => {
    if (selectedProductEdit) {
      const filteredList = sharedVariantAddOnList.filter(item =>
        item.outletItemIdList.includes(selectedProductEdit.uniqueId)
      );

      setPrevOutletItemSharedAddOnList(filteredList);
      setOutletItemSharedAddOnList(filteredList);
    }
  }, [selectedProductEdit, sharedVariantAddOnList])

  // Update SharedVariantAddOnList Printer Area List
  useEffect(() => {
    if (selectedProductEdit && outletItemSharedAddOnList) {
      const { uniqueId } = selectedProductEdit;

      const tempSharedVariantAddOnPrinterAreaDict = outletItemSharedAddOnList.reduce((acc, sharedVA) => {
        const list = (sharedVA.pal && sharedVA.pal[uniqueId]) ? sharedVA.pal[uniqueId] : printerAreaDropdownList.map(area => area.value);
        if (list.length) acc[sharedVA.uniqueId] = list;
        return acc;
      }, {});

      setSharedVariantAddOnPrinterAreaDict(tempSharedVariantAddOnPrinterAreaDict);
    }
  }, [selectedProductEdit, outletItemSharedAddOnList, printerAreaDropdownList]);

  const renderSharedVariantAddOnList = (item, index) => {
    const sharedVariant = item.item;

    return (
      <View style={{
        width: '100%',
        flexDirection: 'row',
        alignItems: 'center',
        marginVertical: 3,
      }}>

        <TouchableOpacity
          onPress={() => {
            handleSelectSharedAction(sharedVariant);
          }}
        >
          <View style={{
            height: windowWidth * 0.015,
            width: windowWidth * 0.015,
            marginRight: 10,
            justifyContent: 'center',
            alignItems: 'center',
            backgroundColor: tempSelectedSharedAddOnList.includes(sharedVariant) ? Colors.primaryColor : Colors.fieldtTxtColor,
          }}>
            <Ionicons name="checkmark-sharp" size={13} color={'#FFFFFF'} style={{ margin: 2 }} />
          </View>
        </TouchableOpacity>

        <View style={{

        }}>
          <Text>{sharedVariant.name}</Text>
        </View>
      </View>
    )
  };

  const handleSelectSharedAction = (item) => {
    if (tempSelectedSharedAddOnList.includes(item)) {
      let newArr = tempSelectedSharedAddOnList.filter(i => i !== item);
      setTempSelectedSharedAddOnList(newArr);
    }
    else {
      setTempSelectedSharedAddOnList([...tempSelectedSharedAddOnList, item]);
    }
  };

  const handleSaveSharedVariantAddOnAction = () => {
    if (JSON.stringify(outletItemSharedAddOnList) !== JSON.stringify(tempSelectedSharedAddOnList)) {
      setOutletItemSharedAddOnList(tempSelectedSharedAddOnList);

      const newDict = tempSelectedSharedAddOnList.reduce((acc, item) => {
        acc[item.uniqueId] = [];
        return acc;
      }, {});

      setSharedVariantAddOnPrinterAreaDict(prevDict => {
        const updatedDict = { ...prevDict };

        Object.keys(updatedDict).forEach(key => {
          if (!newDict.hasOwnProperty(key)) {
            delete updatedDict[key];
          }
        });

        Object.keys(newDict).forEach(key => {
          if (!updatedDict.hasOwnProperty(key)) {
            updatedDict[key] = newDict[key];
          }
        });

        return updatedDict;
      });
      setTempSelectedSharedAddOnList([]);
    }
    setShowSharedGroup(false);
  };

  //////////////////////////////////////////////////////////////////
  // 30-Aug-2024 - Variant/AddOn Choices Order Index Support

  useEffect(() => {
    const updateOrderIndexes = (groupList) => {
      return groupList.map(group => ({
        ...group,
        choices: group.choices.map((choice, idx) => ({
          ...choice,
          orderIndex: idx + 1
        }))
      }));
    };

    let tempVariantChoices = updateOrderIndexes([...variantGroupList]);
    let tempAddOnChoices = updateOrderIndexes([...addOnGroupList]);

    if (JSON.stringify(tempVariantChoices) !== JSON.stringify(variantGroupList)) {
      setVariantGroupList(tempVariantChoices);
    }

    if (JSON.stringify(tempAddOnChoices) !== JSON.stringify(addOnGroupList)) {
      setAddOnGroupList(tempAddOnChoices);
    }
    console.log('addon')
    console.log(addOnGroupList)
    console.log('variant')
    console.log(variantGroupList)
  }, [variantGroupList, addOnGroupList]);

  const findArrayContainingChoice = (targetChoice) => {
    const allGroups = [variantGroupList, addOnGroupList].flatMap(array => array);

    const targetGroup = allGroups.find(group =>
      Array.isArray(group.choices) && group.choices.includes(targetChoice)
    );

    return { targetGroup };
  };

  const moveChoicesForward = (targetChoice) => {
    const { targetGroup } = findArrayContainingChoice(targetChoice);

    if (targetGroup) {
      let updatedChoicesList = [...targetGroup.choices];
      const choiceIndex = updatedChoicesList.findIndex(item => item === targetChoice);

      if (choiceIndex > 0) {
        const tempChoice = updatedChoicesList[choiceIndex - 1];
        updatedChoicesList[choiceIndex - 1] = updatedChoicesList[choiceIndex];
        updatedChoicesList[choiceIndex] = tempChoice;

        updateChoicesArray(targetGroup, updatedChoicesList);
      }
    }
  };

  const moveChoicesBackward = (targetChoice) => {
    const { targetGroup } = findArrayContainingChoice(targetChoice);

    if (targetGroup) {
      let updatedChoicesList = [...targetGroup.choices];
      const choiceIndex = updatedChoicesList.findIndex(item => item === targetChoice);

      if (choiceIndex < updatedChoicesList.length - 1) {
        const tempChoice = updatedChoicesList[choiceIndex + 1];
        updatedChoicesList[choiceIndex + 1] = updatedChoicesList[choiceIndex];
        updatedChoicesList[choiceIndex] = tempChoice;

        updateChoicesArray(targetGroup, updatedChoicesList);
      }
    }
  };

  const moveChoicesToTop = (targetChoice) => {
    const { targetGroup } = findArrayContainingChoice(targetChoice);

    if (targetGroup) {
      let updatedChoicesList = [...targetGroup.choices];
      const choiceIndex = updatedChoicesList.findIndex(item => item === targetChoice);

      if (choiceIndex > -1) {
        const [itemToMove] = updatedChoicesList.splice(choiceIndex, 1);
        updatedChoicesList.unshift(itemToMove);

        updateChoicesArray(targetGroup, updatedChoicesList);
      }
    }
  };

  const updateChoicesArray = (oriArray, updatedChoicesList) => {
    if (variantGroupList.includes(oriArray)) {
      let updatedVariantGroupList = [...variantGroupList];

      let itemToUpdateIndex = updatedVariantGroupList.indexOf(oriArray);
      updatedVariantGroupList[itemToUpdateIndex].choices = updatedChoicesList

      setVariantGroupList(updatedVariantGroupList);
    }
    else if (addOnGroupList.includes(oriArray)) {
      let updatedAddOnGroupList = [...addOnGroupList];
      let itemToUpdateIndex = updatedAddOnGroupList.indexOf(oriArray);
      updatedAddOnGroupList[itemToUpdateIndex].choices = updatedChoicesList

      setAddOnGroupList(updatedAddOnGroupList);
    }
  };

  ////////////////////////////
  navigation.setOptions({
    headerLeft: () => (
      <TouchableOpacity
        onPress={() => {
          if (isAlphaUser || true) {
            navigation.navigate('MenuOrderingScreen');

            CommonStore.update((s) => {
              s.currPage = 'MenuOrderingScreen';
              s.currPageStack = [...currPageStack, 'MenuOrderingScreen'];
            });
          } else {
            navigation.navigate('Table');

            CommonStore.update((s) => {
              s.currPage = 'Table';
              s.currPageStack = [...currPageStack, 'Table'];
            });
          }
          if (expandTab !== EXPAND_TAB_TYPE.OPERATION) {
            CommonStore.update((s) => {
              s.expandTab = EXPAND_TAB_TYPE.OPERATION;
            });
          }
        }}
        style={styles.headerLeftStyle}>
        <Image
          style={{
            width: 124,
            height: 26,
          }}
          resizeMode="contain"
          source={require('../assets/image/logo.png')}
        />
      </TouchableOpacity>
    ),
    headerTitle: () => (
      <View
        style={[
          {
            // justifyContent: Platform.OS === 'android' ? 'center' : 'flex-start',
            // alignItems: Platform.OS === 'android' ? 'center' : 'flex-start',
            // marginRight: Platform.OS === 'ios' ? '27%' : 0,
            // bottom: switchMerchant ? '2%' : 0,
            // width: switchMerchant ? '100%' : Platform.OS === 'ios' ? '96%' : '100%',
            ...global.getHeaderTitleStyle(),
          },
          // windowWidth >= 768 && switchMerchant
          //   ? { right: windowWidth * 0.1 }
          //   : {},
          // windowWidth <= 768
          //   ? { right: 20 }
          //   : {},
        ]}>
        <Text
          style={{
            fontSize: switchMerchant ? 20 : 24,
            // lineHeight: 25,
            textAlign: 'left',
            alignItems: 'flex-start',
            justifyContent: 'flex-start',
            fontFamily: 'NunitoSans-Bold',
            color: Colors.whiteColor,
            opacity: 1,
            paddingLeft: Platform.OS === 'ios' ? '1%' : 0,

          }}>
          {selectedProductEdit ? 'Edit Product' : 'Add Product'}
        </Text>
      </View>
    ),
    headerRight: () => (
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
        }}>
        {outletSelectDropdownView()}
        <View
          style={{
            backgroundColor: 'white',
            width: 0.5,
            height: windowHeight * 0.025,
            opacity: 0.8,
            marginHorizontal: 15,
            bottom: -1,
          }} />
        <TouchableOpacity
          onPress={() => {
            if (global.currUserRole === 'admin') {
              navigation.navigate('Setting');
            }
          }}
          style={{ flexDirection: 'row', alignItems: 'center' }}>
          <Text
            style={[{
              fontFamily: 'NunitoSans-SemiBold',
              fontSize: switchMerchant ? 10 : 16,
              color: Colors.secondaryColor,
              marginRight: 15,
            }, switchMerchant ? { width: windowWidth / 8 } : {}]}
            numberOfLines={switchMerchant ? 1 : 1}
          >

            {userName}
          </Text>
          <View
            style={{
              marginRight: 30,
              width: windowHeight * 0.05,
              height: windowHeight * 0.05,
              borderRadius: windowHeight * 0.05 * 0.5,
              alignItems: 'center',
              justifyContent: 'center',
              backgroundColor: 'white',
            }}>
            <Image
              style={{
                width: windowHeight * 0.035,
                height: windowHeight * 0.035,
                alignSelf: 'center',
              }}
              source={require('../assets/image/profile-pic.jpg')}
            />
          </View>
        </TouchableOpacity>
      </View>
    ),
  });

  // Render Start Here
  return (
    (<View style={[
      styles.container,
      !isTablet() && { transform: [{ scaleX: 1 }, { scaleY: 1 }] },
      getTransformForScreenInsideNavigation()
    ]}>
      {/******* Side Bar ********/}
      {/* <View style={Styles.sideBarWidth}>
        <SideBar navigation={navigation} selectedTab={2} expandProduct />
      </View> */}
      {/******* Main View ********/}
      <View style={styles.content}>
        {/******* Modals ********/}
        <DateTimePickerModal
          locale="en_GB"
          mode={'time'}
          isVisible={showEffectiveStartTimePicker}
          date={effectiveStartTime}
          onConfirm={(dt) => {
            setShowEffectiveStartTimePicker(false);
            setEffectiveStartTime(moment(dt).toDate());
          }}
          onCancel={() => {
            setShowEffectiveStartTimePicker(false);
          }}
        />

        <DateTimePickerModal
          locale="en_GB"
          mode={'time'}
          isVisible={showEffectiveEndTimePicker}
          date={effectiveEndTime}
          onConfirm={(dt) => {
            setShowEffectiveEndTimePicker(false);
            setEffectiveEndTime(moment(dt).toDate());
          }}
          onCancel={() => {
            setShowEffectiveEndTimePicker(false);
          }}
        />

        {/******* Main Content ********/}

        {/* Back Button */}
        <TouchableOpacity
          style={{ width: '10%' }}
          onPress={() => { handleBackButtonAction() }}>
          <View
            style={{
              paddingTop: 10,
              marginBottom: 10,
              display: 'flex',
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'flex-start',
            }}>
            <Feather
              name="chevron-left"
              size={switchMerchant ? 20 : 30}
              style={{ color: Colors.primaryColor, alignSelf: 'center' }}
            />
            <Text
              style={{
                fontFamily: 'Nunitosans-Bold',
                color: Colors.primaryColor,
                fontSize: switchMerchant ? 14 : 17,
                marginBottom: Platform.OS === 'ios' ? 0 : 1,
              }}>
              Back
            </Text>
          </View>
        </TouchableOpacity>

        <View style={{
          backgroundColor: Colors.whiteColor,
          width: windowWidth * 0.87,
          height: switchMerchant ? '75%' : '90%',
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.22,
          shadowRadius: 3.22,
          elevation: 1,
          borderRadius: 8,
          top: !switchMerchant && keyboardHeight > 0
            ? (Platform.OS === 'ios' ? -100 : 0)
            : 0,
        }}>
          <KeyboardAwareScrollView
            style={{
              width: '100%',
              marginTop: 15,
              marginBottom: 5,
              marginRight: 10,
              zIndex: 10000,
            }}
            contentContainerStyle={{ alignItems: 'flex-end' }}
            showsVerticalScrollIndicator={false}
            keyboardShouldPersistTaps="handled"
            behavior="padding"
          >
            {/* Action Button - Delete, Create/Save */}
            <View>
              {/* Delete Button */}
              {selectedProductEdit ? (
                <View style={{
                  justifyContent: 'center',
                  flexDirection: 'row',
                  borderWidth: 1,
                  borderColor: Colors.tabRed,
                  backgroundColor: Colors.tabRed,
                  borderRadius: 5,
                  width: switchMerchant ? 100 : 130,
                  paddingHorizontal: 10,
                  height: switchMerchant ? 35 : 40,
                  alignItems: 'center',
                  shadowOffset: { width: 0, height: 2 },
                  shadowOpacity: 0.22,
                  shadowRadius: 3.22,
                  elevation: 1,
                  zIndex: -1,
                  marginBottom: 10,
                  marginRight: 10,
                }}>
                  <TouchableOpacity
                    style={{
                      alignItems: 'center',
                      flexDirection: 'row',
                      justifyContent: 'center',
                      width: switchMerchant ? 100 : 130,
                      height: switchMerchant ? 35 : 40,
                    }}
                    disabled={isLoading}
                    onPress={() => {
                      if (currOutletShiftStatus === OUTLET_SHIFT_STATUS.OPENED) {
                        Alert.alert('Info', 'Please close the shift first to delete the product.');
                      }
                      else {
                        Alert.alert(
                          'Info',
                          'Are you sure you want to remove this item?',
                          [
                            { text: 'YES', onPress: () => handleDeleteOutletItemAction() },
                            { text: 'NO', onPress: () => { } },
                          ],
                          { cancelable: true },
                        );
                      }
                    }}>

                    {(isLoading && isDelete) ? (
                      <ActivityIndicator
                        color={Colors.whiteColor}
                        size={'small'}
                      />
                    ) : (
                      <Text style={{
                        color: Colors.whiteColor,
                        fontSize: switchMerchant ? 10 : 16,
                        fontFamily: 'NunitoSans-Bold',
                      }}>
                        {'DELETE'}
                      </Text>
                    )}
                  </TouchableOpacity>
                </View>
              ) : <></>}

              {/* Create / Save Button */}
              <View style={{
                justifyContent: 'center',
                flexDirection: 'row',
                borderWidth: 1,
                borderColor: Colors.primaryColor,
                backgroundColor: '#0A1F44',
                borderRadius: 5,
                width: switchMerchant ? 100 : 130,
                paddingHorizontal: 10,
                height: switchMerchant ? 35 : 40,
                alignItems: 'center',
                shadowOffset: { width: 0, height: 2 },
                shadowOpacity: 0.22,
                shadowRadius: 3.22,
                elevation: 1,
                zIndex: -1,
                marginRight: 10,
              }}>
                <TouchableOpacity
                  style={{
                    alignItems: 'center',
                    flexDirection: 'row',
                    justifyContent: 'center',
                    width: switchMerchant ? 100 : 130,
                    height: switchMerchant ? 35 : 40,
                  }}
                  disabled={isLoading}
                  onPress={() => { handleCreateOrSaveOutletItemAction() }}
                >
                  {(isLoading && isSave) ? (
                    <ActivityIndicator
                      color={Colors.whiteColor}
                      size={'small'}
                    />
                  ) : (
                    <Text style={{
                      color: Colors.whiteColor,
                      fontSize: switchMerchant ? 10 : 16,
                      fontFamily: 'NunitoSans-Bold',
                    }}>
                      {selectedProductEdit ? 'SAVE' : 'CREATE'}
                    </Text>
                  )}
                </TouchableOpacity>
              </View>
            </View>



            {/* Main Create/Edit Content */}
            <View
              pointerEvents={'box-none'}
              style={{
                borderBottomWidth: StyleSheet.hairlineWidth,
                flexDirection: 'row',
                padding: 30,
                paddingTop: 20,
                paddingBottom: switchMerchant ? 30 : 60,
              }}>

              <View style={{ flex: 0.475 }}>
                {/* Product Photo */}
                <View>
                  <View style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    marginBottom: '2%',
                  }}>
                    <Text style={{
                      fontSize: switchMerchant ? 15 : 20,
                      fontFamily: 'NunitoSans-Regular',
                    }}>
                      Product Photo
                    </Text>

                    {/* Remove Photo */}
                    <TouchableOpacity
                      onPress={() => {
                        setImage('');
                        setImageType('');
                        setIsClearProductImage(true);
                      }}
                      style={{
                        alignItems: 'center',
                        justifyContent: 'center',
                        marginLeft: '2%',
                      }}
                    >
                      <Ionicons name="reload" color={Colors.primaryColor} size={24} />
                    </TouchableOpacity>
                  </View>

                  <TouchableOpacity onPress={() => { handleChoosePhoto() }}>
                    <View style={{
                      width: '80%',
                      aspectRatio: 16 / 9,
                      backgroundColor: Colors.fieldtBgColor,
                      borderWidth: 1,
                      borderRadius: 5,
                      borderColor: '#E5E5E5',
                      justifyContent: 'center',
                      alignItems: 'center',
                      marginBottom: 10,
                    }}>
                      {image ? (
                        <>
                          <AsyncImage
                            source={{ uri: image }}
                            item={selectedProductEdit}
                            style={{
                              width: '100%',
                              height: '100%'
                            }}
                          />

                          <View style={{ position: 'absolute', bottom: 5, right: 5 }}>
                            <FontAwesome5 name="edit" size={switchMerchant ? 17 : 23} color={Colors.primaryColor} />
                          </View>
                        </>
                      ) : (
                        <Icon
                          name="upload"
                          size={switchMerchant ? 80 : 200}
                          color="lightgrey"
                        />
                      )}
                    </View>
                  </TouchableOpacity>
                </View>

                {/* Outlet Selected */}
                <View style={[styles.inputFieldContainer, {
                  zIndex: -10000,
                }]}>
                  <View style={{ width: '35%' }}>
                    <Text style={{
                      fontFamily: 'NunitoSans-Regular',
                      fontWeight: '500',
                      fontSize: switchMerchant ? 10 : 14,
                    }}>
                      Outlet Selected
                    </Text>
                  </View>

                  {
                    (
                      selectedOutletList.every((val) =>
                        outletDropdownList
                          .map((outlet) => outlet.value)
                          .includes(val)
                      )
                      ||
                      selectedOutletList.length === 0
                    ) ? (
                      <DropDownPicker
                        arrowColor={'black'}
                        arrowSize={20}
                        arrowStyle={styles.ddpArrowStyle}
                        containerStyle={styles.ddpContainerStyle}
                        labelStyle={styles.ddpLabelStyle}
                        style={styles.ddpStyle}
                        items={outletDropdownList}
                        itemStyle={styles.ddpItemStyle}
                        placeholder="Choose Outlet"
                        placeholderStyle={styles.textInputPlaceHolder}
                        dropDownStyle={styles.ddpDDStyle}
                        customTickIcon={(press) => (
                          <Ionicon
                            name={'checkmark-outline'}
                            color={press ? Colors.fieldtBgColor : Colors.primaryColor}
                            size={25}
                          />
                        )}
                        onChangeItem={(items) => {
                          setSelectedOutletList(items);
                        }}
                        defaultValue={selectedOutletList}
                        multipleText={'%d outlet(s) selected'}
                        multiple
                      />
                    ) : <></>
                  }
                </View>

                {/* Product Tags */}
                <View style={{ ...styles.inputFieldContainer, height: 'auto', zIndex: -10050 }}>
                  <View style={{ width: '35%' }}>
                    <Text style={{
                      fontSize: switchMerchant ? 10 : 14,
                      color: '#9FA2B4',
                      fontWeight: '700',
                      fontFamily: 'Nunitosans-Regular',
                      width: switchMerchant ? 98 : 98,
                    }}>
                      Tags:
                    </Text>
                  </View>

                  <View style={{ flex: 1, flexDirection: 'row', marginHorizontal: 5 }}>
                    {/* Display Selected Tags */}
                    <View style={{
                      width: '90%',
                      justifyContent: 'space-between',
                      flexDirection: 'row',
                      alignItems: 'center',
                    }}>

                      {
                        selectedProductEdit &&
                          selectedProductEdit.crmUserTagIdList &&
                          selectedProductEdit.crmUserTagIdList.length > 0
                          ? (
                            <View
                              style={{
                                alignItems: 'baseline',
                                alignSelf: 'baseline',
                                flexDirection: 'row',
                                flexWrap: 'wrap',
                              }}>
                              {selectedProductEdit &&
                                selectedProductEdit.crmUserTagIdList &&
                                selectedProductEdit.crmUserTagIdList.map(
                                  (userTagId, i) => {
                                    var tagText = 'N/A';

                                    if (crmUserTagsDict[userTagId]) {
                                      tagText = crmUserTagsDict[userTagId].name;
                                    }
                                    return (
                                      <View
                                        style={{
                                          alignItems: 'baseline',
                                          marginRight: 5,
                                          alignSelf: 'baseline',
                                          flexDirection: 'row',
                                          marginBottom: 5,
                                        }}>
                                        <Text
                                          style={{
                                            fontSize: switchMerchant ? 10 : 14,
                                            borderColor: 'green',
                                            borderWidth: 1,
                                            borderRadius: 5,
                                            fontWeight: '500',
                                            color: Colors.blackColor,
                                            padding: 3,
                                            alignItems: 'baseline',
                                          }}>
                                          {tagText}
                                        </Text>
                                      </View>
                                    );
                                  },
                                )}
                            </View>
                          ) : (
                            <Text style={{
                              fontSize: switchMerchant ? 10 : 14,
                              color: Colors.descriptionColor,
                            }}>
                              No Tag Yet
                            </Text>
                          )}
                    </View>

                    {/* Display Edit Tag Button only when Editing Product */}
                    {selectedProductEdit && (
                      <TouchableOpacity style={{ flex: 1 }} onPress={() => { setTagModal(true) }}>
                        <AntDesign name="tagso" size={switchMerchant ? 17 : 24} style={{ color: Colors.primaryColor }} />
                      </TouchableOpacity>
                    )}
                  </View>
                </View>

                {/* Printer Area */}
                {
                  (printerAreaDropdownList.length > 0 ||
                    (selectedPrinterAreaList.length > 0 && printerAreaDropdownList.includes(selectedPrinterAreaList[0])))
                    ? (
                      <View style={[styles.inputFieldContainer, {
                        zIndex: -10100,
                      }]}>
                        <View style={{ width: '35%' }}>
                          <Text style={{
                            fontFamily: 'NunitoSans-Regular',
                            fontWeight: '500',
                            fontSize: switchMerchant ? 10 : 14,
                          }}>
                            Printer Area
                          </Text>
                        </View>

                        {(
                          selectedPrinterAreaList.every((val) =>
                            printerAreaDropdownList
                              .map((area) => area.value)
                              .includes(val)
                          )
                          ||
                          selectedPrinterAreaList.length === 0
                        ) ? (
                          <DropDownPicker
                            arrowColor="black"
                            arrowSize={20}
                            arrowStyle={styles.ddpArrowStyle}
                            labelStyle={styles.ddpLabelStyle}
                            style={styles.ddpStyle}
                            containerStyle={styles.ddpContainerStyle}
                            placeholderStyle={styles.textInputPlaceHolder}
                            dropDownStyle={styles.ddpDDStyle}
                            itemStyle={styles.ddpItemStyle}
                            customTickIcon={(press) => (
                              <Ionicon
                                name={'checkmark-outline'}
                                color={press ? Colors.fieldtBgColor : Colors.primaryColor}
                                size={25}
                              />
                            )}
                            placeholder="Choose Printer Area"
                            items={printerAreaDropdownList}
                            multipleText={'%d printer area(s) selected'}
                            multiple
                            defaultValue={selectedPrinterAreaList}
                            onChangeItem={(items) => {
                              setSelectedPrinterAreaList(items);
                            }}
                          />

                        ) : (
                          <></>
                        )}
                      </View>
                    ) : <></>
                }

                {/* /////////////////////////////// */}

                {/* 2024-08-23 - printing type list support */}

                <View style={[styles.inputFieldContainer, {
                  zIndex: -10150,
                }]}>
                  <View style={{ width: '35%' }}>
                    <Text style={{
                      fontFamily: 'NunitoSans-Regular',
                      fontWeight: '500',
                      fontSize: switchMerchant ? 10 : 14,
                    }}>
                      Printer Type
                    </Text>
                  </View>

                  <DropDownPicker
                    arrowColor="black"
                    arrowSize={20}
                    arrowStyle={styles.ddpArrowStyle}
                    labelStyle={styles.ddpLabelStyle}
                    style={styles.ddpStyle}
                    containerStyle={styles.ddpContainerStyle}
                    placeholderStyle={styles.textInputPlaceHolder}
                    dropDownStyle={styles.ddpDDStyle}
                    itemStyle={styles.ddpItemStyle}
                    customTickIcon={(press) => (
                      <Ionicon
                        name={'checkmark-outline'}
                        color={press ? Colors.fieldtBgColor : Colors.primaryColor}
                        size={25}
                      />
                    )}
                    placeholder="Choose Printer Type"
                    items={printingTypeDropdownList}
                    multipleText={'%d printer type(s) selected'}
                    multiple
                    defaultValue={selectedPrintingTypeList}
                    onChangeItem={(items) => {
                      setSelectedPrintingTypeList(items);
                    }}
                  />
                </View>

                {/* /////////////////////////////// */}

                {/* Takeaway Charges */}
                <View style={[styles.inputFieldContainer, {
                  zIndex: -10200,
                }]}>
                  <View style={{ width: '35%' }}>
                    <Text style={{
                      fontFamily: 'NunitoSans-Regular',
                      fontWeight: '500',
                      fontSize: switchMerchant ? 10 : 14,
                    }}>
                      Takeaway Charges
                    </Text>
                  </View>

                  <View style={{ flex: 1 }}>
                    <View style={{
                      width: '90%',
                      flexDirection: 'row',
                      alignItems: 'center',
                      justifyContent: 'space-around'
                    }}>

                      <View style={{ width: '20%' }}>
                        <TextInput
                          underlineColorAndroid={Colors.fieldtBgColor}
                          placeholderTextColor={Platform.select({
                            ios: '#a9a9a9',
                          })}
                          placeholderStyle={{
                            fontFamily: 'NunitoSans-Regular',
                            fontSize: switchMerchant ? 10 : 14,
                          }}
                          style={[
                            styles.textPrice,
                            {
                              padding: 5,
                              height: '80%',
                              fontFamily: 'NunitoSans-Regular',
                              fontSize: switchMerchant ? 10 : 14,
                              backgroundColor: Colors.fieldtBgColor,
                            },
                          ]}
                          placeholder={'0.00'}

                          clearTextOnFocus
                          onFocus={() => {
                            setTemp(pickUpCharges);
                            setPickUpCharges('');
                          }}
                          onEndEditing={() => {
                            if (pickUpCharges == '') {
                              setPickUpCharges(temp);
                            }
                          }}
                          onChangeText={(text) => {
                            setPickUpCharges(parseValidPriceText(text));
                          }}
                          defaultValue={pickUpCharges}
                          keyboardType={'decimal-pad'}
                        />
                      </View>

                      {
                        CHARGES_TYPE_DROPDOWN_LIST.map(charges => charges.value).includes(pickUpChargesType)
                          ? (
                            <DropDownPicker
                              containerStyle={{ ...styles.ddpContainerStyle, marginHorizontal: 10 }}
                              arrowColor={'black'}
                              arrowSize={20}
                              arrowStyle={{ fontWeight: 'bold' }}
                              labelStyle={styles.ddpLabelStyle}
                              style={styles.ddpStyle}
                              placeholderStyle={styles.textInputPlaceHolder}
                              itemStyle={styles.ddpItemStyle}
                              items={CHARGES_TYPE_DROPDOWN_LIST}
                              placeholder="Charges Type"
                              onChangeItem={(item) => {
                                setPickUpChargesType(item.value);
                              }}
                              defaultValue={pickUpChargesType}
                              dropDownMaxHeight={150}
                              dropDownStyle={styles.ddpDDStyle}
                            />
                          ) : <></>
                      }

                      <View style={{ width: '20%' }}>
                        <Switch
                          width={42}
                          value={pickUpChargesActive}
                          onSyncPress={(statusTemp) => setPickUpChargesActive(statusTemp)}
                          circleColorActive={Colors.primaryColor}
                          circleColorInactive={Colors.fieldtTxtColor}
                          backgroundActive="#dddddd"
                        />
                      </View>

                    </View>
                  </View>
                </View>

                {/* Delivery Charges */}
                <View style={[styles.inputFieldContainer, {
                  zIndex: -10250,
                }]}>
                  <View style={{ width: '35%' }}>
                    <Text style={{
                      fontFamily: 'NunitoSans-Regular',
                      fontWeight: '500',
                      fontSize: switchMerchant ? 10 : 14,
                    }}>
                      Delivery Charges
                    </Text>
                  </View>

                  <View style={{ flex: 1 }}>
                    <View style={{
                      width: '90%',
                      flexDirection: 'row',
                      alignItems: 'center',
                      justifyContent: 'space-around'
                    }}>

                      <View style={{ width: '20%' }}>
                        <TextInput
                          underlineColorAndroid={Colors.fieldtBgColor}
                          placeholderTextColor={Platform.select({
                            ios: '#a9a9a9',
                          })}
                          placeholderStyle={{
                            fontFamily: 'NunitoSans-Regular',
                            fontSize: switchMerchant ? 10 : 14,
                          }}
                          style={[
                            styles.textPrice,
                            {
                              padding: 5,
                              height: '80%',
                              fontFamily: 'NunitoSans-Regular',
                              fontSize: switchMerchant ? 10 : 14,
                              backgroundColor: Colors.fieldtBgColor,
                            },
                          ]}
                          placeholder={'0.00'}
                          clearTextOnFocus

                          onFocus={() => {
                            setTemp(deliveryCharges);
                            setDeliveryCharges('');
                          }}
                          onEndEditing={() => {
                            if (deliveryCharges == '') {
                              setDeliveryCharges(temp);
                            }
                          }}
                          onChangeText={(text) => {
                            setDeliveryCharges(parseValidPriceText(text));
                          }}
                          defaultValue={deliveryCharges}
                          keyboardType={'decimal-pad'}
                        />
                      </View>

                      {
                        CHARGES_TYPE_DROPDOWN_LIST.map(charges => charges.value).includes(pickUpChargesType)
                          ? (
                            <DropDownPicker
                              containerStyle={{ ...styles.ddpContainerStyle, marginHorizontal: 10 }}
                              arrowColor={'black'}
                              arrowSize={20}
                              arrowStyle={{ fontWeight: 'bold' }}
                              labelStyle={styles.ddpLabelStyle}
                              style={styles.ddpStyle}
                              placeholderStyle={styles.textInputPlaceHolder}
                              itemStyle={styles.ddpItemStyle}
                              items={CHARGES_TYPE_DROPDOWN_LIST}
                              placeholder="Charges Type"
                              onChangeItem={(item) => {
                                setPickUpChargesType(item.value);
                              }}
                              defaultValue={deliveryChargesType}
                              dropDownStyle={styles.ddpDDStyle}
                            />
                          ) : <></>
                      }

                      <View style={{ width: '20%' }}>
                        <Switch
                          width={42}
                          value={deliveryChargesActive}
                          onSyncPress={(statusTemp) => setDeliveryChargesActive(statusTemp)}
                          circleColorActive={Colors.primaryColor}
                          circleColorInactive={Colors.fieldtTxtColor}
                          backgroundActive="#dddddd"
                        />
                      </View>

                    </View>
                  </View>
                </View>

                {/* Other D. Charges */}
                <View style={[styles.inputFieldContainer, {
                  zIndex: -10300,
                }]}>
                  <View style={{ width: '35%' }}>
                    <Text style={{
                      fontFamily: 'NunitoSans-Regular',
                      fontWeight: '500',
                      fontSize: switchMerchant ? 10 : 14,
                    }}>
                      Other D. Charges
                    </Text>
                  </View>

                  <View style={{ flex: 1 }}>
                    <View style={{
                      width: '90%',
                      flexDirection: 'row',
                      alignItems: 'center',
                      justifyContent: 'space-around'
                    }}>

                      <View style={{ width: '20%' }}>
                        <TextInput
                          underlineColorAndroid={Colors.fieldtBgColor}
                          placeholderTextColor={Platform.select({
                            ios: '#a9a9a9',
                          })}
                          placeholderStyle={{
                            fontFamily: 'NunitoSans-Regular',
                            fontSize: switchMerchant ? 10 : 14,
                          }}
                          style={[
                            styles.textPrice,
                            {
                              padding: 5,
                              height: '80%',
                              fontFamily: 'NunitoSans-Regular',
                              fontSize: switchMerchant ? 10 : 14,
                              backgroundColor: Colors.fieldtBgColor,
                            },
                          ]}
                          placeholder={'0.00'}
                          clearTextOnFocus

                          onFocus={() => {
                            setTemp(otherDCharges);
                            setOtherDCharges('');
                          }}
                          onEndEditing={() => {
                            if (otherDCharges == '') {
                              setOtherDCharges(temp);
                            }
                          }}
                          onChangeText={(text) => {
                            setOtherDCharges(parseValidPriceText(text));
                          }}
                          defaultValue={otherDCharges}
                          keyboardType={'decimal-pad'}
                        />
                      </View>

                      {
                        CHARGES_TYPE_DROPDOWN_LIST.map(charges => charges.value).includes(otherDChargesType)
                          ?
                          <DropDownPicker
                            containerStyle={{ ...styles.ddpContainerStyle, marginHorizontal: 10 }}
                            arrowColor={'black'}
                            arrowSize={20}
                            arrowStyle={{ fontWeight: 'bold' }}
                            labelStyle={styles.ddpLabelStyle}
                            style={styles.ddpStyle}
                            placeholderStyle={styles.textInputPlaceHolder}
                            itemStyle={styles.ddpItemStyle}
                            items={CHARGES_TYPE_DROPDOWN_LIST}
                            placeholder="Charges Type"
                            onChangeItem={(item) => {
                              setOtherDChargesType(item.value);
                            }}
                            defaultValue={otherDChargesType}
                            dropDownStyle={styles.ddpDDStyle}
                          /> : <></>
                      }

                      <View style={{ width: '20%' }}>
                        <Switch
                          width={42}
                          value={otherDChargesActive}
                          onSyncPress={(statusTemp) => setOtherDChargesActive(statusTemp)}
                          circleColorActive={Colors.primaryColor}
                          circleColorInactive={Colors.fieldtTxtColor}
                          backgroundActive="#dddddd"
                        />
                      </View>

                    </View>
                  </View>
                </View>

                {/* Print Docket */}
                <View style={[styles.inputFieldContainer, {
                  zIndex: -10350,
                }]}>
                  <View style={{ width: '35%' }}>
                    <Text style={{
                      fontFamily: 'NunitoSans-Regular',
                      fontWeight: '500',
                      fontSize: switchMerchant ? 10 : 14,
                    }}>
                      Print Docket
                    </Text>
                  </View>

                  <View style={{ flex: 1 }}>
                    <View
                      style={{
                        width: 300,
                        zIndex: 1000,

                        flexDirection: 'row',
                        alignItems: 'center',
                      }}>
                      <View style={{ width: '20%' }}>
                        <TextInput
                          underlineColorAndroid={Colors.fieldtBgColor}
                          placeholderTextColor={Platform.select({
                            ios: '#a9a9a9',
                          })}
                          placeholderStyle={{
                            fontFamily: 'NunitoSans-Regular',
                            fontSize: switchMerchant ? 10 : 14,
                          }}
                          //placeholderTextColor={'#a9a9a9'}
                          style={[
                            styles.textPrice,
                            {
                              padding: 5,
                              height: switchMerchant ? 35 : 40,
                              // width: '120%',
                              fontFamily: 'NunitoSans-Regular',
                              fontSize: switchMerchant ? 10 : 14,
                              backgroundColor: Colors.fieldtBgColor,
                            },
                          ]}
                          placeholder={'0'}
                          //iOS
                          clearTextOnFocus
                          //////////////////////////////////////////////
                          //Android
                          onFocus={() => {
                            setTemp(printDocketQuantity);
                            setPrintDocketQuantity('');
                          }}
                          ///////////////////////////////////////////////
                          //When textinput is not selected
                          onEndEditing={() => {
                            if (printDocketQuantity == '') {
                              setPrintDocketQuantity(temp);
                            }
                          }}
                          onChangeText={(text) => {
                            setPrintDocketQuantity(parseValidIntegerText(text));
                          }}
                          // defaultvalue={deliveryCharges != 0 ? deliveryCharges : ''}
                          defaultValue={printDocketQuantity}
                          keyboardType={'decimal-pad'}
                        />
                      </View>

                      {/* <View
                        style={{
                          width: 120,
                          zIndex: 1000,
                          marginLeft: '4%',
                        }}>
                      </View> */}

                      <View
                        style={{
                          width: '20%',
                          alignItems: 'center',
                          justifyContent: 'center',
                          marginLeft: '1%',
                        }}>
                        <Switch
                          width={42}
                          style={
                            {
                              //flexDirection: 'row',
                              //width: '15%',
                              // marginRight: 20,
                              // marginLeft: 20,
                              //transform: Platform.OS == 'ios' ? [{ scaleX: .6 }, { scaleY: .6 }] : [{ scaleX: .8 }, { scaleY: .8 }]
                              // bottom: -2,
                            }
                          }
                          value={isDocket}
                          onSyncPress={(statusTemp) =>
                            // setState({ status: status })
                            setIsDocket(statusTemp)
                          }
                          circleColorActive={Colors.primaryColor}
                          circleColorInactive={Colors.fieldtTxtColor}
                          backgroundActive="#dddddd"
                        />
                      </View>
                    </View>
                  </View>

                  {/* <View style={{ marginLeft: switchMerchant ? 10 : 20 }}>
                    <Switch
                      width={42}
                      style={
                        {
                          //flexDirection: 'row',
                          //width: '15%',
                          // marginRight: 20,
                          // marginLeft: 20,
                          //transform: Platform.OS == 'ios' ? [{ scaleX: .6 }, { scaleY: .6 }] : [{ scaleX: .8 }, { scaleY: .8 }]
                          // bottom: -2,
                        }
                      }
                      value={isDocket}
                      onSyncPress={(statusTemp) =>
                        // setState({ status: status })
                        setIsDocket(statusTemp)
                      }
                      circleColorActive={Colors.primaryColor}
                      circleColorInactive={Colors.fieldtTxtColor}
                      backgroundActive="#dddddd"
                    />
                  </View> */}
                </View>

                {/* Reservation Menu */}
                <View style={[styles.inputFieldContainer, {
                  zIndex: -10400,
                }]}>
                  <View style={{ width: '35%' }}>
                    <Text style={{
                      fontFamily: 'NunitoSans-Regular',
                      fontWeight: '500',
                      fontSize: switchMerchant ? 10 : 14,
                    }}>
                      Reservation Menu
                    </Text>
                  </View>

                  <View style={{ flex: 1 }}>
                    <Switch
                      width={42}
                      value={isReservationMenu}
                      onSyncPress={(statusTemp) => setIsReservationMenu(statusTemp)}
                      circleColorActive={Colors.primaryColor}
                      circleColorInactive={Colors.fieldtTxtColor}
                      backgroundActive="#dddddd"
                    />
                  </View>
                </View>

                {/* Excluding from Manual Discount*/}
                <View style={[styles.inputFieldContainer, {
                  zIndex: -10450,
                }]}>
                  <View style={{ width: '35%' }}>
                    <Text style={{
                      fontFamily: 'NunitoSans-Regular',
                      fontWeight: '500',
                      fontSize: switchMerchant ? 10 : 14,
                    }}>
                      {`Excluding From\nManual Discount`}
                    </Text>
                  </View>

                  <View style={{ flex: 1 }}>
                    <Switch
                      width={42}
                      value={noManualDisc}
                      onSyncPress={(statusTemp) => setNoManualDisc(statusTemp)}
                      circleColorActive={Colors.primaryColor}
                      circleColorInactive={Colors.fieldtTxtColor}
                      backgroundActive="#dddddd"
                    />
                  </View>
                </View>

                {/* Do not Display*/}
                <View style={[styles.inputFieldContainer, {
                  zIndex: -10500,
                }]}>
                  <View style={{ width: '35%' }}>
                    <Text style={{
                      fontFamily: 'NunitoSans-Regular',
                      fontWeight: '500',
                      fontSize: switchMerchant ? 10 : 14,
                    }}>
                      Do Not Display
                    </Text>
                  </View>

                  {
                    (
                      hideInOrderTypes.every((val) =>
                        [
                          {
                            label: 'Dine In',
                            value: ORDER_TYPE.DINEIN,
                          },
                          {
                            label: 'Takeaway',
                            value: ORDER_TYPE.PICKUP,
                          },
                          {
                            label: 'Delivery',
                            value: ORDER_TYPE.DELIVERY,
                          },
                        ]
                          .map((type) => type.value)
                          .includes(val)
                      )
                      ||
                      hideInOrderTypes.length === 0
                    )
                      ?
                      <DropDownPicker
                        containerStyle={styles.ddpContainerStyle}
                        arrowColor={'black'}
                        arrowSize={20}
                        arrowStyle={styles.ddpArrowStyle}
                        labelStyle={styles.ddpLabelStyle}
                        style={styles.ddpStyle}
                        placeholderStyle={styles.textInputPlaceHolder}
                        itemStyle={styles.ddpItemStyle}
                        items={[
                          {
                            label: 'Dine In',
                            value: ORDER_TYPE.DINEIN,
                          },
                          {
                            label: 'Takeaway',
                            value: ORDER_TYPE.PICKUP,
                          },
                          {
                            label: 'Delivery',
                            value: ORDER_TYPE.DELIVERY,
                          },
                        ]}
                        placeholder="Order Type"
                        multipleText={'%d type(s) selected'}
                        customTickIcon={(press) => (
                          <Ionicon
                            name={'checkmark-outline'}
                            color={press ? Colors.fieldtBgColor : Colors.primaryColor}
                            size={25}
                          />
                        )}
                        onChangeItem={(items) => { setHideInOrderTypes(items); }}
                        defaultValue={hideInOrderTypes}
                        multiple
                        dropDownStyle={styles.ddpDDStyle}
                      />
                      :
                      <></>
                  }
                </View>

                {/* ///////////////////////////////// */}
                {/* 2024-08-15 - add special tags support */}
                <View
                  style={[styles.inputFieldContainer, {
                    zIndex: -10600,
                  }]}>
                  <View style={{ width: '35%' }}>
                    <Text
                      style={{
                        fontFamily: 'NunitoSans-Regular',
                        fontWeight: '500',
                        fontSize: switchMerchant ? 10 : 14,
                      }}>
                      {!switchMerchant && windowWidth < 1000 ? 'Special\nTag' : 'Special Tag'}
                    </Text>
                  </View>
                  {
                    (
                      specialTags.every((val) =>
                        SPECIAL_TAGS_DROPDOWN_LIST
                          .map((type) => type.value)
                          .includes(val)
                      )
                      ||
                      specialTags.length === 0
                    )
                      ?
                      <DropDownPicker
                        containerStyle={styles.ddpContainerStyle}
                        arrowColor={'black'}
                        arrowSize={20}
                        arrowStyle={styles.ddpArrowStyle}
                        labelStyle={styles.ddpLabelStyle}
                        style={styles.ddpStyle}
                        placeholderStyle={styles.textInputPlaceHolder}
                        itemStyle={styles.ddpItemStyle}
                        items={SPECIAL_TAGS_DROPDOWN_LIST}
                        placeholder="Special Tag"
                        multipleText={'%d Tag(s) selected'}
                        customTickIcon={(press) => (
                          <Ionicon
                            name={'checkmark-outline'}
                            color={
                              press ? Colors.fieldtBgColor : Colors.primaryColor
                            }
                            size={25}
                          />
                        )}
                        onChangeItem={(items) => {
                          setSpecialTags(items);
                        }}
                        defaultValue={specialTags}
                        multiple
                        dropDownStyle={styles.ddpDDStyle}
                      />
                      :
                      <></>
                  }
                </View>
                {/* ///////////////////////////////// */}


                {/* Render Confirm Product */}
                {/* <ScrollView
                  pointerEvents={'box-none'}
                  style={{
                    zIndex: -1,
                    maxHeight: 100,
                  }}
                  horizontal
                  showsHorizontalScrollIndicator={false}
                  showsVerticalScrollIndicator={false}>
                  <FlatList
                    data={renderConfirmProductTag}
                    numColumns={3}
                    renderItem={renderConfirmProductTag}
                    keyExtractor={(item, index) => String(index)}
                    style={{
                      paddingVertical: 5,
                      paddingHorizontal: 5,
                    }}
                  />
                </ScrollView> */}
              </View>

              <View style={{ flex: 0.525 }}>
                {/* Name */}
                <View style={styles.inputFieldContainer}>
                  <View style={{ width: '20%', flexDirection: 'row' }}>
                    <Text
                      style={{
                        fontSize: switchMerchant ? 10 : 14,
                        fontFamily: 'NunitoSans-Regular',
                        fontWeight: '500',
                      }}>
                      Name
                    </Text>
                    <Text style={{ color: 'red' }}>*</Text>
                  </View>

                  <TextInput
                    underlineColorAndroid={Colors.fieldtBgColor}
                    style={[
                      styles.textFieldInput,
                      {
                        fontSize: switchMerchant ? 10 : 14,
                        height: switchMerchant ? 35 : 40,
                        width: switchMerchant
                          ? windowWidth * 0.3
                          : Platform.OS == 'ios'
                            ? windowWidth * 0.34
                            : windowWidth * 0.35,
                      },
                      !switchMerchant && windowWidth < 1000 ?
                        {
                          marginLeft: 10,
                          width: windowWidth * 0.33,
                        } : {}
                    ]}
                    placeholder={itemName ? itemName : 'Braised Chicken Wing'}
                    placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                    onChangeText={(text) => {
                      setItemName(text);
                    }}
                    value={itemName}
                  />
                </View>

                {/* Display Name */}
                <View style={styles.inputFieldContainer}>
                  <View style={{ width: '20%', flexDirection: 'row' }}>
                    <Text
                      style={{
                        fontSize: switchMerchant ? 10 : 14,
                        fontFamily: 'NunitoSans-Regular',
                        fontWeight: '500',
                      }}>
                      Display Name
                    </Text>
                  </View>

                  <TextInput
                    underlineColorAndroid={Colors.fieldtBgColor}
                    style={[
                      styles.textFieldInput,
                      {
                        fontSize: switchMerchant ? 10 : 14,
                        height: switchMerchant ? 35 : 40,
                        width: switchMerchant
                          ? windowWidth * 0.3
                          : Platform.OS == 'ios'
                            ? windowWidth * 0.34
                            : windowWidth * 0.35,
                      },
                      !switchMerchant && windowWidth < 1000 ?
                        {
                          marginLeft: 10,
                          width: windowWidth * 0.33,
                        } : {}
                    ]}
                    placeholder={itemDpName ? itemDpName : 'Display Name'}
                    placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                    onChangeText={(text) => {
                      setDpName(text);
                    }}
                    value={itemDpName}
                    ref={myTextInput}
                  />
                </View>

                {/* Description */}
                <View style={styles.inputFieldContainerDescription}>
                  <View style={{ width: '20%', flexDirection: 'row' }}>
                    <Text
                      style={{
                        fontSize: switchMerchant ? 10 : 14,
                        fontFamily: 'NunitoSans-Regular',
                        fontWeight: '500',
                      }}>
                      Description
                    </Text>
                    <Text style={{ color: 'red' }}>*</Text>
                  </View>

                  <View>
                    <TextInput
                      underlineColorAndroid={Colors.fieldtBgColor}
                      style={[
                        styles.textFieldInput,
                        {
                          fontSize: switchMerchant ? 10 : 14,
                          height: switchMerchant ? 120 : 120,
                          width: switchMerchant
                            ? windowWidth * 0.3
                            : Platform.OS == 'ios'
                              ? windowWidth * 0.34
                              : windowWidth * 0.35,
                        },
                        !switchMerchant && windowWidth < 1000 ?
                          {
                            marginLeft: 10,
                            width: windowWidth * 0.33,
                          } : {}
                      ]}
                      placeholder={itemDescription ? itemDescription : 'Savory braised chicken wing'}
                      placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                      textAlignVertical={'top'}
                      multiline
                      //iOS
                      // clearTextOnFocus={true}
                      // //////////////////////////////////////////////
                      // //Android
                      // onFocus={() => {
                      //   setTemp(itemDescription);
                      //   setItemDescription('');
                      // }}
                      // ///////////////////////////////////////////////
                      // //When textinput is not selected
                      // onEndEditing={() => {
                      //   if (itemDescription == '') {
                      //     setItemDescription(temp);
                      //   }
                      // }}
                      onChangeText={(text) => {
                        // setState({ itemDescription: text });
                        setItemDescription(text);
                      }}
                      value={itemDescription}
                    />
                  </View>
                </View>

                {/* SKU */}
                <View style={styles.inputFieldContainer}>
                  <View style={{ width: '20%' }}>
                    <Text
                      style={{
                        fontSize: switchMerchant ? 10 : 14,
                        fontFamily: 'NunitoSans-Regular',
                        fontWeight: '500',
                      }}>
                      SKU
                    </Text>
                  </View>

                  <View
                    style={{
                      flexDirection: 'row',
                      alignItems: 'center',
                      justifyContent: 'space-between',
                      marginBottom: switchMerchant ? 5 : 0,
                      //flex: 1,
                      // width: '45%',
                      // backgroundColor: 'green',
                      width: switchMerchant
                        ? windowWidth * 0.3
                        : Platform.OS == 'ios'
                          ? windowWidth * 0.34
                          : windowWidth * 0.35,
                    }}>
                    <TextInput
                      underlineColorAndroid={Colors.fieldtBgColor}
                      style={[
                        styles.textFieldInput,
                        {
                          fontSize: switchMerchant ? 10 : 14,
                          height: switchMerchant ? 35 : 40,
                          width: switchMerchant
                            ? windowWidth * 0.1
                            : windowWidth * 0.12,
                        },
                        !switchMerchant && windowWidth < 1000 ?
                          {
                            marginLeft: 20,
                            width: windowWidth * 0.11,
                          } : {}
                      ]}
                      placeholder={itemSKU ? itemSKU : 'CW100'}
                      placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                      //iOS
                      clearTextOnFocus
                      //////////////////////////////////////////////
                      //Android
                      onFocus={() => {
                        setTemp(itemSKU);
                        setItemSKU('');
                      }}
                      ///////////////////////////////////////////////
                      //When textinput is not selected
                      onEndEditing={() => {
                        if (itemSKU == '') {
                          setItemSKU(temp);
                        }
                      }}
                      onChangeText={(text) => {
                        // setState({ itemName: text });
                        setItemSKU(text);
                      }}
                      value={itemSKU}
                    />
                    <View
                      style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                        //justifyContent: 'flex-end',
                        // backgroundColor: 'red',
                        // width: '100%',
                      }}>
                      <View
                        style={{
                          marginRight: windowWidth * 0.01,
                          marginLeft: windowWidth * 0.01,
                        }}>
                        {/* <Text
                          style={{
                            fontSize: switchMerchant ? 10 : 14,
                            fontFamily: 'NunitoSans-Regular',
                            fontWeight: '500',
                          }}>
                          Qty
                        </Text> */}
                      </View>
                      <View
                        style={{
                          flexDirection: 'row',
                          alignItems: 'center',
                          alignSelf: 'flex-end',
                        }}>
                        {/* <TextInput
                          underlineColorAndroid={Colors.fieldtBgColor}
                          style={[
                            styles.textTax,
                            {
                              fontSize: switchMerchant ? 10 : 14,
                              width: switchMerchant
                                ? windowWidth * 0.1
                                : windowWidth * 0.12,
                              height: switchMerchant ? 35 : 40,
                            },
                          ]}
                          keyboardType={'decimal-pad'}
                          placeholderTextColor={Platform.select({
                            ios: '#a9a9a9',
                          })}
                          placeholder={'0'}
                          onChangeText={(text) => {
                            // setState({ itemTaxRate: text });
                            setItemQuantity(text);
                          }}
                          value={itemQuantity ? itemQuantity.toString() : '0'}
                        /> */}
                      </View>
                    </View>
                  </View>
                </View>

                {/* Price */}
                <View style={[styles.inputFieldContainer, { zIndex: -10 }]}>
                  <View style={{ width: '20%', flexDirection: 'row' }}>
                    <Text
                      style={{
                        fontSize: switchMerchant ? 10 : 14,
                        fontFamily: 'NunitoSans-Regular',
                        fontWeight: '500',
                      }}>
                      Price
                    </Text>
                    <Text style={{ color: 'red' }}>*</Text>
                  </View>

                  <View
                    style={{
                      flexDirection: 'row',
                      alignItems: 'center',
                      justifyContent: 'space-between',
                      marginBottom: switchMerchant ? 5 : 0,
                      //flex: 1,
                      // width: '45%',
                      // backgroundColor: 'green',
                      width: switchMerchant
                        ? windowWidth * 0.3
                        : Platform.OS == 'ios'
                          ? windowWidth * 0.34
                          : windowWidth * 0.35,
                    }}>
                    <View
                      style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                        alignSelf: 'flex-end',
                        // marginRight: 19,
                      }}>
                      <TextInput
                        underlineColorAndroid={Colors.fieldtBgColor}
                        style={[
                          styles.textPrice,
                          {
                            fontSize: switchMerchant ? 10 : 14,
                            width: switchMerchant
                              ? windowWidth * 0.1
                              : windowWidth * 0.12,
                            height: switchMerchant ? 35 : 40,
                          },
                          !switchMerchant && windowWidth < 1000 ?
                            {
                              marginLeft: 20,
                              width: windowWidth * 0.11,
                            } : {}
                        ]}
                        placeholder={'0.00'}
                        placeholderTextColor={Platform.select({
                          ios: '#a9a9a9',
                        })}
                        //iOS
                        clearTextOnFocus
                        //////////////////////////////////////////////
                        //Android
                        onFocus={() => {
                          setTemp(itemPrice);
                          setItemPrice('');
                        }}
                        ///////////////////////////////////////////////
                        //When textinput is not selected
                        onEndEditing={() => {
                          if (itemPrice == '') {
                            setItemPrice(temp);
                          }
                        }}
                        onChangeText={(text) => {
                          // setState({ itemPrice: text });
                          setItemPrice(parseValidPriceText(text));
                        }}
                        value={itemPrice == '' ? '' : itemPrice}
                        keyboardType={'decimal-pad'}
                      />
                      <Text
                        style={{
                          fontSize: switchMerchant ? 10 : 14,
                          fontWeight: '500',
                          fontFamily: 'NunitoSans-Regular',
                          marginLeft: 10,
                        }}>
                        MYR
                      </Text>
                    </View>

                    <View
                      style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                        //justifyContent: 'flex-end',
                        // backgroundColor: 'red',
                        // width: '100%',
                      }}>
                      <View
                        style={{
                          marginRight: windowWidth * 0.01,
                          marginLeft: windowWidth * 0.01,
                        }}>
                        <Text
                          style={{
                            fontSize: switchMerchant ? 10 : 14,
                            fontFamily: 'NunitoSans-Regular',
                            fontWeight: '500',
                          }}>
                          Type
                        </Text>
                      </View>

                      <View
                        style={{
                          width: switchMerchant
                            ? windowWidth * 0.12
                            : windowWidth * 0.12,
                          zIndex: 1000,
                        }}>
                        <DropDownPicker
                          containerStyle={{
                            height: switchMerchant ? 35 : 40,
                            zIndex: 2,
                          }}
                          arrowColor={'black'}
                          arrowSize={20}
                          arrowStyle={{ fontWeight: 'bold' }}
                          labelStyle={{
                            fontFamily: 'NunitoSans-Regular',
                            fontSize: switchMerchant ? 10 : 14,
                          }}
                          style={{
                            width: switchMerchant
                              ? windowWidth * 0.12
                              : windowWidth * 0.12,
                            paddingVertical: 0,
                            backgroundColor: Colors.fieldtBgColor,
                            borderRadius: 10,
                          }}
                          placeholderStyle={{ color: Colors.fieldtTxtColor }}
                          itemStyle={{
                            justifyContent: 'flex-start',
                            marginLeft: 5,
                            zIndex: 2,
                          }}
                          items={PRODUCT_PRICE_TYPE_DROPDOWN_LIST}
                          placeholder="Price Type"
                          onChangeItem={(item) => {
                            setItemPriceType(item.value);
                          }}
                          defaultValue={itemPriceType}
                          // multiple={true}
                          dropDownMaxHeight={150}
                          dropDownStyle={{
                            width: switchMerchant
                              ? windowWidth * 0.12
                              : windowWidth * 0.12,
                            height: 90,
                            backgroundColor: Colors.fieldtBgColor,
                            borderRadius: 10,
                            borderWidth: 1,
                            textAlign: 'left',
                            zIndex: 2,
                            fontSize: switchMerchant ? 11 : 14,
                          }}
                        />

                        {/* <RNPickerSelect
                          useNativeAndroidPickerStyle={false}
                          style={{
                            inputAndroidContainer: {
                              width: '100%',

                              height: 35,
                              justifyContent: 'center',
                              backgroundColor: '#fafafa',
                              borderRadius: 4,

                              shadowColor: '#000',
                              shadowOffset: {
                                width: 0,
                                height: 2,
                              },
                              shadowOpacity: 0.22,
                              shadowRadius: 3.22,
                              elevation: 1,
                            },
                            inputIOS: {
                              // width: '100%',

                              backgroundColor: '#fafafa',
                              color: 'black',
                              fontFamily: 'NunitoSans-Regular',
                              fontSize: !isTablet() ? 10 : 14,

                              paddingLeft: 12,
                            },
                            inputAndroid: {
                              // width: '100%',

                              backgroundColor: '#fafafa',
                              color: 'black',
                              fontFamily: 'NunitoSans-Regular',
                              fontSize: !isTablet() ? 10 : 14,
                            },
                            // viewContainer: {
                            //   width: '100%',

                            //   backgroundColor: '#fafafa',
                            //   borderRadius: 4,
                            //   height: 35,

                            //   justifyContent: 'center',

                            //   shadowColor: '#000',
                            //   shadowOffset: {
                            //     width: 0,
                            //     height: 2,
                            //   },
                            //   shadowOpacity: 0.22,
                            //   shadowRadius: 3.22,
                            //   elevation: 1,
                            // }
                          }}
                          items={PRODUCT_PRICE_TYPE_DROPDOWN_LIST}
                          value={itemPriceType}
                          onValueChange={(value) =>
                            setItemPriceType(value)
                          }
                        /> */}
                      </View>

                      {/* <View
                        style={{
                          marginRight: windowWidth * 0.01,
                          marginLeft: windowWidth * 0.01,
                        }}>
                        <Text
                          style={{
                            fontSize: switchMerchant ? 10 : 14,
                            fontFamily: 'NunitoSans-Regular',
                            fontWeight: '500',
                          }}>
                          Tax
                        </Text>
                      </View>
                      <View
                        style={{
                          flexDirection: 'row',
                          alignItems: 'center',
                          alignSelf: 'flex-end',
                        }}>
                        <TextInput
                          editable={true}
                          underlineColorAndroid={Colors.fieldtBgColor}
                          style={[
                            styles.textTax,
                            {
                              fontSize: switchMerchant ? 10 : 14,
                              width: switchMerchant
                                ? windowWidth * 0.1
                                : windowWidth * 0.12,
                              height: switchMerchant ? 35 : 40,
                            },
                          ]}
                          keyboardType={'decimal-pad'}
                          placeholderTextColor={Platform.select({
                            ios: '#a9a9a9',
                          })}
                          placeholder={
                            // outletsTaxDict[place]
                            //   ? outletsTaxDict[place].rate
                            //       .toFixed(2)
                            //       .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')
                            //   : '0'
                            (currOutlet && currOutlet.taxActive) ? `${(currOutlet.taxRate * 100).toFixed(0)}%` : '0%'
                          }
                          //iOS
                          clearTextOnFocus={true}
                          //////////////////////////////////////////////
                          //Android
                          onFocus={() => {
                            setTemp(itemTaxRate)
                            setItemTaxRate('');
                          }}
                          ///////////////////////////////////////////////
                          //When textinput is not selected
                          onEndEditing={() => {
                            if (itemTaxRate == '') {
                              setItemTaxRate(temp);
                            }
                          }}
                          onChangeText={(text) => {
                            // setState({ itemTaxRate: text });
                            setItemTaxRate(text);
                          }}
                          value={itemTaxRate ? itemTaxRate : ''}
                          //multiline={true}
                          ref={myTextInput}
                        />
                      </View> */}
                    </View>
                  </View>
                </View>

                {/* Cost Price */}
                <View style={[styles.inputFieldContainer, { zIndex: -11 }]}>
                  <View style={{ width: '20%' }}>
                    <Text
                      style={{
                        fontSize: switchMerchant ? 10 : 14,
                        fontFamily: 'NunitoSans-Regular',
                        fontWeight: '500',
                      }}>
                      Cost Price
                    </Text>
                  </View>

                  <View
                    style={{
                      flexDirection: 'row',
                      alignItems: 'center',
                      justifyContent: 'space-between',
                      marginBottom: switchMerchant ? 5 : 0,
                      //flex: 1,
                      // width: '45%',
                      // backgroundColor: 'green',
                      width: switchMerchant
                        ? windowWidth * 0.3
                        : Platform.OS == 'ios'
                          ? windowWidth * 0.34
                          : windowWidth * 0.35,
                    }}>
                    <View
                      style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                        alignSelf: 'flex-end',
                        // marginRight: 19,
                      }}>
                      <TextInput
                        underlineColorAndroid={Colors.fieldtBgColor}
                        style={[
                          styles.textPrice,
                          {
                            fontSize: switchMerchant ? 10 : 14,
                            width: switchMerchant
                              ? windowWidth * 0.1
                              : windowWidth * 0.12,
                            height: switchMerchant ? 35 : 40,
                          },
                          !switchMerchant && windowWidth < 1000 ?
                            {
                              marginLeft: 20,
                              width: windowWidth * 0.11,
                            } : {}
                        ]}
                        placeholder={'0.00'}
                        placeholderTextColor={Platform.select({
                          ios: '#a9a9a9',
                        })}
                        //iOS
                        clearTextOnFocus
                        //////////////////////////////////////////////
                        //Android
                        onFocus={() => {
                          setTemp(costPrice);
                          setCostPrice('');
                        }}
                        ///////////////////////////////////////////////
                        //When textinput is not selected
                        onEndEditing={() => {
                          if (costPrice == '') {
                            setCostPrice(temp);
                          }
                        }}
                        onChangeText={(text) => {
                          // setState({ itemPrice: text });
                          setCostPrice(parseValidPriceText(text));
                        }}
                        value={costPrice == '' ? '' : costPrice}
                        keyboardType={'decimal-pad'}
                      />
                    </View>
                  </View>
                </View>

                {/* Points */}
                <View style={[styles.inputFieldContainer, { zIndex: -12 }]}>
                  <View style={{ width: '20%' }}>
                    <Text
                      style={{
                        fontSize: switchMerchant ? 10 : 14,
                        fontFamily: 'NunitoSans-Regular',
                        fontWeight: '500',
                      }}>
                      Points
                    </Text>
                  </View>

                  <View
                    style={{
                      flexDirection: 'row',
                      alignItems: 'center',
                      justifyContent: 'space-between',
                      marginBottom: switchMerchant ? 5 : 0,
                      //flex: 1,
                      // width: '45%',
                      // backgroundColor: 'green',
                      width: switchMerchant
                        ? windowWidth * 0.3
                        : Platform.OS == 'ios'
                          ? windowWidth * 0.34
                          : windowWidth * 0.35,
                    }}>
                    <View
                      style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                        alignSelf: 'flex-end',
                        // marginRight: 19,
                      }}>
                      <TextInput
                        underlineColorAndroid={Colors.fieldtBgColor}
                        style={[
                          styles.textPrice,
                          {
                            fontSize: switchMerchant ? 10 : 14,
                            width: switchMerchant
                              ? windowWidth * 0.1
                              : windowWidth * 0.12,
                            height: switchMerchant ? 35 : 40,
                          },
                          !switchMerchant && windowWidth < 1000 ?
                            {
                              marginLeft: 20,
                              width: windowWidth * 0.11,
                            } : {}
                        ]}
                        placeholder={'0.00'}
                        placeholderTextColor={Platform.select({
                          ios: '#a9a9a9',
                        })}
                        //iOS
                        clearTextOnFocus
                        //////////////////////////////////////////////
                        //Android
                        onFocus={() => {
                          setTemp(itemCredit);
                          setItemCredit('');
                        }}
                        ///////////////////////////////////////////////
                        //When textinput is not selected
                        onEndEditing={() => {
                          if (itemCredit == '') {
                            setItemCredit(temp);
                          }
                        }}
                        onChangeText={(text) => {
                          // setState({ itemPrice: text });
                          setItemCredit(parseValidPriceText(text));
                        }}
                        value={itemCredit == '' ? '' : itemCredit}
                        keyboardType={'decimal-pad'}
                      />
                    </View>

                    {
                      itemPriceType === PRODUCT_PRICE_TYPE.UNIT
                        ?
                        <View
                          style={{
                            flexDirection: 'row',
                            alignItems: 'center',
                            //justifyContent: 'flex-end',
                            // backgroundColor: 'red',
                            // width: '100%',
                          }}>
                          <View
                            style={{
                              marginRight: windowWidth * 0.01,
                              marginLeft: windowWidth * 0.01,
                            }}>
                            <Text
                              style={{
                                fontSize: switchMerchant ? 10 : 14,
                                fontFamily: 'NunitoSans-Regular',
                                fontWeight: '500',
                              }}>
                              Unit
                            </Text>
                          </View>

                          <View
                            style={{
                              width: switchMerchant
                                ? windowWidth * 0.12
                                : windowWidth * 0.12,
                              zIndex: 1000,
                            }}>
                            <DropDownPicker
                              containerStyle={{
                                height: switchMerchant ? 35 : 40,
                                zIndex: 2,
                              }}
                              arrowColor={'black'}
                              arrowSize={20}
                              arrowStyle={{ fontWeight: 'bold' }}
                              labelStyle={{
                                fontFamily: 'NunitoSans-Regular',
                                fontSize: switchMerchant ? 10 : 14,
                              }}
                              style={{
                                width: switchMerchant
                                  ? windowWidth * 0.12
                                  : windowWidth * 0.12,
                                paddingVertical: 0,
                                backgroundColor: Colors.fieldtBgColor,
                                borderRadius: 10,
                              }}
                              placeholderStyle={{ color: Colors.fieldtTxtColor }}
                              itemStyle={{
                                justifyContent: 'flex-start',
                                marginLeft: 5,
                                zIndex: 2,
                              }}
                              items={[
                                { label: 'Kilogram', value: 'Kilogram' },
                                { label: 'Gram', value: 'Gram' },
                                { label: 'Litre', value: 'Litre' },
                                { label: 'Millilitre', value: 'Millilitre' },
                                // { label: 'Carton', value: 'Carton' },
                                // { label: 'Box', value: 'Box' },
                                // { label: 'Pack', value: 'Pack' },
                              ]}
                              placeholder="Unit Type"
                              onChangeItem={(item) => {
                                setUnitType(item.value);
                              }}
                              defaultValue={unitType}
                              // multiple={true}
                              dropDownMaxHeight={150}
                              dropDownStyle={{
                                width: switchMerchant
                                  ? windowWidth * 0.12
                                  : windowWidth * 0.12,
                                height: 90,
                                backgroundColor: Colors.fieldtBgColor,
                                borderRadius: 10,
                                borderWidth: 1,
                                textAlign: 'left',
                                zIndex: 2,
                                fontSize: switchMerchant ? 11 : 14,
                              }}
                            />
                          </View>
                        </View>
                        :
                        <></>
                    }
                  </View>
                </View>

                {/* Stock Count */}
                <View style={[styles.inputFieldContainer, { zIndex: -13 }]}>
                  <View style={{ width: '20%' }}>
                    <Text
                      style={{
                        fontSize: switchMerchant ? 10 : 14,
                        fontFamily: 'NunitoSans-Regular',
                        fontWeight: '500',
                      }}>
                      {!switchMerchant && windowWidth < 1000 ? 'Stock\nCount' : 'Stock Count'}
                    </Text>
                  </View>

                  <View
                    style={{
                      width: switchMerchant
                        ? windowWidth * 0.3
                        : Platform.OS == 'ios'
                          ? windowWidth * 0.34
                          : windowWidth * 0.35,
                      flexDirection: 'row',
                      alignItems: 'center',
                    }}>
                    <TextInput
                      underlineColorAndroid={Colors.fieldtBgColor}
                      style={[
                        styles.textFieldInput,
                        {
                          fontSize: switchMerchant ? 10 : 14,
                          height: switchMerchant ? 35 : 40,
                          width: switchMerchant
                            ? windowWidth * 0.1
                            : windowWidth * 0.12,
                        },
                        !switchMerchant && windowWidth < 1000 ?
                          {
                            marginLeft: 20,
                            width: windowWidth * 0.11,
                          } : {}
                      ]}
                      placeholder={'0'}
                      placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                      //iOS
                      clearTextOnFocus
                      ////////////////////////////////////////////
                      //Android
                      onFocus={() => {
                        // if (!isUsingVariantStockCount) {
                        //   setTemp(stockCount)
                        //   setStockCount('');
                        // }

                        setTemp(stockCount);
                        setStockCount('');
                      }}
                      /////////////////////////////////////////////
                      //When textinput is not selected
                      onEndEditing={() => {
                        if (stockCount == '') {
                          setStockCount(temp);
                        }
                      }}
                      onChangeText={(text) => {
                        // setState({ itemDescription: text });

                        // var value = parseInt(text);

                        // setStockCount(!isNaN(value) ? value : 0);

                        setStockCount(parseValidIntegerText(text));
                      }}
                      // defaultValue={isUsingVariantStockCount ?
                      //   getTotalVariantStockCount().toFixed(0)
                      //   : stockCount.toFixed(0)}
                      // defaultValue={stockCount.toFixed(0)}
                      //multiline={true}
                      value={stockCount}
                      disabled={!isStockCountActive}
                      editable={isStockCountActive}
                      keyboardType='decimal-pad'
                    />

                    <View style={{ marginLeft: switchMerchant ? 10 : 20 }}>
                      <Switch
                        width={42}
                        style={
                          {
                            //flexDirection: 'row',
                            //width: '15%',
                            // marginRight: 20,
                            // marginLeft: 20,
                            //transform: Platform.OS == 'ios' ? [{ scaleX: .6 }, { scaleY: .6 }] : [{ scaleX: .8 }, { scaleY: .8 }]
                            // bottom: -2,
                          }
                        }
                        // disabled={true}
                        value={isStockCountActive}
                        circleColorActive={Colors.primaryColor}
                        circleColorInactive={Colors.fieldtTxtColor}
                        backgroundActive="#dddddd"
                        onSyncPress={(value) => {
                          setIsStockCountActive(value);
                        }}
                      />
                    </View>
                  </View>
                </View>

                {/* Category */}
                <View style={[styles.inputFieldContainer, { zIndex: -14 }]}>
                  <View style={{ width: '20%', flexDirection: 'row' }}>
                    <Text
                      style={{
                        fontSize: switchMerchant ? 10 : 14,
                        fontFamily: 'NunitoSans-Regular',
                        fontWeight: '500',
                      }}>
                      Category
                    </Text>
                    <Text style={{ color: 'red' }}>*</Text>
                  </View>

                  <View
                    style={[
                      styles.textInput4,
                      {
                        width: switchMerchant
                          ? windowWidth * 0.3
                          : Platform.OS == 'ios'
                            ? windowWidth * 0.34
                            : windowWidth * 0.35,
                        height: switchMerchant ? 35 : 40,
                        // zIndex: 1000,
                      },
                      !switchMerchant && windowWidth < 1000 ?
                        {
                          marginLeft: 10,
                          width: windowWidth * 0.33,
                        } : {}
                    ]}>
                    {/* {outletCategoryDropdownList.find(
                      (o) => o.value === selectedOutletCategoryId,
                    ) ? ( */}
                    <DropDownPicker
                      // items={categoryOutlet}
                      //  defaultValue={cate.name}
                      //  placeholder={cate.name}

                      containerStyle={{
                        height: switchMerchant ? 35 : 40,
                        zIndex: 2,
                      }}
                      arrowColor={'black'}
                      arrowSize={20}
                      arrowStyle={{ fontWeight: 'bold' }}
                      labelStyle={{
                        fontFamily: 'NunitoSans-Regular',
                        fontSize: switchMerchant ? 10 : 14,
                      }}
                      style={{
                        width: switchMerchant
                          ? windowWidth * 0.3
                          : windowWidth < 1000
                            ? windowWidth * 0.33
                            : windowWidth * 0.35,
                        paddingVertical: 0,
                        backgroundColor: Colors.fieldtBgColor,
                        borderRadius: 10,
                        fontSize: switchMerchant ? 11 : 14,
                      }}
                      placeholderStyle={{ color: Colors.fieldtTxtColor }}
                      items={outletCategoryDropdownList}

                      {...(outletCategoryDropdownList.find((o) =>
                        o.value === selectedOutletCategoryId) &&
                        { defaultValue: selectedOutletCategoryId })}

                      itemStyle={{
                        justifyContent: 'flex-start',
                        marginLeft: 5,
                        zIndex: 2,
                      }}
                      placeholder={'Please select a Category'}
                      placeholderTextColor={Platform.select({
                        ios: '#a9a9a9',
                      })}
                      customTickIcon={(press) => (
                        <Ionicon
                          name={'checkbox-outline'}
                          color={
                            press ? Colors.fieldtBgColor : Colors.primaryColor
                          }
                          size={25}
                        />
                      )}
                      onChangeItem={(item) =>
                        setSelectedOutletCategoryId(item.value)
                      }
                      dropDownMaxHeight={150}
                      dropDownStyle={{
                        width: switchMerchant
                          ? windowWidth * 0.3
                          : windowWidth < 1000
                            ? windowWidth * 0.33
                            : windowWidth * 0.35,
                        height: 150,
                        backgroundColor: Colors.fieldtBgColor,
                        borderRadius: 10,
                        borderWidth: 1,
                        textAlign: 'left',
                        zIndex: 2,
                      }}
                    // items={outletCategoryDropdownList}
                    // {
                    // ...(outletCategoryDropdownList.find(o => o.value === selectedOutletCategoryId))
                    // &&
                    // {
                    //     defaultValue: selectedOutletCategoryId,
                    // }
                    // }
                    // // defaultValue={selectedOutletCategoryId}
                    // containerStyle={{ height: 40 }}

                    // //style={{ backgroundColor: '#fafafa', }}
                    // placeholderStyle={{ color: 'black' }}
                    // itemStyle={{
                    //     justifyContent: 'flex-start', marginLeft: 5,
                    // }}
                    // //dropDownBox= {{ zIndex: 10 }}
                    // //dropDownStyle={{ backgroundColor: '#fafafa', height: 250, zIndex: 1000 }}
                    // // dropDownStyle={{
                    // //     zIndex: 1000,
                    // // }}
                    // onChangeItem={(item) =>
                    //     // setState({
                    //     //     categoryId: item.value,
                    //     // })
                    //     setSelectedOutletCategoryId(item.value)
                    // }
                    />
                    {/* ) : (
                      <></>
                    )} */}
                  </View>
                </View>

                {/* Available On */}
                <View style={[styles.inputFieldContainer, { zIndex: -15 }]}>
                  <View style={{ width: '20%', flexDirection: 'row' }}>
                    <Text style={{
                      fontSize: switchMerchant ? 10 : 14,
                      fontFamily: 'NunitoSans-Regular',
                      fontWeight: '500',
                    }}>
                      {!switchMerchant && windowWidth < 1000 ? 'Available\nOn' : 'Available On'}
                    </Text>
                  </View>

                  <View
                    style={[
                      styles.textInput4,
                      {
                        width: switchMerchant
                          ? windowWidth * 0.1
                          : Platform.OS == 'ios'
                            ? windowWidth * 0.12
                            : windowWidth * 0.15,
                        height: switchMerchant ? 35 : 40,
                      },
                      !switchMerchant && windowWidth < 1000 ?
                        {
                          marginLeft: 28,
                          width: windowWidth * 0.11,
                        } : {}
                    ]}>
                    {
                      (
                        selectedEffectiveTypeOptions.every((val) =>
                          EFFECTIVE_DAY_DROPDOWN_LIST1
                            .map((day) => day.value)
                            .includes(val)
                        )
                        ||
                        selectedEffectiveTypeOptions.length === 0
                      )
                        ?
                        <DropDownPicker
                          containerStyle={styles.ddpContainerStyle}
                          arrowColor={'black'}
                          arrowSize={20}
                          arrowStyle={{ fontWeight: 'bold' }}
                          labelStyle={styles.ddpLabelStyle}
                          style={styles.ddpStyle}
                          placeholderStyle={styles.textInputPlaceHolder}
                          items={EFFECTIVE_DAY_DROPDOWN_LIST1}
                          itemStyle={styles.ddpItemStyle}
                          placeholder={'Monday'}
                          multiple
                          multipleText={'%d day(s) selected'}
                          customTickIcon={(press) => (
                            <Ionicon
                              name={'checkmark-outline'}
                              color={
                                !isAvailableDayActive
                                  ? Colors.descriptionColor
                                  : press
                                    ? Colors.fieldtBgColor
                                    : Colors.primaryColor
                              }
                              size={25}
                            />
                          )}
                          onChangeItem={(items) => {
                            setSelectedEffectiveTypeOptions(items);
                          }}
                          defaultValue={selectedEffectiveTypeOptions}
                          dropDownStyle={styles.ddpDDStyle}
                          globalTextStyle={{
                            fontSize: switchMerchant ? 10 : 14,
                          }}
                          disabled={!isAvailableDayActive}
                        />
                        :
                        <></>
                    }
                  </View>

                  <View style={{
                    marginLeft: switchMerchant ? 10 : 10,
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                  }}>
                    <TouchableOpacity
                      disabled={!isAvailableDayActive}
                      onPress={() => {
                        setShowEffectiveStartTimePicker(true);
                        setShowEffectiveEndTimePicker(false);
                      }}
                      style={{
                        marginBottom: 5,
                      }}
                    >
                      <View style={{ flexDirection: 'row' }}>
                        <Text
                          style={{
                            fontFamily: 'NunitoSans-Regular',
                            color: !isAvailableDayActive ? Colors.descriptionColor : Colors.blackColor,
                            fontSize: switchMerchant ? 10 : 14,
                          }}>
                          {moment(effectiveStartTime).format('hh:mm A')}
                        </Text>
                        <MaterialIcons
                          name="keyboard-arrow-down"
                          size={switchMerchant ? 15 : 20}
                          style={{
                            color: !isAvailableDayActive ? Colors.descriptionColor : Colors.blackColor,
                            paddingLeft: !switchMerchant && windowWidth < 1000 ? 2 : 5,
                          }}
                        />
                      </View>
                    </TouchableOpacity>

                    <TouchableOpacity
                      disabled={!isAvailableDayActive}
                      onPress={() => {
                        setShowEffectiveStartTimePicker(false);
                        setShowEffectiveEndTimePicker(true);
                      }}>
                      <View style={{ flexDirection: 'row' }}>
                        <Text
                          style={{
                            // color: Colors.fieldtTxtColor,
                            fontFamily: 'NunitoSans-Regular',
                            color: !isAvailableDayActive ? Colors.descriptionColor : Colors.blackColor,
                            fontSize: switchMerchant ? 10 : 14,
                          }}>
                          {moment(effectiveEndTime).format('hh:mm A')}
                        </Text>
                        <MaterialIcons
                          name="keyboard-arrow-down"
                          size={switchMerchant ? 15 : 20}
                          style={{
                            color: !isAvailableDayActive ? Colors.descriptionColor : Colors.blackColor,
                            paddingLeft: !switchMerchant && windowWidth < 1000 ? 2 : 5,
                          }}
                        />
                      </View>
                    </TouchableOpacity>
                  </View>

                  <View style={{ marginLeft: switchMerchant ? 10 : Platform.OS == 'ios' ? 10 : windowWidth < 1000 ? 5 : 20 }}>
                    <Switch
                      width={42}
                      style={
                        {
                          //flexDirection: 'row',
                          //width: '15%',
                          // marginRight: 20,
                          // marginLeft: 20,
                          //transform: Platform.OS == 'ios' ? [{ scaleX: .6 }, { scaleY: .6 }] : [{ scaleX: .8 }, { scaleY: .8 }]
                          // bottom: -2,
                        }
                      }
                      // disabled={true}
                      value={isAvailableDayActive}
                      circleColorActive={Colors.primaryColor}
                      circleColorInactive={Colors.fieldtTxtColor}
                      backgroundActive="#dddddd"
                      onSyncPress={(value) => {
                        setIsAvailableDayActive(value);
                      }}
                    />
                  </View>
                </View>

                {/* Options */}
                <View style={[styles.inputFieldContainer, { zIndex: -16 }]}>
                  <View style={{ width: '20%' }}>
                    <Text
                      style={{
                        fontSize: switchMerchant ? 10 : 14,
                        fontFamily: 'NunitoSans-Regular',
                        fontWeight: '500',
                      }}>
                      Option
                    </Text>
                  </View>

                  <View
                    style={{
                      width: switchMerchant
                        ? windowWidth * 0.3
                        : windowWidth * 0.35,
                      flexDirection: 'row',
                      alignItems: 'center',
                    }}>
                    <TouchableOpacity
                      onPress={() => {
                        const lastIndex = variantGroupList.length;

                        setVariantGroupList([
                          ...variantGroupList,
                          {
                            groupId: '',
                            choices: [
                              {
                                choiceId: '',
                                choiceName: '',
                                choicePrice: '0.00',
                                choiceStockCount: '0',

                                choiceStockLinkItems: [],
                                choiceStockLinkItemsActive: false,
                              },
                            ],
                            name: '',
                            minSelect: '1',
                            maxSelect: '1',
                            printerAreaList: printerAreaDropdownList.map(area => area.value),
                            isNew: true,
                            hiddenId: uuidv4(),
                          },
                        ]);

                        setVariantGroupIndex(lastIndex);

                        setShowAddOnModal(false);
                        setShowVariantModal(true);
                      }}>

                      <View style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                      }}>
                        <View style={{
                          alignItems: 'center',
                          justifyContent: 'center',
                          width: 18,
                          height: 18,
                          borderRadius: 9,
                          backgroundColor: Colors.primaryColor,
                        }}>
                          <FontAwesome5 name="plus" size={switchMerchant ? 8 : 10} color={'#ffffff'} />
                        </View>

                        <Text style={{
                          marginLeft: switchMerchant ? 5 : 10,
                          color: Colors.primaryColor,
                          fontSize: switchMerchant ? 10 : 14,
                          fontFamily: 'NunitoSans-Regular',
                        }}>
                          Variant
                        </Text>
                      </View>
                    </TouchableOpacity>

                    <TouchableOpacity
                      onPress={() => {
                        const lastIndex = addOnGroupList.length;

                        setAddOnGroupList([
                          ...addOnGroupList,
                          {
                            groupId: '',
                            choices: [
                              {
                                choiceId: '',
                                choiceName: '',
                                choicePrice: '0.00',
                                choiceMinSelect: '0',
                                choiceMaxSelect: '0',
                                choiceStockCount: '0',

                                choiceStockLinkItems: [],
                                choiceStockLinkItemsActive: false,
                              },
                            ],
                            name: '',
                            min: '0',
                            max: '0',
                            isNew: true,
                            hiddenId: uuidv4(),
                          },
                        ]);

                        setAddOnGroupIndex(lastIndex);

                        setShowAddOnModal(true);
                        setShowVariantModal(false);
                      }}>
                      <View
                        style={{
                          flexDirection: 'row',
                          alignItems: 'center',
                          marginLeft: 25,
                        }}>

                        <View style={{
                          alignItems: 'center',
                          justifyContent: 'center',
                          width: 18,
                          height: 18,
                          borderRadius: 9,
                          backgroundColor: Colors.primaryColor,
                        }}>
                          <FontAwesome5
                            name="plus"
                            size={switchMerchant ? 8 : 10}
                            color={'#ffffff'}
                          />
                        </View>
                        <Text
                          style={{
                            marginLeft: switchMerchant ? 5 : 10,
                            color: Colors.primaryColor,
                            fontSize: switchMerchant ? 10 : 14,
                            fontFamily: 'NunitoSans-Regular',
                          }}>
                          Add Ons
                        </Text>
                      </View>
                    </TouchableOpacity>

                    <TouchableOpacity
                      onPress={() => {
                        setShowSharedGroup(true);
                        setTempSelectedSharedAddOnList(outletItemSharedAddOnList);
                      }}
                    >
                      <View style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                        marginLeft: 25,
                      }}>
                        <View style={{
                          alignItems: 'center',
                          justifyContent: 'center',
                          width: 18,
                          height: 18,
                          borderRadius: 9,
                          backgroundColor: Colors.primaryColor,
                        }}>
                          <FontAwesome5 name="plus" size={switchMerchant ? 8 : 10} color={'#ffffff'} />
                        </View>

                        <Text style={{
                          marginLeft: switchMerchant ? 5 : 10,
                          color: Colors.primaryColor,
                          fontSize: switchMerchant ? 10 : 14,
                          fontFamily: 'NunitoSans-Regular',
                        }}>
                          Select from Shared
                        </Text>
                      </View>
                    </TouchableOpacity>
                  </View>
                </View>

                {/* For Variant AddOns Reordering */}
                <View style={{ flex: 1, width: '90%', zIndex: -17 }}>
                  <DraggableFlatList
                    data={sortedVariantsAddOns}
                    renderItem={renderOptionsList}
                    keyExtractor={(item) => item.groupId}
                    onDragEnd={({ data }) => {
                      const updatedSortedVariantsAddOns = data.map((item, index) => ({
                        ...item,
                        orderIndex: index + 1,
                      }));

                      setSortedVariantsAddOns(updatedSortedVariantsAddOns);
                    }}
                  />
                </View>
              </View>
            </View>

            {/* 2022-08-18 - Product and outlet supply item table */}
            <View style={{
              // zIndex: 15000,
              zIndex: -20000,
              width: '100%',
            }}>
              <Text style={{
                alignSelf: 'center',
                fontFamily: 'NunitoSans-Bold',
                marginTop: 20,
                fontSize: switchMerchant ? 15 : 20,
              }}>
                Inventory Link
              </Text>

              <View style={{
                borderColor: '#E5E5E5',
                borderWidth: 1,
                padding: 16,
                borderRadius: 10,
                marginTop: 20,
                marginHorizontal: 25,
              }}>
                <ScrollView horizontal>
                  <View>
                    <Table borderStyle={{ borderWidth: 0.5, borderColor: '#C1C0B9' }}>
                      {
                        <TableWrapper key={'header'} style={{ flexDirection: 'row', backgroundColor: Colors.primaryColor }}>
                          {
                            tableHead.map((cellData, cellIndex) => (
                              <Cell width={cellIndex === 0 ? 60 : 240} key={cellIndex}
                                data={tableElementHeader(cellData, 0, cellIndex)}
                                height={50}
                              // textStyle={{ textAlign: 'center', fontFamily: 'NunitoSans-Bold', fontSize: 16 }}
                              />
                            ))
                          }
                        </TableWrapper>
                        // tableHead.map((rowData, index) => (
                        //   <TableWrapper key={index} style={{ flexDirection: 'row', backgroundColor: Colors.primaryColor }}>
                        //     {
                        //       rowData.map((cellData, cellIndex) => (
                        //         <Cell width={cellIndex === 0 ? 60 : 240} key={cellIndex}
                        //           data={tableElementHead(cellData, index, cellIndex)}
                        //           // textStyle={{ textAlign: 'center', fontFamily: 'NunitoSans-Bold', fontSize: 16 }}
                        //         />
                        //       ))
                        //     }
                        //   </TableWrapper>
                        // ))
                      }
                    </Table>
                    <ScrollView style={{ marginTop: -1 }} contentContainerStyle={{
                      // paddingBottom: 10,
                    }}>
                      <Table borderStyle={{ borderWidth: 0.5, borderColor: '#C1C0B9' }}>
                        {
                          tableData.map((rowData, index) => (
                            (<TableWrapper key={index} style={{ flexDirection: 'row', backgroundColor: Colors.whiteColor }}>
                              {
                                rowData && rowData.length > 0 && rowData.map((cellData, cellIndex) => (
                                  <Cell width={cellIndex === 0 ? 60 : 240} key={cellIndex}
                                    // data={cellIndex === 3 ? tableElement(cellData, index) : cellData}
                                    data={tableElement(cellData, index, cellIndex)}
                                    textStyle={{ margin: 6 }}
                                  />
                                ))
                              }
                            </TableWrapper>)

                            // <Row
                            //   key={index}
                            //   data={rowData}
                            //   widthArr={widthArr}
                            //   style={[
                            //     { height: 40, backgroundColor: '#E7E6E1' },
                            //     index % 2 && { backgroundColor: '#F7F6E7' }
                            //   ]}
                            //   textStyle={{ textAlign: 'center', fontWeight: '100' }}
                            // />
                          ))
                        }
                      </Table>
                    </ScrollView>

                    <Table borderStyle={{ borderWidth: 1, borderColor: '#C1C0B9' }} />
                  </View>
                </ScrollView>
              </View>

              <View style={{
                flexDirection: 'row',
                width: '100%',
                justifyContent: 'flex-start',
                alignItems: 'center',
                paddingHorizontal: 25,
                marginTop: 20,
                marginBottom: 250,
                zIndex: 15000,
              }}>

                {
                  outletSupplyItemDropdownList.length > 0 &&
                    Object.keys(outletSupplyItemsDict).length > 0 &&
                    selectedOutletSupplyItemIdToAdd &&
                    outletSupplyItemsDict[
                    selectedOutletSupplyItemIdToAdd
                    ] ? (

                    <DropDownPicker
                      containerStyle={{
                        height: switchMerchant ? 35 : 40,
                        zIndex: 15000,
                      }}
                      arrowColor={'black'}
                      arrowSize={20}
                      arrowStyle={{
                        fontWeight: 'bold',
                        marginTop: switchMerchant ? 0 : 1,
                      }}
                      labelStyle={{
                        fontFamily: 'NunitoSans-Regular',
                        fontSize: switchMerchant ? 10 : 14,
                      }}
                      style={{
                        width: switchMerchant ? 90 : 200,
                        paddingVertical: 0,
                        backgroundColor: Colors.fieldtBgColor,
                        borderRadius: 10,
                        fontSize: switchMerchant ? 11 : 14,

                        marginRight: 20,
                      }}
                      placeholderStyle={{ color: Colors.fieldtTxtColor }}
                      items={outletSupplyItemDropdownList}
                      itemStyle={{
                        justifyContent: 'flex-start',
                        marginLeft: 5,
                        zIndex: 15000,
                      }}
                      placeholder={'Product To Link'}
                      zIndex={15000}
                      //customTickIcon={(press) => <Ionicon name={"checkbox-outline"} color={press ? Colors.fieldtBgColor : Colors.primaryColor} size={25} />}
                      searchable
                      searchableStyle={{
                        paddingHorizontal: windowWidth * 0.0079,
                      }}
                      //{...(outletSupplyItemDropdownList.find(o => o.value === selectedOutletCategoryId)) && { defaultValue: selectedOutletCategoryId, }}
                      defaultValue={
                        selectedOutletSupplyItemIdToAdd
                          ? selectedOutletSupplyItemIdToAdd
                          : ''
                      }
                      onChangeItem={(item) => {
                        setSelectedOutletSupplyItemIdToAdd(item.value);
                        // setStockLinkItems(
                        //   stockLinkItems.map((stockLinkItem, i) =>
                        //     i === index
                        //       ? {
                        //         ...stockLinkItem,
                        //         outletSupplyItemId: item.value,
                        //         sku: outletSupplyItemsDict[item.value].sku,
                        //         skuMerchant:
                        //           outletSupplyItemsDict[item.value].skuMerchant,
                        //         name: outletSupplyItemsDict[item.value].name,
                        //         unit: outletSupplyItemsDict[item.value].unit,
                        //         quantityUsage: stockLinkItem.quantityUsage,
                        //       }
                        //       : stockLinkItem,
                        //   ),
                        // );
                      }}
                      dropDownMaxHeight={150}
                      dropDownStyle={{
                        width: switchMerchant ? 90 : 200,
                        height: 150,
                        backgroundColor: Colors.fieldtBgColor,
                        borderRadius: 10,
                        borderWidth: 1,
                        textAlign: 'left',
                        zIndex: 15000,
                      }}
                    />
                  )
                    :
                    <></>
                }

                <TouchableOpacity onPress={() => { addSlotV2() }}>
                  <View style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                  }}>
                    <Icon name="plus-circle" size={switchMerchant ? 17 : 20} color={Colors.primaryColor} />

                    <Text style={{
                      marginLeft: 7,
                      color: Colors.primaryColor,
                      fontFamily: 'NunitoSans-Regular',
                      fontSize: switchMerchant ? 10 : 14,
                    }}>
                      Link Inventory
                    </Text>

                  </View>
                </TouchableOpacity>
              </View>
            </View>
          </KeyboardAwareScrollView>
        </View>
      </View>
      {isLoading ? (
        <ModalView
          style={{ flex: 1 }}
          supportedOrientations={['portrait', 'landscape']}
          transparent
          animationType={'slide'}>
          <View style={styles.modalContainer}>
            <ActivityIndicator color={Colors.whiteColor} size={'large'} />
          </View>
        </ModalView>
      ) : <></>}
      {
        exportModal
          ?
          <ModalView
            style={
              {
                // flex: 1
              }
            }
            visible={exportModal}
            supportedOrientations={['portrait', 'landscape']}
            transparent
            animationType={'slide'}>
            <View style={styles.modalContainer}>
              <View style={[styles.modalView, {
                height: Dimensions.get('window').width * 0.2,
                width: Dimensions.get('window').width * 0.3,
                padding: Dimensions.get('window').width * 0.03,

                ...getTransformForModalInsideNavigation(),
              }]}>
                <TouchableOpacity
                  style={styles.closeButton}
                  onPress={() => {
                    // setState({ changeTable: false });
                    setExportModal(false);
                  }}>
                  <AntDesign
                    name="closecircle"
                    size={25}
                    color={Colors.fieldtTxtColor}
                  />
                </TouchableOpacity>
                <View style={styles.modalTitle}>
                  <Text
                    style={[
                      styles.modalTitleText,
                      { fontSize: switchMerchant ? 18 : 20 },
                    ]}>
                    Export Options
                  </Text>
                </View>
                <View
                  style={{
                    alignItems: 'center',
                    top: '10%',
                  }}>
                  <TouchableOpacity
                    style={styles.modalSaveButton}
                    onPress={() => {
                      exportTemplate();
                    }}>
                    <Text
                      style={[
                        styles.modalDescText,
                        { color: Colors.primaryColor },
                      ]}>
                      Export Template
                    </Text>
                  </TouchableOpacity>
                </View>
              </View>
            </View>
          </ModalView>
          :
          <></>
      }
      {
        importModal
          ?
          <ModalView
            supportedOrientations={['portrait', 'landscape']}
            transparent
            animationType={'slide'}>
            <View style={styles.modalContainer}>
              <View
                style={[
                  styles.modalViewImport,
                  {
                    top:
                      Platform.OS === 'ios' && keyboardHeight > 0 ? -keyboardHeight * 0.3 : 0,
                  },
                  {
                    height: Dimensions.get('window').width * 0.6,
                    width: Dimensions.get('window').width * 0.6,
                    borderRadius: Dimensions.get('window').width * 0.03,

                    ...getTransformForModalInsideNavigation(),
                  }
                ]}>
                <TouchableOpacity
                  style={styles.closeButton}
                  onPress={() => {
                    // setState({ changeTable: false });
                    setImportModal(false);
                  }}>
                  <AntDesign
                    name="closecircle"
                    size={25}
                    color={Colors.fieldtTxtColor}
                  />
                </TouchableOpacity>
                <View style={{ padding: 10, margin: 30 }}>
                  <View
                    style={[
                      styles.modalTitle1,
                      { justifyContent: 'center', alignItems: 'center' },
                    ]}>
                    <Text
                      style={[
                        styles.modalTitleText1,
                        { fontSize: 16, fontWeight: '500' },
                      ]}>
                      Imported List
                    </Text>
                  </View>
                  {/* <View style={{
                  heigth: 70,
                  marginVertical: 10,
                  borderWidth: 1,
                  borderColor: '#E5E5E5',
                  height: '80%'
                }}>
                <Table borderStyle={{ borderWidth: 1 }}>
                  <Row data={TableData.tableHead} flexArr={[1, 2, 1, 1]} style={{}}/>
                  <TableWrapper style={{}}>
                  <Col data={TableData.tableTitle} style={{flex: 1}} heightArr={[28, 28, 28, 28]} textStyle={{}}/>
                  <Rows data={TableData.tableData} flexArr={[1, 2, 1, 1]} style={{height: 28}} textStyle={{textAlign: 'center'}}/>
                  </TableWrapper>
                </Table>
                </View> */}
                  <View
                    style={{
                      flexDirection: 'row',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                    }}>
                    <View
                      style={{
                        backgroundColor: Colors.primaryColor,
                        width: 150,
                        height: 40,
                        marginVertical: 15,
                        borderRadius: 5,
                        alignSelf: 'center',
                      }}>
                      <TouchableOpacity
                        onPress={() => {
                          importSelectFile();
                        }}>
                        <Text
                          style={{
                            color: Colors.whiteColor,
                            alignSelf: 'center',
                            marginVertical: 10,
                          }}>
                          IMPORT
                        </Text>
                      </TouchableOpacity>
                    </View>
                    <View style={{ flexDirection: 'row' }}>
                      <View
                        style={{
                          backgroundColor: Colors.whiteColor,
                          width: 150,
                          height: 40,
                          marginVertical: 15,
                          borderRadius: 5,
                          alignSelf: 'center',
                        }}>
                        <TouchableOpacity
                          onPress={() => {
                            setImportTemplate(false);
                          }}>
                          <Text
                            style={{
                              color: Colors.primaryColor,
                              alignSelf: 'center',
                              marginVertical: 10,
                            }}>
                            CANCEL
                          </Text>
                        </TouchableOpacity>
                      </View>
                      <View
                        style={{
                          backgroundColor: Colors.primaryColor,
                          width: 150,
                          height: 40,
                          marginVertical: 15,
                          borderRadius: 5,
                          alignSelf: 'center',
                        }}>
                        <TouchableOpacity onPress={() => { }}>
                          <Text
                            style={{
                              color: Colors.whiteColor,
                              alignSelf: 'center',
                              marginVertical: 10,
                            }}>
                            SAVE
                          </Text>
                        </TouchableOpacity>
                      </View>
                    </View>
                  </View>
                </View>
              </View>
            </View>
          </ModalView>
          :
          <></>
      }
      {/* Variant Group Modal */}
      <ModalView
        style={{ flex: 1 }}
        visible={showVariantModal}
        supportedOrientations={['portrait', 'landscape']}
        transparent
        animationType="slide">
        <View
          style={{
            backgroundColor: 'rgba(0,0,0,0.5)',
            justifyContent: 'center',
            alignItems: 'center',
            width: windowWidth,
            height: windowHeight,
          }}>
          <View
            style={[
              styles.confirmBox1,
              {
                top:
                  Platform.OS === 'ios'
                    ? -keyboardHeight * 0.4
                    : -keyboardHeight * 0.4,
                height: switchMerchant ? '70%' : '60%',
                width: switchMerchant ? '60%' : '80%',

                ...getTransformForModalFullScreen(),
              },
            ]}>
            <TouchableOpacity
              onPress={() => {
                setShowVariantModal(false);

                if (
                  variantGroupList[variantGroupIndex] &&
                  variantGroupList[variantGroupIndex].isNew
                ) {
                  setVariantGroupList([
                    ...variantGroupList.slice(0, variantGroupIndex),
                    ...variantGroupList.slice(variantGroupIndex + 1),
                  ]);
                }
              }}>
              <View
                style={{
                  marginTop: switchMerchant ? 20 : 20,
                  marginRight: switchMerchant ? 10 : 20,
                  alignSelf: 'flex-end',
                  height: 26,
                  width: 26,
                }}>
                <AntDesign
                  name="closecircle"
                  size={switchMerchant ? 15 : 25}
                  color={Colors.fieldtTxtColor}
                />
              </View>
            </TouchableOpacity>
            <View
              style={{
                alignSelf: 'center',
                marginTop: switchMerchant ? 0 : 20,
                justifyContent: 'center',
                alignItems: 'center',
                width: switchMerchant ? '90%' : '80%',
                height: switchMerchant ? '80%' : '80%',
                alignContent: 'center',
              }}>
              <Text
                style={{
                  fontSize: switchMerchant ? 15 : 20,
                  fontFamily: 'NunitoSans-Bold',
                }}>
                Option (Single)
              </Text>

              {/* this */}

              <>
                {variantGroupList[variantGroupIndex] ? (
                  <>
                    <View
                      style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                        marginTop: 10,
                        alignContent: 'space-between',
                        alignItems: 'center',
                        justifyContent: 'center',
                        height: 50,
                        width: '100%',
                      }}>
                      <Text
                        style={{
                          fontSize: switchMerchant ? 10 : 18,
                          fontFamily: 'NunitoSans-SemiBold',
                          marginRight: 15,
                        }}>
                        Title
                      </Text>
                      <TextInput
                        underlineColorAndroid={Colors.fieldtBgColor}
                        style={{
                          backgroundColor: Colors.fieldtBgColor,
                          width: switchMerchant ? 120 : 140,
                          height: switchMerchant ? 35 : 40,
                          borderRadius: 5,
                          padding: 5,
                          marginVertical: 5,
                          borderWidth: 1,
                          borderColor: '#E5E5E5',
                          paddingLeft: 10,
                          fontSize: switchMerchant ? 10 : 14,
                        }}
                        placeholder="Category"
                        placeholderTextColor={Platform.select({
                          ios: '#a9a9a9',
                        })}
                        //iOS
                        clearTextOnFocus
                        //////////////////////////////////////////////
                        //Android
                        onFocus={() => {
                          setTemp(variantGroupList[variantGroupIndex].name);
                          setVariantGroupList(
                            variantGroupList.map((variantGroup, index) =>
                              index === variantGroupIndex
                                ? {
                                  ...variantGroup,
                                  name: '',
                                }
                                : variantGroup,
                            ),
                          );
                        }}
                        ///////////////////////////////////////////////
                        onEndEditing={() => {
                          if (
                            variantGroupList[variantGroupIndex].name == ''
                          ) {
                            setVariantGroupList(
                              variantGroupList.map((variantGroup, index) =>
                                index === variantGroupIndex
                                  ? {
                                    ...variantGroup,
                                    name: temp,
                                  }
                                  : variantGroup,
                              ),
                            );
                          }
                        }}
                        onChangeText={(text) => {
                          setVariantGroupList(
                            variantGroupList.map((variantGroup, index) =>
                              index === variantGroupIndex
                                ? {
                                  ...variantGroup,
                                  name: text,
                                }
                                : variantGroup,
                            ),
                          );
                        }}
                        value={variantGroupList[variantGroupIndex].name}
                      />

                      <Text
                        style={{
                          fontSize: switchMerchant ? 10 : 18,
                          fontFamily: 'NunitoSans-SemiBold',
                          marginRight: 15,
                          marginLeft: 15,
                        }}>
                        Min Choice
                      </Text>
                      <TextInput
                        underlineColorAndroid={Colors.fieldtBgColor}
                        style={{
                          backgroundColor: Colors.fieldtBgColor,
                          width: switchMerchant ? 30 : 60,
                          height: switchMerchant ? 35 : 40,
                          borderRadius: 5,
                          padding: 5,
                          marginVertical: 5,
                          borderWidth: 1,
                          borderColor: '#E5E5E5',
                          paddingLeft: 10,
                          fontSize: switchMerchant ? 10 : 14,
                        }}
                        placeholder="1"
                        placeholderTextColor={Platform.select({
                          ios: '#a9a9a9',
                        })}
                        //iOS
                        clearTextOnFocus
                        //////////////////////////////////////////////
                        //Android
                        onFocus={() => {
                          setTemp(variantGroupList[variantGroupIndex].minSelect);
                          setVariantGroupList(
                            variantGroupList.map((variantGroup, index) =>
                              index === variantGroupIndex
                                ? {
                                  ...variantGroup,
                                  minSelect: '',
                                }
                                : variantGroup,
                            ),
                          );
                        }}
                        ///////////////////////////////////////////////
                        //When textinput is not selected
                        onEndEditing={() => {
                          if (
                            variantGroupList[variantGroupIndex].minSelect == ''
                          ) {
                            setVariantGroupList(
                              variantGroupList.map((variantGroup, index) =>
                                index === variantGroupIndex
                                  ? {
                                    ...variantGroup,
                                    minSelect: temp.length > 0 ? temp : '',
                                  }
                                  : variantGroup,
                              ),
                            );
                          }
                        }}
                        onChangeText={(text) => {
                          // setState({ options: text });

                          setVariantGroupList(
                            variantGroupList.map((variantGroup, index) =>
                              index === variantGroupIndex
                                ? {
                                  ...variantGroup,
                                  minSelect: parseValidIntegerText(text),
                                }
                                : variantGroup,
                            ),
                          );
                        }}
                        value={variantGroupList[variantGroupIndex].minSelect}
                      //multiline={true}
                      />

                      <Text
                        style={{
                          fontSize: switchMerchant ? 10 : 18,
                          fontFamily: 'NunitoSans-SemiBold',
                          marginRight: 15,
                          marginLeft: 15,
                        }}>
                        Max Choice
                        {/* yes */}
                      </Text>
                      <TextInput
                        underlineColorAndroid={Colors.fieldtBgColor}
                        style={{
                          backgroundColor: Colors.fieldtBgColor,
                          width: switchMerchant ? 30 : 60,
                          height: switchMerchant ? 35 : 40,
                          borderRadius: 5,
                          padding: 5,
                          marginVertical: 5,
                          borderWidth: 1,
                          borderColor: '#E5E5E5',
                          paddingLeft: 10,
                          fontSize: switchMerchant ? 10 : 14,
                        }}
                        placeholder="1"
                        placeholderTextColor={Platform.select({
                          ios: '#a9a9a9',
                        })}
                        //iOS
                        clearTextOnFocus
                        //////////////////////////////////////////////
                        //Android
                        onFocus={() => {
                          setTemp(variantGroupList[variantGroupIndex].maxSelect);
                          setVariantGroupList(
                            variantGroupList.map((variantGroup, index) =>
                              index === variantGroupIndex
                                ? {
                                  ...variantGroup,
                                  maxSelect: '',
                                }
                                : variantGroup,
                            ),
                          );
                        }}
                        ///////////////////////////////////////////////
                        //When textinput is not selected
                        onEndEditing={() => {
                          if (
                            variantGroupList[variantGroupIndex].maxSelect == ''
                          ) {
                            setVariantGroupList(
                              variantGroupList.map((variantGroup, index) =>
                                index === variantGroupIndex
                                  ? {
                                    ...variantGroup,
                                    maxSelect: temp.length > 0 ? temp : '',
                                  }
                                  : variantGroup,
                              ),
                            );
                          }
                        }}
                        onChangeText={(text) => {
                          setVariantGroupList(
                            variantGroupList.map((variantGroup, index) =>
                              index === variantGroupIndex
                                ? {
                                  ...variantGroup,
                                  maxSelect: parseValidIntegerText(text),
                                }
                                : variantGroup,
                            ),
                          );
                        }}
                        value={variantGroupList[variantGroupIndex].maxSelect}
                      />
                      <Text
                        style={{
                          fontSize: switchMerchant ? 10 : 18,
                          fontFamily: 'NunitoSans-SemiBold',
                          marginRight: 5,
                          marginLeft: 15,
                        }}>
                        Auto Select
                      </Text>
                      <Switch
                        width={42}
                        style={{ marginLeft: 5 }}
                        value={variantGroupList[variantGroupIndex].isGroupAutoSelect}
                        onSyncPress={(statusTemp) => {
                          setVariantGroupList(
                            variantGroupList.map((variantGroup, index) =>
                              index === variantGroupIndex
                                ? {
                                  ...variantGroup,
                                  isGroupAutoSelect: statusTemp,
                                }
                                : variantGroup,
                            ),
                          );
                        }}
                        circleColorActive={Colors.primaryColor}
                        circleColorInactive={Colors.fieldtTxtColor}
                        backgroundActive="#dddddd"
                      />
                      {console.log(variantGroupList[variantGroupIndex])}
                      {console.log('stop')}
                    </View>



                    <View
                      style={{
                        marginTop: switchMerchant ? 0 : 10,
                        marginBottom: switchMerchant ? 0 : 10,
                        marginLeft: '80%',
                      }}>
                    </View>

                    <KeyboardAwareScrollView
                      showsVerticalScrollIndicator={false}
                      style={{ width: '100%', height: '50%' }}
                      enableOnAndroid
                      enableAutomaticScroll={
                        Platform.OS === 'ios' ? false : true
                      }
                      extraHeight={
                        variantGroupList[variantGroupIndex] &&
                          variantGroupList[variantGroupIndex].choices
                          ? Platform.OS === 'ios'
                            ? variantGroupList[variantGroupIndex].choices
                              .length * 30
                            : variantGroupList[variantGroupIndex].choices
                              .length * 30
                          : 0
                      }>
                      {renderVariantGroupChoices()}
                    </KeyboardAwareScrollView>

                    <View
                      style={{
                        marginTop: 10,
                        width: '100%',
                        alignItems: 'center',
                      }}>
                      <View>
                        <TouchableOpacity
                          style={{ width: 120 }}
                          onPress={() => {
                            setShowVariantModal(false);

                            setVariantGroupList(
                              variantGroupList.map((variantGroup, index) =>
                                index === variantGroupIndex
                                  ? {
                                    ...variantGroup,
                                    isNew: false,
                                  }
                                  : variantGroup,
                              ),
                            );
                          }}>
                          <View
                            style={{
                              justifyContent: 'center',
                              flexDirection: 'row',
                              borderWidth: 1,
                              borderColor: Colors.primaryColor,
                              backgroundColor: '#0A1F44',
                              borderRadius: 5,
                              width: switchMerchant ? 100 : 120,
                              paddingHorizontal: 10,
                              height: switchMerchant ? 35 : 40,
                              alignItems: 'center',
                              shadowOffset: {
                                width: 0,
                                height: 2,
                              },
                              shadowOpacity: 0.22,
                              shadowRadius: 3.22,
                              elevation: 1,
                              zIndex: -1,
                            }}>
                            <Text
                              style={{
                                color: Colors.whiteColor,
                                fontSize: switchMerchant ? 10 : 16,
                                fontFamily: 'NunitoSans-Bold',
                              }}>
                              {isLoading ? 'LOADING...' : 'SAVE'}
                            </Text>
                          </View>
                        </TouchableOpacity>
                      </View>
                    </View>
                  </>
                ) : (
                  <></>
                )}
              </>
            </View>
          </View>
        </View>
      </ModalView>
      {/* AddOn Group Modal */}
      <ModalView
        style={{ flex: 1 }}
        visible={showAddOnModal}
        supportedOrientations={['portrait', 'landscape']}
        transparent
        animationType="slide">
        <View
          style={{
            backgroundColor: 'rgba(0,0,0,0.5)',
            justifyContent: 'center',
            alignItems: 'center',
            width: windowWidth,
            height: windowHeight,
          }}>
          <View
            style={[
              styles.confirmBox1,
              {
                top:
                  Platform.OS === 'ios'
                    ? -keyboardHeight * 0.3
                    : -keyboardHeight * 0.3,
                height: switchMerchant ? '70%' : '60%',
                width: switchMerchant ? '60%' : '80%',

                ...getTransformForModalFullScreen(),
              },
            ]}>
            <TouchableOpacity
              onPress={() => {
                setShowAddOnModal(false);

                if (
                  addOnGroupList[addOnGroupIndex] &&
                  addOnGroupList[addOnGroupIndex].isNew
                ) {
                  setAddOnGroupList([
                    ...addOnGroupList.slice(0, addOnGroupIndex),
                    ...addOnGroupList.slice(addOnGroupIndex + 1),
                  ]);
                }
              }}>
              <View
                style={{
                  marginTop: 20,
                  marginRight: switchMerchant ? 10 : 20,
                  alignSelf: 'flex-end',
                  height: 26,
                  width: 26,
                }}>
                {/* <Close name="closecircle" size={24} /> */}
                <AntDesign
                  name="closecircle"
                  size={switchMerchant ? 15 : 25}
                  color={Colors.fieldtTxtColor}
                />
              </View>
            </TouchableOpacity>
            <View
              style={{
                alignSelf: 'center',
                marginTop: switchMerchant ? 0 : 5,
                alignItems: 'center',
                width: switchMerchant ? '90%' : '90%',
                height: switchMerchant ? '80%' : '80%',
                alignContent: 'center',
              }}>
              <Text
                style={{
                  fontSize: switchMerchant ? 15 : 20,
                  fontFamily: 'NunitoSans-Bold',
                  marginTop: switchMerchant ? 5 : 20,
                }}>
                Options (Multiple)
              </Text>

              <>
                {addOnGroupList[addOnGroupIndex] ? (
                  <>
                    <View
                      style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                        marginTop: switchMerchant ? 0 : 10,
                        alignItems: 'center',
                        justifyContent: 'center',
                        height: 50,
                        width: switchMerchant ? '100%' : '120%',
                      }}>
                      <Text
                        style={{
                          fontSize: switchMerchant ? 10 : 18,
                          fontFamily: 'NunitoSans-SemiBold',
                          marginRight: 15,
                        }}>
                        Title
                      </Text>
                      <TextInput
                        underlineColorAndroid={Colors.fieldtBgColor}
                        style={{
                          backgroundColor: Colors.fieldtBgColor,
                          width: switchMerchant ? 120 : 140,
                          height: switchMerchant ? 35 : 40,
                          borderRadius: 5,
                          padding: 5,
                          marginVertical: 5,
                          borderWidth: 1,
                          borderColor: '#E5E5E5',
                          paddingLeft: 10,
                          fontSize: switchMerchant ? 10 : 14,
                        }}
                        placeholder="Category"
                        placeholderTextColor={Platform.select({
                          ios: '#a9a9a9',
                        })}
                        //iOS
                        clearTextOnFocus
                        //////////////////////////////////////////////
                        //Android
                        onFocus={() => {
                          setTemp(addOnGroupList[addOnGroupIndex].name);
                          setAddOnGroupList(
                            addOnGroupList.map((addOnGroup, index) =>
                              index === addOnGroupIndex
                                ? {
                                  ...addOnGroup,
                                  name: '',
                                }
                                : addOnGroup,
                            ),
                          );
                        }}
                        ///////////////////////////////////////////////
                        //When textinput is not selected
                        onEndEditing={() => {
                          if (addOnGroupList[addOnGroupIndex].name == '') {
                            setAddOnGroupList(
                              addOnGroupList.map((addOnGroup, index) =>
                                index === addOnGroupIndex
                                  ? {
                                    ...addOnGroup,
                                    name: temp,
                                  }
                                  : addOnGroup,
                              ),
                            );
                          }
                        }}
                        onChangeText={(text) => {
                          // setState({ options: text });

                          setAddOnGroupList(
                            addOnGroupList.map((addOnGroup, index) =>
                              index === addOnGroupIndex
                                ? {
                                  ...addOnGroup,
                                  name: text,
                                }
                                : addOnGroup,
                            ),
                          );
                        }}
                        value={addOnGroupList[addOnGroupIndex].name}
                      />

                      <Text
                        style={{
                          fontSize: switchMerchant ? 10 : 18,
                          fontFamily: 'NunitoSans-SemiBold',
                          marginRight: 15,
                          marginLeft: 15,
                        }}>
                        Min Choice
                      </Text>
                      <TextInput
                        underlineColorAndroid={Colors.fieldtBgColor}
                        style={{
                          backgroundColor: Colors.fieldtBgColor,
                          width: switchMerchant ? 30 : 60,
                          height: switchMerchant ? 35 : 40,
                          borderRadius: 5,
                          padding: 5,
                          marginVertical: 5,
                          borderWidth: 1,
                          borderColor: '#E5E5E5',
                          paddingLeft: 10,
                          fontSize: switchMerchant ? 10 : 14,
                        }}
                        placeholder="1"
                        placeholderTextColor={Platform.select({
                          ios: '#a9a9a9',
                        })}
                        //iOS
                        clearTextOnFocus
                        //////////////////////////////////////////////
                        //Android
                        onFocus={() => {
                          setTemp(addOnGroupList[addOnGroupIndex].min);
                          setAddOnGroupList(
                            addOnGroupList.map((addOnGroup, index) =>
                              index === addOnGroupIndex
                                ? {
                                  ...addOnGroup,
                                  min: '',
                                }
                                : addOnGroup,
                            ),
                          );
                        }}
                        ///////////////////////////////////////////////
                        //When textinput is not selected
                        onEndEditing={() => {
                          if (
                            addOnGroupList[addOnGroupIndex].min == ''
                          ) {
                            setAddOnGroupList(
                              addOnGroupList.map((addOnGroup, index) =>
                                index === addOnGroupIndex
                                  ? {
                                    ...addOnGroup,
                                    min: temp.length > 0 ? temp : '',
                                  }
                                  : addOnGroup,
                              ),
                            );
                          }
                        }}
                        onChangeText={(text) => {
                          // setState({ options: text });

                          setAddOnGroupList(
                            addOnGroupList.map((addOnGroup, index) =>
                              index === addOnGroupIndex
                                ? {
                                  ...addOnGroup,
                                  min: parseValidIntegerText(text),
                                }
                                : addOnGroup,
                            ),
                          );
                        }}
                        value={addOnGroupList[addOnGroupIndex].min}
                      //multiline={true}
                      />

                      <Text
                        style={{
                          fontSize: switchMerchant ? 10 : 18,
                          fontFamily: 'NunitoSans-SemiBold',
                          marginRight: 15,
                          marginLeft: 15,
                        }}>
                        Max Choice
                        {/* yes */}
                      </Text>
                      <TextInput
                        underlineColorAndroid={Colors.fieldtBgColor}
                        style={{
                          backgroundColor: Colors.fieldtBgColor,
                          width: switchMerchant ? 30 : 60,
                          height: switchMerchant ? 35 : 40,
                          borderRadius: 5,
                          padding: 5,
                          marginVertical: 5,
                          borderWidth: 1,
                          borderColor: '#E5E5E5',
                          paddingLeft: 10,
                          fontSize: switchMerchant ? 10 : 14,
                        }}
                        placeholder="1"
                        placeholderTextColor={Platform.select({
                          ios: '#a9a9a9',
                        })}
                        //iOS
                        clearTextOnFocus
                        //////////////////////////////////////////////
                        //Android
                        onFocus={() => {
                          setTemp(addOnGroupList[addOnGroupIndex].max);
                          setAddOnGroupList(
                            addOnGroupList.map((addOnGroup, index) =>
                              index === addOnGroupIndex
                                ? {
                                  ...addOnGroup,
                                  max: '',
                                }
                                : addOnGroup,
                            ),
                          );
                        }}
                        ///////////////////////////////////////////////
                        //When textinput is not selected
                        onEndEditing={() => {
                          if (
                            addOnGroupList[addOnGroupIndex].max == ''
                          ) {
                            setAddOnGroupList(
                              addOnGroupList.map((addOnGroup, index) =>
                                index === addOnGroupIndex
                                  ? {
                                    ...addOnGroup,
                                    max: temp.length > 0 ? temp : '',
                                  }
                                  : addOnGroup,
                              ),
                            );
                          }
                        }}
                        onChangeText={(text) => {
                          setAddOnGroupList(
                            addOnGroupList.map((addOnGroup, index) =>
                              index === addOnGroupIndex
                                ? {
                                  ...addOnGroup,
                                  max: parseValidIntegerText(text),
                                }
                                : addOnGroup,
                            ),
                          );
                        }}
                        value={addOnGroupList[addOnGroupIndex].max}
                      />

                      <Text
                        style={{
                          fontSize: switchMerchant ? 10 : 18,
                          fontFamily: 'NunitoSans-SemiBold',
                          marginRight: 5,
                          marginLeft: 15,
                        }}>
                        Auto Select
                      </Text>
                      <Switch
                        width={42}
                        style={{ marginLeft: 5 }}
                        value={addOnGroupList[addOnGroupIndex].isGroupAutoSelect}
                        onSyncPress={(statusTemp) => {
                          // Alert.alert('statusTemp: ' + addOnGroupList[addOnGroupIndex].isGroupAutoSelect);
                          setAddOnGroupList(
                            addOnGroupList.map((addOnGroup, index) =>
                              index === addOnGroupIndex
                                ? {
                                  ...addOnGroup,
                                  isGroupAutoSelect: statusTemp,
                                }
                                : addOnGroup,
                            ),
                          );
                        }}
                        circleColorActive={Colors.primaryColor}
                        circleColorInactive={Colors.fieldtTxtColor}
                        backgroundActive="#dddddd"
                      />

                    </View>
                    <View />

                    <KeyboardAwareScrollView
                      showsVerticalScrollIndicator={false}
                      style={{ width: '100%', height: '50%' }}
                      enableOnAndroid
                      enableAutomaticScroll={
                        Platform.OS === 'ios' ? false : true
                      }
                      extraHeight={
                        addOnGroupList[addOnGroupIndex] &&
                          addOnGroupList[addOnGroupIndex].choices
                          ? Platform.OS === 'ios'
                            ? addOnGroupList[addOnGroupIndex].choices.length *
                            30
                            : addOnGroupList[addOnGroupIndex].choices.length *
                            30
                          : 0
                      }>
                      {renderAddOnGroupChoices()}
                    </KeyboardAwareScrollView>

                    <View
                      style={{
                        marginTop: 10,
                        width: '100%',
                        alignItems: 'center',
                      }}>
                      <View>
                        <TouchableOpacity
                          style={{ width: 120 }}
                          onPress={() => {
                            setShowAddOnModal(false);

                            setAddOnGroupList(
                              addOnGroupList.map((addOnGroup, index) =>
                                index === addOnGroupIndex
                                  ? {
                                    ...addOnGroup,
                                    isNew: false,
                                  }
                                  : addOnGroup,
                              ),
                            );
                          }}>
                          <View
                            style={{
                              justifyContent: 'center',
                              flexDirection: 'row',
                              borderWidth: 1,
                              borderColor: Colors.primaryColor,
                              backgroundColor: '#0A1F44',
                              borderRadius: 5,
                              width: switchMerchant ? 100 : 120,
                              paddingHorizontal: 10,
                              height: switchMerchant ? 35 : 40,
                              alignItems: 'center',
                              shadowOffset: {
                                width: 0,
                                height: 2,
                              },
                              shadowOpacity: 0.22,
                              shadowRadius: 3.22,
                              elevation: 1,
                              zIndex: -1,
                            }}>
                            <Text
                              style={{
                                color: Colors.whiteColor,
                                //marginLeft: 5,
                                fontSize: switchMerchant ? 10 : 16,
                                fontFamily: 'NunitoSans-Bold',
                              }}>
                              {isLoading ? 'LOADING...' : 'SAVE'}
                            </Text>
                          </View>
                        </TouchableOpacity>
                      </View>
                    </View>
                  </>
                ) : (
                  <></>
                )}
              </>
            </View>
          </View>
        </View>
      </ModalView>
      {/* Shared Group Modal */}
      <ModalView
        style={{ flex: 1 }}
        visible={showSharedModal}
        supportedOrientations={['portrait', 'landscape']}
        animationType="slide"
        transparent
      >
        <View style={{
          backgroundColor: 'rgba(0,0,0,0.5)',
          justifyContent: 'center',
          alignItems: 'center',
          width: windowWidth,
          height: windowHeight,
        }}>
          <View style={[
            styles.confirmBox1,
            {
              top: -keyboardHeight * 0.3,
              height: switchMerchant ? '70%' : '60%',
              width: switchMerchant ? '60%' : '60%',
              justifyContent: 'center',
              alignItems: 'center',
              ...getTransformForModalFullScreen(),
            },
          ]}>

            {/* Close Button */}
            <View style={{ width: '100%' }}>
              <TouchableOpacity onPress={() => { setShowSharedGroup(false); }}>
                <View style={{
                  alignSelf: 'flex-end',
                  marginRight: switchMerchant ? 10 : 20,
                  height: 26,
                  width: 26,
                }}>
                  <AntDesign
                    name="closecircle"
                    size={switchMerchant ? 15 : 25}
                    color={Colors.fieldtTxtColor}
                  />
                </View>
              </TouchableOpacity>
            </View>

            <View style={{
              justifyContent: 'center',
              alignItems: 'center',
              width: switchMerchant ? '90%' : '90%',
              height: switchMerchant ? '80%' : '80%',
            }}>

              <Text
                style={{
                  fontSize: switchMerchant ? 15 : 20,
                  fontFamily: 'NunitoSans-Bold',
                }}>
                Shared Variants / Add-Ons
              </Text>

              <>
                <View style={{ width: '100%' }}>
                  <View style={{ height: '90%', width: '100%', paddingVertical: 10 }}>
                    {sharedVariantAddOnList.length > 0
                      ?
                      <FlatList
                        data={sharedVariantAddOnList}
                        renderItem={renderSharedVariantAddOnList}
                        keyExtractor={(item, index) => String(index)}
                      />
                      :
                      <Text style={{
                        width: '100%',
                        textAlign: 'left',
                        fontSize: 14
                      }}>
                        No shared variants & add-ons yet
                      </Text>
                    }
                  </View>
                </View>
              </>

              {/* Save Button */}
              <View style={{
                width: '100%',
                justifyContent: 'center',
                alignItems: 'center',
              }}>
                <TouchableOpacity
                  onPress={() => {
                    handleSaveSharedVariantAddOnAction();
                  }}
                  style={{
                    width: switchMerchant ? 100 : 120,
                    height: switchMerchant ? 35 : 40,
                    justifyContent: 'center',
                    alignItems: 'center',
                    backgroundColor: Colors.primaryColor,
                    borderRadius: 5,
                    paddingHorizontal: 10,
                    shadowOffset: {
                      width: 0,
                      height: 2,
                    },
                    shadowOpacity: 0.22,
                    shadowRadius: 3.22,
                    elevation: 1,
                    zIndex: -1,
                  }}
                >
                  <Text style={{
                    color: Colors.whiteColor,
                    fontSize: switchMerchant ? 10 : 16,
                    fontFamily: 'NunitoSans-Bold',
                  }}>
                    {sharedVariantAddOnList ? "Save" : 'Close'}
                  </Text>
                </TouchableOpacity>
              </View>

            </View>
          </View>
        </View>
      </ModalView>
      {/* PrinterArea Modal */}
      <ModalView
        style={{ flex: 1 }}
        visible={showPrinterAreaModal}
        supportedOrientations={['portrait', 'landscape']}
        animationType="slide"
        transparent
      >
        <View style={{
          backgroundColor: 'rgba(0,0,0,0.5)',
          justifyContent: 'center',
          alignItems: 'center',
          width: windowWidth,
          height: windowHeight,
        }}>
          <View style={[
            styles.confirmBox1,
            {
              top: -keyboardHeight * 0.3,
              height: switchMerchant ? '70%' : '60%',
              width: switchMerchant ? '60%' : '60%',
              justifyContent: 'center',
              alignItems: 'center',
              ...getTransformForModalFullScreen(),
            },
          ]}>

            {/* Close Button */}
            <View style={{ width: '100%' }}>
              <TouchableOpacity onPress={() => { setShowPrinterAreaModal(false) }}>
                <View style={{
                  alignSelf: 'flex-end',
                  marginRight: switchMerchant ? 10 : 20,
                  height: 26,
                  width: 26,
                }}>
                  <AntDesign
                    name="closecircle"
                    size={switchMerchant ? 15 : 25}
                    color={Colors.fieldtTxtColor}
                  />
                </View>
              </TouchableOpacity>
            </View>

            <View style={{
              justifyContent: 'center',
              alignItems: 'center',
              width: switchMerchant ? '90%' : '90%',
              height: switchMerchant ? '80%' : '80%',
            }}>

              <Text
                style={{
                  fontSize: switchMerchant ? 15 : 20,
                  fontFamily: 'NunitoSans-Bold',
                }}>
                Select Printer Area
              </Text>

              <>
                <View style={{ width: '100%' }}>
                  <View style={{ height: '90%', width: '100%', paddingVertical: 10 }}>
                    {printerAreaDropdownList.length > 0
                      ?
                      <FlatList
                        data={printerAreaDropdownList}
                        renderItem={renderPrinterAreaList}
                        keyExtractor={(item, index) => String(index)}
                      />
                      :
                      <Text style={{
                        width: '100%',
                        textAlign: 'left',
                        fontSize: 14
                      }}>
                        No available printer area yet
                      </Text>
                    }
                  </View>
                </View>
              </>

              {/* Save Button */}
              <View style={{
                width: '100%',
                justifyContent: 'center',
                alignItems: 'center',
              }}>
                <TouchableOpacity
                  onPress={() => {
                    setShowPrinterAreaModal(false);
                  }}
                  style={{
                    width: switchMerchant ? 100 : 120,
                    height: switchMerchant ? 35 : 40,
                    justifyContent: 'center',
                    alignItems: 'center',
                    backgroundColor: Colors.primaryColor,
                    borderRadius: 5,
                    paddingHorizontal: 10,
                    shadowOffset: {
                      width: 0,
                      height: 2,
                    },
                    shadowOpacity: 0.22,
                    shadowRadius: 3.22,
                    elevation: 1,
                    zIndex: -1,
                  }}
                >
                  <Text style={{
                    color: Colors.whiteColor,
                    fontSize: switchMerchant ? 10 : 16,
                    fontFamily: 'NunitoSans-Bold',
                  }}>
                    {sharedVariantAddOnList ? "Save" : 'Close'}
                  </Text>
                </TouchableOpacity>
              </View>

            </View>
          </View>
        </View>
      </ModalView>
      <DateTimePickerModal
        locale="en_GB"
        isVisible={customTimeSelectStart}
        mode={'datetime'}
        date={
          customStartTime
        }
        onConfirm={(dt) => {
          setCustomTimeSelectStart(false);
          setCustomTimeSelectEnd(true);
          setCustomStartTime(moment(dt).toDate());
        }}
        onCancel={() => {
          setCustomTimeSelectStart(false);
        }}
      />
      <DateTimePickerModal
        locale="en_GB"
        isVisible={customTimeSelectEnd}
        mode={'datetime'}
        date={
          customEndTime
        }
        onConfirm={(dt) => {
          setCustomTimeSelectStart(false);
          setCustomTimeSelectEnd(false);
          setCustomEndTime(moment(dt).toDate());
        }}
        onCancel={() => {
          setCustomTimeSelectEnd(false);
        }}
      />
      {hideModal ? (
        <ModalView
          supportedOrientations={['landscape', 'portrait']}
          style={{ flex: 1 }}
          transparent
          animationType="slide">
          <View style={styles.modalContainer}>
            <View style={{
              ...styles.modalView1,
              ...getTransformForModalInsideNavigation(),
            }}>
              {/* Close Button */}
              <TouchableOpacity
                style={styles.closeButton}
                onPress={() => {
                  setHideModal(false);
                  setSelectedChoice(null);
                }}>
                <AntDesign
                  name="closecircle"
                  size={switchMerchant ? 15 : 25}
                  color={Colors.fieldtTxtColor}
                />
              </TouchableOpacity>

              {/* Title */}
              <Text style={{
                fontWeight: '700',
                fontFamily: 'Nunitosans-Bold',
                fontSize: 30,
              }}>
                {`${selectedProductEdit && selectedProductEdit.name ? selectedProductEdit.name : ''} is Out of Stock`}
              </Text>

              <Text style={{
                fontFamily: 'Nunitosans-Regular',
                fontSize: 14,
                color: 'gray'
              }}>
                Please confirm that <Text style={{ fontWeight: '700' }}>{selectedProductEdit && selectedProductEdit.name ? selectedProductEdit.name : ''}</Text> is out of stock
              </Text>

              <View style={{ marginTop: 20, justifyContent: 'flex-start', width: '100%', flexDirection: 'row' }}>
                <Text
                  style={{
                    //fontWeight: '700',
                    fontFamily: 'Nunitosans-Regular',
                    fontSize: 16,
                  }}>
                  Select your preferred time:
                </Text>
                <View style={{ marginLeft: 10, marginTop: 1, }}>
                  <Tooltip
                    isVisible={hideHelp}
                    content={<Text>This category will be hidden from users</Text>}
                    placement="top"
                    onClose={() => setHideHelp(false)}
                  >
                    <TouchableOpacity
                      onPress={() => setHideHelp(true)}
                      style={styles.touchable}>
                      <Icon
                        name="help-circle"
                        size={switchMerchant ? 10 : 20}
                        style={{ color: Colors.primaryColor }}
                      />
                    </TouchableOpacity>
                  </Tooltip>
                </View>
              </View>

              <View style={{
                marginTop: 20,
              }}>
                <RadioForm formHorizontal animation>
                  {/* To create radio buttons, loop through your array of options */}
                  {hideCategoryChoice.map((obj, i) => (
                    <RadioButton labelHorizontal key={i}>
                      <RadioButtonInput
                        obj={obj}
                        //index={i}
                        isSelected={selectedChoice === obj.value}
                        onPress={(item) => {
                          setSelectedChoice(obj.value)

                          if (item === 'CUSTOM_TIME') {
                            setCustomTimeSelectStart(true);
                          }
                        }}
                        borderWidth={1}
                        buttonInnerColor={Colors.primaryColor}
                        buttonOuterColor={'#000'}
                        buttonSize={14}
                        buttonOuterSize={22}
                        buttonStyle={{}}
                        buttonWrapStyle={{ marginLeft: Platform.OS == 'ios' ? 5 : 10 }}
                        style={{ marginTop: 20 }}
                      />
                      <Text style={{
                        fontSize: 14,
                        color: 'black',
                        fontFamily: 'Nunitosans-Regular',
                        marginLeft: 5,
                        marginTop: 1
                      }}>
                        {obj.label}
                      </Text>
                    </RadioButton>
                  ))}
                </RadioForm>
              </View>

              {selectedChoice === 'CUSTOM_TIME' && (
                <View style={{ marginTop: 5 }}>
                  <Text style={{
                    color: 'gray',
                    fontFamily: 'Nunitosans-Regular',
                    fontSize: 14,
                  }}>
                    {`Selected Time: ${moment(customStartTime).format('DD MMM YYYY hh:mm A')} - ${moment(customEndTime).format('DD MMM YYYY hh:mm A')}`}
                  </Text>
                </View>
              )}

              <View style={{ flexDirection: 'row', justifyContent: 'center', marginTop: 20, }}>
                <TouchableOpacity
                  style={{
                    borderRadius: 5,
                    backgroundColor: Colors.primaryColor,
                    height: 35,
                    width: 90,
                    alignItems: 'center',
                    justifyContent: 'center',
                    shadowOffset: {
                      width: 0,
                      height: 2,
                    },
                    shadowOpacity: 0.22,
                    shadowRadius: 3.22,
                    elevation: 1,
                  }}
                  onPress={() => {
                    if (selectedChoice == null) {
                      Alert.alert('Error', 'Please select your preferred time to continue',
                        [{
                          text: 'OK',
                        },
                        {
                          text: 'CANCEL',
                          onPress: () => {
                            setHideModal(false);
                          },
                        },
                        ],
                      )
                    }
                    else {
                      setIsHidden(true);
                      setHideModal(false);
                    }
                  }}>

                  <Text
                    style={{
                      fontSize: 15,
                      color: 'white',
                      fontWeight: '500',
                    }}>
                    CONFIRM
                  </Text>
                </TouchableOpacity>

                <TouchableOpacity
                  style={{
                    borderRadius: 5,
                    backgroundColor: Colors.fieldtBgColor,
                    height: 35,
                    width: 90,
                    alignItems: 'center',
                    justifyContent: 'center',
                    marginLeft: 10,

                    shadowOffset: {
                      width: 0,
                      height: 2,
                    },
                    shadowOpacity: 0.22,
                    shadowRadius: 3.22,
                    elevation: 1,
                  }}
                  onPress={() => {
                    setHideModal(false);
                    setSelectedChoice(null);
                  }}>
                  <Text
                    style={{
                      fontSize: 15,
                      color: 'grey',
                      fontWeight: '500',
                    }}>
                    CANCEL
                  </Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </ModalView>
      ) : <></>}
      {/* Modal for Select Tags */}
      {tagModal ? (
        <View style={{
          flex: 1,
          position: 'absolute',
          shadowColor: '#000',
          shadowOffset: {
            width: 0,
            height: 2,
          },
          shadowOpacity: 0.22,
          shadowRadius: 3.22,
          elevation: 3,
          borderRadius: 8,
          top: '10%',
          left: '10%',
        }}>
          <View style={[
            styles.modalContainer,
            {
              backgroundColor: 'white',
              shadowColor: '#000',
              shadowOffset: {
                width: 0,
                height: 2,
              },
              shadowOpacity: 0.22,
              shadowRadius: 3.22,
              elevation: 3,

              borderWidth: 1,
              borderRadius: 8,
              top:
                Platform.OS === 'ios' && keyboardHeight > 0
                  ? -keyboardHeight * 0.3
                  : 0,
            }
          ]}>
            <View style={[
              styles.tagModalView,
              {
                height: switchMerchant ? 200 : 250,
                width: switchMerchant ? 300 : 415,
                backgroundColor: 'transparent',
              },
            ]}>
              <TouchableOpacity style={styles.closeButton} onPress={() => { setTagModal(false) }}>
                <AntDesign name="closecircle" size={switchMerchant ? 15 : 25} color={Colors.fieldtTxtColor} />
              </TouchableOpacity>

              <View style={{ marginBottom: 2 }}>
                <Text style={{
                  fontWeight: '700',
                  fontSize: switchMerchant ? 10 : 21,
                }}>
                  Tags
                </Text>
              </View>

              <View
                style={{
                  backgroundColor: Colors.fieldtBgColor,
                  height: switchMerchant ? 35 : 40,
                  borderRadius: 5,
                  paddingVertical: 3,
                  paddingLeft: 5,
                  marginVertical: 5,
                  borderWidth: 1,
                  borderColor: '#E5E5E5',
                  flexDirection: 'row',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                  width: '100%',
                }}>
                <View
                  style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    width: '60%',
                    zIndex: 1,
                  }}>
                  <AntDesign
                    name="tags"
                    size={switchMerchant ? 17 : 20}
                    style={{ color: 'black' }}
                  />

                  {
                    (
                      selectedUserTagList.every((val) =>
                        userTagDropdownList
                          .map((tag) => tag.value)
                          .includes(val)
                      )
                      ||
                      selectedUserTagList.length === 0
                    )
                      ?
                      <DropDownPicker
                        style={{
                          height: switchMerchant ? 35 : 35,
                          width: switchMerchant ? 150 : 185,
                          marginLeft: 5,
                          paddingVertical: 0,
                          borderColor: 'darkgreen',
                          fontSize: switchMerchant ? 11 : 14,
                        }}
                        dropDownStyle={{
                          marginLeft: 5,
                          marginTop: 1,
                          borderWidth: 1,
                          width: switchMerchant ? 150 : 185,
                        }}
                        arrowSize={switchMerchant ? 15 : 20}
                        arrowStyle={{
                          paddingVertical: switchMerchant ? 0 : -10,
                          alignSelf: 'center',
                        }}
                        items={userTagDropdownList}
                        placeholder={'Select tag(s)'}
                        multipleText={'%d tag(s) selected'}
                        labelStyle={{ fontSize: switchMerchant ? 10 : 14 }}
                        onChangeItem={(items) => {
                          setSelectedUserTagList(items);
                        }}
                        defaultValue={selectedUserTagList}
                        multiple
                        searchable
                        dropDownMaxHeight={130}
                        onSearch={(text) => {
                          setSearchingUserTagText(text);
                        }}
                        customTickIcon={(press) => (
                          <Ionicon
                            name={'checkbox-outline'}
                            color={
                              press ? Colors.fieldtBgColor : Colors.primaryColor
                            }
                            size={switchMerchant ? 17 : 25}
                          />
                        )}
                      />
                      :
                      <></>
                  }
                </View>

                <View
                  style={{
                    width: '30%',
                    flexDirection: 'row',
                    justifyContent: 'flex-end',
                    alignItems: 'center',
                  }}>
                  <TouchableOpacity
                    style={{
                      backgroundColor: Colors.primaryColor,
                      width: 70,
                      height: 35,
                      justifyContent: 'center',
                      alignItems: 'center',
                      borderRadius: 5,
                      flexDirection: 'row',
                    }}
                    disabled={isLoading}
                    onPress={createCRMUserTagOrAddCRMUserTagToProduct}
                  >
                    <Text
                      style={{
                        fontSize: switchMerchant ? 10 : 17,
                        fontWeight: '600',
                        color: 'white',
                      }}>
                      {isLoading ? '' : 'SAVE'}
                    </Text>

                    {isLoading && (
                      <ActivityIndicator color={Colors.whiteColor} size={'small'} />
                    )}
                  </TouchableOpacity>
                </View>
              </View>

              {/* Choosen Tags Start */}
              <View style={{ minHeight: 40, maxHeight: 140, zIndex: -1 }}>
                <ScrollView
                  style={{
                    //marginRight: 12,
                    zIndex: -1,
                    minHeight: 70,
                  }}
                  horizontal
                  showsHorizontalScrollIndicator={false}
                  showsVerticalScrollIndicator={false}>
                  <FlatList
                    data={userTagList}
                    //numRows={2}
                    numColumns={3}
                    renderItem={renderProductTag}
                    keyExtractor={(item, index) => String(index)}
                    style={{
                      paddingVertical: 5,
                      paddingHorizontal: 5,
                    }}
                    //horizontal={true}
                    showsHorizontalScrollIndicator={false}
                  />
                </ScrollView>
              </View>

            </View>
          </View>
        </View>
      ) : <></>}
    </View>)
  );
});

const styles = StyleSheet.create({
  confirmBox: {
    width: '30%',
    height: '30%',
    borderRadius: 30,
    backgroundColor: Colors.whiteColor,
  },
  confirmBox1: {
    borderRadius: 12,
    backgroundColor: Colors.whiteColor,
  },
  container: {
    flex: 1,
    flexDirection: 'row',
  },
  headerLogo: {
    width: 112,
    height: 25,
    marginLeft: 10,
  },
  content: {
    padding: 20,
    width: Dimensions.get('window').width * (1 - Styles.sideBarWidth),
    backgroundColor: Colors.highlightColor,
  },
  content1: {
    backgroundColor: '#ffffff',
    flexDirection: 'row',
    paddingVertical: 20,
    paddingHorizontal: 20,
    borderBottomWidth: StyleSheet.hairlineWidth,
    borderBottomColor: '#c4c4c4',
  },
  content2: {
    flexDirection: 'row',
    paddingVertical: 20,
    paddingHorizontal: 20,
  },
  content3: {
    backgroundColor: Colors.whiteColor,
    width: 40,
    height: 40,
    borderRadius: 20,
    alignSelf: 'flex-end',
    position: 'absolute',
    right: -15,
    marginVertical: -10,
  },
  content4: {
    backgroundColor: Colors.whiteColor,
    width: 120,
    height: 20,
    borderRadius: 20,
    alignSelf: 'center',
    alignItems: 'center',
    marginTop: 10,
  },
  content5: {
    backgroundColor: Colors.whiteColor,
    width: 140,
    height: 140,
    borderRadius: 5,
    marginLeft: 40,
    borderStyle: 'dashed',
    borderWidth: 2,
    alignItems: 'center',
    borderColor: Colors.primaryColor,
  },
  content6: {
    backgroundColor: '#717378',
    width: 40,
    height: 40,
    marginLeft: 20,
    marginVertical: 10,
    borderRadius: 5,
  },
  textFieldInput: {
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 5,
    padding: 5,
    marginVertical: 5,
    borderWidth: 1,
    borderColor: '#E5E5E5',
    paddingLeft: 10,
  },
  textPrice: {
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 5,
    padding: 5,
    marginVertical: 5,
    borderWidth: 1,
    borderColor: '#E5E5E5',
    paddingLeft: 10,
  },
  textInput4: {
    borderRadius: 10,
  },
  confirmMenu: {
    width: '70%',
    height: '70%',
    borderRadius: 30,
    backgroundColor: Colors.whiteColor,
  },
  headerLeftStyle: {
    width: Dimensions.get('window').width * 0.17,
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    flex: 1,
    backgroundColor: Colors.modalBgColor,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 12,
  },
  modalView: {
    height: Dimensions.get('window').width * 0.2,
    width: Dimensions.get('window').width * 0.3,
    backgroundColor: Colors.whiteColor,
    borderRadius: 12,
    padding: Dimensions.get('window').width * 0.03,
    alignItems: 'center',
    justifyContent: 'center',
  },
  tagModalView: {
    backgroundColor: Colors.whiteColor,
    borderRadius: Dimensions.get('window').width * 0.03,
    padding: 20,
    paddingTop: 25,
  },
  modalViewImport: {
    height: Dimensions.get('window').width * 0.6,
    width: Dimensions.get('window').width * 0.6,
    backgroundColor: Colors.whiteColor,
    borderRadius: Dimensions.get('window').width * 0.03,
    padding: 20,
    paddingTop: 25,
  },
  closeButton: {
    position: 'absolute',
    right: Dimensions.get('window').width * 0.02,
    top: Dimensions.get('window').width * 0.02,
    elevation: 1000,
    zIndex: 1000,
  },
  modalTitle: {
    alignItems: 'center',
    top: '20%',
    position: 'absolute',
  },
  modalBody: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  modalTitleText: {
    fontFamily: 'NunitoSans-Bold',
    textAlign: 'center',
  },
  modalDescText: {
    fontFamily: 'NunitoSans-SemiBold',
    fontSize: 18,
    color: Colors.fieldtTxtColor,
  },
  modalBodyText: {
    flex: 1,
    fontFamily: 'NunitoSans-SemiBold',
    fontSize: 25,
    width: '20%',
  },
  modalSaveButton: {
    width: Dimensions.get('window').width * 0.15,
    backgroundColor: Colors.fieldtBgColor,
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 8,
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 3.22,
    elevation: 1,
    marginVertical: 10,
    zIndex: -1,
  },
  modalView1: {
    height: Dimensions.get('window').height * 0.6,
    width: Dimensions.get('window').width * 0.5,
    padding: Dimensions.get('window').width * 0.035,
    paddingTop: Dimensions.get('window').width * 0.05,
    backgroundColor: Colors.whiteColor,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  //////////////////////
  // For React-Native-DropDown-Picker
  // Width auto adjust with flex: 1 in ddpContainerStyle
  ddpStyle: {
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 10,
  },
  ddpContainerStyle: {
    flex: 1,
    height: '80%',
  },
  ddpLabelStyle: {
    fontFamily: 'NunitoSans-Regular',
    fontSize: 14,
  },
  ddpArrowStyle: {
    fontWeight: 'bold'
  },
  ddpItemStyle: {
    marginLeft: 5,
    marginVertical: 2,
    fontSize: 14,
    fontFamily: 'NunitoSans-Regular',
    justifyContent: 'flex-start'
  },
  ddpDDStyle: {
    backgroundColor: Colors.fieldtBgColor,
  },
  //////////////////////
  inputFieldContainer: {
    width: '90%',
    height: Dimensions.get('window').height * 0.075,
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 5,
  },
  inputFieldContainerDescription: {
    width: '90%',
    height: Dimensions.get('window').height * 0.18,
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 5,
  },
  textInputField: {
  },
  textInputPlaceHolder: {
  },
});

export default ProductAddScreen;