# Resolve react_native_pods.rb with node to allow for hoisting
require Pod::Executable.execute_command('node', ['-p',
  'require.resolve(
    "react-native/scripts/react_native_pods.rb",
    {paths: [process.argv[1]]},
  )', __dir__]).strip

platform :ios, 15.5
prepare_react_native_project!

linkage = ENV['USE_FRAMEWORKS']
if linkage != nil
  Pod::UI.puts "Configuring Pod with #{linkage}ally linked Frameworks".green
  use_frameworks! :linkage => linkage.to_sym
end

target 'KooDooMerchant' do
  config = use_native_modules!

  use_frameworks! :linkage => :static

  # right after `use_frameworks! :linkage => :static`
  $RNFirebaseAsStaticFramework = true

  use_react_native!(
    :path => config[:reactNativePath],
    # An absolute path to your application root.
    :app_path => "#{Pod::Config.instance.installation_root}/..",
    :hermes_enabled => true
  )

  pod 'rn-fetch-blob',
    :path => '../node_modules/rn-fetch-blob'
  
  # /////////////////////////////////////////////////////////////

  # Uncomment this line if you're not using auto-linking or if auto-linking causes trouble
  # pod 'WatermelonDB', path: '../node_modules/@nozbe/watermelondb'

  # WatermelonDB dependency, should not be needed on modern React Native
  # (please file an issue if this causes issues for you)
  # pod 'React-jsi', path: '../node_modules/react-native/ReactCommon/jsi', modular_headers: true

  # WatermelonDB dependency
  pod 'simdjson', path: '../node_modules/@nozbe/simdjson', modular_headers: true

  # /////////////////////////////////////////////////////////////

  target 'KooDooMerchantTests' do
    inherit! :complete
    # Pods for testing
  end

  post_install do |installer|
    # https://github.com/facebook/react-native/blob/main/packages/react-native/scripts/react_native_pods.rb#L197-L202
    react_native_post_install(
      installer,
      config[:reactNativePath],
      :mac_catalyst_enabled => false,
      # :ccache_enabled => true
    )
  end
end
