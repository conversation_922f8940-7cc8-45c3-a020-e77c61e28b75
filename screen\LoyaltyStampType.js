import { Text } from "react-native-fast-text";
import React, { Component, useReducer, useState, useRef, useEffect, useCallback } from 'react';
import {
    StyleSheet,
    Image,
    View,
    Alert,
    TouchableOpacity,
    Modal,
    Dimensions,
    TextInput,
    Animated,
    KeyboardAvoidingView,
    Platform,
    useWindowDimensions,
    ActivityIndicator,
} from 'react-native';
import { ModalView } from 'react-native-multiple-modals';
import { ScrollView, FlatList } from "react-native-gesture-handler";
import Colors from '../constant/Colors';
import Styles from '../constant/Styles';
import SideBar from './SideBar';
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as User from '../util/User';
import Icon from 'react-native-vector-icons/Ionicons';
import ApiClient from '../util/ApiClient';
import API from '../constant/API';
import APILocal, { deleteActiveVoucher } from '../util/apiLocalReplacers';
import DropDownPicker from 'react-native-dropdown-picker';
import AIcon from 'react-native-vector-icons/AntDesign';
import Feather from 'react-native-vector-icons/Feather';
import EvilIcons from 'react-native-vector-icons/EvilIcons';
import moment from 'moment';
import DateTimePickerModal from 'react-native-modal-datetime-picker';
import {
    isTablet
} from '../util/common';
import {
    MERCHANT_VOUCHER_CODE_FORMAT,
    MERCHANT_VOUCHER_TYPE,
    SEGMENT_TYPE,
    EXPAND_TAB_TYPE,
} from '../constant/common';
import { CommonStore } from '../store/commonStore';
import { UserStore } from '../store/userStore';
import { MerchantStore } from '../store/merchantStore';
import { OutletStore } from '../store/outletStore';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import RNPickerSelect from 'react-native-picker-select';
import { useKeyboard } from '../hooks';
import { get } from 'react-native/Libraries/Utilities/PixelRatio';
import Entypo from 'react-native-vector-icons/Entypo';
import AntDesign from 'react-native-vector-icons/AntDesign';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import AsyncImage from '../components/asyncImage';
import Switch from 'react-native-switch-pro';
import XLSX from 'xlsx';
import DocumentPicker from 'react-native-document-picker';
import { writeFile, readFile, DocumentDirectoryPath } from '@dr.pogodin/react-native-fs';
import RNFetchBlob from 'rn-fetch-blob';
import { TARGET_USER_GROUP } from '../constant/promotions';
import FontAwesome5 from 'react-native-vector-icons/FontAwesome5';
import FontAwesome from 'react-native-vector-icons/FontAwesome';
import { useFocusEffect } from '@react-navigation/native';
import UserIdleWrapper from '../components/userIdleWrapper';

//////////////////////////////////////////////////////////////////////////////////////////////////////////

const LoyaltyStampType = (props) => {
    const { navigation } = props;

    ///////////////////////////////////////////////////////////

    const [isMounted, setIsMounted] = useState(true);

    useFocusEffect(
        useCallback(() => {
            setIsMounted(true);
            return () => {
                setIsMounted(false);
            };
        }, [])
    );

    ///////////////////////////////////////////////////////////

    const { width: windowWidth, height: windowHeight } = useWindowDimensions();

    //////////////////////////////////////////////////////////////////////////////////////////////////////////

    const RNFS = require('@dr.pogodin/react-native-fs');

    const [keyboardHeight] = useKeyboard();

    const userName = UserStore.useState((s) => s.name);
    const merchantName = MerchantStore.useState((s) => s.name);

    const [list1, setList1] = useState(true);
    const [customerList, setCustomerList] = useState([]);

    const [perPage, setPerPage] = useState(10);
    const [currentPage, setCurrentPage] = useState(1);
    const [pageCount, setPageCount] = useState(0);
    const [page, setPage] = useState(0);

    const [visible, setVisible] = useState(false); //manage Filter modal
    const [controller, setController] = useState({});
    const [switchMerchant, setSwitchMerchant] = useState(isTablet() ? false : true);

    const [exportModal, setExportModal] = useState(false);

    const [expandThreeDots, setExpandThreeDots] = useState({}); //Use to expand the view when three dots are tapped
    const [threeDotsTapped, setThreeDotsTapped] = useState(false); //when the three dots are tapped will become (true)
    const [inSightsModal, setInSightsModal] = useState(false);

    const [targetOutletDropdownList, setTargetOutletDropdownList] = useState([]);
    const [selectedTargetOutletId, setSelectedTargetOutletId] = useState('');

    const [search, setSearch] = useState('');

    const taggableVouchers = OutletStore.useState((s) => s.taggableVouchers);

    const allOutlets = MerchantStore.useState((s) => s.allOutlets);
    const currOutletId = MerchantStore.useState((s) => s.currOutletId);

    const [gaugeValue, setGaugeValue] = useState(0);

    const accumulator = CommonStore.useState((s) => s.accumulator);

    const crmUsers = OutletStore.useState((s) => s.crmUsers);
    const selectedCustomerEdit = CommonStore.useState(
        (s) => s.selectedCustomerEdit,
    );
    const crmUserTags = OutletStore.useState((s) => s.crmUserTags);
    const crmUserTagsDict = OutletStore.useState((s) => s.crmUserTagsDict);
    const outletSelectDropdownView = CommonStore.useState(
        (s) => s.outletSelectDropdownView,
    );
    const [pushPagingToTop, setPushPagingToTop] = useState(false);

    const isAlphaUser = UserStore.useState((s) => s.isAlphaUser);
    const expandTab = CommonStore.useState((s) => s.expandTab);
    const currPageStack = CommonStore.useState((s) => s.currPageStack);
    const loyaltyStampsType = OutletStore.useState((s) => s.loyaltyStampsType);

    const isLoading = CommonStore.useState((s) => s.isLoading);

    useEffect(() => {
        setTargetOutletDropdownList(
            allOutlets.map((outlet) => ({
                label: outlet.name,
                value: outlet.uniqueId,
            })),
        );

        console.log('targetOutletDropdownList');
        console.log(targetOutletDropdownList);

        if (allOutlets.length > 0) {
            setSelectedTargetOutletId(currOutletId);
        }
    }, [allOutlets, currOutletId]);

    useEffect(() => {
        setCurrentPage(1);
        setPageCount(Math.ceil(loyaltyStampsType.length / perPage));

    }, [loyaltyStampsType.length]);


    //////////////////////////////////////////////////////////////////////////////////////////////////////////

    //To remove unwanted sidebar
    // navigation.dangerouslyGetParent().setOptions({
    //   tabBarVisible: false,
    // });

    //Header
    navigation.setOptions({
        headerLeft: () => (
            <TouchableOpacity
                onPress={() => {
                    if (isAlphaUser || true) {
                        navigation.navigate('MenuOrderingScreen');

                        CommonStore.update((s) => {
                            s.currPage = 'MenuOrderingScreen';
                            s.currPageStack = [...currPageStack, 'MenuOrderingScreen'];
                        });
                    }
                    else {
                        navigation.navigate('Table');

                        CommonStore.update((s) => {
                            s.currPage = 'Table';
                            s.currPageStack = [...currPageStack, 'Table'];
                        });
                    }
                    if (expandTab !== EXPAND_TAB_TYPE.OPERATION) {
                        CommonStore.update((s) => {
                            s.expandTab = EXPAND_TAB_TYPE.OPERATION;
                        });
                    }
                }}
                style={styles.headerLeftStyle}>
                <Image
                    style={{
                        width: 124,
                        height: 26,
                    }}
                    resizeMode="contain"
                    source={require('../assets/image/logo.png')}
                />
            </TouchableOpacity>
        ),
        headerTitle: () => (
            <View
                style={[
                    {
                        // justifyContent: Platform.OS === 'android' ? 'center' : 'flex-start',
                        // alignItems: Platform.OS === 'android' ? 'center' : 'flex-start',
                        // marginRight: Platform.OS === 'ios' ? "27%" : 0,
                        // bottom: switchMerchant ? '2%' : 0,
                        ...global.getHeaderTitleStyle(),
                    },
                    // windowWidth >= 768 && switchMerchant
                    //     ? { right: windowWidth * 0.1 }
                    //     : {},
                    // windowWidth <= 768
                    //     ? { right: 20 }
                    //     : {},
                ]}>
                <Text
                    style={{
                        fontSize: switchMerchant ? 20 : 24,
                        // lineHeight: 25,
                        textAlign: 'left',
                        alignItems: 'flex-start',
                        justifyContent: 'flex-start',
                        fontFamily: 'NunitoSans-Bold',
                        color: Colors.whiteColor,
                        opacity: 1,
                        paddingLeft: Platform.OS === 'ios' ? "1%" : 0,
                    }}>
                    Stamp Type List
                </Text>
            </View>
        ),
        headerRight: () => (
            <View
                style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                }}>
                {outletSelectDropdownView()}
                <View
                    style={{
                        backgroundColor: 'white',
                        width: 0.5,
                        height: windowHeight * 0.025,
                        opacity: 0.8,
                        marginHorizontal: 15,
                        bottom: -1,
                    }}></View>
                <TouchableOpacity
                    onPress={() => {
    if (global.currUserRole === 'admin') {
        navigation.navigate('Setting');
    }
}}
                    style={{ flexDirection: 'row', alignItems: 'center' }}>
                    <Text
                        style={[{
                            fontFamily: 'NunitoSans-SemiBold',
                            fontSize: switchMerchant ? 10 : 16,
                            color: Colors.secondaryColor,
                            marginRight: 15,
                        }, switchMerchant ? { width: windowWidth / 8 } : {}]}
                        numberOfLines={switchMerchant ? 1 : 1}
                    >

                        {userName}
                    </Text>
                    <View
                        style={{
                            marginRight: 30,
                            width: windowHeight * 0.05,
                            height: windowHeight * 0.05,
                            borderRadius: windowHeight * 0.05 * 0.5,
                            alignItems: 'center',
                            justifyContent: 'center',
                            backgroundColor: 'white',
                        }}>
                        <Image
                            style={{
                                width: windowHeight * 0.035,
                                height: windowHeight * 0.035,
                                alignSelf: 'center',
                            }}
                            source={require('../assets/image/profile-pic.jpg')}
                        />
                    </View>
                </TouchableOpacity>
            </View>
        ),
    });
    /////////////////////////////////////////////////////////////////////////////////////////
    //Function Start Here

    const nextPage = () => {
        setCurrentPage(currentPage + 1 > pageCount ? currentPage : currentPage + 1);
    };

    const prevPage = () => {
        setCurrentPage(currentPage - 1 < 1 ? currentPage : currentPage - 1);
    };


    const confirmDeletePromotion = () => {
        return Alert.alert('Alert', 'Do you want to remove this promotion?', [
            {
                text: 'YES',
                onPress: () => {
                    deletePromotion(item);
                },
            },
            {
                text: 'NO',
                onPress: () => { },
            },
        ]);
    };

    const deletePromotion = (promotion) => {
        var body = {
            promotionId: promotion.uniqueId,
        };

        ApiClient.POST(API.deletePromotion, body)
            .then((result) => {
                if (result && result.status === 'success') {
                    Alert.alert(
                        'Success',
                        'Promotion has been removed',
                        [
                            {
                                text: 'OK',
                                onPress: () => {
                                    // navigation.goBack();
                                },
                            },
                        ],
                        { cancelable: false },
                    );
                } else {
                    Alert.alert(
                        'Error',
                        'Failed to remove this promotion',
                        [{ text: 'OK', onPress: () => { } }],
                        { cancelable: false },
                    );
                }
            })
            .catch((err) => {
                console.log(err);
            });
    };

    //delete stmap type
    const deleteStampType = (param) => {
        var body = {
            stampId: param,
        }

        if (param) {
            APILocal.deleteStampType({ body: body })
                .then((result) => {
                    if (result && result.status === 'success') {
                        Alert.alert('Success', 'Stamp type has been removed');
                    }
                })
                .catch((err) => {
                    console.log(err);
                });
        }
    };

    //duplicate voucher
    const duplicateTaggableVoucher = (param) => {
        var body = {
            voucherId: param,
        }

        console.log('duplicating voucher ...');
        console.log(param);

        CommonStore.update((s) => {
            s.isLoading = true;
        });

        if (param) {
            APILocal.duplicateTaggableVoucher({ body: body })
                .then((result) => {
                    if (result.status) {
                        setTimeout(() => {
                            CommonStore.update((s) => {
                                s.isLoading = false;
                            });
                        }, 1000);
                        Alert.alert('Success', 'Voucher has been duplicated');
                    }
                })
                .catch((err) => {
                    console.log(err);
                });
        }
    };

    //when press three dots this function will work
    const expandThreeDotsFunc = (param) => {
        if (expandThreeDots[param.uniqueId]) {
            setExpandThreeDots({
                ...expandThreeDots,
                [param.uniqueId]: false,
            });
        } else {
            setExpandThreeDots({
                // ...expandThreeDots,
                [param.uniqueId]: true,
            });
        }
    };

    const renderItem = ({ item, index }) => {
        return (
            <View
                style={{
                    //backgroundColor: (index + 1) % 2 == 0 ? Colors.whiteColor : Colors.highlightColor,
                    zIndex: (index + 1) % 2 == 0 ? -2 : 1,
                    backgroundColor: '#FFFFFF',
                    paddingVertical: 20,
                    //paddingHorizontal: 3,
                    //paddingLeft: 1,
                    borderColor: '#BDBDBD',
                    borderTopWidth: (index + 1) % 2 == 0 ? 0 : 0.5,
                    borderBottomWidth: (index + 1) % 2 == 0 ? 0 : 0.5,
                    borderBottomLeftRadius: 5,
                    borderBottomRightRadius: 5,
                    //height: 105,
                    // width: '100%',
                }}>
                <TouchableOpacity
                    style={{
                        flexDirection: 'row',
                        justifyContent: 'center',
                        alignItems: 'center',
                    }}
                    onPress={() => {
                        CommonStore.update((s) => {
                            s.selectedStampTypeEdit = item;
                        });

                        navigation.navigate('NewLoyaltyStampType');
                    }}>
                    <View
                        style={{
                            flexDirection: 'row',
                            marginTop: 0,
                            marginBottom: 0,
                            alignItems: 'center',
                            width: '100%',
                        }}>
                        <View style={{ width: '15%', left: 20 }}>
                            {item.image ?
                                <AsyncImage
                                    //source={{ uri: logo }}
                                    source={{ uri: item.image }}
                                    // item={item}
                                    hideLoading={true}
                                    style={[
                                        {
                                            width: 75,
                                            height: 75,
                                            boarderWidth: 1,
                                            borderRadius: 4,
                                            backgroundColor: Colors.secondaryColor,
                                        },
                                        switchMerchant
                                            ? {
                                                width: 45,
                                                height: 45,
                                            }
                                            : {},
                                    ]}></AsyncImage>
                                :
                                <View style={[
                                    {
                                        width: 75,
                                        height: 75,
                                        boarderWidth: 1,
                                        borderRadius: 4,
                                        backgroundColor: Colors.secondaryColor,
                                        alignItems: 'center',
                                        justifyContent: 'center',
                                    },
                                    switchMerchant
                                        ? {
                                            width: 45,
                                            height: 45,
                                        }
                                        : {},
                                ]}>
                                    <FontAwesome
                                        name='image'
                                        size={40}
                                    />
                                </View>}
                        </View>

                        <View style={{ width: '77.5%', marginLeft: 3 }}>
                            <Text
                                style={[
                                    {
                                        fontSize: 14,
                                        fontFamily: 'NunitoSans-Regular',
                                        fontWeight: '500',
                                        textAlign: 'left',
                                        color: 'black',
                                    },
                                    switchMerchant
                                        ? {
                                            fontSize: 10,
                                        }
                                        : {},
                                ]}>
                                {item.stampTypeName}
                            </Text>
                        </View>

                        <View style={{ width: '5%', marginLeft: 20 }}>
                            <TouchableOpacity
                                style={{ alignSelf: 'flex-start' }}
                                onPress={() => {
                                    Alert.alert('Alert', 'Do you want to delete this stamp type?', [
                                        {
                                            text: 'YES',
                                            onPress: () => {
                                                deleteStampType(item.uniqueId);
                                            },
                                        },
                                        {
                                            text: 'NO',
                                            onPress: () => { },
                                        },
                                    ]);

                                }}>
                                <Feather
                                    name="trash-2"
                                    size={switchMerchant ? 20 : 25}
                                    color="#eb3446"
                                />
                            </TouchableOpacity>
                        </View>

                        <View>
                            {expandThreeDots[item.uniqueId] == true ? (
                                <View
                                    style={{
                                        //position: 'absolute',
                                        width: 110,
                                        height: 67,
                                        marginLeft: -110,
                                        zIndex: 1,
                                        flexDirection: 'column',
                                        backgroundColor: '#FFFFFF',
                                        borderWidth: 1,
                                        //borderColor: '#E7E7E7',
                                        borderColor: Colors.highlightColor,
                                        borderRadius: 7,
                                        shadowOpacity: 0,
                                        shadowColor: '#000',
                                        shadowOffset: {
                                            height: 2,
                                        },
                                        shadowOpacity: 0.22,
                                        shadowRadius: 3.22,
                                        elevation: 2,
                                    }}>
                                    <TouchableOpacity
                                        style={{
                                            flexDirection: 'row',
                                            height: '49%',
                                            justifyContent: 'center',
                                            alignItems: 'center',
                                        }}
                                        onPress={() => {
                                            CommonStore.update((s) => {
                                                s.selectedStampTypeEdit = item;
                                            });

                                            navigation.navigate('NewLoyaltyStampType');
                                        }}>
                                        <View style={{ width: '30%', paddingLeft: 12 }}>
                                            {/* <MaterialIcons name='edit' size={17} color='darkgreen' /> */}
                                            <FontAwesome5
                                                name="edit"
                                                size={17}
                                                color={Colors.primaryColor}
                                            />
                                        </View>
                                        <View style={{ width: '70%' }}>
                                            <Text style={{ marginLeft: 5 }}>Edit</Text>
                                        </View>
                                    </TouchableOpacity>

                                    <View
                                        style={{ borderWidth: 1, borderColor: Colors.fieldtBgColor }}
                                    />

                                    <TouchableOpacity
                                        style={{
                                            flexDirection: 'row',
                                            height: '49%',
                                            justifyContent: 'center',
                                            alignItems: 'center',
                                        }}
                                        onPress={() => {
                                            confirmDeletePromotion();
                                            //deletePromotion(item);
                                        }}>
                                        <View style={{ width: '30%', paddingLeft: 12 }}>
                                            <MaterialCommunityIcons
                                                name="delete-sweep"
                                                size={17}
                                                color="grey"
                                            />
                                        </View>
                                        <View style={{ width: '70%' }}>
                                            <Text style={{ marginLeft: 5 }}>Delete</Text>
                                        </View>
                                    </TouchableOpacity>

                                    <View
                                        style={{ borderWidth: 1, borderColor: Colors.fieldtBgColor }}
                                    />
                                </View>
                            ) : null}
                        </View>
                    </View>
                </TouchableOpacity>

            </View>
        );
    };

    //////////////////////////////////////////////////////////////////////////////////////
    //Render start here
    return (
        <UserIdleWrapper disabled={!isMounted}>
            <View
                style={[
                    styles.container,
                    !isTablet()
                        ? {
                            transform: [{ scaleX: 1 }, { scaleY: 1 }],
                        }
                        : {},
                ]}>
                {/* <View
                    style={[
                        styles.sidebar,
                        !isTablet() ? {} : {},
                        switchMerchant
                            ? {
                                // width: '10%'
                            }
                            : {},
                    ]}>
                    <SideBar
                        navigation={props.navigation}
                        selectedTab={11}
                        expandPromotions={true}
                    />
                </View> */}
                <ScrollView style={{}}>
                    <KeyboardAvoidingView>
                        <View>
                            <View
                                style={{
                                    flexDirection: 'row',
                                    justifyContent: 'space-between',
                                    marginBottom: 10,
                                    margin: 20,
                                    width: switchMerchant
                                        ? windowWidth * 0.8
                                        : windowWidth * 0.87,
                                    alignSelf: 'center',
                                    alignItems: 'center',
                                }}>
                                <View style={{ justifyContent: 'flex-start' }}>
                                    <Text
                                        style={[
                                            { fontFamily: 'NunitoSans-Bold', fontSize: 26 },
                                            switchMerchant
                                                ? {
                                                    fontSize: 20,
                                                }
                                                : {},
                                        ]}>
                                        Stamp Type List
                                    </Text>
                                </View>

                                <View style={{ flexDirection: 'row', justifyContent: 'flex-end' }}>
                                    <TouchableOpacity
                                        style={[
                                            {
                                                justifyContent: 'center',
                                                flexDirection: 'row',
                                                borderWidth: 1,
                                                borderColor: Colors.primaryColor,
                                                backgroundColor: '#0A1F44',
                                                borderRadius: 5,
                                                //width: 160,
                                                paddingHorizontal: 10,
                                                height: 40,
                                                alignItems: 'center',
                                                shadowOffset: {
                                                    width: 0,
                                                    height: 2,
                                                },
                                                shadowOpacity: 0.22,
                                                shadowRadius: 3.22,
                                                elevation: 1,
                                                zIndex: -1,
                                                marginRight: switchMerchant ? 20 : 15,
                                            },
                                            switchMerchant
                                                ? {
                                                    height: 35,
                                                }
                                                : {},
                                        ]}
                                        onPress={() => {
                                            CommonStore.update((s) => { s.selectedStampTypeEdit = null; });

                                            props.navigation.navigate('NewLoyaltyStampType');
                                        }}>
                                        <AntDesign
                                            name="pluscircle"
                                            size={switchMerchant ? 10 : 20}
                                            style={{ color: Colors.whiteColor }}
                                        />
                                        <Text
                                            style={[
                                                {
                                                    color: Colors.whiteColor,
                                                    marginLeft: 5,
                                                    fontSize: 16,
                                                    fontFamily: 'NunitoSans-Bold',
                                                },
                                                switchMerchant
                                                    ? {
                                                        fontSize: 10,
                                                    }
                                                    : {},
                                            ]}>
                                            {/* ADD stamp type */}
                                            STAMP TYPE
                                        </Text>
                                    </TouchableOpacity>


                                    {/* Type 2 Search Bar */}
                                    <View
                                        style={[
                                            {
                                                width: 250,
                                                height: 40,
                                                backgroundColor: 'white',
                                                borderRadius: 5,
                                                flexDirection: 'row',
                                                alignContent: 'center',
                                                alignItems: 'center',
                                                shadowColor: '#000',
                                                shadowOffset: {
                                                    width: 0,
                                                    height: 2,
                                                },
                                                shadowOpacity: 0.22,
                                                shadowRadius: 3.22,
                                                elevation: 3,
                                                borderWidth: 1,
                                                borderColor: '#E5E5E5',
                                            },
                                            switchMerchant
                                                ? {
                                                    width: 180,
                                                    height: 35,
                                                }
                                                : {},
                                        ]}>
                                        <Icon
                                            name="search"
                                            size={switchMerchant ? 10 : 18}
                                            color={Colors.primaryColor}
                                            style={{ marginLeft: 15 }}
                                        />

                                        <View style={{ flex: 4 }}>
                                            <TextInput
                                                underlineColorAndroid={Colors.whiteColor}
                                                style={[
                                                    {
                                                        width: 220,
                                                        fontSize: 16,
                                                        fontFamily: 'NunitoSans-Regular',
                                                        paddingLeft: 5,
                                                        height: 45,
                                                    },
                                                    switchMerchant
                                                        ? {
                                                            width: 150,
                                                            height: 35,
                                                            fontSize: 10,
                                                        }
                                                        : {},
                                                ]}
                                                clearButtonMode="while-editing"
                                                placeholder=" Search"
                                                placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                                                onChangeText={(text) => {
                                                    setSearch(text);
                                                    // setSearch(text.trim());
                                                }}
                                            // value={search}
                                            />
                                        </View>
                                    </View>
                                </View>
                            </View>
                        </View>

                        <View
                            style={{
                                flexDirection: 'row',
                                justifyContent: 'space-between',
                                //width: windowWidth * 0.87,
                                margin: 10,
                                marginTop: 0,
                                paddingHorizontal: 32,
                                paddingTop: 10,
                            }}>
                        </View>

                        {/* ~~~~~~~~~~~~~~~~~List View Start Here~~~~~~~~~~~~~~~~~~~~~ */}
                        <View
                            style={{
                                backgroundColor: Colors.whiteColor,
                                width: switchMerchant
                                    ? windowWidth * 0.8
                                    : windowWidth * 0.87,
                                height: windowHeight * 0.78,
                                marginTop: 0,
                                marginBottom: 10,
                                marginHorizontal: 20,
                                alignSelf: 'center',
                                //borderRadius: 5,
                                borderBottomLeftRadius: 5,
                                borderBottomRightRadius: 5,
                                shadowOpacity: 0,
                                shadowColor: '#000',
                                shadowOffset: {
                                    width: 0,
                                    height: 2,
                                },
                                shadowOpacity: 0.22,
                                shadowRadius: 3.22,
                                elevation: 3,
                            }}>
                            {/* for testing width and height */}

                            <View style={{ width: '100%', marginTop: 0 }}>
                                <View
                                    style={{
                                        backgroundColor: Colors.whiteColor,
                                        paddingRight: 0,
                                        paddingTop: 0,
                                        height: '100%',
                                        borderRadius: 5,
                                    }}>
                                    {loyaltyStampsType && loyaltyStampsType.length > 0 ?
                                        <View
                                            style={{
                                                flexDirection: 'row',
                                                alignItems: 'center',
                                                paddingVertical: 10,
                                                paddingTop: 20,
                                            }}>
                                            {/* <View style={{ flexDirection: 'row', width: '30%', borderRightWidth: 0, borderRightColor: 'lightgrey', alignItems: 'center', justifyContent: 'flex-start', marginLeft: 0 }}> */}

                                            <View
                                                style={{
                                                    flexDirection: 'row',
                                                    borderRightColor: 'lightgrey',
                                                    width: '15%',
                                                    justifyContent: 'flex-start',
                                                    marginLeft: 0,
                                                }}>
                                                <View style={{ flexDirection: 'column' }}>
                                                    <Text
                                                        style={{
                                                            fontSize: 14,
                                                            color: 'black',
                                                            fontFamily: 'NunitoSans-Bold',
                                                        }}></Text>
                                                </View>
                                            </View>

                                            <View
                                                style={{
                                                    flexDirection: 'row',
                                                    width: '77.5%',
                                                    borderRightColor: 'lightgrey',
                                                    alignItems: 'center',
                                                    justifyContent: 'flex-start',
                                                }}>
                                                <View style={{ flexDirection: 'column' }}>
                                                    <Text
                                                        style={[
                                                            {
                                                                fontSize: 14,
                                                                color: 'black',
                                                                fontFamily: 'NunitoSans-Bold',
                                                            },
                                                            switchMerchant
                                                                ? {
                                                                    fontSize: 10,
                                                                }
                                                                : {},
                                                            windowWidth <= 1823 &&
                                                                windowWidth >= 1820
                                                                ? {
                                                                    marginLeft: 3,
                                                                }
                                                                : {}, windowWidth === 1280 && windowHeight === 800 ? {
                                                                    marginLeft: 3,
                                                                } : {}
                                                        ]}>
                                                        Stamp Type Title
                                                    </Text>
                                                </View>
                                            </View>

                                            <View
                                                style={{
                                                    flexDirection: 'row',
                                                    flex: 0.25,
                                                    borderRightColor: 'lightgrey',
                                                    alignItems: 'center',
                                                    justifyContent: 'flex-start',
                                                }}>
                                                <View style={{ flexDirection: 'column' }}>
                                                    <Text
                                                        style={{
                                                            fontSize: 14,
                                                            color: Colors.fieldtTxtColor,
                                                            fontFamily: 'NunitoSans-Bold',
                                                        }}></Text>
                                                </View>
                                            </View>
                                        </View> : null}

                                    {loyaltyStampsType && loyaltyStampsType.length > 0 ?
                                        <FlatList
                                            nestedScrollEnabled={true}
                                            showsVerticalScrollIndicator={false}
                                            /* data={loyaltyStampTypeList
                                                .filter((item) => {
                                                    if (search !== '') {
                                                        const searchLowerCase = search.toLowerCase();
                                                        if (
                                                            item.stampTypeName
                                                                .toLowerCase()
                                                                .includes(searchLowerCase)
                                                        ) {
                                                            return true;
                                                        }
                                                        return false;
                                                    } else {
                                                        return true;
                                                    }
                                                })
                                                .slice(
                                                    (currentPage - 1) * perPage,
                                                    currentPage * perPage,
                                                )} */
                                            data={loyaltyStampsType}
                                            renderItem={renderItem}
                                            keyExtractor={(item, index) => String(index)}
                                            style={{ marginTop: 10 }}
                                        /> :
                                        <View style={{ justifyContent: 'center', height: '90%', }}>
                                            <View style={{ flexDirection: 'row', justifyContent: 'center' }}>
                                                <Text style={{
                                                    fontSize: 16,
                                                    fontFamily: 'NunitoSans-SemiBold'
                                                }}>There is no record(s) at the moment</Text>
                                            </View>
                                        </View>}
                                </View>
                            </View>
                        </View>
                        {/* ~~~~~~~~~~~~~~~~~List View END Here~~~~~~~~~~~~~~~~~~~~~ */}

                        <View
                            style={{
                                flexDirection: 'row',
                                marginTop: 10,
                                width: switchMerchant
                                    ? windowWidth * 0.8
                                    : windowWidth * 0.87,
                                alignItems: 'center',
                                alignSelf: 'center',
                                justifyContent: 'flex-end',
                                top:
                                    Platform.OS == 'ios'
                                        ? pushPagingToTop && keyboardHeight > 0
                                            ? -keyboardHeight * 0.94
                                            : 0
                                        : 0,

                                borderRadius: pushPagingToTop && keyboardHeight > 0 ? 8 : 0,
                                paddingHorizontal:
                                    pushPagingToTop && keyboardHeight > 0 ? 10 : 0,
                                marginBottom: switchMerchant ? 30 : 15,
                            }}>
                            <Text
                                style={[
                                    {
                                        fontSize: 14,
                                        fontFamily: 'NunitoSans-Bold',
                                        marginRight: '1%',
                                    },
                                    switchMerchant
                                        ? {
                                            fontSize: 10,
                                        }
                                        : {},
                                ]}>
                                Page
                            </Text>
                            <View
                                style={[
                                    {
                                        width: 65,
                                        height: 35,
                                        backgroundColor: Colors.whiteColor,
                                        borderRadius: 10,
                                        justifyContent: 'center',
                                        paddingHorizontal: 22,
                                        borderWidth: 1,
                                        borderColor: '#E5E5E5',
                                    },
                                    switchMerchant
                                        ? {
                                            width: 60,
                                            height: 20,
                                        }
                                        : {},
                                ]}>
                                {console.log('currentPage')}
                                {console.log(currentPage)}

                                <TextInput
                                    onChangeText={(text) => {
                                        var currentPageTemp = text.length > 0 ? parseInt(text) : 1;

                                        setCurrentPage(
                                            currentPageTemp > pageCount
                                                ? pageCount
                                                : currentPageTemp < 1
                                                    ? 1
                                                    : currentPageTemp,
                                        );
                                    }}
                                    placeholder={currentPage.toString()}
                                    placeholderStyle={{
                                        fontSize: switchMerchant ? 10 : 14,
                                        fontFamily: 'NunitoSans-Regular',
                                    }}
                                    placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                                    style={{
                                        color: 'black',
                                        fontSize: switchMerchant ? 10 : 14,
                                        fontFamily: 'NunitoSans-Regular',
                                        marginTop: Platform.OS === 'ios' ? 0 : -15,
                                        marginBottom: Platform.OS === 'ios' ? 0 : -15,
                                        textAlign: 'center',
                                        width: '100%',
                                    }}
                                    value={currentPage.toString()}
                                    defaultValue={currentPage.toString()}
                                    keyboardType={'numeric'}
                                    onFocus={() => {
                                        setPushPagingToTop(true);
                                    }}
                                />
                            </View>
                            <Text
                                style={[
                                    {
                                        fontSize: 14,
                                        fontFamily: 'NunitoSans-Bold',
                                        marginLeft: '1%',
                                        marginRight: '1%',
                                    },
                                    switchMerchant
                                        ? {
                                            fontSize: 10,
                                        }
                                        : {},
                                ]}>
                                of {pageCount}
                            </Text>
                            <TouchableOpacity
                                style={[
                                    {
                                        width: 45,
                                        height: 28,
                                        backgroundColor: Colors.primaryColor,
                                        alignItems: 'center',
                                        justifyContent: 'center',
                                    },
                                    switchMerchant
                                        ? {
                                            width: 30,
                                            height: 20,
                                        }
                                        : {},
                                ]}
                                onPress={() => {
                                    prevPage();
                                }}>
                                <MaterialIcons
                                    name="keyboard-arrow-left"
                                    size={switchMerchant ? 10 : 25}
                                    style={{ color: Colors.whiteColor }}
                                />
                            </TouchableOpacity>
                            <TouchableOpacity
                                style={[
                                    {
                                        width: 45,
                                        height: 28,
                                        backgroundColor: Colors.primaryColor,
                                        alignItems: 'center',
                                        justifyContent: 'center',
                                    },
                                    switchMerchant
                                        ? {
                                            width: 30,
                                            height: 20,
                                        }
                                        : {},
                                ]}
                                onPress={() => {
                                    nextPage();
                                }}>
                                <MaterialIcons
                                    name="keyboard-arrow-right"
                                    size={switchMerchant ? 10 : 25}
                                    style={{ color: Colors.whiteColor }}
                                />
                            </TouchableOpacity>
                        </View>
                        <View style={{ marginTop: 20 }}></View>
                    </KeyboardAvoidingView>
                </ScrollView>
            </View >
        </UserIdleWrapper >
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: Colors.highlightColor,
        flexDirection: 'row',
    },
    list: {
        backgroundColor: Colors.whiteColor,
        width: Dimensions.get('window').width * 0.87,
        height: Dimensions.get('window').height * 0.78,
        marginTop: 0,
        marginBottom: 10,
        marginHorizontal: 20,
        alignSelf: 'center',
        borderRadius: 5,
        shadowOpacity: 0,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.22,
        shadowRadius: 3.22,
        elevation: 3,
    },
    sidebar: {
        //width: windowWidth * Styles.sideBarWidth,
        // shadowColor: '#000',
        // shadowOffset: {
        //     width: 0,
        //     height: 8,
        // },
        // shadowOpacity: 0.44,
        // shadowRadius: 10.32,

        // elevation: 16,
    },
    content: {
        padding: 16,
    },
    headerLeftStyle: {
        width: Dimensions.get('window').width * 0.17,
        justifyContent: 'center',
        alignItems: 'center',
    },
});

export default LoyaltyStampType;
