
import React, { Component, useState, useEffect, useCallback, } from 'react';
import { View, Text, StyleSheet, FlatList, TouchableOpacity, Alert, Modal, Dimensions, TouchableWithoutFeedback } from 'react-native';
//import { FlatList } from 'react-native-gesture-handler';
import Icon from 'react-native-vector-icons/Ionicons';
import API from '../../constant/API';
import ApiClient from '../../util/ApiClient';
import Colors from '../../constant/Colors';
import * as User from '../../util/User';
import CheckBox from '@react-native-community/checkbox';
import { CommonStore } from '../../store/commonStore';
import { ScrollView } from 'react-native-gesture-handler';
import { OutletStore } from '../../store/outletStore';
import { useFocusEffect } from '@react-navigation/native';
import { USER_ORDER_STATUS, RING_TOP_BAR, RING_TOP_BAR_PARSED, RING_TOP_BAR_SORT } from '../../constant/common';
import moment from 'moment'
import { TableStore } from '../../store/tableStore';
const TableBar = props => {
    const { navigation, page, item } = props;
    const viewTableOrderModal = TableStore.useState(s => s.viewTableOrderModal);
    const [isMounted, setIsMounted] = useState(true);

    useFocusEffect(
        useCallback(() => {
            setIsMounted(true);
            return () => {
                setIsMounted(false);
            };
        }, [])
    );
    const [isID, setIsID] = useState(true);
    const [table, setTable] = useState(null);
    const [modalVisible, setModalVisible] = useState(false);
    const [tableData, setTableData] = useState([]);
    const [tableCode, setTableCode] = useState('');

    const setState = () => { };

    const testing = [
        { table: { code: "A1" }, orderItems: { length: 5 } },
        { table: { code: "A1" }, orderItems: { length: 5 } },
        { table: { code: "A1" }, orderItems: { length: 5 } },
        { table: { code: "A1" }, orderItems: { length: 5 } },
        { table: { code: "A1" }, orderItems: { length: 5 } },
    ];

    const outletTables = OutletStore.useState(s => s.outletTables);

    const outletTablesDict = OutletStore.useState(s => s.outletTablesDict);
    const userOrdersTableDict = OutletStore.useState(s => s.userOrdersTableDict);

    const userRings = OutletStore.useState(s => s.userRings);
    const userRingsDict = OutletStore.useState(s => s.userRingsDict);

    //const [outletTableRang, setOutletTableRang] = useState([]);
    const [outletTablesActive, setOutletTablesActive] = useState([]);
    const [selectedOutletTableActive, setSelectedOutletTableActive] = useState({});

    const [selectedOrderItemList, setSelectedOrderItemList] = useState([]);
    const [selectedOrderItemCancelledList, setSelectedOrderItemCancelledList] = useState([]);
    const [selectedOrder, setSelectedOrder] = useState({});

    const [selectedOrderItemCheckDict, setSelectedOrderItemCheckDict] = useState({});
    const [selectedOrderItemCancelledCheckDict, setSelectedOrderItemCancelledCheckDict] = useState({});

    const [refreshRate, setRefreshRate] = useState(new Date());

    useEffect(() => {
        setTimeout(() => {
            setRefreshRate(new Date());
        }, 30000);
    }, [refreshRate]);

    useEffect(() => {
        var tempOutletTablesActive = [];

        for (var i = 0; i < outletTables.length; i++) {
            if (userOrdersTableDict[outletTables[i].uniqueId] !== undefined) {
                var userOrdersItemNum = 0;

                for (var j = 0; j < userOrdersTableDict[outletTables[i].uniqueId].length; j++) {
                    if (userOrdersTableDict[outletTables[i].uniqueId][j].orderStatus === USER_ORDER_STATUS.ORDER_RECEIVED ||
                        userOrdersTableDict[outletTables[i].uniqueId][j].orderStatus === USER_ORDER_STATUS.ORDER_PREPARING ||
                        userOrdersTableDict[outletTables[i].uniqueId][j].orderStatus === USER_ORDER_STATUS.ORDER_DELIVERED ||
                        userOrdersTableDict[outletTables[i].uniqueId][j].orderStatus === USER_ORDER_STATUS.ORDER_AUTHORIZED) {
                        for (var k = 0; k < userOrdersTableDict[outletTables[i].uniqueId][j].cartItems.length; k++) {
                            userOrdersItemNum += userOrdersTableDict[outletTables[i].uniqueId][j].cartItems[k].quantity;
                        }
                    }
                }


                if (userOrdersItemNum > 0) {
                    // push to active table list, if the table had active orders

                    tempOutletTablesActive.push({
                        outletTableId: outletTables[i].uniqueId,
                        userOrdersItemNum: userOrdersItemNum,
                    });
                }
            }
        }

        tempOutletTablesActive.sort((a, b) => (a.userOrdersItemNum > b.userOrdersItemNum) ? -1 : 1);

        setOutletTablesActive(tempOutletTablesActive);

        console.log('tempOutletTablesActive');
        console.log(tempOutletTablesActive);
        console.log('outletTableaaaa');
        console.log(userOrdersTableDict);

        if (selectedOrder && selectedOrder.tableId && selectedOrder.cartItems && selectedOrder.cartItems.length > 0 &&
            userOrdersTableDict[selectedOrder.tableId] && userOrdersTableDict[selectedOrder.tableId].length > 0) {
            // update the check list based on changes from db

            var tempSelectedOrderItemList = [];
            var tempSelectedOrderItemCancelledList = [];

            if (userOrdersTableDict[selectedOrder.tableId] !== undefined) {
                var userOrdersItemNum = 0;

                for (var j = 0; j < userOrdersTableDict[selectedOrder.tableId].length; j++) {
                    for (var k = 0; k < userOrdersTableDict[selectedOrder.tableId][j].cartItems.length; k++) {
                        tempSelectedOrderItemList.push(userOrdersTableDict[selectedOrder.tableId][j].cartItems[k]);
                    }

                    if (userOrdersTableDict[selectedOrder.tableId][j].cartItemsCancelled) {
                        for (var k = 0; k < userOrdersTableDict[selectedOrder.tableId][j].cartItemsCancelled.length; k++) {
                            tempSelectedOrderItemCancelledList.push(userOrdersTableDict[selectedOrder.tableId][j].cartItemsCancelled[k]);
                        }
                    }
                }
            }

            setSelectedOrderItemList(tempSelectedOrderItemList);
            setSelectedOrderItemCancelledList(tempSelectedOrderItemCancelledList);
            setSelectedOrder(userOrdersTableDict[selectedOrder.tableId][0]);
        }
        else {
            setSelectedOrderItemList([]);
            setSelectedOrderItemCancelledList([]);
            setSelectedOrder({});
        }
    }, [outletTables, userOrdersTableDict]);

    // getTableWithDetails();
    // setInterval(() => { getTableWithDetails() }, 5000)

    // if (prevProps.orderTables) {
    //   if (prevProps.orderTables.length !== props.orderTables.length) {
    //     setState({
    //       table: props.orderTables,
    //     });
    //   }
    // }

    const getTableWithDetails = () => {
        ApiClient.GET(API.getCurrentOutletOrder + User.getOutletId()).then(result => {
            setState({ table: result })

        }).catch(err => { setState({ table: testing }) })
    }

    const cancelUserOrder = () => {
        var body = {
            orderItemList: Object.entries(selectedOrderItemCheckDict).map(([key, value]) => value),
            orderId: selectedOrder.uniqueId,
        };

        ApiClient.POST(API.cancelUserOrderItem, body).then(result => {
            if (result && result.status === 'success') {
                Alert.alert('Success', 'Cancel successfully');

                if (result.data.length <= 0) {
                    setModalVisible(false);
                }
            }
            else {
                Alert.alert('Error', 'Failed to cancel');
            }
        }).catch(err => Alert.alert('Error', 'Something went wrong'));
    };

    const checkOrderItem = (item, orderItem) => {
        console.log('check!');

        // var body = {
        //   itemId: item.itemId,
        //   cartItemDate: item.cartItemDate,
        //   orderId: orderItem.uniqueId,
        // };

        // ApiClient.POST(API.orderDeliver, body).then(result => {
        // }).catch(err => Alert('Error', 'Something went wrong'));

        setSelectedOrderItemCancelledCheckDict({});

        setSelectedOrderItemCheckDict({
            ...selectedOrderItemCheckDict,
            [item.itemId + item.cartItemDate.toString()]: {
                itemId: item.itemId,
                cartItemDate: item.cartItemDate,
            },
        });
    }

    const uncheckOrderItem = (item, orderItem) => {
        console.log('uncheck!');

        // var body = {
        //   itemId: item.itemId,
        //   cartItemDate: item.cartItemDate,
        //   orderId: orderItem.uniqueId,
        // };

        // ApiClient.POST(API.orderDeliverUndo, body).then(result => {
        // }).catch(err => Alert('Error', 'Something went wrong'));

        setSelectedOrderItemCheckDict({
            ...selectedOrderItemCheckDict,
            [item.itemId + item.cartItemDate.toString()]: false,
        });
    }

    const undoUserOrderCancelled = () => {
        var body = {
            orderItemList: Object.entries(selectedOrderItemCancelledCheckDict).map(([key, value]) => value),
            orderId: selectedOrder.uniqueId,
        };

        ApiClient.POST(API.undoUserOrderItemCancelled, body).then(result => {
            if (result && result.status === 'success') {
                Alert.alert('Success', 'Undo successfully');

                if (result.data.length <= 0) {
                    setModalVisible(false);
                }
            }
            else {
                Alert.alert('Error', 'Failed to cancel');
            }
        }).catch(err => Alert.alert('Error', 'Something went wrong'));
    };

    const checkOrderItemCancelled = (item, orderItem) => {
        console.log('check!');

        // var body = {
        //   itemId: item.itemId,
        //   cartItemDate: item.cartItemDate,
        //   orderId: orderItem.uniqueId,
        // };

        // ApiClient.POST(API.orderDeliver, body).then(result => {
        // }).catch(err => Alert('Error', 'Something went wrong'));

        setSelectedOrderItemCheckDict({});

        setSelectedOrderItemCancelledCheckDict({
            ...selectedOrderItemCancelledCheckDict,
            [item.itemId + item.cartItemDate.toString()]: {
                itemId: item.itemId,
                cartItemDate: item.cartItemDate,
            },
        });
    }

    const uncheckOrderItemCancelled = (item, orderItem) => {
        console.log('uncheck!');

        // var body = {
        //   itemId: item.itemId,
        //   cartItemDate: item.cartItemDate,
        //   orderId: orderItem.uniqueId,
        // };

        // ApiClient.POST(API.orderDeliverUndo, body).then(result => {
        // }).catch(err => Alert('Error', 'Something went wrong'));

        setSelectedOrderItemCancelledCheckDict({
            ...selectedOrderItemCancelledCheckDict,
            [item.itemId + item.cartItemDate.toString()]: false,
        });
    }

    const increaseCheckOrderItem = (item, orderItem) => {
        console.log('increase!');

        const record = selectedOrderItemCheckDict[item.itemId + item.cartItemDate.toString()];

        var quantity = 0;
        if (record) {
            quantity = (record.quantity + 1 <= item.quantity) ? record.quantity + 1 : record.quantity;
        }
        else {
            quantity = item.quantity;
        }

        setSelectedOrderItemCheckDict({
            ...selectedOrderItemCheckDict,
            [item.itemId + item.cartItemDate.toString()]: {
                itemId: item.itemId,
                cartItemDate: item.cartItemDate,
                quantity: quantity,
            },
        });
    };

    const decreaseCheckOrderItem = (item, orderItem) => {
        console.log('decrease!');

        const record = selectedOrderItemCheckDict[item.itemId + item.cartItemDate.toString()];

        var quantity = 0;
        if (record) {
            quantity = (record.quantity - 1 >= 0) ? record.quantity - 1 : record.quantity;
        }
        else {
            quantity = item.quantity - 1;
        }

        setSelectedOrderItemCheckDict({
            ...selectedOrderItemCheckDict,
            [item.itemId + item.cartItemDate.toString()]: {
                itemId: item.itemId,
                cartItemDate: item.cartItemDate,
                quantity: quantity,
            },
        });
    };

    const calculateColor = (time) => {
        if (time >= 20) {
            return Colors.tabRed;
        }
        else if (time < 20 && time >= 15) {
            return Colors.tabYellow;
        }
        else if (time < 15 || !time) {
            return Colors.tabGrey;
        }
    }

    const renderOrderTop = ({ item }) => {
        {/* const isID = isID;
    let text;
    if (isID === false) {
      text = <Text style={{ fontSize: 20, fontFamily: 'NunitoSans-Bold' }}>{!item.table ? null : item.table.code}</Text>
    } else {
      text = <Text style={{ fontSize: 20, fontFamily: 'NunitoSans-Bold' }}>#{!item.id ? null : item.id}</Text>
    }  */}

        var orderEstimatedTime = 0;
        var orderStatus = '';
        var orderDate = '';



        if (userOrdersTableDict[item.outletTableId] && userOrdersTableDict[item.outletTableId][0]) {
            orderEstimatedTime = ((moment().valueOf() - userOrdersTableDict[item.outletTableId][0].estimatedPreparedDate) / (1000 * 60));
            orderStatus = userOrdersTableDict[item.outletTableId][0].orderStatus;
            orderDate = userOrdersTableDict[item.outletTableId][0].orderDate;

            var hours = orderEstimatedTime / 60;
            var minutes = orderEstimatedTime % 60;

        }

        console.log(orderStatus);


        return (
            <View style={[styles.tableSlotDisplay, {
                backgroundColor: orderEstimatedTime >= 10 && orderEstimatedTime < 20 ? Colors.tabYellow : orderEstimatedTime >= 20 ? Colors.tabRed : Colors.whiteColor,
                //width: outletTablesDict[item.outletTableId].code.length <= 3 ? 60 : outletTablesDict[item.outletTableId].code.length <= 5 ? 90 : outletTablesDict[item.outletTableId].code.length >=8 ? 140 : outletTablesDict[item.outletTableId].code.length >=6 ? 110 : outletTablesDict[item.outletTableId].code.length >=10 ? 150 : 60,
                // ...((outletTablesDict[item.outletTableId].code) && {
                //width: 18 * (outletTablesDict[item.outletTableId].code.length),
                // }),
                minWidth: 60,
                width: Dimensions.get('window').width * 0.25,
                height: Dimensions.get('window').width * 0.25,
                padding: Dimensions.get('window').width * 0.01,
            },
            {
                shadowOpacity: 0,
                shadowColor: '#000',
                shadowOffset: {
                    width: 0,
                    height: 2,
                },
                shadowOpacity: 0.22,
                shadowRadius: 3.22,
                elevation: 3,

            }]}>
                <TouchableOpacity
                    // style={[styles.emptyTableDisplay,
                    //   // need reimplement
                    //   // filtered[0].status != 2 ? { backgroundColor: ((Math.trunc(item.estimateTime)) < 15 && filtered.status != 2) ? Colors.tabGrey : ((Math.trunc(item.estimateTime) >= 15) && (Math.trunc(item.estimateTime) < 20) && filtered.status != 2) ? Colors.tabYellow : Colors.tabRed, padding: 10 } : { backgroundColor: Colors.primaryColor }
                    //   {
                    //       backgroundColor: orderEstimatedTime >= 10 && orderEstimatedTime < 20 ? Colors.tabYellow : (orderEstimatedTime >= 20 ? Colors.tabRed : Colors.primaryColor),
                    //   },
                    //   {
                    //       shadowOpacity: 0,
                    //       shadowColor: '#000',
                    //       shadowOffset: {
                    //           width: 0,
                    //           height: 2,
                    //       },
                    //       shadowOpacity: 0.22,
                    //       shadowRadius: 3.22,
                    //       elevation: 3,
                    //   },
                    //   ]}
                    style={{}}
                    onPress={() => {
                        // setState({
                        //   modalVisible: !modalVisible, 
                        //   tableData: item.orderItems, //remarks: item.remarks 
                        // })
                        item = outletTablesDict[item.outletTableId];
                        TableStore.update(s => {
                            s.viewTableOrderModal = true;
                        });

                        CommonStore.update(s => {

                            s.selectedOutletTable = item;

                            s.isCheckingOutTakeaway = false;
                            s.checkingOutTakeawayOrder = {};

                            s.checkingOutTakeawayTimestamp = Date.now();
                            s.selectedOutletTableId = item.outletTableId;


                        }, () => {
                            navigation.navigate(page, { item }); // Navigate to the page specified by the prop

                        });

                    }}
                >

                    <View style={{
                        alignItems: 'center',
                        paddingTop: 2,
                        paddingHorizontal: 2,
                        // justifyContent: 'space-between',
                    }}>
                        {/* {item.table != null ?
              <Text style={{ fontSize: 20, fontFamily: 'NunitoSans-Bold' }}>{outletTablesDict[item.outletTableId].code}</Text>
              :
              <Text style={{ fontSize: 20, fontFamily: 'NunitoSans-Bold' }}>#{item.id}</Text>} */}
                        <Text style={{
                            fontSize: 20,
                            fontFamily: 'NunitoSans-Bold',
                            marginHorizontal: 10,
                            color: orderEstimatedTime >= 10 && orderEstimatedTime < 20 ? Colors.blackColor : (orderEstimatedTime >= 20 ? Colors.whiteColor : Colors.blackColor),
                        }}>{outletTablesDict[item.outletTableId].code}</Text>

                        <View style={{ flexDirection: 'column', width: '100%', alignItems: 'center' }}>
                            <Text style={{
                                fontSize: 10,
                                fontFamily: 'NunitoSans-Regular',
                                color: orderEstimatedTime >= 10 && orderEstimatedTime < 20 ? Colors.blackColor : (orderEstimatedTime >= 20 ? Colors.whiteColor : Colors.blackColor),
                                // marginLeft: 1,
                            }}>
                                {/* {orderEstimatedTime < 0 ? 0 : `${Math.floor(hours)}hrs:${Math.floor(minutes)}mins`} */}
                                {orderEstimatedTime < 0 ? `0 mins` : orderEstimatedTime < 60 ? `${Math.floor(minutes)}mins` : `${Math.floor(hours)}hrs:${Math.floor(minutes)}mins`}
                            </Text>
                        </View>

                    </View>
                </TouchableOpacity>

                {item.userOrdersItemNum != 0 ?
                    <View style={[styles.smallCircle, { top: -10, right: -10 }]}>
                        <Text style={styles.smallCircleFont}>{item.userOrdersItemNum}</Text>
                    </View>
                    : null}

                <View style={{
                    position: 'absolute',
                    right: 2,
                    bottom: 0,
                }}>
                    <Icon
                        name={'chevron-down'}
                        style={{
                            // color: Colors.primaryColor,
                            color: orderEstimatedTime >= 10 && orderEstimatedTime < 20 ? Colors.primaryColor : (orderEstimatedTime >= 20 ? Colors.whiteColor : Colors.primaryColor),
                            // marginTop: -7,
                            // alignSelf: 'flex-end'
                        }} size={18}
                    />
                </View>
            </View>
        );
    }


    const renderOrderTopRing = ({ item }) => {

        var orderEstimatedTime = 0;
        var orderStatus = '';
        var orderDate = '';



        if (userOrdersTableDict[item.outletTableId] && userOrdersTableDict[item.outletTableId][0]) {
            orderEstimatedTime = ((moment().valueOf() - userOrdersTableDict[item.outletTableId][0].estimatedPreparedDate) / (1000 * 60));
            orderStatus = userOrdersTableDict[item.outletTableId][0].orderStatus;
            orderDate = userOrdersTableDict[item.outletTableId][0].orderDate;

            var hours = orderEstimatedTime / 60;
            var minutes = orderEstimatedTime % 60;

        }

        console.log(orderStatus);


        return (
            <View style={[styles.topFlat, {
                backgroundColor: orderEstimatedTime >= 10 && orderEstimatedTime < 20 ? Colors.tabYellow : orderEstimatedTime >= 20 ? Colors.tabRed : Colors.whiteColor,
                minWidth: 60,
            },
            {
                shadowOpacity: 0,
                shadowColor: '#000',
                shadowOffset: {
                    width: 0,
                    height: 2,
                },
                shadowOpacity: 0.22,
                shadowRadius: 3.22,
                elevation: 3,

            }]}>
                <TouchableOpacity

                    style={{}}
                    onPress={() => {

                        var tempSelectedOrderItemList = [];
                        var tempSelectedOrderItemCancelledList = [];

                        if (userOrdersTableDict[item.outletTableId] !== undefined) {
                            var userOrdersItemNum = 0;

                            for (var j = 0; j < userOrdersTableDict[item.outletTableId].length; j++) {
                                for (var k = 0; k < userOrdersTableDict[item.outletTableId][j].cartItems.length; k++) {
                                    tempSelectedOrderItemList.push(userOrdersTableDict[item.outletTableId][j].cartItems[k]);
                                }

                                if (userOrdersTableDict[item.outletTableId][j].cartItemsCancelled) {
                                    for (var k = 0; k < userOrdersTableDict[item.outletTableId][j].cartItemsCancelled.length; k++) {
                                        tempSelectedOrderItemCancelledList.push(userOrdersTableDict[item.outletTableId][j].cartItemsCancelled[k]);
                                    }
                                }
                            }
                        }

                        setSelectedOrderItemList(tempSelectedOrderItemList);
                        setSelectedOrderItemCancelledList(tempSelectedOrderItemCancelledList);
                        setSelectedOrder(userOrdersTableDict[item.outletTableId][0]);

                        setSelectedOrderItemCheckDict({});
                        setSelectedOrderItemCancelledCheckDict({});

                        setModalVisible(!modalVisible);
                        setSelectedOutletTableActive(item);

                        console.log(userOrdersTableDict[item.outletTableId])
                        console.log(userRingsDict[item.tableId]);
                    }}
                >
                    <View style={{
                        alignItems: 'center',
                        paddingTop: 2,
                        paddingHorizontal: 2,
                        // justifyContent: 'space-between',
                    }}>
                        <Text style={{
                            fontSize: 20,
                            fontFamily: 'NunitoSans-Bold',
                            marginHorizontal: 10,
                            color: orderEstimatedTime >= 10 && orderEstimatedTime < 20 ? Colors.blackColor : (orderEstimatedTime >= 20 ? Colors.whiteColor : Colors.blackColor),
                        }}>
                            {outletTablesDict[item.outletTableId].code}
                        </Text>

                        <View style={{ flexDirection: 'column', width: '100%', alignItems: 'center' }}>
                            <Text style={{
                                fontSize: 10,
                                fontFamily: 'NunitoSans-Regular',
                                color: orderEstimatedTime >= 10 && orderEstimatedTime < 20 ? Colors.blackColor : (orderEstimatedTime >= 20 ? Colors.whiteColor : Colors.blackColor),
                                // marginLeft: 1,
                            }}>
                                {/* {orderEstimatedTime < 0 ? 0 : orderEstimatedTime.toFixed(0)}mins */}
                                {orderEstimatedTime < 0 ? `0 mins` : orderEstimatedTime < 60 ? `${Math.floor(minutes)}mins` : `${Math.floor(hours)}hrs:${Math.floor(minutes)}mins`}
                            </Text>
                        </View>
                    </View>
                </TouchableOpacity>

                {item.userOrdersItemNum != 0 ?
                    <View style={[styles.smallCircle, { top: -10, right: -10 }]}>
                        <Text style={styles.smallCircleFont}>{item.userOrdersItemNum}</Text>
                    </View>
                    : null}

                <View style={{
                    position: 'absolute',
                    right: 2,
                    bottom: 0,
                }}>
                    <Icon
                        name={'chevron-down'}
                        style={{
                            // color: Colors.primaryColor,
                            color: orderEstimatedTime >= 10 && orderEstimatedTime < 20 ? Colors.primaryColor : (orderEstimatedTime >= 20 ? Colors.whiteColor : Colors.primaryColor),
                            // marginTop: -7,
                            // alignSelf: 'flex-end'
                        }} size={18}
                    />
                </View>
            </View>
        );
    }


    const renderOrderItems = (item, orderItem) => {
        // console.log('item');
        // console.log(item);
        // console.log('remarks');
        // console.log(remarks);

        var tablebarFontSize = 15;

        if (Dimensions.get('screen').width <= 1080) {
            tablebarFontSize = 12;
            //console.log(Dimensions.get('screen').width)
        }

        const tablebarTextStyleScale = {
            fontSize: tablebarFontSize,
        };

        return (

            <View style={styles.bottomPart} key={item.uniqueId}>
                <View style={[styles.topPart, { flex: 0.5 },]}>
                    <CheckBox value={selectedOrderItemCheckDict[item.itemId + item.cartItemDate.toString()] !== false && selectedOrderItemCheckDict[item.itemId + item.cartItemDate.toString()] !== undefined} onValueChange={(value) => {
                        if (selectedOrderItemCheckDict[item.itemId + item.cartItemDate.toString()]) {
                            uncheckOrderItem(item, orderItem);
                        }
                        else {
                            checkOrderItem(item, orderItem);
                        }
                    }} />
                </View>
                <View style={[styles.topPart, { flex: 2, justifyContent: 'flex-start' }]}>
                    <Text style={[tablebarTextStyleScale, {
                        textAlign: 'left',
                        marginLeft: 5,
                        fontFamily: 'NunitoSans-SemiBold',
                    }]}>{item.name}
                        {item.remarks && item.remarks.length > 0 ?
                            <Text style={[tablebarTextStyleScale, {
                                fontFamily: 'NunitoSans-Regular',
                                color: 'grey'
                            }]}>{`\n${item.remarks}`}</Text>
                            : <></>
                        }
                    </Text>


                    {/* <Text style={{ fontSize: 15, color: 'grey', flexDirection: 'row' }}>
            <Text>
              {item.remarks}
            </Text>
          </Text> */}
                </View>

                {/* <View style={styles.topPart}>
          <TouchableOpacity
            onPress={() => {
              decreaseCheckOrderItem(item, orderItem);
            }}>
            <View
              style={[
                styles.addBtn,
                { backgroundColor: Colors.descriptionColor },
              ]}
            >
              <Text
                style={{
                  fontSize: 15,                  
                  color: Colors.whiteColor,
                  fontFamily: 'NunitoSans-Bold',
                  top: -1,
                }}
              >
                -
                </Text>
            </View>
          </TouchableOpacity>
          <View
            style={[
              styles.addBtn,
              {
                backgroundColor: Colors.whiteColor,
                borderWidth: StyleSheet.hairlineWidth,
                borderColor: Colors.descriptionColor,
                borderWidth: 1.5,

                width: 25,
              },
            ]}
          >
            <Text
              style={{
                fontSize: 13,                
                color: Colors.primaryColor,
                fontFamily: 'NunitoSans-Bold',
                top: -1,
              }}>              
              {selectedOrderItemCheckDict[item.itemId + item.cartItemDate.toString()] ? selectedOrderItemCheckDict[item.itemId + item.cartItemDate.toString()].quantity : item.quantity}              
            </Text>
          </View>
          <TouchableOpacity
            onPress={() => {

              increaseCheckOrderItem(item, orderItem);
            }}>
            <View
              style={[
                styles.addBtn,
                {
                  backgroundColor: Colors.primaryColor,
                  left: -1,
                },
              ]}
            >
              <Text
                style={{
                  fontSize: 15,
                  color: Colors.whiteColor,
                  fontFamily: 'NunitoSans-Bold',
                  top: -1,
                }}
              >
                +
                </Text>
            </View>
          </TouchableOpacity>
        </View> */}

                <View style={styles.topPart}><Text style={[tablebarTextStyleScale, { fontFamily: 'NunitoSans-SemiBold' }]}>x{item.quantity}</Text></View>

                <View style={styles.topPart}><Text style={[tablebarTextStyleScale, { fontFamily: 'NunitoSans-SemiBold' }]}>RM{item.price}</Text></View>
            </View>

        );
    }

    const renderOrderItemsCancelled = (item, orderItem) => {
        // console.log('item');
        // console.log(item);
        // console.log('remarks');
        // console.log(remarks);

        var tablebarFontSize = 15;

        if (Dimensions.get('screen').width <= 1080) {
            tablebarFontSize = 12;
            //console.log(Dimensions.get('screen').width)
        }

        const tablebarTextStyleScale = {
            fontSize: tablebarFontSize,
        };

        return (

            <View style={styles.bottomPart} key={item.uniqueId}>

                <View style={[styles.topPart, { flex: 0.5 },]}>
                    <CheckBox value={selectedOrderItemCancelledCheckDict[item.itemId + item.cartItemDate.toString()] !== false && selectedOrderItemCancelledCheckDict[item.itemId + item.cartItemDate.toString()] !== undefined} onValueChange={(value) => {
                        if (selectedOrderItemCancelledCheckDict[item.itemId + item.cartItemDate.toString()]) {
                            uncheckOrderItemCancelled(item, orderItem);
                        }
                        else {
                            checkOrderItemCancelled(item, orderItem);
                        }
                    }} />
                </View>
                <View style={[styles.topPart, { flex: 2, justifyContent: 'flex-start' }]}>
                    <Text style={[tablebarTextStyleScale, {
                        textAlign: 'left',
                        marginLeft: 5,
                        fontFamily: 'NunitoSans-SemiBold',
                        textDecorationLine: 'line-through',
                        textDecorationStyle: 'solid',
                    }]}>{item.name}
                        {item.remarks && item.remarks.length > 0 ?
                            <Text style={[tablebarTextStyleScale, {
                                fontFamily: 'NunitoSans-Regular',
                                color: 'grey'
                            }]}>{`\n${item.remarks}`}</Text>
                            : <></>
                        }
                    </Text>
                </View>

                <View style={styles.topPart}><Text style={[tablebarTextStyleScale, {
                    fontFamily: 'NunitoSans-SemiBold',
                    textDecorationLine: 'line-through',
                    textDecorationStyle: 'solid',
                }]}>x{item.quantity}</Text></View>

                <View style={styles.topPart}><Text style={[tablebarTextStyleScale, {
                    fontFamily: 'NunitoSans-SemiBold',
                    textDecorationLine: 'line-through',
                    textDecorationStyle: 'solid',
                }]}>RM{item.price}</Text></View>
            </View>

        );
    }

    return (
        <View style={{}}>
            {page === RING_TOP_BAR.RING_SCREEN ?
                <FlatList
                    style={{ paddingTop: '4%' }}
                    horizontal={true}
                    showsHorizontalScrollIndicator={false}
                    // data={table}
                    //data={userRings}
                    data={outletTablesActive.filter(item => {
                        if (item.uniqueId === userRingsDict.tableId) {
                            console.log('hi');
                            return true;

                        }
                        else {
                            return console.log('bye')
                        }

                    }
                    )}
                    renderItem={renderOrderTopRing}
                    keyExtractor={(item, index) => String(index)}
                    maxToRenderPerBatch={5}
                />
                :

                <FlatList
                    //style={{ paddingTop: '4%' }}
                    style={{}}
                    horizontal={true}
                    showsHorizontalScrollIndicator={false}
                    // data={table}
                    data={outletTablesActive}
                    renderItem={renderOrderTop}
                    keyExtractor={(item, index) => String(index)}
                    maxToRenderPerBatch={5}
                />

            }



            <Modal
                supportedOrientations={['landscape', 'portrait']}
                animationType="slide"
                transparent={true}
                visible={modalVisible}
                onRequestClose={() => {
                    Alert.alert("Modal has been closed.");
                }}
            >
                <TouchableWithoutFeedback
                    onPress={() => { setModalVisible(!modalVisible) }}
                >
                    <View style={styles.centeredView}>
                        <TouchableWithoutFeedback
                            onPress={() => { }}
                        >
                            <View style={styles.modalView}>
                                <View style={styles.modalCloseButton}>
                                    <Icon
                                        name={'close'}
                                        size={22}
                                        onPress={() => setModalVisible(!modalVisible)}
                                    />
                                </View>

                                {outletTablesDict[selectedOrder.tableId] ?
                                    <Text style={{
                                        fontFamily: 'NunitoSans-Bold',
                                        fontSize: 18,
                                        marginBottom: 10,
                                    }}>
                                        {`Table ${outletTablesDict[selectedOrder.tableId].code} Items`}
                                    </Text>
                                    : <></>
                                }

                                <ScrollView
                                    contentContainerStyle={{ width: Dimensions.get('window').width * 0.8 }}
                                >
                                    {selectedOrderItemList.map((x) => {
                                        return renderOrderItems(x, selectedOrder);
                                    })}

                                    <View style={{
                                        height: 1.5,
                                        width: '100%',
                                        backgroundColor: 'black',
                                        marginTop: 8,
                                        marginBottom: 4,
                                        opacity: 0.1,
                                    }}>

                                    </View>

                                    {selectedOrderItemCancelledList.map((x) => {
                                        return renderOrderItemsCancelled(x, selectedOrder);
                                    })}
                                </ScrollView>

                                {(Object.values(selectedOrderItemCheckDict).find(item => item && item.itemId) ||
                                    Object.values(selectedOrderItemCancelledCheckDict).find(item => item && item.itemId)) &&

                                    <View style={{
                                        bottom: 0,
                                        // backgroundColor: 'red',
                                        backgroundColor: Colors.fieldtBgColor,
                                        width: '100%',
                                        height: Dimensions.get('screen').height * 0.12,

                                        flexDirection: 'row',
                                        alignItems: 'center',

                                        paddingBottom: 20,
                                        paddingTop: 20,
                                        paddingHorizontal: 40,
                                    }}>
                                        <TouchableOpacity style={{
                                            width: '45%',
                                            height: Dimensions.get('screen').height * 0.05,

                                            alignItems: 'center',
                                            justifyContent: 'center',

                                            backgroundColor: Object.values(selectedOrderItemCheckDict).find(item => item && item.itemId) ? Colors.tabRed : Colors.primaryColor,
                                            borderRadius: 8,

                                            shadowColor: '#000',
                                            shadowOffset: {
                                                width: 0,
                                                height: 1,
                                            },
                                            shadowOpacity: 0.22,
                                            shadowRadius: 2.22,
                                            elevation: 3,
                                        }} onPress={() => {
                                            if (Object.values(selectedOrderItemCheckDict).find(item => item && item.itemId)) {
                                                cancelUserOrder();
                                            }
                                            else {
                                                undoUserOrderCancelled();
                                            }
                                        }}>
                                            <Text style={{
                                                color: Colors.whiteColor,
                                                fontFamily: 'NunitoSans-SemiBold',
                                                fontSize: 16,
                                            }}>
                                                {Object.values(selectedOrderItemCheckDict).find(item => item && item.itemId) ? 'Reject' : 'Undo'}
                                            </Text>
                                        </TouchableOpacity>

                                        <View style={{
                                            width: '10%',
                                        }}>

                                        </View>

                                        <TouchableOpacity style={{
                                            width: '45%',
                                            height: Dimensions.get('screen').height * 0.05,

                                            alignItems: 'center',
                                            justifyContent: 'center',

                                            backgroundColor: Colors.lightGrey,
                                            borderRadius: 8,

                                            shadowColor: '#000',
                                            shadowOffset: {
                                                width: 0,
                                                height: 1,
                                            },
                                            shadowOpacity: 0.22,
                                            shadowRadius: 2.22,
                                            elevation: 3,
                                        }} onPress={() => {
                                            setSelectedOrderItemCheckDict({});
                                            setSelectedOrderItemCancelledCheckDict({});
                                        }}>
                                            <Text style={{
                                                color: Colors.fontDark,
                                                fontFamily: 'NunitoSans-SemiBold',
                                                fontSize: 16,
                                            }}>
                                                Cancel
                                            </Text>
                                        </TouchableOpacity>
                                    </View>

                                }
                            </View>
                        </TouchableWithoutFeedback>
                    </View>
                </TouchableWithoutFeedback>
            </Modal>
        </View>
    )
}

const styles = StyleSheet.create({
    centeredView: {
        flex: 1,
        justifyContent: "center",
        alignItems: "center",
        marginTop: 20
    },
    modalView: {
        margin: 20,
        height: Dimensions.get('window').height * 0.6,
        width: Dimensions.get('window').width * 0.8,
        backgroundColor: "white",
        borderRadius: 20,
        alignItems: "center",
        shadowColor: "#000",
        shadowOffset: {
            width: 0,
            height: 2
        },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
        elevation: 5
    },
    modalCloseButton: {
        alignSelf: "flex-end",
        margin: 12
    },
    modalContent: {
        flex: 1,
    },
    topFlat: {
        backgroundColor: 'white',
        margin: 10,
        borderRadius: 8,
        height: 60,
        //minWidth: 60,
        //minWidth: 60,
        elevation: 5,
    },
    tableSlotDisplay: {
        width: Dimensions.get('window').width * 0.12,
        height: Dimensions.get('window').width * 0.12,
        margin: 12,
        borderRadius: 8,
        padding: Dimensions.get('window').width * 0.01,
        borderWidth: 1,
        // borderStyle: 'dashed',
        borderColor: Colors.fieldtTxtColor,
        alignItems: 'center',
        justifyContent: 'center',
    },
    smallCircle: {
        width: 25,
        height: 25,
        backgroundColor: Colors.primaryColor,
        borderRadius: 12.5,
        position: 'absolute',
        top: -5,
        right: -5,
        alignItems: 'center',
        justifyContent: 'center'

    },
    smallCircleFont: {
        color: 'white',
        fontFamily: 'NunitoSans-Bold',
        fontSize: 15,
        marginBottom: Platform.OS == 'android' ? 1 : 0,
    },
    topPart: {
        justifyContent: 'center',
        flex: 1,
        alignSelf: 'center',

        flexDirection: 'row',
        alignItems: 'center',
    },
    bottomPart: {
        flexDirection: 'row',
        padding: 10,
        paddingHorizontal: 10,

        alignItems: 'center',
    },
    tablebarTextStyle: {
        fontFamily: 'NunitoSans-SemiBold',
        fontSize: 15,
    },

    addBtn: {
        backgroundColor: Colors.primaryColor,
        width: 20,
        height: 22,
        justifyContent: "center",
        alignItems: "center",

        borderColor: 'transparent',
    },
})

export default TableBar;