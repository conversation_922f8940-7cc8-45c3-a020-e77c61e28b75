import { Text } from "react-native-fast-text";
import React, { useState, useEffect, useCallback } from 'react';
import {
  StyleSheet,
  Image,
  View,
  Alert,
  TouchableOpacity,
  Modal,
  Dimensions,
  TextInput,
  KeyboardAvoidingView,
  ActivityIndicator,
  useWindowDimensions,
} from 'react-native';
import { ModalView } from 'react-native-multiple-modals';
import { ScrollView, FlatList } from "react-native-gesture-handler";
import CheckBox from 'react-native-check-box';
import Colors from '../constant/Colors';
import Styles from '../constant/Styles';
import SideBar from './SideBar';
import Icon from 'react-native-vector-icons/Ionicons';
import Icon1 from 'react-native-vector-icons/Feather';
import Ionicon from 'react-native-vector-icons/Ionicons';
import DropDownPicker from 'react-native-dropdown-picker';
import Feather from 'react-native-vector-icons/Feather';
import moment from 'moment';
import DateTimePickerModal from 'react-native-modal-datetime-picker';
import {
  isTablet, parseImagePickerResponse
} from '../util/common';
import {
  MERCHANT_VOUCHER_CODE_FORMAT,
  ORDER_TYPE_DROP_DOWN_LIST,
  EXPAND_TAB_TYPE,
} from '../constant/common';
import { CommonStore } from '../store/commonStore';
import { UserStore } from '../store/userStore';
import { MerchantStore } from '../store/merchantStore';
import { OutletStore } from '../store/outletStore';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { useKeyboard } from '../hooks';
import { launchImageLibrary } from 'react-native-image-picker';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import { areArraysEqual, uploadImageToFirebaseStorage, parseValidPriceText } from '../util/common';
import AsyncImage from '../components/asyncImage';
import 'react-native-get-random-values';
import { v4 as uuidv4 } from 'uuid';
import AntDesign from 'react-native-vector-icons/AntDesign';
import FontAwesome5 from 'react-native-vector-icons/FontAwesome5';
import {
  LOYALTY_CAMPAIGN_DROPDOWN_LIST,
  LOYALTY_PROMOTION_TYPE,
  LOYALTY_PROMOTION_TYPE_DROPDOWN_LIST,
} from '../constant/loyalty';
import APILocal from '../util/apiLocalReplacers';
import { useFocusEffect } from '@react-navigation/native';
import UserIdleWrapper from '../components/userIdleWrapper';

//////////////////////////////////////////////////////////////////////////////////////////////////////////

const NewLoyaltyStampType = (props) => {
  const { navigation } = props;

  ///////////////////////////////////////////////////////////

  const [isMounted, setIsMounted] = useState(true);

  useFocusEffect(
    useCallback(() => {
      setIsMounted(true);
      return () => {
        setIsMounted(false);
      };
    }, [])
  );

  ///////////////////////////////////////////////////////////

  const [keyboardHeight] = useKeyboard();

  const { width: windowWidth, height: windowHeight } = useWindowDimensions();

  const [stampTypeName, setStampTypeName] = useState('');

  const [switchMerchant, setSwitchMerchant] = useState(isTablet() ? false : true);


  const [image, setImage] = useState('');
  const [imageType, setImageType] = useState('');
  const [isImageChanged, setIsImageChanged] = useState(false);

  ///////////////////////////////////////////////////////////////////////////////////////////////////////////

  const userId = UserStore.useState((s) => s.firebaseUid);
  const userName = UserStore.useState((s) => s.name);
  const merchantName = MerchantStore.useState((s) => s.name);

  ///////////////////////////////////////////////////////////////////////////////////////////

  const [outletItems, setOutletItems] = useState([]);
  const [outletCategories, setOutletCategories] = useState([]);

  const allOutlets = MerchantStore.useState((s) => s.allOutlets);
  const currOutletId = MerchantStore.useState((s) => s.currOutletId);
  const outletItemsUnsorted = OutletStore.useState((s) => s.outletItems);
  const outletCategoriesUnsorted = OutletStore.useState(
    (s) => s.outletCategories,
  );
  const outletCategoriesDict = OutletStore.useState(
    (s) => s.outletCategoriesDict,
  );
  const outletsTaxDict = OutletStore.useState((s) => s.outletsTaxDict);
  const currOutletTaxes = CommonStore.useState((s) => s.currOutletTaxes);
  const isLoading = CommonStore.useState((s) => s.isLoading);

  const selectedStampTypeEdit = CommonStore.useState(
    (s) => s.selectedStampTypeEdit,
  );

  const merchantId = UserStore.useState((s) => s.merchantId);

  const outletSelectDropdownView = CommonStore.useState(
    (s) => s.outletSelectDropdownView,
  );

  const [temp, setTemp] = useState('');

  //////////////////////////////////////////////////////////////////////////////////////////

  const isAlphaUser = UserStore.useState((s) => s.isAlphaUser);
  const expandTab = CommonStore.useState((s) => s.expandTab);
  const currPageStack = CommonStore.useState((s) => s.currPageStack);

  //////////////////////////////////////////////////////////////////////////////////////////

  const setState = () => { };


  /////////////////////////////////////////////////////////////
  const confirmDeletePromotion = () => {
    return Alert.alert(
      'Delete',
      'Are you sure you want to remove this promotion?',
      [
        {
          text: 'YES',
          onPress: () => {
            deletePromotion(item);
          },
        },
        {
          text: 'NO',
          onPress: () => { },
        },
      ],
    );
  };

  useEffect(() => {
    if (
      selectedStampTypeEdit
    ) {
      // insert info

      setStampTypeName(selectedStampTypeEdit.stampTypeName);
      setImage(selectedStampTypeEdit.image);
      setIsImageChanged(false);

    } else {
      // designed to always mounted, thus need clear manually...

      setStampTypeName('');
      setImage('');

    }
  }, [
    selectedStampTypeEdit,
  ]);


  /////////////////////////////////////////////////////////////

  //To remove unwanted sidebar
  // navigation.dangerouslyGetParent().setOptions({
  //   tabBarVisible: false,
  // });

  //Header
  navigation.setOptions({
    headerLeft: () => (
      <TouchableOpacity
        onPress={() => {
          if (isAlphaUser || true) {
            navigation.navigate('MenuOrderingScreen');

            CommonStore.update((s) => {
              s.currPage = 'MenuOrderingScreen';
              s.currPageStack = [...currPageStack, 'MenuOrderingScreen'];
            });
          }
          else {
            navigation.navigate('Table');

            CommonStore.update((s) => {
              s.currPage = 'Table';
              s.currPageStack = [...currPageStack, 'Table'];
            });
          }
          if (expandTab !== EXPAND_TAB_TYPE.OPERATION) {
            CommonStore.update((s) => {
              s.expandTab = EXPAND_TAB_TYPE.OPERATION;
            });
          }
        }}
        style={styles.headerLeftStyle}>
        <Image
          style={{
            width: 124,
            height: 26,
          }}
          resizeMode="contain"
          source={require('../assets/image/logo.png')}
        />
      </TouchableOpacity>
    ),
    headerTitle: () => (
      <View
        style={[
          {
            // justifyContent: Platform.OS === 'android' ? 'center' : 'flex-start',
            // alignItems: Platform.OS === 'android' ? 'center' : 'flex-start',
            // marginRight: Platform.OS === 'ios' ? "27%" : 0,
            // bottom: switchMerchant ? '2%' : 0,
            ...global.getHeaderTitleStyle(),
          },
          // windowWidth >= 768 && switchMerchant
          //   ? { right: windowWidth * 0.1 }
          //   : {},
          // windowWidth <= 768
          //   ? { right: 20 }
          //   : {},
        ]}>
        <Text
          style={{
            fontSize: switchMerchant ? 20 : 24,
            // lineHeight: 25,
            textAlign: 'left',
            alignItems: 'flex-start',
            justifyContent: 'flex-start',
            fontFamily: 'NunitoSans-Bold',
            color: Colors.whiteColor,
            opacity: 1,
            paddingLeft: Platform.OS === 'ios' ? "1%" : 0,
          }}>
          Loyalty Stamp
        </Text>
      </View>
    ),
    headerRight: () => (
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
        }}>
        {outletSelectDropdownView()}
        <View
          style={{
            backgroundColor: 'white',
            width: 0.5,
            height: windowHeight * 0.025,
            opacity: 0.8,
            marginHorizontal: 15,
            bottom: -1,
          }} />
        <TouchableOpacity
          onPress={() => {
    if (global.currUserRole === 'admin') {
        navigation.navigate('Setting');
    }
}}
          style={{ flexDirection: 'row', alignItems: 'center' }}>
          <Text
            style={[{
              fontFamily: 'NunitoSans-SemiBold',
              fontSize: switchMerchant ? 10 : 16,
              color: Colors.secondaryColor,
              marginRight: 15,
            }, switchMerchant ? { width: windowWidth / 8 } : {}]}
            numberOfLines={switchMerchant ? 1 : 1}
          >

            {userName}
          </Text>
          <View
            style={{
              marginRight: 30,
              width: windowHeight * 0.05,
              height: windowHeight * 0.05,
              borderRadius: windowHeight * 0.05 * 0.5,
              alignItems: 'center',
              justifyContent: 'center',
              backgroundColor: 'white',
            }}>
            <Image
              style={{
                width: windowHeight * 0.035,
                height: windowHeight * 0.035,
                alignSelf: 'center',
              }}
              source={require('../assets/image/profile-pic.jpg')}
            />
          </View>
        </TouchableOpacity>
      </View>
    ),
  });

  /////////////////////////////////////////////////

  const handleChoosePhoto = () => {
    const imagePickerOptions = {
      mediaType: 'photo',
      quality: 0.5,
      includeBase64: false,
      maxWidth: 512,
      maxHeight: 512,
    };

    launchImageLibrary(imagePickerOptions, (response) => {
      if (response.didCancel) {
      } else if (response.error) {
        Alert.alert(response.error.toString());
      } else {
        const responseParsed = parseImagePickerResponse(response);

        // setState({ image: response.uri });
        setImage(responseParsed.uri);
        setImageType(responseParsed.uri.slice(responseParsed.uri.lastIndexOf('.')));

        setIsImageChanged(true);
      }
    });
  };

  const createStamp = async (isAutoPush = false) => {
    var message = '';

    if (!stampTypeName) {
      message += 'Stamp Type name must be filled\n';
    }

    if (message.length > 0) {
      Alert.alert('Info', message);

      
    } else {
      ////////////////////////////////////////////////////////////////

      ///////////////////////////////////
      // upload image

      var stampTypeImagePath = '';
      var stampTypeCommonIdLocal = selectedStampTypeEdit
        ? selectedStampTypeEdit.commonId
        : uuidv4();

      if (image && imageType) {
        // promotionCommonIdLocal = uuidv4();

        stampTypeImagePath = await uploadImageToFirebaseStorage(
          {
            uri: image,
            type: imageType,
          },
          `/merchant/${merchantId}/loyalty-stamp-type/${stampTypeCommonIdLocal}/image${imageType}`,
        );
      }

      ///////////////////////////////////

      if (selectedStampTypeEdit === null) {
        // means new item

        var body = {
          merchantId,
          merchantName,

          stampTypeName,
          image: stampTypeImagePath,
          isImageChanged,
          /////////////////////////////////////////////////////////////
        };


        CommonStore.update((s) => {
          s.isLoading = true;
        });

        // ApiClient.POST(API.createTaggableVoucher, body, false)
        APILocal.createStampType({ body, uid: userId })
          .then(
            (result) => {
              if (result && result.status === 'success') {


                Alert.alert(
                  'Success',
                  'Loyalty Stamp Type has been created',
                  [
                    {
                      text: 'OK',
                      onPress: () => {

                        props.navigation.navigate('LoyaltyStampType');
                      },
                    },
                  ],
                  { cancelable: false },
                );
              }

              CommonStore.update((s) => {
                s.isLoading = false;
              });
            },
          );
      } else if (selectedStampTypeEdit !== null) {
        // means existing item

        var body = {
          stampId: selectedStampTypeEdit.uniqueId,

          merchantId,
          merchantName,

          stampTypeName,
          image: stampTypeImagePath,
          isImageChanged,

          /////////////////////////////////////////////////////////////
        };


        CommonStore.update((s) => {
          s.isLoading = true;
        });

        // ApiClient.POST(API.updateTaggableVoucher, body, false)
        APILocal.updateStampType({ body, uid: userId })
          .then(
            (result) => {
              if (result && result.status === 'success') {

                Alert.alert(
                  'Success',
                  'Loyalty Stamp Type has been updated.',
                  [
                    {
                      text: 'OK',
                      onPress: () => {
                        // navigation.navigate('PromotionList');

                        // props.navigation.navigate('SettingLoyalty');

                        props.navigation.navigate('LoyaltyStampType');
                      },
                    },
                  ],
                  { cancelable: false },
                );
              }

              CommonStore.update((s) => {
                s.isLoading = false;
              });
            },
          );
      }
    }
  };
  /////////////////////////////////////////////////

  //Render start here
  return (
    <UserIdleWrapper disabled={!isMounted}>
      <View
        style={[
          styles.container,
          !isTablet()
            ? {
              transform: [{ scaleX: 1 }, { scaleY: 1 }],
            }
            : {},
        ]}>
        {/* <View
          style={[
            styles.sidebar,
            !isTablet() ? {} : {},
            switchMerchant
              ? {
                // width: '10%'
              }
              : {},
          ]}>
          <SideBar
            navigation={props.navigation}
            selectedTab={11}
            expandPromotions
          />
        </View> */}
        <ScrollView horizontal scrollEnabled={switchMerchant}>
          <KeyboardAvoidingView>
            <View
              style={
                switchMerchant
                  ? {
                    flexDirection: 'row',
                    justifyContent: 'space-between',
                    width: windowWidth * 0.8,
                    marginHorizontal: 15,
                    //alignSelf: 'center',
                    margin: 10,
                    paddingHorizontal: 0,
                    paddingTop: 10,
                  }
                  : {
                    flexDirection: 'row',
                    justifyContent: 'space-between',
                    width: windowWidth * 0.9,
                    marginHorizontal: 15,
                    alignSelf: 'center',
                    margin: 10,
                    paddingHorizontal: 0,
                    paddingTop: 10,
                  }
              }>
              <TouchableOpacity
                style={{ width: 90, height: 35, justifyContent: 'center' }}
                onPress={() => {
                  // props.navigation.navigate('PromotionList');

                  props.navigation.navigate('LoyaltyStampType');
                }}>
                <View
                  style={{
                    flexDirection: 'row',
                    paddingHorizontal: '10%',
                    alignContent: 'center',
                    alignItems: 'center',
                    marginTop: switchMerchant ? 10 : 0,
                  }}>
                  <View style={{ justifyContent: 'center' }}>
                    <Feather
                      name="chevron-left"
                      size={switchMerchant ? 20 : 30}
                      style={{ color: Colors.primaryColor, alignSelf: 'center' }}
                    />
                  </View>
                  <Text
                    style={[
                      {
                        fontSize: 17,
                        color: Colors.primaryColor,
                        fontWeight: '600',
                        marginBottom: Platform.OS === 'ios' ? 0 : 1,
                      },
                      switchMerchant
                        ? {
                          fontSize: 14,
                        }
                        : {},
                    ]}>
                    Back
                  </Text>
                </View>
              </TouchableOpacity>
            </View>

            {/* <KeyBoardAwareScrollView style={styles.list}> */}
            <KeyboardAwareScrollView
              showsVerticalScrollIndicator={false}
              nestedScrollEnabled
              style={{
                backgroundColor: Colors.whiteColor,
                width: switchMerchant
                  ? windowWidth * 0.87
                  : windowWidth * 0.87,
                height: windowHeight * 0.825,
                marginTop: 0,
                marginHorizontal: switchMerchant ? 30 : 30,
                //alignSelf: 'center',
                borderRadius: 5,
                shadowOpacity: 0,
                shadowColor: '#000',
                shadowOffset: {
                  width: 1,
                  height: 2,
                },
                shadowOpacity: 0.22,
                shadowRadius: 3.22,
                elevation: 3,

                marginBottom: switchMerchant ? 50 : 50,
              }}>
              <View
                style={{
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  width: '100%',
                }}>
                <View
                  style={{
                    //flexDirection: 'row',
                    margin: 20,
                    marginBottom: 10,
                    width: '70%',
                  }}>
                  <View
                    style={{
                      width: '100%',
                      justifyContent: 'space-between',
                      flexDirection: 'row',
                    }}>
                    <Text
                      style={[
                        { fontFamily: 'NunitoSans-Bold', fontSize: 30 },
                        switchMerchant
                          ? {
                            fontSize: 20,
                          }
                          : {},
                      ]}>
                      {stampTypeName.length > 0
                        ? stampTypeName
                        : 'New Loyalty Stamp'}
                    </Text>
                  </View>
                </View>
                <View
                  style={{
                    margin: 20,
                    marginBottom: 10,
                  }}>
                  <View style={{}}>
                    <TouchableOpacity
                      style={[
                        {
                          justifyContent: 'center',
                          flexDirection: 'row',
                          borderWidth: 1,
                          borderColor: Colors.primaryColor,
                          backgroundColor: '#0A1F44',
                          borderRadius: 5,
                          width: 130,
                          paddingHorizontal: 10,
                          height: 40,
                          alignItems: 'center',
                          shadowOffset: {
                            width: 0,
                            height: 2,
                          },
                          shadowOpacity: 0.22,
                          shadowRadius: 3.22,
                          elevation: 1,
                          zIndex: -1,
                          marginBottom: 10,
                        },
                        switchMerchant
                          ? {
                            height: 35,
                            width: 120,
                          }
                          : {},
                      ]}
                      disabled={isLoading}
                      onPress={() => { createStamp() }}>
                      <Text
                        style={[
                          {
                            color: Colors.whiteColor,
                            fontSize: 16,
                            fontFamily: 'NunitoSans-Bold',
                          },
                          switchMerchant
                            ? {
                              fontSize: 10,
                            }
                            : {},
                        ]}>
                        {isLoading ? 'LOADING...' : 'SAVE'}
                      </Text>

                      {isLoading ? (
                        <ActivityIndicator
                          color={Colors.whiteColor}
                          size={'small'}
                        />
                      ) : (
                        <></>
                      )}
                    </TouchableOpacity>

                  </View>
                </View>
              </View>

              <View
                style={{
                  flexDirection: 'column',
                  borderWidth: 1,
                  borderColor: '#c4c4c4',
                  width: '95%',
                  alignSelf: 'center',
                  //flex: 1,
                  paddingBottom: 30,
                }}>
                <View
                  style={{ flexDirection: 'row', flex: 1, alignSelf: 'center' }}>
                  <View
                    style={{
                      flex: 4,
                      flexDirection: 'column',
                      marginVertical: 20,
                    }}>
                    <View style={{ flexDirection: 'column', marginLeft: 20 }}>
                      <TouchableOpacity
                        onPress={() => {
                          handleChoosePhoto();
                        }}>
                        <View style={{ flexDirection: 'row', zIndex: -2 }}>
                          {image ? (
                            <View
                              style={{
                                backgroundColor: '#F7F7F7',
                                borderRadius: 5,
                                zIndex: 1,
                              }}>
                              <AsyncImage
                                source={{ uri: image }}
                                style={[
                                  { width: 260, height: 200, borderRadius: 5 },
                                  switchMerchant
                                    ? {
                                      width: 200,
                                      height: 160,
                                    }
                                    : {},
                                ]}
                                hideLoading
                              />
                              <View
                                style={{
                                  position: 'absolute',
                                  bottom: 5,
                                  right: 5,
                                  //opacity: 0.5,
                                }}>
                                <FontAwesome5
                                  name="edit"
                                  size={switchMerchant ? 10 : 23}
                                  color={Colors.primaryColor}
                                />
                              </View>
                            </View>
                          ) : (
                            <View
                              style={[
                                {
                                  backgroundColor: '#F7F7F7',
                                  borderRadius: 5,
                                  width: 260,
                                  height: 200,
                                  alignItems: 'center',
                                  justifyContent: 'center',
                                },
                                switchMerchant
                                  ? {
                                    width: 200,
                                    height: 160,
                                  }
                                  : {},
                              ]}>
                              <Icon1
                                name="upload"
                                size={switchMerchant ? 100 : 150}
                                color="lightgrey"
                                style={{ zIndex: -1 }}
                              />
                              <View
                                style={{
                                  position: 'absolute',
                                  bottom: 5,
                                  right: 5,
                                  //opacity: 0.5,
                                }}>
                                <FontAwesome5
                                  name="edit"
                                  size={switchMerchant ? 10 : 23}
                                  color={Colors.primaryColor}
                                />
                              </View>
                            </View>
                          )}
                        </View>
                      </TouchableOpacity>
                    </View>
                  </View>

                  <View
                    style={{
                      flex: 7,
                      marginVertical: 20,
                      marginHorizontal: 20,
                      marginLeft: 10,
                    }}>
                    <View style={{ flexDirection: 'row', flex: 1, zIndex: 1 }}>
                      <View
                        style={{
                          flex: 1,
                          marginRight: switchMerchant
                            ? '5%'
                            : windowWidth <= 1024
                              ? '3%'
                              : '2%',
                        }}>
                        <Text
                          style={[
                            {
                              alignSelf: 'flex-start',
                              fontFamily: 'NunitoSans-Bold',
                              fontSize: 14,
                              fontWeight: '500',
                            },
                            switchMerchant
                              ? {
                                fontSize: 10,
                              }
                              : {},
                          ]}>
                          Loyalty Stamp Type Name
                        </Text>
                        <TextInput
                          placeholder="Loyalty Stamp Type Name"
                          placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                          placeholderStyle={{
                            fontFamily: 'NunitoSans-Regular',
                            fontSize: 14,
                          }}
                          style={[
                            {
                              backgroundColor: Colors.fieldtBgColor,
                              width: 250,
                              height: 40,
                              borderRadius: 5,
                              padding: 5,
                              // marginVertical: 5,
                              marginTop: 5,
                              borderWidth: 1,
                              borderColor: '#E5E5E5',
                              paddingLeft: 10,
                              fontFamily: 'NunitoSans-Regular',
                              fontSize: 14,
                            },
                            switchMerchant
                              ? {
                                fontSize: 10,
                                width: 200,
                                height: 35,
                              }
                              : {},
                          ]}
                          //iOS
                          clearTextOnFocus
                          //////////////////////////////////////////////
                          //Android
                          onFocus={() => {
                            setTemp(stampTypeName)
                            setStampTypeName('');
                          }}
                          ///////////////////////////////////////////////
                          //When textinput is not selected
                          onEndEditing={() => {
                            if (stampTypeName == '') {
                              setStampTypeName(temp);
                            }
                          }}
                          onChangeText={(text) => {
                            setStampTypeName(text);
                          }}
                          defaultValue={stampTypeName}
                        />
                      </View>

                    </View>
                  </View>
                </View>
              </View>
            </KeyboardAwareScrollView>
          </KeyboardAvoidingView>
        </ScrollView>
      </View>
    </UserIdleWrapper>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.highlightColor,
    flexDirection: 'row',
  },
  list: {
    backgroundColor: Colors.whiteColor,
    width: Dimensions.get('window').width * 0.87,
    height: Dimensions.get('window').height * 0.825,
    marginTop: 0,
    marginHorizontal: 20,
    alignSelf: 'center',
    borderRadius: 5,
    shadowOpacity: 0,
    shadowColor: '#000',
    shadowOffset: {
      width: 1,
      height: 2,
    },
    shadowOpacity: 0.22,
    shadowRadius: 3.22,
    elevation: 1,
  },
  listItem: {
    marginLeft: 20,
    color: Colors.descriptionColor,
    fontSize: 16,
  },
  sidebar: {
    //width: windowWidth * Styles.sideBarWidth,
    // shadowColor: '#000',
    // shadowOffset: {
    //   width: 0,
    //   height: 8,
    // },
    // shadowOpacity: 0.44,
    // shadowRadius: 10.32,

    // elevation: 16,
  },
  textInput: {
    height: 50,
    paddingHorizontal: 20,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 5,
    marginBottom: 20,
    width: 300,
  },
  headerLeftStyle: {
    width: Dimensions.get('window').width * 0.17,
    justifyContent: 'center',
    alignItems: 'center',
  },

});

export default NewLoyaltyStampType;
