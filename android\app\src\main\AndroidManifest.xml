<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
>
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.INTERNET" />
     <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED"/>

    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.DOWNLOAD_WITHOUT_NOTIFICATION" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />

    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />

    <uses-permission android:name="com.google.android.gms.permission.AD_ID"/>

    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW"/>
    <uses-permission android:name="android.permission.USB_PERMISSION" />
    <uses-permission android:name="android.hardware.usb.host" />
    <uses-permission android:name="android.hardware.usb.accessory" />

    <!-- <uses-permission android:name="android.permission.BLUETOOTH"
                     android:maxSdkVersion="30" />
    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN"
                     android:maxSdkVersion="30" /> -->

    <!-- <uses-permission android:name="android.permission.BLUETOOTH" 
    tools:remove="android:maxSdkVersion" />
    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" 
    tools:remove="android:maxSdkVersion" /> -->

    <uses-permission android:name="android.hardware.usb.UsbAccessory" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />

    <!-- <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
    <uses-permission android:name="android.permission.BLUETOOTH_SCAN" /> -->

    <uses-permission android:name="android.permission.BLUETOOTH" android:maxSdkVersion="30" />
    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" android:maxSdkVersion="30" />
    <uses-permission android:name="android.permission.BLUETOOTH_SCAN" android:usesPermissionFlags="neverForLocation" />
    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />

    <!-- <uses-feature android:name="android.hardware.bluetooth_le" android:required="true" /> -->

    <uses-feature
        android:name="android.hardware.bluetooth"
        android:required="true" />
    <uses-feature
        android:name="android.hardware.bluetooth_le"
        android:required="true" />

    <!-- <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
    <uses-permission android:name="android.permission.BLUETOOTH_SCAN" />

    <uses-permission android:name="android.permission.BLUETOOTH" />
    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />

    <permission android:name="android.permission.BLUETOOTH" android:label="BLUETOOTH" />
    <permission android:name="android.permission.BLUETOOTH_ADMIN" />
    <permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <permission android:name="android.permission.ACCESS_COARSE_LOCATION" /> -->

    <uses-feature android:name="android.hardware.usb.accessory" />
    <uses-feature
        android:name="android.hardware.usb.host"
        android:required="true" />

    <!-- <uses-permission android:name="android.permission.NFC" />

    <uses-permission android:name="com.sunmi.perm.LED" />
    <uses-permission android:name="com.sunmi.perm.MSR" />
    <uses-permission android:name="com.sunmi.perm.ICC" />
    <uses-permission android:name="com.sunmi.perm.PINPAD" />
    <uses-permission android:name="com.sunmi.perm.SECURITY" />
    <uses-permission android:name="com.sunmi.perm.CONTACTLESS_CARD" />

    <uses-permission
        android:name="android.permission.QUERY_ALL_PACKAGES"
        tools:ignore="QueryAllPackagesPermission" /> -->

    <!-- <uses-permission
        android:name="android.permission.QUERY_ALL_PACKAGES" /> -->

    <queries>
        <package android:name="woyou.aidlservice.jiuiv5" />

        <package android:name="com.sunmi.scanner" />
        <package android:name="com.sunmi.sunmiqrcodescanner" />
    </queries>

    <application
      android:name=".MainApplication"
      android:label="@string/app_name"
      android:icon="@mipmap/ic_launcher"
      android:roundIcon="@mipmap/ic_launcher_round"
      android:allowBackup="false"
      android:theme="@style/AppTheme">
      <!-- Change the value to true to enable pop-up for in foreground on receiving remote notifications (for prevent duplicating while showing local notifications set this to false) -->
      <meta-data  android:name="com.dieam.reactnativepushnotification.notification_foreground"
                  android:value="false"/>
      <!-- Change the resource name to your App's accent color - or any other color you want -->
      <meta-data  android:name="com.dieam.reactnativepushnotification.notification_color"
                  android:resource="@color/koodooPrimary"/> <!-- or @android:color/{name} to use a standard color -->

      <receiver android:exported="false" android:name="com.dieam.reactnativepushnotification.modules.RNPushNotificationActions" />
      <receiver android:exported="false" android:name="com.dieam.reactnativepushnotification.modules.RNPushNotificationPublisher" />
      <receiver android:exported="false" android:name="com.dieam.reactnativepushnotification.modules.RNPushNotificationBootEventReceiver">
          <intent-filter>
              <action android:name="android.intent.action.BOOT_COMPLETED" />
              <action android:name="android.intent.action.QUICKBOOT_POWERON" />
              <action android:name="com.htc.intent.action.QUICKBOOT_POWERON"/>
          </intent-filter>
      </receiver>

      <service
          android:name="com.dieam.reactnativepushnotification.modules.RNPushNotificationListenerService"
          android:exported="false" >
          <intent-filter>
              <action android:name="com.google.firebase.MESSAGING_EVENT" />
          </intent-filter>
      </service>

      <service android:name="com.asterinet.react.bgactions.RNBackgroundActionsTask" android:foregroundServiceType="shortService"/>

      <!-- <activity android:name="com.jackbayliss.nfcreader.NfcCardReaderActivity"/> -->

      <activity
        android:name=".MainActivity"
        android:label="@string/app_name"
        android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
        android:launchMode="singleTask"
        android:windowSoftInputMode="adjustResize"
        android:exported="true"
        android:resizeableActivity="false"
    >
        <intent-filter>
            <action android:name="android.intent.action.MAIN" />
            <category android:name="android.intent.category.LAUNCHER" />
            <action android:name="android.intent.action.DOWNLOAD_COMPLETE"/>
        </intent-filter>
      </activity>
    </application>
</manifest>
