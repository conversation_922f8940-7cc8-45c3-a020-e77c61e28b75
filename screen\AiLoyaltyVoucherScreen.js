import React, {useState, useEffect, useCallback} from 'react';
import {
    StyleSheet,
    Image,
    View,
    Text,
    Alert,
    TouchableOpacity,
    Modal,
    Dimensions,
    TextInput,
    KeyboardAvoidingView,
    ActivityIndicator,
    useWindowDimensions,
    Platform,
} from 'react-native';
import { ModalView } from 'react-native-multiple-modals';
import { useStoreState } from 'pullstate';
import CheckBox from 'react-native-check-box';
import Colors from '../constant/Colors';
import Styles from '../constant/Styles';
import SideBar from './SideBar';
import Icon from 'react-native-vector-icons/Ionicons';
import Icon1 from 'react-native-vector-icons/Feather';
import Ionicon from 'react-native-vector-icons/Ionicons';
import DropDownPicker from 'react-native-dropdown-picker';
import Feather from 'react-native-vector-icons/Feather';
import moment from 'moment';
import DateTimePickerModal from 'react-native-modal-datetime-picker';
import {
    isTablet
} from '../util/common';
import {
    MERCHANT_VOUCHER_CODE_FORMAT,
    ORDER_TYPE_DROP_DOWN_LIST,
    EXPAND_TAB_TYPE,
    USER_INFO_TO_COLLECTED_TYPE_DROPDOWN_LIST,
    ORDER_REGISTER_QR_SALT,
    VOUCHER_ACTIVATION_DAYS_DROPDOWN_LIST,
    VOUCHER_EXPIRATION_DAYS_DROPDOWN_LIST,
    CHANNEL_TYPE,
    CHANNEL_TYPE_DROP_DOWN_LIST
} from '../constant/common';
import { CommonStore } from '../store/commonStore';
import { UserStore } from '../store/userStore';
import { MerchantStore } from '../store/merchantStore';
import { OutletStore } from '../store/outletStore';
import { LoyaltyStore } from "../store/loyaltyStore";
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { useKeyboard } from '../hooks';
import { launchImageLibrary } from 'react-native-image-picker';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import {
    EFFECTIVE_DAY_DROPDOWN_LIST,
    TARGET_USER_GROUP_DROPDOWN_LIST,
    PROMOTION_TYPE_VARIATION,
    PROMOTION_TYPE_VARIATION_DROPDOWN_LIST,
    EFFECTIVE_TYPE,
    CRM_SEGMENT_DROPDOWN_LIST,
    APPLY_DISCOUNT_PER_DROPDOWN_LIST,
    APPLY_DISCOUNT_PER,
    PROMOTION_TYPE,
    VOUCHER_REDEEM_TYPE,
    VOUCHER_REDEEM_TYPE_DROPDOWN_LIST,
} from '../constant/promotions';
import { areArraysEqual, uploadImageToFirebaseStorage, parseValidPriceText } from '../util/common';
import AsyncImage from '../components/asyncImage';
import 'react-native-get-random-values';
import { v4 as uuidv4 } from 'uuid';
import AntDesign from 'react-native-vector-icons/AntDesign';
import FontAwesome5 from 'react-native-vector-icons/FontAwesome5';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import {
    LOYALTY_CAMPAIGN_DROPDOWN_LIST,
    LOYALTY_PROMOTION_TYPE,
    LOYALTY_PROMOTION_TYPE_DROPDOWN_LIST,
} from '../constant/loyalty';
import APILocal from '../util/apiLocalReplacers';
import Hashids from 'hashids';
import { qrUrl } from '../constant/env';
import Clipboard from '@react-native-clipboard/clipboard';
import { useFocusEffect } from '@react-navigation/native';
import UserIdleWrapper from '../components/userIdleWrapper';
import { CellContainer, FlashList } from '@shopify/flash-list';
import { TouchableHighlight, ScrollView, FlatList } from 'react-native-gesture-handler';
// import { check } from 'prettier';
import { Collections } from '../constant/firebase';
import firestore from '@react-native-firebase/firestore';

const hashids = new Hashids(ORDER_REGISTER_QR_SALT);

//////////////////////////////////////////////////////////////////////////////////////////////////////////

const AiLoyaltyVoucherScreen = (props) => {
    const { navigation } = props;

    ///////////////////////////////////////////////////////////

    const [isMounted, setIsMounted] = useState(true);

    useFocusEffect(
        useCallback(() => {
            setIsMounted(true);
            return () => {
                CommonStore.update(s => {
                    s.isNewTaggableVoucherScreenMounted = false;
                });
                setIsMounted(false);
            };
        }, [])
    );

    ///////////////////////////////////////////////////////////
    // View Constant
    const { width: windowWidth, height: windowHeight } = useWindowDimensions();
    
    const [switchMerchant, setSwitchMerchant] = useState(true);
    
    useEffect(() => {
        const checkTablet = async () => {
            const isTabletResult = await isTablet();
            setSwitchMerchant(!isTabletResult);
        };
        checkTablet();
    }, []);

    ///////////////////////////////////////////////////////////

    // Credentials
    const userId = UserStore.useState((s) => s.firebaseUid);
    const userName = UserStore.useState((s) => s.name);
    const merchantId = UserStore.useState((s) => s.merchantId);
    const merchantName = MerchantStore.useState((s) => s.name);

    ///////////////////////////////////////////////////////////

    // Override in array form
    const [variationItemsProducts, setVariationItemsProducts] = useState([]);
    const [variationItemsCategories, setVariationItemsCategories] = useState([]);

    ///////////////////////////////////////////////////////////

    const [crmUserTagsDropdownList, setCrmUserTagsDropdownList] = useState([]);
    const [crmSegmentsDropdownList, setCrmSegmentsDropdownList] = useState([]);

    ///////////////////////////////////////////////////////////////////////////////////////////

    const [outletItems, setOutletItems] = useState([]);
    const [outletCategories, setOutletCategories] = useState([]);

    ///////////////////////////////////////////////////////////////////////////////////////////

    // Global Store States
    const allOutlets = MerchantStore.useState((s) => s.allOutlets);
    const currOutletId = MerchantStore.useState((s) => s.currOutletId);
    const outletItemsUnsorted = OutletStore.useState((s) => s.outletItems);
    const outletCategoriesUnsorted = OutletStore.useState((s) => s.outletCategories);
    const outletCategoriesDict = OutletStore.useState((s) => s.outletCategoriesDict);

    const outletsTaxDict = OutletStore.useState((s) => s.outletsTaxDict);
    const currOutletTaxes = CommonStore.useState((s) => s.currOutletTaxes);
    const isLoading = CommonStore.useState((s) => s.isLoading);

    const allOutletsCategoriesUnique = OutletStore.useState((s) => s.allOutletsCategoriesUnique);

    const crmUserTags = OutletStore.useState((s) => s.crmUserTags);
    const crmSegments = OutletStore.useState((s) => s.crmSegments);

    const taggableVouchers = OutletStore.useState((s) => s.taggableVouchers);
    const selectedTaggableVoucherEdit = CommonStore.useState((s) => s.selectedTaggableVoucherEdit);

    const recommendedLoyalty = OutletStore.useState((s) => s.selectedRecommendedLoyalty)

    ///////////////////////////////////////////////////////////////////////////////////////////
    
    // Navigation
    const outletSelectDropdownView = CommonStore.useState((s) => s.outletSelectDropdownView);

    const isAlphaUser = UserStore.useState((s) => s.isAlphaUser);
    const expandTab = CommonStore.useState((s) => s.expandTab);
    const currPageStack = CommonStore.useState((s) => s.currPageStack);

    /////////////////////////////////////////////////////////////
    const [temp, setTemp] = useState('');

    const [selectedTaggableVoucher, setSelectedTaggableVoucher] = useState(undefined);

    // Modal Display Control State
    const [showPromoDateStartPicker, setShowPromoDateStartPicker] = useState(false);
    const [showPromoDateEndPicker, setShowPromoDateEndPicker] = useState(false);
    const [showPromoTimeStartPicker, setShowPromoTimeStartPicker] = useState(false);
    const [showPromoTimeEndPicker, setShowPromoTimeEndPicker] = useState(false);
    const [showNotificationDatePicker, setShowNotificationDatePicker] = useState(false);
    const [showNotificationTimePicker, setShowNotificationTimePicker] = useState(false);

    // DropDown List
    const [taggableVoucherDropdownList, setTaggableVoucherDropdownList] = useState([]);
    const [outletDropdownList, setOutletDropdownList] = useState([]);

    /////////////////////////////////////////////////////////////
    //////////////////// Step 1 ////////////////////
    const [voucherType, setVoucherType] = useState();
    const [applyDiscountPer, setApplyDiscountPer] = useState();
    const [minSpend, setMinSpend] = useState(0);

    //////* Shared Dropdown List (Product Category / Product + Items) *///////
    const [selectedVariation, setSelectedVariation] = useState(PROMOTION_TYPE_VARIATION.SPECIFIC_PRODUCTS);
    const [variationItemsDropdownList, setVariationItemsDropdownList] = useState([]);
    const [selectedVariationItems, setSelectedVariationItems] = useState([]);
    const [selectedVariationItemsSku, setSelectedVariationItemsSku] = useState([]);

    //////* Override Existing Price *///////
    const [overrideItems, setOverrideItems] = useState([
        {
            priceBeforeTax: 0,
            variation: PROMOTION_TYPE_VARIATION.SPECIFIC_PRODUCTS,
            variationItems: [],
            variationItemsSku: [],
        }
    ]);

    //////* Bundle Deal *///////
    const [buyInput, setBuyInput] = useState('1');
    const [getInput, setGetInput] = useState('1');
    const [getPriceInput, setGetPriceInput] = useState('0');

    const [selectedVariationB1F1, setSelectedVariationB1F1] = useState(PROMOTION_TYPE_VARIATION.SPECIFIC_PRODUCTS);
    const [selectedVariationItemsB1F1, setSelectedVariationItemsB1F1] = useState([],);
    const [selectedVariationItemsSkuB1F1, setSelectedVariationItemsSkuB1F1] = useState([]);
    const [variationItemsB1F1DropdownList, setVariationItemsB1F1DropdownList] = useState([]);

    //////* Take Amount Off *///////
    const [amountOffPrice, setAmountOffPrice] = useState('0');
    const [amountOffMinQuantity, setAmountOffMinQuantity] = useState('0');
    const [amountOffMaxQuantity, setAmountOffMaxQuantity] = useState('0');

    //////* Take Percentage Off *///////
    const [percentageOffPrice, setPercentageOffPrice] = useState('0');
    const [percentageOffMinQuantity, setPercentageOffMinQuantity] = useState('0');
    const [percentageOffMaxQuantity, setPercentageOffMaxQuantity] = useState('0');

    //////* Delivery *///////
    const [deliveryFreeFlag, setDeliveryFreeFlag] = useState(false);
    const [deliveryFreeAboveAmount, setDeliveryFreeAboveAmount] = useState('0');
    const [deliveryDiscountAmount, setDeliveryDiscountAmount] = useState('0');
    const [deliveryDiscountAboveAmount, setDeliveryDiscountAboveAmount] = useState('0');

    //////* Takeaway *///////
    const [takeawayFreeFlag, setTakeawayFreeFlag] = useState(false);
    const [takeawayFreeAboveAmount, setTakeawayFreeAboveAmount] = useState('0');
    const [takeawayDiscountAmount, setTakeawayDiscountAmount] = useState('0');
    const [takeawayDiscountAboveAmount, setTakeawayDiscountAboveAmount] = useState('0');

    //////////////////// Step 2 ////////////////////
    const campaignName = LoyaltyStore.useState((s) => s.name);
    const campaignDescription = LoyaltyStore.useState((s) => s.description);

    const [selectAllOrderTypes, setSelectAllOrderTypes] = useState(false);
    const orderTypes = LoyaltyStore.useState((s) => s.orderType);
    const outletIdList = LoyaltyStore.useState((s) => s.outletIdList);

    const promoDateStart = LoyaltyStore.useState((s)=> s.promoDateStart);
    const promoDateEnd = LoyaltyStore.useState((s) => s.promoDateEnd);
    const promoTimeStart = LoyaltyStore.useState((s) => s.promoTimeStart);
    const promoTimeEnd = LoyaltyStore.useState((s) => s.promoTimeEnd);

    const effectiveTypeOptions = LoyaltyStore.useState((s) => s.effectiveTypeOption);
    const [isFreeToClaimVoucher, setIsFreeToClaimVoucher] = useState(false);

    //////////////////// Step 3 ////////////////////
    const [voucherCodeFormat, setVoucherCodeFormat] = useState(MERCHANT_VOUCHER_CODE_FORMAT.UNIQUE);
    const [voucherCodeValueGeneric, setVoucherCodeValueGeneric] = useState('');

    const [voucherQuantity, setVoucherQuantity] = useState(100);
    const [voucherMaxClaimPerUser, setVoucherMaxClaimPerUser] = useState(1);

    const [targetSegmentGroupList, setTargetSegmentGroupList] = useState([]);
    const [voucherTerms, setVoucherTerms] = useState('');

    const [isAvailableOnline, setIsAvailableOnline] = useState(false);
    const [userInfoToCollectedList, setUserInfoToCollectedList] = useState([]);
    const [voucherRedeemType, setSelectedVoucherRedeemType] = useState(VOUCHER_REDEEM_TYPE.USE_LATER);

    const [activationDays, setActivationDays] = useState(VOUCHER_ACTIVATION_DAYS_DROPDOWN_LIST[0].value);
    const [expirationDays, setExpirationDays] = useState(VOUCHER_EXPIRATION_DAYS_DROPDOWN_LIST[7].value);

    const [toClaimMinSpent, setToClaimMinSpent] = useState(0);
    const [voucherPointsRequired, setVoucherPointsRequired] = useState(0);

    const [notificationTitle, setNotificationTitle] = useState('');
    const [notificationDescription, setNotificationDescription] = useState('');
    const [isAutoPush, setIsAutoPush] = useState(false);
    const [isPushToSMS, setIsPushToSMS] = useState(false);
    const [isPushToApp, setIsPushToApp] = useState(true);
    const [isPushToEmail, setIsPushToEmail] = useState(false);
    const [notificationDate, setNotificationDate] = useState(moment());
    const [notificationTime, setNotificationTime] = useState(moment());

    const [notificationText, setNotificationText] = useState('');

    /////////////////////////////////////////////////////////////
    // 2024-Jun-11 - State but dont have any Field to change?
    // const [isEnableSellOnline, setIsEnableSellOnline] = useState(false);
    // const [selectedTargetUserGroup, setSelectedTargetUserGroup] = useState(TARGET_USER_GROUP_DROPDOWN_LIST[0].value);
    // const [promoCode, setPromoCode] = useState('');
    // const [isPromoCodeUsageLimit, setIsPromoCodeUsageLimit] = useState(false);
    // const [promoCodeUsageLimit, setPromoCodeUsageLimit] = useState('');
    // const [image, setImage] = useState('');

    //commonId,
    //effectiveType,
    //effectiveDay,
    //effectiveTimeStart,
    //effectiveTimeEnd,
    //criteriaList,
    //isEditNotification,
    //loyaltyCampaignId,
    //promotionId,
    //voucherCodeUniqueLength,
    //voucherCodeUniquePrefix,
    //creditToBuy,

    /////////////////////////////////////////////////////////////

    useEffect(() => {
        var crmSegmentsDropdownListTemp = crmSegments.map((segment) => ({
            label: segment.name,
            value: segment.uniqueId,
        }));

        setCrmSegmentsDropdownList(
            crmSegmentsDropdownListTemp
        );

        if (targetSegmentGroupList.length > 0) {
            var selectedTargetSegmentGroupListTemp = [];

            var combinedCrmSegmentsDropdownListTemp = CRM_SEGMENT_DROPDOWN_LIST.concat(crmSegmentsDropdownListTemp);

            for (let i = 0; i < targetSegmentGroupList.length; i++) {
                if (combinedCrmSegmentsDropdownListTemp.find(item => item.value === targetSegmentGroupList[i])) {
                    selectedTargetSegmentGroupListTemp.push(targetSegmentGroupList[i]);
                }
            }

            var isChanged = false;
            if (targetSegmentGroupList.length !== selectedTargetSegmentGroupListTemp.length) {
                isChanged = true;
            } 
            else {
                for (let i = 0; i < targetSegmentGroupList.length; i++) {
                    const isEqual = targetSegmentGroupList[i] === selectedTargetSegmentGroupListTemp[i];

                    if (!isEqual) {
                        isChanged = true;
                        break;
                    }
                }
            }

            if (isChanged) {
                LoyaltyStore.update(s => {
                    s.targetSegmentGroupList = selectedTargetSegmentGroupListTemp;
                });
            }
        }
    }, [crmSegments, targetSegmentGroupList]);

    /////////////////////////////////////////////////////////////

    useEffect(() => {
        var isDiff = false;
        var overrideItemsTemp = [];

        for (var index = 0; index < overrideItems.length; index++) {
            var skuArray = [];

            if (
                overrideItems[index].variation ===
                PROMOTION_TYPE_VARIATION.SPECIFIC_PRODUCTS
            ) {
                for (let i = 0; i < outletItems.length; i++) {
                    if (
                        overrideItems[index].variationItems.includes(
                            outletItems[i].uniqueId,
                        )
                    ) {
                        skuArray.push(outletItems[i].sku);
                    }
                }
            } else if (
                overrideItems[index].variation ===
                PROMOTION_TYPE_VARIATION.PRODUCT_OF_CATEGORY
            ) {
                for (let i = 0; i < allOutletsCategoriesUnique.length; i++) {
                    if (
                        overrideItems[index].variationItems.includes(
                            allOutletsCategoriesUnique[i].uniqueId,
                        )
                    ) {
                        skuArray.push(allOutletsCategoriesUnique[i].name);
                    }
                }
            }

            isDiff = !areArraysEqual(
                skuArray,
                overrideItems[index].variationItemsSku,
            );

            var overrideItemTemp = {
                priceBeforeTax: overrideItems[index].priceBeforeTax,
                variation: overrideItems[index].variation,
                variationItems: overrideItems[index].variationItems,
                variationItemsSku: skuArray,
            };

            overrideItemsTemp.push(overrideItemTemp);
        }

        if (isDiff) {
            setOverrideItems(overrideItemsTemp);
        }
    }, [
        overrideItems,
        outletItems,
        allOutletsCategoriesUnique,
    ]);

    useEffect(() => {
        var selectedVariationItemsSkuTemp = [];

        if (selectedVariation === PROMOTION_TYPE_VARIATION.SPECIFIC_PRODUCTS) {
            for (let i = 0; i < outletItems.length; i++) {
                if (selectedVariationItems.includes(outletItems[i].uniqueId)) {
                    selectedVariationItemsSkuTemp.push(outletItems[i].sku);
                }
            }
        } else if (
            selectedVariation === PROMOTION_TYPE_VARIATION.PRODUCT_OF_CATEGORY
        ) {
            for (let i = 0; i < allOutletsCategoriesUnique.length; i++) {
                if (
                    selectedVariationItems.includes(
                        allOutletsCategoriesUnique[i].uniqueId,
                    )
                ) {
                    selectedVariationItemsSkuTemp.push(
                        allOutletsCategoriesUnique[i].name,
                    );
                }
            }
        }

        setSelectedVariationItemsSku(selectedVariationItemsSkuTemp);
    }, [
        selectedVariation,
        selectedVariationItems,
        outletItems,
        allOutletsCategoriesUnique,
    ]);

    useEffect(() => {
        var selectedVariationItemsSkuB1F1Temp = [];

        if (selectedVariationB1F1 === PROMOTION_TYPE_VARIATION.SPECIFIC_PRODUCTS) {
            for (let i = 0; i < outletItems.length; i++) {
                if (selectedVariationItemsB1F1.includes(outletItems[i].uniqueId)) {
                    selectedVariationItemsSkuB1F1Temp.push(outletItems[i].sku);
                }
            }
        } else if (
            selectedVariationB1F1 === PROMOTION_TYPE_VARIATION.PRODUCT_OF_CATEGORY
        ) {
            for (let i = 0; i < allOutletsCategoriesUnique.length; i++) {
                if (
                    selectedVariationItemsB1F1.includes(
                        allOutletsCategoriesUnique[i].uniqueId,
                    )
                ) {
                    selectedVariationItemsSkuB1F1Temp.push(
                        allOutletsCategoriesUnique[i].name,
                    );
                }
            }
        }
        setSelectedVariationItemsSkuB1F1(selectedVariationItemsSkuB1F1Temp);
    }, [
        selectedVariationB1F1,
        selectedVariationItemsB1F1,
        outletItems,
        allOutletsCategoriesUnique,
    ]);

    ////////////////////////////////////////////////////////

    useEffect(() => {
        if (selectedVariation === PROMOTION_TYPE_VARIATION.PRODUCT_OF_CATEGORY) {
            setVariationItemsDropdownList(
                allOutletsCategoriesUnique.map((item) => ({
                    label: item.name,
                    value: item.uniqueId,
                })),
            );
        } 
        else if (selectedVariation === PROMOTION_TYPE_VARIATION.SPECIFIC_PRODUCTS) {
            setVariationItemsDropdownList(
                outletItems.map((item) => ({ 
                    label: item.name, 
                    value: item.uniqueId 
                })),
            );
        }

        if (selectedVariationB1F1 === PROMOTION_TYPE_VARIATION.PRODUCT_OF_CATEGORY) {
            setVariationItemsB1F1DropdownList(
                allOutletsCategoriesUnique.map((item) => ({
                    label: item.name,
                    value: item.uniqueId,
                })),
            );
        } 
        else if (selectedVariationB1F1 === PROMOTION_TYPE_VARIATION.SPECIFIC_PRODUCTS) {
            setVariationItemsB1F1DropdownList(
                outletItems.map((item) => ({ 
                    label: item.name, 
                    value: item.uniqueId 
                })),
            );
        }

        setVariationItemsProducts(
            outletItems.map((item) => ({ label: item.name, value: item.uniqueId })),
        );
        setVariationItemsCategories(
            allOutletsCategoriesUnique.map((item) => ({
                label: item.name,
                value: item.uniqueId,
            })),
        );
    }, [
        outletItems,
        allOutletsCategoriesUnique,
        selectedVariation,
        selectedVariationB1F1,
        selectedVariationItems,
        selectedVariationItemsB1F1,
    ]);

    ////////////////////////////////////////////////////////

    useEffect(() => {

        if (selectedVariationItemsB1F1.length > 0 &&
            selectedVariationItemsB1F1.filter(itemId => {
                return variationItemsB1F1DropdownList.find(item2 => item2.value === itemId) ? true : false;
            }).length !== selectedVariationItemsB1F1.length
        ) {
            setSelectedVariationItemsB1F1([]);
        }

        if (selectedVariationItems.length > 0 &&
            selectedVariationItems.filter(itemId => {
                return variationItemsDropdownList.find(item2 => item2.value === itemId) ? true : false;
            }).length !== selectedVariationItems.length
        ) {
            setSelectedVariationItems([]);
        }

        var overrideItemsTemp = [];

        for (let i = 0; i < overrideItems.length; i++) {
            var overrideItemTemp = {
                priceBeforeTax: overrideItems[i].priceBeforeTax,
                variation: overrideItems[i].variation,
                variationItems: overrideItems[i].variationItems,
                variationItemsSku: overrideItems[i].variationItemsSku,
            };

            if (
                overrideItems[i].variationItems.length > 0 &&
                variationItemsDropdownList.find(
                    (item) => item.value === overrideItems[i].variationItems[0],
                ) === undefined &&
                overrideItems[i].variation === selectedVariation
            ) {
                overrideItemTemp.variationItems = [];
            }

            overrideItemsTemp.push(overrideItemTemp);
        }

        setOverrideItems(overrideItemsTemp);
    }, [variationItemsDropdownList, variationItemsB1F1DropdownList]);

    ////////////////////////////////////////////////////////
    // Retrieve Data for States

    useEffect(() => {
        setCrmUserTagsDropdownList(
            TARGET_USER_GROUP_DROPDOWN_LIST.concat(
                crmUserTags.map((item) => ({ label: item.name, value: item.uniqueId })),
            ),
        );
    }, [crmUserTags]);

    useEffect(() => {
        var outletCategoriesTemp = [...outletCategoriesUnsorted];

        outletCategoriesTemp.sort((a, b) => a.name.localeCompare(b.name));

        setOutletCategories(outletCategoriesTemp);
    }, [outletCategoriesUnsorted]);

    useEffect(() => {
        const outletItemsTemp = outletItemsUnsorted.filter(item => outletCategoriesDict[item.categoryId]);
    
        outletItemsTemp.sort((a, b) => a.name.localeCompare(b.name));
    
        setOutletItems(outletItemsTemp);
    }, [outletItemsUnsorted]);

    useEffect(() => {
        setOutletDropdownList(
            allOutlets.map((item) => ({
                label: item.name,
                value: item.uniqueId,
            })),
        );
    }, [allOutlets]);

    useEffect(() => {
        const merchantTaggableVouchers = taggableVouchers.filter(item => item.merchantId === merchantId);

        let tempTaggableVoucherDDL = merchantTaggableVouchers.map((item) => ({
            label: item.campaignName,
            value: item,
        }));
        
        tempTaggableVoucherDDL.unshift({ label: '━━━━━━━━━━━━━━', value: '', disabled: true });
        tempTaggableVoucherDDL.unshift({ label: 'Create New Voucher', value: null });

        setTaggableVoucherDropdownList(tempTaggableVoucherDDL);
    },[taggableVouchers, merchantId]);
    
    ///////////////////////////////////////////////////////

    // Data Initialization
    useEffect(() => {
        // Create New Voucher - Reset State
        if(!selectedTaggableVoucherEdit){
            //Step 1 Data
            setVoucherType(LOYALTY_PROMOTION_TYPE_DROPDOWN_LIST[0].value);
            setApplyDiscountPer(APPLY_DISCOUNT_PER_DROPDOWN_LIST[0].value);
            setMinSpend(0);

            setOverrideItems([{
                priceBeforeTax: 0,
                variation: PROMOTION_TYPE_VARIATION.SPECIFIC_PRODUCTS,
                variationItems: [],
                variationItemsSku: [],
            }]);

            setBuyInput(1);
            setGetInput(1);
            setGetPriceInput(0);
            setSelectedVariation(PROMOTION_TYPE_VARIATION.SPECIFIC_PRODUCTS);
            setSelectedVariationItems([]);
            setSelectedVariationB1F1(PROMOTION_TYPE_VARIATION.SPECIFIC_PRODUCTS);
            setSelectedVariationItemsB1F1([]);

            setAmountOffPrice(0);
            setAmountOffMinQuantity(1);
            setAmountOffMaxQuantity(1);

            setPercentageOffPrice(0);
            setPercentageOffMinQuantity(1);
            setPercentageOffMaxQuantity(1);

            setDeliveryFreeFlag(false);
            setDeliveryFreeAboveAmount(0);
            setDeliveryDiscountAmount(0);
            setDeliveryDiscountAboveAmount(0);

            setTakeawayFreeFlag(false);
            setTakeawayFreeAboveAmount(0);
            setTakeawayDiscountAmount(0);
            setTakeawayDiscountAboveAmount(0);

            //Step 2 Data
            LoyaltyStore.update((s) => {
                s.name = '';
                s.description = '';
                s.orderType = ([CHANNEL_TYPE_DROP_DOWN_LIST[0].value]);
                s.outletIdList = [];
                s.promoDateStart = moment();
                s.promoDateEnd =  moment();
                s.promoTimeStart = moment();
                s.promoTimeEnd = moment();
                s.effectiveTypeOption = [];
            })
            setIsFreeToClaimVoucher(false);

            //Step 3 Data
            setVoucherCodeFormat(MERCHANT_VOUCHER_CODE_FORMAT.UNIQUE);
            setVoucherCodeValueGeneric('');
            setVoucherQuantity(100);
            setVoucherMaxClaimPerUser(10);
            setTargetSegmentGroupList([]);
            setVoucherTerms('');
            setIsAvailableOnline(false);
            setUserInfoToCollectedList([]);
            setSelectedVoucherRedeemType(VOUCHER_REDEEM_TYPE.USE_LATER);
            setActivationDays(VOUCHER_ACTIVATION_DAYS_DROPDOWN_LIST[0].value);
            setExpirationDays(VOUCHER_EXPIRATION_DAYS_DROPDOWN_LIST[7].value);
            setToClaimMinSpent(0);
            setVoucherPointsRequired(0);

            setNotificationTitle('');
            setNotificationDescription('');
            setIsAutoPush(false);
            setIsPushToSMS(false);
            setIsPushToApp(true);
            setIsPushToEmail(false);
            setNotificationDate(moment());
            setNotificationTime(moment())
            setNotificationText('Hi %userName%, we have sent you a voucher of a free cup of coffee expire on %expiryDate%, visit %outletName% to redeem now! Expires on %expiryDate%');
        }
        // Edit Existing Voucher - Read State
        else{
            //Step 1 Data
            setVoucherType(selectedTaggableVoucherEdit.voucherType);
            setApplyDiscountPer(selectedTaggableVoucherEdit.applyDiscountPer);
            setMinSpend(selectedTaggableVoucherEdit.minSpend);

            switch(selectedTaggableVoucherEdit.voucherType) {
                case LOYALTY_PROMOTION_TYPE.OVERRIDE_EXISTING_PRICE:
                    setOverrideItems(selectedTaggableVoucherEdit.criteriaList.map(criteria => {
                        var variationItemsNew = [];
              
                        if (criteria.variation === PROMOTION_TYPE_VARIATION.SPECIFIC_PRODUCTS) {
                            variationItemsNew = criteria.variationItems.filter(findItem => {
                                if (outletItemsUnsorted.find(compareItem => compareItem.uniqueId === findItem)) {
                                    return true;
                                }
                                else {
                                    return false;
                                }
                            });
                        }
                        else if (criteria.variation === PROMOTION_TYPE_VARIATION.PRODUCT_OF_CATEGORY) {
                            variationItemsNew = criteria.variationItems.filter(findItem => {
                                if (outletCategoriesUnsorted.find(compareItem => compareItem.uniqueId === findItem)) {
                                    return true;
                                }
                                else {
                                    return false;
                                }
                            });
                        }
              
                        return {
                          ...criteria,
                          variationItems: variationItemsNew,
                        };
                    }));

                    break;
                case LOYALTY_PROMOTION_TYPE.BUY_A_GET_B_FOR_FREE:
                    selectedTaggableVoucherEdit.criteriaList.forEach(criteria => {
                        setBuyInput(criteria.buyAmount);
                        setGetInput(criteria.getAmount);
                        setGetPriceInput(criteria.getPrice);

                        setSelectedVariation(criteria.buyVariation);
                        if (criteria.buyVariation === PROMOTION_TYPE_VARIATION.SPECIFIC_PRODUCTS) {
                            setSelectedVariationItems(criteria.buyVariationItems.filter(findItem => {
                                if (outletItemsUnsorted.find(compareItem => compareItem.uniqueId === findItem)) {
                                    return true;
                                }
                                else {
                                    return false;
                                }
                            }));
                        }
                        else if (criteria.buyVariation === PROMOTION_TYPE_VARIATION.PRODUCT_OF_CATEGORY) {
                            setSelectedVariationItems(criteria.buyVariationItems.filter(findItem => {
                                if (outletCategoriesUnsorted.find(compareItem => compareItem.uniqueId === findItem)) {
                                    return true;
                                }
                                else {
                                    return false;
                                }
                            }));
                        }

                        setSelectedVariationB1F1(criteria.getVariation);
                        if (criteria.getVariation === PROMOTION_TYPE_VARIATION.SPECIFIC_PRODUCTS) {
                            setSelectedVariationItemsB1F1(criteria.getVariationItems.filter(findItem => {
                                if (outletItemsUnsorted.find(compareItem => compareItem.uniqueId === findItem)) {
                                    return true;
                                }
                                else {
                                    return false;
                                }
                            }));
                        }
                        else if (criteria.getVariation === PROMOTION_TYPE_VARIATION.PRODUCT_OF_CATEGORY) {
                            setSelectedVariationItemsB1F1(criteria.getVariationItems.filter(findItem => {
                                if (outletCategoriesUnsorted.find(compareItem => compareItem.uniqueId === findItem)) {
                                    return true;
                                }
                                else {
                                    return false;
                                }
                            }));
                        }
                    });
                    break;
                case LOYALTY_PROMOTION_TYPE.TAKE_AMOUNT_OFF:
                    selectedTaggableVoucherEdit.criteriaList.forEach(criteria => {
                        setAmountOffPrice(criteria.amountOff);
                        setAmountOffMinQuantity(criteria.minQuantity);
                        setAmountOffMaxQuantity(criteria.maxQuantity);
                        setSelectedVariation(criteria.variation);

                        let variationItems = [];
                        if (criteria.variation === PROMOTION_TYPE_VARIATION.SPECIFIC_PRODUCTS) {
                            variationItems = criteria.variationItems.filter(findItem => 
                                outletItemsUnsorted.some(compareItem => compareItem.uniqueId === findItem)
                            );
                        } else if (criteria.variation === PROMOTION_TYPE_VARIATION.PRODUCT_OF_CATEGORY) {
                            variationItems = criteria.variationItems.filter(findItem => 
                                outletCategoriesUnsorted.some(compareItem => compareItem.uniqueId === findItem)
                            );
                        }

                        setSelectedVariationItems(variationItems);
                    });
                    break;
                case LOYALTY_PROMOTION_TYPE.TAKE_PERCENTAGE_OFF:
                    selectedTaggableVoucherEdit.criteriaList.forEach(criteria => {
                        setPercentageOffPrice(criteria.percentageOff);
                        setPercentageOffMinQuantity(criteria.minQuantity);
                        setPercentageOffMaxQuantity(criteria.maxQuantity);
                        setSelectedVariation(criteria.variation);

                        let variationItems = [];
                        if (criteria.variation === PROMOTION_TYPE_VARIATION.SPECIFIC_PRODUCTS) {
                            variationItems = criteria.variationItems.filter(findItem => 
                                outletItemsUnsorted.some(compareItem => compareItem.uniqueId === findItem)
                            );
                        } else if (criteria.variation === PROMOTION_TYPE_VARIATION.PRODUCT_OF_CATEGORY) {
                            variationItems = criteria.variationItems.filter(findItem => 
                                outletCategoriesUnsorted.some(compareItem => compareItem.uniqueId === findItem)
                            );
                        }

                        setSelectedVariationItems(variationItems);
                    });
                    break;
                case LOYALTY_PROMOTION_TYPE.DELIVERY:
                    selectedTaggableVoucherEdit.criteriaList.forEach(criteria => {
                        setDeliveryFreeFlag(criteria.deliveryFreeFlag);
                        setDeliveryFreeAboveAmount(criteria.deliveryFreeAboveAmount);
                        setDeliveryDiscountAmount(criteria.deliveryDiscountAmount);
                        setDeliveryDiscountAboveAmount(criteria.deliveryDiscountAboveAmount);
                        setSelectedVariation(criteria.variation);

                        let variationItems = [];
                        if (criteria.variation === PROMOTION_TYPE_VARIATION.SPECIFIC_PRODUCTS) {
                            variationItems = criteria.variationItems.filter(findItem => 
                                outletItemsUnsorted.some(compareItem => compareItem.uniqueId === findItem)
                            );
                        } else if (criteria.variation === PROMOTION_TYPE_VARIATION.PRODUCT_OF_CATEGORY) {
                            variationItems = criteria.variationItems.filter(findItem => 
                                outletCategoriesUnsorted.some(compareItem => compareItem.uniqueId === findItem)
                            );
                        }

                        setSelectedVariationItems(variationItems);
                    });
                    break;
                case LOYALTY_PROMOTION_TYPE.TAKEAWAY:
                    selectedTaggableVoucherEdit.criteriaList.forEach(criteria => {
                        setTakeawayFreeFlag(criteria.takeawayFreeFlag);
                        setTakeawayFreeAboveAmount(criteria.takeawayFreeAboveAmount);
                        setTakeawayDiscountAmount(criteria.takeawayDiscountAmount);
                        setTakeawayDiscountAboveAmount(criteria.takeawayDiscountAboveAmount);
                        setSelectedVariation(criteria.variation);

                        let variationItems = [];
                        if (criteria.variation === PROMOTION_TYPE_VARIATION.SPECIFIC_PRODUCTS) {
                            variationItems = criteria.variationItems.filter(findItem => 
                                outletItemsUnsorted.some(compareItem => compareItem.uniqueId === findItem)
                            );
                        } else if (criteria.variation === PROMOTION_TYPE_VARIATION.PRODUCT_OF_CATEGORY) {
                            variationItems = criteria.variationItems.filter(findItem => 
                                outletCategoriesUnsorted.some(compareItem => compareItem.uniqueId === findItem)
                            );
                        }

                        setSelectedVariationItems(variationItems);
                    });
                    break;
                default: 
                    break;
            }

            //Step 2 Data
            LoyaltyStore.update((s) => {
                s.name = selectedTaggableVoucherEdit.campaignName;
                s.description = selectedTaggableVoucherEdit.campaignDescription;
                s.orderType = selectedTaggableVoucherEdit.orderTypes;
                s.outletIdList = selectedTaggableVoucherEdit.outletIdList;
                s.promoDateStart = selectedTaggableVoucherEdit.promoDateStart;
                s.promoDateEnd = selectedTaggableVoucherEdit.promoDateEnd;
                s.promoTimeStart = selectedTaggableVoucherEdit.promoTimeStart;
                s.promoTimeEnd = selectedTaggableVoucherEdit.promoTimeEnd;
                s.effectiveTypeOption = selectedTaggableVoucherEdit.effectiveTypeOptions;
            })
            setIsFreeToClaimVoucher(selectedTaggableVoucherEdit.isFreeToClaimVoucher);

            //Step 3 Data
            setVoucherCodeFormat(selectedTaggableVoucherEdit.voucherCodeFormat);
            setVoucherCodeValueGeneric(selectedTaggableVoucherEdit.voucherCodeValueGeneric);
            setVoucherQuantity(selectedTaggableVoucherEdit.voucherQuantity);
            setVoucherMaxClaimPerUser(selectedTaggableVoucherEdit.voucherMaxClaimPerUser);
            setTargetSegmentGroupList(selectedTaggableVoucherEdit.targetSegmentGroupList);
            setVoucherTerms(selectedTaggableVoucherEdit.voucherTerms);
            setIsAvailableOnline(selectedTaggableVoucherEdit.isAvailableOnline);
            setUserInfoToCollectedList(selectedTaggableVoucherEdit.userInfoToCollectedList);
            setSelectedVoucherRedeemType(selectedTaggableVoucherEdit.voucherRedeemType);
            setActivationDays(selectedTaggableVoucherEdit.activationDays);
            setExpirationDays(selectedTaggableVoucherEdit.expirationDays);
            setToClaimMinSpent(selectedTaggableVoucherEdit.toClaimMinSpent);
            setVoucherPointsRequired(selectedTaggableVoucherEdit.voucherPointsRequired);

            setNotificationTitle(selectedTaggableVoucherEdit.notification.title);
            setNotificationDescription(selectedTaggableVoucherEdit.notification.description);
            setIsAutoPush(selectedTaggableVoucherEdit.notification.isAutoPush);
            setIsPushToSMS(selectedTaggableVoucherEdit.notification.isPushToSMS);
            setIsPushToApp(selectedTaggableVoucherEdit.notification.isPushToApp);
            setIsPushToEmail(selectedTaggableVoucherEdit.notification.isPushToEmail);
            setNotificationDate(moment(selectedTaggableVoucherEdit.notification.date));
            setNotificationTime(moment(selectedTaggableVoucherEdit.notification.time))
            setNotificationText(selectedTaggableVoucherEdit.notificationText);
        }
    }, [selectedTaggableVoucherEdit])

    ///////////////////////////  Local Methods ///////////////////////////
    // const deleteId = ''
    // useEffect(async () => {
    //     await firestore().collection(Collections.TaggableVoucher).doc({deleteId}).delete();
    // }, [])

    // Text Color Changer depends on Text.length
    const getNotificationTextCountColor = (length) => {
        if (length >= 140 && length <= 149) {
          return Colors.tabYellow;
        } 
        else if (length === 150) {
          return Colors.tabRed;
        } 
        else {
          return Colors.fieldtTxtColor;
        }
    };

    // Condition Checking before Create Voucher
    const checkCondition = () => {
        let errors = [];

        const validateField = (condition, message) => {
            if (!condition) errors.push(message);
        };

        // Validate Step 1 Fields
        switch(voucherType) {
            case LOYALTY_PROMOTION_TYPE.OVERRIDE_EXISTING_PRICE:
                validateField(!Number.isNaN(parseFloat(minSpend)), "Invalid Min Spend Amount(RM)");
                for (let i = 0; i < overrideItems.length; i++) {
                    validateField(!Number.isNaN(parseFloat(overrideItems[i].priceBeforeTax)), `Invalid priceBeforeTax for item ${i + 1}`);
                    validateField(overrideItems[i].variationItems.length > 0, "Select at least one Item. (overrideItems.variationItems)");
                }
                break;
            case LOYALTY_PROMOTION_TYPE.BUY_A_GET_B_FOR_FREE:
                validateField(!Number.isNaN(parseFloat(minSpend)), "Invalid Min Spend Amount(RM)");
                validateField(buyInput >= 1, "Invalid Buy Input Amount");
                validateField(getInput >= 1, "Invalid Get Input Amount");
                validateField(parseFloat(getPriceInput).toFixed(2) >= 0, "Invalid Bundle Price(RM)")
                validateField(selectedVariationItems.length > 0, "Select at least one Item. (selectedVariationItems)");
                validateField(selectedVariationItemsB1F1.length >= 1, "Select at least one Product.")
                break;
            case LOYALTY_PROMOTION_TYPE.TAKE_AMOUNT_OFF:
                validateField(!Number.isNaN(parseFloat(minSpend)), "Invalid Min Spend Amount(RM)");
                validateField(!Number.isNaN(parseFloat(amountOffPrice)), "Invalid Amount Off Number");
                validateField(!Number.isNaN(parseInt(amountOffMinQuantity, 10)), "Invalid Minimum Quantity");
                validateField(!Number.isNaN(parseInt(amountOffMaxQuantity, 10)), "Invalid Maximum Quantity");
                break;
            case LOYALTY_PROMOTION_TYPE.TAKE_PERCENTAGE_OFF:
                validateField(!Number.isNaN(parseFloat(minSpend)), "Invalid Min Spend Amount(RM)");
                validateField(!Number.isNaN(parseFloat(percentageOffPrice)), "Invalid Percentage Off Number");
                validateField(!Number.isNaN(parseInt(percentageOffMinQuantity, 10)), "Invalid Minimum Quantity");
                validateField(!Number.isNaN(parseInt(percentageOffMaxQuantity, 10)), "Invalid Maximum Quantity");
                break;
            case LOYALTY_PROMOTION_TYPE.DELIVERY:
                validateField(!Number.isNaN(parseFloat(deliveryFreeAboveAmount)), "Invalid deliveryFreeAboveAmount");
                validateField(!Number.isNaN(parseFloat(deliveryDiscountAmount)), "Invalid deliveryDiscountAmount");
                validateField(!Number.isNaN(parseFloat(deliveryDiscountAboveAmount)), "Invalid deliveryDiscountAboveAmount");
                break;
            case LOYALTY_PROMOTION_TYPE.TAKEAWAY:
                validateField(!Number.isNaN(parseFloat(takeawayFreeAboveAmount)), "Invalid takeawayFreeAboveAmount");
                validateField(!Number.isNaN(parseFloat(takeawayDiscountAmount)), "Invalid takeawayDiscountAmount");
                validateField(!Number.isNaN(parseFloat(takeawayDiscountAboveAmount)), "Invalid takeawayDiscountAboveAmount");
                break;
            default: 
                break;
        }

        // Validate Step 2 Fields
        validateField(campaignName.trim().length > 0, 'Voucher name must be filled');
        validateField(campaignDescription.trim().length > 0, 'Voucher description must be filled');

        validateField(orderTypes.length > 0, 'Availability (Apply to Channel) must be selected');
        validateField(outletIdList.length > 0, 'Outlet(s) must be selected');
        validateField(effectiveTypeOptions.length > 0, "Invalid Effective type option")

        // Validate Step 3 Fields
        validateField(
            voucherCodeFormat !== MERCHANT_VOUCHER_CODE_FORMAT.GENERIC || voucherCodeValueGeneric.trim().length > 0,
            'Generic voucher code must be filled, if not using the unique code format'
        );

        validateField(!Number.isNaN(parseInt(voucherQuantity, 10)), "Invalid voucher quantity")
        validateField((parseInt(voucherQuantity, 10) > 0 && parseInt(voucherQuantity, 10) <= 10000), "Voucher quantity must in the between of 1 and 10000");
        validateField(!Number.isNaN(parseInt(voucherMaxClaimPerUser, 10)), "Invalid voucher max claim per user")
        validateField(targetSegmentGroupList.length > 0, "Select Target Segment Group")
        validateField(voucherTerms.trim().length > 0, "Voucher Terms must be filled")
        // userInfoToCollectedList
        // selectedVoucherRedeemType
        // activationDays
        // expirationDays
        validateField(!Number.isNaN(parseFloat(toClaimMinSpent)), "Invalid Min. Spent to Claim")
        validateField(!Number.isNaN(parseInt(voucherPointsRequired, 10)), "Invalid Point Required Amount")
        
        validateField(notificationText.length > 0, 'Notification/SMS message must be filled');

        if (errors.length > 0) {
            Alert.alert('Error', errors.join('\n'));
        }
        
        return errors.length === 0;
    }
    
    // Handle Create/Save button actions
    const handleButtonAction = async () => {
        CommonStore.update((s) => {s.isLoading = true});

        // Outet Name List
        let outletNameList = allOutlets
            .filter(outlet => outletIdList.some(id => id.includes(outlet.uniqueId)))
            .map(outlet => outlet.name);

        // Taxes (Currently are fixed value, unsure about selectedTax yet)
        let selectedTax = '';
        let { name: taxName = '', rate: taxRate = 0.05 } = currOutletTaxes.find(tax => tax.uniqueId === selectedTax) || {};

        // Notification Object Creation
        let notification = {
            title: notificationTitle,
            description: notificationDescription,
            isPushToApp,
            isPushToEmail,
            isPushToSMS,
            date: moment(notificationDate).valueOf(),
            time: moment(notificationTime).valueOf(),
            isAutoPush,
        };

        // Map criteriaList into Object
        let criteriaList = [];
        switch(voucherType) {
            case LOYALTY_PROMOTION_TYPE.OVERRIDE_EXISTING_PRICE:
                criteriaList = overrideItems.map(overrideItem => {
                    return {
                      ...overrideItem,
                      priceBeforeTax: parseFloat(overrideItem.priceBeforeTax),
                    }
                });
                break;
            case LOYALTY_PROMOTION_TYPE.BUY_A_GET_B_FOR_FREE:
                criteriaList.push({
                    buyAmount: parseInt(buyInput, 10),
                    getAmount: parseInt(getInput, 10),
                    getPrice: parseInt(getPriceInput, 10),
                    buyVariation: selectedVariation,
                    buyVariationItems: selectedVariationItems,
                    buyVariationItemsSku: selectedVariationItemsSku,
                    getVariation: selectedVariationB1F1,
                    getVariationItems: selectedVariationItemsB1F1,
                    getVariationItemsSku: selectedVariationItemsSkuB1F1,
                  });
                break;
            case LOYALTY_PROMOTION_TYPE.TAKE_AMOUNT_OFF:
                criteriaList.push({
                    amountOff: parseFloat(amountOffPrice),
                    minQuantity: parseInt(amountOffMinQuantity, 10),
                    maxQuantity: parseInt(amountOffMaxQuantity, 10),
                    variation: selectedVariation,
                    variationItems: selectedVariationItems,
                    variationItemsSku: selectedVariationItemsSku,
          
                    quantityMin: parseInt(amountOffMinQuantity, 10),
                    quantityMax: parseInt(amountOffMaxQuantity, 10),
                    priceMin: null,
                    priceMax: null,
                });
                break;
            case LOYALTY_PROMOTION_TYPE.TAKE_PERCENTAGE_OFF:
                criteriaList.push({
                    percentageOff: parseFloat(percentageOffPrice),
                    minQuantity: parseInt(percentageOffMinQuantity, 10),
                    maxQuantity: parseInt(percentageOffMaxQuantity, 10),
                    variation: selectedVariation,
                    variationItems: selectedVariationItems,
                    variationItemsSku: selectedVariationItemsSku,
          
                    quantityMin: parseInt(percentageOffMinQuantity, 10),
                    quantityMax: parseInt(percentageOffMaxQuantity, 10),
                    priceMin: null,
                    priceMax: null,
                });
                break;
            case LOYALTY_PROMOTION_TYPE.DELIVERY:
                criteriaList.push({
                    deliveryFreeFlag,
                    deliveryFreeAboveAmount: parseFloat(deliveryFreeAboveAmount),
                    deliveryDiscountAmount: parseFloat(deliveryDiscountAmount),
                    deliveryDiscountAboveAmount: parseFloat(deliveryDiscountAboveAmount),
                    variation: selectedVariation,
                    variationItems: selectedVariationItems,
                    variationItemsSku: selectedVariationItemsSku,
                  });
                break;
            case LOYALTY_PROMOTION_TYPE.TAKEAWAY:
                criteriaList.push({
                    takeawayFreeFlag,
                    takeawayFreeAboveAmount: parseFloat(takeawayFreeAboveAmount),
                    takeawayDiscountAmount: parseFloat(takeawayDiscountAmount),
                    takeawayDiscountAboveAmount: parseFloat(takeawayDiscountAboveAmount),
                    variation: selectedVariation,
                    variationItems: selectedVariationItems,
                    variationItemsSku: selectedVariationItemsSku,
                });
                break;
            default: 
                break;
        }
        
        if (checkCondition()) {
            const taggableVoucherBody = {
                merchantId,
                merchantName,

                // Step 1
                voucherType,
                applyDiscountPer,
                minSpend: parseFloat(minSpend),
                criteriaList,

                // Step 2
                campaignName,
                campaignDescription,

                orderTypes,
                outletIdList,
                outletNameList,

                promoDateStart: moment(promoDateStart).valueOf(),
                promoDateEnd: moment(promoDateEnd).valueOf(),
                promoTimeStart: moment(promoTimeStart).valueOf(),
                promoTimeEnd: moment(promoTimeEnd).valueOf(),

                effectiveTypeOptions,
                isFreeToClaimVoucher,

                // Step 3
                voucherCodeValueGeneric,
                voucherCodeFormat,
                voucherQuantity: parseInt(voucherQuantity, 10),
                voucherMaxClaimPerUser: parseInt(voucherMaxClaimPerUser, 10),
                targetSegmentGroupList,
                voucherTerms,
                isAvailableOnline,
                userInfoToCollectedList,
                voucherRedeemType,
                activationDays,
                expirationDays,
                toClaimMinSpent,
                voucherPointsRequired: parseInt(voucherPointsRequired, 10),

                notification,
                notificationText,

                //isEnableSellOnline,

                //targetUserGroup,
                //promoCode,
                //isPromoCodeUsageLimit,
                //promoCodeUsageLimit,
                //image,
                //commonId,
                //effectiveType,
                
                //effectiveDay,
                //effectiveTimeStart,
                //effectiveTimeEnd,

                //isEditNotification,

                currOutletTaxName: taxName,
                currOutletTaxRate: taxRate,
                currOutletTaxId: selectedTax,
                currOutletId,

                /////////////////////////////////////////////////////////////

                //loyaltyCampaignId,
                //promotionId,

                //voucherCodeUniqueLength,
                //voucherCodeUniquePrefix,

                //creditToBuy,
            }

            // Create Voucher
            if (!selectedTaggableVoucherEdit){
                APILocal.createTaggableVoucher({ body: taggableVoucherBody, uid: userId })
                    .then((result) => {
                        console.log("CREATE VOUCHER RESULT:", result)
                        // Success - Voucher Creation
                        if (result && result.status === 'success') {
                            voucherBinding(result.taggableVoucher.uniqueId);
                        }

                        // Failed - Voucher Creation
                        else{
                            Alert.alert(
                                'Error',
                                `${result.status}: ${result.message}`,
                                [
                                    {
                                        text: 'OK',
                                    },
                                ],
                                { cancelable: false }
                            );
                        }
                    });
            }

            // Update Voucher
            else{
                const body = {...taggableVoucherBody, voucherId: selectedTaggableVoucherEdit.uniqueId};
                
                APILocal.updateTaggableVoucher({ body, uid: userId })
                    .then((result) => {
                        // Success - Voucher Update
                        if (result && result.status === 'success') {
                            voucherBinding(selectedTaggableVoucherEdit.uniqueId);
                        }

                        // Failed - Voucher Update
                        else{
                            Alert.alert(
                                'Error',
                                `${result.status}: ${result.message}`,
                                [
                                    {
                                        text: 'OK',
                                    },
                                ],
                                { cancelable: false }
                            );
                        }
                    });
            }
        }

        CommonStore.update((s) => {s.isLoading = false});
    }

    const voucherBinding = (taggableVoucherId) => {
        let body = {
            merchantId,
            merchantName,

            campaignName: recommendedLoyalty.campaignName,
            campaignDescription: recommendedLoyalty.campaignDescription,

            // isEnableSellOnline,

            orderTypes: recommendedLoyalty.orderTypes,

            // targetUserGroup,
            targetSegmentGroupList: recommendedLoyalty.targetSegmentGroupList,
            // promoCode,
            // isPromoCodeUsageLimit,
            // promoCodeUsageLimit,
            image: recommendedLoyalty.image,
            // commonId,
            effectiveType: recommendedLoyalty.effectiveType,
            effectiveTypeOptions: recommendedLoyalty.effectiveTypeOption,
            // effectiveDay,
            // effectiveTimeStart,
            // effectiveTimeEnd,

            // minSpend,

            // promoDateStart,
            // promoDateEnd,
            // promoTimeStart,
            // promoTimeEnd,

            // promotionType,

            // criteriaList,

            // isEditNotification,
            // notification,

            // outletIdList,
            // outletNameList,

            // currOutletTaxName,
            // currOutletTaxRate,
            // currOutletTaxId,
            // currOutletId,

            loyaltyCampaignType: recommendedLoyalty.loyaltyCampaignType,
            loyaltyCriteriaList: recommendedLoyalty.loyaltyCriteriaList,

            // notificationText,

            //////////////////////////////////////////////////////

            taggableVoucherId: [taggableVoucherId],
            batchList: recommendedLoyalty.batchList,
        };

        APILocal.createLoyaltyCampaign({ body, uid: userId })
            .then((loyaltyResult) => {
                // Success - Loyalty Promotion Creation
                if (loyaltyResult && loyaltyResult.status === 'success') {
                    console.log("CREATED LOYALTY CAMPAIGN:", loyaltyResult.promotion.uniqueId);
                    
                    Alert.alert(
                        'Success',
                        'Voucher and loyalty campaign have been created successfully.',
                        [
                            {
                                text: 'OK',
                                onPress: () => {
                                    props.navigation.navigate('TaggableVoucherList');
                                },
                            },
                        ],
                        { cancelable: false },
                    );
                }

                // Failed
                else{
                    console.error("Failed to create loyalty campaign:", loyaltyResult.message);
                    Alert.alert(
                        'Error',
                        `Failed to create loyalty campaign: ${loyaltyResult.status}: ${loyaltyResult.message}`,
                        [
                            {
                                text: 'OK',
                            },
                        ],
                        { cancelable: false }
                    );
                }
            });
    }
    
    /////////////////////////////////////////////////

    //Header
    navigation.setOptions({
        headerLeft: () => (
            <TouchableOpacity
                onPress={() => {
                    if (isAlphaUser || true) {
                        navigation.navigate('MenuOrderingScreen');

                        CommonStore.update((s) => {
                            s.currPage = 'MenuOrderingScreen';
                            s.currPageStack = [...currPageStack, 'MenuOrderingScreen'];
                        });
                    }
                    else {
                        navigation.navigate('Table');

                        CommonStore.update((s) => {
                            s.currPage = 'Table';
                            s.currPageStack = [...currPageStack, 'Table'];
                        });
                    }
                    if (expandTab !== EXPAND_TAB_TYPE.OPERATION) {
                        CommonStore.update((s) => {
                            s.expandTab = EXPAND_TAB_TYPE.OPERATION;
                        });
                    }
                }}
                style={styles.headerLeftStyle}>
                <Image
                    style={[{
                        width: 124,
                        height: 26,
                    }, switchMerchant ? {
                        transform: [
                            { scaleX: 0.7 },
                            { scaleY: 0.7 }
                        ],
                    } : {}]}
                    resizeMode="contain"
                    source={require('../assets/image/logo.png')}
                />
            </TouchableOpacity>
        ),
        headerTitle: () => (
            <View
                style={[
                    {
                        // justifyContent: Platform.OS === 'android' ? 'center' : 'flex-start',
                        // alignItems: Platform.OS === 'android' ? 'center' : 'flex-start',
                        // marginRight: Platform.OS === 'ios' ? "27%" : 0,
                        // bottom: switchMerchant ? '2%' : 0,
                        // width: switchMerchant ? '100%' : Platform.OS === 'ios' ? "96%" : "55%",
                        ...global.getHeaderTitleStyle(),
                    },
                    // windowWidth >= 768 && switchMerchant
                    //     ? { right: windowWidth * 0.1 }
                    //     : {},
                    // windowWidth <= 768
                    //     ? { right: 20 }
                    //     : {},
                ]}>
                <Text
                    style={{
                        fontSize: switchMerchant ? 20 : 24,
                        // lineHeight: 25,
                        textAlign: 'left',
                        alignItems: 'flex-start',
                        justifyContent: 'flex-start',
                        fontFamily: 'NunitoSans-Bold',
                        color: Colors.whiteColor,
                        opacity: 1,
                        paddingLeft: Platform.OS === 'ios' ? "1%" : 0,
                    }}>
                    Voucher
                </Text>
            </View>
        ),
        headerRight: () => (
            <View style={{
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'space-between',
            }}>

                {outletSelectDropdownView()}

                <TouchableOpacity
                    onPress={() => navigation.navigate('Setting')}
                    style={{ flexDirection: 'row', alignItems: 'center' }}
                    >

                    <Text
                        style={[{
                            fontFamily: 'NunitoSans-SemiBold',
                            fontSize: switchMerchant ? 10 : 16,
                            color: Colors.secondaryColor,
                            marginRight: 15,
                        }, switchMerchant ? { width: windowWidth / 8 } : {}]}
                        numberOfLines={switchMerchant ? 1 : 1}
                    >
                        {userName}
                    </Text>

                    <View style={{
                        marginRight: 30,
                        width: windowHeight * 0.05,
                        height: windowHeight * 0.05,
                        borderRadius: windowHeight * 0.05 * 0.5,
                        alignItems: 'center',
                        justifyContent: 'center',
                        backgroundColor: 'white',
                    }}>
                        <Image style={{
                            width: windowHeight * 0.035,
                            height: windowHeight * 0.035,
                            alignSelf: 'center',
                        }}
                            source={require('../assets/image/profile-pic.jpg')}
                        />
                    </View>
                </TouchableOpacity>
            </View>
        ),
    });

    /////////////////////////////////////////////////

    const styles = StyleSheet.create({
        container: {
            flex: 1,
            backgroundColor: Colors.highlightColor,
            flexDirection: 'row',
        },
        sidebar: {
            shadowColor: '#000',
            shadowOffset: {
                width: 0,
                height: 8,
            },
            shadowOpacity: 0.44,
            shadowRadius: 10.32,
    
            elevation: 16,
        },
        headerLeftStyle: {
            width: Dimensions.get('window').width * 0.17,
            justifyContent: 'center',
            alignItems: 'center',
        },
        modalView: {
            height: 500,
            width: 415,
            backgroundColor: Colors.whiteColor,
            borderRadius: Dimensions.get('window').width * 0.03,
            padding: 12,
            paddingTop: 25,
        },
        modalView1: {
            height:
                Platform.OS === 'ios'
                    ? Dimensions.get('window').width * 0.7
                    : Dimensions.get('window').width * 0.6,
            width:
                Platform.OS === 'ios'
                    ? Dimensions.get('window').width * 0.7
                    : Dimensions.get('window').width * 0.6,
            backgroundColor: Colors.whiteColor,
            borderRadius: Dimensions.get('window').width * 0.03,
            padding: 20,
            paddingTop: 25,
        },
        modalView2: {
            height:
                Platform.OS === 'ios'
                    ? Dimensions.get('window').width * 0.35
                    : Dimensions.get('window').width * 0.25,
            width:
                Platform.OS === 'ios'
                    ? Dimensions.get('window').width * 0.5
                    : Dimensions.get('window').width * 0.4,
            backgroundColor: Colors.whiteColor,
            borderRadius: Dimensions.get('window').width * 0.03,
            padding: 20,
            paddingTop: 25,
        },
        modalContainer1: {
            flex: 1,
            backgroundColor: Colors.modalBgColor,
            alignItems: 'center',
            justifyContent: 'center',
        },
        closeButton: {
            position: 'relative',
            alignSelf: 'flex-end',
            marginRight: -10,
            marginTop: -15,
    
            elevation: 1000,
            zIndex: 1000,
            backgroundColor: Colors.whiteColor,
            borderRadius: 12,
        },
        modalSaveButton: {
            width: Dimensions.get('window').width * 0.15,
            backgroundColor: Colors.fieldtBgColor,
            height: 40,
            alignItems: 'center',
            justifyContent: 'center',
            borderRadius: 8,
    
            shadowOffset: {
                width: 0,
                height: 1,
            },
            shadowOpacity: 0.22,
            shadowRadius: 3.22,
            elevation: 1,
    
            marginVertical: 10,
        },
        stepText: {
            fontFamily: 'NunitoSans-Bold', 
            fontSize: 20,
            marginBottom: 10,
        },
        divider: {
            borderWidth: 1,
            borderColor: Colors.blackColor,
            opacity: 0.2,
        },
        textInput: {
            fontFamily: 'NunitoSans-Regular',
            width: 60,
            height: 40,
            flex: 1,
            backgroundColor: Colors.fieldtBgColor,
            borderRadius: 5,
            paddingHorizontal: 5,
            borderColor: '#E5E5E5',
            borderWidth: 1,
        },
        textInputTitle: {
            fontWeight: '500',
            fontFamily: 'NunitoSans-Bold',
            fontSize: 14,
            color: Colors.blackColor
        },
        textInputField: {
            backgroundColor: Colors.fieldtBgColor,
            width: '80%',
            height: windowHeight * 0.07,
            padding: 5,
            marginTop: 5,
            borderWidth: 1,
            borderRadius: 5,
            borderColor: '#E5E5E5',
            paddingLeft: 10,
            fontFamily: 'NunitoSans-Regular',
            fontSize: 14,
        },
        textInputPlaceholder: {
            color: Colors.fieldtTxtColor,
            fontFamily: 'NunitoSans-Regular',
            fontSize: 14,
        },
        dropDownPickerStyle:{
            width: '80%',
            height: windowHeight * 0.07,
            backgroundColor: Colors.fieldtBgColor,
            borderRadius: 10,
        },
        dropDownPickerArrowStyle:{
            fontWeight: 'bold'
        },
        dropDownPickerItemStyle:{
            justifyContent: 'flex-start',
            marginLeft: 5,
            fontSize: 14,
            fontFamily: 'NunitoSans-Regular'
        },
        dropDownPickerDDStyle:{
            width: '80%',
            backgroundColor: Colors.fieldtBgColor,
            zIndex: 10000  
        },
        toggleButtonStyle: {
            width: 34,
            height: 34,
            borderRadius: 5,
            alignItems: 'center',
            justifyContent: 'center',
            marginRight: 10,
        }
    });

    /////////////////////////////////////////////////

    return (
        <UserIdleWrapper disabled={!isMounted}>
            <View style={[
                styles.container,
                !isTablet() && { transform: [{ scaleX: 1 }, { scaleY: 1 }] }
            ]}>
                
                {/* Sidebar */}
                {/* <View style={styles.sidebar}>
                    <SideBar
                        navigation={props.navigation}
                        selectedTab={11}
                        expandPromotions
                    />
                </View> */}

                <ScrollView scrollEnabled={switchMerchant} horizontal>
                    
                    {/* Modals */}
                    <>
                        <DateTimePickerModal
                            isVisible={showPromoDateStartPicker}
                            mode={'date'}
                            onConfirm={(text) => {
                                LoyaltyStore.update(s => {
                                    s.promoDateStart = moment(text);
                                });
                                setShowPromoDateStartPicker(false);
                            }}
                            onCancel={() => {
                                setShowPromoDateStartPicker(false);
                            }}
                        />

                        <DateTimePickerModal
                            isVisible={showPromoDateEndPicker}
                            mode={'date'}
                            onConfirm={(text) => {
                                LoyaltyStore.update(s => {
                                    s.promoDateEnd = moment(text);
                                });
                                setShowPromoDateEndPicker(false);
                            }}
                            onCancel={() => {
                                setShowPromoDateEndPicker(false);
                            }}
                        />

                        <DateTimePickerModal
                            isVisible={showPromoTimeStartPicker}
                            mode={'time'}
                            onConfirm={(text) => {
                                LoyaltyStore.update(s => {
                                    s.promoTimeStart = moment(text);
                                });
                                setShowPromoTimeStartPicker(false);
                            }}
                            onCancel={() => {
                                setShowPromoTimeStartPicker(false);
                            }}
                        />

                        <DateTimePickerModal
                            isVisible={showPromoTimeEndPicker}
                            mode={'time'}
                            onConfirm={(text) => {
                                LoyaltyStore.update(s => {
                                    s.promoTimeEnd = moment(text);
                                });
                                setShowPromoTimeEndPicker(false);
                            }}
                            onCancel={() => {
                                setShowPromoTimeEndPicker(false);
                            }}
                        />

                        <DateTimePickerModal
                            isVisible={showNotificationDatePicker}
                            mode={'date'}
                            onConfirm={(text) => {
                                setNotificationDate(moment(text));
                                setShowNotificationDatePicker(false);
                            }}
                            onCancel={() => {
                                setShowNotificationDatePicker(false);
                            }}
                        />

                        <DateTimePickerModal
                            isVisible={showNotificationTimePicker}
                            mode={'time'}
                            onConfirm={(text) => {
                                setNotificationTime(moment(text));
                                setShowNotificationTimePicker(false);
                            }}
                            onCancel={() => {
                                setShowNotificationTimePicker(false);
                            }}
                        />
                    </>
                    {/* Modals End */}

                    <KeyboardAvoidingView>
                        <View style={{
                            width: switchMerchant ? windowWidth * 0.8 : windowWidth * 0.9,
                            margin: 10,
                            paddingTop: 10,
                        }}>
                            {/* Back Button */}
                            <TouchableOpacity
                                style={{ width: 90, height: 35, justifyContent: 'center' }}
                                onPress={() => {
                                    props.navigation.goBack();
                                }}>
                                <View
                                    style={{
                                        flexDirection: 'row',
                                        paddingHorizontal: '10%',
                                        alignContent: 'center',
                                        alignItems: 'center',
                                        marginTop: switchMerchant ? 10 : 0,
                                    }}>
                                    <View style={{ justifyContent: 'center' }}>
                                        <Feather
                                            name="chevron-left"
                                            size={switchMerchant ? 20 : 30}
                                            style={{ color: Colors.primaryColor, alignSelf: 'center' }}
                                        />
                                    </View>
                                    <Text
                                        style={[
                                            {
                                                fontSize: 17,
                                                color: Colors.primaryColor,
                                                fontWeight: '600',
                                                marginBottom: Platform.OS === 'ios' ? 0 : 1,
                                            },
                                            switchMerchant
                                                ? {
                                                    fontSize: 14,
                                                }
                                                : {},
                                        ]}>
                                        Back
                                    </Text>
                                </View>
                            </TouchableOpacity>
                        </View>

                        {/* Layout Container */}
                        <KeyboardAwareScrollView
                            showsVerticalScrollIndicator={false}
                            nestedScrollEnabled
                            style={{
                                height: windowHeight * 0.825,
                                width: windowWidth * 0.87,
                                backgroundColor: Colors.whiteColor,
                                marginHorizontal: 30,
                                paddingHorizontal: '2%',
                                borderRadius: 5,
                                shadowColor: '#000',
                                shadowOpacity: 0.22,
                                shadowRadius: 3.22,
                                shadowOffset: {
                                    width: 1,
                                    height: 2,
                                },
                                elevation: 3,
                            }}>

                            {/* Voucher Container & Button */}
                            <View style={{flexDirection: 'row', alignItems: 'center', marginVertical: '1%', height: '10%', flex: 1}}>
                                <View style={{flexDirection:'column', width: '70%'}}>
                                    <Text style={[styles.stepText, {fontSize: 22}]}>Select or Create a voucher to Continue.</Text>

                                    <DropDownPicker
                                        arrowColor={'black'}
                                        arrowSize={20}
                                        arrowStyle={styles.dropDownPickerArrowStyle}
                                        style={[styles.dropDownPickerStyle, {width: '80%'}]}
                                        items={taggableVoucherDropdownList}
                                        itemStyle={styles.dropDownPickerItemStyle}
                                        dropDownStyle={styles.dropDownPickerDDStyle}
                                        placeholder={'Select'}
                                        placeholderStyle={styles.textInputPlaceholder}
                                        onChangeItem={(item) => {
                                            setSelectedTaggableVoucher(item);
                                            CommonStore.update((s) => {
                                                s.selectedTaggableVoucherEdit = item.value;
                                            });
                                        }}
                                    />
                                </View>

                                {/* Save Button */}
                                {
                                    (selectedTaggableVoucher !== undefined) && (
                                        <TouchableOpacity
                                            style={{
                                                justifyContent: 'center',
                                                alignItems: 'center',
                                                borderWidth: 1,
                                                borderRadius: 5,
                                                borderColor: Colors.primaryColor,
                                                backgroundColor: Colors.primaryColor,
                                                width: '20%',
                                                height: '60%',
                                                paddingHorizontal: 10,
                                                shadowOffset: { width: 0, height: 2 },
                                                shadowOpacity: 0.22,
                                                shadowRadius: 3.22,
                                                elevation: 1,
                                            }}
                                            disabled={isLoading || selectedTaggableVoucher === undefined}
                                            onPress={() => handleButtonAction()}>

                                            <Text
                                                style={[
                                                    {
                                                        color: Colors.whiteColor,
                                                        fontSize: switchMerchant ? 10 : 16,
                                                        fontFamily: 'NunitoSans-Bold',
                                                    },
                                                ]}
                                            >
                                                {selectedTaggableVoucherEdit === null
                                                    ? (isLoading ? 'LOADING...' : 'CREATE')
                                                    : (isLoading ? 'LOADING...' : 'SAVE')}
                                            </Text>

                                            {isLoading && (
                                                <ActivityIndicator
                                                    color={Colors.whiteColor}
                                                    size={'small'}
                                                />
                                            )}
                                        </TouchableOpacity>
                                    )
                                }
                            </View>

                            <View style={[styles.divider]}/>

                            {
                                (selectedTaggableVoucher !== undefined) && (
                                    <>
                                        {/* Header Row */}
                                        <View style={{
                                            width: '100%',
                                            flexDirection: 'row',
                                            alignItems: 'center',
                                            justifyContent: 'space-between',
                                            marginVertical: 30
                                        }}>

                                            {/* New Voucher Title & Descriptions */}
                                            <View style={{flexDirection: 'column'}}>
                                                
                                                <Text style={{
                                                    fontFamily: 'NunitoSans-Bold',
                                                    fontSize: switchMerchant ? 20 : 30,
                                                }}>
                                                    New Voucher
                                                </Text>

                                                <Text style={{ fontFamily: 'NunitoSans-Regular', fontSize: 17 }}>
                                                    Please Provide all the necessary details for creating a new voucher.
                                                </Text>

                                            </View>
                                        </View>

                                        {/* Step 1 */}
                                        <View style={{marginBottom: 30}}>
                                            <Text style={styles.stepText}>Step - 1</Text>
                                            <View style={styles.divider}/>
                                            
                                            {/* Step 1 - Content */}
                                            <View style={{flexDirection: 'column', marginHorizontal: 15}}>

                                                <View style={{flexDirection: 'row', marginTop: '2%', width: '100%'}}>
                                                    {/* Select Voucher Type */}
                                                    <View style={{flex: 1}}>
                                                        <Text style={styles.textInputTitle}>Voucher Type</Text>

                                                        <View style={{ marginTop: 5 }}>
                                                            <DropDownPicker
                                                                arrowColor={'black'}
                                                                arrowSize={20}
                                                                arrowStyle={styles.dropDownPickerArrowStyle}
                                                                style={styles.dropDownPickerStyle}
                                                                items={LOYALTY_PROMOTION_TYPE_DROPDOWN_LIST}
                                                                itemStyle={styles.dropDownPickerItemStyle}
                                                                dropDownStyle={styles.dropDownPickerDDStyle}
                                                                placeholder={'Select'}
                                                                placeholderStyle={styles.textInputPlaceholder}
                                                                onChangeItem={(item) => {
                                                                    setVoucherType(item.value);
                                                                }}
                                                                defaultValue={ selectedTaggableVoucherEdit ? selectedTaggableVoucherEdit.voucherType : LOYALTY_PROMOTION_TYPE_DROPDOWN_LIST[0].value}
                                                            />
                                                        </View>
                                                    </View>
                                                    
                                                    {/* Apply Discount Per */}
                                                    <View style={{flex: 1}}>
                                                        {
                                                            (
                                                                voucherType === PROMOTION_TYPE.TAKE_AMOUNT_OFF ||
                                                                voucherType === PROMOTION_TYPE.TAKE_PERCENTAGE_OFF ||
                                                                voucherType === PROMOTION_TYPE.BUY_A_GET_B_FOR_FREE ||
                                                                voucherType === PROMOTION_TYPE.DELIVERY ||
                                                                voucherType === PROMOTION_TYPE.TAKEAWAY
                                                            ) && (
                                                                <>
                                                                    <Text style={styles.textInputTitle}>Apply Discount Per</Text>

                                                                    <View style={{ marginTop: 5}}>

                                                                        <DropDownPicker
                                                                            arrowColor={'black'}
                                                                            arrowSize={20}
                                                                            arrowStyle={styles.dropDownPickerArrowStyle}
                                                                            style={styles.dropDownPickerStyle}
                                                                            items={APPLY_DISCOUNT_PER_DROPDOWN_LIST}
                                                                            itemStyle={styles.dropDownPickerItemStyle}
                                                                            dropDownStyle={styles.dropDownPickerDDStyle}
                                                                            placeholder={'Select'}
                                                                            placeholderStyle={styles.textInputPlaceholder}
                                                                            onChangeItem={item => {
                                                                                setApplyDiscountPer(item.value);
                                                                            }}
                                                                            defaultValue={applyDiscountPer}
                                                                        />
                                                                    </View>
                                                                </>
                                                            )
                                                        }
                                                    </View>
                                                </View>
                                                
                                                {/* Criteria */}
                                                <View style={{flexDirection: 'column', marginTop: '2%', width: '100%'}}>
                                                    <View style={{ marginBottom: 5 }}>
                                                        <Text style={styles.textInputTitle}>Criteria</Text>

                                                            {/* Override Existing Price */}
                                                            {voucherType === LOYALTY_PROMOTION_TYPE.OVERRIDE_EXISTING_PRICE && (
                                                                <>
                                                                    <View>
                                                                        {/* Min Spend Amount */}
                                                                        <View style={{flexDirection: 'row', alignItems: 'center'}}>
                                                                            <Text style={styles.textInputTitle}>Min Spend Amount (RM)</Text>

                                                                            <TextInput
                                                                                style={[styles.textInputField, {width: '15%'}]}
                                                                                placeholder={'0.00'}
                                                                                placeholderTextColor={Platform.select({ios: '#a9a9a9'})}
                                                                                placeholderStyle={styles.textInputPlaceholder}
                                                                                clearTextOnFocus
                                                                                keyboardType={'decimal-pad'}
                                                                                value={minSpend.toString()}
                                                                                onFocus={() => {
                                                                                    setTemp(minSpend)
                                                                                    setMinSpend('');
                                                                                }}
                                                                                onEndEditing={() => {
                                                                                    if (minSpend.trim() == '') {setMinSpend(temp)}
                                                                                }}
                                                                                onChangeText={(text) => {
                                                                                    setMinSpend(parseValidPriceText(text));
                                                                                }}
                                                                            />
                                                                        </View>
                                                                        
                                                                        {/* Borderline */}
                                                                        <View style={[styles.divider, {marginVertical: 15}]}/>
                                                                        
                                                                        {/* Select Product Category/ Product */}
                                                                        <View style={{ flexDirection: 'column' }}>

                                                                            <Text style={styles.textInputTitle}>Product</Text>

                                                                            {overrideItems.map((overrideItem, index) => {
                                                                                return (
                                                                                    <View style={{
                                                                                        width: '100%',
                                                                                        flexDirection: 'row',
                                                                                        alignItems: 'center',
                                                                                        marginTop: 15,
                                                                                    }}>
                                                                                        {overrideItem.variation && (
                                                                                            <View style={{width: '30%'}}>
                                                                                                <DropDownPicker
                                                                                                    arrowColor={'black'}
                                                                                                    arrowSize={20}
                                                                                                    arrowStyle={styles.dropDownPickerArrowStyle}
                                                                                                    style={[styles.dropDownPickerStyle, {width: '95%'}]}
                                                                                                    items={PROMOTION_TYPE_VARIATION_DROPDOWN_LIST}
                                                                                                    itemStyle={styles.dropDownPickerItemStyle}
                                                                                                    placeholder={'Product of Category'}
                                                                                                    onChangeItem={(itemDropdown) => {
                                                                                                        setOverrideItems(
                                                                                                            overrideItems.map((item, i) =>
                                                                                                                i === index ? {
                                                                                                                    ...item,
                                                                                                                    variation: itemDropdown.value,
                                                                                                                }
                                                                                                                : item,
                                                                                                            ),
                                                                                                        );
                                                                                                    }}
                                                                                                    defaultValue={overrideItem.variation}
                                                                                                    dropDownStyle={[styles.dropDownPickerDDStyle, {width: '95%'}]}
                                                                                                />
                                                                                            </View>
                                                                                        )}

                                                                                        {(
                                                                                            overrideItem.variation ===
                                                                                                PROMOTION_TYPE_VARIATION.PRODUCT_OF_CATEGORY
                                                                                                ? variationItemsCategories.length > 0 &&
                                                                                                (variationItemsCategories.find(
                                                                                                    (item) =>
                                                                                                        item.value ===
                                                                                                        overrideItem.variationItems[0],
                                                                                                ) ||
                                                                                                    overrideItem.variationItems
                                                                                                        .length === 0)
                                                                                                : variationItemsProducts.length > 0 &&
                                                                                                (variationItemsProducts.find(
                                                                                                    (item) =>
                                                                                                        item.value ===
                                                                                                        overrideItem.variationItems[0],
                                                                                                ) ||
                                                                                                    overrideItem.variationItems
                                                                                                        .length === 0)
                                                                                        ) && (
                                                                                            <View style={{width: '30%'}}>
                                                                                                <DropDownPicker
                                                                                                    arrowColor={'black'}
                                                                                                    arrowSize={20}
                                                                                                    arrowStyle={styles.dropDownPickerArrowStyle}
                                                                                                    style={[styles.dropDownPickerStyle, {width: '95%'}]}
                                                                                                    placeholderStyle={styles.textInputPlaceholder}
                                                                                                    items={
                                                                                                        overrideItem.variation ===
                                                                                                            PROMOTION_TYPE_VARIATION.PRODUCT_OF_CATEGORY
                                                                                                            ? variationItemsCategories
                                                                                                            : variationItemsProducts
                                                                                                    }
                                                                                                    itemStyle={styles.dropDownPickerItemStyle}
                                                                                                    placeholder={
                                                                                                        overrideItem.variation ===
                                                                                                            PROMOTION_TYPE_VARIATION.PRODUCT_OF_CATEGORY
                                                                                                            ? 'Select a Category'
                                                                                                            : 'Select a Product'
                                                                                                    }
                                                                                                    multipleText={
                                                                                                        overrideItem.variation ===
                                                                                                            PROMOTION_TYPE_VARIATION.PRODUCT_OF_CATEGORY
                                                                                                            ? '%d category(s) selected'
                                                                                                            : '%d product(s) selected'
                                                                                                    }
                                                                                                    customTickIcon={(press) => (
                                                                                                        <Ionicon
                                                                                                            name={'checkmark-outline'}
                                                                                                            color={
                                                                                                                press
                                                                                                                    ? Colors.fieldtBgColor
                                                                                                                    : Colors.primaryColor
                                                                                                            }
                                                                                                            size={switchMerchant ? 20 : 25}
                                                                                                        />
                                                                                                    )}
                                                                                                    onChangeItem={(items) => {
                                                                                                        setOverrideItems(
                                                                                                            overrideItems.map((item, i) =>
                                                                                                                i === index
                                                                                                                    ? {
                                                                                                                        ...item,
                                                                                                                        variationItems: items,
                                                                                                                    }
                                                                                                                    : item,
                                                                                                            ),
                                                                                                        );
                                                                                                    }}
                                                                                                    defaultValue={overrideItem.variationItems}
                                                                                                    multiple
                                                                                                    searchable
                                                                                                    dropDownStyle={[styles.dropDownPickerDDStyle, {width: '95%'}]}
                                                                                                />
                                                                                            </View>
                                                                                        )}

                                                                                        <View style={{
                                                                                            flexDirection: 'row',
                                                                                            alignItems: 'center',
                                                                                            width: '20%',
                                                                                        }}>

                                                                                            <Text style={styles.textInputTitle}>RM </Text>

                                                                                            <TextInput
                                                                                                style={[styles.textInputField, {marginTop: 0}]}
                                                                                                placeholder={'0.00'}
                                                                                                placeholderTextColor={Platform.select({
                                                                                                    ios: '#a9a9a9',
                                                                                                })}
                                                                                                placeholderStyle={styles.textInputPlaceholder}
                                                                                                clearTextOnFocus
                                                                                                onFocus={() => {
                                                                                                    setTemp(overrideItem.priceBeforeTax)
                                                                                                    setOverrideItems(
                                                                                                        overrideItems.map((item, i) =>
                                                                                                            i === index
                                                                                                                ? {
                                                                                                                    ...item,
                                                                                                                    priceBeforeTax: '',
                                                                                                                }
                                                                                                                : item,
                                                                                                        ),
                                                                                                    );
                                                                                                }}
                                                                                                onEndEditing={() => {
                                                                                                    if (overrideItem.priceBeforeTax == '') {
                                                                                                        setOverrideItems(
                                                                                                            overrideItems.map((item, i) =>
                                                                                                                i === index
                                                                                                                    ? {
                                                                                                                        ...item,
                                                                                                                        priceBeforeTax:
                                                                                                                            temp.length > 0
                                                                                                                                ? parseFloat(temp)
                                                                                                                                : 0,
                                                                                                                    }
                                                                                                                    : item,
                                                                                                            ),
                                                                                                        );
                                                                                                    }
                                                                                                }}
                                                                                                onChangeText={(text) => {
                                                                                                    setOverrideItems(
                                                                                                        overrideItems.map((item, i) =>
                                                                                                            i === index
                                                                                                                ? {
                                                                                                                    ...item,
                                                                                                                    priceBeforeTax:
                                                                                                                        parseValidPriceText(text),
                                                                                                                }
                                                                                                                : item,
                                                                                                        ),
                                                                                                    );
                                                                                                }}
                                                                                                defaultValue={typeof overrideItem.priceBeforeTax === 'string' ? overrideItem.priceBeforeTax : overrideItem.priceBeforeTax.toFixed(2)}
                                                                                                keyboardType={'decimal-pad'}
                                                                                            />

                                                                                            <View style={{marginLeft: 10}}>
                                                                                                {index === 0 ? (
                                                                                                    <TouchableOpacity
                                                                                                        onPress={() => {
                                                                                                            setOverrideItems([
                                                                                                                ...overrideItems,
                                                                                                                {
                                                                                                                    priceBeforeTax: 0,
                                                                                                                    variation: PROMOTION_TYPE_VARIATION.SPECIFIC_PRODUCTS,
                                                                                                                    variationItems: [],
                                                                                                                    variationItemsSku: [],
                                                                                                                },
                                                                                                            ]);
                                                                                                        }}
                                                                                                        style={{
                                                                                                            marginBottom: '3%',
                                                                                                            backgroundColor:
                                                                                                                Colors.whiteColor,
                                                                                                            alignItems: 'center',
                                                                                                            flexDirection: 'row',
                                                                                                        }}>
                                                                                                        <Feather
                                                                                                            name="plus-circle"
                                                                                                            size={switchMerchant ? 17 : 20}
                                                                                                            color={Colors.primaryColor}
                                                                                                        />
                                                                                                    </TouchableOpacity>
                                                                                                ) : (
                                                                                                    <TouchableOpacity
                                                                                                        style={{ marginBottom: '3%' }}
                                                                                                        onPress={() => {
                                                                                                            setOverrideItems([
                                                                                                                ...overrideItems.slice(
                                                                                                                    0,
                                                                                                                    index,
                                                                                                                ),
                                                                                                                ...overrideItems.slice(
                                                                                                                    index + 1,
                                                                                                                ),
                                                                                                            ]);
                                                                                                        }}>
                                                                                                        <Feather
                                                                                                            name="minus-circle"
                                                                                                            size={switchMerchant ? 17 : 20}
                                                                                                            color="#eb3446"
                                                                                                        />
                                                                                                    </TouchableOpacity>
                                                                                                )}
                                                                                            </View>
                                                                                        </View>
                                                                                    </View>
                                                                                );
                                                                            })}
                                                                        </View>
                                                                    </View>
                                                                </>
                                                            )}

                                                            {/* Bundle Deal */}
                                                            {voucherType === LOYALTY_PROMOTION_TYPE.BUY_A_GET_B_FOR_FREE && (
                                                                <>
                                                                    <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                                                                        
                                                                        <View style={{width: '8%', flexDirection: 'row', alignItems: 'center', marginRight: 15}}>
                                                                            <View style={{flex: 1, justifyContent: 'center', alignItems: 'center'}}>
                                                                                <Text style={styles.textInputTitle}>Buy</Text>
                                                                            </View>

                                                                            <View style={{flex: 1, justifyContent: 'center', alignItems: 'center'}}>
                                                                                <TextInput
                                                                                    style={{
                                                                                        borderWidth: 1,
                                                                                        borderColor: '#E5E5E5',
                                                                                        borderRadius: 5,
                                                                                        height: switchMerchant ? 35 : 40,
                                                                                        width: 40,
                                                                                        justifyContent: 'center',
                                                                                        textAlign: 'center',
                                                                                        margin: 6,
                                                                                        fontFamily: 'NunitoSans-Regular',
                                                                                        fontSize: switchMerchant ? 10 : 14,
                                                                                    }}
                                                                                    placeholder={'0'}
                                                                                    placeholderTextColor={Platform.select({
                                                                                        ios: '#a9a9a9',
                                                                                    })}
                                                                                    placeholderStyle={styles.textInputPlaceholder}
                                                                                    clearTextOnFocus

                                                                                    onFocus={() => {
                                                                                        setTemp(buyInput)
                                                                                        setBuyInput('');
                                                                                    }}
                                                                                    onEndEditing={() => {
                                                                                        if (buyInput == '') {
                                                                                            setBuyInput(temp);
                                                                                        }
                                                                                    }}
                                                                                    onChangeText={(text) => {
                                                                                        setBuyInput(text);
                                                                                    }}
                                                                                    value={buyInput.toString()}
                                                                                    keyboardType={'decimal-pad'}
                                                                                />
                                                                            </View>
                                                                        </View>
                                                                        
                                                                        <View style={{ width:'30%'}}>

                                                                            <DropDownPicker
                                                                                arrowColor={'black'}
                                                                                arrowSize={20}
                                                                                arrowStyle={styles.dropDownPickerArrowStyle}
                                                                                style={[styles.dropDownPickerStyle, {width: '95%'}]}
                                                                                placeholderStyle={styles.textInputPlaceholder}
                                                                                items={PROMOTION_TYPE_VARIATION_DROPDOWN_LIST}
                                                                                itemStyle={styles.dropDownPickerItemStyle}
                                                                                customTickIcon={(press) => (
                                                                                    <Ionicon
                                                                                        name={'checkmark-outline'}
                                                                                        color={
                                                                                            press
                                                                                                ? Colors.fieldtBgColor
                                                                                                : Colors.primaryColor
                                                                                        }
                                                                                        size={25}
                                                                                    />
                                                                                )}
                                                                                onChangeItem={(item) => {
                                                                                    setSelectedVariation(item.value);
                                                                                }}
                                                                                defaultValue={selectedVariation}
                                                                                dropDownStyle={[styles.dropDownPickerDDStyle, {width: '95%'}]}
                                                                            />
                                                                        </View>

                                                                        <View style={{width: '30%'}}>
                                                                            {variationItemsDropdownList.length > 0 &&
                                                                                (variationItemsDropdownList.find((item) => item.value === selectedVariationItems[0]) || 
                                                                                selectedVariationItems.length === 0) && (
                                                                                
                                                                                <DropDownPicker
                                                                                    arrowColor={'black'}
                                                                                    arrowSize={20}
                                                                                    arrowStyle={styles.dropDownPickerArrowStyle}
                                                                                    style={[styles.dropDownPickerStyle, {width: '95%'}]}
                                                                                    placeholderStyle={styles.textInputPlaceholder}
                                                                                    placeholder={
                                                                                        selectedVariation === PROMOTION_TYPE_VARIATION.PRODUCT_OF_CATEGORY 
                                                                                        ? 'Select a Category' 
                                                                                        : 'Select a Product'
                                                                                    }
                                                                                    multipleText={
                                                                                        selectedVariation === PROMOTION_TYPE_VARIATION.PRODUCT_OF_CATEGORY 
                                                                                        ? '%d Categories selected' 
                                                                                        : '%d Product(s) selected'
                                                                                    }
                                                                                    items={variationItemsDropdownList}
                                                                                    itemStyle={styles.dropDownPickerItemStyle}
                                                                                    customTickIcon={(press) => (
                                                                                        <Ionicon
                                                                                            name={'checkmark-outline'}
                                                                                            color={
                                                                                                press
                                                                                                    ? Colors.fieldtBgColor
                                                                                                    : Colors.primaryColor
                                                                                            }
                                                                                            size={switchMerchant ? 20 : 25}
                                                                                        />
                                                                                    )}
                                                                                    onChangeItem={(items) =>
                                                                                        setSelectedVariationItems(items)
                                                                                    }
                                                                                    defaultValue={selectedVariationItems}
                                                                                    multiple
                                                                                    searchable
                                                                                    dropDownStyle={[styles.dropDownPickerDDStyle, {width: '95%'}]}
                                                                                />
                                                                            )}
                                                                        </View>

                                                                    </View>

                                                                    <View style={{flexDirection: 'row', alignItems: 'center', marginTop: 10}}>

                                                                        <View style={{width: '8%', flexDirection: 'row', alignItems: 'center', marginRight: 15}}>
                                                                            <View style={{flex: 1, justifyContent: 'center', alignItems: 'center'}}>
                                                                                <Text style={styles.textInputTitle}>Get</Text>
                                                                            </View>

                                                                            <View style={{flex: 1, justifyContent: 'center', alignItems: 'center'}}>
                                                                                <TextInput
                                                                                    style={{
                                                                                        borderWidth: 1,
                                                                                        borderColor: '#E5E5E5',
                                                                                        borderRadius: 5,
                                                                                        height: switchMerchant ? 35 : 40,
                                                                                        width: 40,
                                                                                        justifyContent: 'center',
                                                                                        textAlign: 'center',
                                                                                        margin: 6,
                                                                                        fontFamily: 'NunitoSans-Regular',
                                                                                        fontSize: switchMerchant ? 10 : 14,
                                                                                    }}
                                                                                    placeholder={'0'}
                                                                                    placeholderTextColor={Platform.select({
                                                                                        ios: '#a9a9a9',
                                                                                    })}
                                                                                    placeholderStyle={styles.textInputPlaceholder}
                                                                                    clearTextOnFocus

                                                                                    onFocus={() => {
                                                                                        setTemp(getInput)
                                                                                        setGetInput('');
                                                                                    }}
                                                                                    onEndEditing={() => {
                                                                                        if (getInput == '') {
                                                                                            setGetInput(temp);
                                                                                        }
                                                                                    }}
                                                                                    onChangeText={(text) => {
                                                                                        setGetInput(text);
                                                                                    }}
                                                                                    value={getInput.toString()}
                                                                                    keyboardType={'decimal-pad'}
                                                                                />
                                                                            </View>
                                                                        </View>
                                                                        
                                                                        <View style={{width: '30%'}}>
                                                                            <DropDownPicker
                                                                                arrowColor={'black'}
                                                                                arrowSize={20}
                                                                                arrowStyle={styles.dropDownPickerArrowStyle}
                                                                                style={[styles.dropDownPickerStyle, {width: '95%'}]}
                                                                                placeholderStyle={styles.textInputPlaceholder}
                                                                                items={PROMOTION_TYPE_VARIATION_DROPDOWN_LIST.slice(1)}
                                                                                itemStyle={styles.dropDownPickerItemStyle}
                                                                                placeholder={'Product'}
                                                                                defaultValue={selectedVariationB1F1}
                                                                                customTickIcon={(press) => (
                                                                                    <Ionicon
                                                                                        name={'checkmark-outline'}
                                                                                        color={
                                                                                            press
                                                                                                ? Colors.fieldtBgColor
                                                                                                : Colors.primaryColor
                                                                                        }
                                                                                        size={25}
                                                                                    />
                                                                                )}
                                                                                onChangeItem={(item) => {
                                                                                    setSelectedVariationB1F1(item.value);
                                                                                }}
                                                                                dropDownStyle={[styles.dropDownPickerDDStyle, {width: '95%'}]}
                                                                            />
                                                                        </View>

                                                                        <View style={{width: '30%'}}>
                                                                            {variationItemsB1F1DropdownList.length > 0 &&
                                                                                (variationItemsB1F1DropdownList.find(
                                                                                    (item) =>
                                                                                        item.value === selectedVariationItemsB1F1[0],
                                                                                ) ||
                                                                                    selectedVariationItemsB1F1.length === 0) && (
                                                                                <DropDownPicker
                                                                                    arrowColor={'black'}
                                                                                    arrowSize={20}
                                                                                    arrowStyle={styles.dropDownPickerArrowStyle}
                                                                                    style={[styles.dropDownPickerStyle, {width: '95%'}]}
                                                                                    placeholderStyle={styles.textInputPlaceholder}
                                                                                    itemStyle={styles.textInputPlaceholder}
                                                                                    placeholder={'Select a Product'}
                                                                                    multipleText={'%d product(s) selected'}
                                                                                    items={variationItemsB1F1DropdownList}

                                                                                    customTickIcon={(press) => (
                                                                                        <Ionicon
                                                                                            name={'checkmark-outline'}
                                                                                            color={
                                                                                                press
                                                                                                    ? Colors.fieldtBgColor
                                                                                                    : Colors.primaryColor
                                                                                            }
                                                                                            size={switchMerchant ? 20 : 25}
                                                                                        />
                                                                                    )}
                                                                                    onChangeItem={(items) =>
                                                                                        setSelectedVariationItemsB1F1(items)
                                                                                    }
                                                                                    defaultValue={selectedVariationItemsB1F1}
                                                                                    multiple
                                                                                    searchable
                                                                                    dropDownStyle={[styles.dropDownPickerDDStyle, {width: '95%'}]}
                                                                                />
                                                                            )}
                                                                        </View>

                                                                    </View>

                                                                    <View style={{
                                                                        flexDirection: 'row',
                                                                        justifyContent: 'space-between',
                                                                        marginTop: 10,
                                                                        width: '40%'
                                                                    }}>

                                                                        <View style={{flexDirection: 'row', alignItems:'center', flex: 1}}>

                                                                            <Text style={styles.textInputTitle}>Item(s) for</Text>
                                                                            <TextInput
                                                                                style = {[styles.textInputField, {width: '50%', marginLeft: 5}]}
                                                                                placeholder={'0.00'}
                                                                                placeholderTextColor={Platform.select({
                                                                                    ios: '#a9a9a9',
                                                                                })}
                                                                                placeholderStyle={styles.textInputPlaceholder}
                                                                                clearTextOnFocus
                                                                                
                                                                                onFocus={() => {
                                                                                    setTemp(getPriceInput)
                                                                                    setGetPriceInput('');
                                                                                }}
                                                                                onEndEditing={() => {
                                                                                    if (getPriceInput == '') {
                                                                                        setGetPriceInput(temp);
                                                                                    }
                                                                                }}
                                                                                onChangeText={(text) => {
                                                                                    setGetPriceInput(parseValidPriceText(text));
                                                                                }}
                                                                                value={getPriceInput.toString()}
                                                                                keyboardType={'decimal-pad'}
                                                                            />

                                                                        </View>
                                                                        
                                                                        <View style={{flexDirection: 'row', alignItems:'center', flex: 1}}>

                                                                            <Text style={styles.textInputTitle}>Min Spend Amount (RM)</Text>
                                                                            <TextInput
                                                                                style = {[styles.textInputField, {width: '50%', marginLeft: 5}]}
                                                                                placeholder={'0.00'}
                                                                                placeholderTextColor={Platform.select({
                                                                                    ios: '#a9a9a9',
                                                                                })}
                                                                                placeholderStyle={styles.textInputPlaceholder}
                                                                                clearTextOnFocus
                                                                                
                                                                                onFocus={() => {
                                                                                    setTemp(minSpend);
                                                                                    setMinSpend('');
                                                                                }}
                                                                                onEndEditing={() => {
                                                                                    if (minSpend == '') {
                                                                                        setMinSpend(temp);
                                                                                    }
                                                                                }}
                                                                                onChangeText={(text) => {
                                                                                    setMinSpend(parseValidPriceText(text));
                                                                                }}
                                                                                value={minSpend.toString()}
                                                                                keyboardType={'decimal-pad'}
                                                                            />

                                                                        </View>

                                                                        
                                                                    </View>
                                                                </>
                                                            )}

                                                            {/* Take Amount Off */}
                                                            {/* Take Percentage Off */}
                                                            {(voucherType === LOYALTY_PROMOTION_TYPE.TAKE_AMOUNT_OFF || 
                                                            voucherType === LOYALTY_PROMOTION_TYPE.TAKE_PERCENTAGE_OFF) && 
                                                            (
                                                                <>
                                                                    <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                                                                        {/* Amount Off Input Field / Percentage Off Input Field */}
                                                                        <View style={{
                                                                            flexDirection: 'row',
                                                                            alignItems: 'center'
                                                                        }}>

                                                                            <Text style={styles.textInputTitle}> 
                                                                                {voucherType === LOYALTY_PROMOTION_TYPE.TAKE_AMOUNT_OFF ? 'Amount Off (RM)' : 'Discount (%)'}
                                                                            </Text>

                                                                            <TextInput
                                                                                style={{
                                                                                    width: 80,
                                                                                    height: switchMerchant ? 35 : 40,
                                                                                    borderWidth: 1,
                                                                                    borderRadius: 5,
                                                                                    borderColor: '#E5E5E5',
                                                                                    margin: 5,
                                                                                    textAlign: 'center',
                                                                                    fontFamily: 'NunitoSans-Regular',
                                                                                    fontSize: switchMerchant ? 10 : 14,
                                                                                }}
                                                                                placeholder={'0'}
                                                                                placeholderStyle={styles.textInputPlaceholder}
                                                                                placeholderTextColor={Platform.select({ios: '#a9a9a9'})}
                                                                                keyboardType={'decimal-pad'}
                                                                                clearTextOnFocus
                                                                                defaultValue={voucherType === LOYALTY_PROMOTION_TYPE.TAKE_AMOUNT_OFF ? amountOffPrice : percentageOffPrice}

                                                                                onFocus={() => {
                                                                                    switch (voucherType) {
                                                                                        case LOYALTY_PROMOTION_TYPE.TAKE_AMOUNT_OFF:
                                                                                            setTemp(amountOffPrice);
                                                                                            setAmountOffPrice('');
                                                                                            break;
                                                                                        case LOYALTY_PROMOTION_TYPE.TAKE_PERCENTAGE_OFF:
                                                                                            setTemp(percentageOffPrice)
                                                                                            setPercentageOffPrice('');
                                                                                            break;
                                                                                        default:
                                                                                            break;
                                                                                    }
                                                                                }}

                                                                                onEndEditing={() => {
                                                                                    switch (voucherType) {
                                                                                        case LOYALTY_PROMOTION_TYPE.TAKE_AMOUNT_OFF:
                                                                                            if (amountOffPrice == '') {
                                                                                                setAmountOffPrice(temp);
                                                                                            }
                                                                                            break;
                                                                                        case LOYALTY_PROMOTION_TYPE.TAKE_PERCENTAGE_OFF:
                                                                                            if (percentageOffPrice == '') {
                                                                                                setPercentageOffPrice(temp);
                                                                                            }
                                                                                            break;
                                                                                        default:
                                                                                            break;
                                                                                    }
                                                                                }}

                                                                                onChangeText={(text) => {
                                                                                    switch (voucherType) {
                                                                                        case LOYALTY_PROMOTION_TYPE.TAKE_AMOUNT_OFF:
                                                                                            setAmountOffPrice(parseValidPriceText(text));
                                                                                            break;
                                                                                        case LOYALTY_PROMOTION_TYPE.TAKE_PERCENTAGE_OFF:
                                                                                            setPercentageOffPrice(text);
                                                                                            break;
                                                                                        default:
                                                                                            break;
                                                                                    }
                                                                                }}
                                                                            />
                                                                        </View>
                                                                        
                                                                        {/* Minimum Spend Amount Input Field */}
                                                                        <View style={{
                                                                            flexDirection: 'row',
                                                                            alignItems: 'center',
                                                                            marginLeft: 20
                                                                        }}>
                                                                            <Text style={styles.textInputTitle}>Min Spend Amount (RM)</Text>
                                                                            
                                                                            <TextInput
                                                                                style={{
                                                                                    width: 80,
                                                                                    height: switchMerchant ? 35 : 40,
                                                                                    borderWidth: 1,
                                                                                    borderRadius: 5,
                                                                                    borderColor: '#E5E5E5',
                                                                                    margin: 5,
                                                                                    fontFamily: 'NunitoSans-Regular',
                                                                                    fontSize: switchMerchant ? 10 : 14,
                                                                                    textAlign: 'center'
                                                                                }}
                                                                                placeholder={'0.00'}
                                                                                placeholderTextColor={Platform.select({ios: '#a9a9a9'})}
                                                                                placeholderStyle={styles.textInputPlaceholder}
                                                                                value={minSpend.toString()}
                                                                                keyboardType={'decimal-pad'}
                                                                                clearTextOnFocus

                                                                                onFocus={() => {
                                                                                    setTemp(minSpend)
                                                                                    setMinSpend('');
                                                                                }}
                                                                                onEndEditing={() => {
                                                                                    if (minSpend == '') {
                                                                                        setMinSpend(temp);
                                                                                    }
                                                                                }}
                                                                                onChangeText={(text) => {
                                                                                    setMinSpend(parseValidPriceText(text));
                                                                                }}
                                                                            />
                                                                        </View>
                                                                    </View>
                                                                    
                                                                    {/* Divider */}
                                                                    <View style={[styles.divider, {marginVertical: 15}]}/>

                                                                    <View style={{ flexDirection: 'row', width: '70%', justifyContent: 'space-between'}}>

                                                                        {/* Select Product Category /+ Product */}
                                                                        <View style={{ flexDirection: 'column', width: '70%'}}>
                                                                            <Text style={styles.textInputTitle}>Apply To</Text>
                                                                            
                                                                            <View style={{
                                                                                flexDirection: 'row',
                                                                                marginTop: 10,
                                                                                marginRight: 5,
                                                                                width: '100%',
                                                                                justifyContent: 'space-around',
                                                                                alignItems: 'center'
                                                                            }}>

                                                                                <View style={{width: '45%'}}>
                                                                                    <DropDownPicker
                                                                                        arrowColor={'black'}
                                                                                        arrowSize={20}
                                                                                        arrowStyle={styles.dropDownPickerArrowStyle}
                                                                                        style={[styles.dropDownPickerStyle, {width: '100%'}]}
                                                                                        placeholderStyle={styles.textInputPlaceholder}
                                                                                        items={PROMOTION_TYPE_VARIATION_DROPDOWN_LIST}
                                                                                        itemStyle={styles.dropDownPickerItemStyle}
                                                                                        placeholder={'Product'}
                                                                                        customTickIcon={(press) => (
                                                                                            <Ionicon
                                                                                                name={'checkmark-outline'}
                                                                                                color={
                                                                                                    press
                                                                                                        ? Colors.fieldtBgColor
                                                                                                        : Colors.primaryColor
                                                                                                }
                                                                                                size={25}
                                                                                            />
                                                                                        )}
                                                                                        onChangeItem={(item) =>
                                                                                            setSelectedVariation(item.value)
                                                                                        }
                                                                                        defaultValue={selectedVariation}
                                                                                        dropDownStyle={[styles.dropDownPickerDDStyle, {width: '100%'}]}
                                                                                    />

                                                                                </View>

                                                                                <Text style={{
                                                                                    fontFamily: 'NunitoSans-Regular',
                                                                                    fontSize: switchMerchant ? 10 : 14,
                                                                                }}>
                                                                                    +
                                                                                </Text>

                                                                                <View style={{width: '45%'}}>
                                                                                    {variationItemsDropdownList.length > 0
                                                                                        &&
                                                                                        (
                                                                                            selectedVariationItems.filter(itemId => {
                                                                                                return variationItemsDropdownList.find(item2 => item2.value === itemId) ? true : false;
                                                                                            }).length === selectedVariationItems.length
                                                                                            ||
                                                                                            selectedVariationItems.length === 0
                                                                                        ) && (
                                                                                        <DropDownPicker
                                                                                            arrowColor={'black'}
                                                                                            arrowSize={20}
                                                                                            arrowStyle={styles.dropDownPickerArrowStyle}
                                                                                            style={[styles.dropDownPickerStyle, {width: '100%'}]}
                                                                                            placeholderStyle={styles.textInputPlaceholder}
                                                                                            itemStyle={styles.dropDownPickerItemStyle}
                                                                                            placeholder={'Select a Product'}
                                                                                            multipleText={'%d product(s) selected'}
                                                                                            items={variationItemsDropdownList}
                                                                                            customTickIcon={(press) => (
                                                                                                <Ionicon
                                                                                                    name={'checkmark-outline'}
                                                                                                    color={
                                                                                                        press
                                                                                                            ? Colors.fieldtBgColor
                                                                                                            : Colors.primaryColor
                                                                                                    }
                                                                                                    size={switchMerchant ? 20 : 25}
                                                                                                />
                                                                                            )}
                                                                                            onChangeItem={(items) =>
                                                                                                setSelectedVariationItems(items)
                                                                                            }
                                                                                            defaultValue={selectedVariationItems}
                                                                                            multiple
                                                                                            searchable
                                                                                            dropDownStyle={[styles.dropDownPickerDDStyle, {width: '100%'}]}
                                                                                        />
                                                                                    )}

                                                                                </View>
                                                                                
                                                                            </View>
                                                                        </View>

                                                                        {/* Minimum Quantity */}
                                                                        <View style={{ flexDirection: 'column', justifyContent: 'space-around', alignItems: 'center'}}>
                                                                            <Text style={styles.textInputTitle}>Min. Quantity</Text>
                                                                            
                                                                            <TextInput
                                                                                style={{
                                                                                    borderWidth: 1,
                                                                                    borderRadius: 5,
                                                                                    borderColor: '#E5E5E5',
                                                                                    height: switchMerchant ? 35 : 40,
                                                                                    width: 60,
                                                                                    textAlign: 'center',
                                                                                    fontFamily: 'NunitoSans-Regular',
                                                                                    fontSize: switchMerchant ? 10 : 14,
                                                                                }}
                                                                                placeholder={'0'}
                                                                                placeholderTextColor={Platform.select({ios: '#a9a9a9'})}
                                                                                placeholderStyle={styles.textInputPlaceholder}
                                                                                value={voucherType === LOYALTY_PROMOTION_TYPE.TAKE_AMOUNT_OFF ? amountOffMinQuantity.toString() : percentageOffMinQuantity.toString()}
                                                                                keyboardType={'decimal-pad'}
                                                                                clearTextOnFocus

                                                                                onFocus={() => {
                                                                                    if(voucherType === PROMOTION_TYPE.TAKE_AMOUNT_OFF){
                                                                                        setTemp(amountOffMinQuantity);
                                                                                        setAmountOffMinQuantity('');
                                                                                    }
                                                                                    else if(voucherType === PROMOTION_TYPE.TAKE_PERCENTAGE_OFF){
                                                                                        setTemp(percentageOffMinQuantity);
                                                                                        setPercentageOffMinQuantity('');
                                                                                    }
                                                                                }}
                                                                                onEndEditing={() => {
                                                                                    if (voucherType === PROMOTION_TYPE.TAKE_AMOUNT_OFF){
                                                                                        if(amountOffMinQuantity === '')
                                                                                        setAmountOffMinQuantity(temp);
                                                                                    }
                                                                                    else if (voucherType === PROMOTION_TYPE.TAKE_PERCENTAGE_OFF){
                                                                                        if (percentageOffMinQuantity === '')
                                                                                        setPercentageOffMinQuantity(temp);
                                                                                    }
                                                                                }}
                                                                                onChangeText={(text) => {
                                                                                    if(voucherType === PROMOTION_TYPE.TAKE_AMOUNT_OFF){
                                                                                        setAmountOffMinQuantity(text);
                                                                                    }
                                                                                    else if(voucherType === PROMOTION_TYPE.TAKE_PERCENTAGE_OFF){
                                                                                        setPercentageOffMinQuantity(text);
                                                                                    }
                                                                                }}
                                                                            />
                                                                        </View>

                                                                        {/* Maximum Quantity */}
                                                                        <View style={{ flexDirection: 'column', justifyContent: 'space-around', alignItems: 'center'}}>
                                                                            <Text style={styles.textInputTitle}>Max. Quantity</Text>
                                                                            
                                                                            <TextInput
                                                                                style={{
                                                                                    borderWidth: 1,
                                                                                    borderRadius: 5,
                                                                                    borderColor: '#E5E5E5',
                                                                                    height: switchMerchant ? 35 : 40,
                                                                                    width: 60,
                                                                                    textAlign: 'center',
                                                                                    fontFamily: 'NunitoSans-Regular',
                                                                                    fontSize: switchMerchant ? 10 : 14,
                                                                                }}
                                                                                placeholder={'0'}
                                                                                placeholderTextColor={Platform.select({ios: '#a9a9a9'})}
                                                                                placeholderStyle={styles.textInputPlaceholder}
                                                                                value={voucherType === LOYALTY_PROMOTION_TYPE.TAKE_AMOUNT_OFF ? amountOffMaxQuantity.toString() : percentageOffMaxQuantity.toString()}
                                                                                keyboardType={'decimal-pad'}
                                                                                clearTextOnFocus

                                                                                onFocus={() => {
                                                                                    if(voucherType === PROMOTION_TYPE.TAKE_AMOUNT_OFF){
                                                                                        setTemp(amountOffMaxQuantity);
                                                                                        setAmountOffMaxQuantity('');
                                                                                    }
                                                                                    else if(voucherType === PROMOTION_TYPE.TAKE_PERCENTAGE_OFF){
                                                                                        setTemp(percentageOffMaxQuantity);
                                                                                        setPercentageOffMaxQuantity('');
                                                                                    }
                                                                                }}
                                                                                onEndEditing={() => {
                                                                                    if (voucherType === PROMOTION_TYPE.TAKE_AMOUNT_OFF){
                                                                                        if(amountOffMaxQuantity === '')
                                                                                        setAmountOffMaxQuantity(temp);
                                                                                    }
                                                                                    else if (voucherType === PROMOTION_TYPE.TAKE_PERCENTAGE_OFF){
                                                                                        if (percentageOffMaxQuantity === '')
                                                                                        setPercentageOffMaxQuantity(temp);
                                                                                    }
                                                                                }}
                                                                                onChangeText={(text) => {
                                                                                    if(voucherType === PROMOTION_TYPE.TAKE_AMOUNT_OFF){
                                                                                        setAmountOffMaxQuantity(text);
                                                                                    }
                                                                                    else if(voucherType === PROMOTION_TYPE.TAKE_PERCENTAGE_OFF){
                                                                                        setPercentageOffMaxQuantity(text);
                                                                                    }
                                                                                }}
                                                                            />
                                                                        </View>
                                                                    </View>
                                                                </>
                                                            )}

                                                            {/* Delivery */}
                                                            {/* Takeaway */}
                                                            {(voucherType === LOYALTY_PROMOTION_TYPE.DELIVERY || 
                                                            voucherType === LOYALTY_PROMOTION_TYPE.TAKEAWAY) && 
                                                            (
                                                                <>
                                                                    <View style={{width: '60%', flexDirection: 'row', alignItems: 'center'}}>
                                                                        <View style={{flex: 1, marginRight: 5, marginBottom: 5 }}>
                                                                            <DropDownPicker
                                                                                arrowColor={'black'}
                                                                                arrowSize={20}
                                                                                arrowStyle={styles.dropDownPickerArrowStyle}
                                                                                style={[styles.dropDownPickerStyle, {width: '95%'}]}
                                                                                placeholderStyle={styles.textInputPlaceholder}
                                                                                items={PROMOTION_TYPE_VARIATION_DROPDOWN_LIST}
                                                                                itemStyle={styles.dropDownPickerItemStyle}
                                                                                placeholder={'Products'}
                                                                                customTickIcon={(press) => (
                                                                                    <Ionicon
                                                                                        name={'checkmark-outline'}
                                                                                        color={
                                                                                            press
                                                                                                ? Colors.fieldtBgColor
                                                                                                : Colors.primaryColor
                                                                                        }
                                                                                        size={25}
                                                                                    />
                                                                                )}
                                                                                onChangeItem={(item) => {
                                                                                    setSelectedVariation(item.value);
                                                                                }}
                                                                                defaultValue={selectedVariation}
                                                                                dropDownStyle={[styles.dropDownPickerDDStyle, {width: '95%'}]}
                                                                            />
                                                                        </View>

                                                                        {variationItemsDropdownList.length > 0
                                                                            &&
                                                                            (
                                                                                selectedVariationItems.filter(itemId => {
                                                                                    return variationItemsDropdownList.find(item2 => item2.value === itemId) ? true : false;
                                                                                }).length === selectedVariationItems.length
                                                                                ||
                                                                                selectedVariationItems.length === 0
                                                                            ) && (
                                                                            <View style={{flex: 1, marginRight: 5, marginBottom: 5 }}>
                                                                                <DropDownPicker
                                                                                    arrowColor={'black'}
                                                                                    arrowSize={20}
                                                                                    arrowStyle={styles.dropDownPickerArrowStyle}
                                                                                    style={[styles.dropDownPickerStyle, {width: '95%'}]}
                                                                                    placeholderStyle={styles.textInputPlaceholder}
                                                                                    itemStyle={styles.dropDownPickerItemStyle}
                                                                                    placeholder={'Select a Product'}
                                                                                    multipleText={'%d product(s) selected'}
                                                                                    items={variationItemsDropdownList}
                                                                                    customTickIcon={(press) => (
                                                                                        <Ionicon
                                                                                            name={'checkmark-outline'}
                                                                                            color={
                                                                                                press
                                                                                                    ? Colors.fieldtBgColor
                                                                                                    : Colors.primaryColor
                                                                                            }
                                                                                            size={switchMerchant ? 20 : 25}
                                                                                        />
                                                                                    )}
                                                                                    onChangeItem={(items) =>
                                                                                        setSelectedVariationItems(items)
                                                                                    }
                                                                                    defaultValue={selectedVariationItems}
                                                                                    multiple
                                                                                    searchable
                                                                                    dropDownStyle={[styles.dropDownPickerDDStyle, {width: '95%'}]}
                                                                                />
                                                                            </View>
                                                                        )}
                                                                    </View>
                                                                    
                                                                    {voucherType === LOYALTY_PROMOTION_TYPE.DELIVERY && (
                                                                        <>
                                                                            <View style={{flexDirection: 'row', alignItems: 'center'}}>
                                                                                <Text style={styles.textInputTitle}>Free Delivery Above RM</Text>

                                                                                <TextInput
                                                                                    underlineColorAndroid={Colors.fieldtBgColor}
                                                                                    style={[
                                                                                        styles.textInput,
                                                                                        { 
                                                                                            textAlign: 'center', 
                                                                                            fontSize: switchMerchant ? 10 : 14,
                                                                                            marginVertical: 10,
                                                                                            maxWidth: '10%'
                                                                                        }
                                                                                    ]}
                                                                                    placeholder="0"
                                                                                    placeholderStyle={styles.textInputPlaceholder}
                                                                                    placeholderTextColor={Platform.select({ios: '#a9a9a9'})}
                                                                                    
                                                                                    value={deliveryFreeAboveAmount.toString()}
                                                                                    keyboardType={'decimal-pad'}

                                                                                    clearTextOnFocus
                                                                                    onFocus={() => {
                                                                                        setTemp(deliveryFreeAboveAmount)
                                                                                        setDeliveryFreeAboveAmount('');
                                                                                    }}
                                                                                    onEndEditing={() => {
                                                                                        if (deliveryFreeAboveAmount == '') {
                                                                                            setDeliveryFreeAboveAmount(temp);
                                                                                        }
                                                                                    }}
                                                                                    onChangeText={(text) => {
                                                                                        setDeliveryFreeAboveAmount(parseValidPriceText(text));
                                                                                    }}
                                                                                />

                                                                                <CheckBox
                                                                                    style={{
                                                                                        paddingVertical: 10,
                                                                                        marginLeft: 5
                                                                                    }}
                                                                                    checkBoxColor={Colors.fieldtBgColor}
                                                                                    uncheckedCheckBoxColor={Colors.tabGrey}
                                                                                    checkedCheckBoxColor={Colors.primaryColor}
                                                                                    
                                                                                    isChecked={deliveryFreeFlag}
                                                                                    onClick={() => {
                                                                                        setDeliveryFreeFlag(!deliveryFreeFlag);
                                                                                    }}
                                                                                />
                                                                            </View>

                                                                            <View
                                                                                style={{
                                                                                    flexDirection: 'row',
                                                                                    flex: 1,
                                                                                    alignItems: 'center',
                                                                                    marginTop: '1%',
                                                                                }}>
                                                                                <View style={{ alignItems: 'flex-start' }}>
                                                                                    <Text
                                                                                        style={[
                                                                                            {
                                                                                                color: 'black',
                                                                                                fontFamily: 'NunitoSans-Bold',
                                                                                                fontSize: 14,
                                                                                            },
                                                                                            switchMerchant
                                                                                                ? {
                                                                                                    fontSize: 10,
                                                                                                }
                                                                                                : {},
                                                                                        ]}>
                                                                                        Discount (RM)
                                                                                    </Text>
                                                                                </View>
                                                                                <View
                                                                                    style={{
                                                                                        height: switchMerchant ? 35 : 40,
                                                                                        paddingHorizontal: 5,
                                                                                    }}>
                                                                                    <TextInput
                                                                                        underlineColorAndroid={Colors.fieldtBgColor}
                                                                                        style={[
                                                                                            styles.textInput,
                                                                                            { textAlign: 'center', fontSize: 14 },
                                                                                            switchMerchant
                                                                                                ? {
                                                                                                    fontSize: 10,
                                                                                                }
                                                                                                : {},
                                                                                        ]}
                                                                                        placeholder="0"
                                                                                        placeholderTextColor={Platform.select({
                                                                                            ios: '#a9a9a9',
                                                                                        })}
                                                                                        placeholderStyle={{
                                                                                            fontFamily: 'NunitoSans-Regular',
                                                                                            fontSize: switchMerchant ? 10 : 14,
                                                                                        }}
                                                                                        //iOS
                                                                                        clearTextOnFocus
                                                                                        //////////////////////////////////////////////
                                                                                        //Android
                                                                                        onFocus={() => {
                                                                                            setTemp(deliveryDiscountAmount)
                                                                                            setDeliveryDiscountAmount('');
                                                                                        }}
                                                                                        ///////////////////////////////////////////////
                                                                                        //When textinput is not selected
                                                                                        onEndEditing={() => {
                                                                                            if (deliveryDiscountAmount == '') {
                                                                                                setDeliveryDiscountAmount(temp);
                                                                                            }
                                                                                        }}
                                                                                        onChangeText={(text) => {
                                                                                            setDeliveryDiscountAmount(parseValidPriceText(text));
                                                                                        }}
                                                                                        value={deliveryDiscountAmount.toString()}
                                                                                        // ref={myTextInput}
                                                                                        keyboardType={'decimal-pad'}
                                                                                    />
                                                                                </View>

                                                                                <View style={{ marginLeft: 15 }}>
                                                                                    <Text
                                                                                        style={[
                                                                                            {
                                                                                                color: 'black',
                                                                                                fontSize: 14,
                                                                                                fontFamily: 'NunitoSans-Bold',
                                                                                            },
                                                                                            switchMerchant
                                                                                                ? {
                                                                                                    fontSize: 10,
                                                                                                }
                                                                                                : {},
                                                                                        ]}>
                                                                                        Order Above (RM)
                                                                                    </Text>
                                                                                </View>

                                                                                <View
                                                                                    style={{
                                                                                        height: switchMerchant ? 35 : 40,
                                                                                        paddingHorizontal: 5,
                                                                                    }}>
                                                                                    <TextInput
                                                                                        underlineColorAndroid={Colors.fieldtBgColor}
                                                                                        style={[
                                                                                            styles.textInput,
                                                                                            { textAlign: 'center', fontSize: 14 },
                                                                                            switchMerchant
                                                                                                ? {
                                                                                                    fontSize: 10,
                                                                                                }
                                                                                                : {},
                                                                                        ]}
                                                                                        placeholder="0"
                                                                                        placeholderTextColor={Platform.select({
                                                                                            ios: '#a9a9a9',
                                                                                        })}
                                                                                        //iOS
                                                                                        clearTextOnFocus
                                                                                        //////////////////////////////////////////////
                                                                                        //Android
                                                                                        onFocus={() => {
                                                                                            setTemp(deliveryDiscountAboveAmount)
                                                                                            setDeliveryDiscountAboveAmount('');
                                                                                        }}
                                                                                        ///////////////////////////////////////////////
                                                                                        //When textinput is not selected
                                                                                        onEndEditing={() => {
                                                                                            if (deliveryDiscountAboveAmount == '') {
                                                                                                setDeliveryDiscountAboveAmount(temp);
                                                                                            }
                                                                                        }}
                                                                                        onChangeText={(text) => {
                                                                                            setDeliveryDiscountAboveAmount(parseValidPriceText(text));
                                                                                        }}
                                                                                        value={deliveryDiscountAboveAmount.toString()}
                                                                                        // ref={myTextInput}
                                                                                        keyboardType={'decimal-pad'}
                                                                                    />
                                                                                </View>
                                                                            </View>
                                                                        </>
                                                                    )}

                                                                    {voucherType === LOYALTY_PROMOTION_TYPE.TAKEAWAY && (
                                                                        <>
                                                                            <View style={{flexDirection: 'row', alignItems: 'center'}}>
                                                                                <Text style={styles.textInputTitle}>Free Takeaway Above RM</Text>

                                                                                <TextInput
                                                                                    underlineColorAndroid={Colors.fieldtBgColor}
                                                                                    style={[
                                                                                        styles.textInput,
                                                                                        { 
                                                                                            textAlign: 'center', 
                                                                                            fontSize: switchMerchant ? 10 : 14,
                                                                                            marginVertical: 10,
                                                                                            maxWidth: '10%'
                                                                                        }
                                                                                    ]}
                                                                                    placeholder="0"
                                                                                    placeholderStyle={styles.textInputPlaceholder}
                                                                                    placeholderTextColor={Platform.select({ios: '#a9a9a9'})}
                                                                                    
                                                                                    value={takeawayFreeAboveAmount.toString()}
                                                                                    keyboardType={'decimal-pad'}

                                                                                    clearTextOnFocus
                                                                                    onFocus={() => {
                                                                                        setTemp(takeawayFreeAboveAmount)
                                                                                        setTakeawayFreeAboveAmount('');
                                                                                    }}
                                                                                    onEndEditing={() => {
                                                                                        if (takeawayFreeAboveAmount == '') {
                                                                                            setTakeawayFreeAboveAmount(temp);
                                                                                        }
                                                                                    }}
                                                                                    onChangeText={(text) => {
                                                                                        setTakeawayFreeAboveAmount(parseValidPriceText(text));
                                                                                    }}
                                                                                />

                                                                                <CheckBox
                                                                                    style={{
                                                                                        paddingVertical: 10,
                                                                                        marginLeft: 5
                                                                                    }}
                                                                                    checkBoxColor={Colors.fieldtBgColor}
                                                                                    uncheckedCheckBoxColor={Colors.tabGrey}
                                                                                    checkedCheckBoxColor={Colors.primaryColor}
                                                                                    
                                                                                    isChecked={takeawayFreeFlag}
                                                                                    onClick={() => {
                                                                                        setTakeawayFreeFlag(!takeawayFreeFlag);
                                                                                    }}
                                                                                />
                                                                            </View>

                                                                            <View style={{flexDirection: 'row'}}>
                                                                                <View style={{
                                                                                    flex: 3.5,
                                                                                    flexDirection: 'row',
                                                                                    alignItems: 'center',
                                                                                }}>
                                                                                    <Text style={styles.textInputTitle}>Discount (RM)</Text>

                                                                                    <View style={{
                                                                                        height: switchMerchant ? 35 : 40,
                                                                                        paddingHorizontal: 5
                                                                                    }}>
                                                                                        <TextInput
                                                                                            underlineColorAndroid={Colors.fieldtBgColor}
                                                                                            style={[
                                                                                                styles.textInput,
                                                                                                { textAlign: 'center', fontSize: switchMerchant ? 10 : 14 }
                                                                                            ]}
                                                                                            placeholder="0"
                                                                                            placeholderStyle={styles.textInputPlaceholder}
                                                                                            placeholderTextColor={Platform.select({ios: '#a9a9a9'})}
                                                                                            value={takeawayDiscountAmount.toString()}
                                                                                            keyboardType={'decimal-pad'}

                                                                                            clearTextOnFocus
                                                                                            onFocus={() => {
                                                                                                setTemp(takeawayDiscountAmount)
                                                                                                setTakeawayDiscountAmount('');
                                                                                            }}
                                                                                            onEndEditing={() => {
                                                                                                if (takeawayDiscountAmount == '') {
                                                                                                    setTakeawayDiscountAmount(temp);
                                                                                                }
                                                                                            }}
                                                                                            onChangeText={(text) => {
                                                                                                setTakeawayDiscountAmount(parseValidPriceText(text));
                                                                                            }}
                                                                                        />
                                                                                    </View>

                                                                                    <View style={{ marginLeft: 15 }}>
                                                                                        <Text style={styles.textInputTitle}>Order Above (RM)</Text>
                                                                                    </View>

                                                                                    <View
                                                                                        style={{
                                                                                            height: switchMerchant ? 35 : 40,
                                                                                            paddingHorizontal: 5,
                                                                                        }}>
                                                                                        <TextInput
                                                                                            underlineColorAndroid={Colors.fieldtBgColor}
                                                                                            style={[
                                                                                                styles.textInput,
                                                                                                { textAlign: 'center', fontSize: 14 },
                                                                                                switchMerchant
                                                                                                    ? {
                                                                                                        fontSize: 10,
                                                                                                    }
                                                                                                    : {},
                                                                                            ]}
                                                                                            placeholder="0"
                                                                                            placeholderStyle={{
                                                                                                fontFamily: 'NunitoSans-Regular',
                                                                                                fontSize: switchMerchant ? 10 : 14,
                                                                                            }}
                                                                                            //iOS
                                                                                            clearTextOnFocus
                                                                                            //////////////////////////////////////////////
                                                                                            //Android
                                                                                            onFocus={() => {
                                                                                                setTemp(takeawayDiscountAboveAmount)
                                                                                                setTakeawayDiscountAboveAmount('');
                                                                                            }}
                                                                                            ///////////////////////////////////////////////
                                                                                            //When textinput is not selected
                                                                                            onEndEditing={() => {
                                                                                                if (takeawayDiscountAboveAmount == '') {
                                                                                                    setTakeawayDiscountAboveAmount(temp);
                                                                                                }
                                                                                            }}
                                                                                            onChangeText={(text) => {
                                                                                                setTakeawayDiscountAboveAmount(parseValidPriceText(text));
                                                                                            }}
                                                                                            placeholderTextColor={Platform.select({
                                                                                                ios: '#a9a9a9',
                                                                                            })}
                                                                                            value={takeawayDiscountAboveAmount.toString()}
                                                                                            keyboardType={'decimal-pad'}
                                                                                        />
                                                                                    </View>
                                                                                </View>
                                                                            </View>
                                                                        </>
                                                                    )}
                                                                </>
                                                            )}
                                                        </View>

                                                </View>
                                            </View>
                                        </View>
                                        
                                        {/* Step 2 */}
                                        <View style={{marginBottom: 30}}>
                                            <Text style={styles.stepText}>Step - 2</Text>
                                            <View style={styles.divider}/>
                                            
                                            {/* Step 2 - Content */}
                                            <View style={{flexDirection: 'column', marginHorizontal: 15}}>

                                                {/* FlexRowContainer - Voucher Name / Voucher Description */}
                                                <View style={{flexDirection: 'row', marginTop: '2%', width: '100%'}}>

                                                    {/* Voucher Name */}
                                                    <View style={{flex: 1}}>
                                                        <Text style={styles.textInputTitle}>Voucher Name</Text>

                                                        <TextInput
                                                            style={styles.textInputField}
                                                            placeholder="Voucher Name"
                                                            placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                                                            placeholderStyle={styles.textInputPlaceholder}
                                                            value={campaignName}
                                                            clearTextOnFocus

                                                            onFocus={() => {
                                                                setTemp(campaignName)
                                                                LoyaltyStore.update(s => {
                                                                    s.name = '';
                                                                });
                                                            }}
                                                            onEndEditing={() => {
                                                                if (campaignName == '') {
                                                                    LoyaltyStore.update(s => {
                                                                        s.name = temp;
                                                                    });
                                                                }
                                                            }}
                                                            onChangeText={(text) => {
                                                                LoyaltyStore.update(s => {
                                                                    s.name = text;
                                                                });
                                                            }}
                                                        />
                                                    </View>

                                                    {/* Voucher Description */}
                                                    <View style={{flex: 1}}>
                                                        <Text style={styles.textInputTitle}>Voucher Description</Text>

                                                        <TextInput 
                                                            style={styles.textInputField}
                                                            placeholder="Description..."
                                                            placeholderStyle={styles.textInputPlaceholder}
                                                            placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                                                            value={campaignDescription}
                                                            multiline
                                                            clearTextOnFocus
                                                            
                                                            onFocus={() => {
                                                                setTemp(campaignDescription)
                                                                LoyaltyStore.update(s => {
                                                                    s.description = '';
                                                                });
                                                            }}

                                                            onEndEditing={() => {
                                                                if (campaignDescription == '') {
                                                                    LoyaltyStore.update(s => {
                                                                        s.description = temp;
                                                                    });
                                                                }
                                                            }}
                                                            onChangeText={(text) => {
                                                                LoyaltyStore.update(s => {
                                                                    s.description = text;
                                                                });
                                                            }}
                                                        />
                                                    </View>
                                                </View>

                                                {/* FlexRowContainer - Availability / Apply to Outlets  */}
                                                <View style={{flexDirection: 'row', marginTop: '2%', width: '100%'}}>
                                                    
                                                    {/* Availability */}
                                                    <View style={{ flex: 1 }}>
                                                        
                                                        {/* Apply to Channel */}
                                                        <Text style={styles.textInputTitle}>Apply to Channel</Text>
                                                        
                                                        <View style={{marginTop: 5, flexDirection: 'row'}}>
                                                            <View style={{width: '80%'}}>
                                                                <DropDownPicker
                                                                    arrowColor={'black'}
                                                                    arrowSize={20}
                                                                    arrowStyle={styles.dropDownPickerArrowStyle}
                                                                    style={[styles.dropDownPickerStyle, {width: '100%'}]}
                                                                    items={CHANNEL_TYPE_DROP_DOWN_LIST}
                                                                    itemStyle={styles.dropDownPickerItemStyle}
                                                                    dropDownStyle={[styles.dropDownPickerDDStyle, {width: '100%'}]}
                                                                    placeholder={'Select Channel(s)'}
                                                                    placeholderStyle={styles.textInputPlaceholder}
                                                                    onChangeItem={(items) => {
                                                                        LoyaltyStore.update((s) => {s.orderType = items});
                                                                    }}
                                                                    defaultValue={orderTypes}
                                                                    multiple
                                                                    multipleText={CHANNEL_TYPE_DROP_DOWN_LIST.length === orderTypes.length ? 'All selected' : '%d availability selected'}
                                                                    customTickIcon={() => (
                                                                        <Ionicon
                                                                            name={'checkmark-outline'}
                                                                            color={Colors.primaryColor}
                                                                            size={25}
                                                                        />
                                                                    )}
                                                                    searchable
                                                                />
                                                            </View>

                                                            <View style={{marginLeft: 5, justifyContent:'center', alignItems:'center'}}>

                                                                <TouchableOpacity
                                                                    onPress={() => {
                                                                        setSelectAllOrderTypes(!selectAllOrderTypes);

                                                                        LoyaltyStore.update((s) => {
                                                                            s.orderType = selectAllOrderTypes 
                                                                                ? [CHANNEL_TYPE_DROP_DOWN_LIST[0].value] 
                                                                                : CHANNEL_TYPE_DROP_DOWN_LIST.map(item => item.value);
                                                                        })
                                                                    }}
                                                                >
                                                                    <View style={[styles.toggleButtonStyle, {backgroundColor: selectAllOrderTypes ? Colors.primaryColor : '#ACACAC'}]}>
                                                                        <Icon name="checkmark-sharp" size={20} color={'#FFFFFF'} style={{margin: 2}} />
                                                                    </View>
                                                                </TouchableOpacity>

                                                            </View>
                                                        </View>

                                                    </View>

                                                    {/* Apply to Outlets */}
                                                    <View style={{ flex: 1, }}>

                                                        <Text style={styles.textInputTitle}>Apply to Outlets</Text>

                                                        <View style={{ marginTop: 5 }}>
                                                            <DropDownPicker
                                                                arrowColor={'black'}
                                                                arrowSize={20}
                                                                arrowStyle={styles.dropDownPickerArrowStyle}
                                                                style={styles.dropDownPickerStyle}
                                                                items={outletDropdownList}
                                                                itemStyle={styles.dropDownPickerItemStyle}
                                                                dropDownStyle={styles.dropDownPickerDDStyle}
                                                                placeholder={'Select outlet(s)'}
                                                                placeholderStyle={styles.textInputPlaceholder}
                                                                onChangeItem={(items) => {
                                                                    LoyaltyStore.update(s => {
                                                                        s.outletIdList = items;
                                                                    });
                                                                }}
                                                                defaultValue={outletIdList}
                                                                multipleText={'%d outlet(s) selected'}
                                                                multiple
                                                                customTickIcon={(press) => (
                                                                    <Ionicon
                                                                        name={'checkmark-outline'}
                                                                        color={
                                                                            press
                                                                                ? Colors.fieldtBgColor
                                                                                : Colors.primaryColor
                                                                        }
                                                                        size={switchMerchant ? 20 : 25}
                                                                    />
                                                                )}
                                                            />
                                                        </View>
                                                    </View>

                                                </View>

                                                {/* FlexRowContainer - Voucher Date / Voucher Time  */}
                                                <View style={{flexDirection: 'row', marginTop: '2%', width: '100%'}}>

                                                    {/* Voucher Date */}
                                                    <View style={{ flex: 1 }}>
                                                        <Text style={styles.textInputTitle}>Voucher Date</Text>

                                                        <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                                                            <TouchableOpacity onPress={() => {
                                                                setShowPromoDateStartPicker(true);
                                                            }}
                                                                style={[{
                                                                    backgroundColor: Colors.fieldtBgColor,
                                                                    width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 90 : 115,
                                                                    padding: 5,
                                                                    height: 40,
                                                                    borderRadius: 5,
                                                                    marginTop: 5,
                                                                    alignItems: 'center',
                                                                    justifyContent: 'center',
                                                                    borderWidth: 1,
                                                                    borderColor: '#E5E5E5',
                                                                    fontFamily: 'NunitoSans-Regular',
                                                                    fontSize: 14
                                                                }, switchMerchant ? {
                                                                    fontSize: 10,
                                                                    width: 93,
                                                                    height: 35,
                                                                } : {}]}
                                                            >
                                                                <Text style={[{ fontVariant: ['tabular-nums'], fontFamily: 'NunitoSans-Regular', fontSize: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 12 : 14 }, switchMerchant ? {
                                                                    fontSize: 10,
                                                                } : {}]}>{moment(promoDateStart).format('DD MMM YYYY')}</Text>
                                                            </TouchableOpacity>

                                                            <Text style={{ 
                                                                marginLeft: '2%', 
                                                                marginRight: '2%', 
                                                                fontWeight: '500', 
                                                                fontFamily: 'NunitoSans-Regular', 
                                                                fontSize: switchMerchant ? 10 : 14
                                                            }}>
                                                                to
                                                            </Text>

                                                            <TouchableOpacity 
                                                                onPress={() => {setShowPromoDateEndPicker(true)}}
                                                                style={[{
                                                                    backgroundColor: Colors.fieldtBgColor,
                                                                    width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 90 : 115,
                                                                    height: 40,
                                                                    borderRadius: 5,
                                                                    padding: 1,
                                                                    marginTop: 5,
                                                                    alignItems: 'center',
                                                                    justifyContent: 'center',
                                                                    borderWidth: 1,
                                                                    borderColor: '#E5E5E5',
                                                                    fontFamily: 'NunitoSans-Regular',
                                                                    fontSize: 14
                                                                }, switchMerchant ? {
                                                                    fontSize: 10,
                                                                    width: 93,
                                                                    height: 35,
                                                                } : {}]}>
                                                                <Text style={[{ fontVariant: ['tabular-nums'], fontFamily: 'NunitoSans-Regular', fontSize: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 12 : 14 }, switchMerchant ? {
                                                                    fontSize: 10,
                                                                } : {}]}>{moment(promoDateEnd).format('DD MMM YYYY')}</Text>
                                                            </TouchableOpacity>
                                                        </View>
                                                    </View>
                                                    
                                                    {/* Voucher Time */}
                                                    <View style={{ flex: 1, }}>
                                                        <Text style={styles.textInputTitle}>Voucher Time</Text>

                                                        <View style={{ flexDirection: 'row', alignItems: 'center', }}>
                                                            <TouchableOpacity onPress={() => {
                                                                setShowPromoTimeStartPicker(true);
                                                            }}
                                                                style={[{
                                                                    backgroundColor: Colors.fieldtBgColor,
                                                                    // width: 115,
                                                                    width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 90 : 115,
                                                                    height: 40,
                                                                    borderRadius: 5,
                                                                    padding: 1,
                                                                    marginTop: 5,
                                                                    alignItems: 'center',
                                                                    justifyContent: 'center',
                                                                    borderWidth: 1,
                                                                    borderColor: '#E5E5E5',
                                                                    fontFamily: 'NunitoSans-Regular', fontSize: 14
                                                                }, switchMerchant ? {
                                                                    height: 35
                                                                } : {}]}>
                                                                <Text style={[{ fontVariant: ['tabular-nums'], fontFamily: 'NunitoSans-Regular', fontSize: 14 }, switchMerchant ? {
                                                                    fontSize: 10,
                                                                } : {}]}>{moment(promoTimeStart).format('hh:mm A')}</Text>
                                                            </TouchableOpacity>
                                                            <Text style={[{ marginLeft: '2%', marginRight: '2%', fontWeight: '500', textAlign: 'center', fontFamily: 'NunitoSans-Regular', fontSize: 14 }, switchMerchant ? {
                                                                fontSize: 10,
                                                            } : {}]}>to</Text>
                                                            <TouchableOpacity onPress={() => {
                                                                setShowPromoTimeEndPicker(true);
                                                            }}
                                                                style={[{
                                                                    backgroundColor: Colors.fieldtBgColor,
                                                                    // width: 115,
                                                                    width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 90 : 115,
                                                                    height: 40,
                                                                    borderRadius: 5,
                                                                    padding: 1,
                                                                    marginTop: 5,
                                                                    alignItems: 'center',
                                                                    justifyContent: 'center',
                                                                    borderWidth: 1,
                                                                    borderColor: '#E5E5E5',
                                                                    fontFamily: 'NunitoSans-Regular', fontSize: 14
                                                                }, switchMerchant ? {
                                                                    height: 35
                                                                } : {}]}>
                                                                <Text style={[{ fontVariant: ['tabular-nums'], fontFamily: 'NunitoSans-Regular', fontSize: 14 }, switchMerchant ? {
                                                                    fontSize: 10,
                                                                } : {}]}>{moment(promoTimeEnd).format('hh:mm A')}</Text>
                                                            </TouchableOpacity>
                                                        </View>
                                                    </View>

                                                </View>

                                                {/* FlexRowContainer - Effective / Claimable After Order Placed */}
                                                <View style={{flexDirection: 'row', marginTop: '2%', width: '100%'}}>
                                                    
                                                    <View style={{flex: 1, flexDirection: 'column'}}>
                                                        <View style={{ flexDirection: 'row' }}>
                                                            <Text style={styles.textInputTitle}>Effective</Text>

                                                            <Text
                                                                style={[
                                                                    styles.textInputTitle,
                                                                    {
                                                                        marginLeft: 5,
                                                                        color: 'red',
                                                                        fontFamily: 'NunitoSans-Bold',
                                                                        fontSize: 14,
                                                                    }
                                                                ]}>
                                                                Options
                                                            </Text>
                                                        </View>

                                                        <View style={{marginTop: 5}}>

                                                            <DropDownPicker
                                                                arrowColor={'black'}
                                                                arrowSize={20}
                                                                arrowStyle={styles.dropDownPickerArrowStyle}
                                                                style={styles.dropDownPickerStyle}
                                                                items={EFFECTIVE_DAY_DROPDOWN_LIST}
                                                                itemStyle={styles.dropDownPickerItemStyle}
                                                                dropDownStyle={styles.dropDownPickerDDStyle}
                                                                placeholder={'Select Effective Option(s)'}
                                                                placeholderStyle={styles.textInputPlaceholder}
                                                                onChangeItem={(items) => {
                                                                    LoyaltyStore.update(s => {
                                                                        s.effectiveTypeOption = items;
                                                                    });
                                                                }}
                                                                defaultValue={effectiveTypeOptions}
                                                                multiple
                                                                multipleText={'%d day(s) selected'}
                                                                customTickIcon={(press) => (
                                                                    <Ionicon
                                                                        name={'checkmark-outline'}
                                                                        color={
                                                                            press
                                                                                ? Colors.fieldtBgColor
                                                                                : Colors.primaryColor
                                                                        }
                                                                        size={switchMerchant ? 20 : 25}
                                                                    />
                                                                )}
                                                            />
                                                        </View>

                                                    </View>

                                                    <View style={{flex: 1, flexDirection: 'column'}}>
                                                        <Text style={styles.textInputTitle}>Free to Claim</Text>

                                                        <View style={{flexDirection: 'row', alignItems: 'center', height: windowHeight * 0.07}}>
                                                            <TouchableOpacity onPress={() => {setIsFreeToClaimVoucher(!isFreeToClaimVoucher)}}>
                                                                <View style={[styles.toggleButtonStyle, {backgroundColor: isFreeToClaimVoucher ? Colors.primaryColor : '#ACACAC'}]}>
                                                                    <Icon name="checkmark-sharp" size={20} color={'#FFFFFF'} style={{}} />
                                                                </View>
                                                            </TouchableOpacity>

                                                            <Text style={[styles.textInputTitle, {fontFamily: 'NunitoSans-Regular'}]}>Claimable After Order Placed</Text>
                                                        </View>
                                                    </View>
                                                    
                                                </View>

                                            </View>
                                        </View>
                                        
                                        {/* Step 3 */}
                                        <View style={{marginBottom: 30}}>
                                            <Text style={styles.stepText}>Step - 3</Text>
                                            <View style={styles.divider}/>
                                            
                                            {/* Step 3 - Content */}
                                            <View style={{flexDirection: 'column', marginHorizontal: 15}}>

                                                {/* FlexRowContainer - Voucher Code Format / Voucher Generic Code */}
                                                <View style={{flexDirection: 'row', marginTop: '2%', width: '100%'}}>

                                                    {/* Voucher Code Format */}
                                                    <View style={{width: '50%'}}>
                                                        <Text style={styles.textInputTitle}>Voucher Code Format</Text>

                                                        <View style={{marginTop: 5}}>
                                                            <DropDownPicker
                                                                arrowColor={'black'}
                                                                arrowSize={20}
                                                                arrowStyle={styles.dropDownPickerArrowStyle}
                                                                style={styles.dropDownPickerStyle}
                                                                items={[
                                                                    {
                                                                        label: 'Generic',
                                                                        value: MERCHANT_VOUCHER_CODE_FORMAT.GENERIC,
                                                                    },
                                                                    {
                                                                        label: 'Unique',
                                                                        value: MERCHANT_VOUCHER_CODE_FORMAT.UNIQUE,
                                                                    },
                                                                ]}
                                                                itemStyle={styles.dropDownPickerItemStyle}
                                                                dropDownStyle={styles.dropDownPickerDDStyle}
                                                                defaultValue={voucherCodeFormat}
                                                                placeholder={'Voucher Type'}
                                                                placeholderStyle={styles.textInputPlaceholder}
                                                                onChangeItem={(item) => {
                                                                    setVoucherCodeFormat(item.value);
                                                                }}
                                                            />
                                                        </View>
                                                    </View>
                                                    
                                                    {/* Voucher Generic Code - Display when Voucher Code Format selected as Generic */}
                                                    {voucherCodeFormat === MERCHANT_VOUCHER_CODE_FORMAT.GENERIC && (
                                                        <View style={{ flex: 1 }}>
                                                            <Text style={styles.textInputTitle}>Voucher Generic Code</Text>

                                                            <TextInput
                                                                style={styles.textInputField}
                                                                placeholder="DISCOUNT100"
                                                                placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                                                                placeholderStyle={styles.textInputPlaceholder}
                                                                value={voucherCodeValueGeneric}
                                                                clearTextOnFocus
                                                                onFocus={() => {
                                                                    setTemp(voucherCodeValueGeneric)
                                                                    setVoucherCodeValueGeneric('');
                                                                }}
                                                                onEndEditing={() => {
                                                                    if (voucherCodeValueGeneric == '') {
                                                                        setVoucherCodeValueGeneric(temp);
                                                                    }
                                                                }}
                                                                onChangeText={(text) => {
                                                                    setVoucherCodeValueGeneric(text);
                                                                }}
                                                            />
                                                        </View>
                                                    )}
                                                </View>

                                                {/* FlexRowContainer - Voucher Quantity / Voucher Max Claim Per User */}
                                                <View style={{flexDirection: 'row', marginTop: '2%', width: '100%'}}>
                                                    
                                                    {/* Voucher Quantity */}
                                                    <View style={{ flex: 1}}>
                                                        <Text style={styles.textInputTitle}>Voucher Quantity</Text>

                                                        <TextInput
                                                            style={styles.textInputField}
                                                            placeholder="Enter Amount..."
                                                            placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                                                            placeholderStyle={styles.textInputPlaceholder}
                                                            clearTextOnFocus

                                                            onFocus={() => {
                                                                setTemp(voucherQuantity)
                                                                setVoucherQuantity('');
                                                            }}
                                                            onEndEditing={() => {
                                                                if (voucherQuantity == '') {
                                                                    setVoucherQuantity(temp);
                                                                }
                                                            }}
                                                            onChangeText={(text) => {
                                                                setVoucherQuantity(text);
                                                            }}
                                                            value={voucherQuantity.toString()}
                                                            keyboardType='numeric'
                                                        />
                                                    </View>

                                                    {/* Voucher Max Claim Per User */}
                                                    <View style={{ flex: 1}}>
                                                        <Text style={styles.textInputTitle}>Voucher Max Claim Per User</Text>

                                                        <TextInput
                                                            style={styles.textInputField}
                                                            placeholder="Enter Amount..."
                                                            placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                                                            placeholderStyle={styles.textInputPlaceholder}
                                                            clearTextOnFocus

                                                            onFocus={() => {
                                                                setTemp(voucherMaxClaimPerUser)
                                                                setVoucherMaxClaimPerUser('');
                                                            }}
                                                            onEndEditing={() => {
                                                                if (voucherMaxClaimPerUser == '') {
                                                                    setVoucherMaxClaimPerUser(temp);
                                                                }
                                                            }}
                                                            onChangeText={(text) => {
                                                                setVoucherMaxClaimPerUser(text);
                                                            }}
                                                            value={voucherMaxClaimPerUser.toString()}
                                                            keyboardType='numeric'
                                                        />
                                                    </View>

                                                </View>

                                                {/* FlexRowContainer - Target Segment(s), Apply to Channel / Voucher Terms */}
                                                <View style={{flexDirection: 'row', marginTop: '2%', width: '100%'}}>

                                                    {/* Target Segment(s) / Apply to Channel Containers */}
                                                    <View style={{ flex: 1, flexDirection: 'row'}}>

                                                        {/* Target Segment(s) */}
                                                        <View style={{width: '100%'}}>
                                                            <Text style={styles.textInputTitle}>Target Segment(s)</Text>

                                                            {(CRM_SEGMENT_DROPDOWN_LIST.concat(crmSegmentsDropdownList).find((item) =>
                                                                targetSegmentGroupList.includes(item.value)) || targetSegmentGroupList.length === 0) && 
                                                            (
                                                                <View style={{ marginTop: 5 }}>
                                                                    <DropDownPicker
                                                                        arrowColor={'black'}
                                                                        arrowSize={20}
                                                                        arrowStyle={styles.dropDownPickerArrowStyle}
                                                                        style={[styles.dropDownPickerStyle]}
                                                                        items={CRM_SEGMENT_DROPDOWN_LIST.concat(crmSegmentsDropdownList)}
                                                                        itemStyle={styles.dropDownPickerItemStyle}
                                                                        dropDownStyle={[styles.dropDownPickerDDStyle]}
                                                                        placeholder={'Select Segment(s)'}
                                                                        placeholderStyle={styles.textInputPlaceholder}
                                                                        onChangeItem={(items) => {
                                                                            setTargetSegmentGroupList(items);
                                                                        }}
                                                                        defaultValue={targetSegmentGroupList}
                                                                        multiple
                                                                        multipleText={'%d segment(s) selected'}
                                                                        customTickIcon={() => (
                                                                            <Ionicon
                                                                                name={'checkmark-outline'}
                                                                                color={Colors.primaryColor}
                                                                                size={25}
                                                                            />
                                                                        )}
                                                                        searchable
                                                                    />
                                                                </View>
                                                            )}
                                                            
                                                        </View>
                                                    </View>
                                                    
                                                    {/* Voucher Terms */}
                                                    <View style={{ flex: 1, flexDirection: 'column'}}>
                                                        <Text style={styles.textInputTitle}>Voucher Terms</Text>

                                                        <TextInput
                                                            style={styles.textInputField}
                                                            placeholder="Voucher Terms"
                                                            placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                                                            placeholderStyle={styles.textInputPlaceholder}
                                                            clearTextOnFocus

                                                            onFocus={() => {
                                                                setTemp(voucherTerms)
                                                                setVoucherTerms('');
                                                            }}
                                                            onEndEditing={() => {
                                                                if (voucherTerms == '') {
                                                                    setVoucherTerms(temp);
                                                                }
                                                            }}
                                                            onChangeText={(text) => {
                                                                setVoucherTerms(text);
                                                            }}
                                                            value={voucherTerms}
                                                        />
                                                    </View>

                                                </View>

                                                {/* FlexRowContainer - IsOnline, Info to Collected / Redeem Type */}
                                                <View style={{flexDirection: 'row', marginTop: '2%', width: '100%'}}>
                                                    
                                                    {/* Is Online / Info to Collected */}
                                                    <View style={{flex: 1}}>
                                                        
                                                        {/* Text Row */}
                                                        <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                                                            <View style={{width: '30%'}}>
                                                                <Text style={styles.textInputTitle}>Is Online</Text>
                                                            </View>

                                                            <View style={{width: '50%'}}>
                                                                <Text style={styles.textInputTitle}>Info to Collected</Text>
                                                            </View>
                                                        </View>

                                                        {/* Input Row */}
                                                        <View style={{flexDirection: 'row', marginTop: 5}}>

                                                            <View style={{width: '30%',flexDirection: 'row', alignItems: 'center'}}>

                                                                <TouchableOpacity onPress={() => {setIsAvailableOnline(!isAvailableOnline)}}>
                                                                    <View style={[styles.toggleButtonStyle, {backgroundColor: isAvailableOnline ? Colors.primaryColor : '#ACACAC'}]}>
                                                                        <Icon name="checkmark-sharp" size={20} color={'#FFFFFF'} style={{}} />
                                                                    </View>
                                                                </TouchableOpacity>

                                                                <Text style={[styles.textInputTitle, {fontFamily: 'NunitoSans-Regular'}]}>Online</Text>
                                                            </View>

                                                            <View style={{width: '50%'}}>
                                                                <DropDownPicker
                                                                    arrowColor={'black'}
                                                                    arrowSize={20}
                                                                    arrowStyle={styles.dropDownPickerArrowStyle}
                                                                    style={styles.dropDownPickerStyle}
                                                                    items={USER_INFO_TO_COLLECTED_TYPE_DROPDOWN_LIST}
                                                                    itemStyle={styles.dropDownPickerItemStyle}
                                                                    dropDownStyle={styles.dropDownPickerDDStyle}
                                                                    placeholder={'Select'}
                                                                    placeholderStyle={styles.textInputPlaceholder}
                                                                    onChangeItem={(items) => {
                                                                        setUserInfoToCollectedList(items);
                                                                    }}
                                                                    defaultValue={userInfoToCollectedList}
                                                                    multiple
                                                                    multipleText={'%d info(s) selected'}
                                                                    customTickIcon={() => (
                                                                        <Ionicon
                                                                            name={'checkmark-outline'}
                                                                            color={Colors.primaryColor}
                                                                            size={25}
                                                                        />
                                                                    )}
                                                                    searchable
                                                                />
                                                            </View>
                                                        </View>

                                                    </View>
                                                    
                                                    {/* Redeem Type */}
                                                    <View style={{flex: 1}}>
                                                        <Text styles={styles.textInputTitle}>Redeem Type</Text>

                                                        <View style={{ marginTop: 5}}>
                                                            <DropDownPicker
                                                                arrowColor={'black'}
                                                                arrowSize={20}
                                                                arrowStyle={styles.dropDownPickerArrowStyle}
                                                                style={styles.dropDownPickerStyle}
                                                                items={VOUCHER_REDEEM_TYPE_DROPDOWN_LIST}
                                                                itemStyle={styles.dropDownPickerItemStyle}
                                                                dropDownStyle={styles.dropDownPickerDDStyle}
                                                                placeholder={'Select'}
                                                                placeholderStyle={styles.textInputPlaceholder}
                                                                onChangeItem={(item) => {
                                                                    setSelectedVoucherRedeemType(item.value);
                                                                }}
                                                                defaultValue={voucherRedeemType}
                                                            />
                                                        </View>
                                                    </View>
                                                </View>

                                                {/* FlexRowContainer - Activation Day(s) / Expiration Day(s) */}
                                                <View style={{flexDirection: 'row', marginTop: '2%', width: '100%'}}>

                                                    {/* Activation Day(s) */}
                                                    <View style={{flex: 1}}>

                                                        <Text style={styles.textInputTitle}>Activation Day(s)</Text>
                                                        
                                                        <View style={{marginTop: 5}}>
                                                            <DropDownPicker
                                                                arrowColor={'black'}
                                                                arrowSize={20}
                                                                arrowStyle={styles.dropDownPickerArrowStyle}
                                                                style={styles.dropDownPickerStyle}
                                                                items={VOUCHER_ACTIVATION_DAYS_DROPDOWN_LIST}
                                                                itemStyle={styles.dropDownPickerItemStyle}
                                                                dropDownStyle={styles.dropDownPickerDDStyle}
                                                                placeholder={'Select'}
                                                                placeholderStyle={styles.textInputPlaceholder}
                                                                defaultValue={activationDays}
                                                                onChangeItem={(item) => {
                                                                    setActivationDays(item.value);
                                                                }}
                                                            />
                                                        </View>

                                                    </View>

                                                    {/* Expiration Day(s) */}
                                                    <View style={{flex: 1}}>

                                                        <Text style={styles.textInputTitle}>Expiration Day(s)</Text>

                                                        <View style={{ marginTop: 5}}>
                                                            <DropDownPicker
                                                                arrowColor={'black'}
                                                                arrowSize={20}
                                                                arrowStyle={styles.dropDownPickerArrowStyle}
                                                                style={styles.dropDownPickerStyle}
                                                                items={VOUCHER_EXPIRATION_DAYS_DROPDOWN_LIST}
                                                                itemStyle={styles.dropDownPickerItemStyle}
                                                                dropDownStyle={styles.dropDownPickerDDStyle}
                                                                placeholder={'Select'}
                                                                placeholderStyle={styles.textInputPlaceholder}
                                                                defaultValue={expirationDays}
                                                                onChangeItem={(item) => {
                                                                    setExpirationDays(item.value);
                                                                }}
                                                            />
                                                        </View>

                                                    </View>

                                                </View>

                                                {/* FlexRowContainer - Minimum Spent to Claim / Points Required */}
                                                <View style={{flexDirection: 'row', marginTop: '2%', width: '100%'}}>
                                                    
                                                    {/* Minimum Spent to Claim */}
                                                    <View style={{flex: 1}}>
                                                        <Text style={styles.textInputTitle}>Min. Spent to Claim</Text>

                                                        <TextInput
                                                            style={styles.textInputField}
                                                            placeholder="0"
                                                            placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                                                            placeholderStyle={styles.textInputPlaceholder}
                                                            value={toClaimMinSpent.toString()}
                                                            keyboardType='decimal-pad'
                                                            clearTextOnFocus

                                                            onFocus={() => {
                                                                setTemp(toClaimMinSpent)
                                                                setToClaimMinSpent('');
                                                            }}
                                                            onEndEditing={() => {
                                                                if (toClaimMinSpent == '') {
                                                                setToClaimMinSpent(temp);
                                                                }
                                                            }}
                                                            onChangeText={(text) => {
                                                                setToClaimMinSpent(text);
                                                            }}
                                                        />

                                                    </View>

                                                    {/* Points Required */}
                                                    <View style={{flex: 1}}>
                                                        <Text style={styles.textInputTitle}>Points Required</Text>

                                                        <TextInput
                                                            style={styles.textInputField}
                                                            placeholder="1"
                                                            placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                                                            placeholderStyle={styles.textInputPlaceholder}
                                                            value={voucherPointsRequired.toString()}
                                                            clearTextOnFocus
                                                            keyboardType='numeric'

                                                            onFocus={() => {
                                                                setTemp(voucherPointsRequired)
                                                                setVoucherPointsRequired('');
                                                            }}
                                                            onEndEditing={() => {
                                                                if (voucherPointsRequired == '') {
                                                                    setVoucherPointsRequired(temp);
                                                                }
                                                            }}
                                                            onChangeText={(text) => {
                                                                setVoucherPointsRequired(text);
                                                        }}/>

                                                    </View>

                                                </View>

                                                {/* Blasting Notifications */}
                                                <View style={{flexDirection: 'column', marginTop: '2%', width: '100%'}}>

                                                    <Text style={styles.textInputTitle}>Blasting Notification</Text>

                                                    <View style={{
                                                        width: '80%',
                                                        marginTop: '1%',
                                                        borderWidth: 1,
                                                        borderColor: '#E5E5E5',
                                                        paddingHorizontal: 20,
                                                        paddingVertical: 10,
                                                    }}>
                                                        {/* FlexRowContainer - Notification Title / Notification Description */}
                                                        <View style={{flexDirection: 'row', marginTop: '2%', width: '100%'}}>

                                                            {/* Notification Title */}
                                                            <View style={{flex: 1}}>
                                                                <Text style={styles.textInputTitle}>Notification Title</Text>

                                                                <TextInput
                                                                    style={styles.textInputField}
                                                                    placeholder="Notification Title"
                                                                    placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                                                                    placeholderStyle={styles.textInputPlaceholder}
                                                                    value={notificationTitle}
                                                                    clearTextOnFocus

                                                                    onFocus={() => {
                                                                        setTemp(notificationTitle);
                                                                        setNotificationTitle('');
                                                                    }}
                                                                    onEndEditing={() => {
                                                                        if (notificationTitle == '') {
                                                                            setNotificationTitle(temp);
                                                                        }
                                                                    }}
                                                                    onChangeText={(text) => {
                                                                        setNotificationTitle(text);
                                                                    }}
                                                                />
                                                            </View>

                                                            {/* Notification Description */}
                                                            <View style={{flex: 1}}>
                                                                <Text style={styles.textInputTitle}>Notification Description</Text>

                                                                <TextInput 
                                                                    style={styles.textInputField}
                                                                    placeholder="Description..."
                                                                    placeholderStyle={styles.textInputPlaceholder}
                                                                    placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                                                                    value={notificationDescription}
                                                                    multiline
                                                                    clearTextOnFocus
                                                                    
                                                                    onFocus={() => {
                                                                        setTemp(notificationDescription);
                                                                        setNotificationDescription('');
                                                                    }}

                                                                    onEndEditing={() => {
                                                                        if (notificationDescription == '') {
                                                                            setNotificationDescription(temp);
                                                                        }
                                                                    }}
                                                                    onChangeText={(text) => {
                                                                        setNotificationDescription(text);
                                                                    }}
                                                                />
                                                            </View>

                                                        </View>
                                                        
                                                        {/* FlexRowContainer - Notification Date / Time */}
                                                        <View style={{flexDirection: 'row', marginTop: '2%', alignItems: 'center'}}>
                                                            <Text style={styles.textInputTitle}>Notification Date & Time </Text>

                                                            {/* Notification Date Picker */}                                                
                                                            <TouchableOpacity 
                                                                onPress={() => {setShowNotificationDatePicker(true)}}
                                                                style={[{
                                                                    backgroundColor: Colors.fieldtBgColor,
                                                                    width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 90 : 115,
                                                                    height: 40,
                                                                    borderRadius: 5,
                                                                    padding: 1,
                                                                    marginTop: 5,
                                                                    alignItems: 'center',
                                                                    justifyContent: 'center',
                                                                    borderWidth: 1,
                                                                    borderColor: '#E5E5E5',
                                                                    fontFamily: 'NunitoSans-Regular',
                                                                    fontSize: 14
                                                                }, switchMerchant ? {
                                                                    fontSize: 10,
                                                                    width: 93,
                                                                    height: 35,
                                                                } : {}]}>
                                                                <Text style={[{ fontVariant: ['tabular-nums'], fontFamily: 'NunitoSans-Regular', fontSize: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 12 : 14 }, switchMerchant ? {
                                                                    fontSize: 10,
                                                                } : {}]}>{moment(notificationDate).format('DD MMM YYYY')}</Text>
                                                            </TouchableOpacity>

                                                            {/* Notification Time Picker */}
                                                            <TouchableOpacity 
                                                                onPress={() => {setShowNotificationTimePicker(true);}}
                                                                style={[{
                                                                    backgroundColor: Colors.fieldtBgColor,
                                                                    width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 90 : 115,
                                                                    height: 40,
                                                                    borderRadius: 5,
                                                                    padding: 1,
                                                                    marginTop: 5,
                                                                    alignItems: 'center',
                                                                    justifyContent: 'center',
                                                                    borderWidth: 1,
                                                                    borderColor: '#E5E5E5',
                                                                    fontFamily: 'NunitoSans-Regular', fontSize: 14
                                                                }, switchMerchant ? {
                                                                    height: 35
                                                                } : {}]}>
                                                                <Text style={[{ fontVariant: ['tabular-nums'], fontFamily: 'NunitoSans-Regular', fontSize: 14 }, switchMerchant ? {
                                                                    fontSize: 10,
                                                                } : {}]}>{moment(notificationTime).format('hh:mm A')}</Text>
                                                            </TouchableOpacity>
                                                        </View>

                                                        {/*  FlexRowContainer - Auto Push / Push Option */}
                                                        <View style={{flexDirection: 'row', marginTop: '2%'}}>
                                                            <View style={{flexDirection: 'row', alignItems: 'center'}}>
                                                                <Text style={styles.textInputTitle}>Auto Push? </Text>

                                                                <TouchableOpacity onPress={() => {setIsAutoPush(!isAutoPush)}}>
                                                                    <View style={[styles.toggleButtonStyle, {backgroundColor: isAutoPush ? Colors.primaryColor : '#ACACAC'}]}>
                                                                        <Icon name="checkmark-sharp" size={20} color={'#FFFFFF'} style={{ margin: 2 }}/>
                                                                    </View>
                                                                </TouchableOpacity>
                                                            </View>

                                                            <View style={{flexDirection: 'row', alignItems: 'center', marginLeft: 20}}>

                                                                <Text style={styles.textInputTitle}>Push to: </Text>

                                                                {/* Push to SMS */}
                                                                <View style={{flexDirection: 'row', alignItems: 'center'}}>
                                                                    <TouchableOpacity onPress={() => {setIsPushToSMS(!isPushToSMS)}}>
                                                                        <View style={[styles.toggleButtonStyle, {backgroundColor: isPushToSMS ? Colors.primaryColor : '#ACACAC'}]}>
                                                                            <Icon name="checkmark-sharp" size={20} color={'#FFFFFF'} style={{ margin: 2 }}/>
                                                                        </View>
                                                                    </TouchableOpacity>

                                                                    <Text style={[styles.textInputTitle, {fontFamily: 'NunitoSans-Regular', marginRight: 15}]}>SMS</Text>
                                                                </View>

                                                                {/* Push to App */}
                                                                <View style={{flexDirection: 'row', alignItems: 'center'}}>
                                                                    <TouchableOpacity onPress={() => {setIsPushToApp(!isPushToApp)}}>
                                                                    <View style={[styles.toggleButtonStyle, {backgroundColor: isPushToApp ? Colors.primaryColor : '#ACACAC'}]}>
                                                                            <Icon name="checkmark-sharp" size={20} color={'#FFFFFF'} style={{ margin: 2 }}/>
                                                                        </View>
                                                                    </TouchableOpacity>

                                                                    <Text style={[styles.textInputTitle, {fontFamily: 'NunitoSans-Regular', marginRight: 15}]}>App</Text>
                                                                </View>

                                                                {/* Push to Email */}
                                                                <View style={{flexDirection: 'row', alignItems: 'center'}}>
                                                                    <TouchableOpacity onPress={() => {setIsPushToEmail(!isPushToEmail)}}>
                                                                        <View style={[styles.toggleButtonStyle, {backgroundColor: isPushToEmail ? Colors.primaryColor : '#ACACAC'}]}>
                                                                            <Icon name="checkmark-sharp" size={20} color={'#FFFFFF'} style={{ margin: 2 }}/>
                                                                        </View>
                                                                    </TouchableOpacity>

                                                                    <Text style={[styles.textInputTitle, {fontFamily: 'NunitoSans-Regular', marginRight: 15}]}>Email</Text>
                                                                </View>

                                                            </View>
                                                        </View>

                                                        <View style={[styles.divider, {marginVertical: 20}]}/>

                                                        {/* FlexRowContainer - Notification Text */}
                                                        <View style={{flexDirection: 'column', marginTop: '2%'}}>
                                                            <Text style={[
                                                                styles.textInputTitle,
                                                                { alignSelf: 'flex-start' }
                                                            ]}>
                                                                Notification / SMS Message
                                                            </Text>

                                                            <TextInput 
                                                                style={[{
                                                                    width: '100%',
                                                                    height: 140,
                                                                    padding: 10,
                                                                    marginHorizontal: 5,
                                                                    marginVertical: 5,
                                                                    backgroundColor: Colors.fieldtBgColor,
                                                                    borderRadius: 5,
                                                                    borderWidth: 1,
                                                                    borderColor: '#E5E5E5',
                                                                    fontFamily: 'NunitoSans-Regular',
                                                                    fontSize: switchMerchant ? 10 : 14,
                                                                }]}
                                                                textAlignVertical={'top'}
                                                                placeholder="Hi %userName%, we have sent you a voucher of a free cup of coffee, visit %outletName% to redeem now! Expires on %expiryDate%"
                                                                placeholderStyle={styles.textInputPlaceholder}
                                                                placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}

                                                                onChangeText={(text) => {
                                                                    setNotificationText(text);
                                                                }}
                                                                value={notificationText}
                                                                maxLength={150}
                                                                multiline
                                                            />

                                                            {/* Word Count */}
                                                            <View style={{flexDirection: 'row', justifyContent: 'flex-end'}}>

                                                                <Text style={{
                                                                    color: Colors.fieldtTxtColor,
                                                                    fontSize: 13,
                                                                }}>
                                                                    Characters: 
                                                                </Text>

                                                                <Text style={{
                                                                    color: getNotificationTextCountColor(notificationText.length),
                                                                    fontSize: 13,
                                                                }}>
                                                                    {`${notificationText.length}`}
                                                                </Text>

                                                                <Text style={{
                                                                    color: Colors.fieldtTxtColor,
                                                                    fontSize: 13,
                                                                }}>
                                                                    /150
                                                                </Text>
                                                            </View>
                                                        </View>
                                                    </View>
                                                </View>

                                            </View>
                                        </View>
                                    </>
                                )
                            }

                            <View style={{ height: 120 }}/>

                        </KeyboardAwareScrollView>

                    </KeyboardAvoidingView>
                </ScrollView>
            </View>
        </UserIdleWrapper>
    );
};

export default AiLoyaltyVoucherScreen;