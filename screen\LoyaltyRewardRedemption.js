import { Text } from "react-native-fast-text";
import React, { Component, useReducer, useState, useEffect, useCallback } from 'react';
import {
    StyleSheet,
    Image,
    View,
    Alert,
    TouchableOpacity,
    Modal as ModalComponent,
    Dimensions,
    TextInput,
    KeyboardAvoidingView,
    ActivityIndicator,
    Touchable,
    Platform,
    useWindowDimensions,
    TurboModuleRegistry,
} from 'react-native';
import { ModalView as ModalViewComponent } from 'react-native-multiple-modals';
import { ScrollView, FlatList } from "react-native-gesture-handler";
import Colors from '../constant/Colors';
import Styles from '../constant/Styles';
import SideBar from './SideBar';
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as User from '../util/User';
import Icon from 'react-native-vector-icons/Ionicons';
import ApiClient from '../util/ApiClient';
import API from '../constant/API';
import DropDownPicker from 'react-native-dropdown-picker';
import Feather from 'react-native-vector-icons/Feather';
import EvilIcons from 'react-native-vector-icons/EvilIcons';
import moment from 'moment';
import DateTimePickerModal from 'react-native-modal-datetime-picker';
import {
    getTransformForModalInsideNavigation,
    getTransformForScreenInsideNavigation,
    isTablet
} from '../util/common';
import {
    MERCHANT_VOUCHER_CODE_FORMAT,
    MERCHANT_VOUCHER_TYPE,
    SEGMENT_TYPE,
    EXPAND_TAB_TYPE,
    OUTLET_SHIFT_STATUS,
} from '../constant/common';
import { CommonStore } from '../store/commonStore';
import { UserStore } from '../store/userStore';
import { MerchantStore } from '../store/merchantStore';
import { OutletStore } from '../store/outletStore';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import RNPickerSelect from 'react-native-picker-select';
import { useKeyboard } from '../hooks';
import { get } from 'react-native/Libraries/Utilities/PixelRatio';
import AsyncImage from '../components/asyncImage';
import Entypo from 'react-native-vector-icons/Entypo';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
// import { DIMENTIONS } from 'react-native-numeric-input';
import AntDesign from 'react-native-vector-icons/AntDesign';
import { LOYALTY_CAMPAIGN_CREDIT_TRANSACTION_TYPE_PARSED } from '../constant/loyalty';
import GCalendar from '../assets/svg/GCalendar';
import APILocal from '../util/apiLocalReplacers';
// import { Row } from 'react-native-table-component';
import { useFocusEffect } from '@react-navigation/native';
import UserIdleWrapper from '../components/userIdleWrapper';
import Redemption from '../assets/svg/RedemptionW';
import FontAwesome5 from 'react-native-vector-icons/FontAwesome5';
import CheckBox from '@react-native-community/checkbox';
import { PROMOTION_TYPE_VARIATION } from '../constant/promotions';
import firestore from '@react-native-firebase/firestore';
import { Collections } from '../constant/firebase';

//////////////////////////////////////////////////////////////////////////////////////////////////////////
const ModalView = Platform.OS === 'android' ? ModalComponent : ModalViewComponent;

const SELECT_SECTION = {
    HOME: 'HOME',
    CREDIT: 'CREDIT',
    VOUCHER: 'VOUCHER',
    POINT: 'POINT',
    STAMP: 'STAMP',
    DOCKET: 'DOCKET'
};
const dockettestdata = [
    {
        docketname: 'First Test Docket name',
        available: '3',
    },
    {
        docketname: 'Second Test Docket name',
        available: '1',
    },
]
const vouchertestdata = [
    {
        vouchername: 'First Test voucher name',
        expiredays: '3',
    },
    {
        vouchername: 'Second Test voucher name',
        expiredays: '7',
    },

]
const pointtestdata = [
    {
        pointname: 'First Test point name',
        point: '300',
    },
    {
        pointname: 'Second Test point name',
        point: '1000',
    },

]
const stamptestdata = [
    {
        stampname: 'First Test stamp name',
        stampamount: '0',
    },
    {
        stampname: 'Second Test stamp name',
        stampamount: '2',
    },
    {
        stampname: 'Thirt Test stamp name',
        stampamount: '5',
    },

]

//////////////////////////////////////////////////////////////////////////////////////////////////////////

const LoyaltyRewardRedemption = (props) => {
    const { navigation } = props;

    ///////////////////////////////////////////////////////////

    const [isMounted, setIsMounted] = useState(true);

    useFocusEffect(
        useCallback(() => {
            setIsMounted(true);
            return () => {
                setIsMounted(false);
            };
        }, [])
    );

    ///////////////////////////////////////////////////////////

    const { width: windowWidth, height: windowHeight } = useWindowDimensions();

    //////////////////////////////////////////////////////////////////////////////////////////////////////////

    const merchantUserId = UserStore.useState((s) => s.firebaseUid);
    const merchantUserName = UserStore.useState((s) => s.name);
    const merchantId = UserStore.useState((s) => s.merchantId);
    const merchantName = MerchantStore.useState((s) => s.name);
    const merchantLogo = MerchantStore.useState((s) => s.logo);

    const allOutlets = MerchantStore.useState((s) => s.allOutlets);

    const isLoading = CommonStore.useState((s) => s.isLoading);
    const [switchMerchant, setSwitchMerchant] = useState(isTablet() ? false : true);

    const allOutletsItemsSkuDict = OutletStore.useState(
        (s) => s.allOutletsItemsSkuDict,
    );

    const allOutletsItems = OutletStore.useState(
        (s) => s.allOutletsItems,
    );

    const outletSelectDropdownView = CommonStore.useState(
        (s) => s.outletSelectDropdownView,
    );

    const [startDate, setStartDate] = useState(moment());
    const [showStartDatePicker, setShowStartDatePicker] = useState(false);

    const currOutlet = MerchantStore.useState((s) => s.currOutlet);
    const crmUsers = OutletStore.useState((s) => s.crmUsers);

    const selectedCustomerLCCTransactions = OutletStore.useState(
        (s) => s.selectedCustomerLCCTransactions,
    );
    const selectedCustomerLCCBalance = OutletStore.useState(
        (s) => s.selectedCustomerLCCBalance,
    );
    const selectedCustomerPointsBalance = OutletStore.useState(
        (s) => s.selectedCustomerPointsBalance,
    );

    const selectedCustomerEdit = CommonStore.useState(
        (s) => s.selectedCustomerEdit,
    );

    const loyaltyCampaigns = OutletStore.useState((s) => s.loyaltyCampaigns);

    const selectedCustomerUserLoyaltyCampaigns = OutletStore.useState(
        (s) => s.selectedCustomerUserLoyaltyCampaigns,
    );

    const taggableVouchers = OutletStore.useState((s) => s.taggableVouchers);

    const selectedCustomerUserTaggableVouchers = OutletStore.useState(
        (s) => s.selectedCustomerUserTaggableVouchersView,
    );

    const [selectSection, setSelectSection] = useState(SELECT_SECTION.HOME);
    const [selectedSection, setSelectedSection] = useState(SELECT_SECTION.CREDIT);
    const [phoneNumber, setPhoneNumber] = useState('+60');
    const [userName, setUserName] = useState('');
    const [creditModal, setCreditModal] = useState(false);
    const [cashbackdoneModal, setCashbackdoneModal] = useState(false);
    const [voucherModal, setVoucherModal] = useState(false);
    const [pointModal, setPointModal] = useState(false);
    const [stampModal, setStampModal] = useState(false);
    const [docketModal, setDocketModal] = useState(false);
    const [docketRedeemModal, setDocketRedeemModal] = useState(false);
    const [newuserModal, setNewUserModal] = useState(false);
    const [amount, setAmount] = useState('');
    const [addnotes, setAddnotes] = useState(false);
    const [notetext, setNotetext] = useState('');
    const [redeemdone, setRedeemdone] = useState(false);
    const [addCreditdone, setAddCreditdone] = useState(false);
    const [deductCreditdone, setDeductCreditdone] = useState(false);
    const [rev_date, setRev_date] = useState(moment().startOf('day'));
    const [rev_date1, setRev_date1] = useState(moment().endOf('day'));
    const [showDateTimePicker, setShowDateTimePicker] = useState(false);
    const [email, setEmail] = useState('');
    const [tickCheck, setTickcheck] = useState(false)

    const [phoneNumberChecking, setPhoneNumberChecking] = useState(false);
    const [currCRMUser, setCurrCRMUser] = useState(null);

    const isAlphaUser = UserStore.useState((s) => s.isAlphaUser);
    const expandTab = CommonStore.useState((s) => s.expandTab);
    const currPageStack = CommonStore.useState((s) => s.currPageStack);

    ///////////////////////////////////////////////////////////////////////

    // 2022-08-12 - Voucher redemption

    const [selectedVoucherDict, setSelectedVoucherDict] = useState({});

    ///////////////////////////////////////////////////////////////////////

    // 2022-08-15 - Loyalty stamps redemption

    const [selectedStampDict, setSelectedStampDict] = useState({});

    // const [selectedCustomerUserLoyaltyStamps, setSelectedCustomerUserLoyaltyStamps] = useState({});

    const loyaltyStamps = OutletStore.useState(
        (s) => s.loyaltyStamps,
    );

    const selectedCustomerUserLoyaltyStamps = OutletStore.useState(
        (s) => s.selectedCustomerUserLoyaltyStamps,
    );

    // useEffect(() => {

    // }, [loyaltyStamps, selectedCustomerUserLoyaltyStampsRaw]);

    ///////////////////////////////////////////////////////////////////////

    // 2022-08-15 - Beer docket redemption

    const [selectedDocketDict, setSelectedDocketDict] = useState({});

    const [selectedDocketInfo, setSelectedDocketInfo] = useState({});

    const beerDockets = OutletStore.useState(
        (s) => s.beerDockets,
    );

    const selectedCustomerUserBeerDockets = OutletStore.useState(
        (s) => s.selectedCustomerUserBeerDockets
            .filter(docket => {
                if (moment().isBefore(docket.expiryDate) &&
                    docket.bdItems
                    && docket.bdItems.reduce((accum, bdItem) => accum + bdItem.quantity, 0) > 0) {
                    return true;
                }
                else {
                    return false;
                }
            }),
    );

    ///////////////////////////////////////////////////////////////////////

    // 2022-08-16 - Outlet item redemption (use credit)

    const [selectedOutletItemDict, setSelectedOutletItemDict] = useState({});

    const [selectedOutletItemPoints, setSelectedOutletItemPoints] = useState(0);

    const outletItems = OutletStore.useState((s) => s.outletItems.filter(outletItem => {
        if (outletItem.creditToBuy !== undefined &&
            outletItem.creditToBuy > 0) {
            return true;
        }
        else {
            return false;
        }
    }));

    ///////////////////////////////////////////////////////////////////////

    const currOutletShiftStatus = OutletStore.useState((s) => s.currOutletShiftStatus);

    useEffect(() => {
        setPhoneNumberChecking(true);

        if (phoneNumber.length === 12 || phoneNumber.length === 13) {
            checkPhoneNumberData();
        }
    }, [phoneNumber, crmUsers]);

    const checkPhoneNumberData = async () => {
        const phoneNumberParsed = phoneNumber.slice(2);
        const phoneNumberParsedCountry = phoneNumber.slice(1);

        let crmUser = crmUsers.find(
            (crmUser) =>
                crmUser.number === phoneNumberParsedCountry ||
                crmUser.number === phoneNumberParsed,
        );

        /////////////////

        if (!crmUser) {
            console.log('find user');

            const userSnapshot = await firestore()
                .collection(Collections.CRMUser)
                .where('outletId', '==', currOutletId)
                .where('number', '==', phoneNumberParsedCountry)
                .where('deletedAt', '==', null)
                .limit(1)
                .get();

            console.log('find user done');

            if (userSnapshot.size > 0) {

                crmUser = userSnapshot.docs[0].data();
                console.log('search users result:', crmUser);
            }
        }

        if (crmUser) {
            // means got the same user found

            CommonStore.update((s) => {
                s.selectedCustomerEdit = crmUser;
            });

            setPhoneNumberChecking(false);

            setCurrCRMUser(crmUser);
        } else {
            // no existing user found

            setPhoneNumberChecking(false);

            setCurrCRMUser(null);
        }
    };

    const claimCashback = () => {
        var body = {
            amount: parseFloat(amount),

            userName,
            userPhone: phoneNumber.slice(1),
            // emailAddress: email,
            // dob: startDate,

            merchantId,
            merchantName,
            merchantLogo,

            outletId: currOutlet.uniqueId,
            outletName: currOutlet.name,
            outletCover: currOutlet.cover,
        };

        // console.log(body);

        setPhoneNumberChecking(true);

        // ApiClient.POST(API.loyaltyCampaignAddCashback, body, false)
        APILocal.loyaltyCampaignAddCashback({ body, uid: merchantUserId })
            .then(
                (result) => {
                    if (result && result.status === 'success') {
                        Alert.alert(
                            'Success',
                            'Cashback has been claimed',
                            [
                                {
                                    text: 'OK',
                                    onPress: () => {
                                        setCashbackModal(false);
                                        setPhoneNumberChecking(false);

                                        setTimeout(() => {
                                            setCashbackdoneModal(true);
                                        }, 500);
                                    },
                                },
                            ],
                            { cancelable: false },
                        );
                    } else {
                        setCashbackModal(false);
                        setPhoneNumberChecking(false);
                    }
                },
            );
    };
    const reedemcredit = () => {
        var body = {
            amount: parseFloat(amount),

            userName,
            userPhone: phoneNumber.slice(1),
            // emailAddress: email,
            // dob: startDate,

            merchantId,
            merchantName,
            merchantLogo,

            outletId: currOutlet.uniqueId,
            outletName: currOutlet.name,
            outletCover: currOutlet.cover,
        };

        // console.log(body);

        setPhoneNumberChecking(true);

        // ApiClient.POST(API.loyaltyCampaignAddCashback, body, false)
        APILocal.loyaltyCampaignAddCashback({ body, uid: merchantUserId })
            .then(
                (result) => {
                    if (result && result.status === 'success') {
                        Alert.alert(
                            'Success',
                            'Credit has been redeemed',
                            [
                                {
                                    text: 'OK',
                                    onPress: () => {
                                        setRedeemCreditModal(false);
                                        setPhoneNumberChecking(false);
                                        setTimeout(() => {
                                            setRedeemdone(true);
                                        }, 500);
                                    },
                                },
                            ],
                            { cancelable: false },
                        );
                    } else {
                        setRedeemCreditModal(false);
                        setPhoneNumberChecking(false);
                    }
                },
            );
    };

    const addCredit = () => {
        var body = {
            amount: parseFloat(amount),

            userName,
            userPhone: phoneNumber.slice(1),

            merchantId,
            merchantName,
            merchantLogo,

            outletId: currOutlet.uniqueId,
            outletName: currOutlet.name,
            outletCover: currOutlet.cover,
        };

        // console.log(body);

        setPhoneNumberChecking(true);

        // ApiClient.POST(API.loyaltyCampaignAddCredit, body, false)
        APILocal.loyaltyCampaignAddCredit({ body, uid: merchantUserId })
            .then((result) => {
                if (result && result.status === 'success') {
                    Alert.alert(
                        'Success',
                        'Credit has been added',
                        [
                            {
                                text: 'OK',
                                onPress: () => {
                                    setAddCreditModal(false);
                                    setPhoneNumberChecking(false);
                                    setTimeout(() => {
                                        setAddCreditdone(true);
                                    }, 500);
                                },
                            },
                        ],
                        { cancelable: false },
                    );
                } else {
                    setAddCreditModal(false);
                    setPhoneNumberChecking(false);
                }
            });
    };

    const deductCredit = () => {
        var body = {
            amount: -parseFloat(amount),

            userName,
            userPhone: phoneNumber.slice(1),

            merchantId,
            merchantName,
            merchantLogo,

            outletId: currOutlet.uniqueId,
            outletName: currOutlet.name,
            outletCover: currOutlet.cover,
        };

        // console.log(body);

        setPhoneNumberChecking(true);

        // ApiClient.POST(API.loyaltyCampaignAddCredit, body, false)
        APILocal.loyaltyCampaignAddCredit({ body, uid: merchantUserId })
            .then((result) => {
                if (result && result.status === 'success') {
                    setCreditModal(false);
                    setPhoneNumberChecking(false);
                    setAmount('')

                    Alert.alert('Info', 'You have successfully redeemed your rewards.\n\nSpend more and get rewarded with more perks!')

                    // Alert.alert(
                    //     'Success',
                    //     'Credit has been deducted',
                    //     [
                    //         {
                    //             text: 'OK',
                    //             onPress: () => {
                    //                 // setDeductCreditModal(false);
                    //                 // setCreditModal(false);
                    //                 // setPhoneNumberChecking(false);
                    //                 // setTimeout(() => {
                    //                 //     setDeductCreditdone(true);
                    //                 // }, 1000);
                    //             },
                    //         },
                    //     ],
                    //     { cancelable: false },
                    // );
                } else {
                    // setDeductCreditModal(false);
                    setCreditModal(false);
                    setPhoneNumberChecking(false);
                }
            }).catch((ex) => {
                setPhoneNumberChecking(false);
            });
    };

    const PhoneonNumPadBtn = (key) => {
        var plus = phoneNumber.split('+')[1];
        if (key >= 0 || key == '+') {
            var phoneLength = 12;
            if (phoneNumber.startsWith('+6011')) {
                phoneLength = 13;
            }

            if (phoneNumber.includes('+'))
                if (phoneNumber.length < phoneLength && plus.length < phoneLength)
                    setPhoneNumber(phoneNumber + key);
            if (!phoneNumber.includes('+')) {
                if (phoneNumber.length < phoneLength) setPhoneNumber(phoneNumber + key);
            }
        } else if (phoneNumber.length > 0) setPhoneNumber(phoneNumber.slice(0, key));
    };

    const onNumPadBtn = (key) => {
        var decimal = amount.split('.')[1];
        if (key >= 0 || key == '.') {
            if (amount.includes('.'))
                if (amount.length < 12 && decimal.length < 2) setAmount(amount + key);
            if (!amount.includes('.')) {
                if (amount.length < 12) setAmount(amount + key);
            }
        } else if (amount.length > 0) setAmount(amount.slice(0, key));
    };

    const renderViewHistory = ({ item }) => {
        return (
            <View
                style={{
                    flexDirection: 'row',
                    paddingVertical: 10,
                    alignItems: 'center',
                    paddingHorizontal: 10,
                }}>
                <Text style={{ fontSize: switchMerchant ? 10 : 16, width: '20%' }}>
                    {selectedCustomerEdit ? selectedCustomerEdit.name : ''}
                </Text>
                <Text style={{ fontSize: switchMerchant ? 10 : 16, width: '25%' }}>
                    {selectedCustomerEdit ? selectedCustomerEdit.number : ''}
                </Text>
                <View style={{ flexDirection: 'column', width: '20%' }}>
                    <Text style={{ fontSize: switchMerchant ? 10 : 16 }}>
                        {
                            LOYALTY_CAMPAIGN_CREDIT_TRANSACTION_TYPE_PARSED[
                            item.transactionType
                            ]
                        }
                    </Text>
                    <Text style={{ fontSize: switchMerchant ? 10 : 16 }}>
                        RM {item.amount.toFixed(2)}
                    </Text>
                </View>
                <View style={{ flexDirection: 'column', width: '20%' }}>
                    <Text style={{ fontSize: switchMerchant ? 10 : 16 }}>
                        {moment(item.createdAt).format('DD MMM YYYY')}
                    </Text>
                    <Text style={{ fontSize: switchMerchant ? 10 : 16 }}>
                        {moment(item.createdAt).format('hh:mm A')}
                    </Text>
                </View>
                <View stlye={{ width: '15%' }}>
                    <Text
                        style={{
                            fontSize: switchMerchant ? 10 : 16,
                            fontFamily: 'NunitoSans-SemiBold',
                            backgroundColor: Colors.primaryColor,
                            color: Colors.whiteColor,
                            padding: 8,
                            borderRadius: 5,
                        }}>
                        Success
                    </Text>
                </View>
            </View>
        );
    };

    const renderLoyaltyCampaign = ({ item }) => {
        return (
            <TouchableOpacity
                onPress={() => {
                    Alert.alert(
                        `${item.campaignName}`,
                        `Are you sure you want to redeem this voucher?`,
                        [
                            {
                                text: 'YES',
                                onPress: () => {
                                    redeemUserTaggableVoucherByMerchant(item);
                                },
                            },
                            { text: 'NO', onPress: () => { }, style: 'cancel' },
                        ],
                        { cancelable: false },
                    );
                }}
                style={{
                    paddingHorizontal: windowWidth * 0.01,
                    paddingVertical: windowHeight * 0.02,
                    paddingTop: windowHeight * 0.01,
                    borderBottomColor: '#EBEDEF',
                    borderBottomWidth: 1,
                    width: '100%',
                }}>
                <View
                    style={{
                        // borderWidth: 1,
                        flexDirection: 'row',
                        alignItems: 'center',
                        justifyContent: 'space-between',
                    }}>
                    <View>
                        <Text
                            style={{
                                fontFamily: 'NunitoSans-Bold',
                                fontSize: switchMerchant ? 10 : 14,
                                // color: switch1stVisit ? Colors.primaryColor : Colors.tabRed,
                                color: Colors.primaryColor,
                            }}>
                            <Text>{item.isActive ? 'Active' : 'Inactive'}</Text>
                        </Text>
                        <Text
                            style={{
                                fontFamily: 'NunitoSans-Bold',
                                fontSize: switchMerchant ? 10 : 14,
                            }}>
                            {item.campaignName}
                        </Text>
                        <Text
                            style={{
                                fontFamily: 'NunitoSans-Bold',
                                fontSize: switchMerchant ? 10 : 14,
                                color: '#808B96',
                            }}>
                            {item.campaignDescription}
                        </Text>
                    </View>
                </View>
            </TouchableOpacity>
        );
    };


    const redeemUserTaggableVoucherMultipleByMerchant = () => {
        var voucherList = Object.entries(selectedVoucherDict).map(([key, value]) => {
            if (value) {
                const userTaggableVoucher = selectedCustomerUserTaggableVouchers.find(userVoucher => {
                    return (userVoucher
                        .voucherId ===
                        key &&
                        userVoucher
                            .redeemDate === null) &&
                        moment().isBefore(userVoucher.expirationDate);
                });

                return {
                    userTaggableVoucherId: userTaggableVoucher ? userTaggableVoucher.uniqueId : '',
                    taggableVoucherId: userTaggableVoucher ? userTaggableVoucher.voucherId : '',
                };
            }
            else {
                return {
                    userTaggableVoucherId: '',
                    taggableVoucherId: '',
                };
            }
        }).filter(voucher => voucher.userTaggableVoucherId);

        if (voucherList.length > 0) {
            setPhoneNumberChecking(true);

            var body = {
                // taggableVoucherId: taggableVoucher.uniqueId,

                // userTaggableVoucherId: userTaggableVoucher.uniqueId,

                voucherList,
            };

            // ApiClient.POST(API.redeemUserTaggableVoucherByMerchant, body, false)
            APILocal.redeemUserTaggableVoucherMultipleByMerchant({ body, uid: merchantUserId })
                .then(
                    (result) => {
                        if (result && result.status === 'success') {
                            setSelectedVoucherDict({});
                            setVoucherModal(false);

                            Alert.alert(
                                'Info',
                                'You have successfully redeemed your rewards.\n\nSpend more and get rewarded with more perks!',
                                [
                                    {
                                        text: 'OK',
                                        onPress: () => {
                                        },
                                    },
                                ],
                                { cancelable: false },
                            );
                        } else {
                            Alert.alert('Info', 'Unable to redeem the voucher(s)');
                        }

                        setPhoneNumberChecking(false);
                    },
                ).catch((ex) => {
                    setPhoneNumberChecking(false);
                });
        }
        else {
            Alert.alert('Info', 'Empty selection or invalid voucher(s) to proceed.');
        }
    };

    const redeemUserLoyaltyStampMultipleByMerchant = () => {
        var stampList = Object.entries(selectedStampDict).map(([key, value]) => {
            if (value) {
                var userLoyaltyStamp = selectedCustomerUserLoyaltyStamps.find(stamp => stamp.loyaltyStampId === key);
                var loyaltyStamp = loyaltyStamps.find(stamp => stamp.uniqueId === key);
                var lsItem = null;

                if (userLoyaltyStamp && loyaltyStamp.lsItems) {
                    for (var i = 0; i < loyaltyStamp.lsItems.length; i++) {
                        if (!userLoyaltyStamp.getIdHistory.includes(loyaltyStamp.lsItems[i].lsItemId)) {
                            // means havent redeem this stamp yet

                            // var stampsRequired = loyaltyStamp.lsItems[i].noOfStamp - userLoyaltyStamp.stampCount;

                            // if (stampsRequired < 0) {
                            //     stampsRequired = 0;
                            // }

                            // statusText = `${stampsRequired} more to go!`;

                            lsItem = loyaltyStamp.lsItems[i];

                            break;
                        }
                        else {
                            // means already redeem this stamp, proceed to next to check

                            continue;
                        }
                    }
                }

                return {
                    userLoyaltyStampId: userLoyaltyStamp ? userLoyaltyStamp.uniqueId : '',
                    loyaltyStampId: loyaltyStamp ? loyaltyStamp.uniqueId : '',
                    lsItem: {
                        ...(lsItem.variation === PROMOTION_TYPE_VARIATION.SPECIFIC_PRODUCTS ? {
                            itemSku: lsItem.outletItemSku,
                        } : {
                            categoryId: lsItem.outletItemSku,
                        }),

                        loyaltyStampId: loyaltyStamp.uniqueId,
                        lsItemId: lsItem.lsItemId,
                        noOfStamp: lsItem.noOfStamp,
                        quantity: lsItem.quantity,
                    },
                };
            }
            else {
                return {
                    userLoyaltyStampId: '',
                    loyaltyStampId: '',
                    lsItem: {},
                };
            }
        })
            .filter(stamp => stamp.userLoyaltyStampId);

        if (stampList.length > 0) {
            setPhoneNumberChecking(true);

            var body = {
                // taggableVoucherId: taggableVoucher.uniqueId,

                // userTaggableVoucherId: userTaggableVoucher.uniqueId,

                stampList,
            };

            // ApiClient.POST(API.redeemUserTaggableVoucherByMerchant, body, false)
            APILocal.redeemUserLoyaltyStampMultipleByMerchant({ body, uid: merchantUserId })
                .then(
                    (result) => {
                        if (result && result.status === 'success') {
                            setSelectedStampDict({});
                            setStampModal(false);

                            Alert.alert(
                                'Info',
                                'You have successfully redeemed your rewards.\n\nSpend more and get rewarded with more perks!',
                                [
                                    {
                                        text: 'OK',
                                        onPress: () => {
                                        },
                                    },
                                ],
                                { cancelable: false },
                            );
                        } else {
                            Alert.alert('Info', 'Unable to redeem the stamp(s)');
                        }

                        setPhoneNumberChecking(false);
                    },
                ).catch((ex) => {
                    setPhoneNumberChecking(false);
                });
        }
        else {
            Alert.alert('Info', 'Empty selection or invalid stamp(s) to proceed.');
        }
    };

    const redeemUserBeerDocketMultipleByMerchant = (quantityToRedeemed) => {
        setPhoneNumberChecking(true);

        var bdItemsUpdated = selectedDocketInfo.userBeerDocket.bdItems;

        bdItemsUpdated = bdItemsUpdated.map(bdItem => {
            var quantity = bdItem.quantity - quantityToRedeemed;

            if (quantity < 0) {
                quantity = 0;
            }

            return {
                ...bdItem,
                quantity,
            };
        });

        var body = {
            userBeerDocketId: selectedDocketInfo.userBeerDocket.uniqueId,
            userId: selectedDocketInfo.userBeerDocket.userId,
            // bdOutlets: selectedOutletIdList,
            bdItems: bdItemsUpdated,
        };

        // ApiClient.POST(API.redeemUserTaggableVoucherByMerchant, body, false)
        APILocal.updateUserBeerDocketItemQuantityByMerchant({ body, uid: merchantUserId })
            .then(
                (result) => {
                    if (result && result.status === 'success') {
                        setSelectedDocketInfo({});
                        setDocketRedeemModal(false);

                        Alert.alert(
                            'Info',
                            'You have successfully redeemed your rewards.\n\nSpend more and get rewarded with more perks!',
                            [
                                {
                                    text: 'OK',
                                    onPress: () => {
                                    },
                                },
                            ],
                            { cancelable: false },
                        );
                    } else {
                        Alert.alert('Info', 'Unable to redeem the docket(s).');
                    }

                    setPhoneNumberChecking(false);
                },
            ).catch((ex) => {
                setPhoneNumberChecking(false);
            });
    };

    const redeemOutletItemMultipleByMerchant = () => {
        var outletItemList = Object.entries(selectedOutletItemDict).map(([key, value]) => {
            if (value) {
                var outletItem = outletItems.find(item => item.uniqueId === key);

                return {
                    outletItemId: key,
                    creditToBuy: outletItem.creditToBuy ? outletItem.creditToBuy : 0,
                };
            }
            else {
                return {
                    outletItemId: '',
                    creditToBuy: 0,
                };
            }
        })
            .filter(outletItem => outletItem.outletItemId);

        if (outletItemList.length > 0) {
            setPhoneNumberChecking(true);

            var body = {
                // taggableVoucherId: taggableVoucher.uniqueId,

                // userTaggableVoucherId: userTaggableVoucher.uniqueId,

                outletItemList,

                creditToBuyTotal: outletItemList.reduce((accum, outletItem) => accum + outletItem.creditToBuy, 0),

                userId: selectedCustomerEdit.firebaseUid ? selectedCustomerEdit.firebaseUid : '',
                crmUserId: selectedCustomerEdit.uniqueId ? selectedCustomerEdit.uniqueId : '',
                email: selectedCustomerEdit.email ? selectedCustomerEdit.email : '',
                phone: selectedCustomerEdit.number ? selectedCustomerEdit.number : '',
                orderId: '',

                merchantId,
                outletId: currOutlet.uniqueId,

                merchantName,
                outletName: currOutlet.name,
                merchantLogo,
                outletCover: currOutlet.cover,
            };

            // ApiClient.POST(API.redeemUserTaggableVoucherByMerchant, body, false)
            APILocal.redeemOutletItemMultipleByMerchant({ body, uid: merchantUserId })
                .then(
                    (result) => {
                        if (result && result.status === 'success') {
                            setSelectedOutletItemDict({});
                            setPointModal(false);

                            Alert.alert(
                                'Info',
                                'You have successfully redeemed your rewards.\n\nSpend more and get rewarded with more perks!',
                                [
                                    {
                                        text: 'OK',
                                        onPress: () => {
                                        },
                                    },
                                ],
                                { cancelable: false },
                            );
                        } else {
                            Alert.alert('Info', 'Unable to redeem the product(s)');
                        }

                        setPhoneNumberChecking(false);
                    },
                ).catch((ex) => {
                    setPhoneNumberChecking(false);
                });
        }
        else {
            Alert.alert('Info', 'Empty selection or invalid product(s) to proceed.');
        }
    };

    const renderDocketList = ({ item, index }) => {
        //////////////////////

        // show docket info

        var statusCompleted = true;
        var statusText = 'Available';

        var itemsInfo = '';

        var bdOutletItemsStrList = [];

        var maxQuantityAllowed = 0;

        var userBeerDocket = selectedCustomerUserBeerDockets.find(userBeerDocket => userBeerDocket.beerDocketId === item.uniqueId);

        if (userBeerDocket && userBeerDocket.bdItems) {
            for (var i = 0; i < userBeerDocket.bdItems.length; i++) {
                let foundItemList = allOutletsItems.filter(findItem => findItem.sku === userBeerDocket.bdItems[i].outletItemSku);

                if (foundItemList.length > 0) {
                    bdOutletItemsStrList.push(
                        `${foundItemList[0]
                            .name
                        } (x${userBeerDocket.bdItems[i].quantity})`,
                    );

                    if (userBeerDocket.bdItems[i].quantity > maxQuantityAllowed) {
                        maxQuantityAllowed = userBeerDocket.bdItems[i].quantity;
                    }
                    // bdOutletItemsImageList.push(
                    //   allOutletsItemsSkuDict[userBeerDocket.bdItems[i].outletItemSku][0]
                    //     .image,
                    // );
                }
            }
        }

        itemsInfo = bdOutletItemsStrList.join('\n');

        //////////////////////

        return (
            <TouchableOpacity
                onPress={() => {
                    if (userName !== '' && phoneNumber !== '') {
                        setDocketModal(false);
                        setTimeout(() => {
                            setDocketRedeemModal(true);
                        }, 500);

                        setSelectedDocketInfo({
                            beerDocket: item,
                            userBeerDocket,

                            maxQuantityAllowed,
                        });
                    } else {
                        Alert.alert(
                            'Info',
                            'Please fill in the user name and phone number first',
                        );
                    }
                }}
                disabled={phoneNumberChecking}
                style={{
                    paddingHorizontal: windowWidth * 0.02,
                    paddingVertical: windowHeight * 0.02,
                    paddingTop: windowHeight * 0.01,
                    borderWidth: 1,
                    borderRadius: 10,
                    marginBottom: 10,
                }}>
                <View
                    style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                    }}>
                    {/* <CheckBox
                        style={{
                            ...(Platform.OS === 'ios' && {
                                width: switchMerchant ? 10 : 16,
                                height: switchMerchant ? 10 : 16,
                            },
                                { marginRight: 10, }),
                        }}
                        //value={}
                        onValueChange={(value) => { }}
                    /> */}
                    <View>
                        <Text
                            style={{
                                fontFamily: 'NunitoSans-Bold',
                                fontSize: switchMerchant ? 10 : 14,
                                color: Colors.blackColor,
                            }}>
                            <Text>{itemsInfo}</Text>
                        </Text>
                        <Text
                            style={{
                                fontFamily: 'NunitoSans-Bold',
                                fontSize: switchMerchant ? 10 : 14,
                                color: Colors.primaryColor,
                            }}>
                            {statusText}
                        </Text>
                    </View>
                </View>
            </TouchableOpacity>
        );
    };
    const renderOutletItemList = ({ item, index }) => {
        return (
            <TouchableOpacity
                disabled={item.creditToBuy > selectedCustomerPointsBalance}
                onPress={() => {
                    if ((item.creditToBuy + selectedOutletItemPoints) <= selectedCustomerPointsBalance) {
                        // means valid to add to redeem cart

                        if (selectedOutletItemDict[item.uniqueId]) {
                            // if existed, remove it 

                            setSelectedOutletItemPoints(selectedOutletItemPoints - item.creditToBuy);

                            setSelectedOutletItemDict({
                                ...selectedOutletItemDict,
                                [item.uniqueId]: false,
                            });
                        }
                        else {
                            // it not existed, add it

                            setSelectedOutletItemPoints(selectedOutletItemPoints + item.creditToBuy);

                            setSelectedOutletItemDict({
                                ...selectedOutletItemDict,
                                [item.uniqueId]: true,
                            });
                        }
                    }
                    else {
                        // means not valid to add to redeem cart

                        setSelectedOutletItemDict({
                            ...selectedOutletItemDict,
                            [item.uniqueId]: false,
                        });
                    }
                }}
                style={{
                    paddingHorizontal: windowWidth * 0.03,
                    paddingVertical: windowHeight * 0.02,
                    paddingTop: windowHeight * 0.01,
                    borderWidth: 1,
                    //borderColor: item.point < ? Colors.blackColor : Colors.primaryColor, //point more than redeem point change to green
                    borderRadius: 10,
                    marginTop: 10,

                    ...(item.creditToBuy <= selectedCustomerPointsBalance && {
                        borderColor: Colors.primaryColor,
                    }),
                }}>
                <View
                    style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                    }}>
                    <CheckBox
                        disabled
                        style={{
                            ...(Platform.OS === 'ios' && {
                                width: switchMerchant ? 10 : 16,
                                height: switchMerchant ? 10 : 16,
                            },
                                { marginRight: 10, }),
                        }}
                        value={selectedOutletItemDict[item.uniqueId]}
                        disabled={item.creditToBuy > selectedCustomerPointsBalance}
                        onValueChange={(value) => {
                            if ((item.creditToBuy + selectedOutletItemPoints) <= selectedCustomerPointsBalance) {
                                // means valid to add to redeem cart

                                if (selectedOutletItemDict[item.uniqueId]) {
                                    // if existed, remove it 

                                    setSelectedOutletItemPoints(selectedOutletItemPoints - item.creditToBuy);

                                    setSelectedOutletItemDict({
                                        ...selectedOutletItemDict,
                                        [item.uniqueId]: false,
                                    });
                                }
                                else {
                                    // it not existed, add it

                                    setSelectedOutletItemPoints(selectedOutletItemPoints + item.creditToBuy);

                                    setSelectedOutletItemDict({
                                        ...selectedOutletItemDict,
                                        [item.uniqueId]: true,
                                    });
                                }
                            }
                            else {
                                // means not valid to add to redeem cart

                                setSelectedOutletItemDict({
                                    ...selectedOutletItemDict,
                                    [item.uniqueId]: false,
                                });
                            }
                        }}
                    />
                    <View>
                        <Text
                            style={{
                                fontFamily: 'NunitoSans-Bold',
                                fontSize: switchMerchant ? 10 : 14,
                                color: Colors.blackColor,
                            }}>
                            <Text>{item.name}</Text>
                        </Text>
                        <Text
                            style={{
                                fontFamily: 'NunitoSans-Bold',
                                fontSize: switchMerchant ? 10 : 14,
                            }}>
                            {item.creditToBuy.toFixed(0)} point{item.creditToBuy > 1 ? 's' : ''}
                        </Text>
                    </View>
                </View>
            </TouchableOpacity>
        );
    };
    const renderVoucherList = ({ item, index }) => {
        // console.log('render voucher');
        // console.log(item);

        var expirationDate = null;
        var quantityLeft = 0;

        for (
            var i = 0;
            i < selectedCustomerUserTaggableVouchers.length;
            i++
        ) {
            if (
                selectedCustomerUserTaggableVouchers[i]
                    .voucherId ===
                item.uniqueId &&
                selectedCustomerUserTaggableVouchers[i]
                    .redeemDate === null
                &&
                moment().isBefore(selectedCustomerUserTaggableVouchers[i].expirationDate)
            ) {
                if (expirationDate === null) {
                    expirationDate = selectedCustomerUserTaggableVouchers[i].expirationDate;
                }

                quantityLeft++;
            }
        }

        return (
            <TouchableOpacity
                onPress={() => {
                    // Alert.alert(
                    //     `${item.campaignName}`,
                    //     `Are you sure you want to redeem this voucher?`,
                    //     [
                    //         {
                    //             text: 'YES',
                    //             onPress: () => {
                    //                 redeemUserTaggableVoucherByMerchant(item);
                    //             },
                    //         },
                    //         { text: 'NO', onPress: () => { }, style: 'cancel' },
                    //     ],
                    //     { cancelable: false },
                    // );

                    setSelectedVoucherDict({
                        ...selectedVoucherDict,
                        [item.uniqueId]: selectedVoucherDict[item.uniqueId] ? false : true,
                    });
                }}
                style={{
                    paddingHorizontal: windowWidth * 0.02,
                    paddingVertical: windowHeight * 0.02,
                    paddingTop: windowHeight * 0.01,
                    borderWidth: 1,
                    borderRadius: 10,
                    marginTop: 10,
                }}>
                <View
                    style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                    }}>
                    <CheckBox
                        disabled
                        style={{
                            ...(Platform.OS === 'ios' && {
                                width: switchMerchant ? 10 : 16,
                                height: switchMerchant ? 10 : 16,
                            },
                                { marginRight: 10, }),
                        }}
                        value={selectedVoucherDict[item.uniqueId] ? true : false}
                        onValueChange={(value) => {
                            setSelectedVoucherDict({
                                ...selectedVoucherDict,
                                [item.uniqueId]: selectedVoucherDict[item.uniqueId] ? false : true,
                            });
                        }}
                    />
                    <View>
                        <Text
                            style={{
                                fontFamily: 'NunitoSans-Bold',
                                fontSize: switchMerchant ? 10 : 14,
                                color: Colors.blackColor,
                            }}>
                            <Text>{item.campaignName}</Text>
                        </Text>
                        <Text
                            style={{
                                fontFamily: 'NunitoSans-Bold',
                                fontSize: switchMerchant ? 10 : 14,
                            }}>
                            {`Expiring in ${moment(expirationDate).diff(moment(), 'day')} days`}
                        </Text>
                        <Text
                            style={{
                                fontFamily: 'NunitoSans-Bold',
                                fontSize: switchMerchant ? 10 : 14,
                            }}>
                            {`Quantity left: ${quantityLeft} ${quantityLeft >= 50 ? '(and more)' : ''}`}
                        </Text>
                    </View>
                </View>
            </TouchableOpacity>
        );
    };
    const renderStampList = ({ item, index }) => {
        //////////////////////

        // decide stamps to go/completed status

        var statusCompleted = true;
        var statusText = 'Completed!';

        var userLoyaltyStamp = selectedCustomerUserLoyaltyStamps.find(stamp => stamp.loyaltyStampId === item.uniqueId);

        if (userLoyaltyStamp && item.lsItems) {
            for (var i = 0; i < item.lsItems.length; i++) {
                if (!userLoyaltyStamp.getIdHistory.includes(item.lsItems[i].lsItemId)) {
                    // means havent redeem this stamp yet

                    var stampsRequired = item.lsItems[i].noOfStamp - userLoyaltyStamp.stampCount;

                    if (stampsRequired < 0) {
                        stampsRequired = 0;
                    }

                    statusText = `${stampsRequired} more to go!`;

                    statusCompleted = false;

                    break;
                }
                else {
                    // means already redeem this stamp, proceed to next to check

                    continue;
                }
            }
        }

        //////////////////////

        return (
            <TouchableOpacity
                disabled={statusCompleted}
                onPress={() => {
                    setSelectedStampDict({
                        ...selectedStampDict,
                        [item.uniqueId]: selectedStampDict[item.uniqueId] ? false : true,
                    });
                }}
                style={{
                    paddingHorizontal: windowWidth * 0.03,
                    paddingVertical: windowHeight * 0.02,
                    paddingTop: windowHeight * 0.01,
                    borderWidth: 1,
                    borderColor:
                        !statusCompleted ? Colors.blackColor : Colors.primaryColor,
                    borderRadius: 10,
                    marginTop: 10,
                }}
            // disabled={item.stampamount !== '0'}
            >
                <View
                    style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                    }}>
                    <CheckBox
                        disabled
                        style={{
                            ...(Platform.OS === 'ios' && {
                                width: switchMerchant ? 10 : 16,
                                height: switchMerchant ? 10 : 16,
                            },
                                { marginRight: 10, }),
                        }}
                        value={selectedStampDict[item.uniqueId] ? true : false}
                        onValueChange={(value) => {
                            setSelectedStampDict({
                                ...selectedStampDict,
                                [item.uniqueId]: selectedStampDict[item.uniqueId] ? false : true,
                            });
                        }}
                        disabled={statusCompleted}
                    />
                    <View>
                        <Text
                            style={{
                                fontFamily: 'NunitoSans-Bold',
                                fontSize: switchMerchant ? 10 : 14,
                                color: !statusCompleted ? Colors.blackColor : Colors.primaryColor,
                            }}>
                            <Text>{item.name}</Text>
                        </Text>
                        <Text
                            style={{
                                fontFamily: 'NunitoSans-Bold',
                                fontSize: switchMerchant ? 10 : 14,
                                color: !statusCompleted ? Colors.blackColor : Colors.primaryColor,
                            }}>
                            {statusText}
                        </Text>
                    </View>
                </View>
            </TouchableOpacity>
        );
    };

    //////////////////////////////////////////////////////////////////////////////////////////////////////////

    navigation.setOptions({
        headerLeft: () => (
            <TouchableOpacity
                onPress={() => {
                    if (isAlphaUser || true) {
                        navigation.navigate('MenuOrderingScreen');

                        CommonStore.update((s) => {
                            s.currPage = 'MenuOrderingScreen';
                            s.currPageStack = [...currPageStack, 'MenuOrderingScreen'];
                        });
                    }
                    else {
                        navigation.navigate('Table');

                        CommonStore.update((s) => {
                            s.currPage = 'Table';
                            s.currPageStack = [...currPageStack, 'Table'];
                        });
                    }
                    if (expandTab !== EXPAND_TAB_TYPE.OPERATION) {
                        CommonStore.update((s) => {
                            s.expandTab = EXPAND_TAB_TYPE.OPERATION;
                        });
                    }
                }}
                style={styles.headerLeftStyle}>
                <Image
                    style={[{
                        width: 124,
                        height: 26,
                    }, switchMerchant ? {
                        transform: [
                            { scaleX: 0.7 },
                            { scaleY: 0.7 }
                        ],
                    } : {}]}
                    resizeMode="contain"
                    source={require('../assets/image/logo.png')}
                />
            </TouchableOpacity>
        ),
        headerTitle: () => (
            <View
                style={[
                    {
                        // justifyContent: Platform.OS === 'android' ? 'center' : 'flex-start',
                        // alignItems: Platform.OS === 'android' ? 'center' : 'flex-start',
                        // marginRight: Platform.OS === 'ios' ? "27%" : 0,
                        // bottom: switchMerchant ? '2%' : 0,
                        ...global.getHeaderTitleStyle(),
                    },
                    // windowWidth >= 768 && switchMerchant
                    //     ? { right: windowWidth * 0.1 }
                    //     : {},
                    // windowWidth <= 768
                    //     ? { right: 20 }
                    //     : {},
                ]}>
                <Text
                    style={{
                        fontSize: switchMerchant ? 20 : 24,
                        // lineHeight: 25,
                        textAlign: 'left',
                        alignItems: 'flex-start',
                        justifyContent: 'flex-start',
                        fontFamily: 'NunitoSans-Bold',
                        color: Colors.whiteColor,
                        opacity: 1,
                        paddingLeft: Platform.OS === 'ios' ? "1%" : 0,
                    }}>
                    Reward & Redemption
                </Text>
            </View>
        ),
        headerRight: () => (
            <View
                style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                }}>
                {outletSelectDropdownView()}
                <View
                    style={{
                        backgroundColor: 'white',
                        width: 0.5,
                        height: windowHeight * 0.025,
                        opacity: 0.8,
                        marginHorizontal: 15,
                        bottom: -1,
                    }} />
                <TouchableOpacity
                    onPress={() => {
                        if (global.currUserRole === 'admin') {
                            navigation.navigate('Setting');
                        }
                    }}
                    style={{ flexDirection: 'row', alignItems: 'center' }}>
                    <Text
                        style={[{
                            fontFamily: 'NunitoSans-SemiBold',
                            fontSize: switchMerchant ? 10 : 16,
                            color: Colors.secondaryColor,
                            marginRight: 15,
                        }, switchMerchant ? { width: windowWidth / 8 } : {}]}
                        numberOfLines={switchMerchant ? 1 : 1}
                    >
                        {merchantUserName}
                    </Text>
                    <View
                        style={{
                            marginRight: 30,
                            width: windowHeight * 0.05,
                            height: windowHeight * 0.05,
                            borderRadius: windowHeight * 0.05 * 0.5,
                            alignItems: 'center',
                            justifyContent: 'center',
                            backgroundColor: 'white',
                        }}>
                        <Image
                            style={{
                                width: windowHeight * 0.035,
                                height: windowHeight * 0.035,
                                alignSelf: 'center',
                            }}
                            source={require('../assets/image/profile-pic.jpg')}
                        />
                    </View>
                </TouchableOpacity>
            </View>
        ),
    });
    //////////////////////////////////////////////////////////////////

    return (
        <UserIdleWrapper disabled={!isMounted}>
            <View
                style={[
                    styles.container,
                    !isTablet()
                        ? {
                            transform: [{ scaleX: 1 }, { scaleY: 1 }],
                        }
                        : {},
                    {
                        ...getTransformForScreenInsideNavigation(),
                    }
                ]}>
                {/* <View
                    style={[
                        styles.sidebar,
                        !isTablet() ? {} : {},
                        switchMerchant
                            ? {
                                // width: '10%'
                            }
                            : {},
                        {
                            width: windowWidth * 0.08,
                        }
                    ]}>
                    <SideBar navigation={props.navigation} selectedTab={9} />
                </View> */}
                <ModalView
                    visible={newuserModal}
                    supportedOrientations={['landscape', 'portrait']}
                    style={{}}
                    animationType={'fade'}
                    transparent>
                    <View
                        style={{
                            flex: 1,
                            backgroundColor: Colors.modalBgColor,
                            alignItems: 'center',
                            justifyContent: 'center',
                        }}>
                        <View
                            style={{
                                width: switchMerchant
                                    ? windowWidth * 0.4
                                    : windowWidth * 0.4,
                                height: switchMerchant
                                    ? windowHeight * 0.9
                                    : windowHeight * 0.85,
                                backgroundColor: Colors.whiteColor,
                                borderRadius: 5,

                                ...getTransformForModalInsideNavigation(),
                            }}>
                            <TouchableOpacity
                                style={{
                                    position: 'absolute',
                                    right: windowWidth * 0.02,
                                    top: windowWidth * 0.02,

                                    elevation: 1000,
                                    zIndex: 1000,
                                }}
                                onPress={() => {
                                    setNewUserModal(false);
                                    setAmount('');
                                }}>
                                <AntDesign
                                    name="closecircle"
                                    size={switchMerchant ? 15 : 25}
                                    color={Colors.fieldtTxtColor}
                                />
                            </TouchableOpacity>
                            <View style={{ flexDirection: 'row', flex: 1, justifyContent: 'center', alignItems: 'center', padding: 25, }}>
                                <ScrollView style={{ flexDirection: 'column', flex: 1, }}>
                                    <Text
                                        style={{
                                            fontSize: switchMerchant ? 16 : 24,
                                            fontFamily: 'NunitoSans-Bold',
                                        }}>
                                        Enter Guest Details
                                    </Text>
                                    <Text
                                        style={{
                                            fontSize: switchMerchant ? 10 : 16,
                                            paddingVertical: switchMerchant ? 7 : 30,
                                        }}>
                                        First Name *
                                    </Text>
                                    <TextInput
                                        style={{
                                            fontSize: switchMerchant ? 10 : 16,
                                            color: 'black',
                                            fontFamily: 'NunitoSans-Regular',
                                            borderBottomWidth: 1,
                                            borderBottomColor: 'black',
                                            width: switchMerchant ? 200 : 300,
                                            height: 40,
                                        }}
                                        placeholder=""
                                        placeholderTextColor={Platform.select({
                                            ios: '#a9a9a9',
                                        })}
                                        onChangeText={(text) => {
                                            setUserName(text);
                                        }}
                                        defaultValue={userName}
                                    />
                                    <Text
                                        style={{
                                            fontSize: switchMerchant ? 10 : 16,
                                            paddingTop: switchMerchant ? 20 : 30,
                                        }}>
                                        Phone Number
                                    </Text>
                                    <Text
                                        style={{
                                            fontSize: switchMerchant ? 10 : 16,
                                            fontFamily: 'NunitoSans-Bold',
                                            paddingTop: 10,
                                        }}>
                                        {phoneNumber}
                                    </Text>
                                    <Text
                                        style={{
                                            fontSize: switchMerchant ? 10 : 16,
                                            paddingVertical: switchMerchant ? 7 : 30,
                                        }}>
                                        Email Address
                                    </Text>
                                    <TextInput
                                        style={{
                                            fontSize: switchMerchant ? 10 : 16,
                                            color: 'black',
                                            fontFamily: 'NunitoSans-Regular',
                                            borderBottomWidth: 1,
                                            borderBottomColor: 'black',
                                            width: switchMerchant ? 200 : 300,
                                            height: 40,
                                        }}
                                        placeholder=""
                                        placeholderTextColor={Platform.select({
                                            ios: '#a9a9a9',
                                        })}
                                        onChangeText={(text) => {
                                            setEmail(text);
                                        }}
                                        defaultValue={email}
                                    />
                                    <Text
                                        style={{
                                            fontSize: switchMerchant ? 10 : 16,
                                            paddingVertical: switchMerchant ? 7 : 30,
                                        }}>
                                        Birthday
                                    </Text>
                                    <TouchableOpacity
                                        onPress={() => {
                                            setShowStartDatePicker(true);
                                        }}
                                        style={{
                                            // height: 50,
                                            height: switchMerchant ? 35 : 40,
                                            paddingHorizontal: switchMerchant ? 7 : 20,
                                            backgroundColor: Colors.fieldtBgColor,
                                            //marginBottom: 20,
                                            width: switchMerchant ? 100 : 140,
                                            //marginHorizontal: 10,
                                            flexDirection: 'row',
                                            alignItems: 'center',
                                            justifyContent: 'center',
                                            alignContent: 'center',
                                            borderColor: '#E5E5E5',
                                            borderWidth: 1,
                                            fontFamily: 'NunitoSans-Regular',
                                            // fontSize: switchMerchant ? 11 : 14,
                                            borderRadius: 12,
                                        }}>
                                        <Text
                                            style={{
                                                //marginLeft: 15,
                                                color: 'black',
                                                fontSize: switchMerchant ? 10 : 14,
                                                textAlign: 'center',
                                            }}>
                                            {moment(startDate).format('DD MMM YYYY')}
                                        </Text>
                                    </TouchableOpacity>
                                    <TouchableOpacity
                                        onPress={() => {
                                            if (userName !== '') {
                                                //setUserName(currCRMUser ? currCRMUser.name : userName);
                                                //setPhoneNumber(phoneNumber)
                                                setNewUserModal(false);
                                                setTimeout(() => {
                                                    if (selectedSection === SELECT_SECTION.CREDIT) {
                                                        setCreditModal(true);
                                                    }
                                                    else if (selectedSection === SELECT_SECTION.VOUCHER) {
                                                        setVoucherModal(true);
                                                    }
                                                    else if (selectedSection === SELECT_SECTION.POINT) {
                                                        setPointModal(true);
                                                    }
                                                    else if (selectedSection === SELECT_SECTION.STAMP) {
                                                        setStampModal(true);
                                                    }
                                                    else if (selectedSection === SELECT_SECTION.DOCKET) {
                                                        setDocketModal(true);
                                                    }
                                                    else {
                                                        Alert.alert('Info', 'Please select your redemption type first')
                                                    }
                                                }, 500);
                                            }
                                            else {
                                                Alert.alert('Info', 'Please fill in info')
                                            }
                                        }}
                                        disabled={phoneNumberChecking}>
                                        <View
                                            style={{
                                                borderRadius: 10,
                                                backgroundColor: phoneNumberChecking
                                                    ? 'grey'
                                                    : Colors.primaryColor,
                                                paddingVertical: switchMerchant ? 4 : 10,
                                                width: switchMerchant ? 200 : 300,
                                                marginBottom: switchMerchant ? 5 : 15,
                                                marginTop: 20,
                                                alignItems: 'center',
                                                justifyContent: 'center',
                                            }}>
                                            <Text
                                                style={{
                                                    color: Colors.whiteColor,
                                                    fontSize: switchMerchant ? 10 : 16,
                                                    fontFamily: 'NunitoSans-Bold',
                                                }}>
                                                Register
                                            </Text>
                                        </View>
                                    </TouchableOpacity>
                                </ScrollView>
                            </View>
                        </View>
                    </View>
                </ModalView>

                <DateTimePickerModal
                    isVisible={showStartDatePicker}
                    supportedOrientations={['portrait', 'landscape']}
                    mode={'date'}
                    onConfirm={(text) => {
                        setStartDate(moment(text).local());

                        setShowStartDatePicker(false);
                    }}
                    onCancel={() => {
                        setShowStartDatePicker(false);
                    }}
                />
                <View>
                    <View style={{
                        alignItems: 'flex-end',
                        paddingVertical: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 3 : 6,
                        paddingRight: 30,
                    }}>
                        {/* <TouchableOpacity
                            style={{
                                justifyContent: 'center',
                                backgroundColor: '#0A1F44',
                                borderRadius: 5,
                                //width: 160,
                                paddingHorizontal: 15,
                                height: switchMerchant ? 37 : 42,
                                alignItems: 'center',
                                shadowOffset: {
                                    width: 0,
                                    height: 2,
                                },
                                shadowOpacity: 0.22,
                                shadowRadius: 3.22,
                                elevation: 1,
                                zIndex: -1,
                            }}
                            onPress={() => {
                                setHistoryModal(true);
                            }}>
                            <Text
                                style={{
                                    color: Colors.whiteColor,
                                    marginLeft: 5,
                                    fontSize: switchMerchant ? 12 : 16,
                                    fontFamily: 'NunitoSans-Bold',
                                }}>
                                VIEW HISTORY
                            </Text>
                        </TouchableOpacity> */}
                    </View>
                    <View
                        style={{
                            backgroundColor: Colors.whiteColor,
                            width: switchMerchant
                                ? windowWidth * 0.8
                                : windowWidth * 0.87,
                            height: windowHeight * 0.8,
                            // marginTop: 30,
                            marginHorizontal: switchMerchant ? 25 : 30,
                            marginBottom: switchMerchant ? 0 : 30,
                            alignSelf: 'center',
                            justifyContent: 'center',
                            alignItems: 'center',
                            borderRadius: 5,
                            shadowOpacity: 0,
                            shadowColor: '#000',
                            shadowOffset: {
                                width: 0,
                                height: 2,
                            },
                            shadowOpacity: 0.22,
                            shadowRadius: 3.22,
                            elevation: 3,
                        }}>

                        {/* ///////////// HOME /////////////// */}
                        {selectSection === SELECT_SECTION.HOME ? (
                            <View
                                style={{
                                    flex: 1,
                                    width: '100%',
                                    height: '100%',
                                    flexDirection: 'row',
                                    justifyContent: 'space-between',
                                    backgroundColor: Colors.highlightColor,
                                }}>
                                <View
                                    style={{
                                        width:
                                            Platform.OS === 'ios'
                                                ? windowWidth * 0.44
                                                : windowWidth * 0.43,
                                        backgroundColor: Colors.whiteColor,
                                        borderRadius: 5,
                                    }}>
                                    <View
                                        style={{ paddingLeft: 20, paddingTop: switchMerchant ? 5 : 10, paddingTop: 15, }}>
                                        <Text style={{
                                            fontSize: switchMerchant ? 15 : 25,
                                            fontWeight: 'bold',
                                        }}>
                                            Select your redemption type
                                        </Text>
                                    </View>
                                    <View
                                        style={[{
                                            paddingTop: 15,
                                            flexDirection: 'row',
                                            alignItems: 'center',
                                            justifyContent: 'space-evenly',
                                            paddingBottom: 30,
                                        }]} />
                                    <View
                                        style={[{
                                            paddingTop: 15,
                                            flexDirection: 'row',
                                            alignItems: 'center',
                                            justifyContent: 'space-evenly',
                                            paddingBottom: 30,
                                        }]}>

                                        <TouchableOpacity
                                            style={[
                                                {
                                                    width: '25%',
                                                    height: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 130 : 140,
                                                    backgroundColor: Colors.primaryColor,
                                                    borderRadius: 10,
                                                    flexDirection: 'row',
                                                    alignItems: 'center',
                                                    justifyContent: 'center',
                                                    paddingHorizontal: 20,
                                                    paddingVertical: 15,
                                                    shadowColor: '#000',
                                                    shadowOffset: {
                                                        width: 0,
                                                        height: 2,
                                                    },
                                                    shadowOpacity: 0.22,
                                                    shadowRadius: 3.22,
                                                    elevation: 3,
                                                    borderWidth: selectedSection === SELECT_SECTION.CREDIT ? 3.5 : 0,
                                                },
                                            ]}
                                            onPress={() => {
                                                setSelectedSection(SELECT_SECTION.CREDIT);
                                            }}>

                                            <View style={{ alignItems: 'center', }}>
                                                <MaterialCommunityIcons
                                                    name="cash"
                                                    size={switchMerchant ? 30 : 45}
                                                    style={{ color: Colors.whiteColor, }}
                                                />
                                                <Text
                                                    style={[
                                                        {
                                                            fontWeight: 'bold',
                                                            color: Colors.whiteColor,
                                                            fontSize: 18,
                                                            textAlign: 'center',
                                                            paddingBottom: 5,
                                                            paddingVertical: 10,
                                                        },
                                                    ]}>
                                                    Credit
                                                </Text>
                                            </View>
                                        </TouchableOpacity>

                                        <TouchableOpacity
                                            style={[
                                                {
                                                    width: '25%',
                                                    height: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 130 : 140,
                                                    backgroundColor: Colors.secondaryColor,
                                                    borderRadius: 10,
                                                    flexDirection: 'row',
                                                    alignItems: 'center',
                                                    justifyContent: 'center',
                                                    paddingHorizontal: 20,
                                                    paddingVertical: 15,
                                                    shadowColor: '#000',
                                                    shadowOffset: {
                                                        width: 0,
                                                        height: 2,
                                                    },
                                                    shadowOpacity: 0.22,
                                                    shadowRadius: 3.22,
                                                    elevation: 3,
                                                    borderWidth: selectedSection === SELECT_SECTION.VOUCHER ? 3.5 : 0,
                                                },
                                            ]}
                                            onPress={() => {
                                                setSelectedSection(SELECT_SECTION.VOUCHER);
                                            }}>
                                            <View style={{ alignItems: 'center', }}>
                                                <MaterialCommunityIcons
                                                    name="ticket-confirmation-outline"
                                                    size={switchMerchant ? 30 : 45}
                                                    style={{ color: Colors.whiteColor, }}
                                                />
                                                <Text
                                                    style={[
                                                        {
                                                            fontWeight: 'bold',
                                                            color: Colors.whiteColor,
                                                            fontSize: 18,
                                                            textAlign: 'center',
                                                            paddingBottom: 5,
                                                            paddingVertical: 10,
                                                        },
                                                    ]}>
                                                    Voucher
                                                </Text>
                                            </View>
                                        </TouchableOpacity>
                                        <TouchableOpacity
                                            style={[
                                                {
                                                    width: '25%',
                                                    height: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 130 : 140,
                                                    backgroundColor: Colors.tabCyan,
                                                    borderRadius: 10,
                                                    flexDirection: 'row',
                                                    alignItems: 'center',
                                                    justifyContent: 'center',
                                                    paddingHorizontal: 20,
                                                    paddingVertical: 15,
                                                    shadowColor: '#000',
                                                    shadowOffset: {
                                                        width: 0,
                                                        height: 2,
                                                    },
                                                    shadowOpacity: 0.22,
                                                    shadowRadius: 3.22,
                                                    elevation: 3,
                                                    borderWidth: selectedSection === SELECT_SECTION.POINT ? 3.5 : 0,
                                                },
                                            ]}
                                            onPress={() => {
                                                setSelectedSection(SELECT_SECTION.POINT);
                                            }}>

                                            <View style={{ alignItems: 'center', }}>
                                                <MaterialCommunityIcons
                                                    name="star-four-points"
                                                    size={switchMerchant ? 30 : 45}
                                                    style={{ color: Colors.whiteColor }}
                                                />
                                                <Text
                                                    style={[
                                                        {
                                                            fontWeight: 'bold',
                                                            color: Colors.whiteColor,
                                                            fontSize: 18,
                                                            textAlign: 'center',
                                                            paddingBottom: 5,
                                                            paddingVertical: 10,
                                                        },
                                                    ]}>
                                                    Points
                                                </Text>
                                            </View>
                                        </TouchableOpacity>
                                    </View>
                                    <View
                                        style={[{
                                            paddingTop: 15,
                                            flexDirection: 'row',
                                            alignItems: 'center',
                                            justifyContent: 'space-evenly',
                                            paddingBottom: 30,
                                        }]} />
                                    <View
                                        style={[{
                                            paddingTop: 15,
                                            flexDirection: 'row',
                                            alignItems: 'center',
                                            justifyContent: 'space-evenly',
                                            paddingBottom: 30,
                                        }]}>

                                        <TouchableOpacity
                                            style={[
                                                {
                                                    width: '25%',
                                                    height: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 130 : 140,
                                                    backgroundColor: Colors.tabGold,
                                                    borderRadius: 10,
                                                    flexDirection: 'row',
                                                    alignItems: 'center',
                                                    justifyContent: 'center',
                                                    paddingHorizontal: 20,
                                                    paddingVertical: 15,
                                                    shadowColor: '#000',
                                                    shadowOffset: {
                                                        width: 0,
                                                        height: 2,
                                                    },
                                                    shadowOpacity: 0.22,
                                                    shadowRadius: 3.22,
                                                    elevation: 3,
                                                    borderWidth: selectedSection === SELECT_SECTION.STAMP ? 3.5 : 0,
                                                },
                                            ]}
                                            onPress={() => {
                                                setSelectedSection(SELECT_SECTION.STAMP);
                                            }}>

                                            <View style={{ alignItems: 'center', }}>
                                                <FontAwesome5
                                                    name="stamp"
                                                    size={switchMerchant ? 30 : 45}
                                                    style={{ color: Colors.whiteColor }}
                                                />
                                                <Text
                                                    style={[
                                                        {
                                                            fontWeight: 'bold',
                                                            color: Colors.whiteColor,
                                                            fontSize: 18,
                                                            textAlign: 'center',
                                                            paddingBottom: 5,
                                                            paddingVertical: 10,
                                                        },
                                                    ]}>
                                                    Stamps
                                                </Text>
                                            </View>
                                        </TouchableOpacity>

                                        <TouchableOpacity
                                            style={[
                                                {
                                                    width: '25%',
                                                    height: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 130 : 140,
                                                    backgroundColor: Colors.tabMint,
                                                    borderRadius: 10,
                                                    flexDirection: 'row',
                                                    alignItems: 'center',
                                                    justifyContent: 'center',
                                                    paddingHorizontal: 20,
                                                    paddingVertical: 15,
                                                    shadowColor: '#000',
                                                    shadowOffset: {
                                                        width: 0,
                                                        height: 2,
                                                    },
                                                    shadowOpacity: 0.22,
                                                    shadowRadius: 3.22,
                                                    elevation: 3,
                                                    borderWidth: selectedSection === SELECT_SECTION.DOCKET ? 3.5 : 0,
                                                },
                                            ]}
                                            onPress={() => {
                                                setSelectedSection(SELECT_SECTION.DOCKET);
                                            }}>
                                            <View style={{ alignItems: 'center', }}>
                                                <Redemption
                                                    size={switchMerchant ? 30 : 45}
                                                    color={'white'}
                                                />
                                                <Text
                                                    style={[
                                                        {
                                                            fontWeight: 'bold',
                                                            color: Colors.whiteColor,
                                                            fontSize: 18,
                                                            textAlign: 'center',
                                                            paddingBottom: 5,
                                                            paddingVertical: 10,
                                                        },
                                                    ]}>
                                                    Docket
                                                </Text>
                                            </View>
                                        </TouchableOpacity>
                                        <View style={{ width: '25%' }} />
                                    </View>
                                </View>
                                <View
                                    style={{
                                        width:
                                            Platform.OS === 'ios'
                                                ? windowWidth * 0.42
                                                : windowWidth * 0.43,
                                        backgroundColor: Colors.whiteColor,
                                        borderRadius: 5,
                                        paddingBottom: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 10 : 0,
                                    }}>
                                    <View
                                        style={{
                                            alignItems: 'center',
                                            justifyContent: 'center',
                                            flex: 1,
                                            paddingTop: Platform.OS === 'ios' ? 0 : 10,
                                            marginTop: Platform.OS === 'ios' ? 10 : 0,
                                        }}>
                                        <Text
                                            style={{
                                                fontSize: switchMerchant ? 15 : 25,
                                                fontWeight: 'bold',
                                            }}>
                                            Enter Your Phone Number
                                        </Text>
                                    </View>
                                    <View
                                        style={{
                                            justifyContent: 'center',
                                            alignItems: 'center',
                                            flex: 1,
                                        }}>
                                        <Text style={{ fontSize: switchMerchant ? 22 : 45 }}>
                                            {phoneNumber}
                                        </Text>
                                    </View>
                                    <View>
                                        <View
                                            style={{
                                                flexDirection: 'row',
                                                flexWrap: 'wrap',
                                                justifyContent: 'space-between',
                                                alignItems: 'center',
                                                alignSelf: 'center',
                                                width: Platform.OS === 'ios' ? 270 : '45%',
                                                paddingTop: switchMerchant ? 10 : 30,
                                            }}>
                                            <View
                                                style={{
                                                    flexDirection: 'row',
                                                    justifyContent: 'space-between',
                                                    alignSelf: 'center',
                                                    alignItems: 'center',
                                                    width: '100%',
                                                }}>
                                                <TouchableOpacity
                                                    onPress={() => {
                                                        PhoneonNumPadBtn(1);
                                                    }}>
                                                    <View
                                                        style={[
                                                            styles.pinBtn,
                                                            switchMerchant
                                                                ? {
                                                                    width: switchMerchant ? 35 : 70,
                                                                    height: switchMerchant ? 35 : 70,
                                                                    marginBottom: 7,
                                                                }
                                                                : {
                                                                    width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70,
                                                                    height: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70
                                                                },
                                                        ]}>
                                                        <Text
                                                            style={[
                                                                styles.pinNo,
                                                                { fontSize: switchMerchant ? 10 : 20 },
                                                            ]}>
                                                            1
                                                        </Text>
                                                    </View>
                                                </TouchableOpacity>

                                                <TouchableOpacity
                                                    onPress={() => {
                                                        PhoneonNumPadBtn(2);
                                                    }}>
                                                    <View
                                                        style={[
                                                            styles.pinBtn,
                                                            switchMerchant
                                                                ? {
                                                                    width: switchMerchant ? 35 : 70,
                                                                    height: switchMerchant ? 35 : 70,
                                                                    marginBottom: 7,
                                                                }
                                                                : {
                                                                    width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70,
                                                                    height: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70
                                                                },
                                                        ]}>
                                                        <Text
                                                            style={[
                                                                styles.pinNo,
                                                                { fontSize: switchMerchant ? 10 : 20 },
                                                            ]}>
                                                            2
                                                        </Text>
                                                    </View>
                                                </TouchableOpacity>

                                                <TouchableOpacity
                                                    onPress={() => {
                                                        PhoneonNumPadBtn(3);
                                                    }}>
                                                    <View
                                                        style={[
                                                            styles.pinBtn,
                                                            switchMerchant
                                                                ? {
                                                                    width: switchMerchant ? 35 : 70,
                                                                    height: switchMerchant ? 35 : 70,
                                                                    marginBottom: 7,
                                                                }
                                                                : {
                                                                    width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70,
                                                                    height: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70
                                                                },
                                                        ]}>
                                                        <Text
                                                            style={[
                                                                styles.pinNo,
                                                                { fontSize: switchMerchant ? 10 : 20 },
                                                            ]}>
                                                            3
                                                        </Text>
                                                    </View>
                                                </TouchableOpacity>
                                            </View>
                                            <View
                                                style={{
                                                    flexDirection: 'row',
                                                    justifyContent: 'space-between',
                                                    alignSelf: 'center',
                                                    alignItems: 'center',
                                                    width: '100%',
                                                }}>
                                                <TouchableOpacity
                                                    onPress={() => {
                                                        PhoneonNumPadBtn(4);
                                                    }}>
                                                    <View
                                                        style={[
                                                            styles.pinBtn,
                                                            switchMerchant
                                                                ? {
                                                                    width: switchMerchant ? 35 : 70,
                                                                    height: switchMerchant ? 35 : 70,
                                                                    marginBottom: 7,
                                                                }
                                                                : {
                                                                    width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70,
                                                                    height: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70
                                                                },
                                                        ]}>
                                                        <Text
                                                            style={[
                                                                styles.pinNo,
                                                                { fontSize: switchMerchant ? 10 : 20 },
                                                            ]}>
                                                            4
                                                        </Text>
                                                    </View>
                                                </TouchableOpacity>

                                                <TouchableOpacity
                                                    onPress={() => {
                                                        PhoneonNumPadBtn(5);
                                                    }}>
                                                    <View
                                                        style={[
                                                            styles.pinBtn,
                                                            switchMerchant
                                                                ? {
                                                                    width: switchMerchant ? 35 : 70,
                                                                    height: switchMerchant ? 35 : 70,
                                                                    marginBottom: 7,
                                                                }
                                                                : {
                                                                    width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70,
                                                                    height: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70
                                                                },
                                                        ]}>
                                                        <Text
                                                            style={[
                                                                styles.pinNo,
                                                                { fontSize: switchMerchant ? 10 : 20 },
                                                            ]}>
                                                            5
                                                        </Text>
                                                    </View>
                                                </TouchableOpacity>

                                                <TouchableOpacity
                                                    onPress={() => {
                                                        PhoneonNumPadBtn(6);
                                                    }}>
                                                    <View
                                                        style={[
                                                            styles.pinBtn,
                                                            switchMerchant
                                                                ? {
                                                                    width: switchMerchant ? 35 : 70,
                                                                    height: switchMerchant ? 35 : 70,
                                                                    marginBottom: 7,
                                                                }
                                                                : {
                                                                    width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70,
                                                                    height: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70
                                                                },
                                                        ]}>
                                                        <Text
                                                            style={[
                                                                styles.pinNo,
                                                                { fontSize: switchMerchant ? 10 : 20 },
                                                            ]}>
                                                            6
                                                        </Text>
                                                    </View>
                                                </TouchableOpacity>
                                            </View>

                                            <View
                                                style={{
                                                    flexDirection: 'row',
                                                    justifyContent: 'space-between',
                                                    alignSelf: 'center',
                                                    alignItems: 'center',
                                                    width: '100%',
                                                }}>
                                                <TouchableOpacity
                                                    onPress={() => {
                                                        PhoneonNumPadBtn(7);
                                                    }}>
                                                    <View
                                                        style={[
                                                            styles.pinBtn,
                                                            switchMerchant
                                                                ? {
                                                                    width: switchMerchant ? 35 : 70,
                                                                    height: switchMerchant ? 35 : 70,
                                                                    marginBottom: 7,
                                                                }
                                                                : {
                                                                    width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70,
                                                                    height: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70
                                                                },
                                                        ]}>
                                                        <Text
                                                            style={[
                                                                styles.pinNo,
                                                                { fontSize: switchMerchant ? 10 : 20 },
                                                            ]}>
                                                            7
                                                        </Text>
                                                    </View>
                                                </TouchableOpacity>

                                                <TouchableOpacity
                                                    onPress={() => {
                                                        PhoneonNumPadBtn(8);
                                                    }}>
                                                    <View
                                                        style={[
                                                            styles.pinBtn,
                                                            switchMerchant
                                                                ? {
                                                                    width: switchMerchant ? 35 : 70,
                                                                    height: switchMerchant ? 35 : 70,
                                                                    marginBottom: 7,
                                                                }
                                                                : {
                                                                    width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70,
                                                                    height: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70
                                                                },
                                                        ]}>
                                                        <Text
                                                            style={[
                                                                styles.pinNo,
                                                                { fontSize: switchMerchant ? 10 : 20 },
                                                            ]}>
                                                            8
                                                        </Text>
                                                    </View>
                                                </TouchableOpacity>

                                                <TouchableOpacity
                                                    onPress={() => {
                                                        PhoneonNumPadBtn(9);
                                                    }}>
                                                    <View
                                                        style={[
                                                            styles.pinBtn,
                                                            switchMerchant
                                                                ? {
                                                                    width: switchMerchant ? 35 : 70,
                                                                    height: switchMerchant ? 35 : 70,
                                                                    marginBottom: 7,
                                                                }
                                                                : {
                                                                    width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70,
                                                                    height: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70
                                                                },
                                                        ]}>
                                                        <Text
                                                            style={[
                                                                styles.pinNo,
                                                                { fontSize: switchMerchant ? 10 : 20 },
                                                            ]}>
                                                            9
                                                        </Text>
                                                    </View>
                                                </TouchableOpacity>
                                            </View>
                                            <View
                                                style={{
                                                    flexDirection: 'row',
                                                    justifyContent: 'space-between',
                                                    alignSelf: 'center',
                                                    alignItems: 'center',
                                                    width: '100%',
                                                }}>
                                                <TouchableOpacity
                                                    onPress={() => {
                                                        if (phoneNumber.includes('+')) {

                                                        }
                                                        else {
                                                            PhoneonNumPadBtn('+');
                                                        }
                                                    }}>
                                                    <View
                                                        style={[
                                                            styles.pinBtn,
                                                            switchMerchant
                                                                ? {
                                                                    width: switchMerchant ? 35 : 70,
                                                                    height: switchMerchant ? 35 : 70,
                                                                    marginBottom: 7,
                                                                }
                                                                : {
                                                                    width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70,
                                                                    height: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70
                                                                },
                                                        ]}>
                                                        <Text
                                                            style={[
                                                                styles.pinNo,
                                                                { fontSize: switchMerchant ? 10 : 20 },
                                                            ]}>
                                                            +
                                                        </Text>
                                                    </View>
                                                </TouchableOpacity>

                                                <TouchableOpacity
                                                    onPress={() => {
                                                        PhoneonNumPadBtn(0);
                                                    }}>
                                                    <View
                                                        style={[
                                                            styles.pinBtn,
                                                            switchMerchant
                                                                ? {
                                                                    width: switchMerchant ? 35 : 70,
                                                                    height: switchMerchant ? 35 : 70,
                                                                    marginBottom: 7,
                                                                }
                                                                : {
                                                                    width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70,
                                                                    height: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70
                                                                },
                                                        ]}>
                                                        <Text
                                                            style={[
                                                                styles.pinNo,
                                                                { fontSize: switchMerchant ? 10 : 20 },
                                                            ]}>
                                                            0
                                                        </Text>
                                                    </View>
                                                </TouchableOpacity>

                                                <TouchableOpacity
                                                    onPress={() => {
                                                        if (phoneNumber != '+6') {
                                                            PhoneonNumPadBtn(-1);
                                                        }
                                                    }}>
                                                    <View
                                                        style={[
                                                            styles.pinBtn,
                                                            switchMerchant
                                                                ? {
                                                                    width: switchMerchant ? 35 : 70,
                                                                    height: switchMerchant ? 35 : 70,
                                                                    marginBottom: 7,
                                                                }
                                                                : {
                                                                    width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70,
                                                                    height: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70
                                                                },
                                                        ]}>
                                                        <Feather
                                                            name="chevron-left"
                                                            size={switchMerchant ? 13 : 30}
                                                            color={'black'}
                                                            style={{}}
                                                        />
                                                    </View>
                                                </TouchableOpacity>
                                            </View>
                                        </View>
                                    </View>
                                    <View
                                        style={{
                                            flex: 1,
                                            flexDirection: 'row',
                                            alignItems: 'center',
                                            alignSelf: 'center',
                                        }}>
                                        <TouchableOpacity
                                            onPress={() => {
                                                ///if (currOutletShiftStatus === OUTLET_SHIFT_STATUS.OPENED) {
                                                setUserName(currCRMUser ? currCRMUser.name : '');
                                                if (currCRMUser) {
                                                    if (selectedSection === SELECT_SECTION.CREDIT) {
                                                        setCreditModal(true);
                                                    }
                                                    else if (selectedSection === SELECT_SECTION.VOUCHER) {
                                                        setVoucherModal(true);
                                                    }
                                                    else if (selectedSection === SELECT_SECTION.POINT) {
                                                        setPointModal(true);
                                                    }
                                                    else if (selectedSection === SELECT_SECTION.STAMP) {
                                                        setStampModal(true);
                                                    }
                                                    else if (selectedSection === SELECT_SECTION.DOCKET) {
                                                        setDocketModal(true);
                                                    }
                                                    else {
                                                        Alert.alert('Info', 'Please select your redemption type first')
                                                    }
                                                }
                                                else {
                                                    setNewUserModal(true);
                                                }
                                                //}
                                                // else {
                                                //    Alert.alert('Info', 'Shift was closed')
                                                // }

                                            }}
                                            disabled={phoneNumberChecking}>
                                            <View
                                                style={{
                                                    borderRadius: 10,
                                                    backgroundColor: phoneNumberChecking
                                                        ? 'grey'
                                                        : Colors.primaryColor,
                                                    paddingVertical: switchMerchant ? 4 : 10,
                                                    paddingHorizontal: switchMerchant ? 15 : 25,
                                                    marginBottom: switchMerchant ? 5 : (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 0 : 15,
                                                }}>
                                                <Text
                                                    style={{
                                                        color: Colors.whiteColor,
                                                        fontSize: switchMerchant ? 10 : 16,
                                                        fontFamily: 'NunitoSans-Bold',
                                                    }}>
                                                    REDEEM
                                                </Text>
                                            </View>
                                        </TouchableOpacity>
                                    </View>
                                </View>
                                <ModalView
                                    visible={creditModal}
                                    supportedOrientations={['landscape', 'portrait']}
                                    style={{}}
                                    animationType={'fade'}
                                    transparent>
                                    <View
                                        style={{
                                            flex: 1,
                                            backgroundColor: Colors.modalBgColor,
                                            alignItems: 'center',
                                            justifyContent: 'center',
                                        }}>
                                        <View
                                            style={{
                                                width: switchMerchant
                                                    ? windowWidth * 0.77
                                                    : windowWidth * 0.77,
                                                height: switchMerchant
                                                    ? windowHeight * 0.9
                                                    : windowHeight * 0.9,
                                                backgroundColor: Colors.whiteColor,
                                                borderRadius: 5,

                                                ...getTransformForModalInsideNavigation(),
                                            }}>
                                            <TouchableOpacity
                                                style={{
                                                    position: 'absolute',
                                                    right: windowWidth * 0.02,
                                                    top: windowWidth * 0.02,

                                                    elevation: 1000,
                                                    zIndex: 1000,
                                                }}
                                                onPress={() => {
                                                    setCreditModal(false);
                                                    setAmount('');
                                                }}>
                                                <AntDesign
                                                    name="closecircle"
                                                    size={switchMerchant ? 15 : 25}
                                                    color={Colors.fieldtTxtColor}
                                                />
                                            </TouchableOpacity>
                                            <View style={{ flexDirection: 'row', flex: 1 }}>
                                                <View style={{ width: '50%', padding: 30 }}>
                                                    <View style={{ flexDirection: 'column', flex: 1 }}>
                                                        <Text
                                                            style={{
                                                                fontSize: switchMerchant ? 16 : 24,
                                                                fontFamily: 'NunitoSans-Bold',
                                                            }}>
                                                            Customer Details
                                                        </Text>
                                                        <Text
                                                            style={{
                                                                fontSize: switchMerchant ? 10 : 16,
                                                                paddingTop: switchMerchant ? 15 : 30,
                                                            }}>
                                                            First Name
                                                        </Text>
                                                        <Text
                                                            style={{
                                                                fontSize: switchMerchant ? 10 : 16,
                                                                fontFamily: 'NunitoSans-Bold',
                                                                paddingTop: 10,
                                                            }}>
                                                            {currCRMUser ? currCRMUser.name : ''}
                                                        </Text>

                                                        <Text
                                                            style={{
                                                                fontSize: switchMerchant ? 10 : 16,
                                                                paddingTop: switchMerchant ? 15 : 30,
                                                            }}>
                                                            Phone Number
                                                        </Text>
                                                        <Text
                                                            style={{
                                                                fontSize: switchMerchant ? 10 : 16,
                                                                fontFamily: 'NunitoSans-Bold',
                                                                paddingTop: 10,
                                                            }}>
                                                            {currCRMUser ? currCRMUser.number : ''}
                                                        </Text>

                                                        <Text
                                                            style={{
                                                                fontSize: switchMerchant ? 10 : 16,
                                                                paddingTop: switchMerchant ? 15 : 30,
                                                            }}>
                                                            Tier
                                                        </Text>
                                                        <Text
                                                            style={{
                                                                fontSize: switchMerchant ? 10 : 16,
                                                                fontFamily: 'NunitoSans-Bold',
                                                                paddingTop: 10,
                                                            }}>
                                                            {selectedCustomerEdit &&
                                                                selectedCustomerEdit.levelName
                                                                ? selectedCustomerEdit.levelName
                                                                : 'None'}
                                                        </Text>
                                                        <Text
                                                            style={{
                                                                fontSize: switchMerchant ? 10 : 16,
                                                                paddingTop: switchMerchant ? 15 : 30,
                                                            }}>
                                                            Total Available Credit
                                                        </Text>

                                                        <Text
                                                            style={{
                                                                fontSize: switchMerchant ? 10 : 16,
                                                                fontFamily: 'NunitoSans-Bold',
                                                                paddingTop: 10,
                                                            }}>
                                                            RM {selectedCustomerLCCBalance.toFixed(2)}
                                                        </Text>
                                                    </View>
                                                </View>

                                                <View style={{ width: '50%' }}>
                                                    <View
                                                        style={{
                                                            alignItems: 'center',
                                                            justifyContent: 'center',
                                                        }}>
                                                        <Text
                                                            style={{
                                                                fontSize: switchMerchant ? 15 : 20,
                                                                fontWeight: 'bold',
                                                                margin: switchMerchant ? 1 : 5,
                                                                paddingTop: 20,
                                                            }}>
                                                            Eligible Redemption
                                                        </Text>
                                                        <Text
                                                            style={{
                                                                fontSize: switchMerchant ? 15 : 20,
                                                                fontWeight: 'bold',
                                                                margin: switchMerchant ? 1 : (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 1 : 12,
                                                                paddingTop: 5,
                                                            }}>
                                                            Enter the amount to deduct
                                                        </Text>
                                                        <View
                                                            style={{
                                                                justifyContent: 'center',
                                                                flexDirection: 'row',
                                                                alignItems: 'center',
                                                                alignSelf: 'center',
                                                            }}>
                                                            <Text
                                                                style={{
                                                                    fontSize: switchMerchant ? 13 : 20,
                                                                    color: Colors.fieldtTxtColor,
                                                                }}>
                                                                Credit
                                                            </Text>
                                                        </View>
                                                        <View style={{ justifyContent: 'center' }}>
                                                            <Text style={{ fontSize: switchMerchant ? 22 : 50 }}>
                                                                {amount.length === 0 ? '0' : amount}
                                                            </Text>
                                                        </View>
                                                    </View>
                                                    <View>
                                                        <View
                                                            style={{
                                                                flexDirection: 'row',
                                                                flexWrap: 'wrap',
                                                                justifyContent: 'space-between',
                                                                alignItems: 'center',
                                                                alignSelf: 'center',
                                                                width: '55%',
                                                                paddingTop: switchMerchant ? 5 : (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 5 : 30,
                                                            }}>
                                                            <View
                                                                style={{
                                                                    flexDirection: 'row',
                                                                    justifyContent: 'space-between',
                                                                    alignSelf: 'center',
                                                                    alignItems: 'center',
                                                                    width: '100%',
                                                                }}>
                                                                <TouchableOpacity
                                                                    onPress={() => {
                                                                        onNumPadBtn(1);
                                                                    }}>
                                                                    <View
                                                                        style={[
                                                                            styles.pinBtn,
                                                                            switchMerchant
                                                                                ? {
                                                                                    width: switchMerchant ? 35 : 70,
                                                                                    height: switchMerchant ? 35 : 70,
                                                                                    marginBottom: 7,
                                                                                }
                                                                                : {
                                                                                    width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70,
                                                                                    height: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70
                                                                                },
                                                                        ]}>
                                                                        <Text
                                                                            style={[
                                                                                styles.pinNo,
                                                                                { fontSize: switchMerchant ? 10 : 20 },
                                                                            ]}>
                                                                            1
                                                                        </Text>
                                                                    </View>
                                                                </TouchableOpacity>

                                                                <TouchableOpacity
                                                                    onPress={() => {
                                                                        onNumPadBtn(2);
                                                                    }}>
                                                                    <View
                                                                        style={[
                                                                            styles.pinBtn,
                                                                            switchMerchant
                                                                                ? {
                                                                                    width: switchMerchant ? 35 : 70,
                                                                                    height: switchMerchant ? 35 : 70,
                                                                                    marginBottom: 7,
                                                                                }
                                                                                : {
                                                                                    width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70,
                                                                                    height: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70
                                                                                },
                                                                        ]}>
                                                                        <Text
                                                                            style={[
                                                                                styles.pinNo,
                                                                                { fontSize: switchMerchant ? 10 : 20 },
                                                                            ]}>
                                                                            2
                                                                        </Text>
                                                                    </View>
                                                                </TouchableOpacity>

                                                                <TouchableOpacity
                                                                    onPress={() => {
                                                                        onNumPadBtn(3);
                                                                    }}>
                                                                    <View
                                                                        style={[
                                                                            styles.pinBtn,
                                                                            switchMerchant
                                                                                ? {
                                                                                    width: switchMerchant ? 35 : 70,
                                                                                    height: switchMerchant ? 35 : 70,
                                                                                    marginBottom: 7,
                                                                                }
                                                                                : {
                                                                                    width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70,
                                                                                    height: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70
                                                                                },
                                                                        ]}>
                                                                        <Text
                                                                            style={[
                                                                                styles.pinNo,
                                                                                { fontSize: switchMerchant ? 10 : 20 },
                                                                            ]}>
                                                                            3
                                                                        </Text>
                                                                    </View>
                                                                </TouchableOpacity>
                                                            </View>
                                                            <View
                                                                style={{
                                                                    flexDirection: 'row',
                                                                    justifyContent: 'space-between',
                                                                    alignSelf: 'center',
                                                                    alignItems: 'center',
                                                                    width: '100%',
                                                                }}>
                                                                <TouchableOpacity
                                                                    onPress={() => {
                                                                        onNumPadBtn(4);
                                                                    }}>
                                                                    <View
                                                                        style={[
                                                                            styles.pinBtn,
                                                                            switchMerchant
                                                                                ? {
                                                                                    width: switchMerchant ? 35 : 70,
                                                                                    height: switchMerchant ? 35 : 70,
                                                                                    marginBottom: 7,
                                                                                }
                                                                                : {
                                                                                    width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70,
                                                                                    height: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70
                                                                                },
                                                                        ]}>
                                                                        <Text
                                                                            style={[
                                                                                styles.pinNo,
                                                                                { fontSize: switchMerchant ? 10 : 20 },
                                                                            ]}>
                                                                            4
                                                                        </Text>
                                                                    </View>
                                                                </TouchableOpacity>

                                                                <TouchableOpacity
                                                                    onPress={() => {
                                                                        onNumPadBtn(5);
                                                                    }}>
                                                                    <View
                                                                        style={[
                                                                            styles.pinBtn,
                                                                            switchMerchant
                                                                                ? {
                                                                                    width: switchMerchant ? 35 : 70,
                                                                                    height: switchMerchant ? 35 : 70,
                                                                                    marginBottom: 7,
                                                                                }
                                                                                : {
                                                                                    width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70,
                                                                                    height: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70
                                                                                },
                                                                        ]}>
                                                                        <Text
                                                                            style={[
                                                                                styles.pinNo,
                                                                                { fontSize: switchMerchant ? 10 : 20 },
                                                                            ]}>
                                                                            5
                                                                        </Text>
                                                                    </View>
                                                                </TouchableOpacity>

                                                                <TouchableOpacity
                                                                    onPress={() => {
                                                                        onNumPadBtn(6);
                                                                    }}>
                                                                    <View
                                                                        style={[
                                                                            styles.pinBtn,
                                                                            switchMerchant
                                                                                ? {
                                                                                    width: switchMerchant ? 35 : 70,
                                                                                    height: switchMerchant ? 35 : 70,
                                                                                    marginBottom: 7,
                                                                                }
                                                                                : {
                                                                                    width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70,
                                                                                    height: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70
                                                                                },
                                                                        ]}>
                                                                        <Text
                                                                            style={[
                                                                                styles.pinNo,
                                                                                { fontSize: switchMerchant ? 10 : 20 },
                                                                            ]}>
                                                                            6
                                                                        </Text>
                                                                    </View>
                                                                </TouchableOpacity>
                                                            </View>
                                                            <View
                                                                style={{
                                                                    flexDirection: 'row',
                                                                    justifyContent: 'space-between',
                                                                    alignSelf: 'center',
                                                                    alignItems: 'center',
                                                                    width: '100%',
                                                                }}>
                                                                <TouchableOpacity
                                                                    onPress={() => {
                                                                        onNumPadBtn(7);
                                                                    }}>
                                                                    <View
                                                                        style={[
                                                                            styles.pinBtn,
                                                                            switchMerchant
                                                                                ? {
                                                                                    width: switchMerchant ? 35 : 70,
                                                                                    height: switchMerchant ? 35 : 70,
                                                                                    marginBottom: 7,
                                                                                }
                                                                                : {
                                                                                    width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70,
                                                                                    height: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70
                                                                                },
                                                                        ]}>
                                                                        <Text
                                                                            style={[
                                                                                styles.pinNo,
                                                                                { fontSize: switchMerchant ? 10 : 20 },
                                                                            ]}>
                                                                            7
                                                                        </Text>
                                                                    </View>
                                                                </TouchableOpacity>

                                                                <TouchableOpacity
                                                                    onPress={() => {
                                                                        onNumPadBtn(8);
                                                                    }}>
                                                                    <View
                                                                        style={[
                                                                            styles.pinBtn,
                                                                            switchMerchant
                                                                                ? {
                                                                                    width: switchMerchant ? 35 : 70,
                                                                                    height: switchMerchant ? 35 : 70,
                                                                                    marginBottom: 7,
                                                                                }
                                                                                : {
                                                                                    width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70,
                                                                                    height: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70
                                                                                },
                                                                        ]}>
                                                                        <Text
                                                                            style={[
                                                                                styles.pinNo,
                                                                                { fontSize: switchMerchant ? 10 : 20 },
                                                                            ]}>
                                                                            8
                                                                        </Text>
                                                                    </View>
                                                                </TouchableOpacity>

                                                                <TouchableOpacity
                                                                    onPress={() => {
                                                                        onNumPadBtn(9);
                                                                    }}>
                                                                    <View
                                                                        style={[
                                                                            styles.pinBtn,
                                                                            switchMerchant
                                                                                ? {
                                                                                    width: switchMerchant ? 35 : 70,
                                                                                    height: switchMerchant ? 35 : 70,
                                                                                    marginBottom: 7,
                                                                                }
                                                                                : {
                                                                                    width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70,
                                                                                    height: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70
                                                                                },
                                                                        ]}>
                                                                        <Text
                                                                            style={[
                                                                                styles.pinNo,
                                                                                { fontSize: switchMerchant ? 10 : 20 },
                                                                            ]}>
                                                                            9
                                                                        </Text>
                                                                    </View>
                                                                </TouchableOpacity>
                                                            </View>
                                                            <View
                                                                style={{
                                                                    flexDirection: 'row',
                                                                    justifyContent: 'space-between',
                                                                    alignSelf: 'center',
                                                                    alignItems: 'center',
                                                                    width: '100%',
                                                                }}>
                                                                <TouchableOpacity
                                                                    onPress={() => {
                                                                        if (amount.includes('.')) {

                                                                        }
                                                                        else {
                                                                            onNumPadBtn('.');
                                                                        }
                                                                    }}>
                                                                    <View
                                                                        style={[
                                                                            styles.pinBtn,
                                                                            switchMerchant
                                                                                ? {
                                                                                    width: switchMerchant ? 35 : 70,
                                                                                    height: switchMerchant ? 35 : 70,
                                                                                    marginBottom: 7,
                                                                                }
                                                                                : {
                                                                                    width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70,
                                                                                    height: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70
                                                                                },
                                                                        ]}>
                                                                        <Text
                                                                            style={[
                                                                                styles.pinNo,
                                                                                { fontSize: switchMerchant ? 10 : 20 },
                                                                            ]}>
                                                                            .
                                                                        </Text>
                                                                    </View>
                                                                </TouchableOpacity>

                                                                <TouchableOpacity
                                                                    onPress={() => {
                                                                        onNumPadBtn(0);
                                                                    }}>
                                                                    <View
                                                                        style={[
                                                                            styles.pinBtn,
                                                                            switchMerchant
                                                                                ? {
                                                                                    width: switchMerchant ? 35 : 70,
                                                                                    height: switchMerchant ? 35 : 70,
                                                                                    marginBottom: 7,
                                                                                }
                                                                                : {
                                                                                    width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70,
                                                                                    height: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70
                                                                                },
                                                                        ]}>
                                                                        <Text
                                                                            style={[
                                                                                styles.pinNo,
                                                                                { fontSize: switchMerchant ? 10 : 20 },
                                                                            ]}>
                                                                            0
                                                                        </Text>
                                                                    </View>
                                                                </TouchableOpacity>

                                                                <TouchableOpacity
                                                                    onPress={() => {
                                                                        onNumPadBtn(-1);
                                                                    }}>
                                                                    <View
                                                                        style={[
                                                                            styles.pinBtn,
                                                                            switchMerchant
                                                                                ? {
                                                                                    width: switchMerchant ? 35 : 70,
                                                                                    height: switchMerchant ? 35 : 70,
                                                                                    marginBottom: 7,
                                                                                }
                                                                                : {
                                                                                    width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70,
                                                                                    height: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70
                                                                                },
                                                                        ]}>
                                                                        <Feather
                                                                            name="chevron-left"
                                                                            size={switchMerchant ? 13 : 30}
                                                                            color={'black'}
                                                                            style={{}}
                                                                        />
                                                                    </View>
                                                                </TouchableOpacity>
                                                            </View>
                                                        </View>

                                                        <View
                                                            style={{
                                                                width: '100%',
                                                                alignItems: 'center',
                                                                alignSelf: 'center',
                                                            }}>
                                                            <TouchableOpacity
                                                                onPress={() => {
                                                                    if (amount && parseFloat(amount) > 0 && userName !== '') {
                                                                        if (parseFloat(amount) <= selectedCustomerLCCBalance) {
                                                                            deductCredit();
                                                                        }
                                                                        else {
                                                                            Alert.alert(
                                                                                'Info',
                                                                                'Amount must be less than available credit',
                                                                            );
                                                                        }

                                                                        // setCreditModal(false)
                                                                        // Alert.alert('Info', 'You have successfully redeemed your rewards. Spend more and get rewarded with more perks!')
                                                                    } else {
                                                                        Alert.alert(
                                                                            'Info',
                                                                            'Amount must be more than 0\nPlease input first name',
                                                                        );
                                                                    }
                                                                }}
                                                                disabled={phoneNumberChecking}
                                                                style={{
                                                                    width: switchMerchant ? '50%' : '45%',
                                                                }}>
                                                                <View
                                                                    style={{
                                                                        borderRadius: 10,
                                                                        backgroundColor: phoneNumberChecking
                                                                            ? 'grey'
                                                                            : Colors.primaryColor,
                                                                        paddingVertical: switchMerchant ? 5 : 10,
                                                                        paddingHorizontal: 25,
                                                                        display: 'flex',
                                                                        alignItems: 'center',
                                                                        justifyContent: 'center',
                                                                    }}>
                                                                    <Text
                                                                        style={{
                                                                            color: Colors.whiteColor,
                                                                            fontSize: switchMerchant ? 10 : 16,
                                                                            fontFamily: 'NunitoSans-Bold',
                                                                        }}>
                                                                        REDEEM
                                                                    </Text>
                                                                </View>
                                                            </TouchableOpacity>
                                                        </View>
                                                    </View>
                                                </View>
                                            </View>
                                        </View>
                                    </View>
                                </ModalView>
                                {/* voucher */}
                                <ModalView
                                    visible={voucherModal}
                                    supportedOrientations={['landscape', 'portrait']}
                                    style={{}}
                                    animationType={'fade'}
                                    transparent>
                                    <View
                                        style={{
                                            flex: 1,
                                            backgroundColor: Colors.modalBgColor,
                                            alignItems: 'center',
                                            justifyContent: 'center',
                                        }}>
                                        <View
                                            style={{
                                                width: switchMerchant
                                                    ? windowWidth * 0.77
                                                    : windowWidth * 0.77,
                                                height: switchMerchant
                                                    ? windowHeight * 0.9
                                                    : windowHeight * 0.85,
                                                backgroundColor: Colors.whiteColor,
                                                borderRadius: 5,

                                                ...getTransformForModalInsideNavigation(),
                                            }}>
                                            <TouchableOpacity
                                                style={{
                                                    position: 'absolute',
                                                    right: windowWidth * 0.02,
                                                    top: windowWidth * 0.02,

                                                    elevation: 1000,
                                                    zIndex: 1000,
                                                }}
                                                onPress={() => {
                                                    setVoucherModal(false);
                                                }}>
                                                <AntDesign
                                                    name="closecircle"
                                                    size={switchMerchant ? 15 : 25}
                                                    color={Colors.fieldtTxtColor}
                                                />
                                            </TouchableOpacity>
                                            <View style={{ flexDirection: 'row', flex: 1 }}>
                                                <View style={{ width: '50%', padding: 30 }}>
                                                    <View style={{ flexDirection: 'column', flex: 1 }}>
                                                        <Text
                                                            style={{
                                                                fontSize: switchMerchant ? 16 : 24,
                                                                fontFamily: 'NunitoSans-Bold',
                                                            }}>
                                                            Customer Details
                                                        </Text>
                                                        <Text
                                                            style={{
                                                                fontSize: switchMerchant ? 10 : 16,
                                                                paddingTop: switchMerchant ? 15 : 30,
                                                            }}>
                                                            First Name
                                                        </Text>
                                                        <Text
                                                            style={{
                                                                fontSize: switchMerchant ? 10 : 16,
                                                                fontFamily: 'NunitoSans-Bold',
                                                                paddingTop: 10,
                                                            }}>
                                                            {currCRMUser ? currCRMUser.name : ''}
                                                        </Text>

                                                        <Text
                                                            style={{
                                                                fontSize: switchMerchant ? 10 : 16,
                                                                paddingTop: switchMerchant ? 15 : 30,
                                                            }}>
                                                            Phone Number
                                                        </Text>
                                                        <Text
                                                            style={{
                                                                fontSize: switchMerchant ? 10 : 16,
                                                                fontFamily: 'NunitoSans-Bold',
                                                                paddingTop: 10,
                                                            }}>
                                                            {currCRMUser ? currCRMUser.number : ''}
                                                        </Text>

                                                        <Text
                                                            style={{
                                                                fontSize: switchMerchant ? 10 : 16,
                                                                paddingTop: switchMerchant ? 15 : 30,
                                                            }}>
                                                            Tier
                                                        </Text>
                                                        <Text
                                                            style={{
                                                                fontSize: switchMerchant ? 10 : 16,
                                                                fontFamily: 'NunitoSans-Bold',
                                                                paddingTop: 10,
                                                            }}>
                                                            {selectedCustomerEdit &&
                                                                selectedCustomerEdit.levelName
                                                                ? selectedCustomerEdit.levelName
                                                                : 'None'}
                                                        </Text>
                                                    </View>
                                                </View>

                                                <View style={{ width: '50%' }}>
                                                    <View
                                                        style={{
                                                            alignItems: 'center',
                                                            justifyContent: 'center',
                                                        }}>
                                                        <Text
                                                            style={{
                                                                fontSize: switchMerchant ? 15 : 20,
                                                                fontWeight: 'bold',
                                                                margin: switchMerchant ? 1 : 5,
                                                                paddingTop: 20,
                                                            }}>
                                                            Eligible Redemption
                                                        </Text>
                                                    </View>
                                                    {taggableVouchers.filter((taggableVoucher) => {
                                                        // var isValid = false;

                                                        for (
                                                            var i = 0;
                                                            i < selectedCustomerUserTaggableVouchers.length;
                                                            i++
                                                        ) {
                                                            if (
                                                                selectedCustomerUserTaggableVouchers[i]
                                                                    .voucherId === taggableVoucher.uniqueId &&
                                                                selectedCustomerUserTaggableVouchers[i] &&
                                                                selectedCustomerUserTaggableVouchers[i].redeemDate ===
                                                                null
                                                                &&
                                                                moment().isBefore(selectedCustomerUserTaggableVouchers[i].expirationDate)
                                                                &&
                                                                (
                                                                    selectedCustomerUserTaggableVouchers[i].activationDate
                                                                        ?
                                                                        (
                                                                            moment().isBefore(selectedCustomerUserTaggableVouchers[i].activationDate)
                                                                                ?
                                                                                false
                                                                                :
                                                                                true
                                                                        )
                                                                        :
                                                                        true
                                                                )
                                                            ) {
                                                                // console.log('check voucher');
                                                                // console.log(selectedCustomerUserTaggableVouchers[i]);

                                                                return true;
                                                            }
                                                        }
                                                    }).length > 0 ? (
                                                        <>
                                                            <View style={{ height: windowHeight * 0.67, justifyContent: 'center', paddingHorizontal: 20, paddingVertical: 15 }}>
                                                                <FlatList
                                                                    showsVerticalScrollIndicator={false}
                                                                    data={taggableVouchers.filter((taggableVoucher) => {
                                                                        // var isValid = false;

                                                                        for (
                                                                            var i = 0;
                                                                            i < selectedCustomerUserTaggableVouchers.length;
                                                                            i++
                                                                        ) {
                                                                            if (
                                                                                selectedCustomerUserTaggableVouchers[i]
                                                                                    .voucherId ===
                                                                                taggableVoucher.uniqueId &&
                                                                                selectedCustomerUserTaggableVouchers[i]
                                                                                    .redeemDate === null
                                                                                &&
                                                                                moment().isBefore(selectedCustomerUserTaggableVouchers[i].expirationDate)
                                                                                &&
                                                                                (
                                                                                    selectedCustomerUserTaggableVouchers[i].activationDate
                                                                                        ?
                                                                                        (
                                                                                            moment().isBefore(selectedCustomerUserTaggableVouchers[i].activationDate)
                                                                                                ?
                                                                                                false
                                                                                                :
                                                                                                true
                                                                                        )
                                                                                        :
                                                                                        true
                                                                                )
                                                                            ) {
                                                                                // console.log('data voucher');
                                                                                // console.log(selectedCustomerUserTaggableVouchers[i]);
                                                                                return true;
                                                                            }
                                                                        }
                                                                    })}
                                                                    // data={vouchertestdata}
                                                                    renderItem={renderVoucherList}
                                                                />
                                                            </View>


                                                            <View
                                                                style={{
                                                                    width: '100%',
                                                                    alignItems: 'center',
                                                                    alignSelf: 'center',
                                                                }}>
                                                                <TouchableOpacity
                                                                    onPress={() => {
                                                                        redeemUserTaggableVoucherMultipleByMerchant();

                                                                        // if (userName !== '') {
                                                                        //     setStampModal(false)
                                                                        //     Alert.alert('Info', 'You have successfully redeemed your rewards. Spend more and get rewarded with more perks!')
                                                                        // } else {
                                                                        //     Alert.alert(
                                                                        //         'Info',
                                                                        //         'Select voucher first',
                                                                        //     );
                                                                        // }
                                                                    }}
                                                                    disabled={phoneNumberChecking}
                                                                    style={{
                                                                        width: switchMerchant ? '50%' : '45%',
                                                                    }}>
                                                                    <View
                                                                        style={{
                                                                            borderRadius: 10,
                                                                            backgroundColor: phoneNumberChecking
                                                                                ? 'grey'
                                                                                : Colors.primaryColor,
                                                                            paddingVertical: switchMerchant ? 5 : 10,
                                                                            paddingHorizontal: 25,
                                                                            display: 'flex',
                                                                            alignItems: 'center',
                                                                            justifyContent: 'center',
                                                                        }}>
                                                                        <Text
                                                                            style={{
                                                                                color: Colors.whiteColor,
                                                                                fontSize: switchMerchant ? 10 : 16,
                                                                                fontFamily: 'NunitoSans-Bold',
                                                                            }}>
                                                                            REDEEM
                                                                        </Text>
                                                                    </View>
                                                                </TouchableOpacity>
                                                            </View>
                                                        </>
                                                    ) : (
                                                        <View
                                                            style={{
                                                                alignItems: 'center',
                                                                justifyContent: 'center',
                                                                height: '80%',
                                                            }}>
                                                            <Text style={{ color: Colors.blackColor }}>
                                                                - No redeemable vouchers found -
                                                            </Text>
                                                        </View>
                                                    )}
                                                </View>
                                            </View>
                                        </View>
                                    </View>
                                </ModalView>
                                {/* point modal */}
                                <ModalView
                                    visible={pointModal}
                                    supportedOrientations={['landscape', 'portrait']}
                                    style={{}}
                                    animationType={'fade'}
                                    transparent>
                                    <View
                                        style={{
                                            flex: 1,
                                            backgroundColor: Colors.modalBgColor,
                                            alignItems: 'center',
                                            justifyContent: 'center',
                                        }}>
                                        <View
                                            style={{
                                                width: switchMerchant
                                                    ? windowWidth * 0.77
                                                    : windowWidth * 0.77,
                                                height: switchMerchant
                                                    ? windowHeight * 0.9
                                                    : windowHeight * 0.9,
                                                backgroundColor: Colors.whiteColor,
                                                borderRadius: 5,

                                                ...getTransformForModalInsideNavigation(),
                                            }}>
                                            <TouchableOpacity
                                                style={{
                                                    position: 'absolute',
                                                    right: windowWidth * 0.02,
                                                    top: windowWidth * 0.02,

                                                    elevation: 1000,
                                                    zIndex: 1000,
                                                }}
                                                onPress={() => {
                                                    setPointModal(false);
                                                }}>
                                                <AntDesign
                                                    name="closecircle"
                                                    size={switchMerchant ? 15 : 25}
                                                    color={Colors.fieldtTxtColor}
                                                />
                                            </TouchableOpacity>
                                            <View style={{ flexDirection: 'row', flex: 1 }}>
                                                <View style={{ width: '50%', padding: 30 }}>
                                                    <View style={{ flexDirection: 'column', flex: 1 }}>
                                                        <Text
                                                            style={{
                                                                fontSize: switchMerchant ? 16 : 24,
                                                                fontFamily: 'NunitoSans-Bold',
                                                            }}>
                                                            Customer Details
                                                        </Text>
                                                        <Text
                                                            style={{
                                                                fontSize: switchMerchant ? 10 : 16,
                                                                paddingTop: switchMerchant ? 15 : 30,
                                                            }}>
                                                            First Name
                                                        </Text>
                                                        <Text
                                                            style={{
                                                                fontSize: switchMerchant ? 10 : 16,
                                                                fontFamily: 'NunitoSans-Bold',
                                                                paddingTop: 10,
                                                            }}>
                                                            {currCRMUser ? currCRMUser.name : ''}
                                                        </Text>

                                                        <Text
                                                            style={{
                                                                fontSize: switchMerchant ? 10 : 16,
                                                                paddingTop: switchMerchant ? 15 : 30,
                                                            }}>
                                                            Phone Number
                                                        </Text>
                                                        <Text
                                                            style={{
                                                                fontSize: switchMerchant ? 10 : 16,
                                                                fontFamily: 'NunitoSans-Bold',
                                                                paddingTop: 10,
                                                            }}>
                                                            {currCRMUser ? currCRMUser.number : ''}
                                                        </Text>

                                                        <Text
                                                            style={{
                                                                fontSize: switchMerchant ? 10 : 16,
                                                                paddingTop: switchMerchant ? 15 : 30,
                                                            }}>
                                                            Tier
                                                        </Text>
                                                        <Text
                                                            style={{
                                                                fontSize: switchMerchant ? 10 : 16,
                                                                fontFamily: 'NunitoSans-Bold',
                                                                paddingTop: 10,
                                                            }}>
                                                            {selectedCustomerEdit &&
                                                                selectedCustomerEdit.levelName
                                                                ? selectedCustomerEdit.levelName
                                                                : 'None'}
                                                        </Text>
                                                    </View>
                                                </View>

                                                <View style={{ width: '50%' }}>
                                                    <View
                                                        style={{
                                                            alignItems: 'center',
                                                            justifyContent: 'center',
                                                        }}>
                                                        <Text
                                                            style={{
                                                                fontSize: switchMerchant ? 15 : 20,
                                                                fontWeight: 'bold',
                                                                margin: switchMerchant ? 1 : 5,
                                                                paddingTop: 20,
                                                            }}>
                                                            Eligible Redemption
                                                        </Text>
                                                        <Text
                                                            style={{
                                                                fontSize: switchMerchant ? 15 : 20,
                                                                fontFamily: 'NunitoSans-Bold',
                                                                margin: switchMerchant ? 1 : 5,
                                                                paddingTop: 5,
                                                            }}>
                                                            Total points earned: {selectedCustomerPointsBalance.toFixed(0)}
                                                        </Text>
                                                    </View>
                                                    {outletItems.length > 0 ? (
                                                        <>
                                                            <View style={{ height: windowHeight * 0.65, justifyContent: 'center', paddingHorizontal: 20, paddingVertical: 15 }}>
                                                                <FlatList
                                                                    showsVerticalScrollIndicator={false}
                                                                    // data={pointtestdata}
                                                                    data={outletItems}
                                                                    renderItem={renderOutletItemList}
                                                                />
                                                            </View>


                                                            <View
                                                                style={{
                                                                    width: '100%',
                                                                    alignItems: 'center',
                                                                    alignSelf: 'center',
                                                                }}>
                                                                <TouchableOpacity
                                                                    onPress={() => {
                                                                        redeemOutletItemMultipleByMerchant();
                                                                        // if (userName !== '') {
                                                                        //     setStampModal(false)
                                                                        //     Alert.alert('Info', 'You have successfully redeemed your rewards. Spend more and get rewarded with more perks!')
                                                                        // } else {
                                                                        //     Alert.alert(
                                                                        //         'Info',
                                                                        //         'Select product first',
                                                                        //     );
                                                                        // }
                                                                    }}
                                                                    disabled={phoneNumberChecking}
                                                                    style={{
                                                                        width: switchMerchant ? '50%' : '45%',
                                                                    }}>
                                                                    <View
                                                                        style={{
                                                                            borderRadius: 10,
                                                                            backgroundColor: phoneNumberChecking
                                                                                ? 'grey'
                                                                                : Colors.primaryColor,
                                                                            paddingVertical: switchMerchant ? 5 : 10,
                                                                            paddingHorizontal: 25,
                                                                            display: 'flex',
                                                                            alignItems: 'center',
                                                                            justifyContent: 'center',
                                                                        }}>
                                                                        <Text
                                                                            style={{
                                                                                color: Colors.whiteColor,
                                                                                fontSize: switchMerchant ? 10 : 16,
                                                                                fontFamily: 'NunitoSans-Bold',
                                                                            }}>
                                                                            REDEEM
                                                                        </Text>
                                                                    </View>
                                                                </TouchableOpacity>
                                                            </View>
                                                        </>
                                                    ) : (
                                                        <View
                                                            style={{
                                                                alignItems: 'center',
                                                                justifyContent: 'center',
                                                                height: '80%',
                                                            }}>
                                                            <Text style={{ color: Colors.blackColor }}>
                                                                - No redeemable products found -
                                                            </Text>
                                                        </View>
                                                    )}
                                                </View>
                                            </View>
                                        </View>
                                    </View>
                                </ModalView>
                                {/* stamp modal */}
                                <ModalView
                                    visible={stampModal}
                                    supportedOrientations={['landscape', 'portrait']}
                                    style={{}}
                                    animationType={'fade'}
                                    transparent>
                                    <View
                                        style={{
                                            flex: 1,
                                            backgroundColor: Colors.modalBgColor,
                                            alignItems: 'center',
                                            justifyContent: 'center',
                                        }}>
                                        <View
                                            style={{
                                                width: switchMerchant
                                                    ? windowWidth * 0.77
                                                    : windowWidth * 0.77,
                                                height: switchMerchant
                                                    ? windowHeight * 0.9
                                                    : windowHeight * 0.85,
                                                backgroundColor: Colors.whiteColor,
                                                borderRadius: 5,

                                                ...getTransformForModalInsideNavigation(),
                                            }}>
                                            <TouchableOpacity
                                                style={{
                                                    position: 'absolute',
                                                    right: windowWidth * 0.02,
                                                    top: windowWidth * 0.02,

                                                    elevation: 1000,
                                                    zIndex: 1000,
                                                }}
                                                onPress={() => {
                                                    setStampModal(false);
                                                }}>
                                                <AntDesign
                                                    name="closecircle"
                                                    size={switchMerchant ? 15 : 25}
                                                    color={Colors.fieldtTxtColor}
                                                />
                                            </TouchableOpacity>
                                            <View style={{ flexDirection: 'row', flex: 1 }}>
                                                <View style={{ width: '50%', padding: 30 }}>
                                                    <View style={{ flexDirection: 'column', flex: 1 }}>
                                                        <Text
                                                            style={{
                                                                fontSize: switchMerchant ? 16 : 24,
                                                                fontFamily: 'NunitoSans-Bold',
                                                            }}>
                                                            Customer Details
                                                        </Text>
                                                        <Text
                                                            style={{
                                                                fontSize: switchMerchant ? 10 : 16,
                                                                paddingTop: switchMerchant ? 15 : 30,
                                                            }}>
                                                            First Name
                                                        </Text>
                                                        <Text
                                                            style={{
                                                                fontSize: switchMerchant ? 10 : 16,
                                                                fontFamily: 'NunitoSans-Bold',
                                                                paddingTop: 10,
                                                            }}>
                                                            {currCRMUser ? currCRMUser.name : ''}
                                                        </Text>

                                                        <Text
                                                            style={{
                                                                fontSize: switchMerchant ? 10 : 16,
                                                                paddingTop: switchMerchant ? 15 : 30,
                                                            }}>
                                                            Phone Number
                                                        </Text>
                                                        <Text
                                                            style={{
                                                                fontSize: switchMerchant ? 10 : 16,
                                                                fontFamily: 'NunitoSans-Bold',
                                                                paddingTop: 10,
                                                            }}>
                                                            {currCRMUser ? currCRMUser.number : ''}
                                                        </Text>

                                                        <Text
                                                            style={{
                                                                fontSize: switchMerchant ? 10 : 16,
                                                                paddingTop: switchMerchant ? 15 : 30,
                                                            }}>
                                                            Tier
                                                        </Text>
                                                        <Text
                                                            style={{
                                                                fontSize: switchMerchant ? 10 : 16,
                                                                fontFamily: 'NunitoSans-Bold',
                                                                paddingTop: 10,
                                                            }}>
                                                            {selectedCustomerEdit &&
                                                                selectedCustomerEdit.levelName
                                                                ? selectedCustomerEdit.levelName
                                                                : 'None'}
                                                        </Text>
                                                    </View>
                                                </View>

                                                <View style={{ width: '50%' }}>
                                                    <View
                                                        style={{
                                                            alignItems: 'center',
                                                            justifyContent: 'center',
                                                        }}>
                                                        <Text
                                                            style={{
                                                                fontSize: switchMerchant ? 15 : 20,
                                                                fontWeight: 'bold',
                                                                margin: switchMerchant ? 1 : 5,
                                                                paddingTop: 20,
                                                            }}>
                                                            Eligible Redemption
                                                        </Text>
                                                    </View>
                                                    {loyaltyStamps.filter((loyaltyStamp) => {
                                                        // var isValid = false;

                                                        for (
                                                            var i = 0;
                                                            i < selectedCustomerUserLoyaltyStamps.length;
                                                            i++
                                                        ) {
                                                            if (
                                                                selectedCustomerUserLoyaltyStamps[i]
                                                                    .loyaltyStampId === loyaltyStamp.uniqueId
                                                                //     &&
                                                                // selectedCustomerUserLoyaltyStamps[i] &&
                                                                // selectedCustomerUserLoyaltyStamps[i].redeemDate === null
                                                                // &&
                                                                // moment().isBefore(selectedCustomerUserLoyaltyStamps[i].expirationDate)
                                                            ) {
                                                                return true;
                                                            }
                                                        }
                                                    }).length > 0 ? (
                                                        <>
                                                            <View style={{ height: windowHeight * 0.67, justifyContent: 'center', paddingHorizontal: 20, paddingVertical: 15 }}>
                                                                <FlatList
                                                                    showsVerticalScrollIndicator={false}
                                                                    // data={stamptestdata}
                                                                    data={loyaltyStamps.filter((loyaltyStamp) => {
                                                                        // var isValid = false;

                                                                        for (
                                                                            var i = 0;
                                                                            i < selectedCustomerUserLoyaltyStamps.length;
                                                                            i++
                                                                        ) {
                                                                            if (
                                                                                selectedCustomerUserLoyaltyStamps[i]
                                                                                    .loyaltyStampId ===
                                                                                loyaltyStamp.uniqueId
                                                                                //     &&
                                                                                // selectedCustomerUserLoyaltyStamps[i]
                                                                                //     .redeemDate === null
                                                                                // &&
                                                                                // moment().isBefore(selectedCustomerUserLoyaltyStamps[i].expirationDate)
                                                                            ) {
                                                                                return true;
                                                                            }
                                                                        }
                                                                    })}
                                                                    renderItem={renderStampList}
                                                                />
                                                            </View>


                                                            <View
                                                                style={{
                                                                    width: '100%',
                                                                    alignItems: 'center',
                                                                    alignSelf: 'center',
                                                                }}>
                                                                <TouchableOpacity
                                                                    onPress={() => {
                                                                        redeemUserLoyaltyStampMultipleByMerchant();

                                                                        // if (userName !== '') {
                                                                        //     setStampModal(false)
                                                                        //     Alert.alert('Info', 'You have successfully redeemed your rewards. Spend more and get rewarded with more perks!')
                                                                        // } else {
                                                                        //     Alert.alert(
                                                                        //         'Info',
                                                                        //         'Select stamp first',
                                                                        //     );
                                                                        // }
                                                                    }}
                                                                    disabled={phoneNumberChecking}
                                                                    style={{
                                                                        width: switchMerchant ? '50%' : '45%',
                                                                    }}>
                                                                    <View
                                                                        style={{
                                                                            borderRadius: 10,
                                                                            backgroundColor: phoneNumberChecking
                                                                                ? 'grey'
                                                                                : Colors.primaryColor,
                                                                            paddingVertical: switchMerchant ? 5 : 10,
                                                                            paddingHorizontal: 25,
                                                                            display: 'flex',
                                                                            alignItems: 'center',
                                                                            justifyContent: 'center',
                                                                        }}>
                                                                        <Text
                                                                            style={{
                                                                                color: Colors.whiteColor,
                                                                                fontSize: switchMerchant ? 10 : 16,
                                                                                fontFamily: 'NunitoSans-Bold',
                                                                            }}>
                                                                            REDEEM
                                                                        </Text>
                                                                    </View>
                                                                </TouchableOpacity>
                                                            </View>
                                                        </>
                                                    ) : (
                                                        <View
                                                            style={{
                                                                alignItems: 'center',
                                                                justifyContent: 'center',
                                                                height: '80%',
                                                            }}>
                                                            <Text style={{ color: Colors.blackColor }}>
                                                                - No redeemable stamps found -
                                                            </Text>
                                                        </View>
                                                    )}
                                                </View>
                                            </View>
                                        </View>
                                    </View>
                                </ModalView>
                                {/* Docket modal*/}
                                <ModalView
                                    visible={docketModal}
                                    supportedOrientations={['landscape', 'portrait']}
                                    style={{}}
                                    animationType={'fade'}
                                    transparent>
                                    <View
                                        style={{
                                            flex: 1,
                                            backgroundColor: Colors.modalBgColor,
                                            alignItems: 'center',
                                            justifyContent: 'center',
                                        }}>
                                        <View
                                            style={{
                                                width: switchMerchant
                                                    ? windowWidth * 0.77
                                                    : windowWidth * 0.77,
                                                height: switchMerchant
                                                    ? windowHeight * 0.9
                                                    : windowHeight * 0.85,
                                                backgroundColor: Colors.whiteColor,
                                                borderRadius: 5,

                                                ...getTransformForModalInsideNavigation(),
                                            }}>
                                            <TouchableOpacity
                                                style={{
                                                    position: 'absolute',
                                                    right: windowWidth * 0.02,
                                                    top: windowWidth * 0.02,

                                                    elevation: 1000,
                                                    zIndex: 1000,
                                                }}
                                                onPress={() => {
                                                    setDocketModal(false);
                                                }}>
                                                <AntDesign
                                                    name="closecircle"
                                                    size={switchMerchant ? 15 : 25}
                                                    color={Colors.fieldtTxtColor}
                                                />
                                            </TouchableOpacity>
                                            <View style={{ flexDirection: 'row', flex: 1 }}>
                                                <View style={{ width: '50%', padding: 30 }}>
                                                    <View style={{ flexDirection: 'column', flex: 1 }}>
                                                        <Text
                                                            style={{
                                                                fontSize: switchMerchant ? 16 : 24,
                                                                fontFamily: 'NunitoSans-Bold',
                                                            }}>
                                                            Customer Details
                                                        </Text>
                                                        <Text
                                                            style={{
                                                                fontSize: switchMerchant ? 10 : 16,
                                                                paddingTop: switchMerchant ? 15 : 30,
                                                            }}>
                                                            First Name
                                                        </Text>
                                                        <Text
                                                            style={{
                                                                fontSize: switchMerchant ? 10 : 16,
                                                                fontFamily: 'NunitoSans-Bold',
                                                                paddingTop: 10,
                                                            }}>
                                                            {currCRMUser ? currCRMUser.name : ''}
                                                        </Text>

                                                        <Text
                                                            style={{
                                                                fontSize: switchMerchant ? 10 : 16,
                                                                paddingTop: switchMerchant ? 15 : 30,
                                                            }}>
                                                            Phone Number
                                                        </Text>
                                                        <Text
                                                            style={{
                                                                fontSize: switchMerchant ? 10 : 16,
                                                                fontFamily: 'NunitoSans-Bold',
                                                                paddingTop: 10,
                                                            }}>
                                                            {currCRMUser ? currCRMUser.number : ''}
                                                        </Text>

                                                        <Text
                                                            style={{
                                                                fontSize: switchMerchant ? 10 : 16,
                                                                paddingTop: switchMerchant ? 15 : 30,
                                                            }}>
                                                            Tier
                                                        </Text>
                                                        <Text
                                                            style={{
                                                                fontSize: switchMerchant ? 10 : 16,
                                                                fontFamily: 'NunitoSans-Bold',
                                                                paddingTop: 10,
                                                            }}>
                                                            {selectedCustomerEdit &&
                                                                selectedCustomerEdit.levelName
                                                                ? selectedCustomerEdit.levelName
                                                                : 'None'}
                                                        </Text>
                                                    </View>
                                                </View>

                                                <View style={{ width: '50%' }}>
                                                    <View
                                                        style={{
                                                            alignItems: 'center',
                                                            justifyContent: 'center',
                                                        }}>
                                                        <Text
                                                            style={{
                                                                fontSize: switchMerchant ? 15 : 20,
                                                                fontWeight: 'bold',
                                                                margin: switchMerchant ? 1 : 5,
                                                                paddingTop: 20,
                                                            }}>
                                                            Eligible Redemption
                                                        </Text>
                                                    </View>
                                                    {beerDockets.filter((beerDocket) => {
                                                        // var isValid = false;

                                                        for (
                                                            var i = 0;
                                                            i < selectedCustomerUserBeerDockets.length;
                                                            i++
                                                        ) {
                                                            if (
                                                                selectedCustomerUserBeerDockets[i]
                                                                    .beerDocketId === beerDocket.uniqueId
                                                                //     &&
                                                                // selectedCustomerUserLoyaltyStamps[i] &&
                                                                // selectedCustomerUserLoyaltyStamps[i].redeemDate === null
                                                                // &&
                                                                // moment().isBefore(selectedCustomerUserLoyaltyStamps[i].expirationDate)
                                                            ) {
                                                                return true;
                                                            }
                                                        }
                                                    }).length > 0 ? (
                                                        <>
                                                            <View style={{ height: windowHeight * 0.67, justifyContent: 'center', paddingHorizontal: 20, paddingVertical: 15 }}>
                                                                <FlatList
                                                                    showsVerticalScrollIndicator={false}
                                                                    // data={dockettestdata}
                                                                    data={beerDockets.filter((beerDocket) => {
                                                                        // var isValid = false;

                                                                        for (
                                                                            var i = 0;
                                                                            i < selectedCustomerUserBeerDockets.length;
                                                                            i++
                                                                        ) {
                                                                            if (
                                                                                selectedCustomerUserBeerDockets[i]
                                                                                    .beerDocketId ===
                                                                                beerDocket.uniqueId
                                                                                //     &&
                                                                                // selectedCustomerUserLoyaltyStamps[i]
                                                                                //     .redeemDate === null
                                                                                // &&
                                                                                // moment().isBefore(selectedCustomerUserLoyaltyStamps[i].expirationDate)
                                                                            ) {
                                                                                return true;
                                                                            }
                                                                        }
                                                                    })}
                                                                    renderItem={renderDocketList}
                                                                />
                                                            </View>
                                                        </>
                                                    ) : (
                                                        <View
                                                            style={{
                                                                alignItems: 'center',
                                                                justifyContent: 'center',
                                                                height: '80%',
                                                            }}>
                                                            <Text style={{ color: Colors.blackColor }}>
                                                                - No redeemable dockets found -
                                                            </Text>
                                                        </View>
                                                    )}
                                                </View>
                                            </View>
                                        </View>
                                    </View>
                                </ModalView>
                                {/* docket redeem modal */}
                                <ModalView
                                    visible={docketRedeemModal}
                                    supportedOrientations={['landscape', 'portrait']}
                                    style={{}}
                                    animationType={'fade'}
                                    transparent>
                                    <View
                                        style={{
                                            flex: 1,
                                            backgroundColor: Colors.modalBgColor,
                                            alignItems: 'center',
                                            justifyContent: 'center',
                                        }}>
                                        <View
                                            style={{
                                                width: switchMerchant
                                                    ? windowWidth * 0.4
                                                    : windowWidth * 0.4,
                                                height: switchMerchant
                                                    ? windowHeight * 0.9
                                                    : windowHeight * 0.85,
                                                backgroundColor: Colors.whiteColor,
                                                borderRadius: 5,

                                                ...getTransformForModalInsideNavigation(),
                                            }}>
                                            <TouchableOpacity
                                                style={{
                                                    position: 'absolute',
                                                    right: windowWidth * 0.02,
                                                    top: windowWidth * 0.02,

                                                    elevation: 1000,
                                                    zIndex: 1000,
                                                }}
                                                onPress={() => {
                                                    setDocketRedeemModal(false);

                                                    setTimeout(() => {
                                                        setDocketModal(true);
                                                    }, 500);
                                                }}>
                                                <AntDesign
                                                    name="closecircle"
                                                    size={switchMerchant ? 15 : 25}
                                                    color={Colors.fieldtTxtColor}
                                                />
                                            </TouchableOpacity>
                                            <View style={{ flexDirection: 'row', flex: 1 }}>
                                                <View style={{ width: '100%' }}>
                                                    <View
                                                        style={{
                                                            alignItems: 'center',
                                                            justifyContent: 'center',
                                                        }}>
                                                        <Text
                                                            style={{
                                                                fontSize: switchMerchant ? 15 : 20,
                                                                fontWeight: 'bold',
                                                                margin: switchMerchant ? 1 : 5,
                                                                paddingTop: 20,
                                                            }}>
                                                            To Redeem
                                                        </Text>
                                                        {/* <Text
                                                            style={{
                                                                fontSize: switchMerchant ? 15 : 20,
                                                                fontWeight: 'bold',
                                                                margin: switchMerchant ? 1 : 12,
                                                                paddingTop: 5,
                                                            }}>
                                                            To redeem
                                                        </Text> */}
                                                        <View
                                                            style={{
                                                                justifyContent: 'center',
                                                                flexDirection: 'row',
                                                                alignItems: 'center',
                                                                alignSelf: 'center',
                                                            }} />
                                                        <View style={{
                                                            flexDirection: 'row',
                                                            justifyContent: 'center',
                                                            position: 'relative',
                                                        }}>
                                                            <View style={{ justifyContent: 'center' }}>
                                                                <Text style={{
                                                                    fontSize: switchMerchant ? 22 : 50,
                                                                    justifyContent: 'center'
                                                                }}>
                                                                    {amount.length === 0 ? '0' : amount}
                                                                </Text>
                                                            </View>

                                                            {/* <View style={{
                                                                justifyContent: 'center',
                                                                alignItems: 'center',
                                                                position: 'absolute',

                                                                // backgroundColor: 'red',

                                                                marginTop: '2%',
                                                                left: '15%',
                                                            }}>
                                                                <TouchableOpacity
                                                                    onPress={() => {
                                                                    }}
                                                                    style={{
                                                                        width: 100,
                                                                        justifyContent: 'center',
                                                                    }}>
                                                                    <View
                                                                        style={{
                                                                            borderRadius: 10,
                                                                            backgroundColor: phoneNumberChecking
                                                                                ? 'grey'
                                                                                : Colors.primaryColor,
                                                                            paddingVertical: switchMerchant ? 5 : 10,
                                                                            paddingHorizontal: 25,
                                                                            display: 'flex',
                                                                            alignItems: 'center',
                                                                            justifyContent: 'center',
                                                                        }}>
                                                                        <Text
                                                                            style={{
                                                                                color: Colors.whiteColor,
                                                                                fontSize: switchMerchant ? 10 : 16,
                                                                                fontFamily: 'NunitoSans-Bold',
                                                                            }}>                                                                            
                                                                            {'MAX'}
                                                                        </Text>
                                                                    </View>
                                                                </TouchableOpacity>
                                                            </View> */}
                                                        </View>
                                                    </View>
                                                    <View>
                                                        <View
                                                            style={{
                                                                flexDirection: 'row',
                                                                flexWrap: 'wrap',
                                                                justifyContent: 'space-between',
                                                                alignItems: 'center',
                                                                alignSelf: 'center',
                                                                width: '55%',
                                                                paddingTop: switchMerchant ? 5 : 30,
                                                            }}>
                                                            <View
                                                                style={{
                                                                    flexDirection: 'row',
                                                                    justifyContent: 'space-between',
                                                                    alignSelf: 'center',
                                                                    alignItems: 'center',
                                                                    width: '100%',
                                                                }}>
                                                                <TouchableOpacity
                                                                    onPress={() => {
                                                                        onNumPadBtn(1);
                                                                    }}>
                                                                    <View
                                                                        style={[
                                                                            styles.pinBtn,
                                                                            switchMerchant
                                                                                ? {
                                                                                    width: switchMerchant ? 35 : 70,
                                                                                    height: switchMerchant ? 35 : 70,
                                                                                    marginBottom: 7,
                                                                                }
                                                                                : {
                                                                                    width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70,
                                                                                    height: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70
                                                                                },
                                                                        ]}>
                                                                        <Text
                                                                            style={[
                                                                                styles.pinNo,
                                                                                { fontSize: switchMerchant ? 10 : 20 },
                                                                            ]}>
                                                                            1
                                                                        </Text>
                                                                    </View>
                                                                </TouchableOpacity>

                                                                <TouchableOpacity
                                                                    onPress={() => {
                                                                        onNumPadBtn(2);
                                                                    }}>
                                                                    <View
                                                                        style={[
                                                                            styles.pinBtn,
                                                                            switchMerchant
                                                                                ? {
                                                                                    width: switchMerchant ? 35 : 70,
                                                                                    height: switchMerchant ? 35 : 70,
                                                                                    marginBottom: 7,
                                                                                }
                                                                                : {
                                                                                    width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70,
                                                                                    height: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70
                                                                                },
                                                                        ]}>
                                                                        <Text
                                                                            style={[
                                                                                styles.pinNo,
                                                                                { fontSize: switchMerchant ? 10 : 20 },
                                                                            ]}>
                                                                            2
                                                                        </Text>
                                                                    </View>
                                                                </TouchableOpacity>

                                                                <TouchableOpacity
                                                                    onPress={() => {
                                                                        onNumPadBtn(3);
                                                                    }}>
                                                                    <View
                                                                        style={[
                                                                            styles.pinBtn,
                                                                            switchMerchant
                                                                                ? {
                                                                                    width: switchMerchant ? 35 : 70,
                                                                                    height: switchMerchant ? 35 : 70,
                                                                                    marginBottom: 7,
                                                                                }
                                                                                : {
                                                                                    width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70,
                                                                                    height: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70
                                                                                },
                                                                        ]}>
                                                                        <Text
                                                                            style={[
                                                                                styles.pinNo,
                                                                                { fontSize: switchMerchant ? 10 : 20 },
                                                                            ]}>
                                                                            3
                                                                        </Text>
                                                                    </View>
                                                                </TouchableOpacity>
                                                            </View>
                                                            <View
                                                                style={{
                                                                    flexDirection: 'row',
                                                                    justifyContent: 'space-between',
                                                                    alignSelf: 'center',
                                                                    alignItems: 'center',
                                                                    width: '100%',
                                                                }}>
                                                                <TouchableOpacity
                                                                    onPress={() => {
                                                                        onNumPadBtn(4);
                                                                    }}>
                                                                    <View
                                                                        style={[
                                                                            styles.pinBtn,
                                                                            switchMerchant
                                                                                ? {
                                                                                    width: switchMerchant ? 35 : 70,
                                                                                    height: switchMerchant ? 35 : 70,
                                                                                    marginBottom: 7,
                                                                                }
                                                                                : {
                                                                                    width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70,
                                                                                    height: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70
                                                                                },
                                                                        ]}>
                                                                        <Text
                                                                            style={[
                                                                                styles.pinNo,
                                                                                { fontSize: switchMerchant ? 10 : 20 },
                                                                            ]}>
                                                                            4
                                                                        </Text>
                                                                    </View>
                                                                </TouchableOpacity>

                                                                <TouchableOpacity
                                                                    onPress={() => {
                                                                        onNumPadBtn(5);
                                                                    }}>
                                                                    <View
                                                                        style={[
                                                                            styles.pinBtn,
                                                                            switchMerchant
                                                                                ? {
                                                                                    width: switchMerchant ? 35 : 70,
                                                                                    height: switchMerchant ? 35 : 70,
                                                                                    marginBottom: 7,
                                                                                }
                                                                                : {
                                                                                    width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70,
                                                                                    height: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70
                                                                                },
                                                                        ]}>
                                                                        <Text
                                                                            style={[
                                                                                styles.pinNo,
                                                                                { fontSize: switchMerchant ? 10 : 20 },
                                                                            ]}>
                                                                            5
                                                                        </Text>
                                                                    </View>
                                                                </TouchableOpacity>

                                                                <TouchableOpacity
                                                                    onPress={() => {
                                                                        onNumPadBtn(6);
                                                                    }}>
                                                                    <View
                                                                        style={[
                                                                            styles.pinBtn,
                                                                            switchMerchant
                                                                                ? {
                                                                                    width: switchMerchant ? 35 : 70,
                                                                                    height: switchMerchant ? 35 : 70,
                                                                                    marginBottom: 7,
                                                                                }
                                                                                : {
                                                                                    width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70,
                                                                                    height: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70
                                                                                },
                                                                        ]}>
                                                                        <Text
                                                                            style={[
                                                                                styles.pinNo,
                                                                                { fontSize: switchMerchant ? 10 : 20 },
                                                                            ]}>
                                                                            6
                                                                        </Text>
                                                                    </View>
                                                                </TouchableOpacity>
                                                            </View>
                                                            <View
                                                                style={{
                                                                    flexDirection: 'row',
                                                                    justifyContent: 'space-between',
                                                                    alignSelf: 'center',
                                                                    alignItems: 'center',
                                                                    width: '100%',
                                                                }}>
                                                                <TouchableOpacity
                                                                    onPress={() => {
                                                                        onNumPadBtn(7);
                                                                    }}>
                                                                    <View
                                                                        style={[
                                                                            styles.pinBtn,
                                                                            switchMerchant
                                                                                ? {
                                                                                    width: switchMerchant ? 35 : 70,
                                                                                    height: switchMerchant ? 35 : 70,
                                                                                    marginBottom: 7,
                                                                                }
                                                                                : {
                                                                                    width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70,
                                                                                    height: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70
                                                                                },
                                                                        ]}>
                                                                        <Text
                                                                            style={[
                                                                                styles.pinNo,
                                                                                { fontSize: switchMerchant ? 10 : 20 },
                                                                            ]}>
                                                                            7
                                                                        </Text>
                                                                    </View>
                                                                </TouchableOpacity>

                                                                <TouchableOpacity
                                                                    onPress={() => {
                                                                        onNumPadBtn(8);
                                                                    }}>
                                                                    <View
                                                                        style={[
                                                                            styles.pinBtn,
                                                                            switchMerchant
                                                                                ? {
                                                                                    width: switchMerchant ? 35 : 70,
                                                                                    height: switchMerchant ? 35 : 70,
                                                                                    marginBottom: 7,
                                                                                }
                                                                                : {
                                                                                    width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70,
                                                                                    height: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70
                                                                                },
                                                                        ]}>
                                                                        <Text
                                                                            style={[
                                                                                styles.pinNo,
                                                                                { fontSize: switchMerchant ? 10 : 20 },
                                                                            ]}>
                                                                            8
                                                                        </Text>
                                                                    </View>
                                                                </TouchableOpacity>

                                                                <TouchableOpacity
                                                                    onPress={() => {
                                                                        onNumPadBtn(9);
                                                                    }}>
                                                                    <View
                                                                        style={[
                                                                            styles.pinBtn,
                                                                            switchMerchant
                                                                                ? {
                                                                                    width: switchMerchant ? 35 : 70,
                                                                                    height: switchMerchant ? 35 : 70,
                                                                                    marginBottom: 7,
                                                                                }
                                                                                : {
                                                                                    width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70,
                                                                                    height: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70
                                                                                },
                                                                        ]}>
                                                                        <Text
                                                                            style={[
                                                                                styles.pinNo,
                                                                                { fontSize: switchMerchant ? 10 : 20 },
                                                                            ]}>
                                                                            9
                                                                        </Text>
                                                                    </View>
                                                                </TouchableOpacity>
                                                            </View>
                                                            <View
                                                                style={{
                                                                    flexDirection: 'row',
                                                                    justifyContent: 'space-between',
                                                                    alignSelf: 'center',
                                                                    alignItems: 'center',
                                                                    width: '100%',
                                                                }}>
                                                                <TouchableOpacity
                                                                    onPress={() => {
                                                                        onNumPadBtn('00');
                                                                    }}>
                                                                    <View
                                                                        style={[
                                                                            styles.pinBtn,
                                                                            switchMerchant
                                                                                ? {
                                                                                    width: switchMerchant ? 35 : 70,
                                                                                    height: switchMerchant ? 35 : 70,
                                                                                    marginBottom: 7,
                                                                                }
                                                                                : {
                                                                                    width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70,
                                                                                    height: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70
                                                                                },
                                                                        ]}>
                                                                        <Text
                                                                            style={[
                                                                                styles.pinNo,
                                                                                { fontSize: switchMerchant ? 10 : 20 },
                                                                            ]}>
                                                                            00
                                                                        </Text>
                                                                    </View>
                                                                </TouchableOpacity>

                                                                <TouchableOpacity
                                                                    onPress={() => {
                                                                        onNumPadBtn(0);
                                                                    }}>
                                                                    <View
                                                                        style={[
                                                                            styles.pinBtn,
                                                                            switchMerchant
                                                                                ? {
                                                                                    width: switchMerchant ? 35 : 70,
                                                                                    height: switchMerchant ? 35 : 70,
                                                                                    marginBottom: 7,
                                                                                }
                                                                                : {
                                                                                    width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70,
                                                                                    height: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70
                                                                                },
                                                                        ]}>
                                                                        <Text
                                                                            style={[
                                                                                styles.pinNo,
                                                                                { fontSize: switchMerchant ? 10 : 20 },
                                                                            ]}>
                                                                            0
                                                                        </Text>
                                                                    </View>
                                                                </TouchableOpacity>

                                                                <TouchableOpacity
                                                                    onPress={() => {
                                                                        onNumPadBtn(-1);
                                                                    }}>
                                                                    <View
                                                                        style={[
                                                                            styles.pinBtn,
                                                                            switchMerchant
                                                                                ? {
                                                                                    width: switchMerchant ? 35 : 70,
                                                                                    height: switchMerchant ? 35 : 70,
                                                                                    marginBottom: 7,
                                                                                }
                                                                                : {
                                                                                    width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70,
                                                                                    height: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70
                                                                                },
                                                                        ]}>
                                                                        <Feather
                                                                            name="chevron-left"
                                                                            size={switchMerchant ? 13 : 30}
                                                                            color={'black'}
                                                                            style={{}}
                                                                        />
                                                                    </View>
                                                                </TouchableOpacity>
                                                            </View>
                                                        </View>

                                                        <View
                                                            style={{
                                                                width: '100%',
                                                                alignItems: 'center',
                                                                alignSelf: 'center',

                                                                marginTop: '2%',

                                                                // backgroundColor: 'red',

                                                                display: 'flex',
                                                                flexDirection: 'row',
                                                                justifyContent: 'space-around',
                                                            }}>
                                                            <TouchableOpacity
                                                                onPress={() => {
                                                                    if (amount && parseFloat(amount) > 0 && userName !== '' && phoneNumber !== '') {
                                                                        // setDocketRedeemModal(false);
                                                                        // Alert.alert('Info', 'You have successfully redeemed your rewards. Spend more and get rewarded with more perks!');

                                                                        var amountParsed = parseFloat(amount);

                                                                        if (selectedDocketInfo && selectedDocketInfo.beerDocket && selectedDocketInfo.userBeerDocket) {
                                                                            if (amountParsed <= selectedDocketInfo.maxQuantityAllowed) {
                                                                                // means valid

                                                                                redeemUserBeerDocketMultipleByMerchant(amountParsed);
                                                                            }
                                                                            else {
                                                                                Alert.alert('Info', `The max redeemable quantity for this docket is ${selectedDocketInfo.maxQuantityAllowed}.`);
                                                                            }
                                                                        }
                                                                        else {
                                                                            Alert.alert('Info', 'Invalid docket to proceed.');
                                                                        }
                                                                    } else {
                                                                        Alert.alert(
                                                                            'Info',
                                                                            'Amount must be more than 0\nPlease input the anme and phone number to proceed.',
                                                                        );
                                                                    }
                                                                }}
                                                                disabled={phoneNumberChecking}
                                                                style={{
                                                                    width: switchMerchant ? '50%' : '40%',
                                                                }}>
                                                                <View
                                                                    style={{
                                                                        borderRadius: 10,
                                                                        backgroundColor: phoneNumberChecking
                                                                            ? 'grey'
                                                                            : Colors.primaryColor,
                                                                        paddingVertical: switchMerchant ? 5 : 10,
                                                                        paddingHorizontal: 25,
                                                                        display: 'flex',
                                                                        alignItems: 'center',
                                                                        justifyContent: 'center',
                                                                    }}>
                                                                    <Text
                                                                        style={{
                                                                            color: Colors.whiteColor,
                                                                            fontSize: switchMerchant ? 10 : 16,
                                                                            fontFamily: 'NunitoSans-Bold',
                                                                        }}>
                                                                        REDEEM
                                                                    </Text>
                                                                </View>
                                                            </TouchableOpacity>

                                                            <TouchableOpacity
                                                                onPress={() => {
                                                                    if (selectedDocketInfo && selectedDocketInfo.beerDocket && selectedDocketInfo.userBeerDocket) {
                                                                        setAmount(selectedDocketInfo.maxQuantityAllowed.toFixed(0));
                                                                    }
                                                                }}
                                                                disabled={phoneNumberChecking}
                                                                style={{
                                                                    width: switchMerchant ? '50%' : '40%',
                                                                }}>
                                                                <View
                                                                    style={{
                                                                        borderRadius: 10,
                                                                        backgroundColor: phoneNumberChecking
                                                                            ? 'grey'
                                                                            : Colors.secondaryColor,
                                                                        paddingVertical: switchMerchant ? 5 : 10,
                                                                        paddingHorizontal: 25,
                                                                        display: 'flex',
                                                                        alignItems: 'center',
                                                                        justifyContent: 'center',
                                                                    }}>
                                                                    <Text
                                                                        style={{
                                                                            color: Colors.whiteColor,
                                                                            fontSize: switchMerchant ? 10 : 16,
                                                                            fontFamily: 'NunitoSans-Bold',
                                                                        }}>
                                                                        MAX
                                                                    </Text>
                                                                </View>
                                                            </TouchableOpacity>
                                                        </View>
                                                    </View>
                                                </View>
                                            </View>
                                        </View>
                                    </View>
                                </ModalView>
                            </View>
                        ) : (
                            <></>
                        )}
                    </View>
                </View>
            </View >
        </UserIdleWrapper >
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: Colors.highlightColor,
        flexDirection: 'row',
        fontFamily: 'NunitoSans-Regular',
    },
    list: {
        backgroundColor: Colors.whiteColor,
        width: Dimensions.get('window').width * 0.87,
        height: Dimensions.get('window').height * 0.66,
        marginTop: 10,
        marginHorizontal: 30,
        borderRadius: 5,
        shadowOpacity: 0,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
    },
    listItem: {
        fontFamily: 'NunitoSans-Regular',
        marginLeft: 20,
        color: Colors.descriptionColor,
        fontSize: 16,
    },
    sidebar: {
        width: Dimensions.get('window').width * Styles.sideBarWidth,
        // shadowColor: '#000',
        // shadowOffset: {
        //     width: 0,
        //     height: 8,
        // },
        // shadowOpacity: 0.44,
        // shadowRadius: 10.32,
        // elevation: 16,
    },
    content: {
        padding: 20,
        width: Dimensions.get('window').width * (1 - Styles.sideBarWidth),
        // backgroundColor: 'lightgrey',
        backgroundColor: Colors.fieldtBgColor,
    },
    textInput: {
        fontFamily: 'NunitoSans-Regular',
        width: 300,
        height: 50,
        backgroundColor: Colors.fieldtBgColor,
        borderRadius: 10,
        marginRight: 0,
        flex: 1,
        flexDirection: 'row',
    },
    textSize: {
        fontSize: 19,
        fontFamily: 'NunitoSans-SemiBold',
    },
    creditbutton: {
        borderWidth: 1,
        borderRadius: 5,
        padding: 10,
        alignItems: 'center',
        alignSelf: 'center',
        justifyContent: 'center',
        width: Dimensions.get('window').width * 0.3,
    },
    creditbuttonsecond: {
        borderWidth: 1,
        borderRadius: 5,
        padding: 10,
        flexDirection: 'column',
        alignItems: 'center',
        alignSelf: 'center',
        justifyContent: 'center',
        width: Dimensions.get('window').width * 0.10,
        height: Dimensions.get('window').height * 0.15,
    },
    pinBtn: {
        backgroundColor: Colors.lightPrimary,
        width: 115,
        height: 60,
        marginBottom: 16,
        alignContent: 'center',
        justifyContent: 'center',
        borderColor: Colors.fieldtTxtColor,
        alignItems: 'center',
        borderRadius: 10,
    },
    pinNo: {
        fontSize: 20,
        fontFamily: 'NunitoSans-Bold',
    },
    headerLeftStyle: {
        width: Dimensions.get('window').width * 0.17,
        justifyContent: 'center',
        alignItems: 'center',
    },
});

export default LoyaltyRewardRedemption;
