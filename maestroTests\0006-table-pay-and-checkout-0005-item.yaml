# flowTest.yaml

appId: com.mykoodoo.promoter

---

- launchApp

- waitForAnimationToEnd:
    timeout: 20000

- runFlow: 
    file: "0002-pin-signin.yaml"

- tapOn:
    id: "SideBar.buttonOperationOrdering" # make sure the sidebar is fully loaded first

- tapOn:
    id: "SideBar.buttonOperationTable" # make sure the sidebar is fully loaded first

- tapOn:
    text: "Auto Tests"

- tapOn:
    text: "AT1"

- tapOn:
    text: "PAY"

- scrollUntilVisible:
    element:
        text: "Bank Transfer"

- tapOn:
    text: "Bank Transfer"

- scrollUntilVisible:
    element:
        text: "CHECK OUT"

- tapOn:
    text: "CHECK OUT"

- tapOn:
    text: "DONE"

- tapOn:
    text: "CHECKOUT"

- assertVisible:
    text: "Auto Tests"
