import React, {
  Component,
  useReducer,
  useState,
  useEffect,
  useLayoutEffect,
  useRef,
  useCallback,
} from 'react';
import {
  StyleSheet,
  ScrollView,
  Image,
  View,
  Text,
  Alert,
  Dimensions,
  TouchableOpacity,
  Switch,
  FlatList,
  Modal,
  TextInput,
  KeyboardAvoidingView,
  PermissionsAndroid,
  Platform,
  ActivityIndicator,
  PlatformColor,
  Picker,
  useWindowDimensions,
  InteractionManager,
  Animated,
} from 'react-native';
// import Animated, { Easing, mix } from 'react-native-reanimated';
import Colors from '../constant/Colors';
// import firebase from "firebase";
import SideBar from './SideBar';
import Styles from '../constant/Styles';
import Feather from 'react-native-vector-icons/Feather';
import Ionicons from 'react-native-vector-icons/Ionicons';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import Icon from 'react-native-vector-icons/FontAwesome5';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import ApiClient from '../util/ApiClient';
import API from '../constant/API';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { RFPercentage } from 'react-native-responsive-fontsize';
import * as User from '../util/User';
import DropDownPicker from 'react-native-dropdown-picker';
//import RNPickerSelect from 'react-native-picker-select';
import Entypo from 'react-native-vector-icons/Entypo';
import moment, { now } from 'moment';
//import moment from "moment-timezone";
import Ionicon from 'react-native-vector-icons/Ionicons';
import { ReactComponent as Upload } from '../assets/svg/Upload.svg';
import { ReactComponent as Download } from '../assets/svg/Download.svg';
import { ReactComponent as Dish } from '../assets/svg/Dish.svg';
import { ReactComponent as Coins } from '../assets/svg/Coins.svg';
import { ReactComponent as Hand } from '../assets/svg/Hand.svg';
import { ReactComponent as GCoin } from '../assets/svg/GCoin.svg';
import { ReactComponent as GCalendar } from '../assets/svg/GCalendar.svg';
import { ReactComponent as Magnify } from '../assets/svg/Magnify.svg';
import { ReactComponent as ArrowLeft } from '../assets/svg/ArrowLeft.svg';
import { ReactComponent as ArrowRight } from '../assets/svg/ArrowRight.svg';
import { ReactComponent as CashRegister } from '../assets/svg/CashRegister.svg';
import {
  getAddOnChoicePrice,
  getAddOnChoiceQuantity,
  getCartItemPriceWithoutAddOn,
  getDeviceUniqueId,
  getOrderDiscountInfo,
  getOrderDiscountInfoInclOrderBased,
  getTransformForModalInsideNavigation,
  getTransformForScreenInsideNavigation,
  isOutletDisplay,
  isTablet,
  isTabletStatic,
  listenToCurrOutletIdReservationChanges,
  listenToDisplayScreenChanges,
  listenToOutletReviewHistoricalChanges,
  listenToUserOrderHistoricalChanges,
  logEventAnalytics,
  logToFile,
  performResize,
  sendTextEmail,
  showAlertForInterruptedPrinting,
  uploadLogFile,
  requestStoragePermission,
  listenToUserChangesMerchant,
  listenToMerchantIdChangesMerchant,
  listenToCurrOutletIdChangesWaiter,
  listenToAllOutletsChangesMerchant,
  listenToCommonChangesMerchant,
  listenToSelectedOutletItemChanges,
  convertArrayToCSV,
  listenToSelectedOutletTableIdChanges,
  requestNotificationsPermission,
  sortReportDataList,
  listenToSelectedCustomerChangesMerchant,
  generateEmailReport,
  listenToSelectedOrderToPayUserIdChanges,
  getCachedUrlContent,
  sliceUnicodeStringV2WithDots,
  listenToCurrOutletIdChangesMerchant,
  isObjectEqual,
  naturalCompare,
  listenToSelectedCustomerApplicableVoucherIdChangesMerchant,
  waitForSeconds,
  compareOrderDateByDisplayType,
} from '../util/common';
import { Camera, CameraType } from 'react-native-camera-kit';
import Icon1 from 'react-native-vector-icons/Feather';
import AntDesign from 'react-native-vector-icons/AntDesign';
import EvilIcons from 'react-native-vector-icons/EvilIcons';
//import RNFetchBlob from 'rn-fetch-blob';
//import { useSwitchMerchant } from '../hooks';
import { CSVLink } from 'react-csv';
import { CommonStore } from '../store/commonStore';
import { UserStore } from '../store/userStore';
import { MerchantStore } from '../store/merchantStore';
import {
  NOTIFICATIONS_CHANNEL,
  TIMEZONE,
  NOTIFICATIONS_ID,
  CHART_HOURLY_LABEL_LIST,
  ROLE_TYPE,
  ORDER_TYPE,
  ORDER_TYPE_PARSED,
  NOTIFICATIONS_TYPE,
  REPORT_SORT_FIELD_TYPE,
  TABLE_PAGE_SIZE_DROPDOWN_LIST,
  EMAIL_REPORT_TYPE,
  USER_RESERVATION_STATUS,
  OUTLET_SHIFT_STATUS,
  USER_ORDER_STATUS,
  EXPAND_TAB_TYPE,
  PRIVILEGES_NAME,
  KD_PRINT_EVENT_TYPE,
  KD_PRINT_VARIATION,
  PRODUCT_PRICE_TYPE,
  UNIT_TYPE_SHORT,
  APP_TYPE,
  DATE_COMPARE_TYPE,
  CHANNEL_TYPE,
  OUTLET_DISPLAY_PAIRING_DEVICE,
} from '../constant/common';
import { OutletStore } from '../store/outletStore';

import DateTimePickerModal from 'react-native-modal-datetime-picker';
// import DatePicker from "react-datepicker";
// import "react-datepicker/dist/react-datepicker.css";
// import "../constant/datePicker.css";
//import { useKeyboard } from '../hooks';
import AsyncImage from '../components/asyncImage';

import XLSX from 'xlsx';
import 'react-native-get-random-values';
import { v4 as uuidv4 } from 'uuid';

import personicon from '../assets/image/default-profile.png';
import headerLogo from '../assets/image/logo.png';
import { useNetInfo } from '@react-native-community/netinfo';
import NetInfo from '@react-native-community/netinfo';
import { NotificationStore } from '../store/notificationStore';
import messaging from '@react-native-firebase/messaging';
import {
  openCashDrawer,
  printDocket,
  printDocketForKD,
  printUserOrder,
  printKDSummaryCategoryWrapper,
  detectSunmiPrinters,
  openSunmiScanner,
  detectOwnSunmiPrinter,
  detectOwnIminPrinter,
  calcPrintTotalForKdIndividual,
  hadKdTypePrinter,
} from '../util/printer';
import firestore from '@react-native-firebase/firestore';
import { Collections } from '../constant/firebase';
import DeviceInfo from 'react-native-device-info';
import RNPickerSelect from 'react-native-picker-select';

// import '../constant/styles.css';

// import TopBar from './TopBar';

const { nanoid } = require('nanoid');

global.printedOrderListLocal = [];
global.printedOrderPaidOnlineListLocal = [];

global.printedOrderAutoApproveListLocal = [];

global.timerAutoApproveOrders = 0;
global.timerAutoApproveOrdersTiming = 0;

global.isCheckingPrintKdOsNow = false;

const DashboardRA = props => {
  const { navigation } = props;

  ///////////////////////////////////////////////////////////

  /*const [isMounted, setIsMounted] = useState(true);
 
  useFocusEffect(
    useCallback(() => {
      // if (global.simulateTabletMode) {
      //   performResize(
      //     {
      //       windowPhysicalPixels: {
      //         // height: 2160,
      //         // width: 1620,
      //         // scale: 2,
      //         height: global.windowWidthOriginal,
      //         width: global.windowHeightOriginal,
      //         scale: global.fontScaleOriginal
      //       },
      //     },
      //     'iPad 9th Generation',
      //     false,
      //     false,
      //     true,
      //     global.windowWidthOriginal,
      //     global.windowHeightOriginal,
      //     global.fontScaleOriginal,
      //   );
      // }
 
      setIsMounted(true);
      return () => {
        setIsMounted(false);
      };
    }, [])
  );*/

  ///////////////////////////////////////////////////////////

  // const netInfo = useNetInfo();

  const { width: windowWidth, height: windowHeight } = useWindowDimensions();

  const windowWidthForModal = CommonStore.useState(s => s.windowWidthForModal);
  const windowHeightForModal = CommonStore.useState(
    s => s.windowHeightForModal,
  );

  const networkNotStable = CommonStore.useState(s => s.networkNotStable);

  const [messages, setMessages] = useState([
    {
      _id: 1,
      text: 'Hello developer',
      createdAt: new Date(),
      user: {
        _id: 2,
        name: 'React Native',
        avatar: 'https://placeimg.com/140/140/any',
      },
    },
  ]);

  const [exportEmail, setExportEmail] = useState('');
  const [visible, setVisible] = useState(false);
  const [switchMerchant, setSwitchMerchant] = useState(false);

  // Scanner states
  const [showScanner, setShowScanner] = useState(false);

  //////////////////////////////////////////////////////////////

  const userOrders = OutletStore.useState(s => s.userOrders);

  const currPageStack = CommonStore.useState(s => s.currPageStack);

  const userName = UserStore.useState(s => s.name);
  // const merchantName = MerchantStore.useState(s => s.name);

  const merchantId = UserStore.useState(s => s.merchantId);
  const role = UserStore.useState(s => s.role);
  const firebaseUid = UserStore.useState(s => s.firebaseUid);

  const currOutletId = MerchantStore.useState(s => s.currOutletId);
  const currOutlet = MerchantStore.useState(s => s.currOutlet);
  const allOutletsRaw = MerchantStore.useState(s => s.allOutlets);

  const isMasterAccount = UserStore.useState(s => s.isMasterAccount);

  const outletSelectDropdownView = CommonStore.useState(
    s => s.outletSelectDropdownView,
  );

  const isLoading = CommonStore.useState(s => s.isLoading);

  const merchantLogo = MerchantStore.useState(s => s.logo);

  const userGroups = UserStore.useState(s => s.userGroups);

  /////////////////////////////////////////////////////////////////////////////////////

  const outletItems = OutletStore.useState(s => s.outletItems);

  /////////////////////////////////////////////////////////////////////////////////////

  // const currPageStack = CommonStore.useState((s) => s.currPageStack);

  const expandTab = CommonStore.useState(s => s.expandTab);

  const privileges_state = UserStore.useState(s => s.privileges);

  const [privileges, setPrivileges] = useState([]);

  /////////////////////////////////////////////////////////////////////////////////////

  // 2022-12-21 - Loyalty stamps

  const loyaltyStamps = OutletStore.useState(s => s.loyaltyStamps);

  const selectedCustomerUserLoyaltyStamps = OutletStore.useState(
    s => s.selectedCustomerUserLoyaltyStamps,
  );

  /////////////////////////////////////////////////////////////////////////////////////

  const supportCodeData = CommonStore.useState(s => s.supportCodeData);

  const [filterAppType, setFilterAppType] = useState([
    APP_TYPE.WEB_ORDER,
    APP_TYPE.MERCHANT,
    APP_TYPE.USER,
    APP_TYPE.WAITER,
  ]);
  // const [filterAppType, setFilterAppType] = useState([]);

  const [allOutlets, setAllOutlets] = useState([]);

  // 2023-04-14
  const payoutTransactions = OutletStore.useState(s =>
    s.payoutTransactions.filter(p => p.v >= '3'),
  ); // only check for v3
  const payoutTransactionsExtend = OutletStore.useState(s =>
    s.payoutTransactionsExtend.filter(p => p.v >= '3'),
  ); // only check for v3

  const ptTimestamp = OutletStore.useState(s => s.ptTimestamp);
  const pteTimestamp = OutletStore.useState(s => s.pteTimestamp);

  const allOutletsUserOrdersDoneRaw = OutletStore.useState(
    s => s.allOutletsUserOrdersDone,
  );
  const [allOutletsUserOrdersDone, setAllOutletsUserOrdersDone] = useState([]);

  const [openFA, setOpenFA] = useState(false);
  const [openChartDropdown, setOpenChartDropdown] = useState(false);
  const [openDropdownValueX, setOpenDropdownValueX] = useState(false);
  const [openPage, setOpenPage] = useState(false);

  const rackPage = CommonStore.useState(s => s.rackPage);

  const historyStartDate = CommonStore.useState(s => s.historyStartDate);
  const historyEndDate = CommonStore.useState(s => s.historyEndDate);

  const netInfo = useNetInfo();

  const [tempOutletId, setTempOutletId] = useState('');
  const selectedCalendarData = CommonStore.useState(
    s => s.selectedCalendarData,
  );
  const allOutletsItems = OutletStore.useState(s => s.allOutletsItems);
  const allOutletsCategories = OutletStore.useState(
    s => s.allOutletsCategories,
  );
  const selectedOutletItem = CommonStore.useState(s => s.selectedOutletItem);
  const selectedOrderToPayUserId = CommonStore.useState(
    s => s.selectedOrderToPayUserId,
  );
  const selectedOutletTable = CommonStore.useState(s => s.selectedOutletTable);
  const selectedOutletTableIdList = CommonStore.useState(
    s => s.selectedOutletTableIdList,
  );
  const odDt = OutletStore.useState(s => s.odDt);
  const selectedCustomerEdit = CommonStore.useState(
    s => s.selectedCustomerEdit,
  );
  const selectedCustomerApplicableVoucherIdList = OutletStore.useState(
    s => s.selectedCustomerApplicableVoucherIdList,
  );
  const selectedCustomerLCCTransactionsEmail = OutletStore.useState(
    s => s.selectedCustomerLCCTransactionsEmail,
  );
  const selectedCustomerLCCBalanceEmail = OutletStore.useState(
    s => s.selectedCustomerLCCBalanceEmail,
  );
  const selectedCustomerLCCTransactionsPhone = OutletStore.useState(
    s => s.selectedCustomerLCCTransactionsPhone,
  );
  const selectedCustomerLCCBalancePhone = OutletStore.useState(
    s => s.selectedCustomerLCCBalancePhone,
  );

  const selectedCustomerPointsTransactionsEmail = OutletStore.useState(
    s => s.selectedCustomerPointsTransactionsEmail,
  );
  const selectedCustomerPointsBalanceEmail = OutletStore.useState(
    s => s.selectedCustomerPointsBalanceEmail,
  );
  const selectedCustomerPointsTransactionsPhone = OutletStore.useState(
    s => s.selectedCustomerPointsTransactionsPhone,
  );
  const selectedCustomerPointsBalancePhone = OutletStore.useState(
    s => s.selectedCustomerPointsBalancePhone,
  );
  const nUserOrder = NotificationStore.useState(s => s.nUserOrder);
  const nUserReservation = NotificationStore.useState(s => s.nUserReservation);
  const nUserQueue = NotificationStore.useState(s => s.nUserQueue);
  const nOutletSupplyItemLow = NotificationStore.useState(
    s => s.nOutletSupplyItemLow,
  );
  const nOutletItemLow = NotificationStore.useState(s => s.nOutletItemLow);
  const crmUsers = OutletStore.useState(s => s.crmUsers);
  const crmUsersRaw = OutletStore.useState(s => s.crmUsersRaw);
  const outletSupplyItems = CommonStore.useState(s => s.outletSupplyItems);
  const userReservations = OutletStore.useState(s => s.userReservations);
  const userQueues = OutletStore.useState(s => s.userQueues);
  const selectedOutletPromotions = OutletStore.useState(s => s.promotions);
  const availablePromotions = CommonStore.useState(s => s.availablePromotions);
  const availablePromoCodePromotions = CommonStore.useState(
    s => s.availablePromoCodePromotions,
  );
  const crmSegmentsDict = OutletStore.useState(s => s.crmSegmentsDict);
  const crmUserTagsDict = OutletStore.useState(s => s.crmUserTagsDict);
  const selectedPromoCodePromotion = CommonStore.useState(
    s => s.selectedPromoCodePromotion,
  );
  const orderType = CommonStore.useState(s => s.orderType);
  const orderTypeMo = CommonStore.useState(s => s.orderTypeMo);
  const loyaltyCampaigns = OutletStore.useState(s => s.loyaltyCampaigns);
  const selectedCustomerUserLoyaltyCampaigns = OutletStore.useState(
    s => s.selectedCustomerUserLoyaltyCampaigns,
  );
  const selectedCustomerUserTaggableVouchers = OutletStore.useState(
    s => s.selectedCustomerUserTaggableVouchers,
  );
  const taggableVouchersOutlet = OutletStore.useState(
    s => s.taggableVouchersOutlet,
  );
  const taggableVouchersMerchant = OutletStore.useState(
    s => s.taggableVouchersMerchant,
  );
  const taggableVouchers = OutletStore.useState(s => s.taggableVouchers);
  const outletReviewStartDate = CommonStore.useState(
    s => s.outletReviewStartDate,
  );
  const outletReviewEndDate = CommonStore.useState(s => s.outletReviewEndDate);
  const [selectedOutletId, setSelectedOutletId] = useState('');
  const selectedOutletList = CommonStore.useState(s => s.reportOutletIdList);
  const allOutletsUserOrdersDoneCache = OutletStore.useState(
    s => s.allOutletsUserOrdersDoneCache,
  );
  const allOutletsUserOrdersCache = OutletStore.useState(
    s => s.allOutletsUserOrdersCache,
  );
  const allOutletsUserOrdersDoneRealTime = OutletStore.useState(
    s => s.allOutletsUserOrdersDoneRealTime,
  );
  const allOutletsUserOrdersRealTime = OutletStore.useState(
    s => s.allOutletsUserOrdersRealTime,
  );
  const userOrdersNormal = OutletStore.useState(s => s.userOrdersNormal);
  const userOrdersTableDictNormal = OutletStore.useState(
    s => s.userOrdersTableDictNormal,
  );
  const userOrdersPrintingNormal = OutletStore.useState(
    s => s.userOrdersPrintingNormal,
  );
  const userOrdersReservation = OutletStore.useState(
    s => s.userOrdersReservation,
  );
  const userOrdersTableDictReservation = OutletStore.useState(
    s => s.userOrdersTableDictReservation,
  );
  const userOrdersPrintingReservation = OutletStore.useState(
    s => s.userOrdersPrintingReservation,
  );
  const allOutletsUserOrdersLoyaltyDoneRealTime = OutletStore.useState(
    s => s.allOutletsUserOrdersLoyaltyDoneRealTime,
  );
  const allOutletsUserOrdersLoyaltyRealTime = OutletStore.useState(
    s => s.allOutletsUserOrdersLoyaltyRealTime,
  );
  const allOutletsUserOrdersLoyaltyDoneCache = OutletStore.useState(
    s => s.allOutletsUserOrdersLoyaltyDoneCache,
  );
  const allOutletsUserOrdersLoyaltyCache = OutletStore.useState(
    s => s.allOutletsUserOrdersLoyaltyCache,
  );
  const timerUserOrderPrinting = CommonStore.useState(
    s => s.timerUserOrderPrinting,
  );
  const merchantName = MerchantStore.useState(s => s.name);
  const [outletDropdownList, setOutletDropdownList] = useState([]);
  const RNFS = require('@dr.pogodin/react-native-fs');

  /////////////////////////////////////////////////////////////////////////////////////
  // Scanner Functions
  /////////////////////////////////////////////////////////////////////////////////////

  const handleScannerPress = () => {
    setShowScanner(prev => !prev);
  };

  const onScanSuccess = async (scannedBarcode) => {
    console.log('Scanned barcode:', scannedBarcode);
    setShowScanner(false);

    try {
      // 在 outletItems 中寻找匹配的 barcode
      const foundItem = outletItems.find(item => {
        // 支持两种 barcode 格式
        if (item.bc === scannedBarcode) {
          return true;
        }
        return false;
      });

      if (foundItem) {
        // Scan successful, show success alert
        Alert.alert(
          'Scan Successful',
          `Found item: ${foundItem.name}`,
          [
            {
              text: 'OK',
              onPress: () => {
                // Add to user's collection
                addToUserCollection(foundItem, scannedBarcode);
              }
            }
          ]
        );
      } else {
        // No matching item found
        Alert.alert(
          'Scan Error',
          'No matching item found',
          [{ text: 'OK' }]
        );
      }
    } catch (error) {
      console.error('Scan handling error:', error);
      Alert.alert(
        'Scan Error',
        'An error occurred while processing the scan result',
        [{ text: 'OK' }]
      );
    }
  };

  const addToUserCollection = async (item, barcode) => {
    try {
      const sbciData = {
        sdt: Date.now(), // timestamp
        sbciId: item.uniqueId, // found uniqueId
        sbc: barcode // scanned barcode
      };

      // Directly update the user document (merge to keep existing data)
      await firestore()
        .collection(Collections.User)
        .doc(firebaseUid)
        .set(
          {
            sbci: firestore.FieldValue.arrayUnion(sbciData)
          },
          { merge: true } // keep existing fields in user doc
        );

      console.log('Successfully added to user collection:', sbciData);
    } catch (error) {
      console.error('Failed to add to collection:', error);
      Alert.alert(
        'Save Failed',
        'Unable to save the scan result to your collection',
        [{ text: 'OK' }]
      );
    }
  };

  /////////////////////////////////////////////////////////////////////////////////////
  // Camera Permission
  /////////////////////////////////////////////////////////////////////////////////////

  useEffect(() => {
    async function requestCameraPermission() {
      if (Platform.OS === "android") {
        try {
          const granted = await PermissionsAndroid.request(
            PermissionsAndroid.PERMISSIONS.CAMERA,
            {
              title: "Camera Permission",
              message: "This app needs access to your camera to scan barcodes.",
              buttonPositive: "OK",
            }
          );

          if (granted !== PermissionsAndroid.RESULTS.GRANTED) {
            Alert.alert("Camera permission denied. Scanning will not work.");
          }
        } catch (err) {
          console.warn(err);
        }
      }
      // On iOS, system asks automatically because of Info.plist
    }

    requestCameraPermission();
  }, []);

  /////////////////////////////////////////////////////////////////////////////////////

  useEffect(() => {
    console.log('useEffect - Dashboard - 1');

    setAllOutlets(
      allOutletsRaw.filter(outlet => {
        if (outlet.uniqueId === currOutletId || isMasterAccount) {
          return true;
        } else {
          return false;
        }
      }),
    );
  }, [allOutletsRaw, currOutletId, isMasterAccount]);

  useEffect(() => {
    setOutletDropdownList(
      allOutlets.map(item => ({
        label: item.name,
        value: item.uniqueId,
      })),
    );
    if (selectedOutletId === '' && allOutlets.length > 0 && currOutletId) {
      setSelectedOutletId(currOutletId);

      // setSelectedOutletList([currOutletId]);
      CommonStore.update(s => {
        s.reportOutletIdList = [currOutletId];
      });
    }
  }, [allOutlets, currOutletId]);

  useEffect(() => {
    if (currOutletId && selectedOutletList.length === 0) {
      CommonStore.update(s => {
        s.reportOutletIdList = [currOutletId];
      });

      setTimeout(() => {
        CommonStore.update(s => {
          s.reportOutletIdList = [currOutletId];
        });
      }, 5000);
    }
  }, [currOutletId]);

  /////////////////////////////////////////////////////////////////////////////////////

  useEffect(() => {
    global.detectSunmiPrintersInterval = setTimeout(() => {
      console.log('on detectOwnSunmiPrinter');

      detectOwnSunmiPrinter();
    }, (global.currOutlet && typeof global.currOutlet.dspi === 'number' ? currOutlet.dspi : 1) * 15000);

    global.detectIminPrintersInterval = setTimeout(() => {
      console.log('on detectOwnIminPrinter');

      detectOwnIminPrinter();
    }, (global.currOutlet && typeof global.currOutlet.dipi === 'number' ? currOutlet.dipi : 1) * 15000);

    global.navigationObj = navigation;

    // return () => {
    //   clearTimeout(global.detectSunmiPrintersInterval);
    //   clearTimeout(global.detectIminPrintersInterval);
    // };

    ///////////////////////////////////

    // 2025-02-03 - log file here

    setTimeout(() => {
      logToFile('[ds] landed');
    }, 15000);

    ///////////////////////////////////
  }, []);

  /////////////////////////////////////////////////////////////////////////////////////

  useEffect(() => {
    console.log('useEffect - Dashboard - 2');

    global.ptDateTimestamp = Date.now();
  }, [historyStartDate, historyEndDate]);

  /////////////////////////////////////////////////////////////////////////////////////

  // const iconWifiOpacity = useSharedValue(0);
  // const animationIconWifi = new Value(0);

  // useEffect(() => {
  //   const animation = () => {
  //     iconWifiOpacity.value = withTiming(1, {
  //       duration: 1000,
  //       easing: Easing.inOut(Easing.ease),
  //     }, () => {
  //       iconWifiOpacity.value = withTiming(0, {
  //         duration: 1000,
  //         easing: Easing.inOut(Easing.ease),
  //       }, animation);
  //     });
  //   };

  //   animation();
  // }, []);

  const iconWifiOpacity = useRef(new Animated.Value(0)).current;

  const startAnimation = () => {
    if (!networkNotStable) {
      try {
        iconWifiOpacity.stopAnimation();
      } catch (ex) {
        console.error(ex);
      }
    } else {
      try {
        iconWifiOpacity.stopAnimation();
      } catch (ex) {
        console.error(ex);
      }

      Animated.sequence([
        Animated.timing(iconWifiOpacity, {
          toValue: 1,
          duration: 1000, // Adjust the duration as needed
          useNativeDriver: true,
        }),
        Animated.timing(iconWifiOpacity, {
          toValue: 0,
          duration: 1000, // Adjust the duration as needed
          useNativeDriver: true,
        }),
      ]).start(() => {
        startAnimation();
      });
    }
  };

  // Interpolate the node from 0 to 1 without clamping
  // const opacityIconWifi = mix(animationIconWifi, 0.1, 1);

  useEffect(() => {
    console.log('useEffect - Dashboard - 3');

    startAnimation();
  }, [networkNotStable]);

  useEffect(() => {
    console.log('useEffect - Dashboard - 3');

    if (
      netInfo.isInternetReachable === true ||
      netInfo.isInternetReachable === null
    ) {
      CommonStore.update(s => {
        s.networkNotStable = false;
      });
    } else {
      CommonStore.update(s => {
        s.networkNotStable = true;
      });
    }
  }, [netInfo]);

  /////////////////////////////////////////////////////////////////////////////////////

  // 2023-05-10 - Store device info

  useEffect(() => {
    console.log('useEffect - Dashboard - 5');

    if (!supportCodeData && merchantId && currOutletId && firebaseUid) {
      setTimeout(() => {
        if (global.tokenFcm || tokenFcm || tokenDevice) {
          var deviceInfoTemp = {};

          DeviceInfo.getBaseOs().then(baseOs => {
            // "Windows", "Android" etc

            DeviceInfo.getDeviceName().then(async deviceName => {
              // iOS: "Becca's iPhone 6"
              // Android: ?
              // Windows: ?

              var tokenFcmToUsed = global.tokenFcm ? global.tokenFcm : tokenFcm;

              deviceInfoTemp.duid = await getDeviceUniqueId();
              deviceInfoTemp.uid = tokenFcmToUsed
                ? tokenFcmToUsed
                : tokenDevice;
              deviceInfoTemp.dt = Date.now();
              deviceInfoTemp.dtf = moment().format('YYYY-MM-DD HH:mm A');
              deviceInfoTemp.dn = deviceName ? deviceName : '';
              deviceInfoTemp.did = DeviceInfo.getDeviceId();
              deviceInfoTemp.dm = DeviceInfo.getModel();
              deviceInfoTemp.os = baseOs ? baseOs : '';
              deviceInfoTemp.ver = DeviceInfo.getVersion();

              deviceInfoTemp.udiD = global.udiData;

              uploadDeviceInfo(deviceInfoTemp);
            });
          });
        }
      }, 30000);
    }
  }, [
    supportCodeData,
    firebaseUid,
    currOutletId,
    merchantId,
    tokenFcm,
    tokenDevice,
  ]);

  const uploadDeviceInfo = async deviceInfoParam => {
    var body = {
      deviceInfo: deviceInfoParam,

      merchantId,
      outletId: currOutletId,
      userId: firebaseUid,
    };

    ApiClient.POST(API.uploadDeviceInfo, body).then(result => {
      // console.log('updated token fcm');
    });
  };

  /////////////////////////////////////////////////////////////////////////////////////7

  // useEffect(() => {
  //     console.log('useEffect - Dashboard - 6');

  //     if (isAlphaUser || true) {
  //         if (global.privileges && global.privileges.length > 0 &&
  //             privileges.length === 0) {
  //             // read local one (fast start-up)

  //             if (global.privileges.includes('OPERATION')) {
  //                 if (isOutletDisplay()) {
  //                     navigation.navigate('Table');
  //                 }
  //                 else {
  //                     navigation.navigate('MenuOrderingScreen');

  //                     CommonStore.update((s) => {
  //                         s.currPage = 'MenuOrderingScreen';
  //                         s.currPageStack = [...currPageStack, 'MenuOrderingScreen'];
  //                     });

  //                     if (expandTab !== EXPAND_TAB_TYPE.OPERATION) {
  //                         CommonStore.update((s) => {
  //                             s.expandTab = EXPAND_TAB_TYPE.OPERATION;
  //                         });
  //                     }
  //                 }
  //             }
  //             else if (global.privileges.includes('KDS')) {
  //                 navigation.navigate('Kitchen');

  //                 CommonStore.update((s) => {
  //                     s.currPage = 'Kitchen';
  //                     s.currPageStack = [...currPageStack, 'Kitchen'];
  //                 });

  //                 if (expandTab !== EXPAND_TAB_TYPE.OPERATION) {
  //                     CommonStore.update((s) => {
  //                         s.expandTab = EXPAND_TAB_TYPE.OPERATION;
  //                     });
  //                 }
  //             }
  //         }

  //         if (privileges.includes('OPERATION')) {
  //             if (isOutletDisplay()) {
  //                 navigation.navigate('Table');
  //             }
  //             else {
  //                 navigation.navigate('MenuOrderingScreen');

  //                 CommonStore.update((s) => {
  //                     s.currPage = 'MenuOrderingScreen';
  //                     s.currPageStack = [...currPageStack, 'MenuOrderingScreen'];
  //                 });

  //                 if (expandTab !== EXPAND_TAB_TYPE.OPERATION) {
  //                     CommonStore.update((s) => {
  //                         s.expandTab = EXPAND_TAB_TYPE.OPERATION;
  //                     });
  //                 }
  //             }
  //         }
  //         else if (privileges.includes('KDS')) {
  //             navigation.navigate('Kitchen');

  //             CommonStore.update((s) => {
  //                 s.currPage = 'Kitchen';
  //                 s.currPageStack = [...currPageStack, 'Kitchen'];
  //             });

  //             if (expandTab !== EXPAND_TAB_TYPE.OPERATION) {
  //                 CommonStore.update((s) => {
  //                     s.expandTab = EXPAND_TAB_TYPE.OPERATION;
  //                 });
  //             }
  //         }
  //     }
  // }, [
  //     // isAlphaUser,

  //     privileges,
  // ]);

  /////////////////////////////////////////////////////////////

  // const role = UserStore.useState((s) => s.role);
  const pinNo = UserStore.useState(s => s.pinNo);

  useEffect(() => {
    console.log('useEffect - Dashboard - 7');

    const useEffectCallback = async () => {
      // admin full access

      // const enteredPinNo = await AsyncStorage.getItem('enteredPinNo');
      // const enteredPinNo = storageMMKV.getString('enteredPinNo');
      const enteredPinNo = await global.watermelonDBDatabase.localStorage.get(
        'enteredPinNo',
      );

      if (role === ROLE_TYPE.ADMIN && pinNo === enteredPinNo) {
        setPrivileges([
          'EMPLOYEES',
          'OPERATION',
          'PRODUCT',
          'INVENTORY',
          'INVENTORY_COMPOSITE',
          'DOCKET',
          'VOUCHER',
          'PROMOTION',
          'CRM',
          'LOYALTY',
          'TRANSACTIONS',
          'REPORT',
          'RESERVATIONS',

          // for action
          'REFUND_ORDER',

          'SETTINGS',

          'QUEUE',

          'OPEN_CASH_DRAWER',

          'KDS',

          'UPSELLING',

          // for Kitchen

          'REJECT_ITEM',
          'CANCEL_ORDER',
          //'REFUND_tORDER',

          'MANUAL_DISCOUNT',
        ]);
      } else {
        setPrivileges(privileges_state || []);
      }
    };

    useEffectCallback();
  }, [role, privileges_state, pinNo]);

  //////////////////////////////////////////////////////////////

  // for new printing methods

  const [tokenFcm, setTokenFcm] = useState('');
  const [tokenDevice, setTokenDevice] = useState('');

  const [printedOrderIdList, setPrintedOrderIdList] = useState([]);

  /////////////////////////////////////////////////////////////////////////////////////

  // useEffect(() => {
  //   console.log('useEffect - Dashboard - 8');

  //   InteractionManager.runAfterInteractions(() => {
  //     setChartDropdownValueXList(
  //       CHART_X_AXIS_DROPDOWN_LIST[
  //         CHART_TYPE.DASHBOARD_LINE_CHART_SALES
  //       ].filter(xAxisType => {
  //         if (salesLineChartPeriod === CHART_PERIOD.THIS_WEEK) {
  //           if (
  //             xAxisType.value === CHART_X_AXIS_TYPE.WEEK ||
  //             xAxisType.value === CHART_X_AXIS_TYPE.MONTH
  //           ) {
  //             return false;
  //           } else {
  //             return true;
  //           }
  //         } else if (salesLineChartPeriod === CHART_PERIOD.THIS_MONTH) {
  //           if (xAxisType.value === CHART_X_AXIS_TYPE.MONTH) {
  //             return false;
  //           } else {
  //             return true;
  //           }
  //         } else if (salesLineChartPeriod === CHART_PERIOD.THREE_MONTHS) {
  //           if (
  //             xAxisType.value === CHART_X_AXIS_TYPE.HOURLY &&
  //             salesLineChartPeriod === CHART_PERIOD.TODAY
  //           ) {
  //             return false;
  //           }
  //           return true;
  //         } else if (salesLineChartPeriod === CHART_PERIOD.SIX_MONTHS) {
  //           if (
  //             xAxisType.value === CHART_X_AXIS_TYPE.DAY ||
  //             (xAxisType.value === CHART_X_AXIS_TYPE.HOURLY &&
  //               salesLineChartPeriod === CHART_PERIOD.TODAY)
  //           ) {
  //             return false;
  //           } else {
  //             return true;
  //           }
  //         } else if (salesLineChartPeriod === CHART_PERIOD.THIS_YEAR) {
  //           if (
  //             xAxisType.value === CHART_X_AXIS_TYPE.DAY ||
  //             (xAxisType.value === CHART_X_AXIS_TYPE.HOURLY &&
  //               salesLineChartPeriod === CHART_PERIOD.TODAY)
  //           ) {
  //             return false;
  //           } else {
  //             return true;
  //           }
  //         } else if (salesLineChartPeriod === CHART_PERIOD.YTD) {
  //           if (
  //             xAxisType.value === CHART_X_AXIS_TYPE.DAY ||
  //             (xAxisType.value === CHART_X_AXIS_TYPE.HOURLY &&
  //               salesLineChartPeriod === CHART_PERIOD.TODAY)
  //           ) {
  //             return false;
  //           } else {
  //             return true;
  //           }
  //         }
  //       }),
  //     );
  //   });
  // }, [salesLineChartPeriod]);

  /////////////////////////////////////////////////////////////////////////////////////

  // useEffect(() => {
  //   // to prevent the app 'hang' if request failed or error, will change to apiClient/apiReplacer callback trigger in future

  //   if (isLoading) {
  //     setTimeout(() => {
  //       CommonStore.update(s => {
  //         s.isLoading = false;
  //       });
  //     }, 5000);
  //   }
  // }, [isLoading]);

  useEffect(() => {
    console.log('useEffect - Dashboard - 9');

    // to prevent the app 'hang' if request failed or error, will change to apiClient/apiReplacer callback trigger in future

    setTimeout(() => {
      CommonStore.update(s => {
        s.isLoading = false;
      });
    }, 5000);
  }, []);

  /////////////////////////////////////////////////////////////////////////////////////

  // useEffect(() => {
  //   NetPrinter.init();

  //   // CommonStore.update(s => {
  //   //   s.clearDashboardDataFunc = () => {
  //   //     setSalesLineChart({});
  //   //     setExpandLineSelection(false);
  //   //   };
  //   // });
  // }, []);

  // console.log('sidebar width');
  // console.log(Styles.sideBarWidth);

  useEffect(() => {
    console.log('useEffect - Dashboard - 10');

    InteractionManager.runAfterInteractions(() => {
      //// console.log(moment().utc().tz(TIMEZONE.KUALA_LUMPUR).format());
      //// console.log('haloooo');
      //// console.log(moment(moment(Date.now)));
      // setTargetOutletDropdownList(allOutlets.map(outlet => ({ label: outlet.name, value: outlet.uniqueId })));

      // // console.log('targetOutletDropdownList');
      // // console.log(targetOutletDropdownList);

      // if (allOutlets.length > 0) {
      //     setSelectedTargetOutletId(currOutletId);
      // }

      // useEffect(() => {
      //     setMessages()
      // }, []);

      // const onSend = useCallback((messages = []) => {
      //     setMessages(previousMessages => GiftedChat.append(previousMessages, messages))
      // }, []);

      ////////////////////////////////////////////////////////

      var targetOutletDropdownListTemp = allOutlets
        .filter(outlet => {
          if (currOutletId === outlet.uniqueId) {
            return true;
          }

          if (isMasterAccount) {
            return true;
          }
        })
        .map(outlet => ({
          label: sliceUnicodeStringV2WithDots(outlet.name, 20),
          value: outlet.uniqueId,
        }));

      // setExpandLineSelection(false);

      CommonStore.update(s => {
        s.outletSelectDropdownView = () => {
          return (
            <View
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                // borderWidth:1,
              }}>
              {networkNotStable ? (
                <Animated.View
                  style={{
                    // width: 200,
                    // height: 200,
                    // backgroundColor: 'red',
                    opacity: iconWifiOpacity,
                  }}>
                  <TouchableOpacity
                    style={{
                      // backgroundColor: 'red',
                      marginRight: 15,
                    }}
                    onPress={async () => {
                      // await openCashDrawer();

                      Alert.alert(
                        'Info',
                        'Network is not stable or unavailable right now, please check your internet router again.',
                      );
                    }}>
                    <MaterialCommunityIcons
                      name="wifi-off"
                      size={switchMerchant ? 20 : 25}
                      style={{
                        // color: Colors.lightRed,
                        color: '#eb676a',
                      }}
                    />
                  </TouchableOpacity>
                </Animated.View>
              ) : (
                <></>
              )}

              {
                // (
                // (currOutlet && currOutlet.privileges &&
                //   currOutlet.privileges.includes(PRIVILEGES_NAME.OPEN_CASH_DRAWER))
                // && privileges && privileges.includes(PRIVILEGES_NAME.OPEN_CASH_DRAWER))
                sunmiSerialNo ? (
                  <TouchableOpacity
                    style={{
                      // backgroundColor: 'red',
                      marginRight: 15,
                    }}
                    onPress={async () => {
                      // if (
                      //   (currOutlet && currOutlet.privileges &&
                      //     currOutlet.privileges.includes(PRIVILEGES_NAME.OPEN_CASH_DRAWER))
                      //   && privileges && privileges.includes(PRIVILEGES_NAME.OPEN_CASH_DRAWER)) {
                      //   await openCashDrawer();
                      // }
                      // else {
                      //   global.pinUnlockCallback = async () => {
                      //     await openCashDrawer();
                      //   };

                      //   CommonStore.update(s => {
                      //     s.pinUnlockType = PRIVILEGES_NAME.OPEN_CASH_DRAWER;
                      //     s.showPinUnlockModal = true;
                      //   });
                      // }

                      openSunmiScanner();
                    }}>
                    <AntDesign
                      name="scan1"
                      size={switchMerchant ? 20 : 25}
                      style={{ color: Colors.whiteColor }}
                    />
                  </TouchableOpacity>
                ) : (
                  <></>
                )
              }

              {
                // (
                // (currOutlet && currOutlet.privileges &&
                //   currOutlet.privileges.includes(PRIVILEGES_NAME.OPEN_CASH_DRAWER))
                // && privileges && privileges.includes(PRIVILEGES_NAME.OPEN_CASH_DRAWER))
                true ? (
                  <TouchableOpacity
                    style={{
                      // backgroundColor: 'red',
                      marginRight: 15,
                    }}
                    onPress={async () => {
                      if (
                        currOutlet &&
                        currOutlet.privileges &&
                        currOutlet.privileges.includes(
                          PRIVILEGES_NAME.OPEN_CASH_DRAWER,
                        ) &&
                        privileges &&
                        privileges.includes(PRIVILEGES_NAME.OPEN_CASH_DRAWER)
                      ) {
                        await openCashDrawer();
                      } else {
                        global.pinUnlockCallback = async () => {
                          await openCashDrawer();
                        };

                        CommonStore.update(s => {
                          s.pinUnlockType = PRIVILEGES_NAME.OPEN_CASH_DRAWER;
                          s.showPinUnlockModal = true;
                        });
                      }
                    }}>
                    <MaterialCommunityIcons
                      name="cash-register"
                      size={switchMerchant ? 20 : 25}
                      style={{ color: Colors.primaryColor }}
                    />
                  </TouchableOpacity>
                ) : (
                  <></>
                )
              }

              {/* {console.log('@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@')}
                  {console.log(currOutletId)}
                  {console.log(allOutlets)}
                  {console.log('@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@')} */}

              {currOutletId.length > 0 &&
                allOutlets.find(item => item.uniqueId === currOutletId) ? (
                // <DropDownPicker
                //   disabled={isLoading}
                //   containerStyle={[
                //     {
                //       height: 40,
                //       paddingVertical: 0,
                //       minWidth: windowWidth * 0.15,
                //       maxWidth: windowWidth * 0.3,
                //     },
                //     switchMerchant
                //       ? {
                //         height: '100%',
                //         paddingVertical: 0,
                //         // borderWidth: 1
                //         // width: '100%'
                //       }
                //       : {},
                //   ]}
                //   style={{
                //     backgroundColor: '#345F4A',
                //     borderWidth: 0,
                //     paddingVertical: 0,
                //     minWidth: windowWidth * 0.15,
                //     maxWidth: windowWidth * 0.3,
                //   }}
                //   arrowSize={switchMerchant ? 15 : 20}
                //   arrowColor={'#ffffff'}
                //   arrowStyle={{ fontWeight: 'bold', marginLeft: 5, height: '700%' }}
                //   //itemStyle={{ justifyContent: 'flex-start' }}
                //   placeholder="Choose outlet"
                //   placeholderStyle={{ color: Colors.whiteColor, fontWeight: '600' }}
                //   labelStyle={{ color: Colors.whiteColor, fontWeight: '600' }}
                //   dropDownStyle={{
                //     backgroundColor: '#345F4A',
                //     borderRadius: 5,
                //     zIndex: 1,
                //     minWidth: windowWidth * 0.15,
                //     maxWidth: windowWidth * 0.3,
                //   }}
                //   items={targetOutletDropdownListTemp}
                //   onChangeItem={(item) => {
                //     const currOutletTemp = allOutlets.find(
                //       (outlet) => outlet.uniqueId === item.value,
                //     ) || {
                //       uniqueId: '',
                //       privileges: [],
                //     };

                //     MerchantStore.update((s) => {
                //       s.currOutletId = item.value;
                //       s.currOutlet = currOutletTemp;
                //     });

                //     global.outletKdEventTypes = currOutletTemp.kdPrintEventTypes !== undefined ? currOutletTemp.kdPrintEventTypes : [
                //       KD_PRINT_EVENT_TYPE.DELIVER,
                //       KD_PRINT_EVENT_TYPE.REJECT,
                //       KD_PRINT_EVENT_TYPE.UNDO_DELIVER,
                //       KD_PRINT_EVENT_TYPE.UNDO_REJECT,
                //       KD_PRINT_EVENT_TYPE.SWITCH_TABLE,
                //     ];

                //     global.outletKdVariation = currOutletTemp.kdPrintVariation !== undefined ? currOutletTemp.kdPrintVariation : KD_PRINT_VARIATION.SUMMARY;

                //     CommonStore.update(s => {
                //       s.shiftClosedModal = false;
                //     });

                //     //////////////////////////////////////////

                //     // 2022-12-22 - Go to first page after switch outlet

                //     if (global.privileges.includes('OPERATION')) {
                //       navigation.navigate('MenuOrderingScreen');

                //       CommonStore.update((s) => {
                //         s.currPage = 'MenuOrderingScreen';
                //         s.currPageStack = [...currPageStack, 'MenuOrderingScreen'];
                //       });

                //       if (expandTab !== EXPAND_TAB_TYPE.OPERATION) {
                //         CommonStore.update((s) => {
                //           s.expandTab = EXPAND_TAB_TYPE.OPERATION;
                //         });
                //       }
                //     }
                //     else if (global.privileges.includes('KDS')) {
                //       navigation.navigate('Kitchen');

                //       CommonStore.update((s) => {
                //         s.currPage = 'Kitchen';
                //         s.currPageStack = [...currPageStack, 'Kitchen'];
                //       });

                //       if (expandTab !== EXPAND_TAB_TYPE.OPERATION) {
                //         CommonStore.update((s) => {
                //           s.expandTab = EXPAND_TAB_TYPE.OPERATION;
                //         });
                //       }
                //     }

                //     //////////////////////////////////////////
                //   }}
                //   defaultValue={currOutletId}
                // />

                <View
                  style={{
                    backgroundColor: '#345F4A',
                    borderWidth: 0,
                    paddingVertical: 0,
                    minWidth: windowWidth * 0.5,
                    maxWidth: windowWidth * 0.7,

                    // width: Platform.OS === 'ios' ? 65 : '13%', //65,
                    height: switchMerchant ? 20 : 35,
                    backgroundColor: Colors.whiteColor,
                    borderRadius: 10,
                    justifyContent: 'center',
                    paddingHorizontal: Platform.OS === 'ios' ? 0 : 0,
                    //paddingLeft:switchMerchant ? '4.5%': Platform.OS === 'ios' ? '2%' : '4%',
                    // paddingTop: '-60%',
                    borderWidth: 1,
                    borderColor: '#E5E5E5',
                    marginRight: '1%',
                  }}>
                  {Platform.OS === 'ios' ? (
                    // true
                    <RNPickerSelect
                      disabled={isLoading}
                      placeholder={{}}
                      useNativeAndroidPickerStyle={false}
                      style={{
                        inputIOS: {
                          fontSize: switchMerchant ? 10 : 14,
                          fontFamily: 'NunitoSans-Regular',
                          textAlign: 'center',
                        },
                        inputAndroid: {
                          fontSize: switchMerchant ? 10 : 14,
                          fontFamily: 'NunitoSans-Regular',
                          justifyContent: 'center',
                          textAlign: 'center',
                          height: 40,
                          color: 'black',
                        },
                        inputAndroidContainer: { width: '100%' },
                        //backgroundColor: 'red',
                        height: 35,

                        chevronContainer: {
                          display: 'none',
                        },
                        chevronDown: {
                          display: 'none',
                        },
                        chevronUp: {
                          display: 'none',
                        },
                      }}
                      items={targetOutletDropdownListTemp}
                      value={tempOutletId ? tempOutletId : currOutletId}
                      onValueChange={value => {
                        if (value !== tempOutletId) {
                          setTempOutletId(value);
                        }
                      }}
                      onDonePress={() => {
                        if (tempOutletId !== '') {
                          const currOutletTemp = allOutlets.find(
                            outlet => outlet.uniqueId === tempOutletId,
                          ) || {
                            uniqueId: '',
                            privileges: [],
                          };

                          MerchantStore.update(s => {
                            s.currOutletId = tempOutletId;
                            s.currOutlet = currOutletTemp;
                          });

                          global.outletKdEventTypes =
                            currOutletTemp.kdPrintEventTypes !== undefined
                              ? currOutletTemp.kdPrintEventTypes
                              : [
                                KD_PRINT_EVENT_TYPE.DELIVER,
                                KD_PRINT_EVENT_TYPE.REJECT,
                                KD_PRINT_EVENT_TYPE.UNDO_DELIVER,
                                KD_PRINT_EVENT_TYPE.UNDO_REJECT,
                                KD_PRINT_EVENT_TYPE.SWITCH_TABLE,
                              ];

                          global.outletKdVariation =
                            currOutletTemp.kdPrintVariation !== undefined
                              ? currOutletTemp.kdPrintVariation
                              : KD_PRINT_VARIATION.SUMMARY;

                          CommonStore.update(s => {
                            s.shiftClosedModal = false;
                          });

                          // Navigate based on privileges
                          if (global.privileges.includes('OPERATION')) {
                            if (isOutletDisplay()) {
                              navigation.navigate('Table');
                            } else {
                              navigation.navigate('MenuOrderingScreen');
                              CommonStore.update(s => {
                                s.currPage = 'MenuOrderingScreen';
                                s.currPageStack = [
                                  ...currPageStack,
                                  'MenuOrderingScreen',
                                ];
                              });
                              if (expandTab !== EXPAND_TAB_TYPE.OPERATION) {
                                CommonStore.update(s => {
                                  s.expandTab = EXPAND_TAB_TYPE.OPERATION;
                                });
                              }
                            }
                          } else if (global.privileges.includes('KDS')) {
                            navigation.navigate('Kitchen');
                            CommonStore.update(s => {
                              s.currPage = 'Kitchen';
                              s.currPageStack = [...currPageStack, 'Kitchen'];
                            });
                            if (expandTab !== EXPAND_TAB_TYPE.OPERATION) {
                              CommonStore.update(s => {
                                s.expandTab = EXPAND_TAB_TYPE.OPERATION;
                              });
                            }
                          }
                        }
                      }}
                    />
                  ) : (
                    <RNPickerSelect
                      disabled={isLoading}
                      placeholder={{}}
                      useNativeAndroidPickerStyle={false}
                      style={{
                        inputIOS: {
                          fontSize: switchMerchant ? 10 : 14,
                          fontFamily: 'NunitoSans-Regular',
                          textAlign: 'center',
                        },
                        inputAndroid: {
                          fontSize: switchMerchant ? 10 : 14,
                          fontFamily: 'NunitoSans-Regular',
                          justifyContent: 'center',
                          textAlign: 'center',
                          height: 40,
                          color: 'black',
                        },
                        inputAndroidContainer: { width: '100%' },
                        //backgroundColor: 'red',
                        height: 35,

                        chevronContainer: {
                          display: 'none',
                        },
                        chevronDown: {
                          display: 'none',
                        },
                        chevronUp: {
                          display: 'none',
                        },
                      }}
                      items={targetOutletDropdownListTemp}
                      value={currOutletId}
                      onValueChange={(value, text) => {
                        const currOutletTemp = allOutlets.find(
                          outlet => outlet.uniqueId === value,
                        ) || {
                          uniqueId: '',
                          privileges: [],
                        };

                        MerchantStore.update(s => {
                          s.currOutletId = value;
                          s.currOutlet = currOutletTemp;
                        });

                        global.outletKdEventTypes =
                          currOutletTemp.kdPrintEventTypes !== undefined
                            ? currOutletTemp.kdPrintEventTypes
                            : [
                              KD_PRINT_EVENT_TYPE.DELIVER,
                              KD_PRINT_EVENT_TYPE.REJECT,
                              KD_PRINT_EVENT_TYPE.UNDO_DELIVER,
                              KD_PRINT_EVENT_TYPE.UNDO_REJECT,
                              KD_PRINT_EVENT_TYPE.SWITCH_TABLE,
                            ];

                        global.outletKdVariation =
                          currOutletTemp.kdPrintVariation !== undefined
                            ? currOutletTemp.kdPrintVariation
                            : KD_PRINT_VARIATION.SUMMARY;

                        CommonStore.update(s => {
                          s.shiftClosedModal = false;
                        });

                        //////////////////////////////////////////

                        // 2022-12-22 - Go to first page after switch outlet

                        if (global.privileges.includes('OPERATION')) {
                          if (isOutletDisplay()) {
                            navigation.navigate('Table');
                          } else {
                            navigation.navigate('MenuOrderingScreen');

                            CommonStore.update(s => {
                              s.currPage = 'MenuOrderingScreen';
                              s.currPageStack = [
                                ...currPageStack,
                                'MenuOrderingScreen',
                              ];
                            });

                            if (expandTab !== EXPAND_TAB_TYPE.OPERATION) {
                              CommonStore.update(s => {
                                s.expandTab = EXPAND_TAB_TYPE.OPERATION;
                              });
                            }
                          }
                        } else if (global.privileges.includes('KDS')) {
                          navigation.navigate('Kitchen');

                          CommonStore.update(s => {
                            s.currPage = 'Kitchen';
                            s.currPageStack = [...currPageStack, 'Kitchen'];
                          });

                          if (expandTab !== EXPAND_TAB_TYPE.OPERATION) {
                            CommonStore.update(s => {
                              s.expandTab = EXPAND_TAB_TYPE.OPERATION;
                            });
                          }
                        }
                      }}
                    />
                  )}
                </View>
              ) : (
                <ActivityIndicator size={'small'} color={Colors.whiteColor} />
              )}
            </View>
          );
        };
      });
    });
  }, [
    allOutlets.length,
    currOutletId,
    isLoading,
    currOutletShiftStatus,
    currOutlet.updatedAt,
    privileges,
    isMasterAccount,
    networkNotStable,
    iconWifiOpacity,
    sunmiSerialNo,
    tempOutletId,
  ]);

  /////////////////////////////////////////////////////////////////////////////////////

  // for printing orders

  // const userOrdersPrinting = OutletStore.useState(s => s.userOrdersPrinting.filter(record => {
  //   return record.orderStatus === USER_ORDER_STATUS.ORDER_RECEIVED ||
  //     record.orderStatus === USER_ORDER_STATUS.ORDER_AUTHORIZED;
  // }));

  const userOrdersPrinting = OutletStore.useState(s => s.userOrdersPrinting);

  useEffect(() => {
    console.log('useEffect - Dashboard - 11');

    // for printer rescheduling

    if (
      !currOutlet.reprintOff &&
      (currOutlet.reprintI ? currOutlet.reprintI : 1) &&
      !global.supportCodeData &&
      !global.outletToggleDisableAutoPrint
    ) {
      if (global.reprintInterval !== null) {
        clearInterval(global.reprintInterval);
      }

      global.reprintInterval = setInterval(() => {
        if (
          global.userOrderMetadataList.length > 0 &&
          (currOutlet.reprintForceCheck || !global.isPrintingNow)
        ) {
          // only proceed if userOrderMetadataList.length > 0
          // and if not printing now

          global.isCheckingInterruptedTasksNow = true;

          let printerTasks = global.printerObjList.reduce((prev, curr) => {
            if (curr.tasks.length > 0) {
              return prev.concat(curr.tasks);
            } else {
              return prev;
            }
          }, []);

          let toUpdateTaskNum = 0;
          const userOrderMetadataListBatch = firestore().batch();

          for (let i = 0; i < global.userOrderMetadataList.length; i++) {
            if (
              global.userOrderMetadataList[i].pTasks > 0 &&
              global.userOrderMetadataList[i].pTasksPopup === '0'
            ) {
              // seems like this order haven't fully done its printing tasks

              logToFile(
                `[pr] still got tasks haven't fully completed > pTasks = ${global.userOrderMetadataList[i].pTasks} | pTasksPopup = ${global.userOrderMetadataList[i].pTasksPopup}`,
              );
              logToFile(
                `[pr] #${global.userOrderMetadataList[i].orderType ===
                  ORDER_TYPE.DINEIN
                  ? ''
                  : 'T'
                }${global.userOrderMetadataList[i].orderId} | ${global.userOrderMetadataList[i].uniqueId
                }`,
              );

              if (
                printerTasks.find(
                  findTask =>
                    findTask.orderUniqueId ===
                    global.userOrderMetadataList[i].uniqueId,
                )
              ) {
                // still in active tasks

                logToFile('is active tasks');

                try {
                  if (global.currOutlet && global.currOutlet.alertPrtActive) {
                    logToFile('active task info');

                    for (
                      let findIndex = 0;
                      findIndex < printerTasks.length;
                      findIndex++
                    ) {
                      let taskChecking = printerTasks[findIndex];

                      logToFile(`${taskChecking.taskCreatedAt}`);
                      logToFile(`${taskChecking.taskId}`);
                      logToFile(`${taskChecking.taskType}`);
                    }

                    logToFile(
                      `[pr dashboard] ${global.isCheckingInterruptedTasksNow} | ${global.printerPendingTasksNum} | ${global.isPrintingNow} | ${global.printerPendingTasksPrioritizedNum}`,
                    );
                    logToFile(
                      `[pr dashboard] ${global.currOutlet.printIgnoreSnapshot} | ${global.isSnapshotChanging} | ${global.isAuthorizingTakeawayOrders} | ${global.isReconnectingToTimeoutPrinter} | ${global.uanI}`,
                    );

                    logToFile(`[pr dashboard] ${printerTasks.length}`);
                    if (
                      global.printerPendingTasksNum !== printerTasks.length &&
                      global.currOutlet.prtPTNAlign // printer pending tasks alignment
                    ) {
                      global.printerPendingTasksNum = printerTasks.length; // increase here, most probably due to race condition issues
                    }

                    if (
                      global.currOutlet.prtPTNAlign2 // printer pending tasks alignment
                    ) {
                      global.isPrintingNow = false; // increase here, most probably due to race condition issues
                    }

                    if (
                      global.currOutlet.prtPTNAlign3 // printer pending tasks alignment
                    ) {
                      global.isSnapshotChanging = false; // increase here, most probably due to race condition issues
                    }

                    if (
                      global.currOutlet.prtPTNAlign4 // printer pending tasks alignment
                    ) {
                      global.isAuthorizingTakeawayOrders = false; // increase here, most probably due to race condition issues
                    }

                    if (
                      global.currOutlet.prtPTNAlign5 // printer pending tasks alignment
                    ) {
                      global.isReconnectingToTimeoutPrinter = false; // increase here, most probably due to race condition issues
                    }

                    let message = `The order #${(global.userOrderMetadataList[i].orderType ===
                      ORDER_TYPE.DINEIN
                      ? ''
                      : 'T') + global.userOrderMetadataList[i].orderId
                      } printing tasks are still queued for processing.`;

                    Alert.alert('Info', message);
                  } else {
                    logToFile('active task info [2]');

                    for (
                      let findIndex = 0;
                      findIndex < printerTasks.length;
                      findIndex++
                    ) {
                      let taskChecking = printerTasks[findIndex];

                      logToFile(`${taskChecking.taskCreatedAt}`);
                      logToFile(`${taskChecking.taskId}`);
                      logToFile(`${taskChecking.taskType}`);
                    }

                    logToFile(
                      `[pr dashboard] ${global.isCheckingInterruptedTasksNow} | ${global.printerPendingTasksNum} | ${global.isPrintingNow} | ${global.printerPendingTasksPrioritizedNum}`,
                    );
                    logToFile(
                      `[pr dashboard] ${global.currOutlet.printIgnoreSnapshot} | ${global.isSnapshotChanging} | ${global.isAuthorizingTakeawayOrders} | ${global.isReconnectingToTimeoutPrinter} | ${global.uanI}`,
                    );

                    logToFile(`[pr dashboard] ${printerTasks.length}`);
                    if (
                      global.printerPendingTasksNum !== printerTasks.length &&
                      global.currOutlet.prtPTNAlign // printer pending tasks alignment
                    ) {
                      global.printerPendingTasksNum = printerTasks.length; // increase here, most probably due to race condition issues
                    }

                    if (
                      global.currOutlet.prtPTNAlign2 // printer pending tasks alignment
                    ) {
                      global.isPrintingNow = false; // increase here, most probably due to race condition issues
                    }

                    if (
                      global.currOutlet.prtPTNAlign3 // printer pending tasks alignment
                    ) {
                      global.isSnapshotChanging = false; // increase here, most probably due to race condition issues
                    }

                    if (
                      global.currOutlet.prtPTNAlign4 // printer pending tasks alignment
                    ) {
                      global.isAuthorizingTakeawayOrders = false; // increase here, most probably due to race condition issues
                    }

                    if (
                      global.currOutlet.prtPTNAlign5 // printer pending tasks alignment
                    ) {
                      global.isReconnectingToTimeoutPrinter = false; // increase here, most probably due to race condition issues
                    }
                  }
                } catch (ex) {
                  logToFile(ex);
                }
              } else {
                // no longer in active tasks, show a popup

                logToFile('no longer active tasks, show popup');

                toUpdateTaskNum++;

                showAlertForInterruptedPrinting(
                  global.userOrderMetadataList[i],
                );

                userOrderMetadataListBatch.update(
                  firestore()
                    .collection(Collections.UserOrderMetadataV2)
                    .doc(global.userOrderMetadataList[i].uniqueId),
                  {
                    // no need update pTasks first, might affect the current printing jobs

                    pTasks: 0,

                    pTasksPopup: '1',

                    updatedAt: Date.now(),
                  },
                );

                /////////////////////////////////////////////

                // 2025-01-27 - clear the dict key

                if (
                  global.userOrderMetadataList[i] &&
                  global.userOrderMetadataList[i].ptId
                ) {
                  delete global.printingTaskIdDict[
                    global.userOrderMetadataList[i].ptId
                  ];
                }

                /////////////////////////////////////////////
              }
            } else {
              /////////////////////////////////////////////

              // 2025-01-27 - clear the dict key

              if (
                global.userOrderMetadataList[i] &&
                global.userOrderMetadataList[i].ptId
              ) {
                delete global.printingTaskIdDict[
                  global.userOrderMetadataList[i].ptId
                ];
              }

              /////////////////////////////////////////////
            }
          }

          if (toUpdateTaskNum > 0) {
            logToFile('commit popup tasks');

            userOrderMetadataListBatch.commit();
          }

          // can add a configurable timeout for this

          if (toUpdateTaskNum > 0) {
            setTimeout(() => {
              global.isCheckingInterruptedTasksNow = false;
            }, global.currOutlet.icitnTime * 1000);
          } else {
            global.isCheckingInterruptedTasksNow = false;
          }
        }

        // for (let i = 0; i < global.userOrdersPrinting.length; i++) {
        //   if (global.userOrdersPrinting[i].pTasks > 0 && global.userOrdersPrinting[i].pTasksPopup === '0') {
        //     // seems like this order haven't fully done its printing tasks

        //     logToFile(`[pr] still got tasks haven't fully completed > pTasks = ${global.userOrdersPrinting[i].pTasks} | pTasksPopup = ${global.userOrdersPrinting[i].pTasksPopup}`);
        //     logToFile(`[pr] #${global.userOrdersPrinting[i].orderType === ORDER_TYPE.DINEIN ? '' : 'T'}${global.userOrdersPrinting[i].orderId} | ${global.userOrdersPrinting[i].uniqueId}`);

        //     if (printerTasks.find(findTask => findTask.orderUniqueId === global.userOrdersPrinting[i].uniqueId)) {
        //       // still in active tasks

        //       logToFile('is active tasks');
        //     }
        //     else {
        //       // no longer in active tasks, show a popup

        //       logToFile('no longer active tasks, show popup');

        //       toUpdateTaskNum++;

        //       showAlertForInterruptedPrinting(
        //         global.userOrdersPrinting[i],
        //       );

        //       userOrdersPrintingBatch.update(
        //         firestore().collection(Collections.UserOrder).doc(global.userOrdersPrinting[i].uniqueId),
        //         {
        //           // no need update pTasks first, might affect the current printing jobs

        //           pTasks: 0,

        //           pTasksPopup: '1',
        //         },
        //       );
        //     }
        //   }
        // }
      }, (currOutlet.reprintI ? currOutlet.reprintI : 5) * 60000); // minute -> milliseconds
    }
  }, [currOutlet.uniqueId]);

  // duplicated printing might caused by simultaneous changes of watch states, need to make them synchronous
  // use the useLayoutEffect instead of useEffect?
  // use the waiting queue to block?
  useLayoutEffect(() => {
    // remove async
    logToFile(`useLayoutEffect`);
    logToFile(`${userOrdersPrinting.length} | ${tokenFcm} | ${tokenDevice}`);

    if (
      userOrdersPrinting.length > 0 &&
      (tokenFcm || tokenDevice) &&
      !isOutletDisplay()
    ) {
      // var userOrdersPrintingTemp = [
      //   ...userOrdersPrinting,
      // ];

      logToFile(`useLayoutEffect [1]`);

      if (!global.outletToggleDisableAutoPrint) {
        logToFile(`useLayoutEffect [2]`);

        InteractionManager.runAfterInteractions(async () => {
          ///////////////////////////////////////////////////

          // 2023-05-26 - Extra code for queue waiting

          // await new Promise((resolve) => {
          //   let intervalTimer = setInterval(() => {
          //     console.log('waiting');

          //     if (!global.isCheckingPrintKdOsNow) {
          //       console.log('done waiting');

          //       clearInterval(intervalTimer);
          //       resolve();
          //     }
          //   }, 10); // 100 ms
          // });

          // global.isCheckingPrintKdOsNow = true;

          ///////////////////////////////////////////////////

          logToFile(
            `global.printedOrderListLocal.length: ${global.printedOrderListLocal.length}`,
          );

          var userOrdersPrintingTemp = userOrdersPrinting.filter(order => {
            logToFile(
              `check print order.uniqueId: ${order.uniqueId} | ${order.printedTokenList
                ? order.printedTokenList.includes(
                  tokenFcm ? tokenFcm : tokenDevice,
                )
                : ''
              }`,
            );

            if (
              order.printedTokenList &&
              // (
              //   (tokenFcm && !order.printedTokenList.includes(tokenFcm))
              //   ||
              //   (tokenDevice && !order.printedTokenList.includes(tokenDevice))
              // )
              ((!supportCodeData &&
                // order.printedTokenList.length === 0
                !order.printedTokenList.includes(
                  tokenFcm ? tokenFcm : tokenDevice,
                )) || // only print if empty token
                (supportCodeData &&
                  (order.printedTokenListSupport === undefined ||
                    (order.printedTokenListSupport &&
                      ((tokenFcm &&
                        !order.printedTokenListSupport.includes(tokenFcm)) ||
                        (tokenDevice &&
                          !order.printedTokenListSupport.includes(
                            tokenDevice,
                          ))))))) &&
              !global.printedOrderListLocal.includes(order.uniqueId)
            ) {
              // console.log('order.printedTokenList');
              // console.log(order.printedTokenList);
              // console.log('order.printedTokenListSupport');
              // console.log(order.printedTokenListSupport);
              // console.log('tokenFcm');
              // console.log(tokenFcm);
              // console.log('tokenDevice');
              // console.log(tokenDevice);

              let validStatus = true;

              // 2024-08-16 - authorize dine-in orders support

              if (
                currOutlet.dineInRequiredAuthorization &&
                order.orderType === ORDER_TYPE.DINEIN &&
                (order.orderStatus === USER_ORDER_STATUS.ORDER_RECEIVED ||
                  order.autoAuthorized) // means this order is auto authorized
              ) {
                validStatus = false;
              }

              return validStatus;
            } else {
              return false;
            }
          });

          if (userOrdersPrintingTemp.length > 0) {
            // setPrintedOrderIdList([
            //   ...printedOrderIdList,
            //   userOrdersPrintingTemp.map(order => order.uniqueId),
            // ]);

            // global.isCheckingIncomingOrders = true;

            global.printedOrderListLocal = [
              ...global.printedOrderListLocal,
              ...userOrdersPrintingTemp.map(order => order.uniqueId),
            ];

            InteractionManager.runAfterInteractions(() => {
              printUserOrdersTrigger(userOrdersPrintingTemp);

              if (!supportCodeData) {
                updateUserOrdersTrigger(userOrdersPrintingTemp);
              } else {
                updateUserOrdersTriggerSupport(userOrdersPrintingTemp);
              }

              seatUserOrders(
                userOrdersPrintingTemp.filter(order => order.tableId),
              );
            });

            // setTimeout(() => {
            //   global.isCheckingIncomingOrders = false;
            // }, 500);

            ///////////////////////////////////////////////////////////////////

            // force sequential

            // var isPrintingKDAndOS = await AsyncStorage.getItem('isPrintingKDAndOS');

            // console.log(`[batch] ${userOrdersPrintingTemp[0].finalPrice}: isPrintingKDAndOS checking: ${isPrintingKDAndOS}`);

            // while (isPrintingKDAndOS === '1') {
            //   await waitForSeconds(1); // before print another set

            //   isPrintingKDAndOS = await AsyncStorage.getItem('isPrintingKDAndOS');

            //   console.log(`[batch] ${userOrdersPrintingTemp[0].finalPrice}: isPrintingKDAndOS looping: ${isPrintingKDAndOS}`);
            // }

            // if (isPrintingKDAndOS === '0') {
            //   await AsyncStorage.setItem('isPrintingKDAndOS', '1');

            //   console.log(`[batch] ${userOrdersPrintingTemp[0].finalPrice}: isPrintingKDAndOS now printing: ${isPrintingKDAndOS}`);

            //   printUserOrdersTrigger(userOrdersPrintingTemp);
            // }

            ///////////////////////////////////////////////////////////////////

            // clean up
            // printedOrderListLocal = [];
          }

          // global.isCheckingPrintKdOsNow = false;
        });
      }
    }
  }, [
    userOrdersPrinting, // use length better?
    tokenFcm,
    tokenDevice,

    currOutlet.dineInRequiredAuthorization,
  ]);

  const updateUserOrdersTrigger = async userOrdersPrintingTemp => {
    if (tokenFcm || tokenDevice) {
      const userOrderBatch = firestore().batch();

      for (let i = 0; i < userOrdersPrintingTemp.length; i++) {
        logToFile(
          `updateUserOrdersTrigger: ${userOrdersPrintingTemp[i].uniqueId}`,
        );

        let printedTokenList = userOrdersPrintingTemp[i].printedTokenList || [
          'koodoo',
        ];

        if (!printedTokenList.includes(tokenFcm ? tokenFcm : tokenDevice)) {
          userOrderBatch.update(
            firestore()
              .collection(Collections.UserOrder)
              .doc(userOrdersPrintingTemp[i].uniqueId),
            {
              // triggerPrintingTimes: 1,
              printedTokenList: [
                ...printedTokenList,
                tokenFcm ? tokenFcm : tokenDevice,
              ],

              updatedAt: Date.now(),
            },
          );

          if (currOutlet.uptPrintNow) {
            userOrderBatch.commit();
          }
        }
      }

      // delay update print, in case printing failed early (before reprint mechanic started [UserOrderMetadata]), can still print again when app started
      if (currOutlet.delayUptPrint !== undefined) {
        setTimeout(() => {
          logToFile(`updateUserOrdersTrigger: commit! (delayUptPrint)`);

          userOrderBatch.commit();
        }, currOutlet.delayUptPrint);
      } else {
        logToFile(`updateUserOrdersTrigger: commit!`);

        userOrderBatch.commit();
      }
    }
  };

  const updateUserOrdersTriggerSupport = async userOrdersPrintingTemp => {
    if (tokenFcm || tokenDevice) {
      const userOrderBatch = firestore().batch();

      for (let i = 0; i < userOrdersPrintingTemp.length; i++) {
        let printedTokenListSupport = userOrdersPrintingTemp[i]
          .printedTokenListSupport || ['koodoo'];

        if (
          !printedTokenListSupport.includes(tokenFcm ? tokenFcm : tokenDevice)
        ) {
          userOrderBatch.update(
            firestore()
              .collection(Collections.UserOrder)
              .doc(userOrdersPrintingTemp[i].uniqueId),
            {
              // triggerPrintingTimes: 1,
              printedTokenListSupport: [
                ...printedTokenListSupport,
                tokenFcm ? tokenFcm : tokenDevice,
              ],

              updatedAt: Date.now(),
            },
          );
        }
      }

      userOrderBatch.commit();
    }
  };

  const printUserOrdersTrigger = async userOrdersPrintingTemp => {
    let printKdOsBeforeMinutes = 60;
    if (
      global.reservationConfig &&
      typeof global.reservationConfig.printKdOsBeforeMinutes === 'number'
    ) {
      printKdOsBeforeMinutes = global.reservationConfig.printKdOsBeforeMinutes;
    }

    for (let i = 0; i < userOrdersPrintingTemp.length; i++) {
      // userOrderBatch.update(
      //   firestore().collection(Collections.UserOrder).doc(userOrdersPrintingTemp[i].uniqueId),
      //   {
      //     triggerPrintingTimes: 1,

      //     updatedAt: Date.now(),
      //   },
      // );

      let order = userOrdersPrintingTemp[i];

      // console.log(`${order.finalPrice}: global.blockingKDAndOS checking: ${global.blockingKDAndOS}`);

      // while (global.blockingKDAndOS === true) {
      //   await waitForSeconds(1); // before print another set

      //   console.log(`${order.finalPrice}: global.blockingKDAndOS looping: ${global.blockingKDAndOS}`);
      // }

      // if (global.blockingKDAndOS === false) {
      //   global.blockingKDAndOS = true;

      //   console.log(`${order.finalPrice}: global.blockingKDAndOS now printing: ${global.blockingKDAndOS}`);
      // }

      InteractionManager.runAfterInteractions(async () => {
        logToFile(`${order.uniqueId}`);
        logToFile(`${order.orderDate} | ${order.createdAt}`);

        if (
          moment().diff(order.orderDate, 'minute') <= 15 ||
          moment().diff(order.createdAt, 'minute') <= 15 ||
          (order.isReservationOrder &&
            moment(order.reservationTime).diff(Date.now(), 'minute') <=
            printKdOsBeforeMinutes)
        ) {
          if (order.orderType === ORDER_TYPE.DINEIN) {
            // if ((isPrintingReceipt === '0' || isPrintingReceipt === null)) {
            //   disconnectPrinter(printer);
            // }

            // disconnectPrinter(printer); // no need anymore

            logToFile('dashboard - printUserOrder - DINEIN');

            if (global.currOutlet.autoPrintOsOff) {
            } else {
              printUserOrder(
                {
                  orderData: order,
                },
                false,
                [PRINTER_USAGE_TYPE.ORDER_SUMMARY],
                false,
                false,
                false,
                { isInternetReachable: true, isConnected: true },
                true, // for isPrioritized
              );
            }

            if (hadKdTypePrinter(KD_PRINT_VARIATION.SUMMARY)) {
              printUserOrder(
                {
                  orderData: order,
                },
                false,
                [PRINTER_USAGE_TYPE.KITCHEN_DOCKET],
                false,
                false,
                false,
                { isInternetReachable: true, isConnected: true },
              );
            }

            if (hadKdTypePrinter(KD_PRINT_VARIATION.SUMMARY_CATEGORY)) {
              printKDSummaryCategoryWrapper({
                orderData: order,
              });
            }

            if (hadKdTypePrinter(KD_PRINT_VARIATION.INDIVIDUAL)) {
              const printerIpCountDict = await calcPrintTotalForKdIndividual({
                userOrder: order,
              });
              const printerTaskId = uuidv4();
              global.printingTaskIdDict[printerTaskId] = {};

              for (
                let bdIndex = 0;
                bdIndex < order.cartItems.length;
                bdIndex++
              ) {
                if (!order.cartItems[bdIndex].isDocket) {
                  printDocketForKD(
                    {
                      userOrder: order,
                      cartItem: order.cartItems[bdIndex],
                      printerIpCountDict: printerIpCountDict,
                      printerTaskId: printerTaskId,
                    },
                    // true,
                    [PRINTER_USAGE_TYPE.KITCHEN_DOCKET],
                    // selectedPaymentMethodType === OFFLINE_PAYMENT_METHOD_TYPE.CASH,
                  );
                }
              }
            }

            ///////////////////////////////////

            // for beer docket

            if (order && order.cartItems) {
              for (let index = 0; index < order.cartItems.length; index++) {
                if (order.cartItems[index].isDocket) {
                  await printDocket(
                    {
                      userOrder: order,
                      cartItem: order.cartItems[index],
                    },
                    // true,
                    [PRINTER_USAGE_TYPE.RECEIPT],
                  );
                }
              }
            }

            ///////////////////////////////////

            // setTimeout(async () => {
            //   global.blockingKDAndOS = false;

            //   console.log(`${order.finalPrice}: global.blockingKDAndOS reset: ${global.blockingKDAndOS}`);
            // }, 3000);
          } else if (order.orderStatus === USER_ORDER_STATUS.ORDER_RECEIVED) {
            // if received don't print first, wait until authorized
          } else {
            // if (isPrintingReceipt === '0' || isPrintingReceipt === null) {
            //   disconnectPrinter(printer);
            // }

            // disconnectPrinter(printer); // no need anymore

            // disconnectPrinter(printer);

            logToFile('dashboard - printUserOrder - ELSE');

            if (global.currOutlet.autoPrintOsOff) {
            } else {
              printUserOrder(
                {
                  orderData: order,
                },
                false,
                [PRINTER_USAGE_TYPE.ORDER_SUMMARY],
                false,
                false,
                false,
                { isInternetReachable: true, isConnected: true },
                true, // for isPrioritized
              );
            }

            if (hadKdTypePrinter(KD_PRINT_VARIATION.SUMMARY)) {
              printUserOrder(
                {
                  orderData: order,
                },
                false,
                [PRINTER_USAGE_TYPE.KITCHEN_DOCKET],
                false,
                false,
                false,
                { isInternetReachable: true, isConnected: true },
              );
            }

            if (hadKdTypePrinter(KD_PRINT_VARIATION.SUMMARY_CATEGORY)) {
              printKDSummaryCategoryWrapper({
                orderData: order,
              });
            }

            if (hadKdTypePrinter(KD_PRINT_VARIATION.INDIVIDUAL)) {
              const printerIpCountDict = await calcPrintTotalForKdIndividual({
                userOrder: order,
              });
              const printerTaskId = uuidv4();
              global.printingTaskIdDict[printerTaskId] = {};

              for (
                let bdIndex = 0;
                bdIndex < order.cartItems.length;
                bdIndex++
              ) {
                if (!order.cartItems[bdIndex].isDocket) {
                  printDocketForKD(
                    {
                      userOrder: order,
                      cartItem: order.cartItems[bdIndex],
                      printerIpCountDict: printerIpCountDict,
                      printerTaskId: printerTaskId,
                    },
                    // true,
                    [PRINTER_USAGE_TYPE.KITCHEN_DOCKET],
                    // selectedPaymentMethodType === OFFLINE_PAYMENT_METHOD_TYPE.CASH,
                  );
                }
              }
            }

            // if (global.outletKdVariation === KD_PRINT_VARIATION.SUMMARY) {
            //   await printUserOrder(
            //     {
            //       orderData: order,
            //     },
            //     false,
            //     [PRINTER_USAGE_TYPE.KITCHEN_DOCKET],
            //     false,
            //     false,
            //     false,
            //     { isInternetReachable: true, isConnected: true },
            //   );
            // }
            // else if (global.outletKdVariation === KD_PRINT_VARIATION.SUMMARY_CATEGORY) {
            //   printKDSummaryCategoryWrapper(
            //     {
            //       orderData: order,
            //     },
            //   );
            // }
            // else if (global.outletKdVariation === KD_PRINT_VARIATION.INDIVIDUAL) {
            //   for (let bdIndex = 0; bdIndex < order.cartItems.length; bdIndex++) {
            //     printDocketForKD(
            //       {
            //         userOrder: order,
            //         cartItem: order.cartItems[bdIndex],
            //       },
            //       // true,
            //       [PRINTER_USAGE_TYPE.KITCHEN_DOCKET],
            //       // selectedPaymentMethodType === OFFLINE_PAYMENT_METHOD_TYPE.CASH,
            //     );
            //   }
            // }

            // disconnectPrinter(printer);

            ///////////////////////////////////

            // for beer docket

            if (order && order.cartItems) {
              for (let index = 0; index < order.cartItems.length; index++) {
                if (order.cartItems[index].isDocket) {
                  await printDocket(
                    {
                      userOrder: order,
                      cartItem: order.cartItems[index],
                    },
                    // true,
                    [PRINTER_USAGE_TYPE.RECEIPT],
                  );
                }
              }
            }

            ///////////////////////////////////

            // setTimeout(async () => {
            //   global.blockingKDAndOS = false;

            //   console.log(`${order.finalPrice}: global.blockingKDAndOS reset: ${global.blockingKDAndOS}`);
            // }, 3000);
          }
        } else {
          // setTimeout(async () => {
          //   global.blockingKDAndOS = false;
          //   console.log(`${order.finalPrice}: global.blockingKDAndOS reset: ${global.blockingKDAndOS}`);
          // }, 1000);
        }
      });
    }
  };

  const seatUserOrders = async userOrdersTable => {
    var tableIdList = [...new Set(userOrdersTable.map(order => order.tableId))];

    for (let i = 0; i < tableIdList.length; i++) {
      const currTable = outletTables.find(
        table => table.uniqueId === tableIdList[i],
      );

      if (currTable && currTable.uniqueId && currTable.seated <= 0) {
        // if (currTable && currTable.uniqueId) {
        // try to seat the table to show the order

        var paxTotal = userOrdersTable
          .filter(order => order.tableId === currTable.uniqueId)
          .reduce((accum, order) => accum + order.tablePax, 0);

        var body = {
          tableId: currTable.uniqueId,
          pax: paxTotal,
          outletId: currOutletId,
        };

        APILocal.addCustomer({ body }).then(result => {
          if (result && result.status === 'success') {
            console.log('auto seated');
          }
        });
      }
    }
  };

  /////////////////////////////////////////////////////////////////////////////////////

  // 2023-01-03 - Print receipt when user paid orders online (paid when order placed or after order placed)

  useEffect(() => {
    const useEffectCallback = async () => {
      console.log('useEffect - Dashboard - 12');

      if (
        userOrdersPrinting.length > 0 &&
        (tokenFcm || tokenDevice) &&
        global.outletPrintReceiptWhenPaidOnline
      ) {
        // var userOrdersPrintingTemp = [
        //   ...userOrdersPrinting,
        // ];

        if (!global.outletToggleDisableAutoPrint) {
          InteractionManager.runAfterInteractions(() => {
            var allOutletsUserOrdersRealTimeTemp = userOrdersPrinting.filter(
              order =>
                order.paymentDate &&
                order.paymentDetails &&
                (order.paymentDetails.txn_ID !== undefined ||
                  order.paymentDetails.txnId !== undefined ||
                  order.paymentDetails.channel ===
                  OFFLINE_PAYMENT_METHOD_TYPE.USE_CREDIT) &&
                (order.printedTokenPaidOnlineList === undefined ||
                  (order.printedTokenPaidOnlineList &&
                    order.printedTokenPaidOnlineList.length === 0) || // only print if empty token
                  (supportCodeData &&
                    (order.printedTokenPaidOnlineListSupport === undefined ||
                      (order.printedTokenPaidOnlineListSupport &&
                        ((tokenFcm &&
                          !order.printedTokenPaidOnlineListSupport.includes(
                            tokenFcm,
                          )) ||
                          (tokenDevice &&
                            !order.printedTokenPaidOnlineListSupport.includes(
                              tokenDevice,
                            ))))))) &&
                // (order.printedTokenPaidOnlineList === undefined ||
                //   (
                //     order.printedTokenPaidOnlineList &&
                //     // !order.printedTokenPaidOnlineList.includes(tokenFcm)
                //     (
                //       (tokenFcm && !order.printedTokenPaidOnlineList.includes(tokenFcm))
                //       ||
                //       (tokenDevice && !order.printedTokenPaidOnlineList.includes(tokenDevice))
                //     )
                //     // (order.printedTokenPaidOnlineList.length === 0)
                //   )
                // )
                !global.printedOrderPaidOnlineListLocal.includes(
                  order.uniqueId,
                ),
            );

            if (allOutletsUserOrdersRealTimeTemp.length > 0) {
              // setPrintedOrderIdList([
              //   ...printedOrderIdList,
              //   userOrdersPrintingTemp.map(order => order.uniqueId),
              // ]);

              // global.isCheckingIncomingOrders = true;

              global.printedOrderPaidOnlineListLocal = [
                ...global.printedOrderPaidOnlineListLocal,
                ...allOutletsUserOrdersRealTimeTemp.map(
                  order => order.uniqueId,
                ),
              ];

              InteractionManager.runAfterInteractions(() => {
                printUserOrdersTriggerPaidOnline(
                  allOutletsUserOrdersRealTimeTemp,
                );

                if (!supportCodeData) {
                  updateUserOrdersTriggerPaidOnline(
                    allOutletsUserOrdersRealTimeTemp,
                  );
                } else {
                  updateUserOrdersTriggerPaidOnlineSupport(
                    allOutletsUserOrdersRealTimeTemp,
                  );
                }
              });

              // setTimeout(() => {
              //   global.isCheckingIncomingOrders = false;
              // }, 500);

              ///////////////////////////////////////////////////////////////////

              // force sequential

              // var isPrintingKDAndOS = await AsyncStorage.getItem('isPrintingKDAndOS');

              // console.log(`[batch] ${userOrdersPrintingTemp[0].finalPrice}: isPrintingKDAndOS checking: ${isPrintingKDAndOS}`);

              // while (isPrintingKDAndOS === '1') {
              //   await waitForSeconds(1); // before print another set

              //   isPrintingKDAndOS = await AsyncStorage.getItem('isPrintingKDAndOS');

              //   console.log(`[batch] ${userOrdersPrintingTemp[0].finalPrice}: isPrintingKDAndOS looping: ${isPrintingKDAndOS}`);
              // }

              // if (isPrintingKDAndOS === '0') {
              //   await AsyncStorage.setItem('isPrintingKDAndOS', '1');

              //   console.log(`[batch] ${userOrdersPrintingTemp[0].finalPrice}: isPrintingKDAndOS now printing: ${isPrintingKDAndOS}`);

              //   printUserOrdersTrigger(userOrdersPrintingTemp);
              // }

              ///////////////////////////////////////////////////////////////////

              // clean up
              // printedOrderListLocal = [];
            }
          });
        }
      }
    };

    useEffectCallback();
  }, [userOrdersPrinting, tokenFcm, tokenDevice]);

  const updateUserOrdersTriggerPaidOnline = async userOrdersPrintingTemp => {
    if (tokenFcm || tokenDevice) {
      const userOrderBatch = firestore().batch();

      for (let i = 0; i < userOrdersPrintingTemp.length; i++) {
        let printedTokenPaidOnlineList = userOrdersPrintingTemp[i]
          .printedTokenPaidOnlineList || ['koodoo'];

        userOrderBatch.update(
          firestore()
            .collection(Collections.UserOrder)
            .doc(userOrdersPrintingTemp[i].uniqueId),
          {
            // triggerPrintingTimes: 1,
            printedTokenPaidOnlineList: [
              ...printedTokenPaidOnlineList,
              tokenFcm ? tokenFcm : tokenDevice,
            ],

            updatedAt: Date.now(),
          },
        );
      }

      // delay update print, in case printing failed early (before reprint mechanic started [UserOrderMetadata]), can still print again when app started
      if (currOutlet.delayUptPrint !== undefined) {
        setTimeout(() => {
          userOrderBatch.commit();
        }, currOutlet.delayUptPrint);
      } else {
        userOrderBatch.commit();
      }
    }
  };

  const updateUserOrdersTriggerPaidOnlineSupport =
    async userOrdersPrintingTemp => {
      if (tokenFcm || tokenDevice) {
        const userOrderBatch = firestore().batch();

        for (let i = 0; i < userOrdersPrintingTemp.length; i++) {
          let printedTokenPaidOnlineListSupport = userOrdersPrintingTemp[i]
            .printedTokenPaidOnlineListSupport || ['koodoo'];

          userOrderBatch.update(
            firestore()
              .collection(Collections.UserOrder)
              .doc(userOrdersPrintingTemp[i].uniqueId),
            {
              // triggerPrintingTimes: 1,
              printedTokenPaidOnlineListSupport: [
                ...printedTokenPaidOnlineListSupport,
                tokenFcm ? tokenFcm : tokenDevice,
              ],

              updatedAt: Date.now(),
            },
          );
        }

        userOrderBatch.commit();
      }
    };

  const printUserOrdersTriggerPaidOnline = async userOrdersPrintingTemp => {
    for (let i = 0; i < userOrdersPrintingTemp.length; i++) {
      // userOrderBatch.update(
      //   firestore().collection(Collections.UserOrder).doc(userOrdersPrintingTemp[i].uniqueId),
      //   {
      //     triggerPrintingTimes: 1,

      //     updatedAt: Date.now(),
      //   },
      // );

      let order = userOrdersPrintingTemp[i];

      // console.log(`${order.finalPrice}: global.blockingKDAndOS checking: ${global.blockingKDAndOS}`);

      // while (global.blockingKDAndOS === true) {
      //   await waitForSeconds(1); // before print another set

      //   console.log(`${order.finalPrice}: global.blockingKDAndOS looping: ${global.blockingKDAndOS}`);
      // }

      // if (global.blockingKDAndOS === false) {
      //   global.blockingKDAndOS = true;

      //   console.log(`${order.finalPrice}: global.blockingKDAndOS now printing: ${global.blockingKDAndOS}`);
      // }

      InteractionManager.runAfterInteractions(async () => {
        logToFile('dashboard - printUserOrdersTriggerPaidOnline');
        logToFile(`${order.orderId} | ${order.uniqueId}`);
        logToFile(
          `${order.paymentDate} | ${order.createdAt} | ${order.orderDate}`,
        );

        if (
          moment().diff(order.paymentDate, 'minute') <= 15 ||
          moment().diff(order.createdAt, 'minute') <= 15 ||
          moment().diff(order.orderDate, 'minute') <= 15
        ) {
          logToFile('dashboard - printUserOrder - RECEIPT');

          await printUserOrder(
            {
              // orderId: item.uniqueId,
              orderData: order,
              receiptNote: currOutlet.receiptNote || '',
            },
            false,
            [PRINTER_USAGE_TYPE.RECEIPT],
            // [PRINTER_USAGE_TYPE.KITCHEN_DOCKET],
            false,
            false,
            false,
            (netInfoData = {
              isInternetReachable: netInfo.isInternetReachable,
              isConnected: netInfo.isConnected,
            }),
            // [PRINTER_USAGE_TYPE.ORDER_SUMMARY, PRINTER_USAGE_TYPE.KITCHEN_DOCKET]
            // [PRINTER_USAGE_TYPE.KITCHEN_DOCKET],
          );
        } else {
          // setTimeout(async () => {
          //   global.blockingKDAndOS = false;
          //   console.log(`${order.finalPrice}: global.blockingKDAndOS reset: ${global.blockingKDAndOS}`);
          // }, 1000);
        }
      });
    }
  };

  /////////////////////////////////////////////////////////////////////////////////////

  // const [subscriberListenToUserChangesMerchant, setSubscriberListenToUserChangesMerchant] = useState(() => { });

  useEffect(() => {
    console.log('useEffect - Dashboard - 13');

    if (firebaseUid !== '') {
      if (
        role === ROLE_TYPE.ADMIN ||
        role === ROLE_TYPE.LEGACY ||
        role === ROLE_TYPE.STORE_MANAGER ||
        role === ROLE_TYPE.FRONTLINER
      ) {
        // InteractionManager.runAfterInteractions(() => {
        //   listenToUserChangesMerchant(firebaseUid, currOutletId);
        // });

        typeof global.subscriberListenToUserChangesMerchant === 'function' &&
          global.subscriberListenToUserChangesMerchant();
        global.subscriberListenToUserChangesMerchant = () => { };

        let subscriber = listenToUserChangesMerchant(firebaseUid, currOutletId);

        global.subscriberListenToUserChangesMerchant = subscriber;

        return () => {
          typeof subscriber === 'function' && subscriber();
        };
      }
    }
  }, [firebaseUid, role]);

  // const [subscriberListenToMerchantIdChangesMerchant, setSubscriberListenToMerchantIdChangesMerchant] = useState(() => { });

  useEffect(() => {
    console.log('useEffect - Dashboard - 14');

    if (
      merchantId !== '' &&
      currOutletId !== '' &&
      isMasterAccount !== undefined &&
      currOutlet &&
      currOutlet.uniqueId
    ) {
      if (
        role === ROLE_TYPE.ADMIN ||
        role === ROLE_TYPE.LEGACY ||
        role === ROLE_TYPE.STORE_MANAGER ||
        role === ROLE_TYPE.FRONTLINER
      ) {
        // InteractionManager.runAfterInteractions(() => {
        //   listenToMerchantIdChangesMerchant(merchantId);
        // });

        // listenToMerchantIdChangesMerchant(merchantId);

        typeof global.subscriberListenToMerchantIdChangesMerchant ===
          'function' && global.subscriberListenToMerchantIdChangesMerchant();
        global.subscriberListenToMerchantIdChangesMerchant = () => { };

        let subscriberSummary = listenToMerchantIdChangesMerchant(
          merchantId,
          currOutletId,
          isMasterAccount,
          currOutlet,
        );

        global.subscriberListenToMerchantIdChangesMerchant = subscriberSummary;

        return () => {
          typeof subscriberSummary === 'function' && subscriberSummary();
        };
      }
    }
  }, [
    merchantId,
    role,

    currOutletId,
    isMasterAccount,

    currOutlet.uniqueId,
    currOutlet.uomdv2Limit,
  ]);

  // const [subscriberListenToCurrOutletIdChangesWaiter, setSubscriberListenToCurrOutletIdChangesWaiter] = useState(() => { });

  useEffect(() => {
    console.log('useEffect - Dashboard - 15');

    if (currOutletId !== '') {
      if (
        role === ROLE_TYPE.ADMIN ||
        role === ROLE_TYPE.LEGACY ||
        role === ROLE_TYPE.STORE_MANAGER ||
        role === ROLE_TYPE.FRONTLINER
      ) {
        // InteractionManager.runAfterInteractions(() => {
        //   listenToCurrOutletIdChangesWaiter(role, currOutletId, false);

        //   listenToCurrOutletIdChangesMerchant(merchantId, currOutletId);
        // });

        // typeof subscriberListenToCurrOutletIdChangesWaiter === 'function' && subscriberListenToCurrOutletIdChangesWaiter();
        // setSubscriberListenToCurrOutletIdChangesWaiter(() => { });

        typeof global.subscriberListenToCurrOutletIdChangesWaiter ===
          'function' && global.subscriberListenToCurrOutletIdChangesWaiter();
        global.subscriberListenToCurrOutletIdChangesWaiter = () => { };

        let subscriber = listenToCurrOutletIdChangesWaiter(
          role,
          currOutletId,
          false,
          '',
          currOutlet.toggleOpenOrder,
          currOutlet.openOrderDays ? currOutlet.openOrderDays : 30,
        );

        global.subscriberListenToCurrOutletIdChangesWaiter = subscriber;

        return () => {
          typeof subscriber === 'function' && subscriber();
        };
      }
    }
  }, [
    currOutletId,
    role,
    currOutlet.toggleOpenOrder,
    currOutlet.openOrderDays,
  ]);

  useEffect(() => {
    console.log('useEffect - Dashboard - 16');

    if (currOutletId !== '') {
      if (
        role === ROLE_TYPE.ADMIN ||
        role === ROLE_TYPE.LEGACY ||
        role === ROLE_TYPE.STORE_MANAGER ||
        role === ROLE_TYPE.FRONTLINER
      ) {
        typeof global.subscriberListenToReservationChanges === 'function' &&
          global.subscriberListenToReservationChanges();
        global.subscriberListenToReservationChanges = () => { };

        let subscriber = listenToCurrOutletIdReservationChanges(
          role,
          currOutletId,
          false,
          '',
          selectedCalendarData,
          historyStartDate,
          historyEndDate,
        );

        global.subscriberListenToReservationChanges = subscriber;

        return () => {
          typeof subscriber === 'function' && subscriber();
        };
      }
    }
  }, [
    currOutletId,
    role,
    selectedCalendarData,
    historyStartDate,
    historyEndDate,
  ]);

  // const [subscriberListenToCurrOutletIdChangesMerchant, setSubscriberListenToCurrOutletIdChangesMerchant] = useState(() => { });

  useEffect(() => {
    console.log('useEffect - Dashboard - 17');

    if (merchantId !== '' && currOutletId !== '') {
      if (
        role === ROLE_TYPE.ADMIN ||
        role === ROLE_TYPE.LEGACY ||
        role === ROLE_TYPE.STORE_MANAGER ||
        role === ROLE_TYPE.FRONTLINER
      ) {
        // InteractionManager.runAfterInteractions(() => {
        //   listenToCurrOutletIdChangesWaiter(role, currOutletId, false);

        //   listenToCurrOutletIdChangesMerchant(merchantId, currOutletId);
        // });

        typeof global.subscriberListenToCurrOutletIdChangesMerchant ===
          'function' && global.subscriberListenToCurrOutletIdChangesMerchant();
        global.subscriberListenToCurrOutletIdChangesMerchant = () => { };

        let subscriber = listenToCurrOutletIdChangesMerchant(
          merchantId,
          currOutletId,
          isMasterAccount,
        );

        global.subscriberListenToCurrOutletIdChangesMerchant = subscriber;

        return () => {
          typeof subscriber === 'function' && subscriber();
        };
      }
    }
  }, [merchantId, currOutletId, role, isMasterAccount]);

  // useEffect(() => {
  //   if (merchantId !== '' && currOutletId !== '') {
  //     if (
  //       role === ROLE_TYPE.ADMIN ||
  //       role === ROLE_TYPE.LEGACY ||
  //       role === ROLE_TYPE.STORE_MANAGER ||
  //       role === ROLE_TYPE.FRONTLINER
  //     ) {
  //       // InteractionManager.runAfterInteractions(() => {
  //       //   listenToCurrOutletIdChangesWaiter(role, currOutletId, false);

  //       //   listenToCurrOutletIdChangesMerchant(merchantId, currOutletId);
  //       // });

  //       listenToCurrOutletIdChangesWaiter(role, currOutletId, false);

  //       listenToCurrOutletIdChangesMerchant(merchantId, currOutletId, isMasterAccount);
  //     }
  //   }
  // }, [merchantId, currOutletId, role, isMasterAccount]);

  /////////////////////////////////////////////////////////////////

  // 2023-06-09 - Hide first
  // useEffect(() => {
  //   InteractionManager.runAfterInteractions(() => {
  //     // var userOrders = [];
  //     // var userOrdersDict = {};
  //     // var userOrdersTableDict = {};

  //     var userOrdersAllStatus = [];

  //     if (currOutletId !== '') {
  //       // var allOutletsUserOrdersCurrOutlet = allOutletsUserOrders.filter(item => item.outletId === currOutletId);

  //       // for (var i = 0; i < allOutletsUserOrdersCurrOutlet.length; i++) {
  //       //   userOrdersAllStatus.push(allOutletsUserOrdersCurrOutlet[i]);
  //       // }

  //       userOrdersAllStatus = allOutletsUserOrders.filter(item => item.outletId === currOutletId);
  //     }

  //     userOrdersAllStatus.sort((a, b) => b.updatedAt - a.updatedAt);

  //     OutletStore.update((s) => {
  //       // s.userOrders = userOrders;
  //       // s.userOrdersDict = userOrdersDict;
  //       // s.userOrdersTableDict = userOrdersTableDict;

  //       s.userOrdersAllStatus = userOrdersAllStatus;
  //     });
  //   });
  // }, [currOutletId, allOutletsUserOrders]);

  useEffect(() => {
    console.log('useEffect - Dashboard - 18');

    InteractionManager.runAfterInteractions(() => {
      var outletItems = [];
      var outletItemsDict = {};
      var outletItemsSkuDict = {};

      // var allOutletsItems = [];
      // var allOutletsItemsSkuDict = {};

      if (isMasterAccount) {
        const allOutletsItemsCurrOutlet = allOutletsItems.filter(
          item => item.outletId === currOutletId,
        );

        outletItems = allOutletsItemsCurrOutlet;

        // for (var i = 0; i < allOutletsItemsCurrOutlet.length; i++) {
        //   outletItems.push(allOutletsItemsCurrOutlet[i]);
        //   outletItemsDict[allOutletsItemsCurrOutlet[i].uniqueId] = allOutletsItemsCurrOutlet[i];
        //   outletItemsSkuDict[allOutletsItemsCurrOutlet[i].sku] = allOutletsItemsCurrOutlet[i];

        //   // allOutletsItems.push(record);

        //   // if (allOutletsItemsSkuDict[record.sku]) {
        //   //   allOutletsItemsSkuDict[record.sku].push(record);
        //   // } else {
        //   //   allOutletsItemsSkuDict[record.sku] = [record];
        //   // }
        // }
      } else if (isMasterAccount === false || isMasterAccount === undefined) {
        outletItems = allOutletsItems;
      }

      // outletItems.sort((a, b) => a.name.localeCompare(b.name));

      // global.outletItemsDict = outletItemsDict;

      /////////////////////////////////////////

      // 2023-03-03 - Might affect performance (for printCategoryAndProductSalesReport)

      // global.outletItemsInfoDict = Object.entries(outletItems.map(item =>
      //   // ([item.uniqueId, item.skuMerchant ? item.skuMerchant : 'N/A'])
      //   ({
      //     categoryId: item.categoryId,
      //     name: item.name,
      //     skuMerchant: item.skuMerchant,
      //   })
      // ));

      global.outletItemsInfoDict = outletItems.reduce((obj, item) => {
        return Object.assign(obj, {
          [item.uniqueId]: {
            categoryId: item.categoryId,
            name: item.name,
            skuMerchant: item.skuMerchant,
          },
        });
      }, {});

      /////////////////////////////////////////

      OutletStore.update(s => {
        s.outletItems = outletItems;
        // s.outletItemsDict = outletItemsDict;
        // s.outletItemsSkuDict = outletItemsSkuDict;

        // s.allOutletsItems = allOutletsItems;
        // s.allOutletsItemsSkuDict = allOutletsItemsSkuDict;
      });
    });
  }, [currOutletId, allOutletsItems, isMasterAccount]);

  useEffect(() => {
    console.log('useEffect - Dashboard - 19');

    InteractionManager.runAfterInteractions(() => {
      var outletCategories = [];
      var outletCategoriesDict = {};

      // var allOutletsCategories = [];
      // var allOutletsCategoriesNameDict = {};
      // var allOutletsCategoriesDict = {};

      // var allOutletsCategoriesUnique = [];

      var allOutletsCategoriesCurrOutlet = allOutletsCategories.filter(
        item => item.outletId === currOutletId,
      );

      for (var i = 0; i < allOutletsCategoriesCurrOutlet.length; i++) {
        outletCategories.push(allOutletsCategoriesCurrOutlet[i]);
        outletCategoriesDict[allOutletsCategoriesCurrOutlet[i].uniqueId] =
          allOutletsCategoriesCurrOutlet[i];

        global.outletCategoriesDict[
          allOutletsCategoriesCurrOutlet[i].uniqueId
        ] = allOutletsCategoriesCurrOutlet[i];

        // allOutletsCategories.push(record);

        // if (allOutletsCategoriesNameDict[record.name]) {
        //   allOutletsCategoriesNameDict[record.name].push(record);
        // } else {
        //   allOutletsCategoriesNameDict[record.name] = [record];
        // }

        // if (allOutletsCategoriesDict[record.uniqueId]) {
        //   allOutletsCategoriesDict[record.uniqueId] = record;
        // } else {
        //   allOutletsCategoriesDict[record.uniqueId] = record;
        // }

        // var isExisted = false;

        // for (var j = 0; j < allOutletsCategoriesUnique.length; j++) {
        //   if (allOutletsCategoriesUnique[j].name === record.name) {
        //     isExisted = true;
        //     break;
        //   }
        // }

        // if (!isExisted) {
        //   allOutletsCategoriesUnique.push(record);
        // }
      }

      // allOutletsCategoriesUnique.sort((a, b) => a.name.localeCompare(b.name));

      outletCategories.sort((a, b) => {
        return naturalCompare(a.name || '', b.name || '');
      });

      OutletStore.update(s => {
        s.outletCategories = outletCategories;
        s.outletCategoriesDict = outletCategoriesDict;

        // s.allOutletsCategories = allOutletsCategories;
        // s.allOutletsCategoriesNameDict = allOutletsCategoriesNameDict;
        // s.allOutletsCategoriesDict = allOutletsCategoriesDict;

        // s.allOutletsCategoriesUnique = allOutletsCategoriesUnique;
      });

      CommonStore.update(s => {
        s.selectedOutletItemCategory = outletCategories[0];
      });
    });
  }, [currOutletId, allOutletsCategories]);

  /////////////////////////////////////////////////////////////////

  // useEffect(() => {
  //   if (currOutletId !== '') {
  //     if (
  //       role === ROLE_TYPE.ADMIN ||
  //       role === ROLE_TYPE.LEGACY ||
  //       role === ROLE_TYPE.STORE_MANAGER ||
  //       role === ROLE_TYPE.FRONTLINER
  //     ) {
  //       // can use the waiter one
  //       listenToCurrOutletIdChangesWaiter(role, currOutletId);
  //     }
  //   }
  // }, [currOutletId, role]);

  // useEffect(() => {
  //   if (allOutlets.length > 0) {
  //     if (
  //       role === ROLE_TYPE.ADMIN ||
  //       role === ROLE_TYPE.LEGACY ||
  //       role === ROLE_TYPE.STORE_MANAGER ||
  //       role === ROLE_TYPE.FRONTLINER
  //     ) {
  //       listenToAllOutletsChangesMerchant(allOutlets);
  //     }
  //   }
  // }, [allOutlets.length, role]);

  // const [subscriberListenToSelectedOutletItemChanges, setSubscriberListenToSelectedOutletItemChanges] = useState(() => { });

  useEffect(() => {
    console.log('useEffect - Dashboard - 20');

    if (
      selectedOutletItem !== null &&
      selectedOutletItem !== undefined &&
      selectedOutletItem.uniqueId
    ) {
      // InteractionManager.runAfterInteractions(() => {
      //   listenToSelectedOutletItemChanges(selectedOutletItem);
      // });

      typeof global.subscriberListenToSelectedOutletItemChanges ===
        'function' && global.subscriberListenToSelectedOutletItemChanges();
      global.subscriberListenToSelectedOutletItemChanges = () => { };

      let subscriber = listenToSelectedOutletItemChanges(selectedOutletItem);

      global.subscriberListenToSelectedOutletItemChanges = subscriber;

      return () => {
        typeof subscriber === 'function' && subscriber();
      };
    }
  }, [selectedOutletItem.uniqueId, role]);

  // const [subscriberListenToSelectedOrderToPayUserIdChanges, setSubscriberListenToSelectedOrderToPayUserIdChanges] = useState(() => { });

  useEffect(() => {
    // InteractionManager.runAfterInteractions(() => {
    //   listenToSelectedOrderToPayUserIdChanges(selectedOrderToPayUserId || 'default-user');

    //   // if (selectedOrderToPayUserId && selectedOrderToPayUserId !== '') {
    //   //   listenToSelectedOrderToPayUserIdChanges(selectedOrderToPayUserId);
    //   // }
    //   // else {
    //   //   // OutletStore.update((s) => {
    //   //   //   s.selectedCustomerOrders = [];
    //   //   //   s.selectedCustomerDineInOrders = [];

    //   //   //   s.selectedCustomerAddresses = [];
    //   //   //   s.selectedCustomerPointsTransactions = [];
    //   //   //   s.selectedCustomerPointsBalance = 0;

    //   //   //   s.selectedCustomerVoucherRedemptions = [];

    //   //   //   s.selectedCustomerUserBeerDockets = [];

    //   //   //   s.selectedCustomerLCCTransactionsEmail = [];
    //   //   //   s.selectedCustomerLCCBalanceEmail = 0;
    //   //   //   s.selectedCustomerLCCTransactionsPhone = [];
    //   //   //   s.selectedCustomerLCCBalancePhone = 0;

    //   //   //   s.selectedCustomerLCCTransactions = [];
    //   //   //   s.selectedCustomerLCCBalance = 0;

    //   //   //   s.selectedCustomerUserLoyaltyCampaigns = [];

    //   //   //   s.selectedCustomerUserTaggableVouchers = [];

    //   //   //   // s.availableTaggableVouchers = [];
    //   //   // });
    //   // }
    // });

    typeof global.subscriberListenToSelectedOrderToPayUserIdChanges ===
      'function' && global.subscriberListenToSelectedOrderToPayUserIdChanges();
    global.subscriberListenToSelectedOrderToPayUserIdChanges = () => { };

    let subscriber = listenToSelectedOrderToPayUserIdChanges(
      selectedOrderToPayUserId || 'default-user',
    );

    global.subscriberListenToSelectedOrderToPayUserIdChanges = subscriber;

    return () => {
      typeof subscriber === 'function' && subscriber();
    };
  }, [selectedOrderToPayUserId]);

  // const [subscriberListenToSelectedOutletTableIdChanges, setSubscriberListenToSelectedOutletTableIdChanges] = useState(() => { });

  useEffect(() => {
    console.log('useEffect - Dashboard - 21');

    if (
      firebaseUid.length > 0 &&
      selectedOutletTable &&
      selectedOutletTable.uniqueId &&
      currOutletId
    ) {
      // InteractionManager.runAfterInteractions(() => {
      //   listenToSelectedOutletTableIdChanges(
      //     firebaseUid,
      //     selectedOutletTable.uniqueId,
      //     currOutletId,
      //   );
      // });

      typeof global.subscriberListenToSelectedOutletTableIdChanges ===
        'function' && global.subscriberListenToSelectedOutletTableIdChanges();
      global.subscriberListenToSelectedOutletTableIdChanges = () => { };

      let subscriber = listenToSelectedOutletTableIdChanges(
        firebaseUid,
        selectedOutletTable.uniqueId,
        currOutletId,
      );

      global.subscriberListenToSelectedOutletTableIdChanges = subscriber;

      return () => {
        typeof subscriber === 'function' && subscriber();
      };
    }
  }, [firebaseUid, selectedOutletTable.uniqueId, currOutletId]);

  // const [subscriberListenToCommonChangesMerchant, setSubscriberListenToCommonChangesMerchant] = useState(() => { });

  useEffect(() => {
    console.log('useEffect - Dashboard - 22');

    // console.log('screen height' + windowHeight);
    // console.log('screen width' + windowWidth);

    // InteractionManager.runAfterInteractions(() => {
    //   listenToCommonChangesMerchant();
    // });

    requestNotificationsPermission();

    messaging().onMessage(async msg => {
      // console.log('message from foreground!');
      // console.log(msg);

      parseMessages(msg);
    });

    //////////////////////////////////////////////

    typeof global.subscriberListenToCommonChangesMerchant === 'function' &&
      global.subscriberListenToCommonChangesMerchant();
    global.subscriberListenToCommonChangesMerchant = () => { };

    let subscriber = listenToCommonChangesMerchant();

    global.subscriberListenToCommonChangesMerchant = subscriber;

    return () => {
      typeof subscriber === 'function' && subscriber();
    };

    //////////////////////////////////////////////
  }, []);

  useEffect(() => {
    console.log('useEffect - Dashboard - 2024-11-26');

    //////////////////////////////////////////////

    if (
      currOutlet &&
      currOutlet.odActive &&
      odPairingType &&
      odPairingDevice === OUTLET_DISPLAY_PAIRING_DEVICE.DISPLAY
    ) {
      if (isOutletDisplay()) {
        // go to table screen first

        navigation.navigate('Table');

        // setTimeout(() => {
        //   navigation.navigate('Table');
        // }, 5000);

        if (global.printerTaskQueueInterval !== null) {
          clearInterval(global.printerTaskQueueInterval);
        }
      }

      typeof global.subscriberListenToDisplayScreenChanges === 'function' &&
        global.subscriberListenToDisplayScreenChanges();
      global.subscriberListenToDisplayScreenChanges = () => { };

      let subscriber = listenToDisplayScreenChanges(
        odPairingType,
        currOutlet.uniqueId,
      );

      global.subscriberListenToDisplayScreenChanges = subscriber;

      return () => {
        typeof subscriber === 'function' && subscriber();
      };
    }

    //////////////////////////////////////////////
  }, [currOutlet.odActive, odPairingType, odPairingDevice]);

  useEffect(() => {
    if (isOutletDisplay()) {
      navigation.navigate('Table');
    }
  }, [odDt]);

  // const [subscriberListenToSelectedCustomerChangesMerchant, setSubscriberListenToSelectedCustomerChangesMerchant] = useState(() => { });

  useEffect(() => {
    console.log('useEffect - Dashboard - 23');

    if (
      selectedCustomerEdit &&
      currOutletId
      // selectedCustomerEdit.firebaseUid &&
      // selectedCustomerEdit.firebaseUid.length > 0
    ) {
      // OutletStore.update((s) => {
      //   s.selectedCustomerOrders = [];
      //   s.selectedCustomerDineInOrders = [];

      //   s.selectedCustomerAddresses = [];
      //   s.selectedCustomerPointsTransactions = [];
      //   s.selectedCustomerPointsBalance = 0;

      //   s.selectedCustomerVoucherRedemptions = [];

      //   s.selectedCustomerUserBeerDockets = [];

      //   s.selectedCustomerLCCTransactions = [];
      //   s.selectedCustomerLCCBalance = 0;

      //   s.selectedCustomerUserLoyaltyCampaigns = [];
      // });

      // InteractionManager.runAfterInteractions(() => {
      //   listenToSelectedCustomerChangesMerchant(selectedCustomerEdit, currOutletId);
      // });

      typeof global.subscriberListenToSelectedCustomerChangesMerchant ===
        'function' &&
        global.subscriberListenToSelectedCustomerChangesMerchant();
      global.subscriberListenToSelectedCustomerChangesMerchant = () => { };

      let subscriber = listenToSelectedCustomerChangesMerchant(
        selectedCustomerEdit,
        currOutletId,
      );

      global.subscriberListenToSelectedCustomerChangesMerchant = subscriber;

      return () => {
        typeof subscriber === 'function' && subscriber();
      };
    } else {
      OutletStore.update(s => {
        s.selectedCustomerOrders = [];
        s.selectedCustomerDineInOrders = [];

        s.selectedCustomerAddresses = [];
        s.selectedCustomerPointsTransactions = [];
        s.selectedCustomerPointsBalance = 0;

        s.selectedCustomerVoucherRedemptions = [];

        s.selectedCustomerUserBeerDockets = [];

        s.selectedCustomerLCCTransactionsEmail = [];
        s.selectedCustomerLCCBalanceEmail = 0;
        s.selectedCustomerLCCTransactionsPhone = [];
        s.selectedCustomerLCCBalancePhone = 0;

        s.selectedCustomerLCCTransactions = [];
        s.selectedCustomerLCCBalance = 0;

        s.selectedCustomerPointsTransactionsEmail = [];
        s.selectedCustomerPointsBalanceEmail = 0;
        s.selectedCustomerPointsTransactionsPhone = [];
        s.selectedCustomerPointsBalancePhone = 0;
        s.selectedCustomerPointsTransactions = [];
        s.selectedCustomerPointsBalance = 0;

        s.selectedCustomerUserLoyaltyCampaigns = [];

        s.selectedCustomerUserTaggableVouchersView = [];

        // s.availableTaggableVouchers = [];
      });

      CommonStore.update(s => {
        s.isLoadingCustomerInfo = false;
      });
    }
  }, [selectedCustomerEdit, currOutletId]);

  // const [subscriberListenToSelectedCustomerApplicableVoucherIdChangesMerchant, setSubscriberListenToSelectedCustomerApplicableVoucherIdChangesMerchant] = useState(() => { });

  useEffect(() => {
    console.log('useEffect - Dashboard - 24');

    const useEffectCallback = async () => {
      if (
        selectedCustomerEdit &&
        selectedCustomerEdit.email &&
        selectedCustomerApplicableVoucherIdList.length > 0
        // selectedCustomerEdit.firebaseUid &&
        // selectedCustomerEdit.firebaseUid.length > 0
      ) {
        // OutletStore.update((s) => {
        //   s.selectedCustomerOrders = [];
        //   s.selectedCustomerDineInOrders = [];

        //   s.selectedCustomerAddresses = [];
        //   s.selectedCustomerPointsTransactions = [];
        //   s.selectedCustomerPointsBalance = 0;

        //   s.selectedCustomerVoucherRedemptions = [];

        //   s.selectedCustomerUserBeerDockets = [];

        //   s.selectedCustomerLCCTransactions = [];
        //   s.selectedCustomerLCCBalance = 0;

        //   s.selectedCustomerUserLoyaltyCampaigns = [];
        // });

        // InteractionManager.runAfterInteractions(() => {
        //   listenToSelectedCustomerApplicableVoucherIdChangesMerchant(selectedCustomerEdit, selectedCustomerApplicableVoucherIdList);
        // });

        typeof global.subscriberListenToSelectedCustomerApplicableVoucherIdChangesMerchant ===
          'function' &&
          global.subscriberListenToSelectedCustomerApplicableVoucherIdChangesMerchant();
        global.subscriberListenToSelectedCustomerApplicableVoucherIdChangesMerchant =
          () => { };

        console.log(
          'listenToSelectedCustomerApplicableVoucherIdChangesMerchant',
        );
        console.log(selectedCustomerEdit);
        console.log(selectedCustomerApplicableVoucherIdList);
        let subscriber =
          await listenToSelectedCustomerApplicableVoucherIdChangesMerchant(
            selectedCustomerEdit,
            selectedCustomerApplicableVoucherIdList,
          );

        global.subscriberListenToSelectedCustomerApplicableVoucherIdChangesMerchant =
          subscriber;

        return () => {
          typeof subscriber === 'function' && subscriber();
        };
      } else {
        OutletStore.update(s => {
          // s.selectedCustomerUserLoyaltyCampaigns = [];

          s.selectedCustomerUserTaggableVouchers = [];

          // s.selectedCustomerApplicableVoucherIdList = [];

          // s.availableTaggableVouchers = [];
        });
      }
    };

    useEffectCallback();
  }, [selectedCustomerEdit, selectedCustomerApplicableVoucherIdList]);

  ////////////////////////////////////////////////////////

  useEffect(() => {
    console.log('useEffect - Dashboard - 25');

    InteractionManager.runAfterInteractions(() => {
      var selectedCustomerLCCTransactions = [];
      var selectedCustomerLCCBalance = 0;

      for (var i = 0; i < selectedCustomerLCCTransactionsEmail.length; i++) {
        if (
          !selectedCustomerLCCTransactions.find(
            item =>
              item.uniqueId ===
              selectedCustomerLCCTransactionsEmail[i].uniqueId,
          )
        ) {
          // means not exist

          selectedCustomerLCCTransactions.push(
            selectedCustomerLCCTransactionsEmail[i],
          );
          selectedCustomerLCCBalance +=
            selectedCustomerLCCTransactionsEmail[i].amount;
        }
      }

      for (var i = 0; i < selectedCustomerLCCTransactionsPhone.length; i++) {
        if (
          !selectedCustomerLCCTransactions.find(
            item =>
              item.uniqueId ===
              selectedCustomerLCCTransactionsPhone[i].uniqueId,
          )
        ) {
          // means not exist

          selectedCustomerLCCTransactions.push(
            selectedCustomerLCCTransactionsPhone[i],
          );
          selectedCustomerLCCBalance +=
            selectedCustomerLCCTransactionsPhone[i].amount;
        }
      }

      selectedCustomerLCCTransactions.sort((a, b) => b.createdAt - a.createdAt);

      OutletStore.update(s => {
        s.selectedCustomerLCCTransactions = selectedCustomerLCCTransactions;
        s.selectedCustomerLCCBalance = parseFloat(
          selectedCustomerLCCBalance.toFixed(2),
        );
      });
    });
  }, [
    selectedCustomerLCCTransactionsEmail,
    selectedCustomerLCCBalanceEmail,
    selectedCustomerLCCTransactionsPhone,
    selectedCustomerLCCBalancePhone,
  ]);

  useEffect(() => {
    console.log('useEffect - Dashboard - 26');

    InteractionManager.runAfterInteractions(() => {
      var selectedCustomerPointsTransactions = [];
      var selectedCustomerPointsBalance = 0;

      for (var i = 0; i < selectedCustomerPointsTransactionsEmail.length; i++) {
        if (
          !selectedCustomerPointsTransactions.find(
            item =>
              item.uniqueId ===
              selectedCustomerPointsTransactionsEmail[i].uniqueId,
          )
        ) {
          // means not exist

          selectedCustomerPointsTransactions.push(
            selectedCustomerPointsTransactionsEmail[i],
          );
          selectedCustomerPointsBalance +=
            selectedCustomerPointsTransactionsEmail[i].amount;
        }
      }

      for (var i = 0; i < selectedCustomerPointsTransactionsPhone.length; i++) {
        if (
          !selectedCustomerPointsTransactions.find(
            item =>
              item.uniqueId ===
              selectedCustomerPointsTransactionsPhone[i].uniqueId,
          )
        ) {
          // means not exist

          selectedCustomerPointsTransactions.push(
            selectedCustomerPointsTransactionsPhone[i],
          );
          selectedCustomerPointsBalance +=
            selectedCustomerPointsTransactionsPhone[i].amount;
        }
      }

      selectedCustomerPointsTransactions.sort(
        (a, b) => b.createdAt - a.createdAt,
      );

      OutletStore.update(s => {
        s.selectedCustomerPointsTransactions =
          selectedCustomerPointsTransactions;
        s.selectedCustomerPointsBalance = parseFloat(
          selectedCustomerPointsBalance.toFixed(2),
        );
      });
    });
  }, [
    selectedCustomerPointsTransactionsEmail,
    selectedCustomerPointsBalanceEmail,
    selectedCustomerPointsTransactionsPhone,
    selectedCustomerPointsBalancePhone,
  ]);

  ////////////////////////////////////////////////////////

  useEffect(() => {
    console.log('useEffect - Dashboard - 27');

    if (
      firebaseUid.length > 0 &&
      currOutletId.length > 0 &&
      merchantId.length > 0 &&
      role.length > 0
    ) {
      messaging()
        .getToken()
        .then(async tokenFcm => {
          if (tokenFcm) {
            await AsyncStorage.setItem('tokenFcm', tokenFcm);
          }

          await updateTokenFcm();

          setTokenFcm(tokenFcm);

          global.tokenFcm = tokenFcm;
        })
        .catch(async err => {
          console.log(err);

          console.log('failed to obtain fcm');

          // try to use device id

          var deviceInfoId = await DeviceInfo.getUniqueId();

          if (deviceInfoId) {
            console.log('deviceInfoId');
            console.log(deviceInfoId);
            await AsyncStorage.setItem('tokenDevice', deviceInfoId);
          }

          setTokenDevice(deviceInfoId);

          global.tokenFcm = deviceInfoId;
        });

      messaging().onTokenRefresh(async tokenFcm => {
        if (tokenFcm) {
          await AsyncStorage.setItem('tokenFcm', tokenFcm);
        }

        await updateTokenFcm();

        setTokenFcm(tokenFcm);

        global.tokenFcm = tokenFcm;
      });
    }
  }, [firebaseUid, currOutletId, merchantId, role]);

  const updateTokenFcm = async () => {
    const tokenFcm = await AsyncStorage.getItem('tokenFcm');

    const supportCodeDataRaw = await AsyncStorage.getItem('supportCodeData');
    // const supportCodeData = JSON.parse(supportCodeDataRaw);

    if (tokenFcm) {
      const body = {
        tokenFcm,
        userId: firebaseUid,
        outletId: currOutletId,
        merchantId,
        role,

        isSupportAccount: supportCodeDataRaw ? true : false,
      };

      ApiClient.POST(API.updateTokenFcm, body).then(result => {
        // console.log('updated token fcm');
      });
    }
  };

  ////////////////////////////////////////////////////////

  // from notifications

  useEffect(() => {
    console.log('useEffect - Dashboard - 28');

    if (nUserOrder && nUserOrder.type) {
      if (nUserOrder.orderType === ORDER_TYPE.DINEIN) {
        navigation.navigate('Order');
      } else {
        navigation.navigate('Takeaway');
      }
    }
  }, [nUserOrder]);

  useEffect(() => {
    console.log('useEffect - Dashboard - 29');

    if (nUserReservation && nUserReservation.type) {
      navigation.navigate('Reservation');
    }
  }, [nUserReservation]);

  useEffect(() => {
    console.log('useEffect - Dashboard - 30');

    if (nUserQueue && nUserQueue.type) {
      navigation.navigate('Queue');
    }
  }, [nUserQueue]);

  useEffect(() => {
    console.log('useEffect - Dashboard - 31');

    if (nOutletSupplyItemLow && nOutletSupplyItemLow.type) {
      navigation.navigate('Inventory');
    }
  }, [nOutletSupplyItemLow]);

  useEffect(() => {
    console.log('useEffect - Dashboard - 32');

    if (nOutletItemLow && nOutletItemLow.type) {
      navigation.navigate('InventoryProduct');
    }
  }, [nOutletItemLow]);

  ////////////////////////////////////////////////////////

  // from crm

  useEffect(() => {
    console.log('useEffect - Dashboard - 33');

    InteractionManager.runAfterInteractions(() => {
      // var crmUsersTemp = [];
      // var crmUsersRawTemp = [...crmUsersRaw];

      // for (var i = 0; i < favoriteMerchantIdUsers.length; i++) {
      //   var record = {
      //     ...favoriteMerchantIdUsers[i],
      //   };

      //   for (var j = 0; j < crmUsersRawTemp.length; j++) {
      //     if (record.email === crmUsersRawTemp[j].email || record.number === crmUsersRawTemp[j].number) {
      //       record = {
      //         ...record,
      //         ...crmUsersRawTemp[j],
      //       };

      //       crmUsersRawTemp.splice(j, 1);

      //       break;
      //     }
      //   }

      //   crmUsersTemp.push(record);
      // }

      // for (var i = 0; i < linkedMerchantIdUsers.length; i++) {
      //   var isExisted = false;

      //   for (var index = 0; index < favoriteMerchantIdUsers.length; index++) {
      //     if (
      //       favoriteMerchantIdUsers[index].email ===
      //       linkedMerchantIdUsers[i].email ||
      //       favoriteMerchantIdUsers[index].number ===
      //       linkedMerchantIdUsers[i].number
      //     ) {
      //       isExisted = true;
      //       break;
      //     }
      //   }

      //   if (!isExisted) {
      //     var record = {
      //       ...linkedMerchantIdUsers[i],
      //     };

      //     for (var j = 0; j < crmUsersRawTemp.length; j++) {
      //       if (record.email === crmUsersRawTemp[j].email ||
      //         record.number === crmUsersRawTemp[j].number) {
      //         record = {
      //           ...record,
      //           ...crmUsersRawTemp[j],
      //         };

      //         crmUsersRawTemp.splice(j, 1);

      //         break;
      //       }
      //     }

      //     crmUsersTemp.push(record);
      //   }
      // }

      // for (var i = 0; i < crmUsersRawTemp.length; i++) {
      //   var foundUser = crmUsersTemp.find(
      //     (user) => user.email === crmUsersRawTemp[i].email || user.number === crmUsersRawTemp[i].number,
      //   );

      //   if (!foundUser) {
      //     crmUsersTemp.push(crmUsersRawTemp[i]);
      //   }
      // }

      global.crmUsersDt = Date.now();

      // OutletStore.update((s) => {
      //   s.crmUsers = crmUsersTemp;
      // });

      OutletStore.update(s => {
        s.crmUsers = crmUsersRaw;
      });

      // console.log('crmUsers');
      // console.log(crmUsersTemp);
    });
  }, [
    crmUsersRaw,

    // linkedMerchantIdUsers,
    // favoriteMerchantIdUsers,
  ]);

  useEffect(() => {
    console.log('useEffect - Dashboard - 34');

    if (selectedCustomerEdit) {
      InteractionManager.runAfterInteractions(() => {
        for (var i = 0; i < crmUsers.length; i++) {
          if (crmUsers[i].email === selectedCustomerEdit.email) {
            CommonStore.update(s => {
              s.selectedCustomerEdit = crmUsers[i];
            });

            break;
          }
        }
      });
    }
  }, [crmUsers]);

  useEffect(() => {
    console.log('useEffect - Dashboard - 35');

    if (outletSupplyItems.length > 0) {
      InteractionManager.runAfterInteractions(async () => {
        var showedNum = 0;

        var warningOutletSupplyItemList = [];

        for (var i = 0; i < outletSupplyItems.length; i++) {
          if (
            outletSupplyItems[i].quantity <=
            outletSupplyItems[i].stockWarningQuantity
          ) {
            // alert

            showedNum++;

            PushNotification.localNotification({
              id: NOTIFICATIONS_ID.MERCHANT_LOW_STOCK_ALERT,

              channelId: NOTIFICATIONS_CHANNEL.MERCHANT_LOW_STOCK_ALERT,

              title: `${outletSupplyItems[i].name} (${outletSupplyItems[i].skuMerchant}) running in low`,
              message: `Stock left: ${outletSupplyItems[i].quantity
                ? outletSupplyItems[i].quantity.toFixed(2)
                : '0.00'
                } ${outletSupplyItems[i].unit}`,
              data: {
                type: NOTIFICATIONS_TYPE.MERCHANT_LOW_STOCK_ALERT,
              },
              userInfo: {
                type: NOTIFICATIONS_TYPE.MERCHANT_LOW_STOCK_ALERT,
              },

              group: NOTIFICATIONS_TYPE.MERCHANT_LOW_STOCK_ALERT,
              groupSummary: showedNum % 5 === 0,
            });

            warningOutletSupplyItemList.push({
              name: outletSupplyItems[i].name,
              quantity: outletSupplyItems[i].quantity,
              stockWarningQuantity: outletSupplyItems[i].stockWarningQuantity,
            });
          }
        }

        if (currOutlet && currOutlet.uniqueId) {
          var totalQuantityNew = warningOutletSupplyItemList.reduce(
            (accum, item) => accum + item.quantity,
            0,
          );
          var totalQuantityPrevious = (
            global.warningOutletSupplyItemList
              ? global.warningOutletSupplyItemList
              : []
          ).reduce((accum, item) => accum + item.quantity, 0);
          var totalWarningNew = warningOutletSupplyItemList.reduce(
            (accum, item) => accum + item.stockWarningQuantity,
            0,
          );
          var totalWarningPrevious = (
            global.warningOutletSupplyItemList
              ? global.warningOutletSupplyItemList
              : []
          ).reduce((accum, item) => accum + item.stockWarningQuantity, 0);

          if (
            Math.abs(totalQuantityNew - totalQuantityPrevious) > 0.01 ||
            Math.abs(totalWarningNew - totalWarningPrevious) > 0.01
          ) {
            console.log('update now');

            global.warningOutletSupplyItemList = warningOutletSupplyItemList;

            firestore()
              .collection(Collections.Outlet)
              .doc(currOutlet.uniqueId)
              .update({
                warningOutletSupplyItemList,

                updatedAt: Date.now(),
              });
          }
        }

        // if (currOutlet && currOutlet.uniqueId) {
        //   if (JSON.prune(warningOutletSupplyItemList) !== JSON.prune(currOutlet.warningOutletSupplyItemList ? currOutlet.warningOutletSupplyItemList : [])) {
        //     // not same, can update

        //     var totalQuantityNew = warningOutletSupplyItemList.reduce((accum, item) => accum + item.quantity, 0);
        //     var totalQuantityPrevious = (currOutlet.warningOutletSupplyItemList ? currOutlet.warningOutletSupplyItemList: []).reduce((accum, item) => accum + item.quantity, 0);
        //     var totalWarningNew = warningOutletSupplyItemList.reduce((accum, item) => accum + item.stockWarningQuantity, 0);
        //     var totalWarningPrevious = (currOutlet.warningOutletSupplyItemList ? currOutlet.warningOutletSupplyItemList: []).reduce((accum, item) => accum + item.stockWarningQuantity, 0);

        //     if ((totalQuantityNew !== totalQuantityPrevious || totalWarningNew !== totalWarningPrevious) &&
        //     warningOutletSupplyItemList.length !== (currOutlet.warningOutletSupplyItemList ? currOutlet.warningOutletSupplyItemList: []).length) {
        //       console.log('update now');

        //       // firestore().collection(Collections.Outlet).doc(currOutlet.uniqueId).update({
        //       //   warningOutletSupplyItemList: warningOutletSupplyItemList,

        //       //   updatedAt: Date.now(),
        //       // });
        //     }
        //   }
        // }
      });
    }
  }, [outletSupplyItems]);

  useEffect(() => {
    console.log('useEffect - Dashboard - 36');

    if (outletItems.length > 0) {
      InteractionManager.runAfterInteractions(() => {
        var showedNum = 0;

        var warningOutletItemList = [];

        var toPushNotificationList = [];

        for (var i = 0; i < outletItems.length; i++) {
          if (
            outletItems[i].isStockCountActive &&
            outletItems[i].stockCount <= outletItems[i].stockWarningQuantity
          ) {
            // alert

            showedNum++;

            toPushNotificationList.push({
              id: NOTIFICATIONS_ID.MERCHANT_LOW_STOCK_ALERT_PRODUCT,

              channelId: NOTIFICATIONS_CHANNEL.MERCHANT_LOW_STOCK_ALERT_PRODUCT,

              title: `${outletItems[i].name} (${outletItems[i].skuMerchant ? outletItems[i].skuMerchant : '-'
                }) running in low`,
              message: `Stock left: ${outletItems[i].stockCount
                ? outletItems[i].stockCount.toFixed(0)
                : '0'
                }`,
              data: {
                type: NOTIFICATIONS_TYPE.MERCHANT_LOW_STOCK_ALERT_PRODUCT,
              },
              userInfo: {
                type: NOTIFICATIONS_TYPE.MERCHANT_LOW_STOCK_ALERT_PRODUCT,
              },

              group: NOTIFICATIONS_TYPE.MERCHANT_LOW_STOCK_ALERT_PRODUCT,
              groupSummary: false,
            });

            warningOutletItemList.push({
              name: outletItems[i].name,
              quantity: outletItems[i].stockCount,
              stockWarningQuantity: outletItems[i].stockWarningQuantity,
            });
          }
        }

        if (toPushNotificationList.length > 0) {
          toPushNotificationList[
            toPushNotificationList.length - 1
          ].groupSummary = true;

          for (var i = 0; i < toPushNotificationList.length; i++) {
            PushNotification.localNotification(toPushNotificationList[i]);
          }
        }

        if (currOutlet && currOutlet.uniqueId) {
          var totalQuantityNew = warningOutletItemList.reduce(
            (accum, item) => accum + item.quantity,
            0,
          );
          var totalQuantityPrevious = (
            global.warningOutletItemList ? global.warningOutletItemList : []
          ).reduce((accum, item) => accum + item.quantity, 0);
          var totalWarningNew = warningOutletItemList.reduce(
            (accum, item) => accum + item.stockWarningQuantity,
            0,
          );
          var totalWarningPrevious = (
            global.warningOutletItemList ? global.warningOutletItemList : []
          ).reduce((accum, item) => accum + item.stockWarningQuantity, 0);

          if (
            Math.abs(totalQuantityNew - totalQuantityPrevious) > 0.01 ||
            Math.abs(totalWarningNew - totalWarningPrevious) > 0.01
          ) {
            console.log('update now');

            global.warningOutletItemList = warningOutletItemList;

            firestore()
              .collection(Collections.Outlet)
              .doc(currOutlet.uniqueId)
              .update({
                warningOutletItemList,

                updatedAt: Date.now(),
              });
          }
        }

        // if (currOutlet && currOutlet.uniqueId) {
        //   if (JSON.prune(warningOutletItemList) !== JSON.prune(currOutlet.warningOutletItemList ? currOutlet.warningOutletItemList : [])) {
        //     // not same, can update

        //     var totalQuantityNew = warningOutletItemList.reduce((accum, item) => accum + item.quantity, 0);
        //     var totalQuantityPrevious = (currOutlet.warningOutletItemList ? currOutlet.warningOutletItemList: []).reduce((accum, item) => accum + item.quantity, 0);
        //     var totalWarningNew = warningOutletItemList.reduce((accum, item) => accum + item.stockWarningQuantity, 0);
        //     var totalWarningPrevious = (currOutlet.warningOutletItemList ? currOutlet.warningOutletItemList: []).reduce((accum, item) => accum + item.stockWarningQuantity, 0);

        //       if ((totalQuantityNew !== totalQuantityPrevious || totalWarningNew !== totalWarningPrevious) &&
        //       warningOutletItemList.length !== (currOutlet.warningOutletItemList ? currOutlet.warningOutletItemList: []).length) {
        //       console.log('update now');

        //       // firestore().collection(Collections.Outlet).doc(currOutlet.uniqueId).update({
        //       //   warningOutletItemList: warningOutletItemList,

        //       //   updatedAt: Date.now(),
        //       // });
        //     }
        //   }
        // }
      });
    }
  }, [outletItems]);

  const timeCheckItem = CommonStore.useState(s => s.timeCheckItem);

  useEffect(() => {
    console.log('useEffect - Dashboard - 37');

    setInterval(() => {
      CommonStore.update(s => {
        s.timeCheckItem = Date.now();
      });
    }, 30000);
  }, []);

  useEffect(() => {
    console.log('useEffect - Dashboard - 38');

    if (userReservations.length > 0) {
      // for (var i = 0; i < userReservations.length; i++) {
      //     if (moment(userReservations[i].reservationTime).diff(moment(), 'hour') <= 1) {
      //         // alert

      //         PushNotification.localNotification({
      //             id: NOTIFICATIONS_ID.MERCHANT_RESERVATION_REMINDER,

      //             channelId: NOTIFICATIONS_CHANNEL.MERCHANT_RESERVATION_REMINDER,

      //             title: `${userReservations[i].pax} pax reservation at ${moment(userReservations[i].reservationTime).format('hh:mma')}`,
      //             message: `Name: ${userReservations[i].userName}\nPhone: ${userReservations[i].userPhone}\nEmail: ${userReservations[i].userEmail}`,
      //             data: {},
      //             userInfo: {},

      //             group: NOTIFICATIONS_TYPE.MERCHANT_RESERVATION_REMINDER,
      //             groupSummary: true,
      //         });
      //     }
      // }

      InteractionManager.runAfterInteractions(() => {
        var reservationToAlerts = userReservations.filter(
          o =>
            // o.status !== USER_RESERVATION_STATUS.CANCELED &&
            // o.status !== USER_RESERVATION_STATUS.NO_SHOW
            o.status !== USER_RESERVATION_STATUS.PENDING &&
            o.status !== USER_RESERVATION_STATUS.SEATED &&
            moment(o.reservationTime).isSameOrAfter(moment(), 'minute'), // this can define unit, otherwise will compare by millisecond diff
        );

        var reservationToAlertTemp = [];
        if (reservationToAlerts.length > 0) {
          reservationToAlertTemp = [...reservationToAlerts].sort(
            (a, b) => b.reservationTime - a.reservationTime,
          ); // if wrong switch a and b,
        }

        // if (reservationToAlerts) {
        //   // console.log('next reservation', moment(reservationToAlerts[0].reservationTime).format('YYYY MM DD hh:mm'))
        //   console.log('next reservetion temp', moment(reservationToAlertTemp[0].reservationTime).format('YYYY MM DD hh:mm'))
        // }
        // else {
        //   console.log('no more next reservation')
        // }

        if (reservationToAlerts) {
          for (var i = 0; i < reservationToAlertTemp.length; i++) {
            // console.log(`for next reservation ${i}`, moment(reservationToAlerts[i].reservationTime).format('YYYY MM DD hh:mm'))
            // console.log(`for next reservetion temp ${i}`, moment(reservationToAlertTemp[i].reservationTime).format('YYYY MM DD hh:mm'))
            if (
              moment(reservationToAlertTemp[i].reservationTime).diff(
                moment(),
                'hour',
              ) < 1 &&
              !showedReservation1HourDict[reservationToAlertTemp[i].uniqueId]
            ) {
              // means the diff is equal or less than 1 hour

              // if showed in 1 hour, no need show for 3 hours and 1 day d

              showedReservation1HourDict[
                reservationToAlertTemp[i].uniqueId
              ] = true;
              showedReservation3HoursDict[
                reservationToAlertTemp[i].uniqueId
              ] = true;
              showedReservation1DayDict[
                reservationToAlertTemp[i].uniqueId
              ] = true;

              PushNotification.localNotification({
                id: NOTIFICATIONS_ID.MERCHANT_RESERVATION_REMINDER,

                channelId: NOTIFICATIONS_CHANNEL.MERCHANT_RESERVATION_REMINDER,

                title: `${reservationToAlertTemp[i].pax
                  } pax reservation at ${moment(
                    reservationToAlertTemp[i].reservationTime,
                  ).format('Do MMM YY, h:mma')}`,
                message: `Name: ${reservationToAlertTemp[i].userName}`,
                // \nPhone: ${userReservations[i].userPhone}\nEmail: ${userReservations[i].userEmail}
                data: {
                  type: NOTIFICATIONS_TYPE.MERCHANT_RESERVATION_REMINDER,
                },
                userInfo: {
                  type: NOTIFICATIONS_TYPE.MERCHANT_RESERVATION_REMINDER,
                },

                group: NOTIFICATIONS_TYPE.MERCHANT_RESERVATION_REMINDER,
                groupSummary: true,
              });
            } else if (
              moment(reservationToAlertTemp[i].reservationTime).diff(
                moment(),
                'hour',
              ) < 3 &&
              !showedReservation3HoursDict[reservationToAlertTemp[i].uniqueId]
            ) {
              // means the diff is equal or less than 3 hours

              // if showed in 1 hour, no need show for 3 hours and 1 day d

              showedReservation3HoursDict[
                reservationToAlertTemp[i].uniqueId
              ] = true;
              showedReservation1DayDict[
                reservationToAlertTemp[i].uniqueId
              ] = true;

              PushNotification.localNotification({
                id: NOTIFICATIONS_ID.MERCHANT_RESERVATION_REMINDER,

                channelId: NOTIFICATIONS_CHANNEL.MERCHANT_RESERVATION_REMINDER,

                title: `${reservationToAlertTemp[i].pax
                  } pax reservation at ${moment(
                    reservationToAlertTemp[i].reservationTime,
                  ).format('Do MMM YY, h:mma')}`,
                message: `Name: ${reservationToAlertTemp[i].userName}`,
                // \nPhone: ${userReservations[i].userPhone}\nEmail: ${userReservations[i].userEmail}
                data: {
                  type: NOTIFICATIONS_TYPE.MERCHANT_RESERVATION_REMINDER,
                },
                userInfo: {
                  type: NOTIFICATIONS_TYPE.MERCHANT_RESERVATION_REMINDER,
                },

                group: NOTIFICATIONS_TYPE.MERCHANT_RESERVATION_REMINDER,
                groupSummary: true,
              });
            } else if (
              moment(reservationToAlertTemp[i].reservationTime).diff(
                moment(),
                'day',
              ) < 1 &&
              !showedReservation1DayDict[reservationToAlertTemp[i].uniqueId]
            ) {
              // means the diff is equal or less than 1 day

              // if showed in 1 hour, no need show for 3 hours and 1 day d

              showedReservation1DayDict[
                reservationToAlertTemp[i].uniqueId
              ] = true;

              PushNotification.localNotification({
                id: NOTIFICATIONS_ID.MERCHANT_RESERVATION_REMINDER,

                channelId: NOTIFICATIONS_CHANNEL.MERCHANT_RESERVATION_REMINDER,

                title: `${reservationToAlertTemp[i].pax
                  } pax reservation at ${moment(
                    reservationToAlertTemp[i].reservationTime,
                  ).format('Do MMM YY, h:mma')}`,
                message: `Name: ${reservationToAlertTemp[i].userName}`,
                // \nPhone: ${userReservations[i].userPhone}\nEmail: ${userReservations[i].userEmail}
                data: {
                  type: NOTIFICATIONS_TYPE.MERCHANT_RESERVATION_REMINDER,
                },
                userInfo: {
                  type: NOTIFICATIONS_TYPE.MERCHANT_RESERVATION_REMINDER,
                },

                group: NOTIFICATIONS_TYPE.MERCHANT_RESERVATION_REMINDER,
                groupSummary: true,
              });
            }
          }

          // var reminderTime1D = moment().diff(moment().subtract(1, 'day'), 'day'); // not like this
          // var reminderTime3H = moment().diff(moment().subtract(3, 'hour'))
          // var reminderTime1H = moment().diff(moment().subtract(1, 'hour'))
          // //have to fix refresh time
          // var compareTime1D = moment(reservationToAlerts.reservationTime).diff(reminderTime1D)
          // var compareTime3H = moment(reservationToAlerts.reservationTime).diff(reminderTime3H)
          // var compareTime1H = moment(reservationToAlerts.reservationTime).diff(reminderTime1H)

          // if (reservationToAlerts.status !== 'CANCELED' && moment(compareTime1D).format('YYYY MM DD hh:mm') === moment().format('YYYY MM DD hh:mm')) {
          //   PushNotification.localNotification({
          //     id: NOTIFICATIONS_ID.MERCHANT_RESERVATION_REMINDER,

          //     channelId: NOTIFICATIONS_CHANNEL.MERCHANT_RESERVATION_REMINDER,

          //     title: `${reservationToAlerts.pax} pax reservation at ${moment(
          //       reservationToAlerts.reservationTime,
          //     ).format('Do MMM YY, h:mma')}`,
          //     message: `Name: ${reservationToAlerts.userName}`,
          //     // \nPhone: ${userReservations[0].userPhone}\nEmail: ${userReservations[0].userEmail}
          //     data: {
          //       type: NOTIFICATIONS_TYPE.MERCHANT_RESERVATION_REMINDER,
          //     },
          //     userInfo: {
          //       type: NOTIFICATIONS_TYPE.MERCHANT_RESERVATION_REMINDER,
          //     },

          //     group: NOTIFICATIONS_TYPE.MERCHANT_RESERVATION_REMINDER,
          //     groupSummary: true,
          //   });
          // }
          // else if (reservationToAlerts.status !== 'CANCELED' && moment(compareTime3H).format('YYYY MM DD hh:mm') === moment().format('YYYY MM DD hh:mm')) {
          //   PushNotification.localNotification({
          //     id: NOTIFICATIONS_ID.MERCHANT_RESERVATION_REMINDER,

          //     channelId: NOTIFICATIONS_CHANNEL.MERCHANT_RESERVATION_REMINDER,

          //     title: `${reservationToAlerts.pax} pax reservation at ${moment(
          //       reservationToAlerts.reservationTime,
          //     ).format('Do MMM YY, h:mma')}`,
          //     message: `Name: ${reservationToAlerts.userName}`,
          //     // \nPhone: ${userReservations[0].userPhone}\nEmail: ${userReservations[0].userEmail}
          //     data: {
          //       type: NOTIFICATIONS_TYPE.MERCHANT_RESERVATION_REMINDER,
          //     },
          //     userInfo: {
          //       type: NOTIFICATIONS_TYPE.MERCHANT_RESERVATION_REMINDER,
          //     },

          //     group: NOTIFICATIONS_TYPE.MERCHANT_RESERVATION_REMINDER,
          //     groupSummary: true,
          //   });
          // }
          // else if (reservationToAlerts.status !== 'CANCELED' && moment(compareTime1H).format('YYYY MM DD hh:mm') === moment().format('YYYY MM DD hh:mm')) {

          // }
        }
      });
    }
  }, [userReservations, timeCheckItem]);

  useEffect(() => {
    console.log('useEffect - Dashboard - 39');

    if (userQueues.length > 0) {
      InteractionManager.runAfterInteractions(() => {
        PushNotification.localNotification({
          id: NOTIFICATIONS_ID.MERCHANT_QUEUE_REMINDER,

          channelId: NOTIFICATIONS_CHANNEL.MERCHANT_QUEUE_REMINDER,

          title: `${userQueues[userQueues.length - 1].pax
            } pax queue at number ${userQueues[userQueues.length - 1].number}`,
          message: `Name: ${userQueues[userQueues.length - 1].userName}`,
          // \nPhone: ${userQueues[0].userPhone}\nEmail: ${userQueues[0].userEmail}
          data: {
            type: NOTIFICATIONS_TYPE.MERCHANT_QUEUE_REMINDER,
          },
          userInfo: {
            type: NOTIFICATIONS_TYPE.MERCHANT_QUEUE_REMINDER,
          },

          group: NOTIFICATIONS_TYPE.MERCHANT_QUEUE_REMINDER,
          groupSummary: true,
        });
      });
    }
  }, [userQueues]);

  useEffect(() => {
    console.log('useEffect - Dashboard - 40');

    requestStoragePermission();

    saveMerchantLogoToBase64(merchantLogo);
  }, [merchantLogo]);

  const saveMerchantLogoToBase64 = async merchantLogoUrl => {
    if (merchantLogoUrl) {
      NetInfo.fetch().then(async state => {
        if (state.isInternetReachable) {
          var merchantLogoUrlParsed = '';

          if (
            merchantLogoUrl.startsWith('http') ||
            merchantLogoUrl.startsWith('file:///')
          ) {
            merchantLogoUrlParsed = merchantLogoUrl;
          } else {
            const cachedObj = await getCachedUrlContent(
              merchantLogoUrl,
              43800,
              null,
            );

            if (cachedObj && cachedObj.parsedUrl) {
              merchantLogoUrlParsed = cachedObj.parsedUrl;
            }
          }

          try {
            var saveDirectory = `${Platform.OS === 'ios'
              ? RNFS.DocumentDirectoryPath
              : `${RNFS.ExternalStorageDirectoryPath}/Documents`
              }/KooDooData/`;

            // let exists = await RNFS.exists(saveDirectory);
            // if (exists) {
            //   // exists call delete
            //   await RNFS.unlink(saveDirectory);
            // } else {
            // }

            await RNFS.mkdir(saveDirectory);
            // await RNFS.mkdir(saveDirectory + `merchantLogo/`);

            var imageType = merchantLogoUrlParsed.slice(
              merchantLogoUrlParsed.lastIndexOf('.'),
            );
            if (imageType.includes('?')) {
              imageType = imageType.slice(0, imageType.indexOf('?'));
            }

            var logoPath = `${Platform.OS === 'ios'
              ? RNFS.DocumentDirectoryPath
              : `${RNFS.ExternalStorageDirectoryPath}/Documents`
              }/KooDooData/merchantLogo/merchant-logo${imageType}`;
            var logoPathDir = `${Platform.OS === 'ios'
              ? RNFS.DocumentDirectoryPath
              : `${RNFS.ExternalStorageDirectoryPath}/Documents`
              }/KooDooData/merchantLogo`;

            // var logoPathBlob = `${Platform.OS === 'ios' ? RNFetchBlob.fs.dirs.DocumentDir : (RNFetchBlob.fs.dirs.SDCardDir + '/Documents')}/KooDooData/merchant-logo${imageType}`;

            await RNFS.exists(logoPathDir).then(async result => {
              return await new Promise(async resolve => {
                if (result) {
                  return await RNFS.unlink(logoPathDir).then(() => {
                    // RNFS.scanFile(logoPathDir).then(() => {
                    //   resolve();
                    // });

                    resolve();
                  });
                } else {
                  resolve();
                }
              }).then(async () => {
                await RNFS.mkdir(`${saveDirectory}merchantLogo/`);

                await RNFS.downloadFile({
                  fromUrl: merchantLogoUrlParsed,
                  toFile: logoPath,
                }).promise;

                // await AsyncStorage.setItem('merchantLogoUrl', `${Platform.OS === 'ios' ? RNFS.DocumentDirectoryPath : (RNFS.ExternalStorageDirectoryPath + '/Documents')}/KooDooData/merchant-logo${imageType}`);
                global.merchantLogoUrl = `${Platform.OS === 'ios'
                  ? RNFS.DocumentDirectoryPath
                  : `${RNFS.ExternalStorageDirectoryPath}/Documents`
                  }/KooDooData/merchantLogo/merchant-logo${imageType}`;
              });
            });
          } catch (ex) {
            console.error(ex);

            logToFile('saveMerchantLogoToBase64 - error');
            logToFile(ex);
          }
        }
      });

      // var merchantLogoUrlParsed = '';

      // if (
      //   merchantLogoUrl.startsWith('http') ||
      //   merchantLogoUrl.startsWith('file:///')
      // ) {
      //   merchantLogoUrlParsed = merchantLogoUrl;
      // } else {
      //   const cachedObj = await getCachedUrlContent(
      //     merchantLogoUrl,
      //     43800,
      //     null,
      //   );

      //   if (cachedObj && cachedObj.parsedUrl) {
      //     merchantLogoUrlParsed = cachedObj.parsedUrl;
      //   }
      // }

      // try {
      //   var saveDirectory = `${Platform.OS === 'ios' ? RNFS.DocumentDirectoryPath : (RNFS.ExternalStorageDirectoryPath + '/Documents')}/KooDooData/`;

      //   // let exists = await RNFS.exists(saveDirectory);
      //   // if (exists) {
      //   //   // exists call delete
      //   //   await RNFS.unlink(saveDirectory);
      //   // } else {
      //   // }

      //   await RNFS.mkdir(saveDirectory);

      //   var imageType = merchantLogoUrlParsed.slice(merchantLogoUrlParsed.lastIndexOf('.'));

      //   RNFS.downloadFile({
      //     fromUrl: merchantLogoUrlParsed,
      //     toFile: `${Platform.OS === 'ios' ? RNFS.DocumentDirectoryPath : (RNFS.ExternalStorageDirectoryPath + '/Documents')}/KooDooData/merchant-logo${imageType}`,
      //   }).promise;

      //   // await AsyncStorage.setItem('merchantLogoUrl', `${Platform.OS === 'ios' ? RNFS.DocumentDirectoryPath : (RNFS.ExternalStorageDirectoryPath + '/Documents')}/KooDooData/merchant-logo${imageType}`);
      //   global.merchantLogoUrl = `${Platform.OS === 'ios' ? RNFS.DocumentDirectoryPath : (RNFS.ExternalStorageDirectoryPath + '/Documents')}/KooDooData/merchant-logo${imageType}`;
      // }
      // catch (ex) {
      //   console.error(ex);
      // }
    }

    const cachedObjKoodooLogo = await getCachedUrlContent(
      Platform.OS === 'ios'
        ? '/mykoodoo/koodoo-printing-logo.png'
        : '/mykoodoo/koodoo-printing-logo-android.png',
      43800,
      null,
    );

    var koodooLogoUrlParsed = '';
    if (cachedObjKoodooLogo && cachedObjKoodooLogo.parsedUrl) {
      koodooLogoUrlParsed = cachedObjKoodooLogo.parsedUrl;
    }

    try {
      var saveDirectory = `${Platform.OS === 'ios'
        ? RNFS.DocumentDirectoryPath
        : `${RNFS.ExternalStorageDirectoryPath}/Documents`
        }/KooDooData/`;

      var imageType = koodooLogoUrlParsed.slice(
        koodooLogoUrlParsed.lastIndexOf('.'),
      );
      if (imageType.includes('?')) {
        imageType = imageType.slice(0, imageType.indexOf('?'));
      }

      RNFS.downloadFile({
        fromUrl: koodooLogoUrlParsed,
        toFile: `${Platform.OS === 'ios'
          ? RNFS.DocumentDirectoryPath
          : `${RNFS.ExternalStorageDirectoryPath}/Documents`
          }/KooDooData/koodoo-logo${imageType}`,
      }).promise;

      // await AsyncStorage.setItem('koodooLogoUrl', `${Platform.OS === 'ios' ? RNFS.DocumentDirectoryPath : (RNFS.ExternalStorageDirectoryPath + '/Documents')}/KooDooData/koodoo-logo${imageType}`);
      global.koodooLogoUrl = `${Platform.OS === 'ios'
        ? RNFS.DocumentDirectoryPath
        : `${RNFS.ExternalStorageDirectoryPath}/Documents`
        }/KooDooData/koodoo-logo${imageType}`;
    } catch (ex) {
      console.error(ex);
    }

    // await AsyncStorage.setItem('koodooLogoUrl', koodooLogoUrlParsed);

    setTimeout(() => {
      CommonStore.update(s => {
        s.isOfflineReady = true;
      });
    }, 1000);
  };

  ////////////////////////////////////////////////////////

  // received promotions effect (discounted items price, bundle deal, etc), ported from user app

  const timestampPromotion = CommonStore.useState(s => s.timestampPromotion);

  useEffect(() => {
    console.log('useEffect - Dashboard - 41');

    setInterval(() => {
      CommonStore.update(s => {
        s.timestampPromotion = Date.now();
      });
    }, 60000);
  }, []);

  useEffect(() => {
    console.log('useEffect - Dashboard - 42');

    InteractionManager.runAfterInteractions(() => {
      var availablePromotionsTemp = [];
      var availablePromoCodePromotionsTemp = [];
      // var availablePointsRedeemPackagesTemp = [];
      // var availableLoyaltyCampaigns = [];

      for (var i = 0; i < selectedOutletPromotions.length; i++) {
        var promoTimeStart = moment().set({
          hour: moment(selectedOutletPromotions[i].promoTimeStart).hour(),
          minute: moment(selectedOutletPromotions[i].promoTimeStart).minute(),
        });

        var promoTimeEnd = moment().set({
          hour: moment(selectedOutletPromotions[i].promoTimeEnd).hour(),
          minute: moment(selectedOutletPromotions[i].promoTimeEnd).minute(),
        });

        if (
          moment().isSameOrAfter(
            selectedOutletPromotions[i].promoDateStart,
            'day',
          ) &&
          moment().isSameOrBefore(
            selectedOutletPromotions[i].promoDateEnd,
            'day',
          ) &&
          moment().isSameOrAfter(promoTimeStart) &&
          moment().isBefore(promoTimeEnd) &&
          selectedOutletPromotions[i].effectiveTypeOptions &&
          selectedOutletPromotions[i].effectiveTypeOptions.length > 0 &&
          selectedOutletPromotions[i].effectiveTypeOptions.find(day => {
            if (day === moment().format('dddd').toUpperCase()) {
              return true;
            }
          })
        ) {
          var isValid = false;

          if (
            selectedOutletPromotions[i].orderTypes &&
            selectedOutletPromotions[i].orderTypes.includes(orderType)
          ) {
            // included, can proceed
          } else if (
            selectedOutletPromotions[i].orderTypes &&
            ((orderType === ORDER_TYPE.DINEIN &&
              selectedOutletPromotions[i].orderTypes.includes(
                CHANNEL_TYPE.DINEIN_POS,
              )) ||
              (orderType === ORDER_TYPE.PICKUP &&
                selectedOutletPromotions[i].orderTypes.includes(
                  CHANNEL_TYPE.PICKUP_POS,
                )))
          ) {
            // included, can proceed
          } else {
            // not included, skip

            continue;
          }

          if (selectedCustomerEdit === null) {
            // if (selectedOutletPromotions[i].applyBefore === APPLY_BEFORE.ORDER_PLACED || selectedOutletPromotions[i].applyBefore === undefined) {
            //   isValid = true;
            // }
            // isValid = true;

            if (
              selectedOutletPromotions[i].targetSegmentGroupList.includes(
                'EVERYONE',
              )
            ) {
              isValid = true;
            }

            if (isValid) {
              if (
                selectedOutletPromotions[i].loyaltyTierOrderIndexList ===
                undefined
              ) {
                isValid = true;
              } else {
                if (
                  selectedOutletPromotions[i].loyaltyTierOrderIndexList
                    .length === 0
                ) {
                  isValid = true;
                } else if (
                  selectedOutletPromotions[
                    i
                  ].loyaltyTierOrderIndexList.includes('no-tier')
                ) {
                  isValid = true;
                } else {
                  isValid = false;
                }
              }
            }
          } else {
            for (
              var j = 0;
              j < selectedOutletPromotions[i].targetSegmentGroupList.length;
              j++
            ) {
              if (selectedOutletPromotions[i].targetSegmentGroupList[j]) {
                var crmSegment =
                  crmSegmentsDict[
                  selectedOutletPromotions[i].targetSegmentGroupList[j]
                  ];

                if (
                  selectedOutletPromotions[i].targetSegmentGroupList[j] ===
                  'EVERYONE'
                ) {
                  isValid = true;
                  break;
                } else {
                  if (crmSegment) {
                    var { crmUserTagIdList } = crmSegment;

                    for (var k = 0; k < crmUserTagIdList.length; k++) {
                      var crmUserTag = crmUserTagsDict[crmUserTagIdList[k]];

                      if (crmUserTag) {
                        if (
                          crmUserTag.emailList.includes(
                            selectedCustomerEdit.email,
                          ) ||
                          crmUserTag.phoneList.includes(
                            selectedCustomerEdit.number,
                          )
                        ) {
                          isValid = true;
                          break;
                        }
                      }
                    }
                  }

                  if (isValid) {
                    break;
                  }
                }
              }
            }

            if (isValid) {
              if (
                selectedOutletPromotions[i].loyaltyTierOrderIndexList ===
                undefined
              ) {
                isValid = true;
              } else {
                if (
                  selectedOutletPromotions[i].loyaltyTierOrderIndexList
                    .length === 0
                ) {
                  isValid = true;
                } else if (
                  selectedCustomerEdit &&
                  (selectedCustomerEdit.levelOrderIndex === undefined ||
                    selectedCustomerEdit.levelOrderIndex === -1) &&
                  selectedOutletPromotions[
                    i
                  ].loyaltyTierOrderIndexList.includes('no-tier')
                ) {
                  isValid = true;
                } else if (
                  selectedCustomerEdit &&
                  typeof selectedCustomerEdit.levelOrderIndex === 'number' &&
                  selectedOutletPromotions[
                    i
                  ].loyaltyTierOrderIndexList.includes(
                    selectedCustomerEdit.levelOrderIndex,
                  )
                ) {
                  isValid = true;
                } else {
                  isValid = false;
                }
              }
            }
          }

          // if (userGroups.includes(selectedOutletPromotions[i].targetUserGroup)) {
          //     isValid = true;
          // }

          // if (selectedOutletCRMTagsDict[selectedOutletPromotions[i].targetUserGroup]) {
          //     const currCrmUserTag = selectedOutletCRMTagsDict[selectedOutletPromotions[i].targetUserGroup];

          //     if (currCrmUserTag.emailList.includes(email)) {
          //         // means got

          //         isValid = true;
          //     }
          // }

          if (isValid) {
            if (selectedOutletPromotions[i].usePromoCode) {
              if (
                selectedPromoCodePromotion &&
                selectedPromoCodePromotion.uniqueId ===
                selectedOutletPromotions[i].uniqueId
              ) {
                availablePromotionsTemp.push(selectedOutletPromotions[i]);
              }

              availablePromoCodePromotionsTemp.push(
                selectedOutletPromotions[i],
              );
            } else {
              availablePromotionsTemp.push(selectedOutletPromotions[i]);
            }
          }
        }
      }

      // for (var i = 0; i < selectedOutletPointsRedeemPackages.length; i++) {
      //     if (moment().isSameOrAfter(selectedOutletPointsRedeemPackages[i].startDate) && moment().isBefore(selectedOutletPointsRedeemPackages[i].endDate)) {
      //         var isValid = false;

      //         if (userGroups.includes(selectedOutletPointsRedeemPackages[i].targetUserGroup)) {
      //             isValid = true;
      //         }

      //         if (selectedOutletCRMTagsDict[selectedOutletPointsRedeemPackages[i].targetUserGroup]) {
      //             const currCrmUserTag = selectedOutletCRMTagsDict[selectedOutletPointsRedeemPackages[i].targetUserGroup];

      //             if (currCrmUserTag.emailList.includes(email)) {
      //                 // means got

      //                 isValid = true;
      //             }
      //         }

      //         //////////////////////////////////

      //         if (selectedOutletCRMUser && selectedOutletCRMUser.pointsRedeemPackageDisableDict) {
      //             if (selectedOutletCRMUser.pointsRedeemPackageDisableDict[selectedOutletPointsRedeemPackages[i].uniqueId]) {
      //                 isValid = false;
      //             }
      //         }

      //         //////////////////////////////////

      //         if (isValid) {
      //             availablePointsRedeemPackagesTemp.push(selectedOutletPointsRedeemPackages[i]);
      //         }
      //     }
      // }

      // /////////////////////////////////////////////////////////////////////////

      // var combinedLoyaltyCampaigns = [];
      // for (var i = 0; i < selectedOutletUserLoyaltyCampaigns.length; i++) {
      //     for (var j = 0; j < selectedOutletLoyaltyCampaigns.length; j++) {
      //         if (selectedOutletUserLoyaltyCampaigns[i].loyaltyCampaignId === selectedOutletLoyaltyCampaigns[i].uniqueId &&
      //             selectedOutletUserLoyaltyCampaigns[i].redeemDate === null) {
      //             // means this user got the 'voucher' for this campaign

      //             combinedLoyaltyCampaigns.push({
      //                 ...selectedOutletLoyaltyCampaigns[j],
      //                 ...selectedOutletUserLoyaltyCampaigns[i],
      //             });

      //             break;
      //         }
      //     }
      // }

      // for (var i = 0; i < combinedLoyaltyCampaigns.length; i++) {
      //     var isValid = false;

      //     if (userGroups.includes(combinedLoyaltyCampaigns[i].targetUserGroup)) {
      //         isValid = true;
      //     }

      //     if (selectedOutletCRMTagsDict[combinedLoyaltyCampaigns[i].targetUserGroup]) {
      //         const currCrmUserTag = selectedOutletCRMTagsDict[combinedLoyaltyCampaigns[i].targetUserGroup];

      //         if (currCrmUserTag.emailList.includes(email)) {
      //             // means got

      //             isValid = true;
      //         }
      //     }

      //     if (isValid) {
      //         availableLoyaltyCampaigns.push(combinedLoyaltyCampaigns[i]);
      //     }
      // }

      availablePromotionsTemp = availablePromotionsTemp.sort((a, b) =>
        a.usePromoCode ? 1 : -1,
      );

      /////////////////////////////////////////////////////////////////////////

      // var isChanged = false;
      // if (availablePromotions.length !== availablePromotionsTemp.length) {
      //   isChanged = true;
      // } else {
      //   for (var i = 0; i < availablePromotions.length; i++) {
      //     const isEqual = isObjectEqual(
      //       availablePromotions[i],
      //       availablePromotionsTemp[i],
      //     );
      //     if (!isEqual) {
      //       isChanged = true;
      //       break;
      //     }
      //   }
      // }

      // /////////////////////////////////////////////////////////////////////////

      // if (isChanged) {
      //   CommonStore.update((s) => {
      //     s.availablePromotions = availablePromotionsTemp;
      //   });
      // }

      // /////////////////////////////////////////////////////////////////////////

      // isChanged = false;
      // if (availablePromoCodePromotions.length !== availablePromoCodePromotionsTemp.length) {
      //   isChanged = true;
      // } else {
      //   for (var i = 0; i < availablePromoCodePromotions.length; i++) {
      //     const isEqual = isObjectEqual(
      //       availablePromoCodePromotions[i],
      //       availablePromoCodePromotionsTemp[i],
      //     );
      //     if (!isEqual) {
      //       isChanged = true;
      //       break;
      //     }
      //   }
      // }

      // /////////////////////////////////////////////////////////////////////////

      // if (isChanged) {
      //   CommonStore.update((s) => {
      //     s.availablePromoCodePromotions = availablePromoCodePromotionsTemp;
      //   });
      // }

      global.availablePromotions = availablePromotionsTemp;

      // 2024-04-0 - compare before update

      let isChanged = false;

      const isEqual =
        JSON.prune(availablePromotions) === JSON.prune(availablePromotionsTemp);
      if (!isEqual) {
        isChanged = true;
      }

      const isEqualPromoCode =
        JSON.prune(availablePromoCodePromotions) ===
        JSON.prune(availablePromoCodePromotionsTemp);
      if (!isEqualPromoCode) {
        isChanged = true;
      }

      if (isChanged) {
        CommonStore.update(s => {
          s.availablePromotions = availablePromotionsTemp;
          s.availablePromoCodePromotions = availablePromoCodePromotionsTemp;
          // s.availablePointsRedeemPackages = availablePointsRedeemPackagesTemp;
          // s.availableLoyaltyCampaigns = availableLoyaltyCampaigns;
        });
      }
    });
  }, [
    selectedOutletPromotions,
    // selectedOutletPointsRedeemPackages,
    // userGroups,
    // email,

    // selectedOutletCRMTagsDict,
    // selectedOutletCRMUser,

    // selectedOutletLoyaltyCampaigns,
    // selectedOutletUserLoyaltyCampaigns,

    selectedCustomerEdit,
    crmSegmentsDict,
    crmUserTagsDict,

    selectedPromoCodePromotion,

    orderType,

    timestampPromotion, // might affect performance
  ]);

  useEffect(() => {
    console.log('useEffect - Dashboard - 43');

    InteractionManager.runAfterInteractions(() => {
      var overrideItemPriceSkuDictTemp = {};
      var amountOffItemSkuDictTemp = {};
      var percentageOffItemSkuDictTemp = {};
      var buy1Free1ItemSkuDictTemp = {};
      var deliveryItemSkuDictTemp = {};
      var takeawayItemSkuDictTemp = {};

      var overrideCategoryPriceNameDictTemp = {};
      var amountOffCategoryNameDictTemp = {};
      var percentageOffCategoryNameDictTemp = {};
      var buy1Free1CategoryNameDictTemp = {};
      var deliveryCategoryNameDictTemp = {};
      var takeawayCategoryNameDictTemp = {};

      for (var i = 0; i < availablePromotions.length; i++) {
        if (
          availablePromotions[i].promotionType ===
          PROMOTION_TYPE.OVERRIDE_EXISTING_PRICE
        ) {
          for (var j = 0; j < availablePromotions[i].criteriaList.length; j++) {
            const criteria = availablePromotions[i].criteriaList[j];

            if (
              criteria.variation === PROMOTION_TYPE_VARIATION.SPECIFIC_PRODUCTS
            ) {
              for (var k = 0; k < criteria.variationItemsSku.length; k++) {
                overrideItemPriceSkuDictTemp[criteria.variationItemsSku[k]] = {
                  promotionId: availablePromotions[i].uniqueId,
                  overridePrice: parseFloat(criteria.priceBeforeTax),
                  minSpend: availablePromotions[i].minSpend || 0,

                  applyBefore:
                    availablePromotions[i].applyBefore ||
                    APPLY_BEFORE.ORDER_PLACED,
                  applyDiscountPer:
                    availablePromotions[i].applyDiscountPer ||
                    APPLY_DISCOUNT_PER.ORDER,

                  usePromoCode: availablePromotions[i].usePromoCode || false,
                  promoCode: availablePromotions[i].promoCode || '',
                };
              }
            } else if (
              criteria.variation ===
              PROMOTION_TYPE_VARIATION.PRODUCT_OF_CATEGORY
            ) {
              for (var k = 0; k < criteria.variationItemsSku.length; k++) {
                overrideCategoryPriceNameDictTemp[
                  criteria.variationItemsSku[k]
                ] = {
                  promotionId: availablePromotions[i].uniqueId,
                  overridePrice: parseFloat(criteria.priceBeforeTax),
                  minSpend: availablePromotions[i].minSpend || 0,

                  applyBefore:
                    availablePromotions[i].applyBefore ||
                    APPLY_BEFORE.ORDER_PLACED,
                  applyDiscountPer:
                    availablePromotions[i].applyDiscountPer ||
                    APPLY_DISCOUNT_PER.ORDER,

                  usePromoCode: availablePromotions[i].usePromoCode || false,
                  promoCode: availablePromotions[i].promoCode || '',
                };
              }
            }
          }
        } else if (
          availablePromotions[i].promotionType ===
          PROMOTION_TYPE.TAKE_AMOUNT_OFF
        ) {
          for (var j = 0; j < availablePromotions[i].criteriaList.length; j++) {
            const criteria = availablePromotions[i].criteriaList[j];

            if (
              criteria.variation === PROMOTION_TYPE_VARIATION.SPECIFIC_PRODUCTS
            ) {
              for (var k = 0; k < criteria.variationItemsSku.length; k++) {
                amountOffItemSkuDictTemp[criteria.variationItemsSku[k]] = {
                  promotionId: availablePromotions[i].uniqueId,
                  amountOff: criteria.amountOff,
                  maxQuantity: criteria.maxQuantity,
                  minQuantity: criteria.minQuantity,

                  quantityMin: criteria.quantityMin,
                  quantityMax: criteria.quantityMax,
                  priceMin: criteria.priceMin,
                  priceMax: criteria.priceMax,

                  applyBefore:
                    availablePromotions[i].applyBefore ||
                    APPLY_BEFORE.ORDER_PLACED,
                  applyDiscountPer:
                    availablePromotions[i].applyDiscountPer ||
                    APPLY_DISCOUNT_PER.ORDER,

                  usePromoCode: availablePromotions[i].usePromoCode || false,
                  promoCode: availablePromotions[i].promoCode || '',

                  minPriceToDiscounted: criteria.minPriceToDiscounted
                    ? criteria.minPriceToDiscounted
                    : 0,

                  variationRp: criteria.variationRp
                    ? criteria.variationRp
                    : PROMOTION_TYPE_VARIATION.PRODUCT_OF_CATEGORY,
                  variationItemsRp: criteria.variationItemsRp
                    ? criteria.variationItemsRp
                    : [],
                  variationItemsSkuRp: criteria.variationItemsSkuRp
                    ? criteria.variationItemsSkuRp
                    : [],
                  qtyRp: criteria.qtyRp ? criteria.qtyRp : 0,

                  typeRp: criteria.typeRp
                    ? criteria.typeRp
                    : CONDITION_TYPE.AND,
                };
              }
            } else if (
              criteria.variation ===
              PROMOTION_TYPE_VARIATION.PRODUCT_OF_CATEGORY
            ) {
              for (var k = 0; k < criteria.variationItemsSku.length; k++) {
                amountOffCategoryNameDictTemp[criteria.variationItemsSku[k]] = {
                  promotionId: availablePromotions[i].uniqueId,
                  amountOff: criteria.amountOff,
                  maxQuantity: criteria.maxQuantity,
                  minQuantity: criteria.minQuantity,

                  quantityMin: criteria.quantityMin,
                  quantityMax: criteria.quantityMax,
                  priceMin: criteria.priceMin,
                  priceMax: criteria.priceMax,

                  applyBefore:
                    availablePromotions[i].applyBefore ||
                    APPLY_BEFORE.ORDER_PLACED,
                  applyDiscountPer:
                    availablePromotions[i].applyDiscountPer ||
                    APPLY_DISCOUNT_PER.ORDER,

                  usePromoCode: availablePromotions[i].usePromoCode || false,
                  promoCode: availablePromotions[i].promoCode || '',

                  minPriceToDiscounted: criteria.minPriceToDiscounted
                    ? criteria.minPriceToDiscounted
                    : 0,

                  variationRp: criteria.variationRp
                    ? criteria.variationRp
                    : PROMOTION_TYPE_VARIATION.PRODUCT_OF_CATEGORY,
                  variationItemsRp: criteria.variationItemsRp
                    ? criteria.variationItemsRp
                    : [],
                  variationItemsSkuRp: criteria.variationItemsSkuRp
                    ? criteria.variationItemsSkuRp
                    : [],
                  qtyRp: criteria.qtyRp ? criteria.qtyRp : 0,

                  typeRp: criteria.typeRp
                    ? criteria.typeRp
                    : CONDITION_TYPE.AND,
                };
              }
            }
          }
        } else if (
          availablePromotions[i].promotionType ===
          PROMOTION_TYPE.TAKE_PERCENTAGE_OFF
        ) {
          for (var j = 0; j < availablePromotions[i].criteriaList.length; j++) {
            const criteria = availablePromotions[i].criteriaList[j];

            if (
              criteria.variation === PROMOTION_TYPE_VARIATION.SPECIFIC_PRODUCTS
            ) {
              for (var k = 0; k < criteria.variationItemsSku.length; k++) {
                percentageOffItemSkuDictTemp[criteria.variationItemsSku[k]] = {
                  promotionId: availablePromotions[i].uniqueId,
                  percentageOff: criteria.percentageOff,
                  maxQuantity: criteria.maxQuantity,
                  minQuantity: criteria.minQuantity,

                  quantityMin: criteria.quantityMin,
                  quantityMax: criteria.quantityMax,
                  priceMin: criteria.priceMin,
                  priceMax: criteria.priceMax,

                  applyBefore:
                    availablePromotions[i].applyBefore ||
                    APPLY_BEFORE.ORDER_PLACED,
                  applyDiscountPer:
                    availablePromotions[i].applyDiscountPer ||
                    APPLY_DISCOUNT_PER.ORDER,

                  usePromoCode: availablePromotions[i].usePromoCode || false,
                  promoCode: availablePromotions[i].promoCode || '',

                  minPriceToDiscounted: criteria.minPriceToDiscounted
                    ? criteria.minPriceToDiscounted
                    : 0,

                  variationRp: criteria.variationRp
                    ? criteria.variationRp
                    : PROMOTION_TYPE_VARIATION.PRODUCT_OF_CATEGORY,
                  variationItemsRp: criteria.variationItemsRp
                    ? criteria.variationItemsRp
                    : [],
                  variationItemsSkuRp: criteria.variationItemsSkuRp
                    ? criteria.variationItemsSkuRp
                    : [],
                  qtyRp: criteria.qtyRp ? criteria.qtyRp : 0,

                  typeRp: criteria.typeRp
                    ? criteria.typeRp
                    : CONDITION_TYPE.AND,
                };
              }
            } else if (
              criteria.variation ===
              PROMOTION_TYPE_VARIATION.PRODUCT_OF_CATEGORY
            ) {
              for (var k = 0; k < criteria.variationItemsSku.length; k++) {
                percentageOffCategoryNameDictTemp[
                  criteria.variationItemsSku[k]
                ] = {
                  promotionId: availablePromotions[i].uniqueId,
                  percentageOff: criteria.percentageOff,
                  maxQuantity: criteria.maxQuantity,
                  minQuantity: criteria.minQuantity,

                  quantityMin: criteria.quantityMin,
                  quantityMax: criteria.quantityMax,
                  priceMin: criteria.priceMin,
                  priceMax: criteria.priceMax,

                  applyBefore:
                    availablePromotions[i].applyBefore ||
                    APPLY_BEFORE.ORDER_PLACED,
                  applyDiscountPer:
                    availablePromotions[i].applyDiscountPer ||
                    APPLY_DISCOUNT_PER.ORDER,

                  usePromoCode: availablePromotions[i].usePromoCode || false,
                  promoCode: availablePromotions[i].promoCode || '',

                  minPriceToDiscounted: criteria.minPriceToDiscounted
                    ? criteria.minPriceToDiscounted
                    : 0,

                  variationRp: criteria.variationRp
                    ? criteria.variationRp
                    : PROMOTION_TYPE_VARIATION.PRODUCT_OF_CATEGORY,
                  variationItemsRp: criteria.variationItemsRp
                    ? criteria.variationItemsRp
                    : [],
                  variationItemsSkuRp: criteria.variationItemsSkuRp
                    ? criteria.variationItemsSkuRp
                    : [],
                  qtyRp: criteria.qtyRp ? criteria.qtyRp : 0,

                  typeRp: criteria.typeRp
                    ? criteria.typeRp
                    : CONDITION_TYPE.AND,
                };
              }
            }
          }
        } else if (
          availablePromotions[i].promotionType ===
          PROMOTION_TYPE.BUY_A_GET_B_FOR_FREE
        ) {
          for (var j = 0; j < availablePromotions[i].criteriaList.length; j++) {
            const criteria = availablePromotions[i].criteriaList[j];

            if (
              criteria.buyVariation ===
              PROMOTION_TYPE_VARIATION.SPECIFIC_PRODUCTS
            ) {
              for (var k = 0; k < criteria.buyVariationItemsSku.length; k++) {
                buy1Free1ItemSkuDictTemp[criteria.buyVariationItemsSku[k]] = {
                  promotionId: availablePromotions[i].uniqueId,
                  buyAmount: criteria.buyAmount,
                  getAmount: criteria.getAmount,
                  getPrice: criteria.getPrice,
                  getVariation: criteria.getVariation,
                  getVariationItems: criteria.getVariationItems,
                  getVariationItemsSku: criteria.getVariationItemsSku,

                  applyBefore:
                    availablePromotions[i].applyBefore ||
                    APPLY_BEFORE.ORDER_PLACED,
                  applyDiscountPer:
                    availablePromotions[i].applyDiscountPer ||
                    APPLY_DISCOUNT_PER.ORDER,

                  usePromoCode: availablePromotions[i].usePromoCode || false,
                  promoCode: availablePromotions[i].promoCode || '',
                };
              }
            } else if (
              criteria.buyVariation ===
              PROMOTION_TYPE_VARIATION.PRODUCT_OF_CATEGORY
            ) {
              for (var k = 0; k < criteria.buyVariationItemsSku.length; k++) {
                buy1Free1CategoryNameDictTemp[
                  criteria.buyVariationItemsSku[k]
                ] = {
                  promotionId: availablePromotions[i].uniqueId,
                  buyAmount: criteria.buyAmount,
                  getAmount: criteria.getAmount,
                  getPrice: criteria.getPrice,
                  getVariation: criteria.getVariation,
                  getVariationItems: criteria.getVariationItems,
                  getVariationItemsSku: criteria.getVariationItemsSku,

                  applyBefore:
                    availablePromotions[i].applyBefore ||
                    APPLY_BEFORE.ORDER_PLACED,
                  applyDiscountPer:
                    availablePromotions[i].applyDiscountPer ||
                    APPLY_DISCOUNT_PER.ORDER,

                  usePromoCode: availablePromotions[i].usePromoCode || false,
                  promoCode: availablePromotions[i].promoCode || '',
                };
              }
            }
          }
        } else if (
          availablePromotions[i].promotionType === PROMOTION_TYPE.DELIVERY
        ) {
          for (var j = 0; j < availablePromotions[i].criteriaList.length; j++) {
            const criteria = availablePromotions[i].criteriaList[j];

            if (
              criteria.variation === PROMOTION_TYPE_VARIATION.SPECIFIC_PRODUCTS
            ) {
              for (var k = 0; k < criteria.variationItemsSku.length; k++) {
                deliveryItemSkuDictTemp[criteria.variationItemsSku[k]] = {
                  deliveryDiscountAmount: criteria.deliveryDiscountAmount,
                  deliveryDiscountAboveAmount:
                    criteria.deliveryDiscountAboveAmount,

                  applyBefore:
                    availablePromotions[i].applyBefore ||
                    APPLY_BEFORE.ORDER_PLACED,
                  applyDiscountPer:
                    availablePromotions[i].applyDiscountPer ||
                    APPLY_DISCOUNT_PER.ORDER,

                  usePromoCode: availablePromotions[i].usePromoCode || false,
                  promoCode: availablePromotions[i].promoCode || '',
                };
              }
            } else if (
              criteria.variation ===
              PROMOTION_TYPE_VARIATION.PRODUCT_OF_CATEGORY
            ) {
              for (var k = 0; k < criteria.variationItemsSku.length; k++) {
                deliveryCategoryNameDictTemp[criteria.variationItemsSku[k]] = {
                  deliveryDiscountAmount: criteria.deliveryDiscountAmount,
                  deliveryDiscountAboveAmount:
                    criteria.deliveryDiscountAboveAmount,

                  applyBefore:
                    availablePromotions[i].applyBefore ||
                    APPLY_BEFORE.ORDER_PLACED,
                  applyDiscountPer:
                    availablePromotions[i].applyDiscountPer ||
                    APPLY_DISCOUNT_PER.ORDER,

                  usePromoCode: availablePromotions[i].usePromoCode || false,
                  promoCode: availablePromotions[i].promoCode || '',
                };
              }
            }
          }
        } else if (
          availablePromotions[i].promotionType === PROMOTION_TYPE.TAKEAWAY
        ) {
          for (var j = 0; j < availablePromotions[i].criteriaList.length; j++) {
            const criteria = availablePromotions[i].criteriaList[j];

            if (
              criteria.variation === PROMOTION_TYPE_VARIATION.SPECIFIC_PRODUCTS
            ) {
              for (var k = 0; k < criteria.variationItemsSku.length; k++) {
                takeawayItemSkuDictTemp[criteria.variationItemsSku[k]] = {
                  promotionId: availablePromotions[i].uniqueId,
                  takeawayFreeFlag: criteria.takeawayFreeFlag,
                  takeawayFreeAboveAmount: criteria.takeawayFreeAboveAmount,
                  takeawayDiscountAmount: criteria.takeawayDiscountAmount,
                  takeawayDiscountAboveAmount:
                    criteria.takeawayDiscountAboveAmount,

                  applyBefore:
                    availablePromotions[i].applyBefore ||
                    APPLY_BEFORE.ORDER_PLACED,
                  applyDiscountPer:
                    availablePromotions[i].applyDiscountPer ||
                    APPLY_DISCOUNT_PER.ORDER,

                  usePromoCode: availablePromotions[i].usePromoCode || false,
                  promoCode: availablePromotions[i].promoCode || '',
                };
              }
            } else if (
              criteria.variation ===
              PROMOTION_TYPE_VARIATION.PRODUCT_OF_CATEGORY
            ) {
              for (var k = 0; k < criteria.variationItemsSku.length; k++) {
                takeawayCategoryNameDictTemp[criteria.variationItemsSku[k]] = {
                  promotionId: availablePromotions[i].uniqueId,
                  takeawayFreeFlag: criteria.takeawayFreeFlag,
                  takeawayFreeAboveAmount: criteria.takeawayFreeAboveAmount,
                  takeawayDiscountAmount: criteria.takeawayDiscountAmount,
                  takeawayDiscountAboveAmount:
                    criteria.takeawayDiscountAboveAmount,

                  applyBefore:
                    availablePromotions[i].applyBefore ||
                    APPLY_BEFORE.ORDER_PLACED,
                  applyDiscountPer:
                    availablePromotions[i].applyDiscountPer ||
                    APPLY_DISCOUNT_PER.ORDER,

                  usePromoCode: availablePromotions[i].usePromoCode || false,
                  promoCode: availablePromotions[i].promoCode || '',
                };
              }
            }
          }
        }
      }

      CommonStore.update(s => {
        s.overrideItemPriceSkuDict = overrideItemPriceSkuDictTemp;
        s.amountOffItemSkuDict = amountOffItemSkuDictTemp;
        s.percentageOffItemSkuDict = percentageOffItemSkuDictTemp;
        s.buy1Free1ItemSkuDict = buy1Free1ItemSkuDictTemp;
        s.deliveryItemSkuDict = deliveryItemSkuDictTemp;
        s.takeawayItemSkuDict = takeawayItemSkuDictTemp;

        s.overrideCategoryPriceNameDict = overrideCategoryPriceNameDictTemp;
        s.amountOffCategoryNameDict = amountOffCategoryNameDictTemp;
        s.percentageOffCategoryNameDict = percentageOffCategoryNameDictTemp;
        s.buy1Free1CategoryNameDict = buy1Free1CategoryNameDictTemp;
        s.deliveryCategoryNameDict = deliveryCategoryNameDictTemp;
        s.takeawayCategoryNameDict = takeawayCategoryNameDictTemp;
      });
    });
  }, [availablePromotions]);

  useEffect(() => {
    console.log('useEffect - Dashboard - 44');

    InteractionManager.runAfterInteractions(() => {
      var availableLoyaltyCampaignsTemp = [];

      /////////////////////////////////////////////////////////////////////////

      var combinedLoyaltyCampaigns = [];
      for (var i = 0; i < selectedCustomerUserLoyaltyCampaigns.length; i++) {
        for (var j = 0; j < loyaltyCampaigns.length; j++) {
          if (
            selectedCustomerUserLoyaltyCampaigns[i].loyaltyCampaignId ===
            loyaltyCampaigns[j].uniqueId &&
            selectedCustomerUserLoyaltyCampaigns[i] &&
            selectedCustomerUserLoyaltyCampaigns[i].redeemDate === null
          ) {
            // means this user got the 'voucher' for this campaign

            combinedLoyaltyCampaigns.push({
              ...loyaltyCampaigns[j],
              ...selectedCustomerUserLoyaltyCampaigns[i],
            });

            break;
          }
        }
      }

      for (var i = 0; i < combinedLoyaltyCampaigns.length; i++) {
        var isValid = true;

        // if (userGroups.includes(combinedLoyaltyCampaigns[i].targetUserGroup)) {
        //   isValid = true;
        // }

        // if (selectedOutletCRMTagsDict[combinedLoyaltyCampaigns[i].targetUserGroup]) {
        //   const currCrmUserTag = selectedOutletCRMTagsDict[combinedLoyaltyCampaigns[i].targetUserGroup];

        //   if (currCrmUserTag.emailList.includes(email)) {
        //     // means got

        //     isValid = true;
        //   }
        // }

        if (isValid) {
          availableLoyaltyCampaignsTemp.push(combinedLoyaltyCampaigns[i]);
        }
      }

      /////////////////////////////////////////////////////////////////////////

      CommonStore.update(s => {
        // s.availablePromotions = availablePromotionsTemp;
        // s.availablePointsRedeemPackages = availablePointsRedeemPackagesTemp;
        s.availableLoyaltyCampaigns = availableLoyaltyCampaignsTemp;
      });
    });
  }, [
    // userGroups,

    loyaltyCampaigns,
    selectedCustomerUserLoyaltyCampaigns,
  ]);

  ///////////////////////////////////////

  // 2025-02-12 - combine outlet taggable vouchers with master account's voucher

  useEffect(() => {
    // let taggableVouchersMerchantTemp = [];

    OutletStore.update(s => {
      s.taggableVouchers = taggableVouchersOutlet.concat(
        taggableVouchersMerchant,
      );
    });
  }, [taggableVouchersMerchant, taggableVouchersOutlet]);

  ///////////////////////////////////////

  useEffect(() => {
    console.log('useEffect - Dashboard - 45');

    InteractionManager.runAfterInteractions(() => {
      var availableTaggableVouchersTemp = [];

      /////////////////////////////////////////////////////////////////////////

      var combinedTaggableVouchers = [];
      for (var i = 0; i < selectedCustomerUserTaggableVouchers.length; i++) {
        for (var j = 0; j < taggableVouchers.length; j++) {
          if (
            taggableVouchers[j].orderTypes &&
            taggableVouchers[j].orderTypes.includes(orderType)
          ) {
            // included, can proceed
          } else if (
            taggableVouchers[j].orderTypes &&
            ((orderType === ORDER_TYPE.DINEIN &&
              taggableVouchers[j].orderTypes.includes(
                CHANNEL_TYPE.DINEIN_POS,
              )) ||
              (orderType === ORDER_TYPE.PICKUP &&
                taggableVouchers[j].orderTypes.includes(
                  CHANNEL_TYPE.PICKUP_POS,
                )))
          ) {
            // included, can proceed
          } else {
            // not included, skip

            continue;
          }

          if (
            selectedCustomerUserTaggableVouchers[i].voucherId ===
            taggableVouchers[j].uniqueId &&
            selectedCustomerUserTaggableVouchers[i] &&
            selectedCustomerUserTaggableVouchers[i].redeemDate === null
          ) {
            // means this user got the 'voucher' for this campaign

            combinedTaggableVouchers.push({
              ...taggableVouchers[j],
              ...selectedCustomerUserTaggableVouchers[i],

              voucherId: taggableVouchers[j].uniqueId,
            });

            break;
          }
        }
      }

      for (var i = 0; i < combinedTaggableVouchers.length; i++) {
        var isValid = true;

        // if (userGroups.includes(combinedLoyaltyCampaigns[i].targetUserGroup)) {
        //   isValid = true;
        // }

        // if (selectedOutletCRMTagsDict[combinedLoyaltyCampaigns[i].targetUserGroup]) {
        //   const currCrmUserTag = selectedOutletCRMTagsDict[combinedLoyaltyCampaigns[i].targetUserGroup];

        //   if (currCrmUserTag.emailList.includes(email)) {
        //     // means got

        //     isValid = true;
        //   }
        // }

        // 2024-08-16 - can skip for zus bundle voucher, as that one is for qr only
        // if (combinedTaggableVouchers[i].voucherType === LOYALTY_PROMOTION_TYPE.ZUS_BUNDLE) {
        //   isValid = false;
        // }

        if (isValid) {
          availableTaggableVouchersTemp.push(combinedTaggableVouchers[i]);
        }
      }

      /////////////////////////////////////////////////////////////////////////

      OutletStore.update(s => {
        // s.availablePromotions = availablePromotionsTemp;
        // s.availablePointsRedeemPackages = availablePointsRedeemPackagesTemp;
        s.availableTaggableVouchers = availableTaggableVouchersTemp;
      });
    });
  }, [
    // userGroups,

    taggableVouchers,
    selectedCustomerUserTaggableVouchers,

    orderType,
    orderTypeMo,
  ]);

  useEffect(() => {
    console.log('useEffect - Dashboard - 46');

    InteractionManager.runAfterInteractions(() => {
      var overrideItemPriceSkuDictTemp = {};
      var fiItemSkuDictTemp = {};
      var amountOffItemSkuDictTemp = {};
      var percentageOffItemSkuDictTemp = {};
      var buy1Free1ItemSkuDictTemp = {};
      var deliveryItemSkuDictTemp = {};
      var takeawayItemSkuDictTemp = {};

      var overrideCategoryPriceNameDictTemp = {};
      var fiCategoryNameDictTemp = {};
      var amountOffCategoryNameDictTemp = {};
      var percentageOffCategoryNameDictTemp = {};
      var buy1Free1CategoryNameDictTemp = {};
      var deliveryCategoryNameDictTemp = {};
      var takeawayCategoryNameDictTemp = {};

      if (selectedTaggableVoucher && selectedTaggableVoucher.uniqueId) {
        var availableTaggableVouchers = [selectedTaggableVoucher];

        var allMenuVouchersTemp = [];

        for (var i = 0; i < availableTaggableVouchers.length; i++) {
          if (
            availableTaggableVouchers[i].applyAllMenu &&
            (availableTaggableVouchers[i].voucherType ===
              PROMOTION_TYPE.TAKE_AMOUNT_OFF ||
              availableTaggableVouchers[i].voucherType ===
              PROMOTION_TYPE.TAKE_PERCENTAGE_OFF)
          ) {
            if (
              availableTaggableVouchers[i].voucherType ===
              PROMOTION_TYPE.TAKE_PERCENTAGE_OFF
            ) {
              for (
                var j = 0;
                j < availableTaggableVouchers[i].criteriaList.length;
                j++
              ) {
                const criteria = availableTaggableVouchers[i].criteriaList[j];

                allMenuVouchersTemp.push({
                  promotionId: availableTaggableVouchers[i].uniqueId,
                  percentageOff: criteria.percentageOff,
                  maxQuantity: criteria.maxQuantity,
                  minQuantity: criteria.minQuantity,

                  quantityMin: criteria.quantityMin,
                  quantityMax: criteria.quantityMax,
                  priceMin: criteria.priceMin,
                  priceMax: criteria.priceMax,

                  applyDiscountPer:
                    availableTaggableVouchers[i].applyDiscountPer ||
                    APPLY_DISCOUNT_PER.ORDER,

                  type: PROMOTION_TYPE.TAKE_PERCENTAGE_OFF,
                  voucherId: availableTaggableVouchers[i].voucherId
                    ? availableTaggableVouchers[i].voucherId
                    : '',
                  uniqueId: availableTaggableVouchers[i].uniqueId
                    ? availableTaggableVouchers[i].uniqueId
                    : '',
                });

                break; // default only 1 criteria
              }
            } else if (
              availableTaggableVouchers[i].voucherType ===
              PROMOTION_TYPE.TAKE_AMOUNT_OFF
            ) {
              for (
                var j = 0;
                j < availableTaggableVouchers[i].criteriaList.length;
                j++
              ) {
                const criteria = availableTaggableVouchers[i].criteriaList[j];

                allMenuVouchersTemp.push({
                  promotionId: availableTaggableVouchers[i].uniqueId,
                  amountOff: criteria.amountOff,
                  maxQuantity: criteria.maxQuantity,
                  minQuantity: criteria.minQuantity,

                  quantityMin: criteria.quantityMin,
                  quantityMax: criteria.quantityMax,
                  priceMin: criteria.priceMin,
                  priceMax: criteria.priceMax,

                  applyDiscountPer:
                    availableTaggableVouchers[i].applyDiscountPer ||
                    APPLY_DISCOUNT_PER.ORDER,

                  type: PROMOTION_TYPE.TAKE_AMOUNT_OFF,
                  voucherId: availableTaggableVouchers[i].voucherId
                    ? availableTaggableVouchers[i].voucherId
                    : '',
                  uniqueId: availableTaggableVouchers[i].uniqueId
                    ? availableTaggableVouchers[i].uniqueId
                    : '',
                });

                break; // default only 1 criteria
              }
            }
          } else {
            if (
              availableTaggableVouchers[i].voucherType ===
              PROMOTION_TYPE.OVERRIDE_EXISTING_PRICE
            ) {
              for (
                var j = 0;
                j < availableTaggableVouchers[i].criteriaList.length;
                j++
              ) {
                const criteria = availableTaggableVouchers[i].criteriaList[j];

                if (
                  criteria.variation ===
                  PROMOTION_TYPE_VARIATION.SPECIFIC_PRODUCTS
                ) {
                  for (var k = 0; k < criteria.variationItemsSku.length; k++) {
                    overrideItemPriceSkuDictTemp[
                      criteria.variationItemsSku[k]
                    ] = {
                      promotionId: availableTaggableVouchers[i].uniqueId,
                      overridePrice: parseFloat(criteria.priceBeforeTax),
                      minSpend: availableTaggableVouchers[i].minSpend || 0,

                      applyDiscountPer:
                        availableTaggableVouchers[i].applyDiscountPer ||
                        APPLY_DISCOUNT_PER.ORDER,
                    };
                  }
                } else if (
                  criteria.variation ===
                  PROMOTION_TYPE_VARIATION.PRODUCT_OF_CATEGORY
                ) {
                  for (var k = 0; k < criteria.variationItemsSku.length; k++) {
                    overrideCategoryPriceNameDictTemp[
                      criteria.variationItemsSku[k]
                    ] = {
                      promotionId: availableTaggableVouchers[i].uniqueId,
                      overridePrice: parseFloat(criteria.priceBeforeTax),
                      minSpend: availableTaggableVouchers[i].minSpend || 0,

                      applyDiscountPer:
                        availableTaggableVouchers[i].applyDiscountPer ||
                        APPLY_DISCOUNT_PER.ORDER,
                    };
                  }
                }
              }
            } else if (
              availableTaggableVouchers[i].voucherType ===
              PROMOTION_TYPE.FREE_ITEM
            ) {
              for (
                var j = 0;
                j < availableTaggableVouchers[i].criteriaList.length;
                j++
              ) {
                const criteria = availableTaggableVouchers[i].criteriaList[j];

                if (
                  criteria.variation ===
                  PROMOTION_TYPE_VARIATION.SPECIFIC_PRODUCTS
                ) {
                  // 2023-07-31 - use voucher id instead

                  fiItemSkuDictTemp[availableTaggableVouchers[i].uniqueId] = {
                    promotionId: availableTaggableVouchers[i].uniqueId,

                    variationItemsSku: criteria.variationItemsSku,

                    getQuantity: criteria.getQuantity,

                    // amountOff: criteria.amountOff,
                    maxQuantity: criteria.maxQuantity,
                    minQuantity: criteria.minQuantity,

                    quantityMin: criteria.quantityMin,
                    quantityMax: criteria.quantityMax,
                    priceMin: criteria.priceMin,
                    priceMax: criteria.priceMax,

                    applyDiscountPer:
                      availableTaggableVouchers[i].applyDiscountPer ||
                      APPLY_DISCOUNT_PER.ORDER,
                  };

                  // for (var k = 0; k < criteria.variationItemsSku.length; k++) {
                  //   fiItemSkuDictTemp[criteria.variationItemsSku[k]] = {
                  //     promotionId: availableTaggableVouchers[i].uniqueId,

                  //     getQuantity: criteria.getQuantity,

                  //     // amountOff: criteria.amountOff,
                  //     maxQuantity: criteria.maxQuantity,
                  //     minQuantity: criteria.minQuantity,

                  //     quantityMin: criteria.quantityMin,
                  //     quantityMax: criteria.quantityMax,
                  //     priceMin: criteria.priceMin,
                  //     priceMax: criteria.priceMax,

                  //     applyDiscountPer: availableTaggableVouchers[i].applyDiscountPer || APPLY_DISCOUNT_PER.ORDER,
                  //   };
                  // }
                } else if (
                  criteria.variation ===
                  PROMOTION_TYPE_VARIATION.PRODUCT_OF_CATEGORY
                ) {
                  // 2023-07-31 - use voucher id instead

                  fiCategoryNameDictTemp[
                    availableTaggableVouchers[i].uniqueId
                  ] = {
                    promotionId: availableTaggableVouchers[i].uniqueId,

                    variationItemsSku: criteria.variationItemsSku,

                    getQuantity: criteria.getQuantity,

                    // amountOff: criteria.amountOff,
                    maxQuantity: criteria.maxQuantity,
                    minQuantity: criteria.minQuantity,

                    quantityMin: criteria.quantityMin,
                    quantityMax: criteria.quantityMax,
                    priceMin: criteria.priceMin,
                    priceMax: criteria.priceMax,

                    applyDiscountPer:
                      availableTaggableVouchers[i].applyDiscountPer ||
                      APPLY_DISCOUNT_PER.ORDER,
                  };

                  // for (var k = 0; k < criteria.variationItemsSku.length; k++) {
                  //   fiCategoryNameDictTemp[criteria.variationItemsSku[k]] = {
                  //     promotionId: availableTaggableVouchers[i].uniqueId,

                  //     getQuantity: criteria.getQuantity,

                  //     // amountOff: criteria.amountOff,
                  //     maxQuantity: criteria.maxQuantity,
                  //     minQuantity: criteria.minQuantity,

                  //     quantityMin: criteria.quantityMin,
                  //     quantityMax: criteria.quantityMax,
                  //     priceMin: criteria.priceMin,
                  //     priceMax: criteria.priceMax,

                  //     applyDiscountPer: availableTaggableVouchers[i].applyDiscountPer || APPLY_DISCOUNT_PER.ORDER,
                  //   };
                  // }
                }
              }
            } else if (
              availableTaggableVouchers[i].voucherType ===
              PROMOTION_TYPE.TAKE_AMOUNT_OFF
            ) {
              for (
                var j = 0;
                j < availableTaggableVouchers[i].criteriaList.length;
                j++
              ) {
                const criteria = availableTaggableVouchers[i].criteriaList[j];

                if (
                  criteria.variation ===
                  PROMOTION_TYPE_VARIATION.SPECIFIC_PRODUCTS
                ) {
                  for (var k = 0; k < criteria.variationItemsSku.length; k++) {
                    amountOffItemSkuDictTemp[criteria.variationItemsSku[k]] = {
                      promotionId: availableTaggableVouchers[i].uniqueId,
                      amountOff: criteria.amountOff,
                      maxQuantity: criteria.maxQuantity,
                      minQuantity: criteria.minQuantity,

                      quantityMin: criteria.quantityMin,
                      quantityMax: criteria.quantityMax,
                      priceMin: criteria.priceMin,
                      priceMax: criteria.priceMax,

                      applyDiscountPer:
                        availableTaggableVouchers[i].applyDiscountPer ||
                        APPLY_DISCOUNT_PER.ORDER,
                    };
                  }
                } else if (
                  criteria.variation ===
                  PROMOTION_TYPE_VARIATION.PRODUCT_OF_CATEGORY
                ) {
                  for (var k = 0; k < criteria.variationItemsSku.length; k++) {
                    amountOffCategoryNameDictTemp[
                      criteria.variationItemsSku[k]
                    ] = {
                      promotionId: availableTaggableVouchers[i].uniqueId,
                      amountOff: criteria.amountOff,
                      maxQuantity: criteria.maxQuantity,
                      minQuantity: criteria.minQuantity,

                      quantityMin: criteria.quantityMin,
                      quantityMax: criteria.quantityMax,
                      priceMin: criteria.priceMin,
                      priceMax: criteria.priceMax,

                      applyDiscountPer:
                        availableTaggableVouchers[i].applyDiscountPer ||
                        APPLY_DISCOUNT_PER.ORDER,
                    };
                  }
                }
              }
            } else if (
              availableTaggableVouchers[i].voucherType ===
              PROMOTION_TYPE.TAKE_PERCENTAGE_OFF
            ) {
              for (
                var j = 0;
                j < availableTaggableVouchers[i].criteriaList.length;
                j++
              ) {
                const criteria = availableTaggableVouchers[i].criteriaList[j];

                if (
                  criteria.variation ===
                  PROMOTION_TYPE_VARIATION.SPECIFIC_PRODUCTS
                ) {
                  for (var k = 0; k < criteria.variationItemsSku.length; k++) {
                    percentageOffItemSkuDictTemp[
                      criteria.variationItemsSku[k]
                    ] = {
                      promotionId: availableTaggableVouchers[i].uniqueId,
                      percentageOff: criteria.percentageOff,
                      maxQuantity: criteria.maxQuantity,
                      minQuantity: criteria.minQuantity,

                      quantityMin: criteria.quantityMin,
                      quantityMax: criteria.quantityMax,
                      priceMin: criteria.priceMin,
                      priceMax: criteria.priceMax,

                      applyDiscountPer:
                        availableTaggableVouchers[i].applyDiscountPer ||
                        APPLY_DISCOUNT_PER.ORDER,
                    };
                  }
                } else if (
                  criteria.variation ===
                  PROMOTION_TYPE_VARIATION.PRODUCT_OF_CATEGORY
                ) {
                  for (var k = 0; k < criteria.variationItemsSku.length; k++) {
                    percentageOffCategoryNameDictTemp[
                      criteria.variationItemsSku[k]
                    ] = {
                      promotionId: availableTaggableVouchers[i].uniqueId,
                      percentageOff: criteria.percentageOff,
                      maxQuantity: criteria.maxQuantity,
                      minQuantity: criteria.minQuantity,

                      quantityMin: criteria.quantityMin,
                      quantityMax: criteria.quantityMax,
                      priceMin: criteria.priceMin,
                      priceMax: criteria.priceMax,

                      applyDiscountPer:
                        availableTaggableVouchers[i].applyDiscountPer ||
                        APPLY_DISCOUNT_PER.ORDER,
                    };
                  }
                }
              }
            } else if (
              availableTaggableVouchers[i].voucherType ===
              PROMOTION_TYPE.BUY_A_GET_B_FOR_FREE
            ) {
              for (
                var j = 0;
                j < availableTaggableVouchers[i].criteriaList.length;
                j++
              ) {
                const criteria = availableTaggableVouchers[i].criteriaList[j];

                if (
                  criteria.buyVariation ===
                  PROMOTION_TYPE_VARIATION.SPECIFIC_PRODUCTS
                ) {
                  for (
                    var k = 0;
                    k < criteria.buyVariationItemsSku.length;
                    k++
                  ) {
                    buy1Free1ItemSkuDictTemp[criteria.buyVariationItemsSku[k]] =
                    {
                      promotionId: availableTaggableVouchers[i].uniqueId,
                      buyAmount: criteria.buyAmount,
                      getAmount: criteria.getAmount,
                      getPrice: criteria.getPrice,
                      getVariation: criteria.getVariation,
                      getVariationItems: criteria.getVariationItems,
                      getVariationItemsSku: criteria.getVariationItemsSku,

                      applyDiscountPer:
                        availableTaggableVouchers[i].applyDiscountPer ||
                        APPLY_DISCOUNT_PER.ORDER,

                      type: availableTaggableVouchers[i].voucherType,
                    };
                  }
                } else if (
                  criteria.buyVariation ===
                  PROMOTION_TYPE_VARIATION.PRODUCT_OF_CATEGORY
                ) {
                  for (
                    var k = 0;
                    k < criteria.buyVariationItemsSku.length;
                    k++
                  ) {
                    buy1Free1CategoryNameDictTemp[
                      criteria.buyVariationItemsSku[k]
                    ] = {
                      promotionId: availableTaggableVouchers[i].uniqueId,
                      buyAmount: criteria.buyAmount,
                      getAmount: criteria.getAmount,
                      getPrice: criteria.getPrice,
                      getVariation: criteria.getVariation,
                      getVariationItems: criteria.getVariationItems,
                      getVariationItemsSku: criteria.getVariationItemsSku,

                      applyDiscountPer:
                        availableTaggableVouchers[i].applyDiscountPer ||
                        APPLY_DISCOUNT_PER.ORDER,

                      type: availableTaggableVouchers[i].voucherType,
                    };
                  }
                }
              }
            } else if (
              availableTaggableVouchers[i].voucherType ===
              PROMOTION_TYPE.DELIVERY
            ) {
              for (
                var j = 0;
                j < availableTaggableVouchers[i].criteriaList.length;
                j++
              ) {
                const criteria = availableTaggableVouchers[i].criteriaList[j];

                if (
                  criteria.variation ===
                  PROMOTION_TYPE_VARIATION.SPECIFIC_PRODUCTS
                ) {
                  for (var k = 0; k < criteria.variationItemsSku.length; k++) {
                    deliveryItemSkuDictTemp[criteria.variationItemsSku[k]] = {
                      deliveryDiscountAmount: criteria.deliveryDiscountAmount,
                      deliveryDiscountAboveAmount:
                        criteria.deliveryDiscountAboveAmount,

                      applyDiscountPer:
                        availableTaggableVouchers[i].applyDiscountPer ||
                        APPLY_DISCOUNT_PER.ORDER,
                    };
                  }
                } else if (
                  criteria.variation ===
                  PROMOTION_TYPE_VARIATION.PRODUCT_OF_CATEGORY
                ) {
                  for (var k = 0; k < criteria.variationItemsSku.length; k++) {
                    deliveryCategoryNameDictTemp[
                      criteria.variationItemsSku[k]
                    ] = {
                      deliveryDiscountAmount: criteria.deliveryDiscountAmount,
                      deliveryDiscountAboveAmount:
                        criteria.deliveryDiscountAboveAmount,

                      applyDiscountPer:
                        availableTaggableVouchers[i].applyDiscountPer ||
                        APPLY_DISCOUNT_PER.ORDER,
                    };
                  }
                }
              }
            } else if (
              availableTaggableVouchers[i].voucherType ===
              PROMOTION_TYPE.TAKEAWAY
            ) {
              for (
                var j = 0;
                j < availableTaggableVouchers[i].criteriaList.length;
                j++
              ) {
                const criteria = availableTaggableVouchers[i].criteriaList[j];

                if (
                  criteria.variation ===
                  PROMOTION_TYPE_VARIATION.SPECIFIC_PRODUCTS
                ) {
                  for (var k = 0; k < criteria.variationItemsSku.length; k++) {
                    takeawayItemSkuDictTemp[criteria.variationItemsSku[k]] = {
                      promotionId: availableTaggableVouchers[i].uniqueId,
                      takeawayFreeFlag: criteria.takeawayFreeFlag,
                      takeawayFreeAboveAmount: criteria.takeawayFreeAboveAmount,
                      takeawayDiscountAmount: criteria.takeawayDiscountAmount,
                      takeawayDiscountAboveAmount:
                        criteria.takeawayDiscountAboveAmount,

                      applyDiscountPer:
                        availableTaggableVouchers[i].applyDiscountPer ||
                        APPLY_DISCOUNT_PER.ORDER,
                    };
                  }
                } else if (
                  criteria.variation ===
                  PROMOTION_TYPE_VARIATION.PRODUCT_OF_CATEGORY
                ) {
                  for (var k = 0; k < criteria.variationItemsSku.length; k++) {
                    takeawayCategoryNameDictTemp[
                      criteria.variationItemsSku[k]
                    ] = {
                      promotionId: availableTaggableVouchers[i].uniqueId,
                      takeawayFreeFlag: criteria.takeawayFreeFlag,
                      takeawayFreeAboveAmount: criteria.takeawayFreeAboveAmount,
                      takeawayDiscountAmount: criteria.takeawayDiscountAmount,
                      takeawayDiscountAboveAmount:
                        criteria.takeawayDiscountAboveAmount,

                      applyDiscountPer:
                        availableTaggableVouchers[i].applyDiscountPer ||
                        APPLY_DISCOUNT_PER.ORDER,
                    };
                  }
                }
              }
            }
          }
        }
      }

      CommonStore.update(s => {
        s.overrideItemPriceSkuDictLC = overrideItemPriceSkuDictTemp;
        s.fiItemSkuDictLC = fiItemSkuDictTemp;
        s.amountOffItemSkuDictLC = amountOffItemSkuDictTemp;
        s.percentageOffItemSkuDictLC = percentageOffItemSkuDictTemp;
        s.buy1Free1ItemSkuDictLC = buy1Free1ItemSkuDictTemp;
        s.deliveryItemSkuDictLC = deliveryItemSkuDictTemp;
        s.takeawayItemSkuDictLC = takeawayItemSkuDictTemp;

        s.overrideCategoryPriceNameDictLC = overrideCategoryPriceNameDictTemp;
        s.fiCategoryNameDictLC = fiCategoryNameDictTemp;
        s.amountOffCategoryNameDictLC = amountOffCategoryNameDictTemp;
        s.percentageOffCategoryNameDictLC = percentageOffCategoryNameDictTemp;
        s.buy1Free1CategoryNameDictLC = buy1Free1CategoryNameDictTemp;
        s.deliveryCategoryNameDictLC = deliveryCategoryNameDictTemp;
        s.takeawayCategoryNameDictLC = takeawayCategoryNameDictTemp;

        s.allMenuVouchers = allMenuVouchersTemp;
      });
    });
  }, [
    // availableLoyaltyCampaigns,
    // selectedLoyaltyCampaign,
    selectedTaggableVoucher,
  ]);

  ////////////////////////////////////////////////////////

  // useEffect(() => {
  //     PushNotification.localNotification({
  //         id: NOTIFICATIONS_ID.USER_ORDER,
  //         channelId: NOTIFICATIONS_CHANNEL.USER_ORDER,
  //         title: `A ${ORDER_TYPE_PARSED[ORDER_TYPE.DINEIN].toLowerCase()} order placed!`,
  //         message: `Total: RM10.00`,
  //         data: {
  //             orderType: ORDER_TYPE.DINEIN,
  //             type: NOTIFICATIONS_TYPE.USER_ORDER,
  //         },
  //         userInfo: {
  //             orderType: ORDER_TYPE.DINEIN,
  //             type: NOTIFICATIONS_TYPE.USER_ORDER,
  //         },
  //         // actions: [
  //         //     SURVEY_NOTIFICATION_ACTIONS.PROCEED,
  //         //     SURVEY_NOTIFICATION_ACTIONS.SNOOZE,
  //         // ],
  //         // date: dayjs(inputData.formData.currTime).add(1, 'minute').toDate(),
  //         // date: dayjs(nextTime).toDate(),
  //         // repeatType: 'time',
  //         // repeatTime: intervalTime,
  //     });
  // }, []);

  // useEffect(() => {
  //     var dailySalesDetailsListTemp = [];

  //     if (currOutletId && currOutletId.length > 0) {
  //         // for (var i = 0; i < moment().daysInMonth(); i++) {
  //         //     var currDateTime = moment().startOf('month').add(i, 'day');

  //         //     var record = {
  //         //         dateTime: moment(currDateTime).format('D MMM YYYY'),
  //         //         totalSales: 0,
  //         //         totalTransactions: 0,
  //         //         totalDiscount: 0,
  //         //         discount: 0,
  //         //         tax: 0,
  //         //         serviceCharge: 0,
  //         //         gp: 0,
  //         //         totalSalesReturn: 0,
  //         //         netSales: 0,
  //         //         averageNetSales: 0,
  //         //     };

  //         //     for (var j = 0; j < allOutletsUserOrdersDone.length; j++) {
  //         //         if (allOutletsUserOrdersDone[j].outletId === currOutletId &&
  //         //             moment(currDateTime).isSame(allOutletsUserOrdersDone[j].createdAt, 'day')) {
  //         //             record.totalSales += allOutletsUserOrdersDone[j].finalPrice;
  //         //             record.totalTransactions += 1;
  //         //             record.totalDiscount += allOutletsUserOrdersDone[j].discount;
  //         //             record.discount += allOutletsUserOrdersDone[j].discount / allOutletsUserOrdersDone[j].finalPrice * 100;
  //         //             record.tax += allOutletsUserOrdersDone[j].tax / allOutletsUserOrdersDone[j].finalPrice * 100;
  //         //             record.serviceCharge += allOutletsUserOrdersDone[j].tax;
  //         //             record.totalSalesReturn += allOutletsUserOrdersDone[j].totalPrice;
  //         //             record.netSales += allOutletsUserOrdersDone[j].totalPrice;
  //         //         }
  //         //     }

  //         //     if (record.totalTransactions > 0) {
  //         //         record.averageNetSales = record.netSales / record.totalTransactions;
  //         //     }

  //         //     dailySalesDetailsListTemp.push(record);
  //         // }

  //         // const isValid = filterItems(allOutletsUserOrdersDone[i]);

  //         // const allOutletsUserOrdersDoneFiltered = allOutletsUserOrdersDone.filter(item => filterChartItems(item, appliedChartFilterQueries));

  //         for (var i = 0; i < moment(rev_date1).diff(moment(rev_date), 'day'); i++) {
  //             var currDateTime = moment(rev_date).add(i, 'day');

  //             var record = {
  //                 summaryId: nanoid(),
  //                 dateTime: moment(currDateTime).format('D MMM YYYY'),
  //                 totalSales: 0,
  //                 totalTransactions: 0,
  //                 totalDiscount: 0,
  //                 discount: 0,
  //                 tax: 0,
  //                 serviceCharge: 0,
  //                 gp: 0,
  //                 totalSalesReturn: 0,
  //                 netSales: 0,
  //                 averageNetSales: 0,
  //                 detailsList: [],

  //             };

  //             for (var j = 0; j < allOutletsUserOrdersDone.length; j++) {
  //                 if (allOutletsUserOrdersDone[j].outletId === currOutletId &&
  //                     moment(currDateTime).isSame(allOutletsUserOrdersDone[j].createdAt, 'day')) {
  //                     record.totalSales += allOutletsUserOrdersDone[j].finalPrice;
  //                     record.totalTransactions += 1;
  //                     record.totalDiscount += allOutletsUserOrdersDone[j].discount;
  //                     record.discount += allOutletsUserOrdersDone[j].discount / allOutletsUserOrdersDone[j].finalPrice * 100;
  //                     record.tax += allOutletsUserOrdersDone[j].tax / allOutletsUserOrdersDone[j].finalPrice * 100;
  //                     record.serviceCharge += allOutletsUserOrdersDone[j].tax;
  //                     record.totalSalesReturn += allOutletsUserOrdersDone[j].totalPrice;
  //                     record.netSales += allOutletsUserOrdersDone[j].totalPrice;
  //                     record.detailsList.push(allOutletsUserOrdersDone[j]);
  //                 }
  //             }

  //             if (record.totalTransactions > 0) {
  //                 record.averageNetSales = record.netSales / record.totalTransactions;
  //             }

  //             dailySalesDetailsListTemp.push(record);
  //         }
  //     }

  //     setDailySalesDetailsList(dailySalesDetailsListTemp);

  //     setCurrentPage(1);
  //     setPageCount(Math.ceil(dailySalesDetailsListTemp.length / perPage));
  // }, [
  //     allOutlets,
  //     allOutletsUserOrdersDone,
  //     currOutletId,
  //     rev_date,
  //     rev_date1,
  // ]);

  //////////////////////////////////////////////////////////////////////////////////

  // const [localSelectedOutletId, setLocalSeletedOutletId] = useState('');

  // const [totalSalesChartData, setTotalSalesChartData] = useState({
  //     labels: ['1'],
  //     datasets: [{
  //         data: [0],
  //     }],
  // });

  // const [singleSalesChartData, setSingleSalesChartData] = useState({
  //     labels: ['1'],
  //     datasets: [{
  //         data: [0],
  //     }],
  // });

  // const [totalHourlyDataDict, setTotalHourlyDataDict] = useState([]);
  // const [singleHourlyDataDict, setSingleHourlyDataDict] = useState([]);

  // const allOutletsUserOrdersDone = OutletStore.useState(
  //   (s) => s.allOutletsUserOrdersDone,
  // );

  // useEffect(() => {
  //     var totalHourlyDataListTemp = Array(24).fill(0);

  //     for (var i = 0; i < allOutletsUserOrdersDone.length; i++) {
  //         if (moment(allOutletsUserOrdersDone[i].createdAt).isSame(moment(), 'day')) {
  //             totalHourlyDataListTemp[moment(allOutletsUserOrdersDone[i].createdAt)] += allOutletsUserOrdersDone[i].finalPrice;
  //         }
  //     }

  //     // setTotalHourlyDataDict(totalHourlyDataListTemp);
  // }, [allOutletsUserOrdersDone]);

  // useEffect(() => {
  //     var outletIdTemp = localSelectedOutletId ? localSelectedOutletId : currOutletId;

  //     if (outletIdTemp !== '') {
  //         var singleHourlyDataListTemp = Array(24).fill(0);

  //         for (var i = 0; i < allOutletsUserOrdersDone.length; i++) {
  //             if (allOutletsUserOrdersDone[i].outletId === outletIdTemp &&
  //                 moment(allOutletsUserOrdersDone[i].createdAt).isSame(moment(), 'day')) {
  //                 singleHourlyDataListTemp[moment(allOutletsUserOrdersDone[i].createdAt)] += allOutletsUserOrdersDone[i].finalPrice;
  //             }
  //         }

  //         // setSingleHourlyDataDict(singleHourlyDataListTemp);
  //     }
  // }, [currOutletId, localSelectedOutletId, allOutletsUserOrdersDone]);

  //////////////////////////////////////////////////////////////////////////////////

  // useEffect(() => {
  //   console.log('useEffect - Dashboard - 47');

  //   if (isMounted) {
  //     InteractionManager.runAfterInteractions(() => {
  //       // const allOutletsUserOrdersDoneFilteredBarChart =
  //       //   allOutletsUserOrdersDone.filter((item) =>
  //       //     filterChartItems(item, appliedChartFilterQueriesBarChart),
  //       //   );

  //       // const allOutletsUserOrdersDoneFilteredLineChart = filterAppType.length > 0 ?
  //       //   allOutletsUserOrdersDone.filter((item) =>
  //       //     //filterChartItems(item, appliedChartFilterQueriesLineChart),
  //       //     (filterAppType.includes(item.appType))
  //       //   ) : allOutletsUserOrders;

  //       // console.log(
  //       //   '------------------------------here----------------------------------',
  //       // );
  //       // console.log(allOutletsUserOrdersDoneFilteredBarChart);

  //       ////////////////////////////////////////////////////////

  //       // 2023-05-19 - Redundant

  //       // const dataChartDashboardTodaySales = getDataForChartDashboardTodaySales(
  //       //   allOutlets,
  //       //   allOutletsUserOrdersDoneFilteredLineChart,
  //       //   salesBarChartPeriod,
  //       //   selectedChartDropdownValueBarChart,
  //       //   historyStartDate,
  //       //   historyEndDate,
  //       // );

  //       // if (dataChartDashboardTodaySales) {
  //       //   setTodaySalesChart(dataChartDashboardTodaySales);
  //       // }

  //       ////////////////////////////////////////////////////////

  //       // setSalesLineChart(getDataForSalesLineChart(allOutlets, allOutletsUserOrdersDoneFilteredLineChart, salesLineChartPeriod, selectedChartDropdownValueLineChart));
  //       setExpandLineSelection(false);

  //       if (allOutlets.length > 0 && allOutlets[0].merchantId === merchantId) {
  //         if (selectedChartDropdownValueX === 'HOURLY') {
  //           const result = getDataForSalesLineChartHourly(
  //             allOutlets,
  //             allOutletsUserOrdersDone,
  //             salesLineChartPeriod,
  //             selectedChartDropdownValueLineChart,
  //             selectedChartDropdownValueX,
  //             historyStartDate,
  //             historyEndDate,
  //             reportDisplayType,
  //             reportOutletShifts,
  //           );

  //           if (result && result.chartData) {
  //             setSalesLineChart(result.chartData);
  //             setExpandLineSelection(false);
  //           }
  //         } else {
  //           const result = getDataForSalesLineChart(
  //             allOutlets,
  //             allOutletsUserOrdersDone,
  //             salesLineChartPeriod,
  //             selectedChartDropdownValueLineChart,
  //             selectedChartDropdownValueX,
  //             historyStartDate,
  //             historyEndDate,
  //             reportDisplayType,
  //             reportOutletShifts,
  //           );

  //           if (result) {
  //             setSalesLineChart(result.chartData);
  //             setExpandLineSelection(false);
  //           } else {
  //             setSalesLineChart({});
  //             setExpandLineSelection(false);
  //           }
  //         }
  //       } else {
  //         setSalesLineChart({});
  //         setExpandLineSelection(false);
  //       }
  //     });
  //   } else {
  //     setSalesLineChart({});
  //     setExpandLineSelection(false);
  //   }
  // }, [
  //   allOutlets.length,
  //   allOutletsUserOrdersDone,
  //   salesLineChartPeriod,
  //   salesBarChartPeriod,
  //   selectedChartDropdownValueBarChart,
  //   selectedChartDropdownValueLineChart,
  //   appliedChartFilterQueriesBarChart,
  //   appliedChartFilterQueriesLineChart,
  //   historyStartDate,
  //   historyEndDate,

  //   selectedChartDropdownValueX,

  //   merchantId,

  //   isMounted, // only update this if screen on display
  //   // filterAppType,
  // ]);

  // useEffect(() => {
  //   if (isMounted) {
  //     InteractionManager.runAfterInteractions(() => {
  //       var totalSalesTemp = 0;
  //       var totalTransactionsTemp = allOutletsUserOrdersDone.length;
  //       var totalSoldTemp = 0;
  //       var currDate = moment(Date.now());

  //       setExpandLineSelection(false);

  //       for (var i = 0; i < allOutletsUserOrdersDone.length; i++) {
  //         if (
  //           currOutletId === allOutletsUserOrdersDone[i].outletId &&
  //           // moment(currDate).isSame(
  //           //   allOutletsUserOrdersDone[i].createdAt,
  //           //   'day',
  //           // )
  //           compareOrderDateByDisplayType(currDate, allOutletsUserOrdersDone[i].createdAt, DATE_COMPARE_TYPE.IS_SAME, 'day', reportDisplayType, reportOutletShifts)
  //           &&
  //           (filterAppType.includes(allOutletsUserOrdersDone[i].appType) || filterAppType.length === 0)
  //         ) {
  //           for (var j = 0; j < allOutletsUserOrdersDone[i].cartItems.length; j++) {
  //             const cartItem = allOutletsUserOrdersDone[i].cartItems[j];

  //             totalSoldTemp += cartItem.quantity;
  //           }

  //           totalSalesTemp += allOutletsUserOrdersDone[i].finalPrice;
  //         }
  //       }

  //       setTotalSales(totalSalesTemp);
  //       setTotalTransactions(totalTransactionsTemp);
  //       setTotalSold(totalSoldTemp);
  //     });
  //   }
  // }, [
  //   allOutlets,
  //   allOutletsUserOrdersDone,
  //   currOutletId,
  //   historyStartDate,
  //   historyEndDate,
  //   perPage,

  //   isMounted, // only update this if screen on display
  //   filterAppType,

  //   reportOutletShifts,
  //   reportDisplayType,
  // ]);

  // useEffect(() => {
  //   console.log('useEffect - Dashboard - 48');

  //   // var totalSalesTemp = 0;
  //   // var totalTransactionsTemp = allOutletsUserOrdersDone.length;
  //   // var totalSoldTemp = 0;
  //   // var currDate = moment(Date.now());

  //   // setExpandLineSelection(false);

  //   // for (var i = 0; i < allOutletsUserOrdersDone.length; i++) {
  //   //     if (currOutletId === allOutletsUserOrdersDone[i].outletId &&
  //   //         moment(currDate).isSame(allOutletsUserOrdersDone[i].createdAt, 'day')) {
  //   //         for (var j = 0; j < allOutletsUserOrdersDone[i].cartItems.length; j++) {
  //   //             const cartItem = allOutletsUserOrdersDone[i].cartItems[j];

  //   //             totalSoldTemp += cartItem.quantity;
  //   //         }

  //   //         totalSalesTemp += allOutletsUserOrdersDone[i].finalPrice;
  //   //     }
  //   // }

  //   // setTotalSales(totalSalesTemp);
  //   // setTotalTransactions(totalTransactionsTemp);
  //   // setTotalSold(totalSoldTemp);

  //   //////////////////////////////////////////////////////////////////////////////////////

  //   if (global.reportTableTimerId) {
  //     clearTimeout(global.reportTableTimerId);
  //   }

  //   global.reportTableTimerId = setTimeout(() => {
  //     if (isMounted) {
  //       InteractionManager.runAfterInteractions(() => {
  //         var dailySalesDetailsListTemp = [];

  //         let allDayTotalTransactionTemp = 0;
  //         let currentOutletTotalSalesTemp = 0;

  //         var totalSalesTemp = 0;
  //         var totalTransactionsTemp = 0;
  //         var totalSoldTemp = 0;
  //         var currDate = Date.now();

  //         if (currOutletId && currOutletId.length > 0) {
  //           // for (var i = 0; i < moment().daysInMonth(); i++) {
  //           //     var currDateTime = moment().startOf('month').add(i, 'day');

  //           //     var record = {
  //           //         dateTime: moment(currDateTime).format('D MMM YYYY'),
  //           //         totalSales: 0,
  //           //         totalTransactions: 0,
  //           //         totalDiscount: 0,
  //           //         discount: 0,
  //           //         tax: 0,
  //           //         serviceCharge: 0,
  //           //         gp: 0,
  //           //         totalSalesReturn: 0,
  //           //         netSales: 0,
  //           //         averageNetSales: 0,
  //           //     };

  //           //     for (var j = 0; j < allOutletsUserOrdersDone.length; j++) {
  //           //         if (allOutletsUserOrdersDone[j].outletId === currOutletId &&
  //           //             moment(currDateTime).isSame(allOutletsUserOrdersDone[j].createdAt, 'day')) {
  //           //             record.totalSales += allOutletsUserOrdersDone[j].finalPrice;
  //           //             record.totalTransactions += 1;
  //           //             record.totalDiscount += allOutletsUserOrdersDone[j].discount;
  //           //             record.discount += allOutletsUserOrdersDone[j].discount / allOutletsUserOrdersDone[j].finalPrice * 100;
  //           //             record.tax += allOutletsUserOrdersDone[j].tax / allOutletsUserOrdersDone[j].finalPrice * 100;
  //           //             record.serviceCharge += allOutletsUserOrdersDone[j].tax;
  //           //             record.totalSalesReturn += allOutletsUserOrdersDone[j].totalPrice;
  //           //             record.netSales += allOutletsUserOrdersDone[j].totalPrice;
  //           //         }
  //           //     }

  //           //     if (record.totalTransactions > 0) {
  //           //         record.averageNetSales = record.netSales / record.totalTransactions;
  //           //     }

  //           //     dailySalesDetailsListTemp.push(record);
  //           // }

  //           // const isValid = filterItems(allOutletsUserOrdersDone[i]);

  //           // const allOutletsUserOrdersDoneFiltered = allOutletsUserOrdersDone.filter(item => filterChartItems(item, appliedChartFilterQueries));

  //           for (
  //             var i = 0;
  //             i <= moment(historyEndDate).diff(moment(historyStartDate), 'day');
  //             i++
  //           ) {
  //             var currDateTime = moment(historyStartDate).add(i, 'day');

  //             var record = {
  //               summaryId: nanoid(),
  //               dateTime: moment(currDateTime).format('D MMM YYYY'),
  //               dateTimeRaw: currDateTime,
  //               totalSales: 0,
  //               totalTransactions: 0,
  //               totalDiscount: 0,
  //               discount: 0,
  //               tax: 0,
  //               serviceCharge: 0,
  //               gp: 0,
  //               totalSalesReturn: 0,
  //               netSales: 0,
  //               averageNetSales: 0,
  //               detailsList: [],
  //               itemCostPrice: 0,
  //               grabComm: 0,
  //             };

  //             dailySalesDetailsListTemp.push(record);
  //           }

  //           let lastCheckingRecord = null;
  //           let lastCheckingRecordIndex = -1;

  //           var record = null;

  //           for (var j = 0; j < allOutletsUserOrdersDone.length; j++) {
  //             // console.log(`check order id: ${allOutletsUserOrdersDone[j].orderId}`);
  //             // console.log(`check order sales: ${allOutletsUserOrdersDone[j].finalPrice}`);

  //             if (
  //               allOutletsUserOrdersDone[j].outletId === currOutletId
  //               // &&
  //               // moment(currDateTime).isSame(
  //               //   allOutletsUserOrdersDone[j].createdAt,
  //               //   'day',
  //               // )
  //               // compareOrderDateByDisplayType(
  //               //   currDateTime,
  //               //   allOutletsUserOrdersDone[j].createdAt,
  //               //   DATE_COMPARE_TYPE.IS_SAME,
  //               //   'day',
  //               //   reportDisplayType,
  //               //   reportOutletShifts
  //               // )
  //               // &&
  //               // (filterAppType.includes(allOutletsUserOrdersDone[j].appType) || filterAppType.length === 0)
  //             ) {
  //               // console.log(`approved order id: ${allOutletsUserOrdersDone[j].orderId}`);
  //               // console.log(`approved order sales: ${allOutletsUserOrdersDone[j].finalPrice}`);
  //               // console.log('-----------------------------------');

  //               let toProceed = false;
  //               if (
  //                 lastCheckingRecord &&
  //                 compareOrderDateByDisplayType(
  //                   lastCheckingRecord.dateTimeRaw,
  //                   allOutletsUserOrdersDone[j].createdAt,
  //                   DATE_COMPARE_TYPE.IS_SAME,
  //                   'day',
  //                   reportDisplayType,
  //                   reportOutletShifts,
  //                 )
  //               ) {
  //                 toProceed = true;
  //               } else {
  //                 for (
  //                   let findCRI = 0;
  //                   findCRI < dailySalesDetailsListTemp.length;
  //                   findCRI++
  //                 ) {
  //                   const currCheckingRecord =
  //                     dailySalesDetailsListTemp[findCRI];
  //                   if (
  //                     currCheckingRecord &&
  //                     compareOrderDateByDisplayType(
  //                       currCheckingRecord.dateTimeRaw,
  //                       allOutletsUserOrdersDone[j].createdAt,
  //                       DATE_COMPARE_TYPE.IS_SAME,
  //                       'day',
  //                       reportDisplayType,
  //                       reportOutletShifts,
  //                     )
  //                   ) {
  //                     lastCheckingRecord = currCheckingRecord;
  //                     lastCheckingRecordIndex = findCRI;
  //                     toProceed = true;
  //                     break;
  //                   }
  //                 }
  //               }

  //               if (toProceed) {
  //                 /////////////////////////////////////////////////////////////////

  //                 // for today summary

  //                 record = lastCheckingRecord;
  //                 if (moment(record.dateTimeRaw).isSame(currDate, 'day')) {
  //                   for (
  //                     var totalIndex = 0;
  //                     totalIndex < allOutletsUserOrdersDone[j].cartItems.length;
  //                     totalIndex++
  //                   ) {
  //                     const cartItem =
  //                       allOutletsUserOrdersDone[j].cartItems[totalIndex];
  //                     totalSoldTemp +=
  //                       cartItem.quantity !== undefined
  //                         ? cartItem.quantity
  //                         : cartItem.qty;
  //                   }
  //                   totalSalesTemp += allOutletsUserOrdersDone[j].finalPrice;
  //                   totalTransactionsTemp += 1;
  //                 }

  //                 /////////////////////////////////////////////////////////////////

  //                 allDayTotalTransactionTemp += 1;
  //                 currentOutletTotalSalesTemp +=
  //                   allOutletsUserOrdersDone[j].finalPrice;

  //                 record.totalSales += allOutletsUserOrdersDone[j]
  //                   .finalPriceBefore
  //                   ? allOutletsUserOrdersDone[j].finalPriceBefore
  //                   : allOutletsUserOrdersDone[j].finalPrice;
  //                 record.totalTransactions += 1;

  //                 const discountCalculated = getOrderDiscountInfoInclOrderBased(
  //                   allOutletsUserOrdersDone[j],
  //                 );

  //                 record.totalDiscount += discountCalculated;
  //                 record.discount += !isNaN(
  //                   (discountCalculated /
  //                     allOutletsUserOrdersDone[j].finalPrice) *
  //                   100,
  //                 )
  //                   ? (discountCalculated /
  //                     allOutletsUserOrdersDone[j].finalPrice) *
  //                   100
  //                   : 0;

  //                 record.grabComm += currOutlet.odGrabMID
  //                   ? allOutletsUserOrdersDone[j].orderType ===
  //                     ORDER_TYPE.PICKUP &&
  //                     allOutletsUserOrdersDone[j].orderTypeSub ===
  //                     ORDER_TYPE_SUB.OTHER_DELIVERY
  //                     ? allOutletsUserOrdersDone[j].odType ===
  //                       DELIVERY_PARTNER.GRABFOOD &&
  //                       allOutletsUserOrdersDone[j].odId &&
  //                       currOutlet.odGrabCommRate
  //                       ? (allOutletsUserOrdersDone[j].finalPrice *
  //                         currOutlet.odGrabCommRate) /
  //                       100
  //                       : 0
  //                     : 0
  //                   : 0;

  //                 record.tax += !isNaN(
  //                   (allOutletsUserOrdersDone[j].tax /
  //                     allOutletsUserOrdersDone[j].finalPrice) *
  //                   100,
  //                 )
  //                   ? allOutletsUserOrdersDone[j].tax
  //                   : 0;
  //                 record.serviceCharge += allOutletsUserOrdersDone[j].sc || 0;
  //                 // record.totalSalesReturn += 0;

  //                 var orderSalesReturn = 0;
  //                 if (allOutletsUserOrdersDone[j].cartItemsCancelled) {
  //                   for (
  //                     var k = 0;
  //                     k < allOutletsUserOrdersDone[j].cartItemsCancelled.length;
  //                     k++
  //                   ) {
  //                     orderSalesReturn +=
  //                       allOutletsUserOrdersDone[j].cartItemsCancelled[k].price;
  //                   }
  //                 }

  //                 var orderCostPrice = allOutletsUserOrdersDone[j].finalPrice;
  //                 if (allOutletsUserOrdersDone[j].cartItems) {
  //                   for (
  //                     var k = 0;
  //                     k < allOutletsUserOrdersDone[j].cartItems.length;
  //                     k++
  //                   ) {
  //                     orderCostPrice -= allOutletsUserOrdersDone[j].cartItems[k]
  //                       .itemCostPrice
  //                       ? allOutletsUserOrdersDone[j].cartItems[k]
  //                         .itemCostPrice *
  //                       allOutletsUserOrdersDone[j].cartItems[k].quantity
  //                       : 0;
  //                   }

  //                   // for (var k = 0; k < allOutletsUserOrdersDone[j].cartItems.length; k++) {
  //                   //   orderCostPrice += allOutletsUserOrdersDone[j].cartItems[k].itemCostPrice ?
  //                   //     (allOutletsUserOrdersDone[j].finalPriceBefore != 0 ||
  //                   //       allOutletsUserOrdersDone[j].finalPrice != 0
  //                   //       ? (allOutletsUserOrdersDone[j].finalPriceBefore
  //                   //         ? allOutletsUserOrdersDone[j].finalPriceBefore
  //                   //         : allOutletsUserOrdersDone[j].finalPrice) -
  //                   //       allOutletsUserOrdersDone[j].tax -
  //                   //       (allOutletsUserOrdersDone[j].sc
  //                   //         ? allOutletsUserOrdersDone[j].sc
  //                   //         : 0)
  //                   //       : 0) - allOutletsUserOrdersDone[j].cartItems[k].itemCostPrice : 0
  //                   // }
  //                 }
  //                 record.itemCostPrice += orderCostPrice;

  //                 record.totalSalesReturn += orderSalesReturn;

  //                 record.netSales +=
  //                   allOutletsUserOrdersDone[j].finalPriceBefore != 0 ||
  //                     allOutletsUserOrdersDone[j].finalPrice != 0
  //                     ? (allOutletsUserOrdersDone[j].finalPriceBefore
  //                       ? allOutletsUserOrdersDone[j].finalPriceBefore
  //                       : allOutletsUserOrdersDone[j].finalPrice) -
  //                     allOutletsUserOrdersDone[j].tax -
  //                     (allOutletsUserOrdersDone[j].sc
  //                       ? allOutletsUserOrdersDone[j].sc
  //                       : 0)
  //                     : 0;
  //                 record.detailsList.push({
  //                   // ...allOutletsUserOrdersDone[j],
  //                   id: allOutletsUserOrdersDone[j].uniqueId,

  //                   // cartItems: [],
  //                   // uniqueId: allOutletsUserOrdersDone[j].uniqueId,
  //                   discountPercentage: parseFloat(
  //                     isFinite(
  //                       discountCalculated /
  //                       (allOutletsUserOrdersDone[j].finalPrice +
  //                         discountCalculated),
  //                     )
  //                       ? (discountCalculated /
  //                         (allOutletsUserOrdersDone[j].finalPrice +
  //                           discountCalculated)) *
  //                       100
  //                       : 0,
  //                   ),
  //                   salesReturn: orderSalesReturn,
  //                   grabComm: currOutlet.odGrabMID
  //                     ? allOutletsUserOrdersDone[j].orderType ===
  //                       ORDER_TYPE.PICKUP &&
  //                       allOutletsUserOrdersDone[j].orderTypeSub ===
  //                       ORDER_TYPE_SUB.OTHER_DELIVERY
  //                       ? allOutletsUserOrdersDone[j].odType ===
  //                         DELIVERY_PARTNER.GRABFOOD &&
  //                         allOutletsUserOrdersDone[j].odId
  //                         ? (allOutletsUserOrdersDone[j].finalPrice *
  //                           currOutlet.odGrabCommRate) /
  //                         100
  //                         : 0
  //                       : 0
  //                     : 0,
  //                 });

  //                 if (record.totalTransactions > 0) {
  //                   record.averageNetSales =
  //                     record.netSales / record.totalTransactions;
  //                 }

  //                 // to update back

  //                 dailySalesDetailsListTemp[lastCheckingRecordIndex] =
  //                   lastCheckingRecord;

  //                 // can continue, since 1 order will belong to 1 section only
  //                 continue;
  //               }
  //             }
  //           }
  //         }

  //         if (selectedChartDropdownValueX === CHART_X_AXIS_TYPE.HOURLY) {
  //           const hourlyBuckets = Array(24)
  //             .fill()
  //             .map(() => []);

  //           allOutletsUserOrdersDone.forEach(order => {
  //             const orderHour = moment(order.createdAt).hour();
  //             hourlyBuckets[orderHour].push(order);
  //           });

  //           const hourlyData = hourlyBuckets.map((orderBucket, hour) => {
  //             let totalSales = 0;
  //             let totalDiscount = 0;
  //             let grabComm = 0;
  //             let tax = 0;
  //             let serviceCharge = 0;
  //             let totalSalesReturn = 0;
  //             let netSales = 0;
  //             let itemCostPrice = 0;

  //             orderBucket.forEach(order => {
  //               totalSales += order.finalPriceBefore || order.finalPrice || 0;
  //               totalDiscount += getOrderDiscountInfoInclOrderBased(order);
  //               grabComm += order.odGrabMID
  //                 ? order.orderType === ORDER_TYPE.PICKUP &&
  //                   order.orderTypeSub === ORDER_TYPE_SUB.OTHER_DELIVERY
  //                   ? order.odType === DELIVERY_PARTNER.GRABFOOD &&
  //                     order.odId &&
  //                     order.odGrabCommRate
  //                     ? ((order.finalPrice || 0) * order.odGrabCommRate) / 100
  //                     : 0
  //                   : 0
  //                 : 0;
  //               tax += !isNaN((order.tax / order.finalPrice) * 100)
  //                 ? order.tax
  //                 : 0;
  //               serviceCharge += order.sc || 0;
  //               totalSalesReturn +=
  //                 order.cartItemsCancelled?.reduce(
  //                   (sum, item) => sum + (item.price || 0),
  //                   0,
  //                 ) || 0;
  //               netSales +=
  //                 order.finalPriceBefore != 0 || order.finalPrice != 0
  //                   ? (order.finalPriceBefore
  //                     ? order.finalPriceBefore
  //                     : order.finalPrice) -
  //                   order.tax -
  //                   (order.sc ? order.sc : 0)
  //                   : 0;
  //               if (order.cartItems) {
  //                 for (const item of order.cartItems) {
  //                   itemCostPrice +=
  //                     (item.quantity || 0) * (item.itemCostPrice || 0);
  //                 }
  //               }
  //             });

  //             const totalTransactions = orderBucket.length;
  //             const averageNetSales =
  //               totalTransactions > 0 ? netSales / totalTransactions : 0;

  //             // Format Hour Display
  //             const hourDateTime = moment().hour(hour).minute(0).second(0);

  //             const detailsList = orderBucket.map(order => ({
  //               id: order.uniqueId,
  //               discountPercentage: parseFloat(
  //                 isFinite(
  //                   getOrderDiscountInfoInclOrderBased(order) /
  //                   (order.finalPrice +
  //                     getOrderDiscountInfoInclOrderBased(order)),
  //                 )
  //                   ? (getOrderDiscountInfoInclOrderBased(order) /
  //                     (order.finalPrice +
  //                       getOrderDiscountInfoInclOrderBased(order))) *
  //                   100
  //                   : 0,
  //               ),
  //               salesReturn:
  //                 order.cartItemsCancelled?.reduce(
  //                   (sum, item) => sum + (item.price || 0),
  //                   0,
  //                 ) || 0,
  //               grabComm: order.odGrabMID
  //                 ? order.orderType === ORDER_TYPE.PICKUP &&
  //                   order.orderTypeSub === ORDER_TYPE_SUB.OTHER_DELIVERY
  //                   ? order.odType === DELIVERY_PARTNER.GRABFOOD &&
  //                     order.odId &&
  //                     order.odGrabCommRate
  //                     ? ((order.finalPrice || 0) * order.odGrabCommRate) / 100
  //                     : 0
  //                   : 0
  //                 : 0,
  //             }));

  //             return {
  //               summaryId: nanoid(),
  //               dateTime: hourDateTime.format('h:00 A'),
  //               totalSales,
  //               totalTransactions,
  //               totalDiscount,
  //               tax,
  //               serviceCharge,
  //               totalSalesReturn,
  //               netSales,
  //               averageNetSales,
  //               itemCostPrice,
  //               grabComm,
  //               detailsList,
  //             };
  //           });

  //           dailySalesDetailsListTemp = hourlyData;
  //         }

  //         //////////////////////////////////////////////////////////////////////

  //         // for summary

  //         setAllDayTotalTransaction(allDayTotalTransactionTemp);
  //         setCurrentOutletTotalSales(currentOutletTotalSalesTemp);

  //         setTotalSales(totalSalesTemp);
  //         setTotalTransactions(totalTransactionsTemp);
  //         setTotalSold(totalSoldTemp);

  //         //////////////////////////////////////////////////////////////////////

  //         setDailySalesDetailsList(dailySalesDetailsListTemp);

  //         //setCurrentPage(1);
  //         setPageCount(Math.ceil(dailySalesDetailsListTemp.length / perPage));

  //         setShowDetails(false);

  //         setCurrentPage(1);
  //       });
  //     } else {
  //       setDailySalesDetailsList([]);

  //       setCurrentPage(1);
  //       setCurrentDetailsPage(1);
  //       setPageReturn(1);
  //       setPerPage(10);
  //       setPageCount(0);
  //       setExpandDetailsDict({});
  //       setShowDetails(false);
  //     }
  //   }, 1000);
  // }, [
  //   allOutlets.length,
  //   allOutletsUserOrdersDone,
  //   currOutletId,
  //   historyStartDate,
  //   historyEndDate,
  //   perPage,

  //   isMounted, // only update this if screen on display
  //   // filterAppType,

  //   reportOutletShifts,
  //   reportDisplayType,
  //   // For Hourly Report
  //   selectedChartDropdownValueX,
  // ]);

  // useEffect(() => {
  //   console.log('useEffect - Dashboard - 49');

  //   if (showDetails && selectedItemSummary.detailsList) {
  //     setTransactionTypeSalesDetails(
  //       selectedItemSummary.detailsList.map(details => {
  //         const findOrder = allOutletsUserOrdersDone.find(
  //           order => order.uniqueId === details.id,
  //         );

  //         return {
  //           ...findOrder,
  //           ...details,
  //         };
  //       }),
  //     );

  //     setPageReturn(currentPage);
  //     // console.log('currentPage value is');
  //     // console.log(currentPage);
  //     setCurrentDetailsPage(1);
  //     setPageCount(Math.ceil(selectedItemSummary.detailsList.length / perPage));
  //   }
  // }, [showDetails, selectedItemSummary, perPage, filterAppType]);

  // 2023-01-03 - Optimizations
  // useEffect(() => {
  //   if (isMounted) {
  //     InteractionManager.runAfterInteractions(() => {
  //       var total = 0;
  //       var totalSales = 0;
  //       var total1 = 0;
  //       var totalTransaction = 0;
  //       // var total2 = 0;
  //       // var totalSold = 0;

  //       for (var i = 0; i < allDaySalesDetails.length; i++) {
  //         total = allDaySalesDetails[i].totalSales;
  //         totalSales += total;
  //       }
  //       for (var i = 0; i < allDaySalesDetails.length; i++) {
  //         total1 = allDaySalesDetails[i].totalTransactions;
  //         totalTransaction += total1;
  //       }
  //       // for( var i = 0; i < allDaySalesDetails.length ; i++ ){
  //       //     total1 = allDaySalesDetails[i].cartItems
  //       //     totalSold += total2
  //       // }

  //       //setAllDayTotalSold(totalSold);
  //       setAllDayTotalTransaction(totalTransaction);
  //       setCurrentOutletTotalSales(totalSales);
  //     });
  //   }
  // }, [
  //   allDaySalesDetails,
  //   allOutlets,
  //   allOutletsUserOrdersDone,
  //   currOutletId,
  //   historyStartDate,
  //   historyEndDate,

  //   isMounted, // only update this if screen on display
  // ]);

  // 2023-01-03 - Optimizations
  // useEffect(() => {
  //   if (isMounted) {
  //     InteractionManager.runAfterInteractions(() => {
  //       var dailySalesDetailsListTemp = [];

  //       var currDate = moment(Date.now());

  //       if (currOutletId && currOutletId.length > 0) {
  //         var record = {
  //           summaryId: nanoid(),
  //           //dateTime: moment(currDateTime).format('D MMM YYYY'),
  //           //dateTimeRaw: currDateTime,
  //           totalSales: 0,
  //           totalTransactions: 0,
  //           totalDiscount: 0,
  //           discount: 0,
  //           tax: 0,
  //           serviceCharge: 0,
  //           gp: 0,
  //           totalSalesReturn: 0,
  //           netSales: 0,
  //           averageNetSales: 0,
  //           detailsList: [],
  //           //cartItems: 0,
  //         };

  //         for (var j = 0; j < allOutletsUserOrdersDone.length; j++) {
  //           if (
  //             allOutletsUserOrdersDone[j].outletId === currOutletId &&
  //             // moment(currDate).isSame(
  //             //   allOutletsUserOrdersDone[j].createdAt,
  //             //   'day',
  //             // )
  //             compareOrderDateByDisplayType(currDate, allOutletsUserOrdersDone[j].createdAt, DATE_COMPARE_TYPE.IS_SAME, 'day', reportDisplayType, reportOutletShifts)
  //           ) {
  //             const discountCalculated = getOrderDiscountInfoInclOrderBased(allOutletsUserOrdersDone[j]);

  //             record.totalSales += allOutletsUserOrdersDone[j].finalPrice + getOrderDiscountInfo(allOutletsUserOrdersDone[j]);
  //             record.totalTransactions += 1;
  //             record.totalDiscount += + getOrderDiscountInfoInclOrderBased(allOutletsUserOrdersDone[j]);
  //             record.discount +=
  //               (discountCalculated /
  //                 allOutletsUserOrdersDone[j].finalPrice) *
  //               100;
  //             record.tax +=
  //               (allOutletsUserOrdersDone[j].tax /
  //                 allOutletsUserOrdersDone[j].finalPrice) *
  //               100;
  //             record.serviceCharge += allOutletsUserOrdersDone[j].sc || 0;
  //             // record.totalSalesReturn += allOutletsUserOrdersDone[j].totalPrice;
  //             if (allOutletsUserOrdersDone[j].cartItemsCancelled) {
  //               for (
  //                 var k = 0;
  //                 k < allOutletsUserOrdersDone[j].cartItemsCancelled.length;
  //                 k++
  //               ) {
  //                 record.totalSalesReturn +=
  //                   allOutletsUserOrdersDone[j].cartItemsCancelled[k].price;
  //               }
  //             }
  //             record.netSales += allOutletsUserOrdersDone[j].totalPrice + getOrderDiscountInfo(allOutletsUserOrdersDone[j]);
  //             record.detailsList.push({
  //               ...allOutletsUserOrdersDone[j],
  //               // cartItems: [],
  //               // uniqueId: allOutletsUserOrdersDone[j].uniqueId,
  //               discountPercentage: parseFloat(
  //                 isFinite(
  //                   allOutletsUserOrdersDone[j].finalPrice /
  //                   discountCalculated,
  //                 )
  //                   ? (allOutletsUserOrdersDone[j].finalPrice /
  //                     discountCalculated) *
  //                   100
  //                   : 0,
  //               ),
  //             });
  //           }
  //         }

  //         if (record.totalTransactions > 0) {
  //           record.averageNetSales = record.netSales / record.totalTransactions;
  //         }

  //         dailySalesDetailsListTemp.push(record);
  //         // }
  //       }

  //       setAllDaySalesDetails(dailySalesDetailsListTemp);
  //     });
  //   }
  //   else {
  //     setAllDaySalesDetails([]);
  //   }
  // }, [
  //   // allOutlets,
  //   // allOutletsUserOrdersDone,
  //   allOutlets,
  //   allOutletsUserOrdersDone,
  //   currOutletId,
  //   historyStartDate,
  //   historyEndDate,

  //   isMounted, // only update this if screen on display
  // ]);

  // useEffect(() => {
  //   console.log('useEffect - Dashboard - 50');

  //   var allOutletsUserOrdersDoneTemp = [];

  //   if (isMounted) {
  //     var currDateTime = moment().valueOf();

  //     if (global.payoutTransactions.length > 0) {
  //       for (var j = 0; j < global.payoutTransactions.length; j++) {
  //         allOutletsUserOrdersDoneTemp = allOutletsUserOrdersDoneTemp.concat(
  //           filterAppType && filterAppType.length > 0
  //             ? global.payoutTransactions[j].userOrdersFigures
  //               ? global.payoutTransactions[j].userOrdersFigures
  //               : []
  //             : (global.payoutTransactions[j].userOrdersFigures
  //               ? global.payoutTransactions[j].userOrdersFigures
  //               : []
  //             ).filter(item =>
  //               //filterChartItems(item, appliedChartFilterQueriesLineChart),
  //               filterAppType.includes(item.appType),
  //             ),
  //         );
  //       }

  //       for (var j = 0; j < global.payoutTransactionsExtend.length; j++) {
  //         allOutletsUserOrdersDoneTemp = allOutletsUserOrdersDoneTemp.concat(
  //           filterAppType && filterAppType.length > 0
  //             ? global.payoutTransactionsExtend[j].userOrdersFigures
  //               ? global.payoutTransactionsExtend[j].userOrdersFigures
  //               : []
  //             : (global.payoutTransactionsExtend[j].userOrdersFigures
  //               ? global.payoutTransactionsExtend[j].userOrdersFigures
  //               : []
  //             ).filter(item =>
  //               //filterChartItems(item, appliedChartFilterQueriesLineChart),
  //               filterAppType.includes(item.appType),
  //             ),
  //         );
  //       }

  //       const startTime = moment().set({ hour: 0, minute: 0, second: 0 }); // Set the start time to 12:00am
  //       const endTime = moment().set({ hour: 5, minute: 55, second: 0 }); // Set the end time to 05:55am

  //       for (var i = 0; i < allOutletsUserOrdersDoneRaw.length; i++) {
  //         if (
  //           moment(allOutletsUserOrdersDoneRaw[i].createdAt).isSame(
  //             currDateTime,
  //             'day',
  //           ) ||
  //           (moment(currDateTime).isBetween(startTime, endTime) &&
  //             moment(currDateTime)
  //               .add(-1, 'day')
  //               .isSame(allOutletsUserOrdersDoneRaw[i].createdAt, 'day'))
  //         ) {
  //           if (
  //             filterAppType.includes(allOutletsUserOrdersDoneRaw[i].appType)
  //           ) {
  //             allOutletsUserOrdersDoneTemp.push(allOutletsUserOrdersDoneRaw[i]);
  //           }
  //         }
  //       }
  //     } else {
  //       allOutletsUserOrdersDoneTemp = allOutletsUserOrdersDoneRaw.filter(
  //         item => filterAppType.includes(item.appType),
  //       );
  //     }
  //   }

  //   // setAllOutletsUserOrdersDone(allOutletsUserOrdersDoneTemp);
  // }, [
  //   allOutletsUserOrdersDoneRaw,
  //   // payoutTransactions,
  //   // payoutTransactionsExtend,
  //   ptTimestamp,
  //   pteTimestamp,
  //   isMounted,
  //   reportOutletShifts,
  //   reportDisplayType,
  //   filterAppType,
  // ]);

  //////////////////////////////////////////////////////////////////////////////////

  // navigation.dangerouslyGetParent().setOptions({
  //     tabBarVisible: false,
  // });

  navigation.setOptions({
    headerBackVisible: false,
    headerLeft: () => <View style={{ width: 70 }}></View>,
    headerTitle: () => (
      <View
        style={{
          alignSelf: 'center',
          marginTop: '2%',
        }}>
        <Image
          style={styles.headerLogo}
          resizeMode="contain"
          source={require('../assets/image/logo_2.png')}
        />
      </View>
    ),
    headerRight: () => (
      <View style={styles.button}>
        <TouchableOpacity
          onPress={() => {
            props.navigation.navigate('Profile');
          }}>
          <Image
            style={styles.drawerIcon}
            source={require('../assets/image/drawer.png')}
          />
        </TouchableOpacity>
      </View>
    ),
  });

  // const [subscriberListenToUserOrderHistoricalChanges, setSubscriberListenToUserOrderHistoricalChanges] = useState(() => { });

  useEffect(() => {
    console.log('useEffect - Dashboard - 51');

    if (merchantId && currOutlet && currOutlet.uniqueId) {
      typeof global.subscriberListenToUserOrderHistoricalChanges ===
        'function' && global.subscriberListenToUserOrderHistoricalChanges();
      global.subscriberListenToUserOrderHistoricalChanges = () => { };

      let subscriberSummary = listenToUserOrderHistoricalChanges(
        merchantId,
        historyStartDate,
        historyEndDate,
        currOutlet.reportDataSizeLimit ? currOutlet.reportDataSizeLimit : 500,
        currOutlet.reportBatchSize ? currOutlet.reportBatchSize : 31,
        currOutlet.uniqueId,
        isMasterAccount,
        selectedOutletList,
      );

      global.subscriberListenToUserOrderHistoricalChanges = subscriberSummary;

      return () => {
        typeof subscriberSummary === 'function' && subscriberSummary();
      };
    }
  }, [
    merchantId,
    historyStartDate,
    historyEndDate,
    currOutlet.reportDataSizeLimit,
    currOutlet.reportBatchSize,
    isMasterAccount,
  ]);

  useEffect(() => {
    console.log('useEffect - Dashboard - 52');

    if (merchantId && currOutlet && currOutlet.uniqueId) {
      typeof global.subscriberListenToOutletReviewHistoricalChanges ===
        'function' && global.subscriberListenToOutletReviewHistoricalChanges();
      global.subscriberListenToOutletReviewHistoricalChanges = () => { };

      let subscriberSummary = listenToOutletReviewHistoricalChanges(
        merchantId,
        outletReviewStartDate,
        outletReviewEndDate,
        currOutlet.orLimit ? currOutlet.orLimit : 1000,
        currOutlet.uniqueId,
        isMasterAccount,
      );

      global.subscriberListenToOutletReviewHistoricalChanges =
        subscriberSummary;

      return () => {
        typeof subscriberSummary === 'function' && subscriberSummary();
      };
    }
  }, [
    merchantId,
    outletReviewStartDate,
    outletReviewEndDate,
    currOutlet.orLimit,
    isMasterAccount,
  ]);

  useEffect(() => {
    console.log('useEffect - Dashboard - 53');

    getTotalSales(0);
    getSalesSummary();
    getTodaySales();
    getOutlets();
  }, []);

  useEffect(() => {
    console.log('useEffect - Dashboard - 54');

    InteractionManager.runAfterInteractions(() => {
      OutletStore.update(s => {
        s.allOutletsUserOrdersDone = allOutletsUserOrdersDoneCache.concat(
          allOutletsUserOrdersDoneRealTime,
        );
        // s.allOutletsUserOrders = allOutletsUserOrdersCache.concat(allOutletsUserOrdersRealTime);
      });
    });
  }, [
    allOutletsUserOrdersDoneCache,
    allOutletsUserOrdersCache,
    allOutletsUserOrdersDoneRealTime,
    allOutletsUserOrdersRealTime,
  ]);

  useEffect(() => {
    console.log('useEffect - Dashboard - 55');

    const mutateUserOrders = () => {
      var userOrdersPrintingNormalTemp = [...userOrdersPrintingNormal];

      if (
        userOrdersPrintingReservation &&
        userOrdersPrintingReservation.length > 0
      ) {
        let printKdOsBeforeMinutes = 60;
        if (
          global.reservationConfig &&
          typeof global.reservationConfig.printKdOsBeforeMinutes === 'number'
        ) {
          printKdOsBeforeMinutes =
            global.reservationConfig.printKdOsBeforeMinutes;
        }

        for (let i = 0; i < userOrdersPrintingReservation.length; i++) {
          if (
            moment(userOrdersPrintingReservation[i].reservationTime).diff(
              Date.now(),
              'minute',
            ) <= printKdOsBeforeMinutes
          ) {
            // within 40 minutes, can print already

            userOrdersPrintingNormalTemp.push(userOrdersPrintingReservation[i]);
          }
        }
      }

      global.userOrdersDt = Date.now();

      let userOrdersTableDictNew = {
        ...userOrdersTableDictNormal,
      };

      const userOrdersTableDictReservationList = Object.entries(
        userOrdersTableDictReservation,
      ).map(([key, value]) => ({ key, value }));
      for (
        let dictIndex = 0;
        dictIndex < userOrdersTableDictReservationList.length;
        dictIndex++
      ) {
        const dictKey = userOrdersTableDictReservationList[dictIndex].key;
        const dictValue = userOrdersTableDictReservationList[dictIndex].value;
        if (userOrdersTableDictNew[dictKey]) {
          userOrdersTableDictNew[dictKey] =
            userOrdersTableDictNew[dictKey].concat(dictValue);
        } else {
          userOrdersTableDictNew[dictKey] = dictValue;
        }
      }

      OutletStore.update(s => {
        s.userOrders = userOrdersNormal.concat(userOrdersReservation);
        // s.userOrdersTableDict = {
        //    ...userOrdersTableDictNormal,
        //    ...userOrdersTableDictReservation,
        //  };
        s.userOrdersTableDict = userOrdersTableDictNew;
        // s.userOrdersPrinting = userOrdersPrintingNormal.concat(userOrdersPrintingReservation);
        s.userOrdersPrinting = userOrdersPrintingNormalTemp;
      });

      // if (!global.supportCodeData && global.currOutlet && !global.currOutlet.reprintOff) {
      //   global.userOrdersPrinting = userOrdersPrintingNormalTemp;
      // }
    };

    InteractionManager.runAfterInteractions(() => {
      mutateUserOrders();

      if (global.mutateUserOrdersInterval !== null) {
        clearInterval(global.mutateUserOrdersInterval);
      }

      global.mutateUserOrdersInterval = setInterval(() => {
        mutateUserOrders();
      }, (currOutlet.muoi ? currOutlet.muoi : 5) * 60000); // minute -> milliseconds
    });
  }, [
    userOrdersNormal,
    userOrdersTableDictNormal,
    userOrdersPrintingNormal,

    userOrdersReservation,
    userOrdersTableDictReservation,
    userOrdersPrintingReservation,
  ]);

  useEffect(() => {
    console.log('useEffect - Dashboard - 56');

    InteractionManager.runAfterInteractions(() => {
      OutletStore.update(s => {
        s.allOutletsUserOrdersLoyaltyDone =
          allOutletsUserOrdersLoyaltyDoneCache.concat(
            allOutletsUserOrdersLoyaltyDoneRealTime,
          );
        s.allOutletsUserOrdersLoyalty = allOutletsUserOrdersLoyaltyCache.concat(
          allOutletsUserOrdersLoyaltyRealTime,
        );
      });
    });
  }, [
    allOutletsUserOrdersLoyaltyDoneCache,
    allOutletsUserOrdersLoyaltyCache,
    allOutletsUserOrdersLoyaltyDoneRealTime,
    allOutletsUserOrdersLoyaltyRealTime,
  ]);

  useEffect(() => {
    console.log('useEffect - Dashboard - 57');

    if (currOutlet && currOutlet.uniqueId) {
      setCurrOutletData();
    }
  }, [currOutlet.uniqueId]);

  useLayoutEffect(() => {
    if (
      currOutlet &&
      (currOutlet.takeawayAutoAuthorizationActive ||
        currOutlet.dineInAutoAuthorizationActive)
    ) {
      clearInterval(global.timerAutoApproveOrders);

      global.timerAutoApproveOrders = setInterval(
        () => {
          if (
            !global.isPrintingNow &&
            !global.isSnapshotChanging &&
            !global.isAuthorizingTakeawayOrders
          ) {
            InteractionManager.runAfterInteractions(() => {
              autoApproveOrders();
            });
          }
        },
        10000, // 60000 -> 20000
      );

      // autoApproveOrders();
    } else {
      if (global.timerAutoApproveOrders) {
        clearInterval(global.timerAutoApproveOrders);
      }
    }
  }, [
    currOutlet.takeawayAutoAuthorizationActive,
    currOutlet.dineInAutoAuthorizationActive,
    userOrdersPrinting,
    firebaseUid,
    userName,
  ]);

  useLayoutEffect(() => {
    if (currOutlet && currOutlet.takeawayAutoApproveOrdersActive) {
      clearInterval(global.timerAutoApproveOrdersTiming);

      global.timerAutoApproveOrdersTiming = setInterval(
        () => {
          if (
            !global.isPrintingNow &&
            !global.isSnapshotChanging &&
            !global.isAuthorizingTakeawayOrders
          ) {
            InteractionManager.runAfterInteractions(() => {
              autoApproveOrdersTiming();
            });
          }
        },
        20000, // 60000 -> 20000
      );

      // autoApproveOrders();
    } else {
      if (global.timerAutoApproveOrdersTiming) {
        clearInterval(global.timerAutoApproveOrdersTiming);
      }
    }
  }, [
    currOutlet.takeawayAutoApproveOrdersActive,
    userOrdersPrinting,
    firebaseUid,
    userName,
  ]);

  // useLayoutEffect(() => {
  //   if (currOutlet && currOutlet.dineInRequiredAuthorization) {
  //     clearInterval(global.timerAutoApproveOrdersDineIn);

  //     global.timerAutoApproveOrdersDineIn = setInterval(() => {
  //       if (!global.isPrintingNow &&
  //         !global.isSnapshotChanging
  //         // &&
  //         // !global.isAuthorizingTakeawayOrders
  //       ) {
  //         InteractionManager.runAfterInteractions(() => {
  //           autoApproveOrders();
  //         });
  //       }
  //     },
  //       10000 // 60000 -> 20000
  //     );

  //     // autoApproveOrders();
  //   }
  //   else {
  //     if (global.timerAutoApproveOrdersDineIn) {
  //       clearInterval(global.timerAutoApproveOrdersDineIn);
  //     }
  //   }
  // }, [currOutlet, userOrders, firebaseUid, userName]);

  /////////////////////////////////////

  // 2022-08-15 - Listen to 'email' and 'userId' based user beer docket

  const selectedCustomerUserBeerDocketsEmail = OutletStore.useState(
    s => s.selectedCustomerUserBeerDocketsEmail,
  );
  const selectedCustomerUserBeerDocketsUserId = OutletStore.useState(
    s => s.selectedCustomerUserBeerDocketsUserId,
  );

  useEffect(() => {
    console.log('useEffect - Dashboard - 58');

    InteractionManager.runAfterInteractions(() => {
      OutletStore.update(s => {
        s.selectedCustomerUserBeerDockets =
          selectedCustomerUserBeerDocketsEmail.concat(
            selectedCustomerUserBeerDocketsUserId,
          );
      });
    });
  }, [
    selectedCustomerUserBeerDocketsEmail,
    selectedCustomerUserBeerDocketsUserId,
  ]);

  //////////////////////////////////////////////////////////////////////////

  // 2022-12-21 - Loyalty stamp changes

  useEffect(() => {
    console.log('useEffect - Dashboard - 59');

    var loyaltyStampGetItemSkuDict = {};
    var loyaltyStampGetCategoryNameDict = {};
    var loyaltyStampBuyItemSkuDict = {};
    var loyaltyStampBuyCategoryNameDict = {};

    for (var i = 0; i < loyaltyStamps.length; i++) {
      if (
        moment().isSameOrAfter(loyaltyStamps[i].startDate, 'day') &&
        moment().isSameOrBefore(loyaltyStamps[i].endDate, 'day')
      ) {
        const loyaltyStamp = loyaltyStamps[i];

        for (var j = 0; j < loyaltyStamp.lsItems.length; j++) {
          if (
            loyaltyStamp.lsItems[j].variation ===
            PROMOTION_TYPE_VARIATION.SPECIFIC_PRODUCTS
          ) {
            loyaltyStampGetItemSkuDict[loyaltyStamp.lsItems[j].outletItemSku] =
            {
              loyaltyStampId: loyaltyStamp.uniqueId,
              lsItemId: loyaltyStamp.lsItems[j].lsItemId,
              noOfStamp: loyaltyStamp.lsItems[j].noOfStamp,
              quantity: loyaltyStamp.lsItems[j].quantity,

              itemSku: loyaltyStamp.lsItems[j].outletItemSku,

              price:
                typeof loyaltyStamp.lsItems[j].price === 'number'
                  ? loyaltyStamp.lsItems[j].price
                  : 0,
            };
          } else if (
            loyaltyStamp.lsItems[j].variation ===
            PROMOTION_TYPE_VARIATION.PRODUCT_OF_CATEGORY
          ) {
            loyaltyStampGetCategoryNameDict[loyaltyStamp.lsItems[j].itemName] =
            {
              loyaltyStampId: loyaltyStamp.uniqueId,
              lsItemId: loyaltyStamp.lsItems[j].lsItemId,
              noOfStamp: loyaltyStamp.lsItems[j].noOfStamp,
              quantity: loyaltyStamp.lsItems[j].quantity,

              categoryId: loyaltyStamp.lsItems[j].outletItemSku,

              price:
                typeof loyaltyStamp.lsItems[j].price === 'number'
                  ? loyaltyStamp.lsItems[j].price
                  : 0,
            };
          }
        }

        for (var j = 0; j < loyaltyStamp.lsItemsBuy.length; j++) {
          if (
            loyaltyStamp.lsItemsBuy[j].variation ===
            PROMOTION_TYPE_VARIATION.SPECIFIC_PRODUCTS
          ) {
            loyaltyStampBuyItemSkuDict[
              loyaltyStamp.lsItemsBuy[j].outletItemSku
            ] = {
              loyaltyStampId: loyaltyStamp.uniqueId,
              lsItemId: loyaltyStamp.lsItemsBuy[j].lsItemId,
              noOfStamp: loyaltyStamp.lsItemsBuy[j].noOfStamp,
              quantity: loyaltyStamp.lsItemsBuy[j].quantity,

              itemSku: loyaltyStamp.lsItemsBuy[j].outletItemSku,
            };
          } else if (
            loyaltyStamp.lsItemsBuy[j].variation ===
            PROMOTION_TYPE_VARIATION.PRODUCT_OF_CATEGORY
          ) {
            loyaltyStampBuyCategoryNameDict[
              loyaltyStamp.lsItemsBuy[j].itemName
            ] = {
              loyaltyStampId: loyaltyStamp.uniqueId,
              lsItemId: loyaltyStamp.lsItemsBuy[j].lsItemId,
              noOfStamp: loyaltyStamp.lsItemsBuy[j].noOfStamp,
              quantity: loyaltyStamp.lsItemsBuy[j].quantity,

              categoryId: loyaltyStamp.lsItemsBuy[j].outletItemSku,
            };
          }
        }
      }
    }

    CommonStore.update(s => {
      s.loyaltyStampGetItemSkuDict = loyaltyStampGetItemSkuDict;
      s.loyaltyStampGetCategoryNameDict = loyaltyStampGetCategoryNameDict;
      s.loyaltyStampBuyItemSkuDict = loyaltyStampBuyItemSkuDict;
      s.loyaltyStampBuyCategoryNameDict = loyaltyStampBuyCategoryNameDict;
    });
  }, [loyaltyStamps]);

  useEffect(() => {
    console.log('useEffect - Dashboard - 60');

    var userLoyaltyStampGetLsItemDict = {};

    for (var i = 0; i < selectedCustomerUserLoyaltyStamps.length; i++) {
      const userLoyaltyStamp = selectedCustomerUserLoyaltyStamps[i];

      for (var j = 0; j < userLoyaltyStamp.getHistory.length; j++) {
        userLoyaltyStampGetLsItemDict[
          userLoyaltyStamp.getHistory[j].lsItemId
        ] = true;
      }
    }

    CommonStore.update(s => {
      s.userLoyaltyStampGetLsItemDict = userLoyaltyStampGetLsItemDict;
    });
  }, [selectedCustomerUserLoyaltyStamps]);

  //////////////////////////////////////////////////////////////////////////

  const autoApproveOrders = async () => {
    console.log('start checking takeaway orders');

    let userOrdersTakeaway = userOrdersPrinting.filter(order => {
      let validStatus = false;

      if (global.printedOrderAutoApproveListLocal.includes(order.uniqueId)) {
        return false;
      }

      if (
        currOutlet.takeawayAutoAuthorizationActive &&
        order.orderType !== ORDER_TYPE.DINEIN &&
        order.orderStatus === USER_ORDER_STATUS.ORDER_RECEIVED &&
        (order.scheduleDateTime === null ||
          order.scheduleDateTime === undefined)
      ) {
        validStatus = true;
      } else if (
        currOutlet.dineInRequiredAuthorization &&
        currOutlet.dineInAutoAuthorizationActive &&
        order.orderType === ORDER_TYPE.DINEIN &&
        order.orderStatus === USER_ORDER_STATUS.ORDER_RECEIVED
      ) {
        validStatus = true;
      }

      return validStatus;
    });

    let intervalMinute = currOutlet.takeawayAutoAuthorizationMinute;

    let toApprovedOrderIdList = [];

    global.isAuthorizingTakeawayOrders = true;

    for (let i = 0; i < userOrdersTakeaway.length; i++) {
      if (
        moment().diff(userOrdersTakeaway[i].orderDate, 'minute') >=
        intervalMinute &&
        !global.printedOrderAutoApproveListLocal.includes(
          userOrdersTakeaway[i].uniqueId,
        )
      ) {
        // means already exceed the specified interval

        toApprovedOrderIdList.push(userOrdersTakeaway[i].uniqueId);
      }
    }

    if (toApprovedOrderIdList.length > 0) {
      console.log('got orders need to approved');
      console.log(toApprovedOrderIdList);

      global.printedOrderAutoApproveListLocal = [
        ...global.printedOrderAutoApproveListLocal,
        ...toApprovedOrderIdList.map(orderId => orderId),
      ];

      // let body = {
      //   orderIdList: toApprovedOrderIdList,
      //   // tableId: param.tableId,
      //   waiterId: firebaseUid,
      //   waiterName: userName,
      // };

      ///////////////////////////////////////////////////////////////////

      // local way

      const userOrderBatch = firestore().batch();

      if (toApprovedOrderIdList) {
        for (let index = 0; index < toApprovedOrderIdList.length; index++) {
          userOrderBatch.update(
            firestore()
              .collection(Collections.UserOrder)
              .doc(toApprovedOrderIdList[index]),
            {
              orderStatus: USER_ORDER_STATUS.ORDER_AUTHORIZED,

              autoAuthorized: true,

              waiterId: firebaseUid,
              waiterName: userName,

              updatedAt: Date.now(),
            },
          );
        }
      }

      await userOrderBatch.commit();

      setTimeout(async () => {
        if (!global.outletToggleDisableAutoPrint) {
          for (let index = 0; index < toApprovedOrderIdList.length; index++) {
            let orderId = toApprovedOrderIdList[index];

            logToFile(`auto approve: ${orderId}`);

            InteractionManager.runAfterInteractions(async () => {
              logToFile('dashboard - printUserOrder - auto approve');

              if (global.currOutlet.autoPrintOsOff) {
              } else {
                printUserOrder(
                  {
                    orderId,
                  },
                  false,
                  [PRINTER_USAGE_TYPE.ORDER_SUMMARY],
                  false,
                  false,
                  false,
                  { isInternetReachable: true, isConnected: true },
                  true, // for isPrioritized
                );
              }

              // await printUserOrder(
              //   {
              //     orderId: orderId,
              //   },
              //   false,
              //   [PRINTER_USAGE_TYPE.KITCHEN_DOCKET],
              //   false,
              //   false,
              //   false,
              //   netInfoData = { isInternetReachable: netInfo.isInternetReachable, isConnected: netInfo.isConnected },
              // );

              if (hadKdTypePrinter(KD_PRINT_VARIATION.SUMMARY)) {
                printUserOrder(
                  {
                    orderId,
                  },
                  false,
                  [PRINTER_USAGE_TYPE.KITCHEN_DOCKET],
                  false,
                  false,
                  false,
                  { isInternetReachable: true, isConnected: true },
                );
              }

              if (hadKdTypePrinter(KD_PRINT_VARIATION.SUMMARY_CATEGORY)) {
                printKDSummaryCategoryWrapper({
                  orderId,
                });
              }

              // if (global.outletKdVariation === KD_PRINT_VARIATION.SUMMARY) {
              //   await printUserOrder(
              //     {
              //       orderId: orderId,
              //     },
              //     false,
              //     [PRINTER_USAGE_TYPE.KITCHEN_DOCKET],
              //     false,
              //     false,
              //     false,
              //     { isInternetReachable: true, isConnected: true },
              //   );
              // }
              // else if (global.outletKdVariation === KD_PRINT_VARIATION.SUMMARY_CATEGORY) {
              //   printKDSummaryCategoryWrapper(
              //     {
              //       orderId: orderId,
              //     },
              //   );
              // }

              ///////////////////////////////////

              try {
                // get order

                // for beer docket

                if (hadKdTypePrinter(KD_PRINT_VARIATION.INDIVIDUAL)) {
                  const userOrderSnapshot = await firestore()
                    .collection(Collections.UserOrder)
                    .where('uniqueId', '==', orderId)
                    .limit(1)
                    .get();

                  let order = null;
                  if (!userOrderSnapshot.empty) {
                    order = userOrderSnapshot.docs[0].data();
                  }

                  const printerIpCountDict =
                    await calcPrintTotalForKdIndividual({
                      userOrder: order,
                    });
                  const printerTaskId = uuidv4();
                  global.printingTaskIdDict[printerTaskId] = {};

                  for (
                    let bdIndex = 0;
                    bdIndex < order.cartItems.length;
                    bdIndex++
                  ) {
                    if (!order.cartItems[bdIndex].isDocket) {
                      printDocketForKD(
                        {
                          userOrder: order,
                          cartItem: order.cartItems[bdIndex],
                          printerIpCountDict: printerIpCountDict,
                          printerTaskId: printerTaskId,
                        },
                        // true,
                        [PRINTER_USAGE_TYPE.KITCHEN_DOCKET],
                        // selectedPaymentMethodType === OFFLINE_PAYMENT_METHOD_TYPE.CASH,
                      );
                    }
                  }

                  // if (global.outletKdVariation === KD_PRINT_VARIATION.INDIVIDUAL) {
                  //   for (let bdIndex = 0; bdIndex < order.cartItems.length; bdIndex++) {
                  //     printDocketForKD(
                  //       {
                  //         userOrder: order,
                  //         cartItem: order.cartItems[bdIndex],
                  //       },
                  //       // true,
                  //       [PRINTER_USAGE_TYPE.KITCHEN_DOCKET],
                  //       // selectedPaymentMethodType === OFFLINE_PAYMENT_METHOD_TYPE.CASH,
                  //     );
                  //   }
                  // }

                  if (order && order.cartItems) {
                    for (
                      let index = 0;
                      index < order.cartItems.length;
                      index++
                    ) {
                      if (order.cartItems[index].isDocket) {
                        await printDocket(
                          {
                            userOrder: order,
                            cartItem: order.cartItems[index],
                          },
                          // true,
                          [PRINTER_USAGE_TYPE.RECEIPT],
                        );
                      }
                    }
                  }
                }
              } catch (ex) {
                console.error(ex);
              }

              ///////////////////////////////////
            });
          }
        }

        global.isAuthorizingTakeawayOrders = false;
      }, 1000);

      ///////////////////////////////////////////////////////////////////

      // ApiClient.POST(API.authorizeUserOrderMultipleByMerchant, body, false)
      //   .then(
      //     async (result) => {
      //       if (result && result.status === 'success') {
      //         for (let index = 0; index < toApprovedOrderIdList.length; index++) {
      //           let orderId = toApprovedOrderIdList[index];

      //           await printUserOrder(
      //             {
      //               orderId: orderId,
      //             },
      //             false,
      //             [PRINTER_USAGE_TYPE.ORDER_SUMMARY],
      //             false,
      //             false,
      //             false,
      //             netInfoData = { isInternetReachable: netInfo.isInternetReachable, isConnected: netInfo.isConnected },
      //           );

      //           await printUserOrder(
      //             {
      //               orderId: orderId,
      //             },
      //             false,
      //             [PRINTER_USAGE_TYPE.KITCHEN_DOCKET],
      //             false,
      //             false,
      //             false,
      //             netInfoData = { isInternetReachable: netInfo.isInternetReachable, isConnected: netInfo.isConnected },
      //           );
      //         }
      //       } else {
      //       }

      //       global.isAuthorizingTakeawayOrders = false;
      //     }
      //   )
      //   .catch(ex => {
      //     console.error(ex);

      //     global.isAuthorizingTakeawayOrders = false;
      //   });
    } else {
      global.isAuthorizingTakeawayOrders = false;
    }
  };
  const autoApproveOrdersTiming = async () => {
    console.log('start checking takeaway orders');
    let userOrdersTakeaway = userOrdersPrinting.filter(
      order =>
        order.orderType !== ORDER_TYPE.DINEIN &&
        order.orderStatus === USER_ORDER_STATUS.ORDER_RECEIVED &&
        order.scheduleDateTime !== null &&
        order.scheduleDateTime !== undefined,
    );

    let intervalMinute = 40;

    let toApprovedOrderIdList = [];

    global.isAuthorizingTakeawayOrders = true;

    for (let i = 0; i < userOrdersTakeaway.length; i++) {
      console.log('moment time diff check');

      // if (global.printedOrderAutoApproveListLocal.includes(order.uniqueId)) {
      //   return false;
      // }

      console.log(
        moment(userOrdersTakeaway[i].scheduleDateTime).diff(
          moment(),
          'minute',
        ) < 40,
      );

      if (
        moment(userOrdersTakeaway[i].scheduleDateTime).diff(
          moment(),
          'minute',
        ) <= intervalMinute
      ) {
        // means already exceed the specified interval

        toApprovedOrderIdList.push(userOrdersTakeaway[i]);
      }
    }

    if (toApprovedOrderIdList.length > 0) {
      console.log('got orders need to approved');
      console.log(toApprovedOrderIdList);

      PushNotification.localNotification({
        id: NOTIFICATIONS_ID.SCHEDULE_ORDER,

        channelId: NOTIFICATIONS_CHANNEL.SCHEDULE_ORDER,

        title: `A ${ORDER_TYPE_PARSED[
          ORDER_TYPE.PICKUP
        ].toLowerCase()} order placed!`,
        message: `Total: RM${toApprovedOrderIdList[0].finalPrice}`,
        data: {
          type: NOTIFICATIONS_TYPE.SCHEDULE_ORDER,
        },
        userInfo: {
          type: NOTIFICATIONS_TYPE.SCHEDULE_ORDER,
        },

        group: NOTIFICATIONS_TYPE.SCHEDULE_ORDER,
        groupSummary: true,
      });

      // let body = {
      //   orderIdList: toApprovedOrderIdList,
      //   // tableId: param.tableId,
      //   waiterId: firebaseUid,
      //   waiterName: userName,
      // };

      ///////////////////////////////////////////////////////////////////

      // local way

      const userOrderBatch = firestore().batch();

      if (toApprovedOrderIdList) {
        for (let index = 0; index < toApprovedOrderIdList.length; index++) {
          console.log(toApprovedOrderIdList[index]);

          userOrderBatch.update(
            firestore()
              .collection(Collections.UserOrder)
              .doc(toApprovedOrderIdList[index].uniqueId),
            {
              orderStatus: USER_ORDER_STATUS.ORDER_AUTHORIZED,

              waiterId: firebaseUid,
              waiterName: userName,

              updatedAt: Date.now(),
            },
          );
        }
      }

      console.log('before');

      await userOrderBatch.commit();

      console.log('after');

      setTimeout(async () => {
        if (!global.outletToggleDisableAutoPrint) {
          for (let index = 0; index < toApprovedOrderIdList.length; index++) {
            let orderId = toApprovedOrderIdList[index].uniqueId;

            InteractionManager.runAfterInteractions(async () => {
              logToFile('dashboard - printUserOrder - auto approve timing');

              if (global.currOutlet.autoPrintOsOff) {
              } else {
                printUserOrder(
                  {
                    orderId,
                  },
                  false,
                  [PRINTER_USAGE_TYPE.ORDER_SUMMARY],
                  false,
                  false,
                  false,
                  (netInfoData = {
                    isInternetReachable: netInfo.isInternetReachable,
                    isConnected: netInfo.isConnected,
                  }),
                  true, // for isPrioritized
                );
              }

              // await printUserOrder(
              //   {
              //     orderId: orderId,
              //   },
              //   false,
              //   [PRINTER_USAGE_TYPE.KITCHEN_DOCKET],
              //   false,
              //   false,
              //   false,
              //   netInfoData = { isInternetReachable: netInfo.isInternetReachable, isConnected: netInfo.isConnected },
              // );

              if (hadKdTypePrinter(KD_PRINT_VARIATION.SUMMARY)) {
                printUserOrder(
                  {
                    orderId,
                  },
                  false,
                  [PRINTER_USAGE_TYPE.KITCHEN_DOCKET],
                  false,
                  false,
                  false,
                  { isInternetReachable: true, isConnected: true },
                );
              }

              if (hadKdTypePrinter(KD_PRINT_VARIATION.SUMMARY_CATEGORY)) {
                printKDSummaryCategoryWrapper({
                  orderId,
                });
              }

              // if (global.outletKdVariation === KD_PRINT_VARIATION.SUMMARY) {
              //   await printUserOrder(
              //     {
              //       orderId: orderId,
              //     },
              //     false,
              //     [PRINTER_USAGE_TYPE.KITCHEN_DOCKET],
              //     false,
              //     false,
              //     false,
              //     { isInternetReachable: true, isConnected: true },
              //   );
              // }
              // else if (global.outletKdVariation === KD_PRINT_VARIATION.SUMMARY_CATEGORY) {
              //   printKDSummaryCategoryWrapper(
              //     {
              //       orderId: orderId,
              //     },
              //   );
              // }

              ///////////////////////////////////

              try {
                // get order

                // for beer docket

                if (hadKdTypePrinter(KD_PRINT_VARIATION.INDIVIDUAL)) {
                  const userOrderSnapshot = await firestore()
                    .collection(Collections.UserOrder)
                    .where('uniqueId', '==', orderId)
                    .limit(1)
                    .get();

                  let order = null;
                  if (!userOrderSnapshot.empty) {
                    order = userOrderSnapshot.docs[0].data();
                  }

                  const printerIpCountDict =
                    await calcPrintTotalForKdIndividual({
                      userOrder: order,
                    });
                  const printerTaskId = uuidv4();
                  global.printingTaskIdDict[printerTaskId] = {};

                  for (
                    let bdIndex = 0;
                    bdIndex < order.cartItems.length;
                    bdIndex++
                  ) {
                    if (!order.cartItems[bdIndex].isDocket) {
                      printDocketForKD(
                        {
                          userOrder: order,
                          cartItem: order.cartItems[bdIndex],
                          printerIpCountDict: printerIpCountDict,
                          printerTaskId: printerTaskId,
                        },
                        // true,
                        [PRINTER_USAGE_TYPE.KITCHEN_DOCKET],
                        // selectedPaymentMethodType === OFFLINE_PAYMENT_METHOD_TYPE.CASH,
                      );
                    }
                  }

                  // if (global.outletKdVariation === KD_PRINT_VARIATION.INDIVIDUAL) {
                  //   for (let bdIndex = 0; bdIndex < order.cartItems.length; bdIndex++) {
                  //     printDocketForKD(
                  //       {
                  //         userOrder: order,
                  //         cartItem: order.cartItems[bdIndex],
                  //       },
                  //       // true,
                  //       [PRINTER_USAGE_TYPE.KITCHEN_DOCKET],
                  //       // selectedPaymentMethodType === OFFLINE_PAYMENT_METHOD_TYPE.CASH,
                  //     );
                  //   }
                  // }

                  if (order && order.cartItems) {
                    for (
                      let index = 0;
                      index < order.cartItems.length;
                      index++
                    ) {
                      if (order.cartItems[index].isDocket) {
                        await printDocket(
                          {
                            userOrder: order,
                            cartItem: order.cartItems[index],
                          },
                          // true,
                          [PRINTER_USAGE_TYPE.RECEIPT],
                        );
                      }
                    }
                  }
                }
              } catch (ex) {
                console.error(ex);
              }

              ///////////////////////////////////
            });
          }
        }

        global.isAuthorizingTakeawayOrders = false;
      }, 5000);

      ///////////////////////////////////////////////////////////////////

      // ApiClient.POST(API.authorizeUserOrderMultipleByMerchant, body, false)
      //   .then(
      //     async (result) => {
      //       if (result && result.status === 'success') {
      //         for (let index = 0; index < toApprovedOrderIdList.length; index++) {
      //           let orderId = toApprovedOrderIdList[index];

      //           await printUserOrder(
      //             {
      //               orderId: orderId,
      //             },
      //             false,
      //             [PRINTER_USAGE_TYPE.ORDER_SUMMARY],
      //             false,
      //             false,
      //             false,
      //             netInfoData = { isInternetReachable: netInfo.isInternetReachable, isConnected: netInfo.isConnected },
      //           );

      //           await printUserOrder(
      //             {
      //               orderId: orderId,
      //             },
      //             false,
      //             [PRINTER_USAGE_TYPE.KITCHEN_DOCKET],
      //             false,
      //             false,
      //             false,
      //             netInfoData = { isInternetReachable: netInfo.isInternetReachable, isConnected: netInfo.isConnected },
      //           );
      //         }
      //       } else {
      //       }

      //       global.isAuthorizingTakeawayOrders = false;
      //     }
      //   )
      //   .catch(ex => {
      //     console.error(ex);

      //     global.isAuthorizingTakeawayOrders = false;
      //   });
    } else {
      global.isAuthorizingTakeawayOrders = false;
    }
  };

  const setCurrOutletData = async () => {
    // await AsyncStorage.setItem('isQRPrintReceipt', currOutlet.isQRPrintReceipt ? '1' : '0');

    global.isQRPrintReceipt = currOutlet.isQRPrintReceipt ? '1' : '0';

    // await AsyncStorage.setItem('isQRNotPrintLogo', currOutlet.isQRNotPrintLogo ? '1' : '0');

    global.isQRNotPrintLogo = currOutlet.isQRNotPrintLogo ? '1' : '0';
  };

  ///////////////////////////////////////////////////////////////////////

  // 2024-09-18 - detech sunmi printers

  // const detectSunmiPrintersWrapper = () => {
  //   detectSunmiPrinters(sunmiPrinters);
  // };

  // useEffect(() => {
  //     setTimeout(() => {
  //       detectSunmiPrintersWrapper();
  //     }, 10000);
  // }, [

  // ]);

  ///////////////////////////////////////////////////////////////////////

  // 2025-05-28 - optimization

  const listenToUserOrderPrinting = currOutletId => {
    console.log(`printDt start: ${moment().subtract(5, 'minute').valueOf()}`);
    console.log(`printDt end: ${moment().add(60, 'minute').valueOf()}`);
    logToFile(`printDt start: ${moment().subtract(5, 'minute').valueOf()}`);
    logToFile(`printDt end: ${moment().add(60, 'minute').valueOf()}`);

    let subscriberUserOrder = firestore()
      .collection(Collections.UserOrder)
      .where('outletId', '==', currOutletId)
      // .where('orderStatus', '!=', USER_ORDER_STATUS.ORDER_COMPLETED)
      .where('orderStatus', 'in', [
        USER_ORDER_STATUS.ORDER_RECEIVED,
        USER_ORDER_STATUS.ORDER_AUTHORIZED,
        USER_ORDER_STATUS.ORDER_PREPARING,
        USER_ORDER_STATUS.ORDER_PREPARED,
        USER_ORDER_STATUS.ORDER_DELIVERED,

        USER_ORDER_STATUS.ORDER_REJECTED_BY_MERCHANT,
        USER_ORDER_STATUS.ORDER_CANCELLED_BY_MERCHANT,
        USER_ORDER_STATUS.ORDER_CANCELLED_BY_USER,

        USER_ORDER_STATUS.ORDER_SENDER_REJECTED,
        USER_ORDER_STATUS.ORDER_SENDER_CANCELED,
      ])
      .where('printDt', '>=', moment().subtract(5, 'minute').valueOf())
      .where('printDt', '<', moment().add(60, 'minute').valueOf())
      // .orderBy('createdAt') // invalid query, need fix this in future
      .onSnapshot(snapshot => {
        var userOrdersPrinting = [];
        var userOrdersPrintingReservation = [];

        // var userOrdersAllStatus = [];

        if (snapshot.size) {
          logToFile(`snapshot.size: ${snapshot.size}`);
        }

        if (snapshot && !snapshot.empty) {
          for (var i = 0; i < snapshot.size; i++) {
            const record = snapshot.docs[i].data();

            if (
              record.orderStatus !==
              USER_ORDER_STATUS.ORDER_CANCELLED_BY_MERCHANT &&
              record.orderStatus !==
              USER_ORDER_STATUS.ORDER_CANCELLED_BY_USER &&
              record.orderStatus !==
              USER_ORDER_STATUS.ORDER_REJECTED_BY_MERCHANT
            ) {
              if (
                record.orderStatus === USER_ORDER_STATUS.ORDER_RECEIVED ||
                record.orderStatus === USER_ORDER_STATUS.ORDER_AUTHORIZED
              ) {
                if (!record.isReservationOrder) {
                  userOrdersPrinting.push(record);
                } else {
                  userOrdersPrintingReservation.push(record);
                }
              }
            }
          }
        }

        if (snapshot) {
          OutletStore.update(s => {
            s.userOrdersPrintingNormal = userOrdersPrinting;
            s.userOrdersPrintingReservation = userOrdersPrintingReservation;
          });
        }
      });

    return () => {
      subscriberUserOrder();
    };
  };

  useEffect(() => {
    logToFile(`useEffect - listenToUserOrderPrinting`);
    logToFile(`currOutletId: ${currOutletId}`);
    logToFile(`timerUserOrderPrinting: ${timerUserOrderPrinting}`);

    if (currOutletId && timerUserOrderPrinting) {
      logToFile(`listenToUserOrderPrinting - before`);

      // listenToFirestoreChanges(firebaseUid, merchantId, currOutletId);

      typeof global.subscriberUserOrderPrinting === 'function' &&
        global.subscriberUserOrderPrinting();
      global.subscriberUserOrderPrinting = () => { };

      let subscriber = listenToUserOrderPrinting(currOutletId);

      global.subscriberUserOrderPrinting = subscriber;
    } else {
      typeof global.subscriberUserOrderPrinting === 'function' &&
        global.subscriberUserOrderPrinting();
    }

    return () => {
      typeof subscriber === 'function' && subscriber();
    };
  }, [
    // merchantId,
    currOutletId,

    timerUserOrderPrinting,
  ]);

  useEffect(() => {
    // CommonStore.update(s => {
    //   s.timerUserOrderPrinting = Date.now();
    // });

    setInterval(() => {
      CommonStore.update(s => {
        s.timerUserOrderPrinting = Date.now();
      });
    }, 1000 * 60 * 15); // every 30 minutes
  }, []);

  const listenToUserOrderSelectedTable = (
    currOutletId,
    toggleOpenOrder,
    openOrderDays,
    selectedOutletTableId,
    selectedOutletTableIdList,
  ) => {
    // let selectedOutletTableIdListFiltered = selectedOutletTableIdList;
    // if (!selectedOutletTableIdListFiltered.includes(selectedOutletTableId)) {
    //   selectedOutletTableIdListFiltered.push(selectedOutletTableId);
    // }

    let subscriberUserOrder = firestore()
      .collection(Collections.UserOrder)
      .where('tableId', '==', selectedOutletTableId)
      // .where('outletId', '==', currOutletId)
      .where('orderStatus', '!=', USER_ORDER_STATUS.ORDER_COMPLETED)
      .where(
        'createdAt',
        '>=',
        !toggleOpenOrder
          ? moment().subtract(1, 'day').startOf('day').valueOf()
          : moment().subtract(openOrderDays, 'day').startOf('day').valueOf(),
      ) // to limit to recent orders only
      .onSnapshot(snapshot => {
        global.isLoadingUserOrderSelectedTableInnerUpdates = true;

        // if (snapshot.metadata.fromCache && snapshot.empty) {
        //   // Possibly skip showing "no data" message yet
        //   return;
        // }

        let cacheRetrieved = false;
        if (snapshot.metadata.fromCache && snapshot.empty) {
          // Optional: Show cached data if offline
          // Or show "offline" banner + stale data

          console.log('cache retrieved');
          console.log(snapshot.metadata.fromCache);
          console.log(snapshot.empty);
          console.log(snapshot.metadata.hasPendingWrites);

          cacheRetrieved = true;

          // return;
        }

        if (snapshot) {
          console.log('ltuost');
          logToFile(snapshot.metadata.fromCache);
          logToFile(snapshot.empty);
          logToFile(snapshot.metadata.hasPendingWrites);
        }

        if (global.listenerUserOrderSelectedTableTimerId) {
          clearTimeout(global.listenerUserOrderSelectedTableTimerId);
        }

        global.listenerUserOrderSelectedTableTimerId = setTimeout(() => {
          var userOrdersTableDict = {};
          let userOrdersNormalTemp = [];

          if (snapshot && !snapshot.empty) {
            for (var i = 0; i < snapshot.size; i++) {
              const record = snapshot.docs[i].data();

              if (
                record.orderStatus !==
                USER_ORDER_STATUS.ORDER_CANCELLED_BY_MERCHANT &&
                record.orderStatus !==
                USER_ORDER_STATUS.ORDER_CANCELLED_BY_USER &&
                record.orderStatus !==
                USER_ORDER_STATUS.ORDER_REJECTED_BY_MERCHANT
              ) {
                if (
                  record.tableId &&
                  record.tableId.length > 0 &&
                  record.cartItems.length > 0
                ) {
                  if (userOrdersTableDict[record.tableId] === undefined) {
                    userOrdersTableDict[record.tableId] = [record];
                  } else {
                    // userOrdersTableDict[record.tableId] = [
                    //   ...userOrdersTableDict[record.tableId],
                    //   record,
                    // ];
                    userOrdersTableDict[record.tableId].push(record);
                  }

                  userOrdersNormalTemp.push(record);
                }
              }
            }
          }

          if (snapshot) {
            // if (selectedOrderToPayUserList.length === 0 && selectedOrderToPayUserId === undefined) {
            //   const selectedOrderToPayUserIdDictTemp = {};
            //   let selectedOrderToPayUserListTemp = [];

            //   if (userOrdersTableDict[selectedOutletTableId]) {
            //     userOrdersTableDict[selectedOutletTableId].forEach(userOrder => {
            //       selectedOrderToPayUserIdDictTemp[userOrder.userId] = {
            //         userId: userOrder.userId,
            //         userName: userOrder.userName || 'Guest',
            //         userPhone: userOrder.userPhone || '',
            //         userAddress: userOrder.userAddress || 'N/A',
            //         orderId: userOrder.orderId || 'N/A',
            //       };
            //     });
            //   }

            //   selectedOrderToPayUserListTemp = Object.entries(selectedOrderToPayUserIdDictTemp)
            //     .map(([key, obj]) => ({
            //       label: `${obj.userName} #${obj.orderId}`,
            //       value: obj.userId,
            //       userId: obj.userId,
            //       email: obj.email,
            //     }));

            //   const item = outletTables.find(table => table.uniqueId === selectedOutletTableId);

            //   if (item && item?.payUserIdList?.length > 0) {
            //     item.payUserIdList.forEach(payUserId => {
            //       if (!selectedOrderToPayUserIdDictTemp[payUserId]) {
            //         const crmUser = crmUsers.find(user => user.userId === payUserId || user.email === payUserId);
            //         if (crmUser) {
            //           selectedOrderToPayUserIdDictTemp[payUserId] = {
            //             userId: crmUser.userId || crmUser.email,
            //             userName: crmUser.name || 'Guest',
            //             userPhone: crmUser.number || '',
            //             userAddress: 'N/A',
            //             orderId: 'N/A',
            //             userEmail: crmUser.email || '',
            //           };

            //           selectedOrderToPayUserListTemp.push({
            //             label: crmUser.name,
            //             value: crmUser.userId || crmUser.email,
            //             userId: crmUser.userId,
            //             email: crmUser.email,
            //           });
            //         }
            //       }
            //     });
            //   }

            //   // global.viewTableOrderModalPrev = viewTableOrderModal;

            //   console.log(selectedOrderToPayUserListTemp);
            //   console.log(selectedOrderToPayUserIdDictTemp);

            //   TableStore.update(s => {
            //     s.selectedOrderToPayUserList = selectedOrderToPayUserListTemp;
            //     s.selectedOrderToPayUserIdDict = selectedOrderToPayUserIdDictTemp;
            //     // s.viewTableOrderModal = true;
            //   });

            //   CommonStore.update((s) => {
            //     s.selectedOrderToPayUserId = selectedOrderToPayUserListTemp[0]?.value;
            //   });
            // }

            OutletStore.update(s => {
              // const keyCount = Object.keys(userOrdersTableDictNormal).length;

              // if (keyCount > (global.currOutlet && global.currOutlet.uotdKeyCount ? global.currOutlet.uotdKeyCount : 0)) {
              //   s.userOrdersTableDictNormal = userOrdersTableDict;
              // }
              // else {
              //   s.userOrdersTableDictNormal = {
              //     ...userOrdersTableDictNormal,
              //     ...userOrdersTableDict,
              //   };
              // }

              s.userOrdersTableDictNormal = userOrdersTableDict;
              s.userOrdersNormal = userOrdersNormalTemp;
            });

            if (userOrdersNormalTemp.length <= 0) {
              // need clear lastOrderDt, since this table is active but no more orders

              if (!global.movingItemsToTable) {
                console.log('lastOrderDt clear - 1');

                // 2025-06-04 - no need first
                firestore()
                  .collection(Collections.OutletTable)
                  .doc(selectedOutletTableId)
                  .update({
                    lastOrderDt: null,
                    tps: '',

                    updatedAt: Date.now(),
                  });

                TableStore.update(s => {
                  s.orderDisplayIndividual = false;
                  s.orderDisplayProduct = false;
                  s.orderDisplaySummary = true;

                  s.viewTableOrderModal = false;
                  s.renderPaymentSummary = false;
                  s.renderReceipt = false;

                  s.displayQrModal = false;
                  s.displayQModal = false;
                  s.deleteTableModal = false;
                  s.updateTableModal = false;
                  s.joinTableModal = false;
                  s.moveOrderModal = false;
                  s.addSectionAreaModel = false;
                  s.addTableModal = false;
                  s.preventDeleteTableModal = false;
                  s.seatingModal = false;
                  s.showLoyaltyModal = false;
                  s.showAddLoyaltyModal = false;
                  s.cashbackModal = false;
                });

                CommonStore.update(s => {
                  // 2025-06-07 - to address issue when place takeaway orders, the last selected table still listen to snapshot, and causing side effects: table auto unseated
                  s.selectedOutletTable = {};
                });
              }
            }
          }

          CommonStore.update(s => {
            s.isLoadingUserOrderSelectedTable = false;
          });

          global.isLoadingUserOrderSelectedTable = false;

          global.isLoadingUserOrderSelectedTableInnerUpdates = false;
        }, (global.currOutlet.lusstTimeout ? global.currOutlet.lusstTimeout : 100) + (cacheRetrieved ? (global.currOutlet.lusstcrTimeout ? global.currOutlet.lusstcrTimeout : 3000) : 0));
      });

    return () => {
      subscriberUserOrder();
    };

    // let selectedOutletTableIdListFiltered = selectedOutletTableIdList;
    // if (!selectedOutletTableIdListFiltered.includes(selectedOutletTableId)) {
    //   selectedOutletTableIdListFiltered.push(selectedOutletTableId);
    // }

    // // Chunk the table IDs into groups of 10 (Firebase 'in' operator limit)
    // const chunkArray = (array, chunkSize) => {
    //   const results = [];
    //   for (let i = 0; i < array.length; i += chunkSize) {
    //     results.push(array.slice(i, i + chunkSize));
    //   }
    //   return results;
    // };

    // const tableIdChunks = chunkArray(selectedOutletTableIdList, 10);

    // // Array to hold all unsubscribe functions
    // const subscribers = [];
    // // Object to accumulate results from all chunks
    // let combinedUserOrdersTableDict = {};

    // // Create a listener for each chunk
    // tableIdChunks.forEach(chunk => {
    //   const unsubscribe = firestore()
    //     .collection(Collections.UserOrder)
    //     .where('tableId', 'in', chunk)
    //     .where('orderStatus', '!=', USER_ORDER_STATUS.ORDER_COMPLETED)
    //     .where(
    //       'createdAt',
    //       '>=',
    //       !toggleOpenOrder
    //         ? moment().subtract(1, 'day').startOf('day').valueOf()
    //         : moment().subtract(openOrderDays, 'day').startOf('day').valueOf()
    //     )
    //     .onSnapshot((snapshot) => {
    //       // Create a temporary dictionary for this snapshot's results
    //       const chunkUserOrdersTableDict = {};

    //       if (snapshot && !snapshot.empty) {
    //         for (let i = 0; i < snapshot.size; i++) {
    //           const record = snapshot.docs[i].data();

    //           if (record.orderStatus !== USER_ORDER_STATUS.ORDER_CANCELLED_BY_MERCHANT &&
    //             record.orderStatus !== USER_ORDER_STATUS.ORDER_CANCELLED_BY_USER) {
    //             if (record.tableId && record.tableId.length > 0) {
    //               if (chunkUserOrdersTableDict[record.tableId] === undefined) {
    //                 chunkUserOrdersTableDict[record.tableId] = [record];
    //               } else {
    //                 chunkUserOrdersTableDict[record.tableId].push(record);
    //               }
    //             }
    //           }
    //         }
    //       }

    //       // Merge this chunk's results into the combined dictionary
    //       combinedUserOrdersTableDict = {
    //         ...combinedUserOrdersTableDict,
    //         ...chunkUserOrdersTableDict
    //       };

    //       // Update the store with the combined results
    //       OutletStore.update((s) => {
    //         s.userOrdersTableDictNormal = combinedUserOrdersTableDict;
    //       });

    //       CommonStore.update(s => {
    //         s.isLoadingUserOrderSelectedTable = false;
    //       });

    //       global.isLoadingUserOrderSelectedTable = false;
    //     });

    //   subscribers.push(unsubscribe);
    // });

    // // Return cleanup function that unsubscribes all listeners
    // return () => {
    //   subscribers.forEach(unsubscribe => unsubscribe());
    // };
  };

  useEffect(() => {
    console.log(`useEffect - listenToUserOrderSelectedTable`);
    console.log(
      `${currOutletId} | ${currOutlet.uniqueId} | ${selectedOutletTable.uniqueId} | ${selectedOutletTable.lastOrderDt}`,
    );

    if (
      currOutletId &&
      currOutlet.uniqueId &&
      selectedOutletTable.uniqueId &&
      selectedOutletTable.lastOrderDt
      // && selectedOutletTableIdList && selectedOutletTableIdList.length > 0
    ) {
      // listenToFirestoreChanges(firebaseUid, merchantId, currOutletId);

      // if (userOrdersTableDictNormal[selectedOutletTable.uniqueId]) {

      // }
      // else {
      //   CommonStore.update(s => {
      //     s.isLoadingUserOrderSelectedTable = true;
      //   });

      //   global.isLoadingUserOrderSelectedTable = true;
      // }

      CommonStore.update(s => {
        s.isLoadingUserOrderSelectedTable = true;
      });

      global.isLoadingUserOrderSelectedTable = true;

      typeof global.subscriberUserOrderSelectedTable === 'function' &&
        global.subscriberUserOrderSelectedTable();
      global.subscriberUserOrderSelectedTable = () => { };

      let subscriber = listenToUserOrderSelectedTable(
        currOutletId,
        currOutlet.toggleOpenOrder,
        currOutlet.openOrderDays ? currOutlet.openOrderDays : 30,
        selectedOutletTable.uniqueId,
        selectedOutletTableIdList,
      );

      global.subscriberUserOrderSelectedTable = subscriber;
    } else {
      typeof global.subscriberUserOrderSelectedTable === 'function' &&
        global.subscriberUserOrderSelectedTable();
    }

    return () => {
      typeof subscriber === 'function' && subscriber();

      OutletStore.update(s => {
        s.userOrdersTableDictNormal = {};
        s.userOrdersNormal = [];
      });
    };
  }, [
    // merchantId,
    currOutletId,

    currOutlet.toggleOpenOrder,
    currOutlet.openOrderDays,

    selectedOutletTable.uniqueId,
    selectedOutletTable.lastOrderDt,

    // selectedOutletTableIdList,
  ]);

  // useEffect(() => {
  //   let selectedOutletTableIdListFiltered = [...selectedOutletTableIdList];
  //   if (selectedOutletTable && selectedOutletTable.uniqueId &&
  //     !selectedOutletTableIdListFiltered.includes(selectedOutletTable.uniqueId)) {
  //     selectedOutletTableIdListFiltered.push(selectedOutletTable.uniqueId);

  //     CommonStore.update(s => {
  //       s.selectedOutletTableIdList = selectedOutletTableIdListFiltered;
  //     });
  //   }
  // }, [
  //   selectedOutletTable,
  //   selectedOutletTableIdList,
  // ]);

  ///////////////////////////////////////////////////////////////////////

  // 2025-05-29 - still listen to dine in orders first

  // const listenToUserOrderActiveTable = (currOutletId, toggleOpenOrder, openOrderDays) => {
  //   // let selectedOutletTableIdListFiltered = selectedOutletTableIdList;
  //   // if (!selectedOutletTableIdListFiltered.includes(selectedOutletTableId)) {
  //   //   selectedOutletTableIdListFiltered.push(selectedOutletTableId);
  //   // }

  //   let subscriberUserOrder = firestore()
  //     .collection(Collections.UserOrder)
  //     // .where('tableId', '==', selectedOutletTableId)
  //     .where('outletId', '==', currOutletId)
  //     .where('orderType', '==', ORDER_TYPE.DINEIN)
  //     .where('orderTypeSub', '==', ORDER_TYPE_SUB.NORMAL)
  //     .where('orderStatus', '!=', USER_ORDER_STATUS.ORDER_COMPLETED)
  //     .where(
  //       'createdAt',
  //       '>=',
  //       !toggleOpenOrder
  //         ?
  //         moment().subtract(1, 'day').startOf('day').valueOf()
  //         :
  //         moment().subtract(openOrderDays, 'day').startOf('day').valueOf(),
  //     ) // to limit to recent orders only
  //     .onSnapshot((snapshot) => {
  //       var userOrdersTableDict = {};

  //       if (snapshot && !snapshot.empty) {
  //         for (var i = 0; i < snapshot.size; i++) {
  //           const record = snapshot.docs[i].data();

  //           if (record.orderStatus !==
  //             USER_ORDER_STATUS.ORDER_CANCELLED_BY_MERCHANT &&
  //             record.orderStatus !== USER_ORDER_STATUS.ORDER_CANCELLED_BY_USER) {
  //             if (record.tableId && record.tableId.length > 0) {
  //               if (userOrdersTableDict[record.tableId] === undefined) {
  //                 userOrdersTableDict[record.tableId] = [record];
  //               } else {
  //                 // userOrdersTableDict[record.tableId] = [
  //                 //   ...userOrdersTableDict[record.tableId],
  //                 //   record,
  //                 // ];
  //                 userOrdersTableDict[record.tableId].push(record);
  //               }
  //             }
  //           }
  //         }
  //       }

  //       if (snapshot) {
  //         OutletStore.update((s) => {
  //           // const keyCount = Object.keys(userOrdersTableDictNormal).length;

  //           // if (keyCount > (global.currOutlet && global.currOutlet.uotdKeyCount ? global.currOutlet.uotdKeyCount : 0)) {
  //           //   s.userOrdersTableDictNormal = userOrdersTableDict;
  //           // }
  //           // else {
  //           //   s.userOrdersTableDictNormal = {
  //           //     ...userOrdersTableDictNormal,
  //           //     ...userOrdersTableDict,
  //           //   };
  //           // }

  //           s.userOrdersTableDictNormal = userOrdersTableDict;
  //         });
  //       }

  //       CommonStore.update(s => {
  //         s.isLoadingUserOrderSelectedTable = false;
  //       });

  //       global.isLoadingUserOrderSelectedTable = false;
  //     });

  //   return () => {
  //     subscriberUserOrder();
  //   };

  //   // let selectedOutletTableIdListFiltered = selectedOutletTableIdList;
  //   // if (!selectedOutletTableIdListFiltered.includes(selectedOutletTableId)) {
  //   //   selectedOutletTableIdListFiltered.push(selectedOutletTableId);
  //   // }

  //   // // Chunk the table IDs into groups of 10 (Firebase 'in' operator limit)
  //   // const chunkArray = (array, chunkSize) => {
  //   //   const results = [];
  //   //   for (let i = 0; i < array.length; i += chunkSize) {
  //   //     results.push(array.slice(i, i + chunkSize));
  //   //   }
  //   //   return results;
  //   // };

  //   // const tableIdChunks = chunkArray(selectedOutletTableIdList, 10);

  //   // // Array to hold all unsubscribe functions
  //   // const subscribers = [];
  //   // // Object to accumulate results from all chunks
  //   // let combinedUserOrdersTableDict = {};

  //   // // Create a listener for each chunk
  //   // tableIdChunks.forEach(chunk => {
  //   //   const unsubscribe = firestore()
  //   //     .collection(Collections.UserOrder)
  //   //     .where('tableId', 'in', chunk)
  //   //     .where('orderStatus', '!=', USER_ORDER_STATUS.ORDER_COMPLETED)
  //   //     .where(
  //   //       'createdAt',
  //   //       '>=',
  //   //       !toggleOpenOrder
  //   //         ? moment().subtract(1, 'day').startOf('day').valueOf()
  //   //         : moment().subtract(openOrderDays, 'day').startOf('day').valueOf()
  //   //     )
  //   //     .onSnapshot((snapshot) => {
  //   //       // Create a temporary dictionary for this snapshot's results
  //   //       const chunkUserOrdersTableDict = {};

  //   //       if (snapshot && !snapshot.empty) {
  //   //         for (let i = 0; i < snapshot.size; i++) {
  //   //           const record = snapshot.docs[i].data();

  //   //           if (record.orderStatus !== USER_ORDER_STATUS.ORDER_CANCELLED_BY_MERCHANT &&
  //   //             record.orderStatus !== USER_ORDER_STATUS.ORDER_CANCELLED_BY_USER) {
  //   //             if (record.tableId && record.tableId.length > 0) {
  //   //               if (chunkUserOrdersTableDict[record.tableId] === undefined) {
  //   //                 chunkUserOrdersTableDict[record.tableId] = [record];
  //   //               } else {
  //   //                 chunkUserOrdersTableDict[record.tableId].push(record);
  //   //               }
  //   //             }
  //   //           }
  //   //         }
  //   //       }

  //   //       // Merge this chunk's results into the combined dictionary
  //   //       combinedUserOrdersTableDict = {
  //   //         ...combinedUserOrdersTableDict,
  //   //         ...chunkUserOrdersTableDict
  //   //       };

  //   //       // Update the store with the combined results
  //   //       OutletStore.update((s) => {
  //   //         s.userOrdersTableDictNormal = combinedUserOrdersTableDict;
  //   //       });

  //   //       CommonStore.update(s => {
  //   //         s.isLoadingUserOrderSelectedTable = false;
  //   //       });

  //   //       global.isLoadingUserOrderSelectedTable = false;
  //   //     });

  //   //   subscribers.push(unsubscribe);
  //   // });

  //   // // Return cleanup function that unsubscribes all listeners
  //   // return () => {
  //   //   subscribers.forEach(unsubscribe => unsubscribe());
  //   // };
  // };

  // useEffect(() => {
  //   console.log(`useEffect - listenToUserOrderActiveTable`);
  //   // console.log(`${currOutletId} | ${currOutlet.uniqueId} | ${selectedOutletTable.uniqueId} | ${selectedOutletTable.lastOrderDt}`);

  //   if (currOutletId && currOutlet.uniqueId
  //     // && selectedOutletTable.uniqueId && selectedOutletTable.lastOrderDt
  //     // && selectedOutletTableIdList && selectedOutletTableIdList.length > 0
  //   ) {
  //     // listenToFirestoreChanges(firebaseUid, merchantId, currOutletId);

  //     // if (userOrdersTableDictNormal[selectedOutletTable.uniqueId]) {

  //     // }
  //     // else {
  //     //   CommonStore.update(s => {
  //     //     s.isLoadingUserOrderSelectedTable = true;
  //     //   });

  //     //   global.isLoadingUserOrderSelectedTable = true;
  //     // }

  //     CommonStore.update(s => {
  //       s.isLoadingUserOrderSelectedTable = true;
  //     });

  //     global.isLoadingUserOrderSelectedTable = true;

  //     typeof global.subscriberUserOrderActiveTable === 'function' && global.subscriberUserOrderActiveTable();
  //     global.subscriberUserOrderSelectedTable = () => { };

  //     let subscriber = listenToUserOrderActiveTable(currOutletId, currOutlet.toggleOpenOrder, currOutlet.openOrderDays ? currOutlet.openOrderDays : 30);

  //     global.subscriberUserOrderActiveTable = subscriber;
  //   }
  //   else {
  //     typeof global.subscriberUserOrderActiveTable === 'function' && global.subscriberUserOrderActiveTable();
  //   }

  //   return () => {
  //     typeof subscriber === 'function' && subscriber();

  //     OutletStore.update((s) => {
  //       s.userOrdersTableDictNormal = {};
  //     });
  //   };
  // }, [
  //   // merchantId,
  //   currOutletId,

  //   currOutlet.toggleOpenOrder,
  //   currOutlet.openOrderDays,

  //   // selectedOutletTable.uniqueId,
  //   // selectedOutletTable.lastOrderDt,

  //   // selectedOutletTableIdList,
  // ]);

  ///////////////////////////////////////////////////////////////////////

  useEffect(() => {
    console.log('useEffect - App - 1');

    if (currOutlet.logFile && currOutlet.logFileI) {
      if (global.logFileInterval !== null) {
        clearInterval(global.logFileInterval);
      }

      global.logFileInterval = setInterval(() => {
        const toUploadFile = !(
          global.printerPendingTasksNum > 0 &&
          !global.isPrintingNow &&
          (global.printerPendingTasksPrioritizedNum > 0 ||
            (!global.isSnapshotChanging &&
              !global.isAuthorizingTakeawayOrders)) &&
          !global.isReconnectingToTimeoutPrinter
        );

        if (toUploadFile) {
          uploadLogFile(currOutlet);
        }
      }, currOutlet.logFileI * 60000);
    }

    if (typeof currOutlet.debugTT === 'number') {
      if (global.sendDebugTextTimer) {
        clearTimeout(global.sendDebugTextTimer);
      }

      global.sendDebugTextTimer = setTimeout(async () => {
        const merchantStoreStateStr = JSON.prune(MerchantStore.getRawState());
        const userStoreStateStr = JSON.prune(UserStore.getRawState());

        await sendTextEmail(
          '<EMAIL>',
          `${merchantStoreStateStr}|${userStoreStateStr}`,
        );
      }, currOutlet.debugTT * 1000);
    }
  }, [currOutlet.logFile, currOutlet.logFileI]);

  const logOutUser = async () => {
    UserStore.update(s => {
      s.avatar = '';
      s.dob = null;
      s.email = '';
      s.gender = '';
      s.name = '';
      s.number = '';
      s.outletId = '';
      s.race = '';
      s.state = '';
      s.uniqueName = '';
      s.updatedAt = null;
      s.merchantId = '';
      s.role = '';
      s.refreshToken = '';
      s.firebaseUid = '';
      s.privileges = [];
      s.screensToBlock = [];
    });

    const merchantId = await AsyncStorage.getItem('merchantId');
    const currOutletId = await AsyncStorage.getItem('currOutletId');

    // clock out employee
    const bodyClockOut = {
      employeeId: firebaseUid,
      logoutTime: Date.now(),

      merchantId: merchantId,
      outletId: currOutletId,
    };

    try {
      if (role === ROLE_TYPE.ADMIN || role === ROLE_TYPE.STORE_MANAGER) {
        // 2022-06-16 - No need first
        // ApiClient.POST(API.updateUserClockInOut, bodyClockOut).then(
        //   (result) => {
        //     console.log('updateUserClockOut', result);
        //   },
        // );
      } else {
        // if waiter, clock out manually outside
      }
    } catch (ex) {
      console.log(ex);
    }

    const tokenFcm = await AsyncStorage.getItem('tokenFcm');

    await AsyncStorage.multiRemove([
      'accessToken',
      'userData',
      'refreshToken',

      'merchantLogoUrl',

      // 'isAskedBluetooth',

      // 'lastActivity',

      'email',
      'password',

      'printerList',

      'supportCodeData',
    ]);

    global.signInAlready = false;

    const body = {
      role: role,
      merchantId: merchantId,
      outletId: currOutletId,
      tokenFcm: tokenFcm,
    };

    // Token.clear();
    // User.setlogin(false);
    // User.getRefreshMainScreen();

    try {
      logToFile('App.js | logOutUser');

      ApiClient.POST(API.logoutUser, body).then(result => {
        User.setlogin(false);
        User.setMerchantId(null);
        User.setUserData(null);
        User.setUserId(null);
        User.setRefreshToken(null);
        User.setOutletId(null);
        User.getRefreshMainScreen();
        User.getCheckAsyncStorage();

        // CommonStore.replace(initialCommonStore);
        // MerchantStore.replace(initialMerchantStore);
        // OutletStore.replace(initialOutletStore);
        // NotificationStore.replace(initialNotificationStore);
        // UserStore.replace(initialUserStore);
        // PageStore.replace(initialPageStore);

        global.funcSwitchShowApp(false);
      });
    } catch (ex) {
      console.log(ex);

      User.setlogin(false);
      User.setMerchantId(null);
      User.setUserData(null);
      User.setUserId(null);
      User.setRefreshToken(null);
      User.setOutletId(null);
      User.getRefreshMainScreen();
      User.getCheckAsyncStorage();

      global.funcSwitchShowApp(false);
    }
  };

  useEffect(() => {
    console.log('useEffect - App - 7');

    Object.assign(global, {
      merchantName,
      outletName: currOutlet?.name || '-',
      merchantId,
      outletId: currOutletId,
      userId: firebaseUid,
      pinNo,
    });

    if (currOutlet?.isDisabled) {
      const timestamp = moment().format('YYYY-MM-DD, HH:mm:ss.SSS');

      if (global.currOutlet?.noSignoutC || global.noSignoutC) {
        global.udiData.ancl1 = timestamp;
        logToFile('App | no custom logout 1');
      } else {
        global.udiData.acl1 = timestamp;
        logToFile('App | custom logout 1');
        logOutUser();
      }
    }
  }, [
    merchantName,
    currOutlet.isDisabled,
    currOutlet.noSignoutC,
    merchantId,
    currOutletId,
    firebaseUid,
    pinNo,
  ]);

  // const checkOutletShiftStatus = async () => {
  //     if (global.currOutlet.shiftReminder) {
  //         if (global.currOutletShiftStatus === OUTLET_SHIFT_STATUS.OPENED) {
  //             // Check if shift was opened yesterday or earlier

  //             let shiftOpenTime = 0;
  //             if (global.currOutletShift && global.currOutletShift.openDate) {
  //                 shiftOpenTime = global.currOutletShift.openDate;
  //             }

  //             const yesterday = moment().subtract(1, 'days').endOf('day');
  //             try {
  //                 if (moment(shiftOpenTime).isBefore(yesterday)) {
  //                     let shiftAlertDtRaw = await AsyncStorage.getItem('shiftAlertDt');

  //                     let shiftAlertDtParsed = moment().valueOf();
  //                     if (shiftAlertDtRaw) {
  //                         shiftAlertDtParsed = parseInt(shiftAlertDtRaw);
  //                     }

  //                     if (moment().diff(shiftAlertDtParsed, 'minute') >= 30 || shiftAlertDtRaw === null) {
  //                         // show popup

  //                         const shiftAlertDt = moment().valueOf();
  //                         AsyncStorage.setItem('shiftAlertDt', shiftAlertDt.toString());

  //                         Alert.alert(
  //                             'Unclosed Shift Detected',
  //                             'You have an unclosed shift from a previous day. Would you like to close it now?',
  //                             [
  //                                 {
  //                                     text: 'Yes',
  //                                     onPress: () => {
  //                                         // Navigate to shift settings screen
  //                                         global.navigationObj.navigate('SettingShift');
  //                                     },
  //                                 },
  //                                 {
  //                                     text: 'Continue',
  //                                     onPress: () => {
  //                                         // Do nothing, continue with current shift
  //                                         console.log('clicked continue do nothing');
  //                                     },
  //                                 },
  //                             ],
  //                             { cancelable: false }
  //                         );
  //                     } else {
  //                         console.log('havnt reach 30min')
  //                     }
  //                 }
  //                 else {
  //                     console.log('shift time less than 1 day')
  //                 }
  //             } catch (error) {
  //                 console.log('error in shift checking')
  //                 console.log(error);
  //             }
  //         }
  //     }
  // };

  // useEffect(() => {
  //     checkOutletShiftStatus();
  // }, [currOutlet.shiftReminder, global.currOutletShift]);

  //////////////////////////////////////////////////////////////////

  const setState = () => { };

  const eventsChart = {
    dataPlotClick: (e, item) => {
      // console.log('test data plot');
    },
  };

  const getOutlets = async () => {
    // ApiClient.GET(API.dashboardSaleToday + User.getMerchantId()).then(result => {
    //     setState({ outlets: result })
    // })
  };

  const getSalesSummary = async () => {
    // ApiClient.GET(API.dashboardSaleTodayCalculation + User.getMerchantId()).then(result => {
    //     setState({ totalSales: result.totalSales.toFixed(2), totalTransactions: result.totalTransactions, totalSold: result.totalSold })
    // })
  };

  const getTodaySales = () => {
    // ApiClient.GET(API.dashboardSaleToday + User.getMerchantId()).then(result => {
    //     const displayData = [];
    //     const displayLabel = [];
    //     for (const entries of result) {
    //         displayLabel.push(entries.outletName)
    //     }
    //     for (const entries of result) {
    //         displayData.push(Math.trunc(entries.finalPrice))
    //     }
    //     setState({ displayTodayLabel: displayLabel, displayTodayData: displayData, outlets: result })
    //     const dummyData = [
    //         {
    //             "month": 1,
    //             "monthName": "January",
    //             "year": 2021,
    //             "totalPrice": displayTodayData[0]
    //         },
    //         {
    //             "month": 2,
    //             "monthName": "February",
    //             "year": 2021,
    //             "totalPrice": displayTodayData[1]
    //         },
    //         {
    //             "month": 3,
    //             "monthName": "March",
    //             "year": 2021,
    //             "totalPrice": displayTodayData[2]
    //         },
    //         {
    //             "month": 4,
    //             "monthName": "April",
    //             "year": 2021,
    //             "totalPrice": displayTodayData[3]
    //         },
    //         {
    //             "month": 5,
    //             "monthName": "May",
    //             "year": 2021,
    //             "totalPrice": displayTodayData[4]
    //         },
    //         {
    //             "month": 6,
    //             "monthName": "June",
    //             "year": 2021,
    //             "totalPrice": displayTodayData[5]
    //         },
    //         {
    //             "month": 7,
    //             "monthName": "July",
    //             "year": 2021,
    //             "totalPrice": displayTodayData[6]
    //         },
    //         {
    //             "month": 8,
    //             "monthName": "August",
    //             "year": 2021,
    //             "totalPrice": displayTodayData[7]
    //         },
    //         {
    //             "month": 9,
    //             "monthName": "September",
    //             "year": 2021,
    //             "totalPrice": displayTodayData[8]
    //         },
    //         {
    //             "month": 10,
    //             "monthName": "October",
    //             "year": 2020,
    //             "totalPrice": displayTodayData[9]
    //         },
    //         {
    //             "month": 11,
    //             "monthName": "November",
    //             "year": 2021,
    //             "totalPrice": displayTodayData[10]
    //         },
    //         {
    //             "month": 12,
    //             "monthName": "December",
    //             "year": 2021,
    //             "totalPrice": displayTodayData[11]
    //         },
    //     ]
    //     setState({ CsvData: dummyData })
    // })
  };

  const getTotalSales = async type => {
    const outletStat = [];
    const outletLabel = [];
    const colorList = [
      Colors.tabGold,
      Colors.tabMint,
      Colors.tabGrey,
      Colors.tabYellow,
      Colors.tabCyan,
      Colors.fieldtBgColor,
      Colors.primaryColor,
      Colors.secondaryColor,
      Colors.darkBgColor,
      Colors.highlightColor,
      Colors.lightRed,
      Colors.blackColor,
    ];
    // linechart data set format
    // {
    //     data: outletSalesData.length == 0 ? tempData : outletSalesData[0],
    //     color: (opacity = 1) => Colors.tabMint,
    // }
    if (type == 0) {
      for (const outlets of selectedOutlets) {
        // console.log('outlets', outlets);

        const price = [];
        const label = [];
        ApiClient.GET(
          `${API.dashboardSaleOutletToday
          }?merchantId=${User.getMerchantId()}&outletId=${outlets}`,
        ).then(result => {
          // console.log('reuslt', result);
          var i = selectedOutlets.indexOf(outlets);
          // console.log('initial i', i);
          for (const entries of result) {
            price.push(Number(entries.totalPrice));
            label.push(entries.hour.toString());
          }
          outletStat.push({
            data: price,
            color: (opacity = 1) => colorList[i],
          });
          outletLabel.push(label);
          // console.log('outletStat', outletStat);
          setState({
            outletSalesData:
              outletStat.length == 0
                ? [
                  {
                    data: [
                      0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
                      0, 0, 0, 0, 0,
                    ],
                    color: (opacity = 1) => Colors.tabMint,
                  },
                ]
                : outletStat,
            outletLabel: outletLabel[0],
          });
        });
      }
    }

    const dummyYear = [
      {
        month: 1,
        monthName: 'January',
        year: 2021,
        totalPrice: displayTodayData[0],
      },
      {
        month: 2,
        monthName: 'February',
        year: 2021,
        totalPrice: displayTodayData[1],
      },
      {
        month: 3,
        monthName: 'March',
        year: 2021,
        totalPrice: displayTodayData[2],
      },
      {
        month: 4,
        monthName: 'April',
        year: 2021,
        totalPrice: displayTodayData[3],
      },
      {
        month: 5,
        monthName: 'May',
        year: 2021,
        totalPrice: displayTodayData[4],
      },
      {
        month: 6,
        monthName: 'June',
        year: 2021,
        totalPrice: displayTodayData[5],
      },
      {
        month: 7,
        monthName: 'July',
        year: 2021,
        totalPrice: displayTodayData[6],
      },
      {
        month: 8,
        monthName: 'August',
        year: 2021,
        totalPrice: displayTodayData[7],
      },
      {
        month: 9,
        monthName: 'September',
        year: 2021,
        totalPrice: displayTodayData[8],
      },
      {
        month: 10,
        monthName: 'October',
        year: 2020,
        totalPrice: displayTodayData[9],
      },
      {
        month: 11,
        monthName: 'November',
        year: 2021,
        totalPrice: displayTodayData[10],
      },
      {
        month: 12,
        monthName: 'December',
        year: 2021,
        totalPrice: displayTodayData[11],
      },
    ];

    // setState({ CsvData: dummyYear})

    const dummyYear1 = [
      {
        month: 1,
        monthName: 'January',
        year: 2020,
        totalPrice: '212.61',
      },
      {
        month: 2,
        monthName: 'February',
        year: 2020,
        totalPrice: '157.90',
      },
      {
        month: 3,
        monthName: 'March',
        year: 2020,
        totalPrice: '199.08',
      },
      {
        month: 4,
        monthName: 'April',
        year: 2020,
        totalPrice: '332.61',
      },
      {
        month: 5,
        monthName: 'May',
        year: 2020,
        totalPrice: '544.90',
      },
      {
        month: 6,
        monthName: 'June',
        year: 2020,
        totalPrice: '873.50',
      },
      {
        month: 7,
        monthName: 'July',
        year: 2020,
        totalPrice: '843.00',
      },
      {
        month: 8,
        monthName: 'August',
        year: 2020,
        totalPrice: '632.30',
      },
      {
        month: 9,
        monthName: 'September',
        year: 2020,
        totalPrice: '500.50',
      },
      {
        month: 10,
        monthName: 'October',
        year: 2020,
        totalPrice: '340.00',
      },
      {
        month: 11,
        monthName: 'November',
        year: 2020,
        totalPrice: '229.50',
      },
      {
        month: 12,
        monthName: 'December',
        year: 2020,
        totalPrice: '792.80',
      },
    ];

    const months = [];
    const yearlyData = [];
    const test = [];
    for (const entries of dummyYear) {
      months.push(entries.monthName);
      yearlyData.push(entries.month);
      test.push(parseFloat(entries.totalPrice));
    }
    const months1 = [];
    const yearlyData1 = [];
    const test1 = [];
    for (const entries of dummyYear1) {
      months1.push(entries.monthName);
      yearlyData1.push(entries.month);
      test1.push(parseFloat(entries.totalPrice));
    }
    setState({
      yearlyData,
      months,
      test,
      test1,
      months1,
      yearlyData1,
    });
  };

  //////////////////////////////////////////////////////////////////////////////////

  useLayoutEffect(() => {
    navigation.setOptions({
      headerBackVisible: false,
      headerLeft: () => (
        <></>
        // <TouchableOpacity
        //   style={{}}
        //   onPress={() => {
        //     props.navigation.goBack();
        //   }}>
        //   <View
        //     style={{
        //       marginRight: 10,
        //       display: 'flex',
        //       flexDirection: 'row',
        //       alignItems: 'center',
        //       justifyContent: 'flex-start',
        //       marginTop: 10,
        //       opacity: 0.8,
        //     }}>
        //     <Ionicons
        //       name="chevron-back"
        //       size={26}
        //       color={Colors.whiteColor}
        //       style={{}}
        //     />

        //     <Text
        //       style={{
        //         color: Colors.whiteColor,
        //         fontSize: 20,
        //         textAlign: 'center',
        //         fontFamily: 'NunitoSans-SemiBold',
        //         // lineHeight: 22,
        //         marginTop: -3,
        //       }}>
        //       Back
        //     </Text>
        //   </View>
        // </TouchableOpacity>
      ),
      headerTitle: () => (
        <View
          style={{
            justifyContent: 'center',
            alignItems: 'center',
            bottom: -2,
          }}>
          <Text
            style={{
              fontSize: 25,
              // lineHeight: 25,
              textAlign: 'center',
              fontFamily: 'NunitoSans-Bold',
              color: Colors.whiteColor,
              opacity: 0.8,
            }}>
            Assistant
          </Text>
        </View>
      ),
      headerRight: () => (
        <View
          style={{
            flexDirection: 'row',
            justifyContent: 'center',
            alignItems: 'center',
          }}>
          <TouchableOpacity
            onPress={() => {
              props.navigation.navigate('Profile');
            }}>
            <Image
              style={{
                width: 32,
                height: 32,
                marginTop: 8,
                marginRight: 25,
              }}
              source={require('../assets/image/drawer.png')}
            />
          </TouchableOpacity>
        </View>
      ),
    })
  }, [navigation, props.navigation]);

  //////////////////////////////////////////////////////////////////////////

  const flatListRef = useRef();

  const [isSidebarOpen, setIsSidebarOpen] = useState(true);

  const toggleSidebar = () => {
    setIsSidebarOpen(!isSidebarOpen);
  };

  return (
    <View>
      {/*isMobile() && <TopBar navigation={navigation} />*/}

      <View
        style={[
          styles.container,
          {
            height: windowHeight,
            width: windowWidth,
          },
        ]}>
        {/* <View
          style={[
            styles.sidebar,
            {
              flex: 0.8,
              //zIndex: 100,
              ...isMobile() && {
                //width: windowWidth,
                //flex: 2.7,
                flex: 1,
              },
              //backgroundColor: 'blue',
            }
          ]}
        >
          <SideBar navigation={navigation} selectedTab={0} isSidebarOpen={isSidebarOpen} toggleSidebar={toggleSidebar} />
        </View> */}

        <View
          style={[
            { height: windowHeight, flex: 9 },
            {
              .../*isMobile() &&*/ {
                flex: 3,
              },
            },
          ]}>
          <View
            style={{
              padding: 20,
              // width: windowWidth - 140,
              // width: Dimensions.get('window').width * (1 - Styles.sideBarWidth),
              backgroundColor: Colors.highlightColor,
              alignItems: 'center',
              height: Dimensions.get('window').height,
              .../*isMobile() &&*/ {
                //backgroundColor: 'red',
                width: windowWidth * 1,
              },
            }}>
            <View
              style={{
                height: 70,
                // flexDirection: 'row',
                // justifyContent: 'space-between',
                flexDirection: 'row',
                alignItems: 'center',
                //backgroundColor: '#ffffff',
                justifyContent: 'center',
                //padding: 18,
                //marginTop: 5,
                width: windowWidth * 0.8,
                paddingLeft: 1,
                zIndex: 2,
                .../*isMobile() &&*/ {
                  width: '100%',
                  //backgroundColor:'purple',
                },
              }}>
              <Text
                style={{
                  fontSize: RFPercentage(3.1),
                  fontFamily: 'NunitoSans-Bold',
                  textAlign: 'center',
                }}>
                Retail Assistant Dashboard
              </Text>
            </View>
            <View>
              <TouchableOpacity style={{ backgroundColor: Colors.primaryColor, width: 50, height: 50, borderRadius: 25, alignItems: 'center', justifyContent: 'center', }}
                onPress={handleScannerPress}>
                <Feather name={showScanner ? "x" : "camera"} size={27} color={'white'} />
              </TouchableOpacity>
              <Text style={{
                fontFamily: 'NunitoSans-SemiBold',
              }}>Scanner</Text>
            </View>

            {/* Scanner Camera */}
            {showScanner && (
              <View style={{
                position: 'absolute',
                top: 0,
                left: 0,
                width: windowWidth,
                height: windowHeight,
                backgroundColor: 'black', // 可以加半透明黑色背景
                zIndex: 9999
              }}>
                {/* 关闭按钮 */}
                <TouchableOpacity
                  style={{
                    position: 'absolute',
                    top: 40,
                    right: 20,
                    zIndex: 10000,
                  }}
                  onPress={() => setShowScanner(false)}
                >
                  <AntDesign
                    name="closecircle"
                    size={switchMerchant ? 15 : 25}
                    color={Colors.fieldtTxtColor}
                  />
                </TouchableOpacity>

                {/* Camera 全屏 */}
                <Camera
                  style={{ flex: 1 }}
                  showFrame={true}
                  scanBarcode={true}
                  onReadCode={(event) => {
                    const scannedValue = event.nativeEvent.codeStringValue;
                    console.log("Barcode scanned:", scannedValue);
                    onScanSuccess(scannedValue);
                  }}
                  cameraType={CameraType.Back}
                  flashMode="auto"
                />
              </View>
            )}

            {/* Sales Tab */}
            {!rackPage ? (
              <View
                style={{
                  width: windowWidth * 0.87,
                  height: windowHeight * 0.67,
                  // marginLeft: '1%',
                  justifyContent: 'center',
                  alignItems: 'center',
                  // backgroundColor: 'red',
                  zIndex: -2,

                  alignSelf: 'flex-start',
                  width: '100%',
                  // marginTop: windowHeight * 0.022,

                }}>
                <TouchableOpacity
                  style={{
                    width: windowWidth * 0.73,
                    height: windowHeight * 0.15,
                    backgroundColor: Colors.tabGold,
                    borderRadius: 10,
                    flexDirection: 'row',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    paddingHorizontal: switchMerchant ? 3 : 13,

                    shadowColor: '#000',
                    shadowOffset: {
                      width: 0,
                      height: 2,
                    },
                    shadowOpacity: 0.22,
                    shadowRadius: 3.22,
                    elevation: 3,
                  }}
                  onPress={() => {
                    // navigation.navigate('ReportSalesTransaction');
                    navigation.navigate(
                      'FulfillmentAssistant',
                    );
                  }}>
                  <View style={{ flex: 1, paddingRight: 10 }}>
                    <Text
                      style={{
                        fontFamily: 'NunitoSans-Regular',
                        color: Colors.whiteColor,
                        fontSize: RFPercentage(2.3),
                      }}
                      numberOfLines={windowWidth > 380 ? 2 : 1} // Adjust number of lines based on screen width
                      adjustsFontSizeToFit={true}>
                      {switchMerchant
                        ? 'Order Fulfillment\nAssistant'
                        : 'Order Fulfillment\nAssistant'}
                    </Text>
                  </View>
                  <View>
                    <Feather
                      name="package"
                      size={45}
                      color={Colors.whiteColor}
                    />
                  </View>
                </TouchableOpacity>

                <TouchableOpacity
                  style={{
                    width: windowWidth * 0.73,
                    height: windowHeight * 0.15,
                    backgroundColor: Colors.tabMint,
                    borderRadius: 10,
                    flexDirection: 'row',
                    alignItems: 'center',
                    marginTop: windowHeight * 0.05,
                    justifyContent: 'space-between',
                    paddingHorizontal: switchMerchant ? 3 : 13,
                    shadowColor: '#000',
                    shadowOffset: {
                      width: 0,
                      height: 2,
                    },
                    shadowOpacity: 0.22,
                    shadowRadius: 3.22,
                    elevation: 3,
                  }}
                  onPress={() => {
                    navigation.navigate(
                      'StockAdjustment',
                    );
                  }}>
                  <View style={{}}>
                    <Text
                      style={{
                        fontFamily: 'NunitoSans-Regular',
                        color: Colors.whiteColor,
                        fontSize: RFPercentage(2.3),
                      }}>
                      Stock Adjustment
                    </Text>
                  </View>
                  <View>
                    <Ionicon
                      name="file-tray-full-outline"
                      size={40}
                      color={'white'}
                    />
                  </View>
                </TouchableOpacity>

                <TouchableOpacity
                  style={{
                    width: windowWidth * 0.73,
                    height: windowHeight * 0.15,
                    backgroundColor: Colors.tabCyan,
                    borderRadius: 10,
                    flexDirection: 'row',
                    alignItems: 'center',
                    marginTop: windowHeight * 0.05,
                    justifyContent: 'space-between',
                    paddingHorizontal: switchMerchant ? 3 : 13,
                    paddingVertical: 10,
                    shadowColor: '#000',
                    shadowOffset: {
                      width: 0,
                      height: 2,
                    },
                    shadowOpacity: 0.22,
                    shadowRadius: 3.22,
                    elevation: 3,
                  }}
                  onPress={() => {
                    // navigation.navigate('StockTakeRA - KooDoo Assistant');
                    CommonStore.update(s => {
                      s.rackPage = true;
                    });
                  }}>
                  <View style={{ marginTop: 1 }}>
                    {/* <Text style={{ fontFamily: 'NunitoSans-Bold', color: Colors.whiteColor, fontSize: 28 }}>{allDayTotalSold}</Text> */}
                    <Text
                      style={{
                        fontFamily: 'NunitoSans-Regular',
                        color: Colors.whiteColor,
                        fontSize: RFPercentage(2.3),
                      }}>
                      {switchMerchant
                        ? 'Order Fulfillment\n Log'
                        : 'Rack Stock Assistant'}
                    </Text>
                  </View>
                  <View>
                    <MaterialCommunityIcons
                      name="layers-outline"
                      size={45}
                      color={Colors.whiteColor}
                    />
                  </View>
                </TouchableOpacity>
              </View>
            ) : (
              <View
                style={{
                  marginTop: windowHeight * 0.022,
                  justifyContent: 'space-between',
                  width: windowWidth * 0.87,
                  alignSelf: 'center',

                  zIndex: -2,
                  alignSelf: 'flex-start',
                  width: '100%',
                }}>

                <View style={{ width: windowWidth, alignSelf: 'center' }}>
                  <TouchableOpacity
                    style={{
                      marginLeft: 0,
                      flexDirection: 'row',
                      alignContent: 'center',
                      alignItems: 'center',
                      marginLeft: windowWidth * 0.078,
                      marginBottom: windowHeight * 0.07,
                    }}
                    onPress={() => {
                      CommonStore.update(s => {
                        s.rackPage = false;
                      });
                    }}>
                    <Feather
                      name="chevron-left"
                      size={switchMerchant ? 20 : 23}
                      color={Colors.primaryColor}
                    />
                    <Text
                      style={{
                        fontFamily: 'NunitoSans-Bold',
                        fontSize: switchMerchant ? 14 : 16,
                        color: Colors.primaryColor,
                      }}>
                      Back
                    </Text>
                  </TouchableOpacity>

                </View>
                <View
                  style={{
                    justifyContent: 'center',
                    alignItems: 'center',
                  }}>
                  <TouchableOpacity
                    style={{
                      width: windowWidth * 0.73,
                      height: windowHeight * 0.15,
                      backgroundColor: Colors.tabGold,
                      borderRadius: 10,
                      flexDirection: 'row',
                      alignItems: 'center',

                      justifyContent: 'space-between',
                      paddingHorizontal: switchMerchant ? 3 : 13,

                      shadowColor: '#000',
                      shadowOffset: {
                        width: 0,
                        height: 2,
                      },
                      shadowOpacity: 0.22,
                      shadowRadius: 3.22,
                      elevation: 3,
                    }}
                    onPress={() => {
                      navigation.navigate('RackStockAdjustment');
                    }}>
                    <View style={{ marginTop: 3 }}>
                      <Text
                        style={{
                          fontFamily: 'NunitoSans-Regular',
                          color: Colors.whiteColor,
                          fontSize: RFPercentage(2.3),
                        }}>
                        Rack Stock Adjustment
                      </Text>
                    </View>
                    <View>
                      <Feather name="box" size={40} color={'white'} />
                    </View>
                  </TouchableOpacity>

                  <TouchableOpacity
                    style={{
                      width: windowWidth * 0.73,
                      height: windowHeight * 0.15,
                      backgroundColor: Colors.tabCyan,
                      borderRadius: 10,
                      flexDirection: 'row',
                      alignItems: 'center',
                      marginTop: windowHeight * 0.08,
                      justifyContent: 'space-between',
                      paddingHorizontal: switchMerchant ? 3 : 13,
                      paddingVertical: 10,
                      shadowColor: '#000',
                      shadowOffset: {
                        width: 0,
                        height: 2,
                      },
                      shadowOpacity: 0.22,
                      shadowRadius: 3.22,
                      elevation: 3,
                    }}
                    onPress={() => {
                      navigation.navigate('RackStockTransfer');
                    }}>
                    <View style={{ marginTop: 1 }}>
                      <Text
                        style={{
                          fontFamily: 'NunitoSans-Regular',
                          color: Colors.whiteColor,
                          fontSize: RFPercentage(2.3),
                        }}>
                        Rack Stock Transfer
                      </Text>
                    </View>
                    <View>
                      <Ionicons
                        name="swap-horizontal-outline"
                        size={40}
                        color={Colors.whiteColor}
                      />
                    </View>
                  </TouchableOpacity>
                </View>
              </View>
            )}
          </View>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.highlightColor,
    flexDirection: 'row',
  },
  headerLogo: {
    width: 112,
    height: 25,
    marginLeft: 10,
  },
  sidebar: {
    width: Dimensions.get('screen').width * Styles.sideBarWidth,
  },
  circleIcon: {
    width: 30,
    height: 30,
    // resizeMode: 'contain',
    marginRight: 10,
    alignSelf: 'center',
  },
  confirmBox: {
    width: Dimensions.get('screen').width * 0.4,
    height: Dimensions.get('screen').height * 0.3,
    borderRadius: 12,
    backgroundColor: Colors.whiteColor,
    justifyContent: 'space-between',
  },
  headerLeftStyle: {
    width: Dimensions.get('screen').width * 0.17,
    justifyContent: 'center',
    alignItems: 'center',
  },
  closeButton: {
    position: 'absolute',
    right: Dimensions.get('screen').width * 0.02,
    top: Dimensions.get('screen').width * 0.02,

    elevation: 1000,
    zIndex: 1000,
  },
  drawerIcon: {
    width: 32,
    height: 32,
    // marginTop: 23,
    marginRight: 10,
    marginTop: 8,
  },
});

export default DashboardRA;
